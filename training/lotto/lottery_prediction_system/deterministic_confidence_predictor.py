#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
確定性最高信心度預測器
基於歷史數據分析，產生穩定一致的預測結果
"""

import sqlite3
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
from collections import Counter, defaultdict
import logging
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('deterministic_predictor')

class DeterministicConfidencePredictor:
    """確定性最高信心度預測器 - 每次產生相同結果"""
    
    def __init__(self, db_path: str = None):
        if db_path is None:
            self.db_path = os.path.join(
                os.path.dirname(os.path.abspath(__file__)), 
                'data', 'lottery_data.db'
            )
        else:
            self.db_path = db_path
            
        self.analysis_periods = 100  # 分析最近100期
        
    def get_deterministic_prediction(self, lottery_type: str = 'powercolor') -> Dict:
        """
        獲取確定性預測 - 基於歷史數據分析，每次返回相同結果
        """
        logger.info(f"開始確定性預測分析 - {lottery_type}")
        
        # 1. 獲取歷史數據
        historical_data = self._get_historical_data(lottery_type)
        
        if not historical_data:
            logger.warning("無歷史數據，使用默認預測")
            return self._get_default_prediction(lottery_type)
        
        # 2. 執行多維度分析（完全確定性）
        frequency_analysis = self._analyze_frequency(historical_data, lottery_type)
        trend_analysis = self._analyze_trends(historical_data, lottery_type)
        pattern_analysis = self._analyze_patterns(historical_data, lottery_type)
        cycle_analysis = self._analyze_cycles(historical_data, lottery_type)
        
        # 3. 綜合評分（確定性算法）
        final_scores = self._calculate_final_scores(
            frequency_analysis,
            trend_analysis,
            pattern_analysis,
            cycle_analysis,
            lottery_type
        )
        
        # 4. 選擇最優號碼（確定性選擇）
        selected_numbers = self._select_best_numbers(final_scores, lottery_type)
        selected_special = self._select_best_special(historical_data, lottery_type)
        
        # 5. 計算信心度（基於數據支持度）
        confidence_score = self._calculate_data_confidence(
            selected_numbers, 
            selected_special,
            final_scores,
            historical_data
        )
        
        # 6. 生成分析報告
        analysis_report = self._generate_analysis_report(
            selected_numbers,
            selected_special,
            confidence_score,
            frequency_analysis,
            trend_analysis,
            pattern_analysis,
            cycle_analysis
        )
        
        result = {
            'lottery_type': lottery_type,
            'prediction_numbers': sorted(selected_numbers),
            'prediction_special': selected_special,
            'confidence_score': confidence_score,
            'prediction_method': '確定性數據分析',
            'is_deterministic': True,
            'analysis_basis': {
                'historical_periods': len(historical_data),
                'analysis_date': datetime.now().strftime('%Y-%m-%d'),
                'data_completeness': len(historical_data) / self.analysis_periods
            },
            'detailed_analysis': analysis_report,
            'stability_guarantee': '此預測基於歷史數據分析，相同數據下結果保持一致'
        }
        
        logger.info(f"確定性預測完成 - 號碼: {selected_numbers}, 特別號: {selected_special}")
        return result
    
    def _get_historical_data(self, lottery_type: str) -> List[Dict]:
        """獲取歷史開獎數據"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            if lottery_type == 'powercolor':
                query = """
                SELECT Period, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, 
                       Second_district, Sdate
                FROM Powercolor
                ORDER BY Period DESC
                LIMIT ?
                """
            elif lottery_type == 'lotto649':
                query = """
                SELECT Period, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6,
                       SpecialNumber, Sdate
                FROM Lotto649
                ORDER BY Period DESC
                LIMIT ?
                """
            else:  # dailycash
                query = """
                SELECT Period, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Sdate
                FROM DailyCash
                ORDER BY Period DESC
                LIMIT ?
                """
            
            cursor = conn.execute(query, (self.analysis_periods,))
            rows = cursor.fetchall()
            conn.close()
            
            historical_data = []
            for row in rows:
                if lottery_type in ['powercolor', 'lotto649']:
                    historical_data.append({
                        'period': row[0],
                        'numbers': [row[1], row[2], row[3], row[4], row[5], row[6]],
                        'special': row[7] if len(row) > 7 else None,
                        'date': row[8] if len(row) > 8 else None
                    })
                else:
                    historical_data.append({
                        'period': row[0],
                        'numbers': [row[1], row[2], row[3], row[4], row[5]],
                        'special': None,
                        'date': row[6] if len(row) > 6 else None
                    })
            
            return historical_data
            
        except Exception as e:
            logger.error(f"獲取歷史數據失敗: {str(e)}")
            return []
    
    def _analyze_frequency(self, historical_data: List[Dict], lottery_type: str) -> Dict[int, float]:
        """頻率分析 - 確定性算法"""
        frequency_scores = {}
        total_periods = len(historical_data)
        
        # 統計每個號碼出現次數
        number_count = Counter()
        for data in historical_data:
            for num in data['numbers']:
                number_count[num] += 1
        
        # 計算頻率分數（標準化到0-1）
        max_count = max(number_count.values()) if number_count else 1
        
        # 根據彩票類型設定號碼範圍
        if lottery_type == 'powercolor':
            num_range = range(1, 39)
        elif lottery_type == 'lotto649':
            num_range = range(1, 50)
        else:  # dailycash
            num_range = range(1, 40)
        
        for num in num_range:
            count = number_count.get(num, 0)
            # 頻率分數：出現次數 / 最大出現次數
            frequency_scores[num] = count / max_count if max_count > 0 else 0
        
        return frequency_scores
    
    def _analyze_trends(self, historical_data: List[Dict], lottery_type: str) -> Dict[int, float]:
        """趨勢分析 - 確定性算法"""
        trend_scores = {}
        
        # 將數據分為三個時期：近期、中期、遠期
        recent = historical_data[:30] if len(historical_data) >= 30 else historical_data
        middle = historical_data[30:60] if len(historical_data) >= 60 else []
        old = historical_data[60:] if len(historical_data) > 60 else []
        
        # 統計各時期號碼出現頻率
        recent_count = Counter()
        middle_count = Counter()
        old_count = Counter()
        
        for data in recent:
            for num in data['numbers']:
                recent_count[num] += 1
                
        for data in middle:
            for num in data['numbers']:
                middle_count[num] += 1
                
        for data in old:
            for num in data['numbers']:
                old_count[num] += 1
        
        # 計算趨勢分數（近期權重更高）
        if lottery_type == 'powercolor':
            num_range = range(1, 39)
        elif lottery_type == 'lotto649':
            num_range = range(1, 50)
        else:
            num_range = range(1, 40)
        
        for num in num_range:
            recent_freq = recent_count.get(num, 0) / max(len(recent), 1)
            middle_freq = middle_count.get(num, 0) / max(len(middle), 1)
            old_freq = old_count.get(num, 0) / max(len(old), 1)
            
            # 加權計算：近期50%，中期30%，遠期20%
            trend_scores[num] = recent_freq * 0.5 + middle_freq * 0.3 + old_freq * 0.2
        
        return trend_scores
    
    def _analyze_patterns(self, historical_data: List[Dict], lottery_type: str) -> Dict[int, float]:
        """模式分析 - 確定性算法"""
        pattern_scores = defaultdict(float)
        
        # 分析連號模式
        consecutive_bonus = {}
        for data in historical_data:
            sorted_nums = sorted(data['numbers'])
            for i in range(len(sorted_nums) - 1):
                if sorted_nums[i+1] - sorted_nums[i] == 1:
                    # 連號加分
                    consecutive_bonus[sorted_nums[i]] = consecutive_bonus.get(sorted_nums[i], 0) + 0.1
                    consecutive_bonus[sorted_nums[i+1]] = consecutive_bonus.get(sorted_nums[i+1], 0) + 0.1
        
        # 分析奇偶平衡
        odd_even_patterns = []
        for data in historical_data:
            odd_count = sum(1 for n in data['numbers'] if n % 2 == 1)
            even_count = len(data['numbers']) - odd_count
            odd_even_patterns.append((odd_count, even_count))
        
        # 最常見的奇偶模式
        most_common_pattern = Counter(odd_even_patterns).most_common(1)[0][0] if odd_even_patterns else (3, 3)
        target_odd, target_even = most_common_pattern
        
        # 分析大小平衡
        size_patterns = []
        mid_point = 20 if lottery_type == 'powercolor' else 25
        for data in historical_data:
            small_count = sum(1 for n in data['numbers'] if n < mid_point)
            large_count = len(data['numbers']) - small_count
            size_patterns.append((small_count, large_count))
        
        most_common_size = Counter(size_patterns).most_common(1)[0][0] if size_patterns else (3, 3)
        target_small, target_large = most_common_size
        
        # 綜合計算模式分數
        if lottery_type == 'powercolor':
            num_range = range(1, 39)
        elif lottery_type == 'lotto649':
            num_range = range(1, 50)
        else:
            num_range = range(1, 40)
        
        for num in num_range:
            score = 0.5  # 基礎分數
            
            # 連號加分
            score += consecutive_bonus.get(num, 0)
            
            # 奇偶平衡加分
            if (num % 2 == 1 and target_odd > target_even) or (num % 2 == 0 and target_even > target_odd):
                score += 0.1
            
            # 大小平衡加分
            if (num < mid_point and target_small > target_large) or (num >= mid_point and target_large > target_small):
                score += 0.1
            
            pattern_scores[num] = min(score, 1.0)
        
        return dict(pattern_scores)
    
    def _analyze_cycles(self, historical_data: List[Dict], lottery_type: str) -> Dict[int, float]:
        """週期分析 - 確定性算法"""
        cycle_scores = {}
        
        # 計算每個號碼的平均出現週期
        last_appearance = {}
        appearance_intervals = defaultdict(list)
        
        for idx, data in enumerate(historical_data):
            for num in data['numbers']:
                if num in last_appearance:
                    interval = idx - last_appearance[num]
                    appearance_intervals[num].append(interval)
                last_appearance[num] = idx
        
        # 計算平均週期和當前間隔
        if lottery_type == 'powercolor':
            num_range = range(1, 39)
        elif lottery_type == 'lotto649':
            num_range = range(1, 50)
        else:
            num_range = range(1, 40)
        
        for num in num_range:
            if num in appearance_intervals and appearance_intervals[num]:
                avg_interval = sum(appearance_intervals[num]) / len(appearance_intervals[num])
                current_interval = last_appearance.get(num, self.analysis_periods)
                
                # 如果當前間隔接近平均週期，給予高分
                if current_interval > 0:
                    deviation = abs(current_interval - avg_interval) / avg_interval if avg_interval > 0 else 1
                    cycle_scores[num] = max(0, 1 - deviation)
                else:
                    cycle_scores[num] = 0.5
            else:
                # 從未出現或很少出現的號碼
                cycle_scores[num] = 0.3
        
        return cycle_scores
    
    def _calculate_final_scores(self, frequency: Dict, trend: Dict, pattern: Dict, 
                               cycle: Dict, lottery_type: str) -> Dict[int, float]:
        """計算最終綜合分數 - 確定性算法"""
        final_scores = {}
        
        # 權重分配
        weights = {
            'frequency': 0.35,  # 頻率權重35%
            'trend': 0.25,      # 趨勢權重25%
            'pattern': 0.20,    # 模式權重20%
            'cycle': 0.20       # 週期權重20%
        }
        
        # 計算綜合分數
        all_nums = set(frequency.keys()) | set(trend.keys()) | set(pattern.keys()) | set(cycle.keys())
        
        for num in all_nums:
            score = (
                frequency.get(num, 0) * weights['frequency'] +
                trend.get(num, 0) * weights['trend'] +
                pattern.get(num, 0) * weights['pattern'] +
                cycle.get(num, 0) * weights['cycle']
            )
            final_scores[num] = score
        
        return final_scores
    
    def _select_best_numbers(self, final_scores: Dict[int, float], lottery_type: str) -> List[int]:
        """選擇最佳號碼 - 確定性選擇"""
        
        # 根據彩票類型確定選擇數量
        if lottery_type == 'powercolor':
            select_count = 6
        elif lottery_type == 'lotto649':
            select_count = 6
        else:  # dailycash
            select_count = 5
        
        # 按分數排序
        sorted_numbers = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 選擇前N個高分號碼，但要確保分布合理
        selected = []
        zone_count = defaultdict(int)
        tail_count = defaultdict(int)
        
        for num, score in sorted_numbers:
            if len(selected) >= select_count:
                break
            
            # 檢查區間分布（每個區間最多2個）
            zone = (num - 1) // 6
            if zone_count[zone] >= 2:
                continue
            
            # 檢查尾數分布（同一尾數最多2個）
            tail = num % 10
            if tail_count[tail] >= 2:
                continue
            
            selected.append(num)
            zone_count[zone] += 1
            tail_count[tail] += 1
        
        # 如果還不夠，補充高分號碼
        if len(selected) < select_count:
            for num, score in sorted_numbers:
                if num not in selected:
                    selected.append(num)
                    if len(selected) >= select_count:
                        break
        
        return sorted(selected)
    
    def _select_best_special(self, historical_data: List[Dict], lottery_type: str) -> int:
        """選擇最佳特別號 - 確定性選擇"""
        
        if lottery_type == 'dailycash':
            return None  # 今彩539沒有特別號
        
        # 統計特別號出現頻率
        special_count = Counter()
        for data in historical_data:
            if data.get('special'):
                special_count[data['special']] += 1
        
        if not special_count:
            return 1  # 默認返回1
        
        # 計算加權分數（近期權重更高）
        weighted_scores = defaultdict(float)
        for idx, data in enumerate(historical_data):
            if data.get('special'):
                # 近期數據權重更高
                weight = 1.0 / (idx + 1)
                weighted_scores[data['special']] += weight
        
        # 選擇分數最高的特別號
        if weighted_scores:
            best_special = max(weighted_scores.items(), key=lambda x: x[1])[0]
        else:
            # 如果沒有加權分數，選擇出現次數最多的
            best_special = special_count.most_common(1)[0][0]
        
        return best_special
    
    def _calculate_data_confidence(self, numbers: List[int], special: Optional[int], 
                                  scores: Dict, historical_data: List[Dict]) -> float:
        """計算數據支持的信心度"""
        
        # 基於選中號碼的平均分數
        avg_score = sum(scores.get(num, 0) for num in numbers) / len(numbers) if numbers else 0
        
        # 基於歷史數據量
        data_completeness = min(len(historical_data) / self.analysis_periods, 1.0)
        
        # 基於號碼在近期的表現
        recent_performance = 0
        recent_data = historical_data[:20] if len(historical_data) >= 20 else historical_data
        for data in recent_data:
            matched = len(set(numbers) & set(data['numbers']))
            recent_performance += matched / len(numbers)
        recent_performance = recent_performance / len(recent_data) if recent_data else 0
        
        # 綜合信心度
        confidence = (
            avg_score * 0.5 +           # 綜合分數權重50%
            data_completeness * 0.3 +    # 數據完整性權重30%
            recent_performance * 0.2      # 近期表現權重20%
        )
        
        return min(confidence, 0.95)  # 最高95%信心度
    
    def _generate_analysis_report(self, numbers: List[int], special: Optional[int],
                                 confidence: float, frequency: Dict, trend: Dict,
                                 pattern: Dict, cycle: Dict) -> Dict:
        """生成詳細分析報告"""
        
        report = {
            'selected_numbers': numbers,
            'selected_special': special,
            'confidence_score': confidence,
            'selection_rationale': {
                'frequency_top5': sorted(frequency.items(), key=lambda x: x[1], reverse=True)[:5],
                'trend_top5': sorted(trend.items(), key=lambda x: x[1], reverse=True)[:5],
                'pattern_top5': sorted(pattern.items(), key=lambda x: x[1], reverse=True)[:5],
                'cycle_top5': sorted(cycle.items(), key=lambda x: x[1], reverse=True)[:5]
            },
            'number_characteristics': {
                'odd_even_ratio': f"{sum(1 for n in numbers if n % 2 == 1)}:{sum(1 for n in numbers if n % 2 == 0)}",
                'small_large_ratio': f"{sum(1 for n in numbers if n < 20)}:{sum(1 for n in numbers if n >= 20)}",
                'zone_distribution': self._analyze_number_zones(numbers),
                'tail_distribution': Counter([n % 10 for n in numbers])
            },
            'methodology': '四維度綜合分析：頻率(35%) + 趨勢(25%) + 模式(20%) + 週期(20%)',
            'stability': '此預測基於確定性算法，相同歷史數據下結果保持一致'
        }
        
        return report
    
    def _analyze_number_zones(self, numbers: List[int]) -> Dict:
        """分析號碼區間分布"""
        zones = defaultdict(list)
        for num in numbers:
            zone = (num - 1) // 6 + 1
            zones[f'zone_{zone}'].append(num)
        return dict(zones)
    
    def _get_default_prediction(self, lottery_type: str) -> Dict:
        """無歷史數據時的默認預測"""
        if lottery_type == 'powercolor':
            numbers = [3, 8, 15, 22, 28, 35]
            special = 5
        elif lottery_type == 'lotto649':
            numbers = [7, 14, 21, 28, 35, 42]
            special = 6
        else:  # dailycash
            numbers = [5, 12, 19, 26, 33]
            special = None
        
        return {
            'lottery_type': lottery_type,
            'prediction_numbers': numbers,
            'prediction_special': special,
            'confidence_score': 0.3,
            'prediction_method': '默認預測（無歷史數據）',
            'is_deterministic': True,
            'note': '由於缺乏歷史數據，使用默認號碼組合'
        }


def test_deterministic_predictor():
    """測試確定性預測器"""
    
    predictor = DeterministicConfidencePredictor()
    
    print("=" * 80)
    print("確定性最高信心度預測測試")
    print("=" * 80)
    
    # 測試三次，確認結果一致
    results = []
    for i in range(3):
        print(f"\n第 {i+1} 次預測:")
        result = predictor.get_deterministic_prediction('powercolor')
        
        if result:
            print(f"預測號碼: {result['prediction_numbers']}")
            print(f"特別號: {result['prediction_special']}")
            print(f"信心度: {result['confidence_score']:.2%}")
            print(f"方法: {result['prediction_method']}")
            
            results.append(result['prediction_numbers'])
    
    # 驗證結果一致性
    print("\n" + "=" * 80)
    print("一致性驗證:")
    if len(results) == 3:
        if results[0] == results[1] == results[2]:
            print("✅ 成功！三次預測結果完全一致")
            print(f"穩定預測號碼: {results[0]}")
        else:
            print("❌ 失敗！預測結果不一致")
            for i, r in enumerate(results, 1):
                print(f"第{i}次: {r}")
    
    # 顯示詳細分析
    print("\n" + "=" * 80)
    print("詳細分析報告:")
    if result.get('detailed_analysis'):
        analysis = result['detailed_analysis']
        print(f"\n選號理由:")
        print(f"  頻率前5: {[f'{num}({score:.2f})' for num, score in analysis['selection_rationale']['frequency_top5']]}")
        print(f"  趨勢前5: {[f'{num}({score:.2f})' for num, score in analysis['selection_rationale']['trend_top5']]}")
        print(f"\n號碼特徵:")
        print(f"  奇偶比: {analysis['number_characteristics']['odd_even_ratio']}")
        print(f"  大小比: {analysis['number_characteristics']['small_large_ratio']}")
        print(f"  區間分布: {analysis['number_characteristics']['zone_distribution']}")


if __name__ == "__main__":
    test_deterministic_predictor()