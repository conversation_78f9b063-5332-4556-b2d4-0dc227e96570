database:
  backup_retention: 30
  backup_schedule: 0 2 * * *
deployment:
  backup_enabled: true
  domain: localhost
  environment: production
  monitoring_enabled: true
  ssl_enabled: false
security:
  allowed_hosts:
  - localhost
  - 127.0.0.1
  rate_limit_api: 10r/s
  rate_limit_web: 5r/s
services:
  api:
    host: 0.0.0.0
    port: 8000
    timeout: 30
    workers: 4
  elasticsearch:
    enabled: false
    memory: 512m
  grafana:
    admin_password: admin123
    enabled: true
  prometheus:
    enabled: true
    retention: 30d
  redis:
    enabled: true
    max_memory: 256mb
  web:
    port: 8080
