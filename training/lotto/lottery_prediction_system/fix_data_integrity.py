#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
資料完整性修正工具
修正威力彩資料不一致問題，確保與官方開獎結果同步
"""

import sqlite3
import requests
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import time
import re

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/data_fix.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DataIntegrityFixer:
    """資料完整性修正器"""
    
    def __init__(self, db_path: str = "data/lottery_data.db"):
        self.db_path = db_path
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Referer': 'https://www.taiwanlottery.com.tw/',
            'Origin': 'https://www.taiwanlottery.com.tw'
        })
        
        # 已知的正確資料（從官方網站確認）
        self.correct_data = {
            '114000068': {
                'date': '2025-08-26',
                'numbers': [1, 12, 14, 21, 36, 37],
                'special': 3,
                'source': 'official_verified'
            },
            '114000069': {
                'date': '2025-08-29', 
                'numbers': [9, 13, 14, 17, 23, 30],
                'special': 5,
                'source': 'official_verified'
            }
        }
    
    def connect_db(self) -> sqlite3.Connection:
        """連接資料庫"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def backup_database(self) -> str:
        """備份當前資料庫"""
        import shutil
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = f"{self.db_path}.backup_{timestamp}"
        shutil.copy2(self.db_path, backup_path)
        logger.info(f"✅ 資料庫已備份到: {backup_path}")
        return backup_path
    
    def validate_lottery_numbers(self, numbers: List[int], special: int, 
                               lottery_type: str = 'powercolor') -> Tuple[bool, str]:
        """驗證彩票號碼的合理性"""
        errors = []
        
        # 威力彩驗證規則
        if lottery_type == 'powercolor':
            # 檢查主號碼
            if len(numbers) != 6:
                errors.append(f"主號碼應該是6個，實際: {len(numbers)}")
            
            if any(n < 1 or n > 38 for n in numbers):
                errors.append(f"主號碼應在1-38範圍內，實際: {numbers}")
            
            if len(set(numbers)) != len(numbers):
                errors.append(f"主號碼有重複: {numbers}")
            
            # 檢查特別號
            if special < 1 or special > 8:
                errors.append(f"特別號應在1-8範圍內，實際: {special}")
        
        is_valid = len(errors) == 0
        error_msg = "; ".join(errors) if errors else ""
        
        return is_valid, error_msg
    
    def check_current_data_integrity(self) -> Dict:
        """檢查當前資料完整性"""
        conn = self.connect_db()
        cursor = conn.cursor()
        
        logger.info("🔍 開始檢查資料完整性...")
        
        # 檢查最新10期資料
        cursor.execute("""
            SELECT Period, Sdate, Anumber1, Anumber2, Anumber3, 
                   Anumber4, Anumber5, Anumber6, Second_district
            FROM Powercolor 
            ORDER BY Period DESC LIMIT 10
        """)
        
        results = cursor.fetchall()
        issues = []
        
        for row in results:
            period = str(row['Period'])
            numbers = [row[f'Anumber{i}'] for i in range(1, 7)]
            special = row['Second_district']
            date = row['Sdate']
            
            # 驗證號碼合理性
            is_valid, error_msg = self.validate_lottery_numbers(numbers, special)
            
            if not is_valid:
                issues.append({
                    'period': period,
                    'date': date,
                    'numbers': numbers,
                    'special': special,
                    'error': error_msg,
                    'type': 'invalid_numbers'
                })
                logger.warning(f"❌ 期號 {period} 數據異常: {error_msg}")
            
            # 檢查是否為已知錯誤資料
            if period in self.correct_data:
                correct = self.correct_data[period]
                if (numbers != correct['numbers'] or 
                    special != correct['special']):
                    issues.append({
                        'period': period,
                        'date': date,
                        'numbers': numbers,
                        'special': special,
                        'error': '與官方數據不符',
                        'type': 'data_mismatch',
                        'correct_data': correct
                    })
                    logger.warning(f"❌ 期號 {period} 與官方數據不符")
        
        conn.close()
        
        return {
            'total_checked': len(results),
            'issues_found': len(issues),
            'issues': issues
        }
    
    def fix_known_issues(self) -> Dict:
        """修正已知問題"""
        conn = self.connect_db()
        cursor = conn.cursor()
        
        fixed_count = 0
        errors = []
        
        logger.info("🔧 開始修正已知問題...")
        
        for period, correct_data in self.correct_data.items():
            try:
                # 檢查記錄是否存在
                cursor.execute("SELECT Period FROM Powercolor WHERE Period = ?", (period,))
                exists = cursor.fetchone()
                
                if exists:
                    # 更新現有記錄
                    cursor.execute("""
                        UPDATE Powercolor 
                        SET Sdate = ?, 
                            Anumber1 = ?, Anumber2 = ?, Anumber3 = ?,
                            Anumber4 = ?, Anumber5 = ?, Anumber6 = ?,
                            Second_district = ?
                        WHERE Period = ?
                    """, (
                        correct_data['date'],
                        *correct_data['numbers'],
                        correct_data['special'],
                        period
                    ))
                    logger.info(f"✅ 已更新期號 {period}")
                else:
                    # 插入新記錄
                    cursor.execute("""
                        INSERT INTO Powercolor 
                        (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3,
                         Anumber4, Anumber5, Anumber6, Second_district)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        '威力彩',
                        period,
                        correct_data['date'],
                        *correct_data['numbers'],
                        correct_data['special']
                    ))
                    logger.info(f"✅ 已插入期號 {period}")
                
                fixed_count += 1
                
            except Exception as e:
                error_msg = f"修正期號 {period} 時發生錯誤: {str(e)}"
                errors.append(error_msg)
                logger.error(error_msg)
        
        # 提交變更
        conn.commit()
        conn.close()
        
        return {
            'fixed_count': fixed_count,
            'errors': errors
        }
    
    def remove_invalid_data(self) -> Dict:
        """移除明顯異常的資料"""
        conn = self.connect_db()
        cursor = conn.cursor()
        
        removed_count = 0
        errors = []
        
        logger.info("🗑️ 檢查並移除異常資料...")
        
        try:
            # 查找有重複號碼的記錄（明顯錯誤）
            cursor.execute("""
                SELECT Period, Anumber1, Anumber2, Anumber3, 
                       Anumber4, Anumber5, Anumber6, Second_district
                FROM Powercolor 
                ORDER BY Period DESC LIMIT 20
            """)
            
            records = cursor.fetchall()
            periods_to_remove = []
            
            for row in records:
                period = row['Period']
                numbers = [row[f'Anumber{i}'] for i in range(1, 7)]
                
                # 檢查是否有重複號碼
                if len(set(numbers)) != len(numbers):
                    periods_to_remove.append(period)
                    logger.warning(f"🗑️ 發現重複號碼，準備移除期號: {period}, 號碼: {numbers}")
            
            # 移除異常記錄
            for period in periods_to_remove:
                # 先確認這不是正確的資料（有些資料可能真的有這種情況）
                if str(period) not in self.correct_data:  # 不是已知正確資料
                    cursor.execute("DELETE FROM Powercolor WHERE Period = ?", (period,))
                    removed_count += 1
                    logger.info(f"✅ 已移除異常期號: {period}")
            
            conn.commit()
            
        except Exception as e:
            error_msg = f"移除異常資料時發生錯誤: {str(e)}"
            errors.append(error_msg)
            logger.error(error_msg)
        
        conn.close()
        
        return {
            'removed_count': removed_count,
            'errors': errors
        }
    
    def generate_report(self) -> str:
        """生成修正報告"""
        report_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 重新檢查資料完整性
        integrity_check = self.check_current_data_integrity()
        
        report = f"""
# 資料完整性修正報告

**修正時間**: {report_time}
**資料庫路徑**: {self.db_path}

## 📊 修正摘要

- **檢查筆數**: {integrity_check['total_checked']}
- **發現問題**: {integrity_check['issues_found']}

## 📝 修正內容

### 已知問題修正
"""
        
        for period, data in self.correct_data.items():
            numbers_str = ','.join(f"{n:02d}" for n in data['numbers'])
            report += f"- **期號 {period}**: {numbers_str}+{data['special']:02d} (日期: {data['date']})\n"
        
        report += f"""
### 剩餘問題

"""
        
        if integrity_check['issues']:
            for issue in integrity_check['issues']:
                numbers_str = ','.join(f"{n:02d}" for n in issue['numbers'])
                report += f"- **期號 {issue['period']}**: {numbers_str}+{issue['special']:02d} - {issue['error']}\n"
        else:
            report += "✅ 沒有發現剩餘問題\n"
        
        return report

def main():
    """主函數"""
    logger.info("🚀 開始資料完整性修正程序")
    
    fixer = DataIntegrityFixer()
    
    try:
        # 1. 備份資料庫
        backup_path = fixer.backup_database()
        
        # 2. 檢查資料完整性
        integrity_check = fixer.check_current_data_integrity()
        logger.info(f"📊 完整性檢查完成: 發現 {integrity_check['issues_found']} 個問題")
        
        # 3. 修正已知問題
        fix_result = fixer.fix_known_issues()
        logger.info(f"🔧 修正完成: 修正了 {fix_result['fixed_count']} 筆記錄")
        
        # 4. 移除明顯異常的資料
        remove_result = fixer.remove_invalid_data()
        logger.info(f"🗑️ 清理完成: 移除了 {remove_result['removed_count']} 筆異常記錄")
        
        # 5. 生成報告
        report = fixer.generate_report()
        
        # 儲存報告
        report_path = f"logs/data_fix_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"✅ 修正完成！報告已儲存到: {report_path}")
        print(f"\n{report}")
        
    except Exception as e:
        logger.error(f"❌ 修正程序發生錯誤: {str(e)}")
        raise

if __name__ == "__main__":
    main()