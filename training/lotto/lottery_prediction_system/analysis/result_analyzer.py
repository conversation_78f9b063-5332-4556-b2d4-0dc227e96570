"""
彩票預測系統 - 結果分析模組
負責分析預測結果與實際開獎結果的差異
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import logging
import os
import json
import traceback
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/result_analyzer.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('result_analyzer')

class PredictionAnalyzer:
    def __init__(self, db_manager=None, output_dir='analysis_results'):
        """
        初始化預測結果分析器
        
        Args:
            db_manager: 資料庫管理器實例，用於查詢資料
            output_dir: 分析結果輸出目錄
        """
        self.db_manager = db_manager
        self.output_dir = output_dir
        
        # 確保輸出目錄存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def load_prediction_history(self, db_manager, lottery_type='powercolor', limit=None):
        """從資料庫加載預測歷史數據"""
        try:
            logger.info("從資料庫加載預測歷史...")
            prediction_df = db_manager.load_prediction_records(lottery_type, limit)
            
            logger.info(f"成功加載 {len(prediction_df)} 筆預測記錄")
            return prediction_df
        except Exception as e:
            logger.error(f"加載預測歷史時出錯: {str(e)}")
            logger.error(traceback.format_exc())
            return pd.DataFrame()
    
    def analyze_accuracy(self, lottery_type='powercolor', period_range=None, prediction_methods=None):
        """
        分析預測準確性
        
        Args:
            lottery_type: 彩票類型 ('powercolor'=威力彩, 'lotto649'=大樂透, 'dailycash'=今彩539)
            period_range: 期數範圍元組 (開始期數, 結束期數)
            prediction_methods: 要分析的預測方法列表
            
        Returns:
            dict: 準確性分析結果
        """
        logger.info(f"開始分析{self._get_lottery_name(lottery_type)}預測準確性...")
        
        try:
            # 如果沒有提供資料庫管理器，無法進行分析
            if not self.db_manager:
                logger.error("未提供資料庫管理器，無法分析預測準確性")
                return {'error': "未提供資料庫管理器"}
            
            # 使用提供的db_manager加載預測記錄
            prediction_df = self.load_prediction_history(self.db_manager, lottery_type)
            
            if prediction_df.empty:
                logger.warning(f"找不到{self._get_lottery_name(lottery_type)}的預測記錄")
                return {'warning': "找不到預測記錄"}
            
            # 分析預測準確度
            analysis_results = self.analyze_prediction_accuracy(prediction_df)
            
            logger.info(f"{self._get_lottery_name(lottery_type)}預測準確性分析完成")
            return analysis_results
            
        except Exception as e:
            logger.error(f"分析預測準確性時出錯: {str(e)}")
            logger.error(traceback.format_exc())
            return {'error': str(e)}
    
    def analyze_method_comparison(self, lottery_type='powercolor', period_count=10):
        """
        比較不同預測方法的效果
        
        Args:
            lottery_type: 彩票類型
            period_count: 要分析的最近期數
            
        Returns:
            dict: 方法比較結果
        """
        logger.info(f"開始比較{self._get_lottery_name(lottery_type)}不同預測方法...")
        
        try:
            # 如果沒有提供資料庫管理器，無法進行分析
            if not self.db_manager:
                logger.error("未提供資料庫管理器，無法比較預測方法")
                return {'error': "未提供資料庫管理器"}
            
            # 加載最近的預測記錄
            prediction_df = self.load_prediction_history(self.db_manager, lottery_type, period_count)
            
            if prediction_df.empty:
                logger.warning(f"找不到{self._get_lottery_name(lottery_type)}的預測記錄")
                return {'warning': "找不到預測記錄"}
            
            # 分析不同預測方法的效果
            methods_comparison = self.analyze_prediction_methods(prediction_df)
            
            logger.info(f"{self._get_lottery_name(lottery_type)}預測方法比較完成")
            return methods_comparison
            
        except Exception as e:
            logger.error(f"比較預測方法時出錯: {str(e)}")
            logger.error(traceback.format_exc())
            return {'error': str(e)}
    
    def analyze_prediction_accuracy(self, prediction_df):
        """分析預測準確度"""
        try:
            logger.info("分析預測準確度...")
            
            # 只分析有實際結果的記錄
            completed_df = prediction_df.dropna(subset=['ActualA1'])
            
            if len(completed_df) == 0:
                logger.warning("沒有找到有實際結果的預測記錄")
                return None
            
            # 計算各種匹配水平的次數和比例
            total = len(completed_df)
            
            # 初始化計數器
            match_counts = {i: 0 for i in range(7)}  # 0-6個匹配
            second_match = 0
            prize_counts = {
                '頭獎': 0,
                '二獎': 0,
                '三獎': 0,
                '四獎': 0,
                '五獎': 0,
                '六獎': 0,
                '七獎': 0,
                '八獎': 0,
                '九獎': 0,
                '未中獎': 0
            }
            
            # 判断彩票类型
            is_dailycash = ('PredA5' in prediction_df.columns and 'ActualA5' in prediction_df.columns and 
                             'PredA6' not in prediction_df.columns and 'ActualA6' not in prediction_df.columns)
            
            # 分析每筆記錄
            for _, row in completed_df.iterrows():
                if is_dailycash:  # 今彩539
                    # 提取預測和實際號碼
                    pred_nums = {row['PredA1'], row['PredA2'], row['PredA3'], 
                                row['PredA4'], row['PredA5']}
                    actual_nums = {row['ActualA1'], row['ActualA2'], row['ActualA3'], 
                                  row['ActualA4'], row['ActualA5']}
                    
                    # 計算匹配數
                    match_count = len(pred_nums.intersection(actual_nums))
                    match_counts[match_count] += 1
                    
                    # 判斷中獎情況 (今彩539規則)
                    if match_count == 5:
                        prize_counts['頭獎'] += 1
                    elif match_count == 4:
                        prize_counts['二獎'] += 1
                    elif match_count == 3:
                        prize_counts['三獎'] += 1
                    elif match_count == 2:
                        prize_counts['四獎'] += 1
                    else:
                        prize_counts['未中獎'] += 1
                else:  # 威力彩或大樂透
                    # 提取預測和實際號碼
                    pred_first = {row['PredA1'], row['PredA2'], row['PredA3'], 
                                 row['PredA4'], row['PredA5'], row['PredA6']}
                    actual_first = {row['ActualA1'], row['ActualA2'], row['ActualA3'], 
                                   row['ActualA4'], row['ActualA5'], row['ActualA6']}
                    
                    # 計算第一區匹配數
                    match_count = len(pred_first.intersection(actual_first))
                    match_counts[match_count] += 1
                    
                    # 檢查第二區是否匹配
                    if 'PredS' in row and 'ActualS' in row:  # 威力彩
                        is_second_match = row['PredS'] == row['ActualS']
                        if is_second_match:
                            second_match += 1
                        
                        # 判斷中獎情況
                        if match_count == 6 and is_second_match:
                            prize_counts['頭獎'] += 1
                        elif match_count == 6:
                            prize_counts['二獎'] += 1
                        elif match_count == 5 and is_second_match:
                            prize_counts['三獎'] += 1
                        elif match_count == 5:
                            prize_counts['四獎'] += 1
                        elif match_count == 4 and is_second_match:
                            prize_counts['五獎'] += 1
                        elif match_count == 4:
                            prize_counts['六獎'] += 1
                        elif match_count == 3 and is_second_match:
                            prize_counts['七獎'] += 1
                        elif match_count == 2 and is_second_match:
                            prize_counts['八獎'] += 1
                        elif match_count == 3:
                            prize_counts['九獎'] += 1
                        else:
                            prize_counts['未中獎'] += 1
                    
                    elif 'PredSpecial' in row and 'ActualSpecial' in row:  # 大樂透
                        is_special_match = row['PredSpecial'] == row['ActualSpecial']
                        if is_special_match:
                            second_match += 1
                        
                        # 判斷中獎情況（大樂透規則）
                        if match_count == 6:
                            prize_counts['頭獎'] += 1
                        elif match_count == 5 and is_special_match:
                            prize_counts['二獎'] += 1
                        elif match_count == 5:
                            prize_counts['三獎'] += 1
                        elif match_count == 4 and is_special_match:
                            prize_counts['四獎'] += 1
                        elif match_count == 4:
                            prize_counts['五獎'] += 1
                        elif match_count == 3 and is_special_match:
                            prize_counts['六獎'] += 1
                        elif match_count == 2 and is_special_match:
                            prize_counts['七獎'] += 1
                        elif match_count == 3:
                            prize_counts['八獎'] += 1
                        else:
                            prize_counts['未中獎'] += 1
            
            # 計算百分比
            match_percentages = {k: v / total * 100 for k, v in match_counts.items()}
            second_match_percentage = second_match / total * 100 if not is_dailycash else 0
            prize_percentages = {k: v / total * 100 for k, v in prize_counts.items()}
            
            # 計算平均匹配數
            avg_match = sum(k * v for k, v in match_counts.items()) / total
            
            # 計算超過特定匹配數的比例
            cumulative_percentages = {}
            for i in range(7 if not is_dailycash else 6):
                cumulative_percentages[f'匹配{i}個或以上'] = sum(match_counts[j] for j in range(i, 7 if not is_dailycash else 6)) / total * 100
            
            # 按月份分析匹配情況
            completed_df['YearMonth'] = pd.to_datetime(completed_df['PredictionDate']).dt.strftime('%Y-%m')
            monthly_stats = completed_df.groupby('YearMonth').agg({
                'MatchCount': 'mean'
            })
            
            if not is_dailycash and 'SecondMatch' in completed_df.columns:
                monthly_stats['SecondMatch'] = completed_df.groupby('YearMonth')['SecondMatch'].mean()
            
            # 整理分析結果
            analysis_results = {
                'total_predictions': total,
                'match_counts': {int(k): int(v) for k, v in match_counts.items()},
                'match_percentages': {int(k): float(v) for k, v in match_percentages.items()},
                'prize_counts': {k: int(v) for k, v in prize_counts.items()},
                'prize_percentages': {k: float(v) for k, v in prize_percentages.items()},
                'average_match': float(avg_match),
                'cumulative_percentages': {k: float(v) for k, v in cumulative_percentages.items()},
                'monthly_stats': monthly_stats.to_dict(),
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            if not is_dailycash:
                analysis_results['second_match_count'] = int(second_match)
                analysis_results['second_match_percentage'] = float(second_match_percentage)
            
            # 保存分析結果
            self.save_analysis_results(analysis_results)
            
            # 繪製圖表
            self.plot_analysis_results(analysis_results)
            
            logger.info("預測準確度分析完成")
            return analysis_results
            
        except Exception as e:
            logger.error(f"分析預測準確度時出錯: {str(e)}")
            logger.error(traceback.format_exc())
            return None
    
    def save_analysis_results(self, analysis_results, filename=None):
        """保存分析結果到文件"""
        if filename is None:
            filename = f"prediction_analysis_{datetime.now().strftime('%Y%m%d')}.json"
        
        try:
            filepath = os.path.join(self.output_dir, filename)
            with open(filepath, 'w', encoding='utf-8') as f:
                # 處理不可序列化的類型
                def json_serial(obj):
                    if isinstance(obj, (np.integer, np.int64)):
                        return int(obj)
                    elif isinstance(obj, (np.floating, np.float64)):
                        return float(obj)
                    elif isinstance(obj, datetime):
                        return obj.strftime('%Y-%m-%d %H:%M:%S')
                    elif isinstance(obj, np.ndarray):
                        return obj.tolist()
                    raise TypeError(f"Type {type(obj)} not serializable")
                
                json.dump(analysis_results, f, ensure_ascii=False, indent=4, default=json_serial)
            
            logger.info(f"分析結果已保存到 {filepath}")
            return True
        except Exception as e:
            logger.error(f"保存分析結果時出錯: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def _determine_lottery_type(self, df):
        """根據DataFrame結構判斷彩票類型"""
        # 檢查列名判斷彩票類型
        if 'PredS' in df.columns:
            return 'powercolor'
        elif 'PredSpecial' in df.columns:
            return 'lotto649'
        else:
            return 'dailycash'  # 假設如果既沒有PredS也沒有PredSpecial，就是今彩539
    
    def plot_analysis_results(self, analysis_results):
        """繪製分析結果的圖表"""
        try:
            logger.info("繪製分析結果圖表...")
            
            # 1. 匹配數量分佈圖
            plt.figure(figsize=(12, 8))
            
            match_counts = analysis_results['match_counts']
            plt.bar(match_counts.keys(), match_counts.values())
            plt.title('第一區號碼匹配數量分佈')
            plt.xlabel('匹配數量')
            plt.ylabel('次數')
            plt.xticks(range(7))
            plt.grid(axis='y', alpha=0.3)
            
            # 添加數值標籤
            for k, v in match_counts.items():
                plt.text(k, v + 0.5, str(v), ha='center')
            
            plt.savefig(os.path.join(self.output_dir, 'match_distribution.png'))
            plt.close()
            
            # 2. 中獎情況餅圖
            plt.figure(figsize=(12, 8))
            
            # 過濾掉0值以避免餅圖中的空扇區
            prize_counts = {k: v for k, v in analysis_results['prize_counts'].items() if v > 0}
            
            if prize_counts:
                labels = prize_counts.keys()
                sizes = prize_counts.values()
                
                plt.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
                plt.axis('equal')
                plt.title('預測中獎情況分佈')
                
                plt.savefig(os.path.join(self.output_dir, 'prize_distribution.png'))
            plt.close()
            
            # 3. 月度趨勢圖
            if analysis_results['monthly_stats']:
                monthly_stats = analysis_results['monthly_stats']
                
                if 'MatchCount' in monthly_stats:
                    months = list(monthly_stats['MatchCount'].keys())
                    match_means = list(monthly_stats['MatchCount'].values())
                    
                    if len(months) > 1:
                        plt.figure(figsize=(14, 7))
                        plt.plot(months, match_means, marker='o', linestyle='-')
                        plt.title('月度平均匹配數量趨勢')
                        plt.xlabel('月份')
                        plt.ylabel('平均匹配數量')
                        plt.grid(True, alpha=0.3)
                        plt.xticks(rotation=45)
                        
                        plt.savefig(os.path.join(self.output_dir, 'monthly_match_trend.png'))
                        plt.close()
                
                if 'SecondMatch' in monthly_stats:
                    months = list(monthly_stats['SecondMatch'].keys())
                    second_means = list(monthly_stats['SecondMatch'].values())
                    
                    if len(months) > 1:
                        plt.figure(figsize=(14, 7))
                        plt.plot(months, [x * 100 for x in second_means], marker='o', linestyle='-', color='orange')
                        plt.title('月度第二區匹配率趨勢')
                        plt.xlabel('月份')
                        plt.ylabel('匹配率 (%)')
                        plt.grid(True, alpha=0.3)
                        plt.xticks(rotation=45)
                        
                        plt.savefig(os.path.join(self.output_dir, 'monthly_second_trend.png'))
                        plt.close()
            
            logger.info(f"圖表已保存到 {self.output_dir} 目錄")
            return True
        except Exception as e:
            logger.error(f"繪製圖表時出錯: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def analyze_prediction_methods(self, prediction_df):
        """分析不同預測方法的效果"""
        try:
            logger.info("分析不同預測方法的效果...")
            
            # 確保有模型版本和方法列
            if 'ModelVersion' not in prediction_df.columns:
                prediction_df['ModelVersion'] = 'unknown'
            
            # 定義一個PredictionMethod列（如果不存在）
            if 'PredictionMethod' not in prediction_df.columns:
                # 從詳細信息中提取，或設為默認值
                prediction_df['PredictionMethod'] = 'unknown'
            
            # 只分析有實際結果的記錄
            completed_df = prediction_df.dropna(subset=['ActualA1'])
            
            if len(completed_df) == 0:
                logger.warning("沒有找到有實際結果的預測記錄")
                return None
            
            # 按預測方法分組
            method_groups = completed_df.groupby('PredictionMethod')
            
            methods_analysis = {}
            
            # 判斷彩票類型
            is_dailycash = ('PredA5' in prediction_df.columns and 'ActualA5' in prediction_df.columns and 
                             'PredA6' not in prediction_df.columns and 'ActualA6' not in prediction_df.columns)
            
            for method, group in method_groups:
                group_size = len(group)
                
                # 計算平均匹配數
                avg_match = group['MatchCount'].mean()
                
                # 初始化分析結果
                method_analysis = {
                    'sample_size': group_size,
                    'average_match': avg_match,
                }
                
                # 根據彩種類型添加不同的分析內容
                if is_dailycash:  # 今彩539
                    # 計算各種匹配水平的比例
                    match_levels = {}
                    for i in range(6):  # 0-5個匹配
                        match_levels[f'匹配{i}個'] = len(group[group['MatchCount'] == i]) / group_size * 100
                    
                    # 計算中獎率 (今彩539規則)
                    prize_counts = {}
                    prize_counts['頭獎'] = len(group[group['MatchCount'] == 5]) / group_size * 100
                    prize_counts['貳獎'] = len(group[group['MatchCount'] == 4]) / group_size * 100
                    prize_counts['參獎'] = len(group[group['MatchCount'] == 3]) / group_size * 100
                    prize_counts['肆獎'] = len(group[group['MatchCount'] == 2]) / group_size * 100
                    
                    # 任何獎項的總體中獎率
                    any_prize = sum(prize_counts.values())
                    
                    # 添加到分析結果
                    method_analysis.update({
                        'match_levels': match_levels,
                        'prize_percentages': prize_counts,
                        'any_prize_rate': any_prize
                    })
                    
                else:  # 威力彩或大樂透
                    # 計算第二區匹配率
                    if 'SecondMatch' in group.columns:
                        second_match_rate = group['SecondMatch'].mean() * 100
                        method_analysis['second_match_rate'] = second_match_rate
                    elif 'SpecialMatch' in group.columns:
                        special_match_rate = group['SpecialMatch'].mean() * 100
                        method_analysis['special_match_rate'] = special_match_rate
                    
                    # 計算各種匹配水平的比例
                    match_levels = {}
                    for i in range(7):  # 0-6個匹配
                        match_levels[f'匹配{i}個'] = len(group[group['MatchCount'] == i]) / group_size * 100
                    
                    method_analysis['match_levels'] = match_levels
                    
                    # 計算中獎率 (根據彩種不同)
                    prize_counts = {}
                    
                    if 'SecondMatch' in group.columns:  # 威力彩
                        # 計算頭獎到九獎的比例
                        prize_counts['頭獎'] = len(group[(group['MatchCount'] == 6) & (group['SecondMatch'] == 1)]) / group_size * 100
                        prize_counts['二獎'] = len(group[(group['MatchCount'] == 6) & (group['SecondMatch'] == 0)]) / group_size * 100
                        prize_counts['三獎'] = len(group[(group['MatchCount'] == 5) & (group['SecondMatch'] == 1)]) / group_size * 100
                        prize_counts['四獎'] = len(group[(group['MatchCount'] == 5) & (group['SecondMatch'] == 0)]) / group_size * 100
                        prize_counts['五獎'] = len(group[(group['MatchCount'] == 4) & (group['SecondMatch'] == 1)]) / group_size * 100
                        prize_counts['六獎'] = len(group[(group['MatchCount'] == 4) & (group['SecondMatch'] == 0)]) / group_size * 100
                        prize_counts['七獎'] = len(group[(group['MatchCount'] == 3) & (group['SecondMatch'] == 1)]) / group_size * 100
                        prize_counts['八獎'] = len(group[(group['MatchCount'] == 2) & (group['SecondMatch'] == 1)]) / group_size * 100
                        prize_counts['九獎'] = len(group[(group['MatchCount'] == 3) & (group['SecondMatch'] == 0)]) / group_size * 100
                    elif 'SpecialMatch' in group.columns:  # 大樂透
                        # 根據大樂透的中獎規則計算
                        prize_counts['普獎'] = len(group[(group['MatchCount'] == 3) & (group['SpecialMatch'] == 0)]) / group_size * 100
                        prize_counts['柒獎'] = len(group[(group['MatchCount'] == 2) & (group['SpecialMatch'] == 1)]) / group_size * 100
                        prize_counts['陸獎'] = len(group[(group['MatchCount'] == 3) & (group['SpecialMatch'] == 1)]) / group_size * 100
                        prize_counts['伍獎'] = len(group[(group['MatchCount'] == 4) & (group['SpecialMatch'] == 0)]) / group_size * 100
                        prize_counts['肆獎'] = len(group[(group['MatchCount'] == 4) & (group['SpecialMatch'] == 1)]) / group_size * 100
                        prize_counts['參獎'] = len(group[(group['MatchCount'] == 5) & (group['SpecialMatch'] == 0)]) / group_size * 100
                        prize_counts['貳獎'] = len(group[(group['MatchCount'] == 5) & (group['SpecialMatch'] == 1)]) / group_size * 100
                        prize_counts['頭獎'] = len(group[(group['MatchCount'] == 6)]) / group_size * 100
                    
                    # 任何獎項的總體中獎率
                    any_prize = sum(prize_counts.values())
                    
                    # 添加到分析結果
                    method_analysis.update({
                        'prize_percentages': prize_counts,
                        'any_prize_rate': any_prize
                    })
                
                # 保存此方法的分析結果
                methods_analysis[method] = method_analysis
            
            # 按模型版本分組進行額外分析
            version_groups = completed_df.groupby('ModelVersion')
            versions_analysis = {}
            
            for version, group in version_groups:
                group_size = len(group)
                
                # 計算平均匹配數
                avg_match = group['MatchCount'].mean()
                
                # 初始化版本分析
                version_analysis = {
                    'sample_size': group_size,
                    'average_match': avg_match,
                }
                
                # 添加彩種特定的分析
                if is_dailycash:  # 今彩539
                    # 計算任何獎項的中獎率
                    prize_rate = len(group[group['MatchCount'] >= 2]) / group_size * 100
                    version_analysis['prize_rate'] = prize_rate
                else:  # 威力彩或大樂透
                    # 計算第二區匹配率
                    if 'SecondMatch' in group.columns:
                        second_match_rate = group['SecondMatch'].mean() * 100
                        version_analysis['second_match_rate'] = second_match_rate
                    elif 'SpecialMatch' in group.columns:
                        special_match_rate = group['SpecialMatch'].mean() * 100
                        version_analysis['special_match_rate'] = special_match_rate
                    
                    # 計算任何獎項的中獎率 (根據彩種不同)
                    if 'SecondMatch' in group.columns:  # 威力彩
                        prize_rate = (
                            len(group[(group['MatchCount'] >= 3) | 
                                ((group['MatchCount'] == 2) & (group['SecondMatch'] == 1))]) / group_size * 100
                        )
                    else:  # 大樂透
                        prize_rate = (
                            len(group[(group['MatchCount'] >= 3) | 
                                ((group['MatchCount'] == 2) & (group['SpecialMatch'] == 1))]) / group_size * 100
                        )
                    version_analysis['prize_rate'] = prize_rate
                
                versions_analysis[version] = version_analysis
            
            # 整合分析結果
            methods_comparison = {
                'methods_analysis': methods_analysis,
                'versions_analysis': versions_analysis,
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 保存分析結果
            try:
                filepath = os.path.join(self.output_dir, 'methods_comparison.json')
                with open(filepath, 'w', encoding='utf-8') as f:
                    def json_serial(obj):
                        if isinstance(obj, (np.integer, np.int64)):
                            return int(obj)
                        elif isinstance(obj, (np.floating, np.float64)):
                            return float(obj)
                        elif isinstance(obj, datetime):
                            return obj.strftime('%Y-%m-%d %H:%M:%S')
                        elif isinstance(obj, np.ndarray):
                            return obj.tolist()
                        raise TypeError(f"Type {type(obj)} not serializable")
                    
                    json.dump(methods_comparison, f, ensure_ascii=False, indent=4, default=json_serial)
            except Exception as e:
                logger.error(f"保存方法比較分析結果時出錯: {str(e)}")
                logger.error(traceback.format_exc())
            
            # 繪製方法比較圖
            self.plot_methods_comparison(methods_analysis, versions_analysis)
            
            # 儲存分析結果到實例變數
            self.methods_comparison = methods_comparison
            
            logger.info("不同預測方法的效果分析完成")
            return methods_comparison
            
        except Exception as e:
            logger.error(f"分析不同預測方法的效果時出錯: {str(e)}")
            logger.error(traceback.format_exc())
            return None
    
    def plot_methods_comparison(self, methods_analysis, versions_analysis):
        """繪製不同預測方法的比較圖"""
        try:
            # 1. 平均匹配數比較
            plt.figure(figsize=(12, 7))
            
            methods = list(methods_analysis.keys())
            avg_matches = [data['average_match'] for data in methods_analysis.values()]
            
            plt.bar(methods, avg_matches)
            plt.title('不同預測方法的平均匹配數比較')
            plt.xlabel('預測方法')
            plt.ylabel('平均匹配數')
            plt.grid(axis='y', alpha=0.3)
            
            # 添加數值標籤
            for i, v in enumerate(avg_matches):
                plt.text(i, v + 0.1, f"{v:.2f}", ha='center')
            
            plt.savefig(os.path.join(self.output_dir, 'methods_avg_match.png'))
            plt.close()
            
            # 2. 任何獎項中獎率比較
            plt.figure(figsize=(12, 7))
            
            methods = list(methods_analysis.keys())
            prize_rates = [data.get('any_prize_rate', 0) for data in methods_analysis.values()]
            
            plt.bar(methods, prize_rates, color='green')
            plt.title('不同預測方法的中獎率比較')
            plt.xlabel('預測方法')
            plt.ylabel('中獎率 (%)')
            plt.grid(axis='y', alpha=0.3)
            
            # 添加數值標籤
            for i, v in enumerate(prize_rates):
                plt.text(i, v + 1, f"{v:.2f}%", ha='center')
            
            plt.savefig(os.path.join(self.output_dir, 'methods_prize_rate.png'))
            plt.close()
            
            # 3. 模型版本比較
            if len(versions_analysis) > 1:
                plt.figure(figsize=(14, 7))
                
                versions = list(versions_analysis.keys())
                avg_matches = [data['average_match'] for data in versions_analysis.values()]
                
                # 檢查不同彩票類型的指標
                has_second_rate = all('second_match_rate' in data for data in versions_analysis.values())
                has_special_rate = all('special_match_rate' in data for data in versions_analysis.values())
                
                # 創建分組條形圖
                width = 0.25
                x = np.arange(len(versions))
                
                plt.bar(x - width, avg_matches, width, label='平均匹配數')
                
                if has_second_rate:
                    second_rates = [data['second_match_rate'] for data in versions_analysis.values()]
                    plt.bar(x, second_rates, width, label='第二區匹配率 (%)')
                elif has_special_rate:
                    special_rates = [data['special_match_rate'] for data in versions_analysis.values()]
                    plt.bar(x, special_rates, width, label='特別號匹配率 (%)')
                
                prize_rates = [data['prize_rate'] for data in versions_analysis.values()]
                plt.bar(x + width, prize_rates, width, label='中獎率 (%)')
                
                plt.title('不同模型版本性能比較')
                plt.xlabel('模型版本')
                plt.ylabel('指標值')
                plt.xticks(x, versions)
                plt.legend()
                plt.grid(axis='y', alpha=0.3)
                
                plt.savefig(os.path.join(self.output_dir, 'versions_comparison.png'))
                plt.close()
            
            logger.info("預測方法比較圖表已生成")
            return True
        except Exception as e:
            logger.error(f"繪製預測方法比較圖時出錯: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def generate_detailed_report(self, analysis_results, prediction_df, filename=None):
        """生成詳細的分析報告"""
        if filename is None:
            filename = f"detailed_report_{datetime.now().strftime('%Y%m%d')}.txt"
        
        try:
            filepath = os.path.join(self.output_dir, filename)
            
            # 判斷彩票類型
            is_dailycash = ('PredA5' in prediction_df.columns and 'ActualA5' in prediction_df.columns and 
                            'PredA6' not in prediction_df.columns and 'ActualA6' not in prediction_df.columns)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                # 根據彩種類型選擇標題
                if is_dailycash:
                    f.write("今彩539預測系統 - 詳細分析報告\n")
                elif 'PredS' in prediction_df.columns:
                    f.write("威力彩預測系統 - 詳細分析報告\n")
                else:
                    f.write("大樂透預測系統 - 詳細分析報告\n")
                    
                f.write("=" * 80 + "\n")
                f.write(f"生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 80 + "\n\n")
                
                # 1. 基本統計
                f.write("1. 基本統計\n")
                f.write("-" * 50 + "\n")
                f.write(f"分析樣本數: {analysis_results['total_predictions']} 筆\n")
                f.write(f"平均匹配數: {analysis_results['average_match']:.2f}\n")
                
                # 根據彩種類型顯示不同的統計信息
                if not is_dailycash and 'second_match_percentage' in analysis_results:
                    f.write(f"第二區匹配率: {analysis_results['second_match_percentage']:.2f}%\n\n")
                else:
                    f.write("\n")
                
                # 2. 匹配數量分佈
                f.write("2. 匹配數量分佈\n")
                f.write("-" * 50 + "\n")
                for k, v in analysis_results['match_counts'].items():
                    percentage = analysis_results['match_percentages'][k]
                    f.write(f"匹配 {k} 個號碼: {v} 次 ({percentage:.2f}%)\n")
                f.write("\n")
                
                # 3. 中獎情況
                f.write("3. 中獎情況\n")
                f.write("-" * 50 + "\n")
                for prize, count in analysis_results['prize_counts'].items():
                    if count > 0:
                        percentage = analysis_results['prize_percentages'][prize]
                        f.write(f"{prize}: {count} 次 ({percentage:.2f}%)\n")
                
                # 計算任何獎項的總體中獎率
                total_prize = sum(v for k, v in analysis_results['prize_counts'].items() if k != '未中獎')
                total_prize_rate = total_prize / analysis_results['total_predictions'] * 100
                f.write(f"\n總體中獎率: {total_prize} / {analysis_results['total_predictions']} ({total_prize_rate:.2f}%)\n\n")
                
                # 4. 累積匹配率
                f.write("4. 累積匹配率\n")
                f.write("-" * 50 + "\n")
                for k, v in analysis_results['cumulative_percentages'].items():
                    f.write(f"{k}: {v:.2f}%\n")
                f.write("\n")
                
                # 5. 月度趨勢 (如果存在)
                if ('monthly_stats' in analysis_results and analysis_results['monthly_stats'] and 
                    'MatchCount' in analysis_results['monthly_stats']):
                    f.write("5. 月度趨勢\n")
                    f.write("-" * 50 + "\n")
                    months = analysis_results['monthly_stats']['MatchCount'].keys()
                    
                    for month in months:
                        monthly_avg = analysis_results['monthly_stats']['MatchCount'][month]
                        report_content = f"{month}: 平均匹配 {monthly_avg:.2f} 個"
                        
                        # 安全檢查：添加特別號匹配率（如果有的話）
                        if is_dailycash:
                            f.write(f"{report_content}\n")
                        elif 'PredS' in prediction_df.columns:  # 威力彩
                            if 'SecondMatch' in analysis_results['monthly_stats'] and month in analysis_results['monthly_stats']['SecondMatch']:
                                second_rate = analysis_results['monthly_stats']['SecondMatch'][month] * 100
                                f.write(f"{report_content}, 第二區匹配率 {second_rate:.2f}%\n")
                            else:
                                f.write(f"{report_content}\n")
                        elif 'PredSpecial' in prediction_df.columns:  # 大樂透
                            if 'SpecialMatch' in analysis_results['monthly_stats'] and month in analysis_results['monthly_stats']['SpecialMatch']:
                                special_rate = analysis_results['monthly_stats']['SpecialMatch'][month] * 100
                                f.write(f"{report_content}, 特別號匹配率 {special_rate:.2f}%\n")
                            else:
                                f.write(f"{report_content}\n")
                    f.write("\n")
                
                # 6. 預測方法比較（如果有）
                if hasattr(self, 'methods_comparison') and self.methods_comparison:
                    f.write("6. 預測方法比較\n")
                    f.write("-" * 50 + "\n")
                    
                    for method, data in self.methods_comparison['methods_analysis'].items():
                        f.write(f"\n預測方法: {method} (樣本數: {data['sample_size']})\n")
                        f.write(f"  平均匹配數: {data['average_match']:.2f}\n")
                        
                        # 針對不同彩票類型顯示不同信息
                        if not is_dailycash and 'second_match_rate' in data:
                            f.write(f"  第二區匹配率: {data['second_match_rate']:.2f}%\n")
                        elif not is_dailycash and 'special_match_rate' in data:
                            f.write(f"  特別號匹配率: {data['special_match_rate']:.2f}%\n")
                        
                        f.write(f"  總體中獎率: {data['any_prize_rate']:.2f}%\n")
                        
                        f.write("  中獎分佈:\n")
                        for prize, rate in data['prize_percentages'].items():
                            if rate > 0:
                                f.write(f"    {prize}: {rate:.2f}%\n")
                    f.write("\n")
                
                # 7. 最近預測結果
                recent_predictions = prediction_df.sort_values('PredictionDate', ascending=False).head(5)
                
                if not recent_predictions.empty:
                    f.write("7. 最近5筆預測結果\n")
                    f.write("-" * 50 + "\n")
                    
                    for i, row in recent_predictions.iterrows():
                        f.write(f"\n期數: {row['Period']}, 預測日期: {row['PredictionDate']}\n")
                        
                        if is_dailycash:  # 今彩539
                            pred_numbers = [row['PredA1'], row['PredA2'], row['PredA3'], 
                                        row['PredA4'], row['PredA5']]
                            f.write(f"預測號碼: {pred_numbers}\n")
                            
                            if pd.notna(row['ActualA1']):
                                actual_numbers = [row['ActualA1'], row['ActualA2'], row['ActualA3'], 
                                                row['ActualA4'], row['ActualA5']]
                                f.write(f"實際號碼: {actual_numbers}\n")
                                f.write(f"匹配結果: 匹配 {row['MatchCount']} 個\n")
                            else:
                                f.write("實際結果: 尚未開獎\n")
                        
                        elif 'PredS' in row:  # 威力彩
                            pred_numbers = [row['PredA1'], row['PredA2'], row['PredA3'], 
                                        row['PredA4'], row['PredA5'], row['PredA6']]
                            f.write(f"預測號碼: 第一區 {pred_numbers}, 第二區 {row['PredS']}\n")
                            
                            if pd.notna(row['ActualA1']):
                                actual_numbers = [row['ActualA1'], row['ActualA2'], row['ActualA3'], 
                                                row['ActualA4'], row['ActualA5'], row['ActualA6']]
                                f.write(f"實際號碼: 第一區 {actual_numbers}, 第二區 {row['ActualS']}\n")
                                f.write(f"匹配結果: 第一區匹配 {row['MatchCount']} 個, 第二區匹配: {'是' if row['SecondMatch'] else '否'}\n")
                            else:
                                f.write("實際結果: 尚未開獎\n")
                                
                        else:  # 大樂透
                            pred_numbers = [row['PredA1'], row['PredA2'], row['PredA3'], 
                                        row['PredA4'], row['PredA5'], row['PredA6']]
                            f.write(f"預測號碼: 第一區 {pred_numbers}, 特別號 {row['PredSpecial']}\n")
                            
                            if pd.notna(row['ActualA1']):
                                actual_numbers = [row['ActualA1'], row['ActualA2'], row['ActualA3'], 
                                                row['ActualA4'], row['ActualA5'], row['ActualA6']]
                                f.write(f"實際號碼: 第一區 {actual_numbers}, 特別號 {row['ActualSpecial']}\n")
                                f.write(f"匹配結果: 第一區匹配 {row['MatchCount']} 個, 特別號匹配: {'是' if row['SpecialMatch'] else '否'}\n")
                            else:
                                f.write("實際結果: 尚未開獎\n")
                
                # 8. 結論與建議
                f.write("\n8. 結論與建議\n")
                f.write("-" * 50 + "\n")
                
                # 根據分析結果生成結論
                avg_match = analysis_results['average_match']
                
                if is_dailycash:  # 今彩539
                    if avg_match >= 2.0:
                        f.write("- 預測模型表現良好，平均能匹配多個號碼。\n")
                    else:
                        f.write("- 預測模型整體表現有待提高，建議調整模型參數或融合更多預測方法。\n")
                
                else:  # 威力彩或大樂透
                    second_rate = analysis_results.get('second_match_percentage', 0)
                    
                    if avg_match >= 2.0 and second_rate >= 20:
                        f.write("- 預測模型表現良好，平均能匹配多個號碼，第二區匹配率也達到了較高水平。\n")
                    elif avg_match >= 2.0:
                        f.write("- 第一區預測表現良好，但第二區預測還有提升空間。\n")
                    elif second_rate >= 20:
                        f.write("- 第二區預測表現不錯，但第一區預測準確度需要提高。\n")
                    else:
                        f.write("- 預測模型整體表現有待提高，建議調整模型參數或融合更多預測方法。\n")
                
                # 根據中獎率給出建議
                if total_prize_rate >= 15:
                    f.write("- 總體中獎率較高，建議可以適當增加投注頻率。\n")
                elif total_prize_rate >= 5:
                    f.write("- 中獎率達到一定水平，可以考慮繼續使用當前的預測策略。\n")
                else:
                    f.write("- 中獎率較低，建議優化預測算法或考慮調整投注策略。\n")
                
                # 未來改進建議
                f.write("\n未來改進建議:\n")
                f.write("- 持續收集更多歷史數據，擴大訓練樣本。\n")
                f.write("- 嘗試更多機器學習算法，尋找更適合彩票預測的模型。\n")
                f.write("- 深入分析板路規律，加強號碼間的關聯性分析。\n")
                f.write("- 考慮時間序列特性，捕捉彩票開獎的週期性模式。\n")
                f.write("- 優化特徵工程，提取更有效的預測因子。\n")
            
            logger.info(f"詳細分析報告已生成: {filepath}")
            return True
        except Exception as e:
            logger.error(f"生成詳細報告時出錯: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    def format_analysis_for_display(self, analysis_results):
        """格式化分析結果以便於顯示"""
        if not analysis_results:
            return "沒有可用的分析結果"
        
        # 判断彩票类型 (通过分析second_match_percentage是否存在)
        is_dailycash = 'second_match_percentage' not in analysis_results
        
        # 創建基本顯示信息
        display = [
            f"分析時間: {analysis_results.get('analysis_time', 'N/A')}",
            f"分析樣本數: {analysis_results.get('total_predictions', 0)} 筆",
            f"\n匹配數量統計:",
            f"平均匹配數: {analysis_results.get('average_match', 0):.2f}",
        ]
        
        # 根据彩票类型添加不同的显示内容
        if not is_dailycash:
            display.append(f"第二區匹配率: {analysis_results.get('second_match_percentage', 0):.2f}%")
        
        # 添加匹配數量分佈
        display.append(f"\n匹配數量分佈:")
        for k, v in sorted(analysis_results.get('match_counts', {}).items()):
            percentage = analysis_results.get('match_percentages', {}).get(k, 0)
            display.append(f"匹配 {k} 個號碼: {v} 次 ({percentage:.2f}%)")
        
        # 添加中獎情況
        prizes = analysis_results.get('prize_counts', {})
        if any(prizes.values()):
            display.append(f"\n中獎情況:")
            for prize, count in prizes.items():
                if count > 0:
                    percentage = analysis_results.get('prize_percentages', {}).get(prize, 0)
                    display.append(f"{prize}: {count} 次 ({percentage:.2f}%)")
            
            # 計算總體中獎率
            total_prize = sum(v for k, v in prizes.items() if k != '未中獎')
            total_prize_rate = total_prize / analysis_results['total_predictions'] * 100
            display.append(f"\n總體中獎率: {total_prize} / {analysis_results['total_predictions']} ({total_prize_rate:.2f}%)")
        
        # 添加月度趨勢
        monthly_stats = analysis_results.get('monthly_stats', {})
        if monthly_stats and 'MatchCount' in monthly_stats:
            display.append(f"\n月度趨勢:")
            for month, avg_match in list(monthly_stats['MatchCount'].items())[-5:]:  # 只顯示最近5個月
                if is_dailycash:
                    display.append(f"{month}: 平均匹配 {avg_match:.2f} 個")
                else:
                    if 'SecondMatch' in monthly_stats:
                        second_rate = monthly_stats['SecondMatch'][month] * 100
                        display.append(f"{month}: 平均匹配 {avg_match:.2f} 個，第二區匹配率 {second_rate:.2f}%")
        
        return "\n".join(display)
    
    def _get_lottery_name(self, lottery_type):
        """根據彩票類型獲取中文名稱"""
        name_map = {
            'powercolor': '威力彩',
            'lotto649': '大樂透',
            'dailycash': '今彩539'
        }
        return name_map.get(lottery_type.lower(), lottery_type)

# 測試代碼
if __name__ == "__main__":
    from data.db_manager import DBManager
    from config.config_manager import ConfigManager
    
    # 加載數據
    config_manager = ConfigManager()
    db = DBManager(config_manager=config_manager)
    prediction_df = db.load_prediction_records()
    
    # 創建分析器
    analyzer = PredictionAnalyzer(db_manager=db)
    
    # 分析預測準確度
    analysis_results = analyzer.analyze_prediction_accuracy(prediction_df)
    
    # 分析不同預測方法
    methods_comparison = analyzer.analyze_prediction_methods(prediction_df)
    
    # 生成詳細報告
    if analysis_results:
        analyzer.generate_detailed_report(analysis_results, prediction_df)
        
        # 顯示分析結果
        print(analyzer.format_analysis_for_display(analysis_results))