"""
預測信心度計算器
基於多個因素計算預測結果的信心度
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class ConfidenceCalculator:
    """預測信心度計算器"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.weights = {
            'historical_accuracy': 0.3,    # 歷史準確率
            'pattern_strength': 0.25,      # 模式強度
            'data_consistency': 0.2,       # 數據一致性
            'model_consensus': 0.15,       # 模型一致性
            'recency_factor': 0.1          # 時效性因子
        }
    
    def calculate_prediction_confidence(self, 
                                      prediction_data: Dict, 
                                      lottery_type: str,
                                      method: str = 'ensemble') -> float:
        """
        計算預測信心度
        
        Args:
            prediction_data: 預測數據
            lottery_type: 彩票類型
            method: 預測方法
            
        Returns:
            float: 信心度分數 (0-1)
        """
        try:
            # 計算各個因子
            historical_score = self._calculate_historical_accuracy(lottery_type, method)
            pattern_score = self._calculate_pattern_strength(prediction_data, lottery_type)
            consistency_score = self._calculate_data_consistency(lottery_type)
            consensus_score = self._calculate_model_consensus(prediction_data)
            recency_score = self._calculate_recency_factor(lottery_type)
            
            # 加權計算總信心度
            confidence = (
                historical_score * self.weights['historical_accuracy'] +
                pattern_score * self.weights['pattern_strength'] +
                consistency_score * self.weights['data_consistency'] +
                consensus_score * self.weights['model_consensus'] +
                recency_score * self.weights['recency_factor']
            )
            
            # 確保在0-1範圍內
            confidence = max(0.0, min(1.0, confidence))
            
            logger.info(f"信心度計算完成: {confidence:.3f} ({method}, {lottery_type})")
            
            return confidence
            
        except Exception as e:
            logger.error(f"計算信心度時出錯: {str(e)}")
            return 0.5  # 返回中等信心度作為預設值
    
    def _calculate_historical_accuracy(self, lottery_type: str, method: str) -> float:
        """計算歷史準確率分數"""
        try:
            # 獲取最近30次預測記錄
            df = self.db_manager.load_prediction_records(lottery_type, limit=30)
            if df.empty:
                return 0.5
            
            # 篩選指定方法的記錄
            method_df = df[df['PredictionMethod'].str.contains(method, na=False)]
            if method_df.empty:
                return 0.5
            
            # 計算命中率（3個或以上號碼匹配視為成功）
            successful_predictions = method_df[method_df['MatchCount'] >= 3]
            accuracy_rate = len(successful_predictions) / len(method_df)
            
            # 轉換為0-1分數（最高80%準確率對應1.0分數）
            return min(accuracy_rate / 0.8, 1.0)
            
        except Exception as e:
            logger.error(f"計算歷史準確率時出錯: {str(e)}")
            return 0.5
    
    def _calculate_pattern_strength(self, prediction_data: Dict, lottery_type: str) -> float:
        """計算模式強度分數"""
        try:
            # 獲取歷史數據
            df = self.db_manager.load_lottery_data(lottery_type)
            if not df.empty and len(df) > 100:
                df = df.head(100)
            if df.empty:
                return 0.5
            
            predicted_numbers = prediction_data.get('numbers', [])
            if not predicted_numbers:
                return 0.5
            
            # 計算號碼出現頻率
            all_numbers = []
            for _, row in df.iterrows():
                if lottery_type == 'powercolor':
                    numbers = [int(row[f'Anumber{i}']) for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])]
                elif lottery_type == 'lotto649':
                    numbers = [int(row[f'Anumber{i}']) for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])]
                elif lottery_type == 'dailycash':
                    numbers = [int(row[f'Anumber{i}']) for i in range(1, 6) if pd.notna(row[f'Anumber{i}'])]
                all_numbers.extend(numbers)
            
            # 計算預測號碼的平均出現頻率
            total_count = len(all_numbers)
            if total_count == 0:
                return 0.5
            
            predicted_frequency = sum(all_numbers.count(num) for num in predicted_numbers) / len(predicted_numbers)
            average_frequency = total_count / len(set(all_numbers))
            
            # 正規化分數
            pattern_strength = min(predicted_frequency / average_frequency / 2, 1.0)
            
            return pattern_strength
            
        except Exception as e:
            logger.error(f"計算模式強度時出錯: {str(e)}")
            return 0.5
    
    def _calculate_data_consistency(self, lottery_type: str) -> float:
        """計算數據一致性分數"""
        try:
            # 檢查最近數據的完整性
            df = self.db_manager.load_lottery_data(lottery_type)
            if not df.empty and len(df) > 10:
                df = df.head(10)
            if df.empty:
                return 0.0
            
            # 檢查數據缺失情況
            total_fields = len(df.columns)
            missing_ratio = df.isnull().sum().sum() / (len(df) * total_fields)
            
            # 檢查數據更新時效性
            if 'Sdate' in df.columns:
                latest_date = pd.to_datetime(df['Sdate']).max()
                days_since_update = (datetime.now() - latest_date).days
                recency_factor = max(0, 1 - days_since_update / 7)  # 7天內為滿分
            else:
                recency_factor = 0.5
            
            # 綜合一致性分數
            consistency = (1 - missing_ratio) * 0.7 + recency_factor * 0.3
            
            return max(0.0, min(1.0, consistency))
            
        except Exception as e:
            logger.error(f"計算數據一致性時出錯: {str(e)}")
            return 0.5
    
    def _calculate_model_consensus(self, prediction_data: Dict) -> float:
        """計算模型一致性分數"""
        try:
            # 如果有多個候選結果，計算它們的一致性
            candidates = prediction_data.get('candidates', [])
            if len(candidates) < 2:
                return 0.7  # 單一結果給予中等分數
            
            # 計算候選結果之間的相似度
            similarities = []
            for i in range(len(candidates)):
                for j in range(i + 1, len(candidates)):
                    similarity = self._calculate_number_similarity(
                        candidates[i].get('numbers', []),
                        candidates[j].get('numbers', [])
                    )
                    similarities.append(similarity)
            
            # 平均相似度作為一致性分數
            consensus = np.mean(similarities) if similarities else 0.5
            
            return consensus
            
        except Exception as e:
            logger.error(f"計算模型一致性時出錯: {str(e)}")
            return 0.5
    
    def _calculate_recency_factor(self, lottery_type: str) -> float:
        """計算時效性因子"""
        try:
            # 檢查最近的開獎時間
            df = self.db_manager.load_lottery_data(lottery_type)
            if not df.empty:
                df = df.head(1)
            if df.empty or 'Sdate' not in df.columns:
                return 0.5
            
            latest_date = pd.to_datetime(df['Sdate'].iloc[0])
            days_since = (datetime.now() - latest_date).days
            
            # 數據越新，時效性分數越高
            if days_since <= 1:
                return 1.0
            elif days_since <= 3:
                return 0.8
            elif days_since <= 7:
                return 0.6
            else:
                return 0.4
                
        except Exception as e:
            logger.error(f"計算時效性因子時出錯: {str(e)}")
            return 0.5
    
    def _calculate_number_similarity(self, numbers1: List[int], numbers2: List[int]) -> float:
        """計算兩組號碼的相似度"""
        if not numbers1 or not numbers2:
            return 0.0
        
        set1 = set(numbers1)
        set2 = set(numbers2)
        
        intersection = len(set1 & set2)
        union = len(set1 | set2)
        
        return intersection / union if union > 0 else 0.0
    
    def get_confidence_level_description(self, confidence: float) -> Tuple[str, str]:
        """
        獲取信心度等級描述
        
        Returns:
            Tuple[str, str]: (等級, 描述)
        """
        if confidence >= 0.8:
            return "極高", "多個指標顯示高成功機率"
        elif confidence >= 0.7:
            return "高", "各項指標表現良好"
        elif confidence >= 0.6:
            return "中高", "預測具有一定可信度"
        elif confidence >= 0.5:
            return "中等", "預測準確性一般"
        elif confidence >= 0.4:
            return "中低", "預測可信度較低"
        else:
            return "低", "建議謹慎參考"
    
    def get_confidence_color(self, confidence: float) -> str:
        """獲取信心度對應的顏色"""
        if confidence >= 0.7:
            return "success"  # 綠色
        elif confidence >= 0.6:
            return "info"     # 藍色
        elif confidence >= 0.5:
            return "warning"  # 黃色
        else:
            return "danger"   # 紅色