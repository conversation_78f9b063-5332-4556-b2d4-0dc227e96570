#!/usr/bin/env python3
"""
策略觀察表 Web API
提供策略追蹤數據的REST接口
"""

from flask import Blueprint, jsonify, request
import logging
from strategy_tracker import StrategyTracker
from datetime import datetime

# 創建藍圖
strategy_api = Blueprint('strategy_api', __name__)
logger = logging.getLogger('strategy_api')

@strategy_api.route('/api/strategy/performance', methods=['GET'])
def get_strategy_performance():
    """獲取策略表現數據"""
    try:
        lottery_type = request.args.get('lottery_type')
        
        tracker = StrategyTracker()
        performance_data = tracker.get_strategy_performance(lottery_type)
        
        return jsonify({
            'success': True,
            'data': performance_data,
            'message': f'成功獲取 {len(performance_data)} 個策略表現數據'
        })
        
    except Exception as e:
        logger.error(f"獲取策略表現失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@strategy_api.route('/api/strategy/predictions', methods=['GET'])
def get_strategy_predictions():
    """獲取策略預測記錄"""
    try:
        strategy_type = request.args.get('strategy_type')
        lottery_type = request.args.get('lottery_type')
        limit = int(request.args.get('limit', 20))
        
        tracker = StrategyTracker()
        predictions = tracker.get_recent_predictions(strategy_type, lottery_type, limit)
        
        return jsonify({
            'success': True,
            'data': predictions,
            'message': f'成功獲取 {len(predictions)} 筆預測記錄'
        })
        
    except Exception as e:
        logger.error(f"獲取預測記錄失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@strategy_api.route('/api/strategy/generate', methods=['POST'])
def generate_ai_predictions():
    """生成AI策略預測（6組）"""
    try:
        data = request.get_json()
        lottery_type = data.get('lottery_type')
        target_period = data.get('target_period')
        
        if not lottery_type or not target_period:
            return jsonify({
                'success': False,
                'error': '缺少必要參數: lottery_type, target_period'
            }), 400
        
        tracker = StrategyTracker()
        tracker.simulate_ai_predictions(lottery_type, target_period)
        
        return jsonify({
            'success': True,
            'message': f'成功為 {lottery_type} 期號 {target_period} 生成6組AI預測'
        })
        
    except Exception as e:
        logger.error(f"生成AI預測失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@strategy_api.route('/api/strategy/update_results', methods=['POST'])
def update_actual_results():
    """更新實際開獎結果"""
    try:
        data = request.get_json()
        period = data.get('period')
        lottery_type = data.get('lottery_type')
        actual_numbers = data.get('actual_numbers')
        actual_special = data.get('actual_special')
        
        if not all([period, lottery_type, actual_numbers]):
            return jsonify({
                'success': False,
                'error': '缺少必要參數: period, lottery_type, actual_numbers'
            }), 400
        
        tracker = StrategyTracker()
        tracker.update_actual_results(period, lottery_type, actual_numbers, actual_special)
        
        return jsonify({
            'success': True,
            'message': f'成功更新 {period} 的開獎結果'
        })
        
    except Exception as e:
        logger.error(f"更新開獎結果失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@strategy_api.route('/api/strategy/dashboard', methods=['GET'])
def get_strategy_dashboard():
    """獲取策略總覽數據"""
    try:
        tracker = StrategyTracker()
        
        # 獲取所有彩票類型的表現數據
        all_performance = tracker.get_strategy_performance()
        
        # 按彩票類型分組
        dashboard_data = {}
        for perf in all_performance:
            lottery_type = perf['lottery_type']
            if lottery_type not in dashboard_data:
                dashboard_data[lottery_type] = {
                    'lottery_name': {
                        'powercolor': '威力彩',
                        'lotto649': '大樂透',
                        'dailycash': '今彩539'
                    }.get(lottery_type, lottery_type),
                    'strategies': []
                }
            
            dashboard_data[lottery_type]['strategies'].append({
                'name': perf['strategy_type'],
                'total_predictions': perf['total_predictions'],
                'hit_rate': perf['hit_rate'],
                'average_accuracy': perf['average_accuracy'],
                'best_accuracy': perf['best_accuracy'],
                'average_hit_count': perf['average_hit_count']
            })
        
        # 排序策略（按命中率降序）
        for lottery_type in dashboard_data:
            dashboard_data[lottery_type]['strategies'].sort(
                key=lambda x: x['average_accuracy'], reverse=True
            )
        
        return jsonify({
            'success': True,
            'data': dashboard_data,
            'generated_at': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"獲取策略總覽失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@strategy_api.route('/api/strategy/comparison', methods=['GET'])
def get_strategy_comparison():
    """獲取策略比較數據"""
    try:
        lottery_type = request.args.get('lottery_type', 'lotto649')
        
        tracker = StrategyTracker()
        performance_data = tracker.get_strategy_performance(lottery_type)
        
        # 準備比較數據
        comparison_data = {
            'lottery_type': lottery_type,
            'lottery_name': {
                'powercolor': '威力彩',
                'lotto649': '大樂透', 
                'dailycash': '今彩539'
            }.get(lottery_type, lottery_type),
            'strategies': [],
            'best_strategy': None,
            'total_predictions': sum(p['total_predictions'] for p in performance_data)
        }
        
        for perf in performance_data:
            strategy_data = {
                'name': perf['strategy_type'],
                'predictions': perf['total_predictions'],
                'hits': perf['total_hits'],
                'hit_rate': perf['hit_rate'],
                'avg_accuracy': perf['average_accuracy'],
                'best_accuracy': perf['best_accuracy'],
                'avg_hit_count': perf['average_hit_count'],
                'rank': 0  # 稍後計算
            }
            comparison_data['strategies'].append(strategy_data)
        
        # 按平均準確度排序並設定排名
        comparison_data['strategies'].sort(key=lambda x: x['avg_accuracy'], reverse=True)
        for i, strategy in enumerate(comparison_data['strategies'], 1):
            strategy['rank'] = i
        
        # 設定最佳策略
        if comparison_data['strategies']:
            comparison_data['best_strategy'] = comparison_data['strategies'][0]['name']
        
        return jsonify({
            'success': True,
            'data': comparison_data
        })
        
    except Exception as e:
        logger.error(f"獲取策略比較失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500