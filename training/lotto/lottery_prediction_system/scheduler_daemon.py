#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定期任務調度守護進程 (Scheduler Daemon)
自動執行數據同步、完整性檢查、系統維護等定期任務

核心功能：
1. 自動數據同步 (Auto Data Sync)
2. 數據完整性監控 (Data Integrity Monitoring)  
3. 系統健康檢查 (System Health Check)
4. 日誌維護清理 (Log Maintenance)
5. 數據備份任務 (Data Backup Tasks)
"""

import os
import sys
import time
import signal
import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import json
import schedule
from dataclasses import dataclass, asdict

# 添加項目根目錄到Python路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 導入項目模組
try:
    from taiwan_lottery_api import get_auto_sync_manager
    from data_integrity_framework import get_integrity_manager
    from real_data_integration import check_data_integrity
    from intelligent_data_checker import get_data_checker, check_lottery_data_intelligently
    from real_web_scraper_updated import get_updated_scraper
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"模組導入失敗: {e}")
    MODULES_AVAILABLE = False

# 配置日誌系統
def setup_logging():
    """設置調度守護進程日誌"""
    os.makedirs('logs', exist_ok=True)
    
    # 創建調度器專用日誌記錄器
    logger = logging.getLogger('scheduler_daemon')
    logger.setLevel(logging.INFO)
    
    # 文件處理器
    file_handler = logging.FileHandler(
        f'logs/scheduler_daemon_{datetime.now().strftime("%Y%m%d")}.log',
        encoding='utf-8'
    )
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s - SCHEDULER - %(levelname)s - %(message)s'
    ))
    
    # 控制台處理器  
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    ))
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

logger = setup_logging()

@dataclass
class TaskResult:
    """任務執行結果"""
    task_name: str
    success: bool
    start_time: datetime
    end_time: datetime
    message: str
    details: Optional[Dict[str, Any]] = None
    
    @property
    def duration(self) -> timedelta:
        return self.end_time - self.start_time
    
    def to_dict(self) -> Dict[str, Any]:
        result = asdict(self)
        result['start_time'] = self.start_time.isoformat()
        result['end_time'] = self.end_time.isoformat()
        result['duration_seconds'] = self.duration.total_seconds()
        return result

class SchedulerDaemon:
    """調度守護進程主類"""
    
    def __init__(self):
        self.running = False
        self.tasks_history: List[TaskResult] = []
        self.max_history = 1000  # 保留最多1000條歷史記錄
        
        # 任務統計
        self.task_stats = {
            'total_runs': 0,
            'successful_runs': 0,
            'failed_runs': 0,
            'last_run': None
        }
        
        # 信號處理
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
        
        logger.info("調度守護進程初始化完成")
    
    def _signal_handler(self, signum, frame):
        """處理終止信號"""
        logger.info(f"收到信號 {signum}，正在優雅關閉...")
        self.running = False
    
    def _execute_task(self, task_name: str, task_func, *args, **kwargs) -> TaskResult:
        """執行單個任務並記錄結果"""
        start_time = datetime.now()
        
        try:
            logger.info(f"開始執行任務: {task_name}")
            
            result = task_func(*args, **kwargs)
            
            end_time = datetime.now()
            task_result = TaskResult(
                task_name=task_name,
                success=True,
                start_time=start_time,
                end_time=end_time,
                message="任務執行成功",
                details=result if isinstance(result, dict) else {'result': str(result)}
            )
            
            self.task_stats['successful_runs'] += 1
            logger.info(f"任務完成: {task_name} (耗時: {task_result.duration.total_seconds():.2f}秒)")
            
        except Exception as e:
            end_time = datetime.now()
            task_result = TaskResult(
                task_name=task_name,
                success=False,
                start_time=start_time,
                end_time=end_time,
                message=f"任務執行失敗: {str(e)}",
                details={'error': str(e), 'error_type': type(e).__name__}
            )
            
            self.task_stats['failed_runs'] += 1
            logger.error(f"任務失敗: {task_name} - {str(e)}")
        
        # 更新統計信息
        self.task_stats['total_runs'] += 1
        self.task_stats['last_run'] = datetime.now().isoformat()
        
        # 記錄任務歷史
        self.tasks_history.append(task_result)
        if len(self.tasks_history) > self.max_history:
            self.tasks_history = self.tasks_history[-self.max_history:]
        
        return task_result
    
    def intelligent_sync_official_data(self) -> Dict[str, Any]:
        """智能自動同步官方數據任務（2小時一次，先檢查再同步）"""
        if not MODULES_AVAILABLE:
            raise Exception("必要模組不可用，無法執行智能數據同步")
        
        # 使用智能數據檢查器
        data_checker = get_data_checker()
        check_results, recommendation = data_checker.perform_intelligent_check()
        
        sync_report = {
            'check_results': {k: {
                'lottery_name': v.lottery_name,
                'current_period': v.current_period,
                'has_new_data': v.has_new_data,
                'data_age_hours': v.data_age_hours,
                'sync_needed': v.sync_needed
            } for k, v in check_results.items()},
            'recommendation': {
                'immediate_sync_needed': recommendation.immediate_sync_needed,
                'lottery_types_to_sync': recommendation.lottery_types_to_sync,
                'priority_level': recommendation.priority_level,
                'recommendation_reason': recommendation.recommendation_reason
            },
            'sync_performed': False,
            'sync_results': {}
        }
        
        # 如果建議需要同步，則執行同步
        if recommendation.immediate_sync_needed:
            logger.info(f"智能檢查建議執行同步: {recommendation.recommendation_reason}")
            
            try:
                # 使用更新版網站爬蟲進行同步
                scraper = get_updated_scraper()
                scraper_results = scraper.auto_sync_with_check()
                
                sync_report['sync_performed'] = scraper_results.get('sync_performed', False)
                sync_report['sync_results'] = scraper_results.get('sync_results', {})
                
                if sync_report['sync_performed']:
                    total_synced = scraper_results.get('sync_results', {}).get('total_synced', 0)
                    logger.info(f"智能同步完成: 成功同步 {total_synced} 筆數據")
                else:
                    logger.info("智能檢查後發現無新數據，跳過同步")
                    
            except Exception as e:
                error_msg = f"智能同步執行失敗: {str(e)}"
                logger.error(error_msg)
                sync_report['sync_error'] = error_msg
        else:
            logger.info("智能檢查結果：無需同步")
        
        return sync_report
    
    def sync_official_data(self) -> Dict[str, Any]:
        """傳統自動同步官方數據任務（保留向後兼容性）"""
        if not MODULES_AVAILABLE:
            raise Exception("必要模組不可用，無法執行數據同步")
        
        sync_manager = get_auto_sync_manager()
        sync_report = sync_manager.perform_auto_sync()
        
        logger.info(f"官方數據同步完成: 成功 {sync_report['total_success']} 筆，失敗 {sync_report['total_failed']} 筆")
        
        return sync_report
    
    def check_data_integrity_task(self) -> Dict[str, Any]:
        """數據完整性檢查任務"""
        if not MODULES_AVAILABLE:
            raise Exception("必要模組不可用，無法執行完整性檢查")
        
        # 生成完整性報告
        integrity_manager = get_integrity_manager()
        report = integrity_manager.generate_integrity_report()
        
        # 檢查整體數據質量
        quality_report = check_data_integrity()
        
        combined_report = {
            'integrity_report': report,
            'quality_report': quality_report,
            'health_score': report.get('integrity_score', 0)
        }
        
        # 記錄關鍵指標
        health_score = combined_report['health_score']
        fake_blocked = report.get('fake_data_attempts_blocked', 0)
        
        if health_score < 80:
            logger.warning(f"數據完整性評分較低: {health_score}/100")
        
        if fake_blocked > 0:
            logger.warning(f"檢測到 {fake_blocked} 次假數據嘗試")
        
        logger.info(f"數據完整性檢查完成，健康評分: {health_score}/100")
        
        return combined_report
    
    def system_health_check(self) -> Dict[str, Any]:
        """系統健康檢查任務"""
        health_report = {
            'timestamp': datetime.now().isoformat(),
            'system_status': 'healthy',
            'checks': {}
        }
        
        # 檢查數據目錄
        data_dir = 'data'
        if os.path.exists(data_dir):
            data_files = os.listdir(data_dir)
            health_report['checks']['data_directory'] = {
                'status': 'ok',
                'files_count': len(data_files),
                'total_size_mb': sum(
                    os.path.getsize(os.path.join(data_dir, f)) 
                    for f in data_files 
                    if os.path.isfile(os.path.join(data_dir, f))
                ) / (1024 * 1024)
            }
        else:
            health_report['checks']['data_directory'] = {
                'status': 'missing',
                'message': '數據目錄不存在'
            }
            health_report['system_status'] = 'warning'
        
        # 檢查日誌目錄
        logs_dir = 'logs'
        if os.path.exists(logs_dir):
            log_files = [f for f in os.listdir(logs_dir) if f.endswith('.log')]
            health_report['checks']['log_directory'] = {
                'status': 'ok',
                'log_files_count': len(log_files)
            }
        else:
            health_report['checks']['log_directory'] = {
                'status': 'missing',
                'message': '日誌目錄不存在'
            }
        
        # 檢查模組可用性
        health_report['checks']['modules'] = {
            'status': 'ok' if MODULES_AVAILABLE else 'error',
            'available': MODULES_AVAILABLE
        }
        
        # 檢查任務執行統計
        health_report['checks']['task_statistics'] = {
            'status': 'ok',
            'stats': self.task_stats.copy()
        }
        
        logger.info(f"系統健康檢查完成，狀態: {health_report['system_status']}")
        
        return health_report
    
    def cleanup_old_logs(self) -> Dict[str, Any]:
        """清理舊日誌文件任務"""
        cleanup_report = {
            'cleaned_files': [],
            'total_cleaned': 0,
            'total_size_freed_mb': 0
        }
        
        # 清理超過30天的日誌文件
        cutoff_date = datetime.now() - timedelta(days=30)
        
        for log_dir in ['logs', 'data']:
            if not os.path.exists(log_dir):
                continue
            
            for filename in os.listdir(log_dir):
                if not filename.endswith('.log'):
                    continue
                
                filepath = os.path.join(log_dir, filename)
                if not os.path.isfile(filepath):
                    continue
                
                # 檢查文件修改時間
                file_mtime = datetime.fromtimestamp(os.path.getmtime(filepath))
                if file_mtime < cutoff_date:
                    try:
                        file_size = os.path.getsize(filepath)
                        os.remove(filepath)
                        
                        cleanup_report['cleaned_files'].append(filename)
                        cleanup_report['total_cleaned'] += 1
                        cleanup_report['total_size_freed_mb'] += file_size / (1024 * 1024)
                        
                        logger.info(f"清理舊日誌文件: {filename}")
                        
                    except Exception as e:
                        logger.error(f"清理日誌文件失敗 {filename}: {str(e)}")
        
        logger.info(f"日誌清理完成: 清理 {cleanup_report['total_cleaned']} 個文件，釋放 {cleanup_report['total_size_freed_mb']:.2f} MB")
        
        return cleanup_report
    
    def backup_critical_data(self) -> Dict[str, Any]:
        """備份關鍵數據任務"""
        import shutil
        
        backup_report = {
            'backup_time': datetime.now().isoformat(),
            'backed_up_files': [],
            'backup_size_mb': 0,
            'backup_location': None
        }
        
        # 創建備份目錄
        backup_dir = f"backups/backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(backup_dir, exist_ok=True)
        backup_report['backup_location'] = backup_dir
        
        # 需要備份的文件/目錄列表
        backup_targets = [
            'data/lottery_data.db',
            'data/auto_sync_status.json',
            'data/integrity_audit.log',
            'config.json'
        ]
        
        for target in backup_targets:
            if os.path.exists(target):
                try:
                    backup_path = os.path.join(backup_dir, os.path.basename(target))
                    
                    if os.path.isfile(target):
                        shutil.copy2(target, backup_path)
                        file_size = os.path.getsize(target)
                        backup_report['backup_size_mb'] += file_size / (1024 * 1024)
                    else:
                        shutil.copytree(target, backup_path)
                        # 計算目錄大小
                        for root, dirs, files in os.walk(backup_path):
                            for file in files:
                                file_size = os.path.getsize(os.path.join(root, file))
                                backup_report['backup_size_mb'] += file_size / (1024 * 1024)
                    
                    backup_report['backed_up_files'].append(target)
                    logger.info(f"已備份: {target}")
                    
                except Exception as e:
                    logger.error(f"備份失敗 {target}: {str(e)}")
        
        logger.info(f"數據備份完成: 備份 {len(backup_report['backed_up_files'])} 個項目到 {backup_dir}")
        
        return backup_report
    
    def save_task_report(self):
        """保存任務執行報告"""
        report = {
            'daemon_stats': self.task_stats,
            'recent_tasks': [task.to_dict() for task in self.tasks_history[-50:]],  # 最近50次任務
            'generated_at': datetime.now().isoformat()
        }
        
        os.makedirs('reports', exist_ok=True)
        report_file = f"reports/scheduler_report_{datetime.now().strftime('%Y%m%d')}.json"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            logger.info(f"任務報告已保存: {report_file}")
        except Exception as e:
            logger.error(f"保存任務報告失敗: {str(e)}")
    
    def setup_schedules(self):
        """設置定期任務調度"""
        # 智能官方數據同步 - 每2小時執行一次（先檢查再同步）
        schedule.every(2).hours.do(
            lambda: self._execute_task("智能數據同步", self.intelligent_sync_official_data)
        )
        
        # 數據完整性檢查 - 每4小時執行一次
        schedule.every(4).hours.do(
            lambda: self._execute_task("數據完整性檢查", self.check_data_integrity_task)
        )
        
        # 系統健康檢查 - 每小時執行一次
        schedule.every().hour.do(
            lambda: self._execute_task("系統健康檢查", self.system_health_check)
        )
        
        # 日誌清理 - 每天凌晨2點執行
        schedule.every().day.at("02:00").do(
            lambda: self._execute_task("日誌清理", self.cleanup_old_logs)
        )
        
        # 數據備份 - 每天凌晨1點執行
        schedule.every().day.at("01:00").do(
            lambda: self._execute_task("數據備份", self.backup_critical_data)
        )
        
        # 任務報告生成 - 每天晚上11點執行
        schedule.every().day.at("23:00").do(self.save_task_report)
        
        logger.info("定期任務調度設置完成")
    
    def run(self):
        """運行守護進程"""
        logger.info("調度守護進程啟動")
        
        if not MODULES_AVAILABLE:
            logger.warning("關鍵模組不可用，某些任務可能無法正常執行")
        
        # 設置任務調度
        self.setup_schedules()
        
        self.running = True
        
        # 執行初始健康檢查
        self._execute_task("初始系統健康檢查", self.system_health_check)
        
        # 主循環
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分鐘檢查一次
                
            except KeyboardInterrupt:
                logger.info("收到鍵盤中斷信號")
                break
            except Exception as e:
                logger.error(f"調度守護進程運行錯誤: {str(e)}")
                time.sleep(60)  # 錯誤後等待1分鐘再繼續
        
        # 清理和關閉
        logger.info("調度守護進程正在關閉...")
        self.save_task_report()
        logger.info("調度守護進程已關閉")

def main():
    """主函數"""
    print("="*80)
    print("彩票預測系統 - 調度守護進程")
    print("="*80)
    
    # 檢查是否以守護進程模式運行
    daemon_mode = '--daemon' in sys.argv
    
    if daemon_mode:
        print("以守護進程模式啟動...")
        # 在生產環境中，這裡可以實現真正的守護進程化
        # 例如使用 python-daemon 庫
    
    # 創建並運行調度器
    scheduler = SchedulerDaemon()
    
    try:
        scheduler.run()
    except Exception as e:
        logger.error(f"守護進程運行失敗: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()