#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彩票開獎數據更新修復工具
臨時解決方案 - 手動更新最新開獎結果
"""

import sqlite3
import logging
from datetime import datetime
from config_manager import ConfigManager
from data.db_manager import DBManager

logger = logging.getLogger('lottery_updater_fix')

def check_all_lottery_status():
    """檢查所有彩票類型的數據狀態"""
    try:
        config_manager = ConfigManager()
        db = DBManager(config_manager=config_manager)
        
        lottery_types = {
            'powercolor': '威力彩',
            'lotto649': '大樂透', 
            'dailycash': '今彩539'
        }
        
        print("🔍 檢查所有彩票數據狀態...")
        print("=" * 60)
        
        all_current = True
        status_report = {}
        
        for lottery_type, chinese_name in lottery_types.items():
            try:
                df = db.load_lottery_data(lottery_type)
                
                if not df.empty:
                    latest_period = df['Period'].max()
                    latest_date = df['Sdate'].max()
                    record_count = len(df)
                    
                    # 處理日期格式
                    try:
                        if isinstance(latest_date, str):
                            latest_date_obj = datetime.strptime(latest_date, '%Y-%m-%d %H:%M:%S')
                        else:
                            latest_date_obj = latest_date
                    except:
                        latest_date_obj = datetime.strptime(str(latest_date)[:10], '%Y-%m-%d')
                    
                    days_old = (datetime.now() - latest_date_obj).days
                    
                    print(f"📊 {chinese_name}:")
                    print(f"   最新期數: {latest_period}")
                    print(f"   最新日期: {latest_date_obj.strftime('%Y-%m-%d')}")
                    print(f"   數據筆數: {record_count}")
                    print(f"   數據年齡: {days_old} 天")
                    
                    if days_old <= 2:
                        print(f"   狀態: ✅ 數據較新")
                        status_report[lottery_type] = {'status': 'current', 'days_old': days_old}
                    elif days_old <= 5:
                        print(f"   狀態: ⚠️ 數據稍舊，建議更新")
                        status_report[lottery_type] = {'status': 'needs_update', 'days_old': days_old}
                        all_current = False
                    else:
                        print(f"   狀態: ❌ 數據過舊，需要更新")
                        status_report[lottery_type] = {'status': 'outdated', 'days_old': days_old}
                        all_current = False
                else:
                    print(f"📊 {chinese_name}: ❌ 無數據")
                    status_report[lottery_type] = {'status': 'no_data', 'days_old': 999}
                    all_current = False
                    
                print()
                
            except Exception as e:
                print(f"📊 {chinese_name}: ❌ 檢查失敗 - {str(e)}")
                status_report[lottery_type] = {'status': 'error', 'days_old': 999}
                all_current = False
                print()
        
        print("=" * 60)
        if all_current:
            print("✅ 所有彩票數據都是最新的")
        else:
            print("⚠️ 部分彩票數據需要更新")
            print("💡 提示：由於台灣彩券網站結構變更，自動更新暫時無法使用")
            print("📋 您可以：")
            print("   1. 等待修復完成")
            print("   2. 手動添加最新開獎結果")
            print("   3. 繼續使用現有數據進行預測")
        
        return status_report
        
    except Exception as e:
        logger.error(f"檢查所有彩票狀態失敗: {e}")
        print(f"❌ 檢查失敗: {e}")
        return {}

def manual_update_latest_result():
    """手動更新最新開獎結果 - 臨時解決方案"""
    return check_all_lottery_status()

def add_manual_result(lottery_type, period, date, numbers, special=None):
    """手動添加開獎結果"""
    try:
        config_manager = ConfigManager()
        db = DBManager(config_manager=config_manager)
        
        # 插入數據庫
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()
        
        # 根據彩票類型選擇對應的表格
        if lottery_type == 'powercolor':
            table_name = 'Powercolor'
            check_sql = "SELECT COUNT(*) FROM Powercolor WHERE Period = ?"
            
            # 檢查期號是否已存在
            cursor.execute(check_sql, (period,))
            exists = cursor.fetchone()[0] > 0
            
            if exists:
                print(f"⚠️ 威力彩期數 {period} 已存在，將更新")
                cursor.execute("""
                    UPDATE Powercolor 
                    SET Sdate = ?, Anumber1 = ?, Anumber2 = ?, Anumber3 = ?, 
                        Anumber4 = ?, Anumber5 = ?, Anumber6 = ?, Second_district = ?
                    WHERE Period = ?
                """, (date, numbers[0], numbers[1], numbers[2], numbers[3], 
                      numbers[4], numbers[5], special, period))
            else:
                print(f"✅ 新增威力彩期數 {period}")
                cursor.execute("""
                    INSERT INTO Powercolor (Period, Sdate, Anumber1, Anumber2, Anumber3, 
                                           Anumber4, Anumber5, Anumber6, Second_district)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (period, date, numbers[0], numbers[1], numbers[2], 
                      numbers[3], numbers[4], numbers[5], special))
                
        elif lottery_type == 'lotto649':
            table_name = 'Lotto649'
            check_sql = "SELECT COUNT(*) FROM Lotto649 WHERE Period = ?"
            
            # 檢查期號是否已存在
            cursor.execute(check_sql, (period,))
            exists = cursor.fetchone()[0] > 0
            
            if exists:
                print(f"⚠️ 大樂透期數 {period} 已存在，將更新")
                cursor.execute("""
                    UPDATE Lotto649 
                    SET Sdate = ?, Anumber1 = ?, Anumber2 = ?, Anumber3 = ?, 
                        Anumber4 = ?, Anumber5 = ?, Anumber6 = ?, SpecialNumber = ?
                    WHERE Period = ?
                """, (date, numbers[0], numbers[1], numbers[2], numbers[3], 
                      numbers[4], numbers[5], special, period))
            else:
                print(f"✅ 新增大樂透期數 {period}")
                cursor.execute("""
                    INSERT INTO Lotto649 (Period, Sdate, Anumber1, Anumber2, Anumber3, 
                                         Anumber4, Anumber5, Anumber6, SpecialNumber)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (period, date, numbers[0], numbers[1], numbers[2], 
                      numbers[3], numbers[4], numbers[5], special))
                
        elif lottery_type == 'dailycash':
            table_name = 'DailyCash'
            check_sql = "SELECT COUNT(*) FROM DailyCash WHERE Period = ?"
            
            # 檢查期號是否已存在
            cursor.execute(check_sql, (period,))
            exists = cursor.fetchone()[0] > 0
            
            if exists:
                print(f"⚠️ 今彩539期數 {period} 已存在，將更新")
                cursor.execute("""
                    UPDATE DailyCash 
                    SET Sdate = ?, Anumber1 = ?, Anumber2 = ?, Anumber3 = ?, 
                        Anumber4 = ?, Anumber5 = ?
                    WHERE Period = ?
                """, (date, numbers[0], numbers[1], numbers[2], numbers[3], 
                      numbers[4], period))
            else:
                print(f"✅ 新增今彩539期數 {period}")
                cursor.execute("""
                    INSERT INTO DailyCash (Period, Sdate, Anumber1, Anumber2, Anumber3, 
                                          Anumber4, Anumber5)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (period, date, numbers[0], numbers[1], numbers[2], 
                      numbers[3], numbers[4]))
        else:
            print(f"❌ 不支援的彩票類型: {lottery_type}")
            return False
        
        conn.commit()
        conn.close()
        
        lottery_names = {
            'powercolor': '威力彩',
            'lotto649': '大樂透',
            'dailycash': '今彩539'
        }
        
        chinese_name = lottery_names.get(lottery_type, lottery_type)
        if special is not None:
            print(f"✅ 成功添加{chinese_name}開獎結果: 期數 {period}, 號碼 {numbers}, 特別號 {special}")
        else:
            print(f"✅ 成功添加{chinese_name}開獎結果: 期數 {period}, 號碼 {numbers}")
        return True
        
    except Exception as e:
        logger.error(f"手動添加結果失敗: {e}")
        print(f"❌ 添加失敗: {e}")
        return False

def get_update_status():
    """獲取所有彩票類型的更新狀態信息"""
    try:
        status_report = manual_update_latest_result()
        
        if not status_report:
            return {
                'update_available': False,
                'status': 'error',
                'message': '無法檢查數據狀態',
                'last_check': datetime.now().isoformat()
            }
        
        # 分析整體狀態
        all_current = True
        needs_update_count = 0
        outdated_count = 0
        
        lottery_status = {}
        
        for lottery_type, info in status_report.items():
            lottery_names = {
                'powercolor': '威力彩',
                'lotto649': '大樂透',
                'dailycash': '今彩539'
            }
            
            chinese_name = lottery_names.get(lottery_type, lottery_type)
            days_old = info.get('days_old', 999)
            
            if info['status'] == 'current':
                lottery_status[lottery_type] = {
                    'status': 'success',
                    'message': f'✅ {chinese_name} 數據為最新狀態',
                    'data_source': '本地數據庫 (2天內)'
                }
            elif info['status'] == 'needs_update':
                lottery_status[lottery_type] = {
                    'status': 'warning',
                    'message': f'⚠️ {chinese_name} 數據稍舊 ({days_old}天)',
                    'data_source': f'本地數據庫 ({days_old}天前)'
                }
                needs_update_count += 1
                all_current = False
            elif info['status'] == 'outdated':
                lottery_status[lottery_type] = {
                    'status': 'outdated',
                    'message': f'❌ {chinese_name} 數據過舊 ({days_old}天)',
                    'data_source': f'本地數據庫 ({days_old}天前)'
                }
                outdated_count += 1
                all_current = False
            else:
                lottery_status[lottery_type] = {
                    'status': 'error',
                    'message': f'❌ {chinese_name} 無法讀取數據',
                    'data_source': '數據庫錯誤'
                }
                all_current = False
        
        # 整體狀態消息
        if all_current:
            overall_message = '✅ 所有彩票數據都是最新的'
            overall_status = 'success'
        else:
            messages = []
            if needs_update_count > 0:
                messages.append(f'{needs_update_count}個需要更新')
            if outdated_count > 0:
                messages.append(f'{outdated_count}個數據過舊')
            
            overall_message = f'⚠️ 因台灣彩券網站結構變更，自動更新暫時無法使用 ({", ".join(messages)})'
            overall_status = 'needs_update'
        
        return {
            'update_available': not all_current,
            'status': overall_status,
            'message': overall_message,
            'last_check': datetime.now().isoformat(),
            'lottery_details': lottery_status
        }
        
    except Exception as e:
        return {
            'update_available': False,
            'status': 'error',
            'message': f'檢查失敗: {str(e)}',
            'last_check': datetime.now().isoformat()
        }

if __name__ == "__main__":
    print("🔧 彩票數據更新修復工具")
    print("=" * 50)
    
    # 檢查狀態
    manual_update_latest_result()
    
    print("\n💡 如需手動添加最新開獎結果，請使用以下格式：")
    print("python lottery_updater_fix.py add 114000067 2025-08-19 [1,2,3,4,5,6] 7")