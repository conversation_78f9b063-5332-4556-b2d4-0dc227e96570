# 核心Web框架
Flask==2.3.3
Flask-CORS==4.0.0
Werkzeug==2.3.6
Jinja2==3.1.2
MarkupSafe==2.1.3

# 數據處理
numpy==1.24.3
pandas==2.0.3
scipy==1.11.1

# 機器學習
scikit-learn==1.3.0

# 數據可視化
matplotlib==3.7.2
seaborn==0.12.2
plotly==5.15.0

# 網絡請求
requests==2.31.0

# 緩存
redis==4.6.0
cachetools==5.3.1

# 系統監控
psutil==5.9.5

# 日期時間處理
python-dateutil==2.8.2
pytz==2023.3

# 配置管理
PyYAML==6.0.1
python-dotenv==1.0.0

# 測試
pytest==7.4.0
pytest-cov==4.1.0

# 代碼質量
flake8==6.0.0
black==23.7.0

# 開發工具
ipython==8.14.0

# 並發處理
gevent==23.7.0
gunicorn==21.2.0

# 文件處理
openpyxl==3.1.2

# 進度條
tqdm==4.65.0

# 命令行工具
click==8.1.6

# 字符串處理
fuzzywuzzy==0.18.0
python-Levenshtein==0.21.1

# 統計分析
statsmodels==0.14.0

# API文檔
flask-restx==1.1.0

# 數據驗證
marshmallow==3.20.1

# 安全
bcrypt==4.0.1
PyJWT==2.8.0

# 並行處理
joblib==1.3.1

# 文件監控
watchdog==3.0.0

# 錯誤追蹤
sentry-sdk==1.29.2

# 性能監控
prometheus-client==0.17.1

# 限流
Flask-Limiter==3.4.1

# 重試機制
retrying==1.3.4

# 編碼檢測
chardet==5.1.0

# 類型檢查
typing-extensions==4.7.1

# HTTP客戶端
httpx==0.24.1

# 數據庫ORM
SQLAlchemy==2.0.19

# 日誌聚合
loguru==0.7.0

# 郵件
email-validator==2.0.0

# Excel處理
xlsxwriter==3.1.2

# HTML解析
BeautifulSoup4==4.12.2

# 表單處理
WTForms==3.0.1
flask-wtf==1.1.1

# 梯度提升
xgboost==1.7.6
lightgbm==4.0.0

# 超參數優化
optuna==3.3.0

# 線性規劃
pulp==2.7.0

# 變化點檢測
ruptures==1.1.8