#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
異常處理模組
定義自定義異常類和異常處理器
"""

import logging
import traceback
from typing import Dict, Any, Optional, Type
from datetime import datetime
from abc import ABC, abstractmethod

class BaseException(Exception):
    """基礎異常類"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.timestamp = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            'error_code': self.error_code,
            'message': self.message,
            'details': self.details,
            'timestamp': self.timestamp,
            'type': self.__class__.__name__
        }
    
    def __str__(self) -> str:
        return f"{self.error_code}: {self.message}"

class ValidationError(BaseException):
    """驗證錯誤"""
    pass

class DatabaseError(BaseException):
    """數據庫錯誤"""
    pass

class PredictionError(BaseException):
    """預測錯誤"""
    pass

class AnalysisError(BaseException):
    """分析錯誤"""
    pass

class ConfigurationError(BaseException):
    """配置錯誤"""
    pass

class ServiceError(BaseException):
    """服務錯誤"""
    pass

class CacheError(BaseException):
    """緩存錯誤"""
    pass

class AuthenticationError(BaseException):
    """認證錯誤"""
    pass

class AuthorizationError(BaseException):
    """授權錯誤"""
    pass

class RateLimitError(BaseException):
    """速率限制錯誤"""
    pass

class DataNotFoundError(BaseException):
    """數據未找到錯誤"""
    pass

class InvalidLotteryTypeError(ValidationError):
    """無效彩票類型錯誤"""
    
    def __init__(self, lottery_type: str, valid_types: list = None):
        valid_types = valid_types or ['powercolor', 'lotto649', 'dailycash']
        message = f"無效的彩票類型: {lottery_type}。有效類型: {', '.join(valid_types)}"
        super().__init__(message, 'INVALID_LOTTERY_TYPE', {
            'provided_type': lottery_type,
            'valid_types': valid_types
        })

class InvalidParameterError(ValidationError):
    """無效參數錯誤"""
    
    def __init__(self, parameter: str, value: Any, expected: str = None):
        message = f"無效的參數 '{parameter}': {value}"
        if expected:
            message += f"。期望: {expected}"
        super().__init__(message, 'INVALID_PARAMETER', {
            'parameter': parameter,
            'value': str(value),
            'expected': expected
        })

class ModelNotFoundError(PredictionError):
    """模型未找到錯誤"""
    
    def __init__(self, model_type: str, lottery_type: str = None):
        message = f"模型未找到: {model_type}"
        if lottery_type:
            message += f" (彩票類型: {lottery_type})"
        super().__init__(message, 'MODEL_NOT_FOUND', {
            'model_type': model_type,
            'lottery_type': lottery_type
        })

class InsufficientDataError(AnalysisError):
    """數據不足錯誤"""
    
    def __init__(self, required_count: int, actual_count: int, data_type: str = "記錄"):
        message = f"數據不足：需要至少 {required_count} 條{data_type}，實際只有 {actual_count} 條"
        super().__init__(message, 'INSUFFICIENT_DATA', {
            'required_count': required_count,
            'actual_count': actual_count,
            'data_type': data_type
        })

class ExceptionHandler(ABC):
    """異常處理器基類"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def handle(self, exception: Exception) -> Dict[str, Any]:
        """處理異常
        
        Args:
            exception: 異常實例
            
        Returns:
            處理結果字典
        """
        pass
    
    def log_exception(self, exception: Exception, context: str = None) -> None:
        """記錄異常
        
        Args:
            exception: 異常實例
            context: 上下文信息
        """
        context_info = f" [{context}]" if context else ""
        
        if isinstance(exception, BaseException):
            self.logger.error(f"自定義異常{context_info}: {exception}")
            if exception.details:
                self.logger.error(f"異常詳情: {exception.details}")
        else:
            self.logger.error(f"系統異常{context_info}: {str(exception)}")
            self.logger.error(f"異常堆棧: {traceback.format_exc()}")

class WebExceptionHandler(ExceptionHandler):
    """Web 異常處理器"""
    
    def handle(self, exception: Exception) -> Dict[str, Any]:
        """處理 Web 異常
        
        Args:
            exception: 異常實例
            
        Returns:
            包含狀態碼和響應數據的字典
        """
        self.log_exception(exception, "Web API")
        
        if isinstance(exception, BaseException):
            return self._handle_custom_exception(exception)
        else:
            return self._handle_system_exception(exception)
    
    def _handle_custom_exception(self, exception: BaseException) -> Dict[str, Any]:
        """處理自定義異常"""
        status_code_map = {
            ValidationError: 400,
            InvalidLotteryTypeError: 400,
            InvalidParameterError: 400,
            AuthenticationError: 401,
            AuthorizationError: 403,
            DataNotFoundError: 404,
            ModelNotFoundError: 404,
            RateLimitError: 429,
            DatabaseError: 500,
            PredictionError: 500,
            AnalysisError: 500,
            ConfigurationError: 500,
            ServiceError: 500,
            CacheError: 500,
            InsufficientDataError: 422
        }
        
        status_code = 500  # 默認狀態碼
        for exc_type, code in status_code_map.items():
            if isinstance(exception, exc_type):
                status_code = code
                break
        
        return {
            'status_code': status_code,
            'response': {
                'success': False,
                'error': exception.to_dict(),
                'timestamp': datetime.now().isoformat()
            }
        }
    
    def _handle_system_exception(self, exception: Exception) -> Dict[str, Any]:
        """處理系統異常"""
        # 根據異常類型確定狀態碼
        if isinstance(exception, ValueError):
            status_code = 400
            error_message = f"參數錯誤: {str(exception)}"
        elif isinstance(exception, KeyError):
            status_code = 400
            error_message = f"缺少必需參數: {str(exception)}"
        elif isinstance(exception, FileNotFoundError):
            status_code = 404
            error_message = "請求的資源未找到"
        elif isinstance(exception, PermissionError):
            status_code = 403
            error_message = "權限不足"
        else:
            status_code = 500
            error_message = "內部服務器錯誤"
        
        return {
            'status_code': status_code,
            'response': {
                'success': False,
                'error': {
                    'error_code': 'SYSTEM_ERROR',
                    'message': error_message,
                    'type': exception.__class__.__name__,
                    'timestamp': datetime.now().isoformat()
                },
                'timestamp': datetime.now().isoformat()
            }
        }

class APIExceptionHandler(ExceptionHandler):
    """API 異常處理器"""
    
    def handle(self, exception: Exception) -> Dict[str, Any]:
        """處理 API 異常
        
        Args:
            exception: 異常實例
            
        Returns:
            API 響應格式的字典
        """
        self.log_exception(exception, "API")
        
        if isinstance(exception, BaseException):
            return {
                'success': False,
                'error': exception.to_dict(),
                'timestamp': datetime.now().isoformat()
            }
        else:
            return {
                'success': False,
                'error': {
                    'error_code': 'SYSTEM_ERROR',
                    'message': str(exception),
                    'type': exception.__class__.__name__,
                    'timestamp': datetime.now().isoformat()
                },
                'timestamp': datetime.now().isoformat()
            }

class ServiceExceptionHandler(ExceptionHandler):
    """服務異常處理器"""
    
    def handle(self, exception: Exception) -> Dict[str, Any]:
        """處理服務異常
        
        Args:
            exception: 異常實例
            
        Returns:
            服務響應格式的字典
        """
        self.log_exception(exception, "Service")
        
        return {
            'success': False,
            'error': str(exception),
            'error_type': exception.__class__.__name__,
            'timestamp': datetime.now().isoformat()
        }

# 全域異常處理器實例
_exception_handlers: Dict[str, ExceptionHandler] = {
    'web': WebExceptionHandler(),
    'api': APIExceptionHandler(),
    'service': ServiceExceptionHandler()
}

def get_exception_handler(handler_type: str = 'web') -> ExceptionHandler:
    """獲取異常處理器
    
    Args:
        handler_type: 處理器類型 ('web', 'api', 'service')
        
    Returns:
        異常處理器實例
    """
    return _exception_handlers.get(handler_type, _exception_handlers['web'])

def register_exception_handler(handler_type: str, handler: ExceptionHandler) -> None:
    """註冊異常處理器
    
    Args:
        handler_type: 處理器類型
        handler: 處理器實例
    """
    _exception_handlers[handler_type] = handler

def handle_exception(exception: Exception, handler_type: str = 'web') -> Dict[str, Any]:
    """處理異常的便捷函數
    
    Args:
        exception: 異常實例
        handler_type: 處理器類型
        
    Returns:
        處理結果
    """
    handler = get_exception_handler(handler_type)
    return handler.handle(exception)

# 常用異常創建函數
def create_validation_error(message: str, details: Dict[str, Any] = None) -> ValidationError:
    """創建驗證錯誤"""
    return ValidationError(message, details=details)

def create_invalid_lottery_type_error(lottery_type: str) -> InvalidLotteryTypeError:
    """創建無效彩票類型錯誤"""
    return InvalidLotteryTypeError(lottery_type)

def create_invalid_parameter_error(parameter: str, value: Any, expected: str = None) -> InvalidParameterError:
    """創建無效參數錯誤"""
    return InvalidParameterError(parameter, value, expected)

def create_model_not_found_error(model_type: str, lottery_type: str = None) -> ModelNotFoundError:
    """創建模型未找到錯誤"""
    return ModelNotFoundError(model_type, lottery_type)

def create_insufficient_data_error(required: int, actual: int, data_type: str = "記錄") -> InsufficientDataError:
    """創建數據不足錯誤"""
    return InsufficientDataError(required, actual, data_type)