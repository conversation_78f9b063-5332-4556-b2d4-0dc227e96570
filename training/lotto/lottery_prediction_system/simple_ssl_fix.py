#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單的 SSL 連接修復方案
"""

import ssl
import requests
import urllib3

# 抑制SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_connection():
    """測試網路連接"""
    print("🔧 簡單 SSL 連接修復器")
    print("=" * 40)
    
    # 創建不驗證SSL的會話
    session = requests.Session()
    session.verify = False
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
    })
    
    # 測試不同的URL
    test_urls = [
        ("Google", "https://www.google.com"),
        ("台灣彩券 (主站)", "https://www.taiwanlottery.com"),
        ("威力彩結果頁", "https://www.taiwanlottery.com/lotto/result/super_lotto638")
    ]
    
    working_count = 0
    
    for name, url in test_urls:
        try:
            print(f"🌐 測試 {name}...")
            response = session.get(url, timeout=15)
            
            if response.status_code == 200:
                print(f"✅ {name} 連接成功 (HTTP {response.status_code})")
                working_count += 1
            else:
                print(f"⚠️ {name} 連接異常 (HTTP {response.status_code})")
                
        except requests.exceptions.SSLError as e:
            print(f"❌ {name} SSL錯誤: {str(e)[:100]}...")
        except requests.exceptions.ConnectionError as e:
            print(f"❌ {name} 連接錯誤: {str(e)[:100]}...")
        except Exception as e:
            print(f"❌ {name} 其他錯誤: {str(e)[:100]}...")
    
    print(f"\n📊 測試結果: {working_count}/{len(test_urls)} 個連接成功")
    
    if working_count > 0:
        print("✅ 部分網路連接正常")
        if working_count >= 2:
            print("💡 建議系統使用備援資料源策略")
        else:
            print("⚠️ 建議檢查網路設定或使用手動資料")
    else:
        print("❌ 所有網路連接都失敗")
        print("💡 系統將使用離線模式和手動資料")
    
    session.close()
    
    # 創建系統配置建議
    config_suggestions = {
        "network_status": f"{working_count}/{len(test_urls)} connections working",
        "ssl_verification": False,
        "use_manual_backup": True if working_count < 2 else False,
        "timeout_settings": 15,
        "retry_attempts": 3
    }
    
    print("\n📋 系統配置建議:")
    for key, value in config_suggestions.items():
        print(f"   {key}: {value}")

if __name__ == "__main__":
    test_connection()