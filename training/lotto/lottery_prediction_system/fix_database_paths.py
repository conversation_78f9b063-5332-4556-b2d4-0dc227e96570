#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復所有使用錯誤資料庫路徑的程式檔案
"""

import os
import re

def fix_database_paths():
    """修正所有使用 lottery.db 的檔案，改為使用 lottery_data.db"""
    
    # 需要修改的檔案
    files_to_fix = [
        'web_api.py',
        'core/config.py',
        'updated_lottery_updater.py',
        'phase3/web_api.py'
    ]
    
    print("🔧 修復資料庫路徑配置")
    print("=" * 60)
    print("將所有 'data/lottery.db' 改為 'data/lottery_data.db'")
    print("=" * 60)
    
    fixed_count = 0
    
    for file_path in files_to_fix:
        if not os.path.exists(file_path):
            print(f"⏭️  檔案不存在: {file_path}")
            continue
            
        try:
            # 讀取檔案
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 檢查是否需要修改
            if 'data/lottery.db' in content and 'data/lottery_data.db' not in content:
                # 替換路徑
                original_content = content
                content = content.replace('data/lottery.db', 'data/lottery_data.db')
                
                # 寫回檔案
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                fixed_count += 1
                print(f"✅ 已修復: {file_path}")
                
                # 顯示修改的行
                lines_changed = []
                for i, (orig, new) in enumerate(zip(original_content.split('\n'), content.split('\n'))):
                    if orig != new:
                        lines_changed.append((i+1, new.strip()))
                
                if lines_changed:
                    print(f"   修改的行:")
                    for line_no, line in lines_changed[:3]:  # 只顯示前3行
                        print(f"     第{line_no}行: {line[:60]}...")
            else:
                print(f"⏭️  無需修改: {file_path}")
                
        except Exception as e:
            print(f"❌ 無法修改 {file_path}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 修復完成: 共修改 {fixed_count} 個檔案")
    
    # 驗證修改結果
    print("\n🔍 驗證修改結果...")
    remaining_issues = []
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 檢查是否還有 lottery.db（不包含 lottery_data.db）
            if re.search(r'lottery\.db(?![\w])', content) and 'lottery_data.db' not in content:
                remaining_issues.append(file_path)
    
    if remaining_issues:
        print("⚠️  仍有檔案使用 lottery.db:")
        for path in remaining_issues:
            print(f"   - {path}")
    else:
        print("✅ 所有檔案都已正確使用 lottery_data.db")
    
    return fixed_count

def verify_database_usage():
    """驗證整個系統的資料庫使用情況"""
    print("\n🔍 驗證整個系統的資料庫使用情況...")
    print("=" * 60)
    
    # 檢查 config.json
    if os.path.exists('config.json'):
        with open('config.json', 'r') as f:
            content = f.read()
        if 'lottery_data.db' in content:
            print("✅ config.json 正確使用 lottery_data.db")
        else:
            print("⚠️  config.json 可能需要檢查")
    
    # 檢查主要模組
    main_modules = [
        'data/db_manager.py',
        'web/app.py',
        'automated_lottery_updater.py'
    ]
    
    for module in main_modules:
        if os.path.exists(module):
            with open(module, 'r') as f:
                content = f.read()
            
            if 'lottery_data.db' in content:
                print(f"✅ {module} 使用正確的資料庫")
            elif 'lottery.db' in content:
                print(f"❌ {module} 使用錯誤的資料庫")
            else:
                print(f"ℹ️  {module} 可能使用變數配置")

def main():
    print("🎯 資料庫路徑修復工具")
    print("=" * 60)
    
    # 修復路徑
    fixed = fix_database_paths()
    
    # 驗證結果
    verify_database_usage()
    
    print("\n" + "=" * 60)
    print("🎉 完成！系統現在統一使用 data/lottery_data.db")

if __name__ == "__main__":
    main()