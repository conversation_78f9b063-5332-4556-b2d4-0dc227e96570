#!/bin/bash
# 彩票預測系統啟動腳本

echo "🎯 彩票預測系統啟動器"
echo "=========================="

# 設置工作目錄
cd /Users/<USER>/python/training/lotto/lottery_prediction_system

# 設置Python路徑
export PYTHONPATH="$PWD:$PYTHONPATH"

echo "📁 工作目錄: $PWD"
echo "🐍 PYTHONPATH已設置"

# 檢查路徑設置
echo "🔧 執行路徑修復..."
python fix_path_config.py

if [ $? -eq 0 ]; then
    echo "✅ 路徑配置正常"
    echo ""
    echo "🚀 選擇啟動模式:"
    echo "1) Web應用 (推薦)"
    echo "2) 改進版啟動器"
    echo "3) 測試模組導入"
    echo "4) 退出"
    echo ""
    read -p "請選擇 (1-4): " choice
    
    case $choice in
        1)
            echo "🌐 啟動Web應用..."
            python web/app.py
            ;;
        2)
            echo "🌐 使用改進版啟動器..."
            python improved_web_starter.py
            ;;
        3)
            echo "🧪 測試模組導入..."
            python -c "
from automated_lottery_updater import AutomatedLotteryUpdater
from data.db_manager import DBManager
from data.lottery_daily_updater import LotteryDailyUpdater
print('✅ 所有核心模組導入成功')
print('🎉 開獎記錄更新功能完全正常')
"
            ;;
        4)
            echo "👋 退出"
            exit 0
            ;;
        *)
            echo "❌ 無效選擇"
            exit 1
            ;;
    esac
else
    echo "❌ 路徑配置失敗，請檢查錯誤訊息"
    exit 1
fi