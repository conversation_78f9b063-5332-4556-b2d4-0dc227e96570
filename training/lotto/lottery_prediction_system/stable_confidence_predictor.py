#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
穩定信心度預測器
結合版路追蹤和確定性分析，產生真正穩定且有依據的預測
"""

import sqlite3
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional, Set
from collections import Counter, defaultdict
import logging
import os

# 導入版路追蹤和確定性預測
from pattern_tracker import PatternTracker
from deterministic_confidence_predictor import DeterministicConfidencePredictor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('stable_predictor')

class StableConfidencePredictor:
    """
    穩定信心度預測器
    - 結合版路分析找出有規律的核心號碼
    - 使用確定性算法確保結果穩定
    - 每次產生相同的預測結果
    """
    
    def __init__(self, db_path: str = None):
        if db_path is None:
            self.db_path = os.path.join(
                os.path.dirname(os.path.abspath(__file__)), 
                'data', 'lottery_data.db'
            )
        else:
            self.db_path = db_path
        
        self.pattern_tracker = PatternTracker(self.db_path)
        self.deterministic_predictor = DeterministicConfidencePredictor(self.db_path)
    
    def generate_stable_prediction(self, lottery_type: str = 'powercolor') -> Dict:
        """
        生成穩定預測
        每次執行都會返回相同的結果
        """
        logger.info(f"開始穩定預測分析 - {lottery_type}")
        
        # 1. 版路分析 - 找出有規律可循的核心號碼
        pattern_result = self.pattern_tracker.identify_traceable_patterns(lottery_type, 100)
        traceable_numbers = pattern_result.get('traceable_numbers', [])
        
        # 2. 確定性分析 - 基於歷史數據的穩定分析
        deterministic_result = self.deterministic_predictor.get_deterministic_prediction(lottery_type)
        deterministic_numbers = deterministic_result.get('prediction_numbers', [])
        
        # 3. 智能融合 - 將版路號碼與確定性分析結合
        final_prediction = self._intelligent_fusion(
            traceable_numbers,
            deterministic_numbers,
            pattern_result,
            deterministic_result,
            lottery_type
        )
        
        # 4. 穩定性驗證
        stability_score = self._calculate_stability_score(
            final_prediction,
            pattern_result,
            deterministic_result
        )
        
        # 5. 生成最終報告
        result = self._generate_final_report(
            final_prediction,
            pattern_result,
            deterministic_result,
            stability_score,
            lottery_type
        )
        
        logger.info(f"穩定預測完成 - 號碼: {final_prediction['numbers']}")
        return result
    
    def _intelligent_fusion(self, traceable_numbers: List[int], 
                           deterministic_numbers: List[int],
                           pattern_result: Dict, deterministic_result: Dict,
                           lottery_type: str) -> Dict:
        """
        智能融合版路號碼與確定性分析
        """
        
        # 確定選號數量
        if lottery_type == 'dailycash':
            target_count = 5
        else:
            target_count = 6
        
        selected_numbers = []
        selection_reasons = []
        
        # Step 1: 優先選擇有版路支撐的號碼
        for num in traceable_numbers[:3]:  # 最多3個版路號碼
            selected_numbers.append(num)
            
            # 找出版路證據
            pattern_evidence = []
            for detail in pattern_result.get('detailed_analysis', []):
                if detail['number'] == num:
                    evidence_types = [ev['type'] for ev in detail['evidence']]
                    pattern_evidence = evidence_types
                    break
            
            selection_reasons.append({
                'number': num,
                'reason': '版路支撐',
                'evidence': pattern_evidence,
                'confidence': 'high'
            })
        
        # Step 2: 從確定性分析中選擇補充號碼
        remaining_count = target_count - len(selected_numbers)
        
        for num in deterministic_numbers:
            if num not in selected_numbers and remaining_count > 0:
                selected_numbers.append(num)
                selection_reasons.append({
                    'number': num,
                    'reason': '數據分析',
                    'evidence': ['頻率分析', '趨勢分析', '模式分析'],
                    'confidence': 'medium'
                })
                remaining_count -= 1
        
        # Step 3: 如果還不夠，用確定性算法補充
        if len(selected_numbers) < target_count:
            # 根據歷史數據選擇安全號碼
            safe_numbers = self._get_safe_numbers(lottery_type, selected_numbers)
            for num in safe_numbers:
                if len(selected_numbers) >= target_count:
                    break
                selected_numbers.append(num)
                selection_reasons.append({
                    'number': num,
                    'reason': '安全補充',
                    'evidence': ['歷史穩定'],
                    'confidence': 'low'
                })
        
        # 選擇特別號
        special_number = self._select_stable_special(lottery_type)
        
        return {
            'numbers': sorted(selected_numbers),
            'special': special_number,
            'selection_reasons': selection_reasons,
            'fusion_method': 'pattern_deterministic_fusion'
        }
    
    def _get_safe_numbers(self, lottery_type: str, exclude: List[int]) -> List[int]:
        """
        獲取安全的補充號碼（基於歷史穩定性）
        """
        try:
            conn = sqlite3.connect(self.db_path)
            
            if lottery_type == 'powercolor':
                query = """
                SELECT Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6
                FROM Powercolor
                ORDER BY Period DESC
                LIMIT 50
                """
                num_range = range(1, 39)
            elif lottery_type == 'lotto649':
                query = """
                SELECT Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6
                FROM Lotto649
                ORDER BY Period DESC
                LIMIT 50
                """
                num_range = range(1, 50)
            else:  # dailycash
                query = """
                SELECT Anumber1, Anumber2, Anumber3, Anumber4, Anumber5
                FROM DailyCash
                ORDER BY Period DESC
                LIMIT 50
                """
                num_range = range(1, 40)
            
            cursor = conn.execute(query)
            rows = cursor.fetchall()
            conn.close()
            
            # 統計每個號碼的出現頻率
            number_count = Counter()
            for row in rows:
                for num in row:
                    if num:
                        number_count[num] += 1
            
            # 選擇中等頻率的號碼（不太熱也不太冷）
            total_appearances = sum(number_count.values())
            avg_frequency = total_appearances / len(num_range)
            
            safe_candidates = []
            for num in num_range:
                if num not in exclude:
                    count = number_count.get(num, 0)
                    # 選擇頻率在平均值70%-130%之間的號碼
                    if 0.7 * avg_frequency <= count <= 1.3 * avg_frequency:
                        safe_candidates.append(num)
            
            # 按號碼大小排序，確保結果穩定
            return sorted(safe_candidates)
            
        except Exception as e:
            logger.error(f"獲取安全號碼失敗: {str(e)}")
            # 默認安全號碼
            if lottery_type == 'powercolor':
                default_safe = [7, 14, 21, 28]
            elif lottery_type == 'lotto649':
                default_safe = [7, 14, 21, 35]
            else:
                default_safe = [7, 14, 21]
            
            return [num for num in default_safe if num not in exclude]
    
    def _select_stable_special(self, lottery_type: str) -> Optional[int]:
        """選擇穩定的特別號"""
        
        if lottery_type == 'dailycash':
            return None
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            if lottery_type == 'powercolor':
                query = """
                SELECT Second_district
                FROM Powercolor
                WHERE Second_district IS NOT NULL
                ORDER BY Period DESC
                LIMIT 50
                """
            else:  # lotto649
                query = """
                SELECT SpecialNumber
                FROM Lotto649
                WHERE SpecialNumber IS NOT NULL
                ORDER BY Period DESC
                LIMIT 50
                """
            
            cursor = conn.execute(query)
            rows = cursor.fetchall()
            conn.close()
            
            if not rows:
                return 1  # 默認
            
            # 統計特別號頻率
            special_count = Counter([row[0] for row in rows if row[0]])
            
            if special_count:
                # 選擇出現頻率第二高的（第一高可能太熱）
                most_common = special_count.most_common(3)
                if len(most_common) >= 2:
                    return most_common[1][0]  # 第二常見的
                else:
                    return most_common[0][0]  # 最常見的
            
            return 1
            
        except Exception as e:
            logger.error(f"選擇特別號失敗: {str(e)}")
            return 1
    
    def _calculate_stability_score(self, prediction: Dict, 
                                  pattern_result: Dict, 
                                  deterministic_result: Dict) -> float:
        """計算預測穩定性分數"""
        
        stability_factors = []
        
        # 1. 版路支撐度
        pattern_count = len([r for r in prediction['selection_reasons'] if r['reason'] == '版路支撐'])
        pattern_support = pattern_count / len(prediction['numbers'])
        stability_factors.append(pattern_support * 0.4)
        
        # 2. 確定性分析支撐度
        deterministic_confidence = deterministic_result.get('confidence_score', 0)
        stability_factors.append(deterministic_confidence * 0.3)
        
        # 3. 號碼分布合理性
        numbers = prediction['numbers']
        
        # 奇偶平衡
        odd_count = sum(1 for n in numbers if n % 2 == 1)
        even_count = len(numbers) - odd_count
        odd_even_balance = 1 - abs(odd_count - even_count) / len(numbers)
        stability_factors.append(odd_even_balance * 0.1)
        
        # 大小平衡
        mid_point = 20
        small_count = sum(1 for n in numbers if n < mid_point)
        large_count = len(numbers) - small_count
        size_balance = 1 - abs(small_count - large_count) / len(numbers)
        stability_factors.append(size_balance * 0.1)
        
        # 尾數分布
        tails = [n % 10 for n in numbers]
        tail_diversity = len(set(tails)) / len(numbers)
        stability_factors.append(tail_diversity * 0.1)
        
        return sum(stability_factors)
    
    def _generate_final_report(self, prediction: Dict, pattern_result: Dict,
                              deterministic_result: Dict, stability_score: float,
                              lottery_type: str) -> Dict:
        """生成最終報告"""
        
        # 計算信心度
        confidence_score = min(0.85, stability_score + 0.1)
        
        # 生成選號解釋
        explanations = []
        for reason in prediction['selection_reasons']:
            num = reason['number']
            reason_type = reason['reason']
            evidence = ', '.join(reason['evidence'])
            explanations.append(f"號碼 {num}: {reason_type} ({evidence})")
        
        result = {
            'prediction_type': '穩定信心度預測',
            'lottery_type': lottery_type,
            'numbers': prediction['numbers'],
            'special': prediction['special'],
            'confidence_score': confidence_score,
            'stability_score': stability_score,
            'is_stable': True,
            'methodology': {
                'step1': '版路追蹤 - 識別有規律可循的號碼',
                'step2': '確定性分析 - 基於歷史數據的穩定算法',
                'step3': '智能融合 - 優先版路號碼，確定性補充',
                'step4': '穩定性驗證 - 多維度穩定性評估'
            },
            'selection_details': {
                'pattern_supported': len([r for r in prediction['selection_reasons'] if r['reason'] == '版路支撐']),
                'data_supported': len([r for r in prediction['selection_reasons'] if r['reason'] == '數據分析']),
                'safe_numbers': len([r for r in prediction['selection_reasons'] if r['reason'] == '安全補充']),
                'explanations': explanations
            },
            'pattern_analysis': {
                'traceable_count': pattern_result.get('reliable_count', 0),
                'pattern_confidence': pattern_result.get('overall_confidence', 0),
                'pattern_recommendation': pattern_result.get('recommendation', '')
            },
            'deterministic_analysis': {
                'method': deterministic_result.get('prediction_method', ''),
                'confidence': deterministic_result.get('confidence_score', 0),
                'data_periods': deterministic_result.get('analysis_basis', {}).get('historical_periods', 0)
            },
            'stability_guarantee': '此預測結合版路分析與確定性算法，每次執行結果保持一致',
            'usage_recommendation': self._generate_usage_recommendation(prediction, pattern_result, stability_score),
            'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return result
    
    def _generate_usage_recommendation(self, prediction: Dict, pattern_result: Dict, 
                                     stability_score: float) -> str:
        """生成使用建議"""
        
        pattern_count = len([r for r in prediction['selection_reasons'] if r['reason'] == '版路支撐'])
        
        if pattern_count >= 3 and stability_score > 0.6:
            return "高穩定性預測，建議作為主要投注組合，版路支撐強勁"
        elif pattern_count >= 2 and stability_score > 0.5:
            return "中等穩定性預測，建議與其他分析方法結合使用"
        elif stability_score > 0.4:
            return "基礎穩定性預測，建議小額測試或作為參考"
        else:
            return "穩定性較低，建議等待更多數據或使用其他方法"

    def test_consistency(self, lottery_type: str = 'powercolor', test_rounds: int = 3) -> Dict:
        """測試預測一致性"""
        
        results = []
        for i in range(test_rounds):
            prediction = self.generate_stable_prediction(lottery_type)
            results.append({
                'round': i + 1,
                'numbers': prediction['numbers'],
                'special': prediction['special'],
                'confidence': prediction['confidence_score']
            })
        
        # 檢查一致性
        first_numbers = results[0]['numbers']
        first_special = results[0]['special']
        
        is_consistent = all(
            r['numbers'] == first_numbers and r['special'] == first_special 
            for r in results
        )
        
        return {
            'is_consistent': is_consistent,
            'test_rounds': test_rounds,
            'results': results,
            'consistency_rate': '100%' if is_consistent else '不一致',
            'message': '✅ 預測結果完全一致' if is_consistent else '❌ 預測結果不一致'
        }


def demonstrate_stable_predictor():
    """演示穩定預測器"""
    
    predictor = StableConfidencePredictor()
    
    print("=" * 100)
    print("穩定信心度預測器 - 版路分析 + 確定性算法")
    print("=" * 100)
    
    # 1. 生成穩定預測
    result = predictor.generate_stable_prediction('powercolor')
    
    print(f"\n🎯 預測結果:")
    print(f"預測號碼: {result['numbers']}")
    print(f"特別號: {result['special']}")
    print(f"信心度: {result['confidence_score']:.1%}")
    print(f"穩定性: {result['stability_score']:.1%}")
    
    print(f"\n📊 選號分析:")
    details = result['selection_details']
    print(f"版路支撐號碼: {details['pattern_supported']} 個")
    print(f"數據分析號碼: {details['data_supported']} 個")
    print(f"安全補充號碼: {details['safe_numbers']} 個")
    
    print(f"\n🔍 詳細說明:")
    for explanation in details['explanations']:
        print(f"  {explanation}")
    
    print(f"\n📈 版路分析:")
    pattern_analysis = result['pattern_analysis']
    print(f"可追蹤號碼數量: {pattern_analysis['traceable_count']} 個")
    print(f"版路信心度: {pattern_analysis['pattern_confidence']:.1%}")
    print(f"版路建議: {pattern_analysis['pattern_recommendation']}")
    
    print(f"\n💡 使用建議:")
    print(f"{result['usage_recommendation']}")
    
    # 2. 測試一致性
    print(f"\n" + "=" * 100)
    print("一致性測試 - 驗證預測穩定性")
    print("=" * 100)
    
    consistency_test = predictor.test_consistency('powercolor', 3)
    
    print(f"\n{consistency_test['message']}")
    print(f"測試輪數: {consistency_test['test_rounds']}")
    print(f"一致性率: {consistency_test['consistency_rate']}")
    
    if consistency_test['is_consistent']:
        print(f"穩定預測號碼: {consistency_test['results'][0]['numbers']}")
        print(f"特別號: {consistency_test['results'][0]['special']}")
    else:
        print("各輪結果:")
        for result in consistency_test['results']:
            print(f"  第{result['round']}輪: {result['numbers']}")


if __name__ == "__main__":
    demonstrate_stable_predictor()