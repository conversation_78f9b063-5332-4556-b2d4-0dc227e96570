#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復 SSL 憑證驗證問題的網路連接修復器
"""

import ssl
import requests
from urllib3.util.ssl_ import create_urllib3_context
import urllib3
import logging

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SSLConnectionFixer:
    """SSL 連接修復器"""
    
    def __init__(self):
        # 抑制SSL警告
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        urllib3.disable_warnings(urllib3.exceptions.SubjectAltNameWarning)
        
    def create_ssl_context(self):
        """創建寬鬆的SSL上下文"""
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        # 允許舊的協議
        context.set_ciphers('DEFAULT:!DH')
        
        return context
    
    def create_session_with_ssl_fix(self):
        """創建修復SSL問題的會話"""
        session = requests.Session()
        
        # 設定不驗證SSL憑證
        session.verify = False
        
        # 設定更寬鬆的請求標頭
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        return session
    
    def test_connection(self, url: str = "https://www.taiwanlottery.com") -> bool:
        """測試連接到台灣彩券官網"""
        session = self.create_session_with_ssl_fix()
        
        try:
            logger.info(f"🌐 測試連接到 {url}...")
            response = session.get(url, timeout=15)
            
            if response.status_code == 200:
                logger.info(f"✅ 連接成功 (HTTP {response.status_code})")
                return True
            else:
                logger.warning(f"⚠️ 連接異常 (HTTP {response.status_code})")
                return False
                
        except requests.exceptions.SSLError as e:
            logger.error(f"❌ SSL錯誤: {e}")
            return False
        except requests.exceptions.ConnectionError as e:
            logger.error(f"❌ 連接錯誤: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ 其他錯誤: {e}")
            return False
        finally:
            session.close()
    
    def apply_global_ssl_fix(self):
        """應用全域SSL修復"""
        try:
            # 設定全域SSL上下文
            ssl_context = self.create_ssl_context()
            
            # 修改urllib3的默認SSL上下文
            urllib3.util.ssl_.create_urllib3_context = lambda: ssl_context
            
            # 設定環境變數（可選）
            import os
            os.environ['PYTHONHTTPSVERIFY'] = '0'
            os.environ['REQUESTS_CA_BUNDLE'] = ''
            
            logger.info("✅ 全域SSL修復已應用")
            
        except Exception as e:
            logger.error(f"❌ 全域SSL修復失敗: {e}")
    
    def create_fixed_scraper_config(self) -> dict:
        """創建修復SSL問題的爬蟲配置"""
        return {
            'verify_ssl': False,
            'timeout': 30,
            'headers': {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
                'Connection': 'keep-alive'
            },
            'allow_redirects': True,
            'max_redirects': 5
        }

def main():
    """主函數"""
    print("🔧 SSL 連接問題修復器")
    print("=" * 40)
    
    fixer = SSLConnectionFixer()
    
    # 應用全域修復
    fixer.apply_global_ssl_fix()
    
    # 測試連接
    urls_to_test = [
        "https://www.google.com",
        "https://www.taiwanlottery.com",
        "https://www.taiwanlottery.com/lotto/result/super_lotto638"
    ]
    
    working_urls = []
    for url in urls_to_test:
        if fixer.test_connection(url):
            working_urls.append(url)
    
    print(f"\n📊 測試結果: {len(working_urls)}/{len(urls_to_test)} 個URL可連接")
    
    if working_urls:
        print("✅ 可連接的URL:")
        for url in working_urls:
            print(f"   - {url}")
    
    if "https://www.taiwanlottery.com" in working_urls:
        print("\n🎉 台灣彩券官網連接修復成功！")
        print("💡 建議重新啟動彩票預測系統以使用修復")
    else:
        print("\n⚠️ 台灣彩券官網仍無法連接，將使用備援資料")
    
    # 創建配置文件
    config = fixer.create_fixed_scraper_config()
    
    import json
    with open('ssl_fix_config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("📄 SSL修復配置已儲存到 ssl_fix_config.json")

if __name__ == "__main__":
    main()