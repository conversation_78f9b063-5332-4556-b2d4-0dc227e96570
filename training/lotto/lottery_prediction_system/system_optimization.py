#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彩票預測系統優化工具
解決模組依賴和網路連接問題
"""

import os
import json
import subprocess
import sys
from pathlib import Path

class SystemOptimizer:
    """系統優化器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.config_path = self.project_root / "config.json"
    
    def update_config_for_ssl(self):
        """更新配置文件以解決SSL問題"""
        try:
            # 載入現有配置
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                config = {}
            
            # 新增網路配置
            if 'network' not in config:
                config['network'] = {}
            
            config['network'].update({
                'ssl_verify': False,
                'timeout': 15,
                'retry_attempts': 3,
                'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'backup_enabled': True
            })
            
            # 更新爬蟲設定
            if 'scraper' not in config:
                config['scraper'] = {}
            
            config['scraper'].update({
                'ssl_verification': False,
                'enable_fallback': True,
                'fallback_sources': ['manual', 'cache'],
                'max_retries': 3
            })
            
            # 儲存更新的配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print("✅ 配置文件已更新，加入SSL修復設定")
            return True
            
        except Exception as e:
            print(f"❌ 配置文件更新失敗: {e}")
            return False
    
    def create_ssl_environment_fix(self):
        """創建SSL環境修復腳本"""
        ssl_fix_content = '''#!/usr/bin/env python3
"""
SSL環境修復 - 在系統啟動前執行
"""

import ssl
import urllib3
import os

# 抑制SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 設定環境變數
os.environ['PYTHONHTTPSVERIFY'] = '0'
os.environ['CURL_CA_BUNDLE'] = ''
os.environ['REQUESTS_CA_BUNDLE'] = ''

# 創建寬鬆的SSL上下文
def create_unverified_context():
    context = ssl.create_default_context()
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE
    return context

# 替換默認SSL上下文
ssl._create_default_https_context = create_unverified_context

print("🔧 SSL環境修復已應用")
'''
        
        ssl_fix_path = self.project_root / "ssl_env_fix.py"
        
        try:
            with open(ssl_fix_path, 'w', encoding='utf-8') as f:
                f.write(ssl_fix_content)
            
            print(f"✅ SSL環境修復腳本已創建: {ssl_fix_path}")
            return True
            
        except Exception as e:
            print(f"❌ SSL修復腳本創建失敗: {e}")
            return False
    
    def create_optimized_requirements(self):
        """創建優化的依賴需求文件"""
        optimized_requirements = [
            "# 核心依賴",
            "Flask>=2.3.0",
            "Flask-CORS>=4.0.0", 
            "requests>=2.31.0",
            "numpy>=1.24.0",
            "pandas>=2.0.0",
            "scikit-learn>=1.3.0",
            "",
            "# 快取和資料庫",
            "redis>=4.6.0",
            "sqlite3",  # 內建模組
            "",
            "# 網路和爬蟲",
            "beautifulsoup4>=4.12.0",
            "urllib3>=1.26.0",
            "",
            "# 系統工具",
            "python-dotenv>=1.0.0",
            "python-dateutil>=2.8.0",
            "pytz>=2023.3",
            "",
            "# 可選依賴 (可能需要手動安裝)",
            "# TaiwanLottery",
            "# playwright",
            "# optimization",
        ]
        
        requirements_path = self.project_root / "requirements_optimized.txt"
        
        try:
            with open(requirements_path, 'w', encoding='utf-8') as f:
                f.write('\\n'.join(optimized_requirements))
            
            print(f"✅ 優化需求文件已創建: {requirements_path}")
            return True
            
        except Exception as e:
            print(f"❌ 需求文件創建失敗: {e}")
            return False
    
    def install_basic_dependencies(self):
        """安裝基礎依賴"""
        basic_packages = [
            "redis", 
            "python-dotenv", 
            "beautifulsoup4",
            "lxml"
        ]
        
        print("🔧 安裝基礎依賴包...")
        success_count = 0
        
        for package in basic_packages:
            try:
                print(f"📦 安裝 {package}...")
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", package, "--quiet"
                ], capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    print(f"✅ {package} 安裝成功")
                    success_count += 1
                else:
                    print(f"⚠️ {package} 安裝跳過")
                    
            except subprocess.TimeoutExpired:
                print(f"⚠️ {package} 安裝超時，跳過")
            except Exception as e:
                print(f"⚠️ {package} 安裝錯誤: {e}")
        
        print(f"📊 依賴安裝結果: {success_count}/{len(basic_packages)} 成功")
        return success_count > 0
    
    def create_startup_wrapper(self):
        """創建啟動包裝腳本"""
        wrapper_content = '''#!/usr/bin/env python3
"""
優化的系統啟動包裝器
"""

print("🚀 彩票預測系統 - 優化啟動器")
print("=" * 50)

# 1. 應用SSL修復
try:
    exec(open('ssl_env_fix.py').read())
except FileNotFoundError:
    print("⚠️ SSL修復腳本不存在，跳過")
except Exception as e:
    print(f"⚠️ SSL修復應用失敗: {e}")

# 2. 檢查關鍵依賴
missing_deps = []

try:
    import redis
    print("✅ redis 可用")
except ImportError:
    missing_deps.append("redis")

try:
    import dotenv
    print("✅ python-dotenv 可用")  
except ImportError:
    missing_deps.append("python-dotenv")

if missing_deps:
    print(f"⚠️ 缺少依賴: {', '.join(missing_deps)}")
    print("💡 系統將使用替代方案運行")
else:
    print("✅ 所有關鍵依賴都可用")

# 3. 啟動主系統
print("\\n🌐 啟動主系統...")

try:
    # 導入並啟動主應用
    import app
    if hasattr(app, 'app'):
        print("✅ Flask應用載入成功")
        print("🎯 系統運行在: http://localhost:7891")
        app.app.run(host='0.0.0.0', port=7891, debug=True)
    else:
        print("❌ Flask應用載入失敗")
except Exception as e:
    print(f"❌ 系統啟動失敗: {e}")
    print("💡 請檢查系統配置或使用原始啟動腳本")
'''
        
        wrapper_path = self.project_root / "optimized_start.py"
        
        try:
            with open(wrapper_path, 'w', encoding='utf-8') as f:
                f.write(wrapper_content)
            
            # 設定執行權限
            os.chmod(wrapper_path, 0o755)
            
            print(f"✅ 優化啟動器已創建: {wrapper_path}")
            return True
            
        except Exception as e:
            print(f"❌ 啟動器創建失敗: {e}")
            return False
    
    def optimize_system(self):
        """執行完整系統優化"""
        print("🎯 彩票預測系統優化器")
        print("=" * 50)
        
        tasks = [
            ("更新配置文件", self.update_config_for_ssl),
            ("創建SSL修復", self.create_ssl_environment_fix),
            ("安裝基礎依賴", self.install_basic_dependencies), 
            ("創建需求文件", self.create_optimized_requirements),
            ("創建啟動器", self.create_startup_wrapper),
        ]
        
        success_count = 0
        
        for task_name, task_func in tasks:
            print(f"\\n🔧 執行: {task_name}...")
            try:
                if task_func():
                    success_count += 1
            except Exception as e:
                print(f"❌ {task_name} 失敗: {e}")
        
        print(f"\\n📊 優化結果: {success_count}/{len(tasks)} 項成功")
        
        if success_count >= len(tasks) - 1:  # 允許一項失敗
            print("\\n🎉 系統優化完成！")
            print("\\n💡 使用方式:")
            print("1. 重新啟動系統: python3 optimized_start.py")
            print("2. 或使用原始啟動: ./start_lottery_system.sh")
            print("3. 網頁訪問: http://localhost:7891")
        else:
            print("\\n⚠️ 部分優化失敗，但系統仍可正常運行")
            print("💡 建議使用原始啟動腳本")

def main():
    optimizer = SystemOptimizer()
    optimizer.optimize_system()

if __name__ == "__main__":
    main()