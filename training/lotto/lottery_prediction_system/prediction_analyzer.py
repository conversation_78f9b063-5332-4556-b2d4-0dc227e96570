#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
預測分析器 - 提供實用的預測對比和分析工具
"""

import sys
import os
import pandas as pd
from datetime import datetime
import json
from typing import Dict, List, Tuple

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from unified_prediction_system import UnifiedPredictionSystem
from data.db_manager import DBManager

class PredictionAnalyzer:
    """預測分析器"""
    
    def __init__(self):
        self.system = UnifiedPredictionSystem()
        self.db_manager = DBManager()
    
    def generate_complete_analysis(self, lottery_type: str) -> Dict:
        """生成完整的預測分析報告"""
        
        # 獲取數據
        df = self.system.get_lottery_data(lottery_type)
        
        if df.empty:
            return {"error": f"無法載入{lottery_type}數據"}
        
        # 執行所有預測方法
        stat_result = self.system.statistical_prediction(df, lottery_type)
        math_result = self.system.mathematical_prediction(df, lottery_type)
        trend_result = self.system.trend_analysis_prediction(df, lottery_type)
        ml_result = self.system.ml_prediction(df, lottery_type)
        
        # 綜合預測
        all_predictions = [stat_result, math_result, trend_result, ml_result]
        ensemble_result = self.system.ensemble_prediction(all_predictions)
        
        # 分析最新開獎結果
        latest_draw = self._get_latest_draw(df, lottery_type)
        
        # 號碼分析
        number_analysis = self._analyze_numbers(df, lottery_type)
        
        # 趨勢分析
        trend_analysis = self._analyze_trends(df, lottery_type)
        
        return {
            "lottery_type": lottery_type,
            "lottery_name": self._get_lottery_name(lottery_type),
            "timestamp": datetime.now().isoformat(),
            "data_info": {
                "total_records": len(df),
                "latest_period": latest_draw.get("period", "N/A"),
                "latest_date": latest_draw.get("date", "N/A"),
                "data_range": f"{df.iloc[-1]['Sdate']} ~ {df.iloc[0]['Sdate']}" if len(df) > 1 else df.iloc[0]['Sdate']
            },
            "predictions": {
                "statistical": stat_result,
                "mathematical": math_result,
                "trend": trend_result,
                "machine_learning": ml_result,
                "ensemble": ensemble_result
            },
            "latest_draw": latest_draw,
            "number_analysis": number_analysis,
            "trend_analysis": trend_analysis,
            "recommendations": self._generate_recommendations(ensemble_result, number_analysis)
        }
    
    def _get_lottery_name(self, lottery_type: str) -> str:
        """獲取彩票中文名稱"""
        names = {
            "lotto649": "大樂透",
            "powercolor": "威力彩", 
            "dailycash": "今彩539"
        }
        return names.get(lottery_type, lottery_type)
    
    def _get_latest_draw(self, df: pd.DataFrame, lottery_type: str) -> Dict:
        """獲取最新開獎結果"""
        if df.empty:
            return {}
        
        latest = df.iloc[0]
        
        select_count = 6 if lottery_type in ['lotto649', 'powercolor'] else 5
        
        numbers = []
        for i in range(1, select_count + 1):
            col = f'Anumber{i}'
            if pd.notna(latest.get(col)):
                numbers.append(int(latest[col]))
        
        special_number = None
        if lottery_type in ['lotto649', 'powercolor']:
            special_col = 'SpecialNumber' if 'SpecialNumber' in latest else 'Anumber7'
            if pd.notna(latest.get(special_col)):
                special_number = int(latest[special_col])
        
        return {
            "period": latest.get('Period', 'N/A'),
            "date": str(latest.get('Sdate', 'N/A'))[:10],
            "numbers": sorted(numbers),
            "special_number": special_number
        }
    
    def _analyze_numbers(self, df: pd.DataFrame, lottery_type: str) -> Dict:
        """分析號碼統計"""
        select_count = 6 if lottery_type in ['lotto649', 'powercolor'] else 5
        max_num = 49 if lottery_type == 'lotto649' else (38 if lottery_type == 'powercolor' else 39)
        
        # 收集所有號碼
        all_numbers = []
        for _, row in df.head(100).iterrows():  # 分析最近100期
            for i in range(1, select_count + 1):
                col = f'Anumber{i}'
                if pd.notna(row.get(col)):
                    all_numbers.append(int(row[col]))
        
        from collections import Counter
        counter = Counter(all_numbers)
        
        # 計算統計
        total_draws = len(df.head(100))
        
        # 熱門號碼 (出現頻率 > 平均)
        avg_frequency = len(all_numbers) / max_num
        hot_numbers = [(num, count) for num, count in counter.items() if count > avg_frequency]
        hot_numbers.sort(key=lambda x: x[1], reverse=True)
        
        # 冷門號碼 (出現頻率 < 平均)
        cold_numbers = [(num, count) for num, count in counter.items() if count < avg_frequency and count > 0]
        cold_numbers.sort(key=lambda x: x[1])
        
        # 遺漏號碼 (最近沒出現)
        recent_numbers = []
        for _, row in df.head(10).iterrows():  # 最近10期
            for i in range(1, select_count + 1):
                col = f'Anumber{i}'
                if pd.notna(row.get(col)):
                    recent_numbers.append(int(row[col]))
        
        recent_set = set(recent_numbers)
        missing_numbers = [num for num in range(1, max_num + 1) if num not in recent_set]
        
        # 奇偶分析
        odd_count = sum(1 for num in all_numbers if num % 2 == 1)
        even_count = len(all_numbers) - odd_count
        
        # 大小分析 (以中位數為界)
        mid = max_num // 2
        small_count = sum(1 for num in all_numbers if num <= mid)
        large_count = len(all_numbers) - small_count
        
        return {
            "total_analysis": len(all_numbers),
            "average_frequency": round(avg_frequency, 2),
            "hot_numbers": hot_numbers[:10],
            "cold_numbers": cold_numbers[:10],
            "missing_numbers": sorted(missing_numbers[:10]),
            "odd_even_ratio": {
                "odd": odd_count,
                "even": even_count,
                "odd_percentage": round(odd_count / len(all_numbers) * 100, 1),
                "even_percentage": round(even_count / len(all_numbers) * 100, 1)
            },
            "size_ratio": {
                "small": small_count,
                "large": large_count,
                "small_percentage": round(small_count / len(all_numbers) * 100, 1),
                "large_percentage": round(large_count / len(all_numbers) * 100, 1)
            }
        }
    
    def _analyze_trends(self, df: pd.DataFrame, lottery_type: str) -> Dict:
        """分析號碼趨勢"""
        select_count = 6 if lottery_type in ['lotto649', 'powercolor'] else 5
        
        # 分析最近5期 vs 前5期的變化
        recent_5 = []
        previous_5 = []
        
        for i, (_, row) in enumerate(df.head(10).iterrows()):
            numbers = []
            for j in range(1, select_count + 1):
                col = f'Anumber{j}'
                if pd.notna(row.get(col)):
                    numbers.append(int(row[col]))
            
            if i < 5:
                recent_5.extend(numbers)
            else:
                previous_5.extend(numbers)
        
        from collections import Counter
        recent_counter = Counter(recent_5)
        previous_counter = Counter(previous_5)
        
        # 上升趨勢號碼
        trending_up = []
        for num in recent_counter:
            recent_freq = recent_counter[num]
            previous_freq = previous_counter.get(num, 0)
            if recent_freq > previous_freq:
                trending_up.append((num, recent_freq - previous_freq))
        
        trending_up.sort(key=lambda x: x[1], reverse=True)
        
        # 下降趨勢號碼
        trending_down = []
        for num in previous_counter:
            previous_freq = previous_counter[num]
            recent_freq = recent_counter.get(num, 0)
            if previous_freq > recent_freq:
                trending_down.append((num, previous_freq - recent_freq))
        
        trending_down.sort(key=lambda x: x[1], reverse=True)
        
        # 計算總和趨勢
        recent_sums = []
        for i in range(5):
            if i < len(df):
                row = df.iloc[i]
                numbers = []
                for j in range(1, select_count + 1):
                    col = f'Anumber{j}'
                    if pd.notna(row.get(col)):
                        numbers.append(int(row[col]))
                recent_sums.append(sum(numbers))
        
        avg_sum = sum(recent_sums) / len(recent_sums) if recent_sums else 0
        
        return {
            "recent_5_periods": recent_counter.most_common(10),
            "previous_5_periods": previous_counter.most_common(10),
            "trending_up": trending_up[:5],
            "trending_down": trending_down[:5],
            "average_sum": round(avg_sum, 1),
            "sum_trend": "偏高" if avg_sum > 150 else ("偏低" if avg_sum < 100 else "正常")
        }
    
    def _generate_recommendations(self, ensemble_result: Dict, number_analysis: Dict) -> Dict:
        """生成推薦建議"""
        
        recommendations = {
            "primary_choice": {
                "numbers": ensemble_result["final_prediction"],
                "confidence": ensemble_result["confidence"],
                "reason": "綜合多種預測方法的投票結果"
            },
            "alternative_strategies": [],
            "number_insights": [],
            "risk_assessment": ""
        }
        
        # 添加替代策略
        if number_analysis["hot_numbers"]:
            hot_nums = [num for num, _ in number_analysis["hot_numbers"][:6]]
            recommendations["alternative_strategies"].append({
                "name": "熱門號碼策略",
                "numbers": hot_nums,
                "description": "選擇出現頻率最高的號碼"
            })
        
        if number_analysis["missing_numbers"]:
            missing_nums = number_analysis["missing_numbers"][:6]
            recommendations["alternative_strategies"].append({
                "name": "遺漏號碼策略", 
                "numbers": missing_nums,
                "description": "選擇最近未出現的號碼"
            })
        
        # 號碼洞察
        odd_percentage = number_analysis["odd_even_ratio"]["odd_percentage"]
        if odd_percentage > 60:
            recommendations["number_insights"].append("最近奇數出現頻率較高，可考慮多選奇數")
        elif odd_percentage < 40:
            recommendations["number_insights"].append("最近偶數出現頻率較高，可考慮多選偶數")
        
        small_percentage = number_analysis["size_ratio"]["small_percentage"]
        if small_percentage > 60:
            recommendations["number_insights"].append("最近小號碼(1-24)出現較多，注意大小號平衡")
        elif small_percentage < 40:
            recommendations["number_insights"].append("最近大號碼(25+)出現較多，注意大小號平衡")
        
        # 風險評估
        confidence = ensemble_result["confidence"]
        if confidence >= 0.7:
            recommendations["risk_assessment"] = "高信心度預測，但仍需謹慎投注"
        elif confidence >= 0.6:
            recommendations["risk_assessment"] = "中等信心度，建議小額測試"
        else:
            recommendations["risk_assessment"] = "低信心度，建議觀望或極小額投注"
        
        return recommendations

def main():
    """主程序"""
    analyzer = PredictionAnalyzer()
    
    print("🎯 彩票預測分析器")
    print("=" * 60)
    
    # 分析所有三種彩票
    lottery_types = ["lotto649", "powercolor", "dailycash"]
    
    for lottery_type in lottery_types:
        print(f"\n\n📊 {analyzer._get_lottery_name(lottery_type)} 完整分析")
        print("-" * 50)
        
        analysis = analyzer.generate_complete_analysis(lottery_type)
        
        if "error" in analysis:
            print(f"❌ {analysis['error']}")
            continue
        
        # 基本信息
        data_info = analysis["data_info"]
        print(f"📈 數據範圍: {data_info['data_range']}")
        print(f"📊 總記錄: {data_info['total_records']} 期")
        print(f"🎯 最新期號: {data_info['latest_period']}")
        
        # 最新開獎
        latest = analysis["latest_draw"]
        if latest:
            print(f"🏆 最新開獎: {latest['numbers']}", end="")
            if latest["special_number"]:
                print(f" + 特別號 {latest['special_number']}")
            else:
                print()
        
        # 各種預測結果
        predictions = analysis["predictions"]
        print(f"\n🔮 預測結果:")
        print(f"   📊 統計分析: {predictions['statistical']['hot_strategy']}")
        print(f"   🧮 數學模式: {predictions['mathematical']['arithmetic_sequence']}")  
        print(f"   📈 趨勢分析: {predictions['trend']['trending_up']}")
        print(f"   🤖 機器學習: {predictions['machine_learning']['prediction']}")
        print(f"   🏆 綜合推薦: {predictions['ensemble']['final_prediction']} (信心度: {predictions['ensemble']['confidence']:.1%})")
        
        # 號碼分析
        num_analysis = analysis["number_analysis"]
        print(f"\n📊 號碼統計:")
        print(f"   🔥 最熱門: {[f'{num}({count})' for num, count in num_analysis['hot_numbers'][:3]]}")
        print(f"   ❄️ 最冷門: {[f'{num}({count})' for num, count in num_analysis['cold_numbers'][:3]]}")
        print(f"   👻 遺漏號: {num_analysis['missing_numbers'][:5]}")
        print(f"   ⚡ 奇偶比: {num_analysis['odd_even_ratio']['odd_percentage']:.1f}% : {num_analysis['odd_even_ratio']['even_percentage']:.1f}%")
        
        # 推薦建議
        recommendations = analysis["recommendations"]
        print(f"\n💡 智能建議:")
        print(f"   🎯 主要推薦: {recommendations['primary_choice']['numbers']}")
        print(f"   📈 信心度: {recommendations['primary_choice']['confidence']:.1%}")
        print(f"   ⚠️ 風險評估: {recommendations['risk_assessment']}")
        
        if recommendations["number_insights"]:
            print(f"   💭 號碼洞察:")
            for insight in recommendations["number_insights"]:
                print(f"      - {insight}")

if __name__ == "__main__":
    main()