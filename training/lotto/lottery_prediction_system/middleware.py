#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中間件模組
包含錯誤處理、性能監控、速率限制等中間件
"""

import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from functools import wraps
from flask import request, jsonify, g
from collections import defaultdict, deque

@dataclass
class APIResponse:
    """標準化API響應格式"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    message: Optional[str] = None
    timestamp: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            'success': self.success,
            'timestamp': self.timestamp
        }
        if self.data is not None:
            result['data'] = self.data
        if self.error is not None:
            result['error'] = self.error
        if self.message is not None:
            result['message'] = self.message
        return result

class ErrorHandler:
    """錯誤處理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def handle_validation_error(self, error: str) -> APIResponse:
        """處理驗證錯誤
        
        Args:
            error: 錯誤訊息
            
        Returns:
            API響應
        """
        self.logger.warning(f"驗證錯誤: {error}")
        return APIResponse(
            success=False,
            error=f"驗證錯誤: {error}"
        )
    
    def handle_not_found_error(self, resource: str) -> APIResponse:
        """處理資源未找到錯誤
        
        Args:
            resource: 資源名稱
            
        Returns:
            API響應
        """
        self.logger.warning(f"資源未找到: {resource}")
        return APIResponse(
            success=False,
            error=f"資源未找到: {resource}"
        )
    
    def handle_internal_error(self, error: Exception) -> APIResponse:
        """處理內部錯誤
        
        Args:
            error: 異常對象
            
        Returns:
            API響應
        """
        self.logger.error(f"內部錯誤: {str(error)}", exc_info=True)
        return APIResponse(
            success=False,
            error="內部服務器錯誤"
        )
    
    def handle_rate_limit_error(self) -> APIResponse:
        """處理速率限制錯誤
        
        Returns:
            API響應
        """
        self.logger.warning("請求速率超限")
        return APIResponse(
            success=False,
            error="請求過於頻繁，請稍後再試"
        )

class PerformanceMonitor:
    """性能監控器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.request_times = defaultdict(list)
        self.slow_requests = []
        self.slow_threshold = 2.0  # 2秒
    
    def start_request(self) -> None:
        """開始請求計時"""
        g.start_time = time.time()
    
    def end_request(self, endpoint: str) -> float:
        """結束請求計時
        
        Args:
            endpoint: 端點名稱
            
        Returns:
            請求耗時（秒）
        """
        if not hasattr(g, 'start_time'):
            return 0.0
        
        duration = time.time() - g.start_time
        
        # 記錄請求時間
        self.request_times[endpoint].append({
            'duration': duration,
            'timestamp': datetime.now().isoformat()
        })
        
        # 保持最近100次請求記錄
        if len(self.request_times[endpoint]) > 100:
            self.request_times[endpoint] = self.request_times[endpoint][-100:]
        
        # 記錄慢請求
        if duration > self.slow_threshold:
            self.slow_requests.append({
                'endpoint': endpoint,
                'duration': duration,
                'timestamp': datetime.now().isoformat(),
                'ip': request.remote_addr if request else 'unknown'
            })
            
            # 保持最近50次慢請求記錄
            if len(self.slow_requests) > 50:
                self.slow_requests = self.slow_requests[-50:]
            
            self.logger.warning(f"慢請求: {endpoint} 耗時 {duration:.2f}秒")
        
        return duration
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取性能統計
        
        Returns:
            性能統計數據
        """
        stats = {
            'endpoints': {},
            'slow_requests': self.slow_requests[-10:],  # 最近10次慢請求
            'total_slow_requests': len(self.slow_requests)
        }
        
        for endpoint, times in self.request_times.items():
            if times:
                durations = [t['duration'] for t in times]
                stats['endpoints'][endpoint] = {
                    'count': len(times),
                    'avg_duration': sum(durations) / len(durations),
                    'max_duration': max(durations),
                    'min_duration': min(durations),
                    'recent_requests': times[-5:]  # 最近5次請求
                }
        
        return stats

class RateLimiter:
    """速率限制器"""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = defaultdict(deque)
        self.logger = logging.getLogger(__name__)
    
    def is_allowed(self, client_id: str) -> bool:
        """檢查是否允許請求
        
        Args:
            client_id: 客戶端ID（通常是IP地址）
            
        Returns:
            是否允許請求
        """
        now = time.time()
        client_requests = self.requests[client_id]
        
        # 清理過期請求
        while client_requests and client_requests[0] < now - self.window_seconds:
            client_requests.popleft()
        
        # 檢查請求數量
        if len(client_requests) >= self.max_requests:
            self.logger.warning(f"客戶端 {client_id} 請求速率超限")
            return False
        
        # 記錄新請求
        client_requests.append(now)
        return True
    
    def get_remaining_requests(self, client_id: str) -> int:
        """獲取剩餘請求數
        
        Args:
            client_id: 客戶端ID
            
        Returns:
            剩餘請求數
        """
        now = time.time()
        client_requests = self.requests[client_id]
        
        # 清理過期請求
        while client_requests and client_requests[0] < now - self.window_seconds:
            client_requests.popleft()
        
        return max(0, self.max_requests - len(client_requests))

# 裝飾器函數
def handle_api_errors(func):
    """API錯誤處理裝飾器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            # 如果返回的是APIResponse對象，轉換為字典
            if isinstance(result, APIResponse):
                return jsonify(result.to_dict())
            return result
        except ValueError as e:
            logger = logging.getLogger(__name__)
            logger.warning(f"參數錯誤在 {func.__name__}: {str(e)}")
            response = APIResponse(success=False, error=f"參數錯誤: {str(e)}")
            return jsonify(response.to_dict()), 400
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"API錯誤在 {func.__name__}: {str(e)}", exc_info=True)
            response = APIResponse(success=False, error="內部服務器錯誤")
            return jsonify(response.to_dict()), 500
    return wrapper

def validate_lottery_type(func):
    """彩票類型驗證裝飾器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 支援的彩票類型
        supported_types = ['powercolor', 'lotto649', 'dailycash']
        
        # 從路由參數或請求參數中獲取彩票類型
        lottery_type = kwargs.get('lottery_type')
        if not lottery_type and request:
            lottery_type = request.args.get('lottery_type') or request.json.get('lottery_type') if request.json else None
        
        if lottery_type and lottery_type not in supported_types:
            logger = logging.getLogger(__name__)
            logger.warning(f"不支援的彩票類型: {lottery_type}")
            response = APIResponse(
                success=False,
                error=f"不支援的彩票類型: {lottery_type}，支援的類型: {', '.join(supported_types)}"
            )
            return jsonify(response.to_dict()), 400
        
        return func(*args, **kwargs)
    return wrapper

def monitor_performance(func):
    """性能監控裝飾器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            duration = time.time() - start_time
            logger = logging.getLogger(__name__)
            
            if duration > 2.0:  # 超過2秒的請求
                logger.warning(f"慢請求: {func.__name__} 耗時 {duration:.2f}秒")
            else:
                logger.debug(f"請求: {func.__name__} 耗時 {duration:.2f}秒")
    
    return wrapper

def rate_limit(max_requests: int = 100, window_seconds: int = 60):
    """速率限制裝飾器
    
    Args:
        max_requests: 最大請求數
        window_seconds: 時間窗口（秒）
    """
    limiter = RateLimiter(max_requests, window_seconds)
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            client_id = request.remote_addr if request else 'unknown'
            
            if not limiter.is_allowed(client_id):
                response = APIResponse(
                    success=False,
                    error="請求過於頻繁，請稍後再試"
                )
                return jsonify(response.to_dict()), 429
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

def require_json(func):
    """要求JSON請求體的裝飾器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        if not request or not request.is_json:
            response = APIResponse(
                success=False,
                error="請求必須包含有效的JSON數據"
            )
            return jsonify(response.to_dict()), 400
        
        return func(*args, **kwargs)
    return wrapper

def validate_required_fields(required_fields: List[str]):
    """驗證必需欄位的裝飾器
    
    Args:
        required_fields: 必需欄位列表
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not request or not request.json:
                response = APIResponse(
                    success=False,
                    error="請求必須包含JSON數據"
                )
                return jsonify(response.to_dict()), 400
            
            missing_fields = []
            for field in required_fields:
                if field not in request.json or request.json[field] is None:
                    missing_fields.append(field)
            
            if missing_fields:
                response = APIResponse(
                    success=False,
                    error=f"缺少必需欄位: {', '.join(missing_fields)}"
                )
                return jsonify(response.to_dict()), 400
            
            return func(*args, **kwargs)
        return wrapper
    return decorator