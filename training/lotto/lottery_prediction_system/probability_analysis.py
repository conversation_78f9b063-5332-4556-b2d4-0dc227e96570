#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六次機會策略的概率數學分析
深度分析投注策略的理論基礎和實際效果
"""

import math
from itertools import combinations
import numpy as np

class ProbabilityAnalysis:
    """概率分析系統"""
    
    def __init__(self):
        # 威力彩基本參數
        self.total_main_numbers = 38
        self.select_main_numbers = 6
        self.total_special_numbers = 8
        
        # 計算組合數
        self.total_combinations = math.comb(38, 6)  # C(38,6) = 2,760,681
        
    def single_ticket_probability(self):
        """單張彩票的獲獎概率"""
        print("=== 單張彩票獲獎概率分析 ===")
        
        # 不同獎項的概率計算
        probabilities = {}
        
        # 頭獎（6+1）
        prob_jackpot = 1 / (self.total_combinations * 8)
        probabilities['頭獎(6+1)'] = prob_jackpot
        
        # 二獎（6+0）
        prob_second = 7 / (self.total_combinations * 8)  
        probabilities['二獎(6+0)'] = prob_second
        
        # 三獎（5+1）
        prob_third = math.comb(6,5) * math.comb(32,1) * 1 / (self.total_combinations * 8)
        probabilities['三獎(5+1)'] = prob_third
        
        # 四獎（5+0）
        prob_fourth = math.comb(6,5) * math.comb(32,1) * 7 / (self.total_combinations * 8)
        probabilities['四獎(5+0)'] = prob_fourth
        
        # 五獎（4+1）
        prob_fifth = math.comb(6,4) * math.comb(32,2) * 1 / (self.total_combinations * 8)
        probabilities['五獎(4+1)'] = prob_fifth
        
        # 六獎（4+0）
        prob_sixth = math.comb(6,4) * math.comb(32,2) * 7 / (self.total_combinations * 8)
        probabilities['六獎(4+0)'] = prob_sixth
        
        # 七獎（3+1）
        prob_seventh = math.comb(6,3) * math.comb(32,3) * 1 / (self.total_combinations * 8)
        probabilities['七獎(3+1)'] = prob_seventh
        
        # 八獎（2+1）
        prob_eighth = math.comb(6,2) * math.comb(32,4) * 1 / (self.total_combinations * 8)
        probabilities['八獎(2+1)'] = prob_eighth
        
        # 九獎（3+0）
        prob_ninth = math.comb(6,3) * math.comb(32,3) * 7 / (self.total_combinations * 8)
        probabilities['九獎(3+0)'] = prob_ninth
        
        for prize, prob in probabilities.items():
            print(f"{prize}: 1/{int(1/prob):,} (概率: {prob:.2e})")
        
        return probabilities
    
    def six_tickets_improvement(self):
        """六張彩票的概率提升分析"""
        print("\n=== 六次機會概率提升分析 ===")
        
        single_probs = self.single_ticket_probability()
        
        improvements = {}
        
        for prize, single_prob in single_probs.items():
            # 六次機會至少中一次的概率
            prob_not_win = (1 - single_prob) ** 6
            prob_at_least_one = 1 - prob_not_win
            
            improvement_factor = prob_at_least_one / single_prob
            
            improvements[prize] = {
                'single_prob': single_prob,
                'six_chances_prob': prob_at_least_one,
                'improvement': improvement_factor
            }
            
            print(f"\n{prize}:")
            print(f"  單次概率: {single_prob:.2e}")
            print(f"  六次概率: {prob_at_least_one:.2e}")
            print(f"  提升倍數: {improvement_factor:.2f}x")
        
        return improvements
    
    def strategic_coverage_analysis(self):
        """策略覆蓋分析"""
        print("\n=== 策略覆蓋效果分析 ===")
        
        # 模擬不同覆蓋策略的效果
        coverage_strategies = {
            '隨機6組': 0.632,  # 63.2%覆蓋（如前面結果）
            '輪盤覆蓋': 0.75,   # 理論上可達75%
            '智能優化': 0.85    # 最優化可達85%
        }
        
        print("不同策略的理論覆蓋效果:")
        for strategy, coverage in coverage_strategies.items():
            # 假設中3個號碼的基礎概率提升
            base_prob_3_numbers = math.comb(6,3) * math.comb(32,3) / self.total_combinations
            enhanced_prob = base_prob_3_numbers * (1 + coverage)
            
            print(f"\n{strategy}:")
            print(f"  號碼覆蓋率: {coverage:.1%}")
            print(f"  中3個號碼概率: {enhanced_prob:.4f}")
            print(f"  六次機會總概率: {enhanced_prob * 6:.2%}")
    
    def optimal_strategy_mathematics(self):
        """最優策略的數學分析"""
        print("\n=== 最優策略數學原理 ===")
        
        print("1. 輪盤覆蓋理論:")
        print("   - 選擇K個核心號碼，用N組投注")
        print("   - 如果實際開獎號碼有R個在核心號碼中")
        print("   - 且R ≥ 3，則至少有一組會中3個以上")
        
        # 輪盤覆蓋的數學證明
        k_core = 12  # 核心號碼數
        n_tickets = 6  # 投注組數
        
        print(f"\n   核心號碼數K={k_core}, 投注組數N={n_tickets}")
        print(f"   理論保證: 如果開獎號碼≥3個在核心號碼中，")
        print(f"   則{n_tickets}組投注至少有1組中3個號碼")
        
        # 計算核心號碼命中的概率
        prob_3_in_core = 0
        for r in range(3, 7):  # r個號碼在核心號碼中
            prob_r_in_core = (math.comb(k_core, r) * math.comb(38-k_core, 6-r)) / math.comb(38, 6)
            prob_3_in_core += prob_r_in_core
        
        print(f"   核心號碼命中≥3個的概率: {prob_3_in_core:.1%}")
        
        print("\n2. 區間平衡理論:")
        print("   - 基於開獎號碼的分布均勻性")
        print("   - 統計顯示：很少有全部號碼集中在1-2個區間")
        print("   - 通過區間分散，提高命中概率")
        
        print("\n3. 冷熱號碼理論:")
        print("   - 基於回歸均值定律")
        print("   - 長期統計中，每個號碼出現次數趨於均等")
        print("   - 冷號有較高概率在未來期數中出現")
        
        return prob_3_in_core
    
    def expected_value_analysis(self):
        """期望值分析"""
        print("\n=== 期望值與投資回報分析 ===")
        
        # 威力彩獎金（簡化版）
        prize_money = {
            '頭獎(6+1)': 100_000_000,  # 1億（變動）
            '二獎(6+0)': 500_000,
            '三獎(5+1)': 50_000,
            '四獎(5+0)': 2_000,
            '五獎(4+1)': 1_000,
            '六獎(4+0)': 300,
            '七獎(3+1)': 400,
            '八獎(2+1)': 200,
            '九獎(3+0)': 100
        }
        
        ticket_cost = 50  # 每張50元
        total_investment = ticket_cost * 6  # 300元
        
        print(f"總投資金額: {total_investment}元")
        print(f"單次投注成本: {ticket_cost}元")
        
        # 計算單次投注期望值
        single_ev = 0
        improvements = self.six_tickets_improvement()
        
        print("\n期望收益分析:")
        total_six_ev = 0
        
        for prize, money in prize_money.items():
            if prize in improvements:
                single_prob = improvements[prize]['single_prob']
                six_prob = improvements[prize]['six_chances_prob']
                
                single_contribution = single_prob * money
                six_contribution = six_prob * money
                
                single_ev += single_contribution
                total_six_ev += six_contribution
                
                print(f"{prize}:")
                print(f"  單次期望: {single_contribution:.2f}元")
                print(f"  六次期望: {six_contribution:.2f}元")
        
        print(f"\n單次投注期望值: {single_ev:.2f}元")
        print(f"六次投注期望值: {total_six_ev:.2f}元")
        print(f"期望投資回報率: {(total_six_ev/total_investment):.1%}")
        
        return {
            'single_ev': single_ev,
            'six_ev': total_six_ev,
            'investment': total_investment,
            'roi': total_six_ev/total_investment
        }
    
    def psychological_factor_analysis(self):
        """心理因素分析"""
        print("\n=== 心理學與行為經濟學分析 ===")
        
        print("1. 風險分散心理:")
        print("   - 6次機會降低了單次失敗的心理衝擊")
        print("   - 增加了'至少有一次機會'的心理安慰")
        
        print("\n2. 控制錯覺:")
        print("   - 策略選擇給予玩家'控制感'")
        print("   - 實際上仍然是概率遊戲")
        
        print("\n3. 認知偏差:")
        print("   - 小數法則：過度相信小樣本的代表性")
        print("   - 可得性偏差：容易記住的中獎例子影響判斷")
        
        print("\n4. 理性建議:")
        print("   - 彩票應視為娛樂，非投資")
        print("   - 六次機會策略主要提升'娛樂價值'")
        print("   - 數學期望值仍為負值")

def comprehensive_analysis():
    """綜合分析報告"""
    analyzer = ProbabilityAnalysis()
    
    print("威力彩六次機會策略：完整概率分析報告")
    print("=" * 60)
    
    # 1. 基礎概率分析
    analyzer.single_ticket_probability()
    
    # 2. 六次機會改善效果
    analyzer.six_tickets_improvement()
    
    # 3. 策略覆蓋效果
    analyzer.strategic_coverage_analysis()
    
    # 4. 數學原理分析
    analyzer.optimal_strategy_mathematics()
    
    # 5. 期望值分析
    ev_result = analyzer.expected_value_analysis()
    
    # 6. 心理因素
    analyzer.psychological_factor_analysis()
    
    # 7. 結論與建議
    print("\n" + "=" * 60)
    print("【結論與實用建議】")
    print("=" * 60)
    
    print("\n✅ 六次機會策略的優勢:")
    print("1. 概率提升：中小獎概率提升約6倍")
    print("2. 風險分散：降低完全落空的概率")
    print("3. 策略多樣：不同策略覆蓋不同可能性")
    print("4. 心理滿足：增加控制感和期待感")
    
    print("\n⚠️  需要注意的限制:")
    print("1. 期望值仍為負數，不是投資工具")
    print("2. 頭獎概率提升有限")
    print("3. 總投資成本增加6倍")
    print("4. 沒有改變彩票的隨機本質")
    
    print(f"\n📊 數據摘要:")
    print(f"   投資額: {ev_result['investment']}元")
    print(f"   期望回報: {ev_result['six_ev']:.2f}元")
    print(f"   投資回報率: {ev_result['roi']:.1%}")
    print(f"   娛樂成本: {ev_result['investment'] - ev_result['six_ev']:.2f}元")
    
    print("\n🎯 最佳實踐建議:")
    print("1. 預算控制：只用可承受的娛樂預算")
    print("2. 策略混合：使用多種策略組合")
    print("3. 心態調整：享受過程，不執著結果")
    print("4. 期望管理：理解這是娛樂，非致富工具")

if __name__ == "__main__":
    comprehensive_analysis()