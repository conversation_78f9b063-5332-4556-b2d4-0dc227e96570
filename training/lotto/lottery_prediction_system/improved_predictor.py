#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改進的彩票預測器 - 簡化版本
專為 Phase 3 功能設計，不依賴複雜的核心模組
"""

import logging
import sqlite3
import json
import random
from datetime import datetime
from typing import Dict, List, Optional
from collections import Counter

logger = logging.getLogger('improved_predictor')

class ImprovedPredictor:
    """改進的彩票預測器 - 簡化版本"""
    
    def __init__(self, db_path="data/lottery_data.db"):
        self.db_path = db_path
        
        # 彩票配置
        self.lottery_config = {
            'powercolor': {
                'table_name': 'Powercolor',
                'number_range': (1, 38),
                'special_range': (1, 8),
                'number_count': 6,
                'has_special': True
            },
            'lotto649': {
                'table_name': 'Lotto649',
                'number_range': (1, 49),
                'special_range': (1, 49),
                'number_count': 6,
                'has_special': True
            },
            'dailycash': {
                'table_name': 'DailyCash',
                'number_range': (1, 39),
                'special_range': None,
                'number_count': 5,
                'has_special': False
            }
        }
    
    def predict_next_draw(self, lottery_type: str) -> Optional[Dict]:
        """預測下一期開獎號碼
        
        Args:
            lottery_type: 彩票類型
            
        Returns:
            Dict: 預測結果，包含期號、號碼、信心度等
        """
        logger.info(f"開始預測 {lottery_type} 下一期號碼...")
        
        try:
            # 1. 獲取配置
            if lottery_type not in self.lottery_config:
                logger.error(f"不支援的彩票類型: {lottery_type}")
                return None
                
            config = self.lottery_config[lottery_type]
            
            # 2. 獲取歷史數據
            historical_data = self._get_historical_data(lottery_type)
            if not historical_data:
                logger.warning("沒有足夠的歷史數據，使用隨機預測")
                return self._generate_random_prediction(lottery_type)
            
            # 3. 計算下一期期號
            next_period = self._calculate_next_period(lottery_type)
            
            # 4. 進行頻率分析
            prediction = self._frequency_analysis_prediction(lottery_type, historical_data, next_period)
            
            result = {
                'period': next_period,
                'numbers': prediction['numbers'],
                'confidence': prediction['confidence'],
                'method': '頻率分析',
                'timestamp': datetime.now().isoformat(),
                'lottery_type': lottery_type
            }
            
            if config['has_special']:
                result['special_number'] = prediction.get('special_number', 1)
            
            logger.info(f"預測完成，信心度: {prediction['confidence']:.3f}")
            return result
            
        except Exception as e:
            logger.error(f"預測過程發生錯誤: {str(e)}")
            return self._generate_random_prediction(lottery_type)
    
    def _get_historical_data(self, lottery_type: str) -> Optional[List[Dict]]:
        """獲取歷史數據"""
        try:
            config = self.lottery_config[lottery_type]
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if config['has_special']:
                sql = f"""
                SELECT Period, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6,
                       Second_district as SpecialNumber
                FROM {config['table_name']}
                ORDER BY Period DESC
                LIMIT 50
                """
            else:
                sql = f"""
                SELECT Period, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5
                FROM {config['table_name']}
                ORDER BY Period DESC
                LIMIT 50
                """
            
            cursor.execute(sql)
            rows = cursor.fetchall()
            conn.close()
            
            if not rows:
                return None
            
            # 轉換為字典列表
            data = []
            for row in rows:
                if config['has_special']:
                    data.append({
                        'period': row[0],
                        'numbers': [row[1], row[2], row[3], row[4], row[5], row[6]] if config['number_count'] == 6 else [row[1], row[2], row[3], row[4], row[5]],
                        'special': row[7] if len(row) > 7 else None
                    })
                else:
                    data.append({
                        'period': row[0],
                        'numbers': [row[1], row[2], row[3], row[4], row[5]]
                    })
            
            logger.info(f"獲取到 {len(data)} 期歷史數據")
            return data
            
        except Exception as e:
            logger.error(f"獲取歷史數據失敗: {str(e)}")
            return None
    
    def _frequency_analysis_prediction(self, lottery_type: str, historical_data: List[Dict], next_period: str = None) -> Dict:
        """基於頻率分析的預測"""
        config = self.lottery_config[lottery_type]
        
        # 統計所有號碼出現頻率
        all_numbers = []
        special_numbers = []
        
        for data in historical_data:
            all_numbers.extend(data['numbers'])
            if config['has_special'] and data.get('special'):
                special_numbers.append(data['special'])
        
        # 計算頻率
        freq_counter = Counter(all_numbers)
        
        # 選擇出現頻率適中的號碼（避免過熱和過冷）
        sorted_by_freq = sorted(freq_counter.items(), key=lambda x: x[1])
        
        # 選擇中等頻率範圍的號碼
        total_numbers = len(sorted_by_freq)
        start_idx = total_numbers // 4
        end_idx = total_numbers * 3 // 4
        candidate_numbers = [num for num, _ in sorted_by_freq[start_idx:end_idx]]
        
        # 如果候選號碼不夠，添加其他號碼
        if len(candidate_numbers) < config['number_count']:
            all_possible = list(range(config['number_range'][0], config['number_range'][1] + 1))
            remaining = [n for n in all_possible if n not in candidate_numbers]
            random.shuffle(remaining)
            candidate_numbers.extend(remaining[:config['number_count'] - len(candidate_numbers)])
        
        # 使用期號作為隨機種子，確保同期預測結果一致
        # 如果沒有提供期號，計算下一期期號
        if next_period is None:
            next_period = self._calculate_next_period(lottery_type)
        
        period_seed = int(next_period[-6:]) if len(next_period) >= 6 else 1
        random.seed(period_seed)
        
        # 確保有足夠的候選號碼
        if len(candidate_numbers) >= config['number_count']:
            selected_numbers = sorted(random.sample(candidate_numbers, config['number_count']))
        else:
            # 如果候選號碼不足，使用所有可能的號碼
            all_possible = list(range(config['number_range'][0], config['number_range'][1] + 1))
            selected_numbers = sorted(random.sample(all_possible, config['number_count']))
        
        result = {
            'numbers': selected_numbers,
            'confidence': 0.6 + (len(historical_data) / 100) * 0.2  # 歷史數據越多，信心度越高
        }
        
        # 處理特別號
        if config['has_special']:
            if special_numbers:
                special_freq = Counter(special_numbers)
                # 選擇頻率適中的特別號
                sorted_special = sorted(special_freq.items(), key=lambda x: x[1])
                if sorted_special:
                    mid_idx = len(sorted_special) // 2
                    result['special_number'] = sorted_special[mid_idx][0]
                else:
                    result['special_number'] = random.randint(*config['special_range'])
            else:
                result['special_number'] = random.randint(*config['special_range'])
        
        return result
    
    def _generate_random_prediction(self, lottery_type: str) -> Dict:
        """生成隨機預測（當沒有歷史數據時）"""
        config = self.lottery_config[lottery_type]
        
        # 生成隨機號碼
        numbers = sorted(random.sample(
            range(config['number_range'][0], config['number_range'][1] + 1),
            config['number_count']
        ))
        
        # 獲取下一期期號
        next_period = self._calculate_next_period(lottery_type)
        
        result = {
            'period': next_period,
            'numbers': numbers,
            'confidence': 0.3,  # 隨機預測信心度較低
            'method': '隨機預測',
            'timestamp': datetime.now().isoformat(),
            'lottery_type': lottery_type
        }
        
        # 生成特別號
        if config['has_special']:
            result['special_number'] = random.randint(*config['special_range'])
        
        return result
    
    def _calculate_next_period(self, lottery_type: str) -> str:
        """計算下一期期號"""
        try:
            config = self.lottery_config[lottery_type]
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            sql = f"SELECT Period FROM {config['table_name']} ORDER BY Period DESC LIMIT 1"
            cursor.execute(sql)
            result = cursor.fetchone()
            conn.close()
            
            if result:
                latest_period = str(result[0])
                logger.info(f"最新期號: {latest_period}")
                
                # 台灣彩票期號格式：114000XXX (民國年+000+期數)
                if len(latest_period) >= 9:
                    # 提取期數部分（最後3位）
                    period_num = int(latest_period[-3:])
                    # 提取年份和固定部分（前6位：114000）
                    year_prefix = latest_period[:-3]
                    # 計算下一期
                    next_num = period_num + 1
                    next_period = f"{year_prefix}{next_num:03d}"
                    logger.info(f"計算下一期期號: {next_period}")
                    return next_period
                else:
                    # 如果期號格式不符預期，使用民國114年的格式
                    logger.warning(f"期號格式異常: {latest_period}")
                    return "114000001"
            else:
                # 如果沒有歷史數據，返回民國114年第一期
                logger.warning("沒有歷史數據，使用默認期號")
                return "114000001"
                
        except Exception as e:
            logger.error(f"計算下一期期號失敗: {str(e)}")
            # 返回默認期號（民國114年第一期）
            return "114000001"

# 為了向後兼容，提供預測函數
def predict_lottery(lottery_type: str, **kwargs) -> Dict:
    """預測彩票號碼（向後兼容函數）"""
    predictor = ImprovedPredictor()
    result = predictor.predict_next_draw(lottery_type)
    
    if result:
        return {
            'success': True,
            'prediction': result,
            'message': '預測成功'
        }
    else:
        return {
            'success': False,
            'error': '預測失敗',
            'message': '無法生成預測結果'
        }

if __name__ == "__main__":
    # 測試代碼
    predictor = ImprovedPredictor()
    
    for lottery_type in ['powercolor', 'lotto649', 'dailycash']:
        print(f"\n測試 {lottery_type} 預測:")
        result = predictor.predict_next_draw(lottery_type)
        if result:
            print(f"期號: {result['period']}")
            print(f"號碼: {result['numbers']}")
            if 'special_number' in result:
                print(f"特別號: {result['special_number']}")
            print(f"信心度: {result['confidence']:.3f}")
            print(f"方法: {result['method']}")
        else:
            print("預測失敗")