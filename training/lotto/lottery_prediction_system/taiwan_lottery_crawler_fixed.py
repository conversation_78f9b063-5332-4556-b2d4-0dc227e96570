#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復版台灣彩券爬蟲
解決HTML結構變更導致的解析問題
"""

import requests
from bs4 import BeautifulSoup
import re
import logging
from datetime import datetime
from typing import Dict, List, Optional

logger = logging.getLogger('taiwan_lottery_crawler_fixed')

class TaiwanLotteryCrawlerFixed:
    """修復版台灣彩券爬蟲"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def get_powerball_latest(self) -> Optional[Dict]:
        """獲取威力彩最新開獎結果"""
        try:
            # 使用威力彩主頁面
            url = "https://www.taiwanlottery.com/result/powerball"
            logger.info(f"正在獲取威力彩數據: {url}")
            
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找最新開獎結果
            # 嘗試多種可能的選擇器
            selectors = [
                '.result-box .result-info',
                '.lottery-result .result-number',
                '[data-testid="result"]',
                '.draw-result',
                'table.result-table tr:first-child',
                '.latest-result'
            ]
            
            result_element = None
            for selector in selectors:
                result_element = soup.select_one(selector)
                if result_element:
                    logger.info(f"找到結果元素，使用選擇器: {selector}")
                    break
            
            if not result_element:
                # 如果找不到特定選擇器，嘗試查找包含數字的元素
                logger.info("使用通用方法查找開獎號碼...")
                return self._extract_numbers_generic(soup, 'powerball')
            
            # 提取期號和號碼
            period = self._extract_period(result_element)
            numbers = self._extract_powerball_numbers(result_element)
            date = self._extract_date(result_element)
            
            if numbers and len(numbers) >= 6:
                return {
                    'period': period,
                    'date': date,
                    'numbers': numbers[:6],
                    'special': numbers[6] if len(numbers) > 6 else None
                }
            
            return None
            
        except Exception as e:
            logger.error(f"獲取威力彩數據失敗: {e}")
            return None
    
    def _extract_numbers_generic(self, soup: BeautifulSoup, lottery_type: str) -> Optional[Dict]:
        """通用數字提取方法"""
        try:
            # 查找所有包含數字的元素
            text = soup.get_text()
            
            # 威力彩格式：尋找6個1-38的數字 + 1個1-8的特別號
            if lottery_type == 'powerball':
                # 尋找期號
                period_match = re.search(r'第\s*(\d+)\s*期', text)
                period = period_match.group(1) if period_match else None
                
                # 尋找日期
                date_match = re.search(r'(\d{4})[年/-](\d{1,2})[月/-](\d{1,2})', text)
                date = f"{date_match.group(1)}-{date_match.group(2):0>2}-{date_match.group(3):0>2}" if date_match else None
                
                # 尋找開獎號碼 - 修改正則表達式以更靈活地匹配
                # 尋找類似 "01 02 03 04 05 06 特別號 07" 的模式
                number_patterns = [
                    r'(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s*[特別號]*\s*(\d{1,2})',
                    r'(\d{1,2})[,\s]+(\d{1,2})[,\s]+(\d{1,2})[,\s]+(\d{1,2})[,\s]+(\d{1,2})[,\s]+(\d{1,2})[,\s]*[特別號]*[,\s]*(\d{1,2})',
                ]
                
                for pattern in number_patterns:
                    numbers_match = re.search(pattern, text)
                    if numbers_match:
                        numbers = [int(numbers_match.group(i)) for i in range(1, 8)]
                        logger.info(f"成功提取號碼: {numbers}")
                        return {
                            'period': period,
                            'date': date,
                            'numbers': numbers[:6],
                            'special': numbers[6] if len(numbers) > 6 else None
                        }
                
                # 如果上述模式都不匹配，嘗試查找所有2位數字
                all_numbers = re.findall(r'\b([0-3]?\d)\b', text)
                valid_numbers = []
                
                for num_str in all_numbers:
                    num = int(num_str)
                    if 1 <= num <= 38:  # 威力彩主號碼範圍
                        valid_numbers.append(num)
                        if len(valid_numbers) == 6:
                            break
                
                if len(valid_numbers) == 6:
                    # 查找特別號 (1-8)
                    special = None
                    for num_str in all_numbers[len(valid_numbers):]:
                        num = int(num_str)
                        if 1 <= num <= 8:
                            special = num
                            break
                    
                    logger.info(f"通用方法提取號碼: {valid_numbers}, 特別號: {special}")
                    return {
                        'period': period,
                        'date': date,
                        'numbers': valid_numbers,
                        'special': special
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"通用數字提取失敗: {e}")
            return None
    
    def _extract_period(self, element) -> Optional[str]:
        """提取期號"""
        try:
            text = element.get_text() if element else ""
            match = re.search(r'第?\s*(\d+)\s*期', text)
            return match.group(1) if match else None
        except:
            return None
    
    def _extract_date(self, element) -> Optional[str]:
        """提取開獎日期"""
        try:
            text = element.get_text() if element else ""
            # 嘗試多種日期格式
            patterns = [
                r'(\d{4})[年/-](\d{1,2})[月/-](\d{1,2})',
                r'(\d{4})\.(\d{1,2})\.(\d{1,2})',
                r'(\d{1,2})/(\d{1,2})/(\d{4})',
            ]
            
            for pattern in patterns:
                match = re.search(pattern, text)
                if match:
                    if len(match.group(1)) == 4:  # 年份在前
                        return f"{match.group(1)}-{match.group(2):0>2}-{match.group(3):0>2}"
                    else:  # 年份在後
                        return f"{match.group(3)}-{match.group(1):0>2}-{match.group(2):0>2}"
            
            return None
        except:
            return None
    
    def _extract_powerball_numbers(self, element) -> List[int]:
        """提取威力彩號碼"""
        try:
            # 查找數字元素
            number_elements = element.find_all(['span', 'div', 'td'], class_=re.compile(r'number|ball|digit'))
            
            if number_elements:
                numbers = []
                for elem in number_elements:
                    text = elem.get_text().strip()
                    if text.isdigit():
                        numbers.append(int(text))
                return numbers
            
            # 如果找不到特定的數字元素，使用正則表達式
            text = element.get_text()
            number_matches = re.findall(r'\b(\d{1,2})\b', text)
            numbers = []
            
            for match in number_matches:
                num = int(match)
                if 1 <= num <= 38:  # 威力彩號碼範圍
                    numbers.append(num)
                    if len(numbers) == 6:
                        break
            
            # 查找特別號
            if len(numbers) == 6:
                for match in number_matches[6:]:
                    num = int(match)
                    if 1 <= num <= 8:  # 威力彩特別號範圍
                        numbers.append(num)
                        break
            
            return numbers
            
        except Exception as e:
            logger.error(f"提取威力彩號碼失敗: {e}")
            return []

def test_crawler():
    """測試爬蟲功能"""
    crawler = TaiwanLotteryCrawlerFixed()
    result = crawler.get_powerball_latest()
    
    if result:
        print("✅ 爬蟲測試成功!")
        print(f"期號: {result.get('period', '未知')}")
        print(f"日期: {result.get('date', '未知')}")
        print(f"號碼: {result.get('numbers', [])}")
        print(f"特別號: {result.get('special', '無')}")
    else:
        print("❌ 爬蟲測試失敗")

if __name__ == "__main__":
    test_crawler()