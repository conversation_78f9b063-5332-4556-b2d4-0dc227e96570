#!/usr/bin/env python3
"""
Phase 3.4 Web API 接口
基於 FastAPI 的彩票預測系統 Web API
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional, Union
import asyncio
import json
import uuid
from datetime import datetime, timedelta
from contextlib import asynccontextmanager
import logging

# 導入系統組件
from data.db_manager import DBManager
from phase3.accuracy_assessment_engine import AccuracyAssessmentEngine
from phase3.universal_prediction_framework import (
    UniversalPredictor, LotteryType, LotteryConfig, UniversalPredictionResult
)
from phase3.realtime_data_manager import RealTimeDataManager
from phase3.auto_prediction_scheduler import AutoPredictionScheduler
from phase3.web_api_tracking_integration import (
    get_tracking_router, initialize_tracking_integration, 
    get_tracking_integration, record_prediction_to_tracking
)

# 配置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# === Pydantic 模型定義 ===

class PredictionRequest(BaseModel):
    lottery_type: str = Field(..., description="彩票類型")
    strategy: Optional[str] = Field("ensemble", description="預測策略")
    use_cross_learning: bool = Field(True, description="使用跨彩票學習")
    ensemble_size: int = Field(10, ge=1, le=50, description="集成大小")
    data_window: int = Field(100, ge=10, le=500, description="數據窗口")

class BatchPredictionRequest(BaseModel):
    lottery_types: List[str] = Field(..., description="彩票類型列表")
    strategy: Optional[str] = Field("ensemble", description="預測策略")
    use_cross_learning: bool = Field(True, description="使用跨彩票學習")
    ensemble_size: int = Field(10, ge=1, le=30, description="集成大小")

class PredictionResponse(BaseModel):
    prediction_id: str
    lottery_type: str
    main_numbers: List[int]
    special_numbers: Optional[List[int]]
    confidence: float
    strategy_used: str
    prediction_timestamp: datetime
    metadata: Dict[str, Any] = {}

class BatchPredictionResponse(BaseModel):
    batch_id: str
    results: Dict[str, Optional[PredictionResponse]]
    total_predictions: int
    successful_predictions: int
    timestamp: datetime

class AccuracyRequest(BaseModel):
    prediction_id: str
    actual_numbers: List[int]
    actual_special: Optional[int] = None

class LotteryConfigResponse(BaseModel):
    lottery_type: str
    name: str
    main_numbers_count: int
    special_numbers_count: int
    main_numbers_range: List[int]
    special_numbers_range: Optional[List[int]]
    draw_days: List[str]
    supported_strategies: List[str]

class SystemStatusResponse(BaseModel):
    status: str
    uptime: str
    active_predictions: int
    total_predictions: int
    system_health: Dict[str, str]
    last_updated: datetime

class RecommendationResponse(BaseModel):
    lottery_type: str
    recommended_strategies: List[Dict[str, Any]]
    cross_learning_opportunities: List[Dict[str, Any]]
    confidence_factors: Dict[str, float]
    optimization_suggestions: List[str]

# === 全局變量和依賴 ===

# 系統組件
db_manager: Optional[DBManager] = None
assessment_engine: Optional[AccuracyAssessmentEngine] = None
universal_predictor: Optional[UniversalPredictor] = None
realtime_manager: Optional[RealTimeDataManager] = None
scheduler: Optional[AutoPredictionScheduler] = None

# 系統狀態
system_start_time = datetime.now()
active_predictions: Dict[str, Dict] = {}
prediction_queue = asyncio.Queue()

# === 系統初始化 ===

async def initialize_system():
    """初始化系統組件"""
    global db_manager, assessment_engine, universal_predictor, realtime_manager, scheduler
    
    try:
        logger.info("🚀 初始化彩票預測系統...")
        
        # 初始化數據庫管理器
        db_manager = DBManager()
        logger.info("✅ 數據庫管理器初始化完成")
        
        # 初始化評估引擎
        assessment_engine = AccuracyAssessmentEngine(db_manager)
        logger.info("✅ 評估引擎初始化完成")
        
        # 初始化通用預測器
        universal_predictor = UniversalPredictor(db_manager, assessment_engine)
        logger.info("✅ 通用預測器初始化完成")
        
        # 初始化實時數據管理器
        realtime_manager = RealTimeDataManager("data/lottery_data.db")
        logger.info("✅ 實時數據管理器初始化完成")
        
        # 初始化自動調度器
        scheduler = AutoPredictionScheduler(db_manager, universal_predictor)
        logger.info("✅ 自動調度器初始化完成")
        
        # 初始化追蹤系統集成
        await initialize_tracking_integration(db_manager)
        logger.info("✅ 追蹤系統集成初始化完成")
        
        logger.info("🎉 系統初始化完成")
        
    except Exception as e:
        logger.error(f"❌ 系統初始化失敗: {e}")
        raise

async def shutdown_system():
    """關閉系統組件"""
    global scheduler, realtime_manager
    
    try:
        logger.info("🔄 關閉系統...")
        
        if scheduler:
            scheduler.stop_scheduler()
            logger.info("✅ 調度器已關閉")
        
        if realtime_manager:
            # 停止實時數據更新
            logger.info("✅ 實時數據管理器已關閉")
        
        logger.info("✅ 系統關閉完成")
        
    except Exception as e:
        logger.error(f"❌ 系統關閉失敗: {e}")

@asynccontextmanager
async def lifespan(app: FastAPI):
    """應用生命週期管理"""
    await initialize_system()
    yield
    await shutdown_system()

# === FastAPI 應用創建 ===

app = FastAPI(
    title="彩票預測系統 API",
    description="基於多算法集成的智能彩票預測系統",
    version="3.4.0",
    lifespan=lifespan
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生產環境應該限制具體域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含追蹤系統路由
app.include_router(get_tracking_router())

# === 依賴函數 ===

def get_universal_predictor() -> UniversalPredictor:
    """獲取通用預測器依賴"""
    if universal_predictor is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="預測器尚未初始化"
        )
    return universal_predictor

def get_assessment_engine() -> AccuracyAssessmentEngine:
    """獲取評估引擎依賴"""
    if assessment_engine is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="評估引擎尚未初始化"
        )
    return assessment_engine

def get_realtime_manager() -> RealTimeDataManager:
    """獲取實時數據管理器依賴"""
    if realtime_manager is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="實時數據管理器尚未初始化"
        )
    return realtime_manager

# === API 路由 ===

@app.get("/", tags=["系統"])
async def root():
    """根路由 - 系統歡迎信息"""
    return {
        "message": "彩票預測系統 API",
        "version": "3.4.0",
        "status": "運行中",
        "docs": "/docs",
        "timestamp": datetime.now()
    }

@app.get("/health", tags=["系統"], response_model=SystemStatusResponse)
async def health_check():
    """系統健康檢查"""
    uptime = datetime.now() - system_start_time
    
    # 檢查各組件狀態
    system_health = {}
    system_health["database"] = "正常" if db_manager else "異常"
    system_health["predictor"] = "正常" if universal_predictor else "異常"
    system_health["assessment"] = "正常" if assessment_engine else "異常"
    system_health["realtime"] = "正常" if realtime_manager else "異常"
    system_health["scheduler"] = "正常" if scheduler else "異常"
    
    # 計算總預測數
    total_predictions = len(active_predictions)
    
    return SystemStatusResponse(
        status="正常" if all(status == "正常" for status in system_health.values()) else "部分異常",
        uptime=str(uptime),
        active_predictions=len([p for p in active_predictions.values() if p.get('status') == 'active']),
        total_predictions=total_predictions,
        system_health=system_health,
        last_updated=datetime.now()
    )

@app.get("/lotteries", tags=["配置"], response_model=List[LotteryConfigResponse])
async def get_supported_lotteries(
    predictor: UniversalPredictor = Depends(get_universal_predictor)
):
    """獲取支持的彩票類型"""
    try:
        supported_lotteries = predictor.get_supported_lotteries()
        
        configs = []
        for lottery_type, config in supported_lotteries.items():
            configs.append(LotteryConfigResponse(
                lottery_type=lottery_type,
                name=config.name,
                main_numbers_count=config.main_numbers_count,
                special_numbers_count=config.special_numbers_count,
                main_numbers_range=config.main_numbers_range,
                special_numbers_range=config.special_numbers_range,
                draw_days=config.draw_days,
                supported_strategies=config.supported_strategies
            ))
        
        return configs
        
    except Exception as e:
        logger.error(f"獲取支持彩票類型失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"獲取彩票配置失敗: {str(e)}"
        )

@app.post("/predict", tags=["預測"], response_model=PredictionResponse)
async def create_prediction(
    request: PredictionRequest,
    background_tasks: BackgroundTasks,
    predictor: UniversalPredictor = Depends(get_universal_predictor)
):
    """創建單個預測"""
    try:
        # 驗證彩票類型
        supported_lotteries = predictor.get_supported_lotteries()
        if request.lottery_type not in supported_lotteries:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的彩票類型: {request.lottery_type}"
            )
        
        # 生成預測ID
        prediction_id = str(uuid.uuid4())
        
        # 記錄預測請求
        active_predictions[prediction_id] = {
            'status': 'active',
            'request': request.dict(),
            'start_time': datetime.now()
        }
        
        # 執行預測
        result = predictor.predict(
            lottery_type=request.lottery_type,
            strategy=request.strategy,
            use_cross_learning=request.use_cross_learning,
            ensemble_size=request.ensemble_size,
            data_window=request.data_window
        )
        
        if not result:
            active_predictions[prediction_id]['status'] = 'failed'
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="預測執行失敗"
            )
        
        # 更新預測狀態
        active_predictions[prediction_id]['status'] = 'completed'
        active_predictions[prediction_id]['result'] = result
        
        # 背景任務：記錄預測結果
        background_tasks.add_task(
            record_prediction_background,
            prediction_id,
            result
        )
        
        # 背景任務：記錄到追蹤系統
        tracking_integration = get_tracking_integration()
        if tracking_integration._initialized:
            background_tasks.add_task(
                record_prediction_to_tracking,
                tracking_integration.tracking_system,
                result,
                {"api_source": True, "prediction_type": "single"}
            )
        
        return PredictionResponse(
            prediction_id=result.prediction_id,
            lottery_type=result.lottery_type,
            main_numbers=result.main_numbers,
            special_numbers=result.special_numbers,
            confidence=result.confidence,
            strategy_used=result.strategy_used,
            prediction_timestamp=result.prediction_timestamp,
            metadata=result.metadata
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"預測創建失敗: {e}")
        if prediction_id in active_predictions:
            active_predictions[prediction_id]['status'] = 'failed'
            active_predictions[prediction_id]['error'] = str(e)
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"預測失敗: {str(e)}"
        )

@app.post("/predict/batch", tags=["預測"], response_model=BatchPredictionResponse)
async def create_batch_prediction(
    request: BatchPredictionRequest,
    background_tasks: BackgroundTasks,
    predictor: UniversalPredictor = Depends(get_universal_predictor)
):
    """創建批量預測"""
    try:
        # 驗證彩票類型
        supported_lotteries = predictor.get_supported_lotteries()
        unsupported_types = [lt for lt in request.lottery_types if lt not in supported_lotteries]
        
        if unsupported_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的彩票類型: {unsupported_types}"
            )
        
        # 生成批次ID
        batch_id = str(uuid.uuid4())
        
        # 執行批量預測
        batch_results = predictor.batch_predict(
            lottery_types=request.lottery_types,
            strategy=request.strategy,
            use_cross_learning=request.use_cross_learning,
            ensemble_size=request.ensemble_size
        )
        
        # 轉換結果格式
        response_results = {}
        successful_count = 0
        
        for lottery_type, result in batch_results.items():
            if result:
                response_results[lottery_type] = PredictionResponse(
                    prediction_id=result.prediction_id,
                    lottery_type=result.lottery_type,
                    main_numbers=result.main_numbers,
                    special_numbers=result.special_numbers,
                    confidence=result.confidence,
                    strategy_used=result.strategy_used,
                    prediction_timestamp=result.prediction_timestamp,
                    metadata=result.metadata
                )
                successful_count += 1
            else:
                response_results[lottery_type] = None
        
        # 背景任務：記錄批量預測結果
        background_tasks.add_task(
            record_batch_prediction_background,
            batch_id,
            batch_results
        )
        
        # 背景任務：記錄到追蹤系統
        tracking_integration = get_tracking_integration()
        if tracking_integration._initialized:
            for lottery_type, result in batch_results.items():
                if result:
                    background_tasks.add_task(
                        record_prediction_to_tracking,
                        tracking_integration.tracking_system,
                        result,
                        {"api_source": True, "prediction_type": "batch", "batch_id": batch_id}
                    )
        
        return BatchPredictionResponse(
            batch_id=batch_id,
            results=response_results,
            total_predictions=len(request.lottery_types),
            successful_predictions=successful_count,
            timestamp=datetime.now()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量預測創建失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量預測失敗: {str(e)}"
        )

@app.get("/predictions/{prediction_id}", tags=["預測"])
async def get_prediction(prediction_id: str):
    """獲取預測結果"""
    if prediction_id not in active_predictions:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="預測記錄不存在"
        )
    
    prediction = active_predictions[prediction_id]
    return {
        "prediction_id": prediction_id,
        "status": prediction['status'],
        "request": prediction['request'],
        "start_time": prediction['start_time'],
        "result": prediction.get('result'),
        "error": prediction.get('error')
    }

@app.post("/evaluate", tags=["評估"])
async def evaluate_prediction(
    request: AccuracyRequest,
    assessment: AccuracyAssessmentEngine = Depends(get_assessment_engine)
):
    """評估預測準確度"""
    try:
        # 構建實際結果
        actual_result = {}
        for i, num in enumerate(request.actual_numbers[:6], 1):
            actual_result[f'Anumber{i}'] = num
        
        if request.actual_special is not None:
            actual_result['Bnumber'] = request.actual_special
        
        # 執行評估
        success = assessment.evaluate_prediction_accuracy(
            request.prediction_id,
            actual_result
        )
        
        return {
            "prediction_id": request.prediction_id,
            "evaluation_success": success,
            "actual_numbers": request.actual_numbers,
            "actual_special": request.actual_special,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"評估失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"評估失敗: {str(e)}"
        )

@app.get("/recommendations/{lottery_type}", tags=["建議"], response_model=RecommendationResponse)
async def get_prediction_recommendations(
    lottery_type: str,
    predictor: UniversalPredictor = Depends(get_universal_predictor)
):
    """獲取預測建議"""
    try:
        # 驗證彩票類型
        supported_lotteries = predictor.get_supported_lotteries()
        if lottery_type not in supported_lotteries:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的彩票類型: {lottery_type}"
            )
        
        # 獲取建議
        recommendations = predictor.get_prediction_recommendations(lottery_type)
        
        if not recommendations:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="無法生成預測建議"
            )
        
        return RecommendationResponse(
            lottery_type=lottery_type,
            recommended_strategies=recommendations.get('recommended_strategies', []),
            cross_learning_opportunities=recommendations.get('cross_learning_opportunities', []),
            confidence_factors=recommendations.get('confidence_factors', {}),
            optimization_suggestions=recommendations.get('optimization_suggestions', [])
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取建議失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"獲取建議失敗: {str(e)}"
        )

@app.get("/analytics/{lottery_type}", tags=["分析"])
async def get_analytics(
    lottery_type: str,
    days: int = 30,
    assessment: AccuracyAssessmentEngine = Depends(get_assessment_engine)
):
    """獲取分析統計"""
    try:
        # 獲取綜合報告
        report = assessment.get_comprehensive_report(lottery_type)
        
        if 'error' in report:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=report['error']
            )
        
        return {
            "lottery_type": lottery_type,
            "analysis_period_days": days,
            "accuracy_metrics": report.get('accuracy_metrics', {}),
            "algorithm_performance": report.get('algorithm_performance', {}),
            "optimization_history": report.get('optimization_history', []),
            "recommendations": report.get('recommendations', []),
            "timestamp": datetime.now()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取分析失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"獲取分析失敗: {str(e)}"
        )

@app.get("/realtime/status", tags=["實時數據"])
async def get_realtime_status(
    realtime: RealTimeDataManager = Depends(get_realtime_manager)
):
    """獲取實時數據狀態"""
    try:
        # 獲取所有彩票類型的最新數據狀態
        status_info = {}
        
        for lottery_type in LotteryType.get_all_types():
            if LotteryType.is_supported(lottery_type):
                # 檢查最新數據
                latest_data = realtime.get_latest_data(lottery_type)
                if latest_data:
                    status_info[lottery_type] = {
                        "last_update": latest_data.last_updated,
                        "data_status": "正常",
                        "cache_status": "已緩存" if realtime.cache_manager.get_cached_data(lottery_type) else "無緩存"
                    }
                else:
                    status_info[lottery_type] = {
                        "last_update": None,
                        "data_status": "無數據",
                        "cache_status": "無緩存"
                    }
        
        return {
            "overall_status": "正常",
            "lottery_status": status_info,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"獲取實時狀態失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"獲取實時狀態失敗: {str(e)}"
        )

# === 背景任務 ===

async def record_prediction_background(prediction_id: str, result: UniversalPredictionResult):
    """背景任務：記錄預測結果"""
    try:
        if assessment_engine:
            # 這裡可以添加更多的記錄邏輯
            logger.info(f"已記錄預測結果: {prediction_id}")
    except Exception as e:
        logger.error(f"記錄預測結果失敗: {e}")

async def record_batch_prediction_background(batch_id: str, results: Dict[str, Any]):
    """背景任務：記錄批量預測結果"""
    try:
        if assessment_engine:
            # 這裡可以添加更多的記錄邏輯
            logger.info(f"已記錄批量預測結果: {batch_id}")
    except Exception as e:
        logger.error(f"記錄批量預測結果失敗: {e}")

# === WebSocket 支持 (可選) ===

from fastapi import WebSocket, WebSocketDisconnect

class ConnectionManager:
    """WebSocket 連接管理器"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
    
    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
    
    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
    
    async def send_message(self, message: dict, websocket: WebSocket):
        await websocket.send_json(message)
    
    async def broadcast(self, message: dict):
        for connection in self.active_connections:
            try:
                await connection.send_json(message)
            except:
                # 移除斷開的連接
                self.disconnect(connection)

manager = ConnectionManager()

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket 端點 - 實時預測更新"""
    await manager.connect(websocket)
    try:
        while True:
            # 接收客戶端消息
            data = await websocket.receive_json()
            
            # 處理不同類型的消息
            if data.get("type") == "subscribe":
                # 訂閱實時更新
                await manager.send_message({
                    "type": "subscription_confirmed",
                    "message": "已訂閱實時更新"
                }, websocket)
            
            elif data.get("type") == "status_request":
                # 發送系統狀態
                uptime = datetime.now() - system_start_time
                status_message = {
                    "type": "status_update",
                    "data": {
                        "uptime": str(uptime),
                        "active_predictions": len(active_predictions),
                        "system_status": "正常",
                        "timestamp": datetime.now().isoformat()
                    }
                }
                await manager.send_message(status_message, websocket)
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket 錯誤: {e}")
        manager.disconnect(websocket)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "web_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )