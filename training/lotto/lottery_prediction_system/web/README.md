# 彩票預測系統 - 重構版

這是一個基於Python和Flask的彩票預測系統，提供多種彩票類型的數據分析、預測和結果驗證功能。本文檔提供系統架構、安裝步驟和使用指南。

## 系統架構

系統採用模塊化設計，主要組件包括：

### 核心模塊

- **app_refactored.py**: 應用程序入口點，使用Flask框架實現Web服務
- **config_manager.py**: 配置管理器，負責加載和管理系統配置
- **services.py**: 服務層，封裝業務邏輯
- **middleware.py**: 中間件，提供錯誤處理、API響應標準化、性能監控等功能
- **cache_manager.py**: 緩存管理器，提供統一的緩存接口
- **exceptions.py**: 異常處理模塊，定義和處理系統異常

### 工具模塊

- **deploy.py**: 部署工具，用於部署和管理應用程序
- **monitor.py**: 監控工具，監控系統健康狀態和性能
- **tests.py**: 測試模塊，提供單元測試和集成測試

### 架構圖

```
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  Web Interface   |---->|  Service Layer   |---->|  Data Access     |
|  (Flask Routes)  |     |  (Business Logic)|     |  (DB/Cache)      |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
         |                        |                        |
         v                        v                        v
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  Middleware      |     |  Configuration   |     |  Exception       |
|  (Error/Perf)    |     |  Management      |     |  Handling        |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
```

## 安裝與部署

### 系統要求

- Python 3.8+
- SQLite 3
- 可選: Redis (用於高級緩存)

### 安裝步驟

1. 克隆代碼庫

```bash
git clone <repository-url>
cd lottery_prediction_system
```

2. 安裝依賴

```bash
pip install -r requirements.txt
```

3. 使用部署工具部署應用

```bash
python web/deploy.py deploy
```

### 配置

系統配置存儲在 `web/config.json` 文件中，包含以下主要部分：

- **app**: 應用程序設置（主機、端口、調試模式等）
- **database**: 數據庫設置
- **cache**: 緩存設置
- **logging**: 日誌設置
- **api**: API設置（速率限制、CORS等）
- **prediction**: 預測相關設置

## 使用指南

### 啟動應用

```bash
python web/deploy.py start
```

應用將在 http://localhost:5003 啟動。

### 停止應用

```bash
python web/deploy.py stop
```

### 查看應用狀態

```bash
python web/deploy.py status
```

### 運行監控

```bash
python web/monitor.py
```

### 運行測試

```bash
python web/tests.py
```

## API 參考

系統提供以下主要API端點：

### 預測相關

- **GET /api/latest_predictions/{lottery_type}**: 獲取最新預測
- **GET /api/accuracy/{lottery_type}**: 獲取預測準確率
- **POST /api/predict**: 創建新預測
- **PUT /api/verify_prediction/{prediction_id}**: 驗證預測結果

### 分析相關

- **GET /api/comprehensive_analysis/{lottery_type}**: 獲取綜合分析
- **POST /api/analyze_numbers**: 分析數字組合
- **GET /api/success_analysis/{lottery_type}**: 獲取成功預測分析

### 數據相關

- **GET /api/periods/{lottery_type}**: 獲取彩票期數列表
- **GET /api/actual_results/{lottery_type}/{period}**: 獲取實際開獎結果

### 系統相關

- **GET /api/system_status**: 獲取系統狀態
- **POST /api/daily_automation**: 執行日常自動化任務
- **POST /api/strategy_optimize/{lottery_type}**: 優化預測策略
- **POST /api/cache/clear**: 清除緩存

## 頁面導航

- **/** 或 **/dashboard**: 儀表板頁面，顯示系統概覽和最新預測
- **/predictions**: 預測歷史記錄頁面，可查詢和過濾預測記錄
- **/analysis**: 分析頁面，提供數據分析工具
- **/comprehensive_analysis**: 綜合分析頁面，顯示深度分析結果
- **/enhanced_prediction**: 智能預測頁面，提供高級預測功能

## 開發指南

### 代碼結構

```
lottery_prediction_system/
├── web/
│   ├── app_refactored.py       # 應用程序入口
│   ├── config_manager.py       # 配置管理
│   ├── services.py             # 服務層
│   ├── middleware.py           # 中間件
│   ├── cache_manager.py        # 緩存管理
│   ├── exceptions.py           # 異常處理
│   ├── deploy.py               # 部署工具
│   ├── monitor.py              # 監控工具
│   ├── tests.py                # 測試模塊
│   ├── static/                 # 靜態資源
│   └── templates/              # 模板文件
├── data/                       # 數據文件
└── backups/                    # 備份文件
```

### 添加新功能

1. 在 `services.py` 中添加業務邏輯
2. 在 `app_refactored.py` 中添加路由
3. 更新測試用例
4. 運行測試確保功能正常

### 代碼風格

- 遵循PEP 8編碼規範
- 使用類型提示增強代碼可讀性
- 為所有函數和類添加文檔字符串
- 使用異常處理捕獲和處理錯誤

## 故障排除

### 常見問題

1. **應用無法啟動**
   - 檢查端口是否被佔用
   - 檢查配置文件是否正確
   - 查看日誌文件獲取詳細錯誤信息

2. **數據庫錯誤**
   - 確保數據庫文件存在且有正確的權限
   - 檢查數據庫路徑配置

3. **預測功能不工作**
   - 確保已有足夠的歷史數據
   - 檢查預測引擎配置

### 日誌位置

- 應用日誌: `web/app.log`
- 監控日誌: `web/monitor.log`

## 維護

### 數據庫備份

```bash
python web/deploy.py backup
```

備份文件將保存在 `backups/` 目錄中。

### 系統更新

1. 拉取最新代碼
2. 重新部署應用

```bash
python web/deploy.py deploy
```

## 許可證

本項目採用 MIT 許可證。詳見 LICENSE 文件。

## 聯繫方式

如有問題或建議，請聯繫系統管理員。