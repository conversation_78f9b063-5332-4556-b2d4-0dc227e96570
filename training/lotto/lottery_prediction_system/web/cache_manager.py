#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
緩存管理模組
提供統一的緩存接口，支持Redis和內存緩存
"""

import json
import time
import pickle
import hashlib
import logging
from typing import Any, Optional, Dict, Union, Callable
from functools import wraps
from datetime import datetime, timedelta
from abc import ABC, abstractmethod

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False


class CacheBackend(ABC):
    """緩存後端抽象基類"""
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """獲取緩存值"""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """設置緩存值"""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """刪除緩存"""
        pass
    
    @abstractmethod
    def exists(self, key: str) -> bool:
        """檢查緩存是否存在"""
        pass
    
    @abstractmethod
    def clear(self) -> bool:
        """清空所有緩存"""
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, Any]:
        """獲取緩存統計信息"""
        pass


class MemoryCache(CacheBackend):
    """內存緩存實現"""
    
    def __init__(self, max_size: int = 1000):
        self.cache = {}
        self.access_times = {}
        self.expire_times = {}
        self.max_size = max_size
        self.hits = 0
        self.misses = 0
        self.logger = logging.getLogger(__name__)
    
    def _cleanup_expired(self):
        """清理過期的緩存項"""
        current_time = time.time()
        expired_keys = [
            key for key, expire_time in self.expire_times.items()
            if expire_time and expire_time < current_time
        ]
        
        for key in expired_keys:
            self._remove_key(key)
    
    def _remove_key(self, key: str):
        """移除緩存項"""
        self.cache.pop(key, None)
        self.access_times.pop(key, None)
        self.expire_times.pop(key, None)
    
    def _evict_lru(self):
        """LRU淘汰策略"""
        if len(self.cache) >= self.max_size:
            # 找到最少使用的key
            lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
            self._remove_key(lru_key)
    
    def get(self, key: str) -> Optional[Any]:
        """獲取緩存值"""
        self._cleanup_expired()
        
        if key in self.cache:
            # 檢查是否過期
            if key in self.expire_times and self.expire_times[key]:
                if time.time() > self.expire_times[key]:
                    self._remove_key(key)
                    self.misses += 1
                    return None
            
            # 更新訪問時間
            self.access_times[key] = time.time()
            self.hits += 1
            return self.cache[key]
        
        self.misses += 1
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """設置緩存值"""
        try:
            self._cleanup_expired()
            self._evict_lru()
            
            self.cache[key] = value
            self.access_times[key] = time.time()
            
            if ttl:
                self.expire_times[key] = time.time() + ttl
            else:
                self.expire_times[key] = None
            
            return True
        except Exception as e:
            self.logger.error(f"設置內存緩存失敗: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """刪除緩存"""
        if key in self.cache:
            self._remove_key(key)
            return True
        return False
    
    def exists(self, key: str) -> bool:
        """檢查緩存是否存在"""
        return self.get(key) is not None
    
    def clear(self) -> bool:
        """清空所有緩存"""
        self.cache.clear()
        self.access_times.clear()
        self.expire_times.clear()
        return True
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取緩存統計信息"""
        total_requests = self.hits + self.misses
        hit_rate = self.hits / total_requests if total_requests > 0 else 0
        
        return {
            'backend': 'memory',
            'total_keys': len(self.cache),
            'max_size': self.max_size,
            'hits': self.hits,
            'misses': self.misses,
            'hit_rate': hit_rate,
            'memory_usage': sum(len(str(v)) for v in self.cache.values())
        }


class RedisCache(CacheBackend):
    """Redis緩存實現"""
    
    def __init__(self, redis_client: 'redis.Redis', key_prefix: str = 'lottery_cache:'):
        self.redis = redis_client
        self.key_prefix = key_prefix
        self.logger = logging.getLogger(__name__)
    
    def _make_key(self, key: str) -> str:
        """生成完整的緩存key"""
        return f"{self.key_prefix}{key}"
    
    def get(self, key: str) -> Optional[Any]:
        """獲取緩存值"""
        try:
            full_key = self._make_key(key)
            data = self.redis.get(full_key)
            
            if data is None:
                return None
            
            # 嘗試JSON解析，失敗則使用pickle
            try:
                return json.loads(data)
            except (json.JSONDecodeError, TypeError):
                return pickle.loads(data)
                
        except Exception as e:
            self.logger.error(f"Redis獲取緩存失敗: {e}")
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """設置緩存值"""
        try:
            full_key = self._make_key(key)
            
            # 嘗試JSON序列化，失敗則使用pickle
            try:
                data = json.dumps(value, ensure_ascii=False)
            except (TypeError, ValueError):
                data = pickle.dumps(value)
            
            if ttl:
                return self.redis.setex(full_key, ttl, data)
            else:
                return self.redis.set(full_key, data)
                
        except Exception as e:
            self.logger.error(f"Redis設置緩存失敗: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """刪除緩存"""
        try:
            full_key = self._make_key(key)
            return bool(self.redis.delete(full_key))
        except Exception as e:
            self.logger.error(f"Redis刪除緩存失敗: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """檢查緩存是否存在"""
        try:
            full_key = self._make_key(key)
            return bool(self.redis.exists(full_key))
        except Exception as e:
            self.logger.error(f"Redis檢查緩存失敗: {e}")
            return False
    
    def clear(self) -> bool:
        """清空所有緩存"""
        try:
            pattern = f"{self.key_prefix}*"
            keys = self.redis.keys(pattern)
            if keys:
                return bool(self.redis.delete(*keys))
            return True
        except Exception as e:
            self.logger.error(f"Redis清空緩存失敗: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取緩存統計信息"""
        try:
            info = self.redis.info()
            pattern = f"{self.key_prefix}*"
            keys = self.redis.keys(pattern)
            
            return {
                'backend': 'redis',
                'total_keys': len(keys),
                'redis_version': info.get('redis_version'),
                'used_memory': info.get('used_memory_human'),
                'connected_clients': info.get('connected_clients'),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0)
            }
        except Exception as e:
            self.logger.error(f"獲取Redis統計失敗: {e}")
            return {'backend': 'redis', 'error': str(e)}


class CacheManager:
    """緩存管理器"""
    
    def __init__(self, backend: CacheBackend, default_ttl: int = 3600):
        self.backend = backend
        self.default_ttl = default_ttl
        self.logger = logging.getLogger(__name__)
    
    def get(self, key: str) -> Optional[Any]:
        """獲取緩存"""
        return self.backend.get(key)
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """設置緩存"""
        if ttl is None:
            ttl = self.default_ttl
        return self.backend.set(key, value, ttl)
    
    def delete(self, key: str) -> bool:
        """刪除緩存"""
        return self.backend.delete(key)
    
    def exists(self, key: str) -> bool:
        """檢查緩存是否存在"""
        return self.backend.exists(key)
    
    def clear(self) -> bool:
        """清空緩存"""
        return self.backend.clear()
    
    def get_or_set(self, key: str, func: Callable[[], Any], ttl: Optional[int] = None) -> Any:
        """獲取緩存，如果不存在則執行函數並緩存結果"""
        value = self.get(key)
        if value is not None:
            return value
        
        # 執行函數獲取值
        value = func()
        if value is not None:
            self.set(key, value, ttl)
        
        return value
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取緩存統計"""
        return self.backend.get_stats()
    
    def make_key(self, *parts) -> str:
        """生成緩存key"""
        # 將所有部分轉換為字符串並連接
        key_parts = [str(part) for part in parts if part is not None]
        key = ':'.join(key_parts)
        
        # 如果key太長，使用hash
        if len(key) > 200:
            key_hash = hashlib.md5(key.encode('utf-8')).hexdigest()
            return f"hash:{key_hash}"
        
        return key


def cached(ttl: int = 3600, key_func: Optional[Callable] = None):
    """緩存裝飾器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 獲取緩存管理器實例
            cache_manager = getattr(wrapper, '_cache_manager', None)
            if not cache_manager:
                return func(*args, **kwargs)
            
            # 生成緩存key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # 默認key生成策略
                func_name = f"{func.__module__}.{func.__name__}"
                args_str = '_'.join(str(arg) for arg in args)
                kwargs_str = '_'.join(f"{k}={v}" for k, v in sorted(kwargs.items()))
                cache_key = cache_manager.make_key(func_name, args_str, kwargs_str)
            
            # 嘗試從緩存獲取
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 執行函數並緩存結果
            result = func(*args, **kwargs)
            if result is not None:
                cache_manager.set(cache_key, result, ttl)
            
            return result
        
        return wrapper
    return decorator


def create_cache_manager(redis_url: Optional[str] = None, 
                        memory_max_size: int = 1000,
                        default_ttl: int = 3600) -> CacheManager:
    """創建緩存管理器"""
    logger = logging.getLogger(__name__)
    
    # 嘗試使用Redis
    if redis_url and REDIS_AVAILABLE:
        try:
            redis_client = redis.from_url(redis_url, decode_responses=False)
            redis_client.ping()  # 測試連接
            backend = RedisCache(redis_client)
            logger.info("使用Redis緩存後端")
            return CacheManager(backend, default_ttl)
        except Exception as e:
            logger.warning(f"Redis連接失敗，降級到內存緩存: {e}")
    
    # 降級到內存緩存
    backend = MemoryCache(memory_max_size)
    logger.info("使用內存緩存後端")
    return CacheManager(backend, default_ttl)


# 全局緩存管理器實例
cache_manager = None

def init_cache_manager(redis_url: Optional[str] = None, **kwargs):
    """初始化全局緩存管理器"""
    global cache_manager
    cache_manager = create_cache_manager(redis_url, **kwargs)
    return cache_manager

def get_cache_manager() -> Optional[CacheManager]:
    """獲取全局緩存管理器"""
    return cache_manager