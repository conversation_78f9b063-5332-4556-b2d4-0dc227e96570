#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服務層模組
封裝業務邏輯，提供統一的服務接口
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from abc import ABC, abstractmethod
from functools import wraps
import time

# 導入核心模組
try:
    from data.db_manager import DBManager
    from analysis import PredictionAnalyzer
    from prediction import IntegratedPredictor
    from display import DisplayManager
    from automation import DailyAutomation
    from optimization import StrategyOptimizer
    from pattern_analysis import PatternAnalyzer
    from enhanced_board_analysis import EnhancedBoardAnalyzer
    from best_prediction_integrator import BestPredictionIntegrator
    CORE_MODULES_AVAILABLE = True
except ImportError as e:
    logging.warning(f"導入核心模組失敗: {e}")
    # 創建模擬類型
    DBManager = None
    PredictionAnalyzer = None
    IntegratedPredictor = None
    DisplayManager = None
    DailyAutomation = None
    StrategyOptimizer = None
    PatternAnalyzer = None
    EnhancedBoardAnalyzer = None
    BestPredictionIntegrator = None
    CORE_MODULES_AVAILABLE = False

from cache_manager import CacheManager, cached
from middleware import APIResponse


@dataclass
class PredictionRequest:
    """預測請求數據"""
    lottery_type: str
    period: Optional[str] = None
    mode: str = 'next_period'
    analysis_type: str = 'comprehensive'
    use_cache: bool = True


@dataclass
class AnalysisRequest:
    """分析請求數據"""
    lottery_type: str
    period: Optional[str] = None
    analysis_types: List[str] = None
    include_patterns: bool = True
    include_board_analysis: bool = True


class BaseService(ABC):
    """服務基類"""
    
    def __init__(self, cache_manager: Optional[CacheManager] = None):
        self.cache_manager = cache_manager
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def _get_cache_key(self, *parts) -> str:
        """生成緩存key"""
        if self.cache_manager:
            return self.cache_manager.make_key(self.__class__.__name__.lower(), *parts)
        return ':'.join(str(part) for part in parts)
    
    def _cache_get(self, key: str) -> Optional[Any]:
        """獲取緩存"""
        if self.cache_manager:
            return self.cache_manager.get(key)
        return None
    
    def _cache_set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """設置緩存"""
        if self.cache_manager:
            return self.cache_manager.set(key, value, ttl)
        return False


class PredictionService(BaseService):
    """預測服務"""
    
    def __init__(self, db_manager=None, 
                 integrated_predictor=None,
                 best_integrator=None,
                 cache_manager: Optional[CacheManager] = None,
                 config_manager=None):
        super().__init__(cache_manager)
        self.config_manager = config_manager
        # 如果沒有提供 db_manager，則使用配置管理器創建
        if db_manager is None and CORE_MODULES_AVAILABLE:
            self.db_manager = DBManager(config_manager=config_manager)
        else:
            self.db_manager = db_manager
        self.integrated_predictor = integrated_predictor
        self.best_integrator = best_integrator
        self.core_available = CORE_MODULES_AVAILABLE
    
    @cached(ttl=1800)  # 30分鐘緩存
    def get_latest_predictions(self, lottery_type: str, limit: int = 10) -> APIResponse:
        """獲取最新預測"""
        if not self.core_available or not self.db_manager:
            # 返回模擬數據
            import random
            mock_predictions = []
            for i in range(min(limit, 5)):
                if lottery_type in ['powercolor', 'lotto649']:
                    numbers = sorted(random.sample(range(1, 50), 6))
                    special = random.randint(1, 49)
                else:
                    numbers = sorted(random.sample(range(1, 40), 5))
                    special = None
                
                mock_predictions.append({
                    'id': 1000 + i,
                    'period': f'2025{str(i+1).zfill(3)}',
                    'predicted_numbers': numbers,
                    'special_number': special,
                    'prediction_time': datetime.now().isoformat(),
                    'method': 'mock',
                    'confidence': round(random.uniform(0.6, 0.9), 2),
                    'status': 'pending'
                })
            
            return APIResponse.success({
                'predictions': mock_predictions,
                'total': len(mock_predictions),
                'lottery_type': lottery_type
            })
        
        try:
            predictions = self.db_manager.get_latest_predictions(lottery_type, limit)
            
            formatted_predictions = []
            for pred in predictions:
                formatted_pred = {
                    'id': pred.get('id'),
                    'period': pred.get('period'),
                    'predicted_numbers': pred.get('predicted_numbers', []),
                    'prediction_time': pred.get('prediction_time'),
                    'method': pred.get('method', 'unknown'),
                    'confidence': pred.get('confidence', 0),
                    'status': pred.get('status', 'pending')
                }
                formatted_predictions.append(formatted_pred)
            
            return APIResponse.success({
                'predictions': formatted_predictions,
                'total': len(formatted_predictions),
                'lottery_type': lottery_type
            })
            
        except Exception as e:
            self.logger.error(f"獲取最新預測失敗: {e}")
            return APIResponse.error(f"獲取預測數據失敗: {str(e)}")
    
    def create_prediction(self, request: PredictionRequest) -> APIResponse:
        """創建新預測"""
        try:
            # 檢查緩存
            if request.use_cache:
                cache_key = self._get_cache_key('prediction', request.lottery_type, 
                                              request.period, request.mode)
                cached_result = self._cache_get(cache_key)
                if cached_result:
                    return APIResponse.success(cached_result)
            
            # 生成預測
            if request.mode == 'simulation':
                prediction_result = self.integrated_predictor.predict_with_simulation(
                    request.lottery_type, request.period
                )
            else:
                prediction_result = self.integrated_predictor.predict_next_period(
                    request.lottery_type
                )
            
            # 使用最佳預測整合器
            if hasattr(self.best_integrator, 'integrate_predictions'):
                enhanced_result = self.best_integrator.integrate_predictions(
                    request.lottery_type, [prediction_result]
                )
                if enhanced_result:
                    prediction_result = enhanced_result
            
            # 緩存結果
            if request.use_cache and prediction_result:
                self._cache_set(cache_key, prediction_result, 1800)
            
            return APIResponse.success(prediction_result)
            
        except Exception as e:
            self.logger.error(f"創建預測失敗: {e}")
            return APIResponse.error(f"預測生成失敗: {str(e)}")
    
    def verify_prediction(self, prediction_id: int, actual_numbers: List[int]) -> APIResponse:
        """驗證預測結果"""
        try:
            # 獲取預測記錄
            prediction = self.db_manager.get_prediction_by_id(prediction_id)
            if not prediction:
                return APIResponse.error("預測記錄不存在")
            
            # 計算匹配度
            predicted_numbers = prediction.get('predicted_numbers', [])
            matches = len(set(predicted_numbers) & set(actual_numbers))
            total_numbers = len(predicted_numbers)
            accuracy = matches / total_numbers if total_numbers > 0 else 0
            
            # 更新預測記錄
            verification_data = {
                'actual_numbers': actual_numbers,
                'matches': matches,
                'accuracy': accuracy,
                'verified_at': datetime.now(),
                'status': 'verified'
            }
            
            success = self.db_manager.update_prediction_verification(
                prediction_id, verification_data
            )
            
            if success:
                return APIResponse.success({
                    'prediction_id': prediction_id,
                    'matches': matches,
                    'accuracy': accuracy,
                    'status': 'verified'
                })
            else:
                return APIResponse.error("更新驗證結果失敗")
                
        except Exception as e:
            self.logger.error(f"驗證預測失敗: {e}")
            return APIResponse.error(f"驗證失敗: {str(e)}")


class AnalysisService(BaseService):
    """分析服務"""
    
    def __init__(self, db_manager=None,
                 prediction_analyzer=None,
                 pattern_analyzer=None,
                 board_analyzer=None,
                 cache_manager: Optional[CacheManager] = None,
                 config_manager=None):
        super().__init__(cache_manager)
        self.config_manager = config_manager
        # 如果沒有提供 db_manager，則使用配置管理器創建
        if db_manager is None and CORE_MODULES_AVAILABLE:
            self.db_manager = DBManager(config_manager=config_manager)
        else:
            self.db_manager = db_manager
        self.prediction_analyzer = prediction_analyzer
        self.pattern_analyzer = pattern_analyzer
        self.board_analyzer = board_analyzer
        self.core_available = CORE_MODULES_AVAILABLE
    
    @cached(ttl=3600)  # 1小時緩存
    def get_accuracy_analysis(self, lottery_type: str, days: int = 30) -> APIResponse:
        """獲取準確率分析"""
        try:
            # 獲取指定天數內的預測記錄
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            accuracy_data = self.prediction_analyzer.analyze_accuracy(
                lottery_type, start_date, end_date
            )
            
            return APIResponse.success({
                'lottery_type': lottery_type,
                'period': f'{days}天',
                'accuracy_data': accuracy_data,
                'analysis_time': datetime.now().isoformat()
            })
            
        except Exception as e:
            self.logger.error(f"準確率分析失敗: {e}")
            return APIResponse.error(f"分析失敗: {str(e)}")
    
    @cached(ttl=7200)  # 2小時緩存
    def get_comprehensive_analysis(self, request: AnalysisRequest) -> APIResponse:
        """獲取綜合分析"""
        try:
            analysis_result = {
                'lottery_type': request.lottery_type,
                'analysis_time': datetime.now().isoformat()
            }
            
            # 預測準確率分析
            if not request.analysis_types or 'accuracy' in request.analysis_types:
                accuracy_data = self.prediction_analyzer.analyze_accuracy(
                    request.lottery_type
                )
                analysis_result['accuracy_analysis'] = accuracy_data
            
            # 成功預測分析
            if not request.analysis_types or 'success' in request.analysis_types:
                success_data = self.prediction_analyzer.analyze_successful_predictions(
                    request.lottery_type
                )
                analysis_result['success_analysis'] = success_data
            
            # 數字模式分析
            if request.include_patterns:
                pattern_data = self.pattern_analyzer.analyze_patterns(
                    request.lottery_type
                )
                analysis_result['pattern_analysis'] = pattern_data
            
            # 增強看板分析
            if request.include_board_analysis:
                board_data = self.board_analyzer.analyze(
                    request.lottery_type, request.period or 'next_period'
                )
                analysis_result['board_analysis'] = board_data
            
            return APIResponse.success(analysis_result)
            
        except Exception as e:
            self.logger.error(f"綜合分析失敗: {e}")
            return APIResponse.error(f"分析失敗: {str(e)}")
    
    def analyze_numbers(self, lottery_type: str, numbers: List[int]) -> APIResponse:
        """分析數字組合"""
        try:
            # 基本統計分析
            analysis_result = {
                'numbers': numbers,
                'lottery_type': lottery_type,
                'analysis_time': datetime.now().isoformat()
            }
            
            # 數字頻率分析
            if hasattr(self.pattern_analyzer, 'analyze_number_frequency'):
                frequency_data = self.pattern_analyzer.analyze_number_frequency(
                    lottery_type, numbers
                )
                analysis_result['frequency_analysis'] = frequency_data
            
            # 數字模式分析
            if hasattr(self.pattern_analyzer, 'analyze_number_patterns'):
                pattern_data = self.pattern_analyzer.analyze_number_patterns(
                    lottery_type, numbers
                )
                analysis_result['pattern_analysis'] = pattern_data
            
            # 歷史匹配分析
            historical_matches = self.db_manager.find_similar_predictions(
                lottery_type, numbers
            )
            analysis_result['historical_matches'] = historical_matches
            
            return APIResponse.success(analysis_result)
            
        except Exception as e:
            self.logger.error(f"數字分析失敗: {e}")
            return APIResponse.error(f"分析失敗: {str(e)}")


class DataService(BaseService):
    """數據服務"""
    
    def __init__(self, db_manager=None, 
                 cache_manager: Optional[CacheManager] = None,
                 config_manager=None):
        super().__init__(cache_manager)
        self.config_manager = config_manager
        # 如果沒有提供 db_manager，則使用配置管理器創建
        if db_manager is None and CORE_MODULES_AVAILABLE:
            self.db_manager = DBManager(config_manager=config_manager)
        else:
            self.db_manager = db_manager
        self.core_available = CORE_MODULES_AVAILABLE
    
    @cached(ttl=3600)  # 1小時緩存
    def get_periods(self, lottery_type: str, limit: int = 50) -> APIResponse:
        """獲取期數列表"""
        try:
            periods = self.db_manager.get_periods(lottery_type, limit)
            
            formatted_periods = []
            for period in periods:
                formatted_period = {
                    'period': period.get('period'),
                    'draw_date': period.get('draw_date'),
                    'numbers': period.get('numbers', []),
                    'status': period.get('status', 'completed')
                }
                formatted_periods.append(formatted_period)
            
            return APIResponse.success({
                'periods': formatted_periods,
                'total': len(formatted_periods),
                'lottery_type': lottery_type
            })
            
        except Exception as e:
            self.logger.error(f"獲取期數失敗: {e}")
            return APIResponse.error(f"獲取數據失敗: {str(e)}")
    
    @cached(ttl=7200)  # 2小時緩存
    def get_actual_results(self, lottery_type: str, period: str) -> APIResponse:
        """獲取實際開獎結果"""
        try:
            result = self.db_manager.get_actual_result(lottery_type, period)
            
            if not result:
                return APIResponse.error("未找到開獎結果")
            
            formatted_result = {
                'lottery_type': lottery_type,
                'period': period,
                'numbers': result.get('numbers', []),
                'special_number': result.get('special_number'),
                'draw_date': result.get('draw_date'),
                'prize_info': result.get('prize_info', {})
            }
            
            return APIResponse.success(formatted_result)
            
        except Exception as e:
            self.logger.error(f"獲取開獎結果失敗: {e}")
            return APIResponse.error(f"獲取結果失敗: {str(e)}")
    
    def get_dashboard_data(self) -> APIResponse:
        """獲取儀表板數據"""
        try:
            dashboard_data = {
                'timestamp': datetime.now().isoformat(),
                'lottery_types': ['lotto649', 'dailycash', 'powercolor']
            }
            
            # 獲取各彩票類型的最新數據
            for lottery_type in dashboard_data['lottery_types']:
                try:
                    # 最新預測
                    latest_predictions = self.db_manager.get_latest_predictions(
                        lottery_type, 3
                    )
                    
                    # 系統狀態
                    system_status = {
                        'status': 'active',
                        'last_prediction': latest_predictions[0].get('prediction_time') if latest_predictions else None,
                        'total_predictions': len(latest_predictions)
                    }
                    
                    dashboard_data[lottery_type] = {
                        'latest_predictions': latest_predictions,
                        'system_status': system_status
                    }
                    
                except Exception as e:
                    self.logger.warning(f"獲取{lottery_type}數據失敗: {e}")
                    dashboard_data[lottery_type] = {
                        'latest_predictions': [],
                        'system_status': {'status': 'error', 'error': str(e)}
                    }
            
            return APIResponse.success(dashboard_data)
            
        except Exception as e:
            self.logger.error(f"獲取儀表板數據失敗: {e}")
            return APIResponse.error(f"獲取數據失敗: {str(e)}")


class AutomationService(BaseService):
    """自動化服務"""
    
    def __init__(self, daily_automation=None,
                 strategy_optimizer=None,
                 cache_manager: Optional[CacheManager] = None,
                 config_manager=None):
        super().__init__(cache_manager)
        self.config_manager = config_manager
        self.daily_automation = daily_automation
        self.strategy_optimizer = strategy_optimizer
        self.core_available = CORE_MODULES_AVAILABLE
    
    def run_daily_tasks(self) -> APIResponse:
        """執行日常任務"""
        try:
            results = self.daily_automation.run_daily_tasks()
            
            return APIResponse.success({
                'task_results': results,
                'execution_time': datetime.now().isoformat(),
                'status': 'completed'
            })
            
        except Exception as e:
            self.logger.error(f"執行日常任務失敗: {e}")
            return APIResponse.error(f"任務執行失敗: {str(e)}")
    
    def optimize_strategy(self, lottery_type: str) -> APIResponse:
        """優化預測策略"""
        try:
            optimization_result = self.strategy_optimizer.optimize_for_lottery(
                lottery_type
            )
            
            return APIResponse.success({
                'lottery_type': lottery_type,
                'optimization_result': optimization_result,
                'optimization_time': datetime.now().isoformat()
            })
            
        except Exception as e:
            self.logger.error(f"策略優化失敗: {e}")
            return APIResponse.error(f"優化失敗: {str(e)}")


class ServiceContainer:
    """服務容器"""
    
    def __init__(self, config_manager=None):
        self._services = {}
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        self._initialized = False
    
    def register(self, service_name: str, service_instance: BaseService):
        """註冊服務"""
        self._services[service_name] = service_instance
        self.logger.info(f"註冊服務: {service_name}")
    
    def initialize(self) -> bool:
        """初始化所有服務"""
        if self._initialized:
            return True
        
        success_count = 0
        total_count = len(self._services)
        
        for name, service in self._services.items():
            try:
                if hasattr(service, 'initialize') and service.initialize():
                    success_count += 1
                    self.logger.info(f"服務初始化成功: {name}")
                else:
                    self.logger.warning(f"服務初始化失敗: {name}")
            except Exception as e:
                self.logger.error(f"服務初始化異常: {name}, 錯誤: {e}")
        
        self._initialized = True
        self.logger.info(f"服務容器初始化完成: {success_count}/{total_count} 服務成功初始化")
        
        return success_count > 0 or total_count == 0
    
    def get(self, service_name: str) -> Optional[BaseService]:
        """獲取服務"""
        return self._services.get(service_name)
    
    def get_prediction_service(self) -> Optional[PredictionService]:
        """獲取預測服務"""
        return self.get('prediction')
    
    def get_analysis_service(self) -> Optional[AnalysisService]:
        """獲取分析服務"""
        return self.get('analysis')
    
    def get_data_service(self) -> Optional[DataService]:
        """獲取數據服務"""
        return self.get('data')
    
    def get_automation_service(self) -> Optional[AutomationService]:
        """獲取自動化服務"""
        return self.get('automation')
    
    def list_services(self) -> List[str]:
        """列出所有服務"""
        return list(self._services.keys())


# 全局服務容器
service_container = None

def get_service_container(config_manager=None) -> ServiceContainer:
    """獲取服務容器"""
    global service_container
    if service_container is None:
        service_container = ServiceContainer(config_manager)
    return service_container