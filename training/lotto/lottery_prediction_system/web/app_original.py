"""
彩票預測系統 - Web界面
提供網頁查看預測結果、歷史記錄和分析報告
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for
import os
import sys
import json
import pandas as pd
from datetime import datetime
import logging

# 添加父目錄到路徑，以便導入其他模塊
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.db_manager import DBManager
from analysis.result_analyzer import PredictionAnalyzer
from utils.helpers import get_lottery_name
from core.config import Config
from prediction.integrated_predictor import IntegratedPredictor
from display.prediction_display import PredictionDisplay
from daily_automation import DailyAutomation
from strategy.strategy_optimizer import StrategyOptimizer
from analysis.number_pattern_analyzer import NumberPatternAnalyzer
from analysis.prediction_success_analyzer import PredictionSuccessAnalyzer
from prediction.enhanced_board_path_analyzer import EnhancedBoardPathAnalyzer

app = Flask(__name__)
app.secret_key = 'lottery_prediction_system_2025'
config = Config()

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/web_app_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('web_app')

# 初始化數據庫管理器和新組件
from config_manager import ConfigManager
config_manager = ConfigManager()
db = DBManager(config_manager)
analyzer = PredictionAnalyzer(db)
integrated_predictor = IntegratedPredictor(db)
display = PredictionDisplay()
automation = DailyAutomation()
optimizer = StrategyOptimizer(db)
pattern_analyzer = NumberPatternAnalyzer(db)
success_analyzer = PredictionSuccessAnalyzer(db)

# 初始化板路分析引擎（只初始化一次）
try:
    integrated_predictor.initialize_board_path_engines()
    logger.info("板路分析引擎初始化完成")
except Exception as e:
    logger.error(f"板路分析引擎初始化失敗: {str(e)}")

# 創建增強板路分析器的緩存
enhanced_board_analyzers = {}

def get_enhanced_board_analyzer(lottery_type):
    """獲取或創建增強板路分析器（避免重複創建）"""
    if lottery_type not in enhanced_board_analyzers:
        enhanced_board_analyzers[lottery_type] = EnhancedBoardPathAnalyzer(db, lottery_type)
        logger.info(f"創建 {lottery_type} 增強板路分析器")
    return enhanced_board_analyzers[lottery_type]

@app.route('/')
def index():
    """首頁"""
    return render_template('index.html')

@app.route('/predictions')
def predictions():
    """預測記錄頁面"""
    lottery_type = request.args.get('type', 'powercolor')
    limit = int(request.args.get('limit', 20))
    
    # 加載預測記錄
    df = db.load_prediction_records(lottery_type, limit)
    
    # 轉換為字典列表
    records = []
    if not df.empty:
        records = df.to_dict('records')
    
    return render_template(
        'predictions.html', 
        records=records, 
        lottery_type=lottery_type,
        lottery_name=get_lottery_name(lottery_type)
    )

@app.route('/analysis')
def analysis():
    """分析報告頁面"""
    lottery_type = request.args.get('type', 'powercolor')
    
    # 加載最新分析結果
    analysis_dir = config.get('paths.analysis_dir', 'analysis_results')
    analysis_files = [f for f in os.listdir(analysis_dir) 
                     if f.startswith('prediction_analysis_') and f.endswith('.json')]
    
    if not analysis_files:
        return render_template('analysis.html', analysis=None, lottery_type=lottery_type)
    
    # 獲取最新的分析文件
    latest_file = sorted(analysis_files)[-1]
    with open(os.path.join(analysis_dir, latest_file), 'r', encoding='utf-8') as f:
        analysis_data = json.load(f)
    
    return render_template(
        'analysis.html', 
        analysis=analysis_data, 
        lottery_type=lottery_type,
        lottery_name=get_lottery_name(lottery_type)
    )

@app.route('/api/latest_predictions/<lottery_type>')
def api_latest_predictions(lottery_type):
    """API: 獲取最新預測"""
    limit = int(request.args.get('limit', 5))
    
    try:
        df = db.load_prediction_records(lottery_type, limit)
        if df.empty:
            return jsonify({'error': 'No predictions found'})
        
        # 轉換為JSON格式
        records = df.to_dict('records')
        return jsonify({'predictions': records})
    except Exception as e:
        logger.error(f"API獲取預測時出錯: {str(e)}")
        return jsonify({'error': str(e)})

@app.route('/api/accuracy/<lottery_type>')
def api_accuracy(lottery_type):
    """API: 獲取預測準確度"""
    try:
        # 加載預測歷史
        prediction_df = analyzer.load_prediction_history(db, lottery_type)
        
        if prediction_df.empty:
            return jsonify({'error': 'No prediction history found'})
        
        # 分析預測準確度
        analysis_results = analyzer.analyze_prediction_accuracy(prediction_df)
        
        if not analysis_results:
            return jsonify({'error': 'Could not analyze prediction accuracy'})
        
        return jsonify(analysis_results)
    except Exception as e:
        logger.error(f"API獲取準確度時出錯: {str(e)}")
        return jsonify({'error': str(e)})

@app.route('/separated_prediction')
def separated_prediction():
    """分離式預測頁面"""
    return render_template('separated_prediction.html')

@app.route('/api/separated_predict', methods=['POST'])
def api_separated_predict():
    """API: 執行分離式預測"""
    try:
        data = request.get_json()
        lottery_type = data.get('lottery_type', 'powercolor')
        candidates_count = int(data.get('candidates_count', 5))
        min_confidence = float(data.get('min_confidence', 0.5))

        # 執行分離式預測
        results = integrated_predictor.predict_separated(
            lottery_type=lottery_type,
            candidates_count=candidates_count,
            min_confidence=min_confidence
        )

        if results:
            # 導入最佳預測整合器
            from prediction.best_prediction_integrator import BestPredictionIntegrator
            integrator = BestPredictionIntegrator(db)

            # 轉換結果為JSON格式
            json_results = {
                'period': results['period'],
                'lottery_type': results['lottery_type'],
                'prediction_time': results['prediction_time'],
                'ml_prediction': results['ml_prediction'].to_dict() if results['ml_prediction'] else None,
                'board_path_prediction': results['board_path_prediction'].to_dict() if results['board_path_prediction'] else None,
                'success_analysis': results['success_analysis']
            }

            # 生成最佳整合預測
            best_predictions = {}
            if results['ml_prediction']:
                best_ml = integrator.integrate_best_prediction(results['ml_prediction'], lottery_type)
                if best_ml:
                    best_predictions['ml_best'] = best_ml

            if results['board_path_prediction']:
                best_bp = integrator.integrate_best_prediction(results['board_path_prediction'], lottery_type)
                if best_bp:
                    best_predictions['board_path_best'] = best_bp

            json_results['best_predictions'] = best_predictions

            return jsonify({'success': True, 'results': json_results})
        else:
            return jsonify({'success': False, 'error': '預測失敗'})

    except Exception as e:
        logger.error(f"分離式預測API錯誤: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/daily_automation', methods=['POST'])
def api_daily_automation():
    """API: 執行每日自動化任務"""
    try:
        data = request.get_json()
        task_type = data.get('task_type', 'full')

        if task_type == 'full':
            automation.run_daily_tasks()
            message = "完整每日任務執行完成"
        elif task_type == 'update':
            automation.update_lottery_results()
            message = "開獎結果更新完成"
        elif task_type == 'analysis':
            automation.analyze_prediction_accuracy()
            message = "預測準確度分析完成"
        elif task_type == 'prediction':
            automation.generate_daily_predictions()
            message = "每日預測生成完成"
        else:
            return jsonify({'success': False, 'error': '無效的任務類型'})

        return jsonify({'success': True, 'message': message})

    except Exception as e:
        logger.error(f"每日自動化API錯誤: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/strategy_optimize', methods=['POST'])
def api_strategy_optimize():
    """API: 策略優化"""
    try:
        data = request.get_json()
        lottery_type = data.get('lottery_type', 'powercolor')

        # 獲取成功分析
        success_analysis = integrated_predictor.success_analyzer.analyze_successful_predictions(lottery_type)

        if success_analysis:
            # 執行策略優化
            optimized_strategy = optimizer.optimize_strategy(lottery_type, success_analysis)
            return jsonify({
                'success': True,
                'strategy': optimized_strategy,
                'success_analysis': success_analysis
            })
        else:
            return jsonify({'success': False, 'error': '無足夠數據進行策略優化'})

    except Exception as e:
        logger.error(f"策略優化API錯誤: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/dashboard')
def dashboard():
    """儀表板頁面"""
    return render_template('dashboard.html')

@app.route('/api/dashboard_data')
def api_dashboard_data():
    """API: 獲取儀表板數據"""
    try:
        dashboard_data = {}

        # 獲取各彩票類型的最新預測
        for lottery_type in ['powercolor', 'lotto649', 'dailycash']:
            try:
                df = db.load_prediction_records(lottery_type, limit=1)
                if not df.empty:
                    latest = df.iloc[0].to_dict()
                    dashboard_data[f'{lottery_type}_latest'] = latest
                else:
                    dashboard_data[f'{lottery_type}_latest'] = None
            except:
                dashboard_data[f'{lottery_type}_latest'] = None

        # 獲取系統狀態
        dashboard_data['system_status'] = {
            'last_update': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_predictions': sum([
                len(db.load_prediction_records(lt, limit=100))
                for lt in ['powercolor', 'lotto649', 'dailycash']
            ])
        }

        return jsonify(dashboard_data)

    except Exception as e:
        logger.error(f"儀表板數據API錯誤: {str(e)}")
        return jsonify({'error': str(e)})

@app.route('/number_analysis')
def number_analysis():
    """號碼分析頁面"""
    return render_template('number_analysis.html')

@app.route('/api/analyze_numbers', methods=['POST'])
def api_analyze_numbers():
    """API: 分析號碼組合"""
    try:
        data = request.get_json()
        lottery_type = data.get('lottery_type', 'powercolor')
        numbers = data.get('numbers', [])

        if not numbers:
            return jsonify({'success': False, 'error': '請提供要分析的號碼'})

        # 驗證號碼
        try:
            numbers = [int(num) for num in numbers]
        except ValueError:
            return jsonify({'success': False, 'error': '號碼格式不正確'})

        # 執行分析
        analysis_result = pattern_analyzer.analyze_number_appearance_reasons(
            lottery_type, numbers
        )

        if "error" in analysis_result:
            return jsonify({'success': False, 'error': analysis_result["error"]})

        return jsonify({'success': True, 'analysis': analysis_result})

    except Exception as e:
        logger.error(f"號碼分析API錯誤: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/analyze_prediction_result', methods=['POST'])
def api_analyze_prediction_result():
    """API: 分析預測結果中的號碼"""
    try:
        data = request.get_json()
        lottery_type = data.get('lottery_type', 'powercolor')
        prediction_id = data.get('prediction_id')
        candidate_index = data.get('candidate_index', 0)

        if not prediction_id:
            return jsonify({'success': False, 'error': '請提供預測ID'})

        # 從資料庫載入預測結果
        # 這裡需要實現從資料庫載入特定預測結果的邏輯
        # 暫時使用示例數據

        # 示例：分析 1,2,3,4,5,6 組合
        if lottery_type == 'powercolor':
            numbers = [1, 2, 3, 4, 5, 6]
        else:
            numbers = [1, 2, 3, 4, 5]

        analysis_result = pattern_analyzer.analyze_number_appearance_reasons(
            lottery_type, numbers
        )

        return jsonify({'success': True, 'analysis': analysis_result})

    except Exception as e:
        logger.error(f"預測結果分析API錯誤: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/success_analysis/<lottery_type>')
def api_success_analysis(lottery_type):
    """API: 獲取成功預測分析"""
    try:
        min_match = int(request.args.get('min_match', 3))
        days_back = int(request.args.get('days_back', 90))

        analysis_result = success_analyzer.analyze_successful_predictions(
            lottery_type, min_match, days_back
        )

        if "error" in analysis_result:
            return jsonify({'success': False, 'error': analysis_result["error"]})

        return jsonify({'success': True, 'analysis': analysis_result})

    except Exception as e:
        logger.error(f"成功分析API錯誤: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/enhanced_board_analysis', methods=['POST'])
def api_enhanced_board_analysis():
    """API: 增強板路分析"""
    try:
        data = request.get_json()
        lottery_type = data.get('lottery_type', 'powercolor')
        prediction_mode = data.get('predictionMode', 'next_period')
        target_period = data.get('targetPeriod')

        # 載入歷史數據
        df = db.load_lottery_data(lottery_type)
        if df.empty:
            return jsonify({'success': False, 'error': '無歷史數據'})

        # 根據預測模式處理數據
        if prediction_mode == 'simulation' and target_period:
            # 模擬校準模式：只使用目標期數之前的數據
            df = df[df['Period'] < int(target_period)]
            if df.empty:
                return jsonify({'success': False, 'error': f'期數 {target_period} 之前無足夠歷史數據'})
            logger.info(f"模擬校準模式：使用期數 {target_period} 之前的 {len(df)} 筆數據")
        else:
            # 下期預測模式：使用所有歷史數據
            logger.info(f"下期預測模式：使用全部 {len(df)} 筆歷史數據")

        # 執行增強板路分析
        enhanced_analyzer = get_enhanced_board_analyzer(lottery_type)
        analysis_result = enhanced_analyzer.analyze_mathematical_relationships(df)
        
        # 添加模式信息到結果中
        analysis_result['prediction_mode'] = prediction_mode
        if target_period:
            analysis_result['target_period'] = target_period

        return jsonify({'success': True, 'analysis': analysis_result})

    except Exception as e:
        logger.error(f"增強板路分析API錯誤: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/predict_with_enhanced_analysis', methods=['POST'])
def api_predict_with_enhanced_analysis():
    """API: 基於增強分析進行預測"""
    try:
        data = request.get_json()
        lottery_type = data.get('lottery_type', 'powercolor')
        candidates_count = int(data.get('candidates_count', 5))
        prediction_mode = data.get('predictionMode', 'next_period')
        target_period = data.get('targetPeriod')

        # 載入歷史數據
        df = db.load_lottery_data(lottery_type)
        if df.empty:
            return jsonify({'success': False, 'error': '無歷史數據'})

        # 根據預測模式處理數據
        if prediction_mode == 'simulation' and target_period:
            # 模擬校準模式：只使用目標期數之前的數據
            df = df[df['Period'] < int(target_period)]
            if df.empty:
                return jsonify({'success': False, 'error': f'期數 {target_period} 之前無足夠歷史數據'})
            logger.info(f"模擬校準模式：使用期數 {target_period} 之前的 {len(df)} 筆數據進行預測")
        else:
            # 下期預測模式：使用所有歷史數據
            logger.info(f"下期預測模式：使用全部 {len(df)} 筆歷史數據進行預測")

        # 執行增強預測
        enhanced_analyzer = get_enhanced_board_analyzer(lottery_type)
        prediction_result = enhanced_analyzer.predict_with_enhanced_analysis(df, candidates_count)

        # 添加模式信息到結果中
        if prediction_result:
            prediction_result['prediction_mode'] = prediction_mode
            if target_period:
                prediction_result['target_period'] = target_period

        # 如果預測結果有候選列表，生成最佳整合預測
        if prediction_result and 'candidates' in prediction_result:
            from prediction.best_prediction_integrator import BestPredictionIntegrator
            integrator = BestPredictionIntegrator(db)

            # 轉換候選格式以適配整合器
            candidates = prediction_result['candidates']
            best_prediction = integrator.integrate_best_prediction(candidates, lottery_type)

            if best_prediction:
                prediction_result['best_prediction'] = best_prediction

        return jsonify({'success': True, 'prediction': prediction_result})

    except Exception as e:
        logger.error(f"增強預測API錯誤: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/comprehensive_analysis')
def comprehensive_analysis():
    """綜合分析頁面"""
    return render_template('comprehensive_analysis.html')

@app.route('/enhanced_prediction')
def enhanced_prediction():
    """智能預測頁面"""
    return render_template('enhanced_prediction.html')

@app.route('/api/comprehensive_analysis/<lottery_type>')
def api_comprehensive_analysis(lottery_type):
    """API: 綜合分析"""
    try:
        # 1. 基本預測準確度分析
        prediction_df = analyzer.load_prediction_history(db, lottery_type)
        accuracy_analysis = analyzer.analyze_prediction_accuracy(prediction_df) if not prediction_df.empty else {}

        # 2. 成功預測分析
        success_analysis = success_analyzer.analyze_successful_predictions(lottery_type)

        # 3. 載入歷史數據進行模式分析
        df = db.load_lottery_data(lottery_type)
        pattern_analysis = {}
        enhanced_analysis = {}

        if not df.empty:
            # 最近一期號碼模式分析
            latest_row = df.iloc[-1]
            latest_numbers = []

            # 提取最新號碼
            if lottery_type == 'powercolor':
                for i in range(1, 7):
                    col = f'Anumber{i}'
                    if col in latest_row and pd.notna(latest_row[col]):
                        latest_numbers.append(int(latest_row[col]))
            elif lottery_type == 'lotto649':
                for i in range(1, 7):
                    col = f'Anumber{i}'
                    if col in latest_row and pd.notna(latest_row[col]):
                        latest_numbers.append(int(latest_row[col]))
            elif lottery_type == 'dailycash':
                for i in range(1, 6):
                    col = f'Anumber{i}'
                    if col in latest_row and pd.notna(latest_row[col]):
                        latest_numbers.append(int(latest_row[col]))

            if latest_numbers:
                pattern_analysis = pattern_analyzer.analyze_number_appearance_reasons(
                    lottery_type, latest_numbers
                )

            # 增強板路分析
            enhanced_analyzer = get_enhanced_board_analyzer(lottery_type)
            enhanced_analysis = enhanced_analyzer.analyze_mathematical_relationships(df)

        # 4. 整合所有分析結果
        comprehensive_result = {
            'lottery_type': lottery_type,
            'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'accuracy_analysis': accuracy_analysis,
            'success_analysis': success_analysis,
            'pattern_analysis': pattern_analysis,
            'enhanced_analysis': enhanced_analysis,
            'summary': {
                'total_predictions': len(prediction_df) if not prediction_df.empty else 0,
                'successful_predictions': len(success_analysis.get('successful_predictions', [])) if success_analysis else 0,
                'success_rate': success_analysis.get('success_rate', 0) if success_analysis else 0,
                'latest_period': latest_row['Period'] if not df.empty else 'N/A'
            }
        }

        return jsonify({'success': True, 'analysis': comprehensive_result})

    except Exception as e:
        logger.error(f"綜合分析API錯誤: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/periods/<lottery_type>')
def api_get_periods(lottery_type):
    """API: 獲取期數列表"""
    try:
        periods = db.get_periods_list(lottery_type)
        return jsonify({'success': True, 'periods': periods})
    except Exception as e:
        logger.error(f"獲取期數列表API錯誤: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/actual_results/<lottery_type>/<period>')
def api_get_actual_results(lottery_type, period):
    """API: 獲取指定期數的實際開獎結果"""
    try:
        # 獲取指定期數的開獎數據
        period_data = db.get_period_data(lottery_type, int(period))
        
        if period_data is None or period_data.empty:
            return jsonify({'success': False, 'error': f'找不到期數 {period} 的開獎結果'})
        
        # 提取開獎號碼
        result_data = period_data.iloc[0]
        actual_numbers = []
        
        if lottery_type == 'powercolor':
            # 威力彩：6個號碼 + 1個威力球
            for i in range(1, 7):
                col = f'Anumber{i}'
                if col in result_data and pd.notna(result_data[col]):
                    actual_numbers.append(int(result_data[col]))
            
            power_ball = None
            if 'PowerBall' in result_data and pd.notna(result_data['PowerBall']):
                power_ball = int(result_data['PowerBall'])
                
            return jsonify({
                'success': True,
                'period': period,
                'lottery_type': lottery_type,
                'numbers': actual_numbers,
                'power_ball': power_ball,
                'draw_date': result_data.get('DrawDate', '').strftime('%Y-%m-%d') if 'DrawDate' in result_data else None
            })
            
        elif lottery_type in ['lotto649', 'dailycash']:
            # 大樂透649 或 今彩539：5-6個號碼
            num_count = 6 if lottery_type == 'lotto649' else 5
            for i in range(1, num_count + 1):
                col = f'Anumber{i}'
                if col in result_data and pd.notna(result_data[col]):
                    actual_numbers.append(int(result_data[col]))
                    
            return jsonify({
                'success': True,
                'period': period,
                'lottery_type': lottery_type,
                'numbers': actual_numbers,
                'draw_date': result_data.get('DrawDate', '').strftime('%Y-%m-%d') if 'DrawDate' in result_data else None
            })
        
        else:
            return jsonify({'success': False, 'error': f'不支援的彩票類型: {lottery_type}'})
            
    except Exception as e:
        logger.error(f"獲取實際開獎結果API錯誤: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5003)