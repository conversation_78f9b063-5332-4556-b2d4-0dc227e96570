#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模組
提供統一的配置管理和環境變數處理
"""

import os
import json
import logging
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class DatabaseConfig:
    """資料庫配置"""
    host: str = 'localhost'
    port: int = 3306
    username: str = ''
    password: str = ''
    database: str = 'lottery_prediction'
    charset: str = 'utf8mb4'
    pool_size: int = 10
    pool_recycle: int = 3600
    pool_pre_ping: bool = True


@dataclass
class RedisConfig:
    """Redis配置"""
    host: str = 'localhost'
    port: int = 6379
    password: str = ''
    db: int = 0
    decode_responses: bool = True
    socket_timeout: int = 5
    connection_pool_max_connections: int = 50


@dataclass
class LoggingConfig:
    """日誌配置"""
    level: str = 'INFO'
    format: str = '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    file_max_bytes: int = 10 * 1024 * 1024  # 10MB
    file_backup_count: int = 5
    console_output: bool = True
    file_output: bool = True
    log_dir: str = 'logs'


@dataclass
class APIConfig:
    """API配置"""
    rate_limit_per_minute: int = 100
    rate_limit_per_hour: int = 1000
    max_request_size: int = 16 * 1024 * 1024  # 16MB
    timeout_seconds: int = 30
    cors_enabled: bool = True
    cors_origins: list = field(default_factory=lambda: ['*'])


@dataclass
class PredictionConfig:
    """預測配置"""
    default_candidates_count: int = 5
    max_candidates_count: int = 20
    min_confidence_threshold: float = 0.1
    max_confidence_threshold: float = 1.0
    default_min_confidence: float = 0.5
    cache_predictions: bool = True
    cache_ttl_seconds: int = 3600


@dataclass
class SystemConfig:
    """系統配置"""
    debug: bool = False
    testing: bool = False
    secret_key: str = 'lottery_prediction_system_2025'
    timezone: str = 'Asia/Taipei'
    max_workers: int = 4
    temp_dir: str = 'temp'
    backup_dir: str = 'backup'
    export_dir: str = 'exports'


class ConfigManager:
    """統一配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None, env_prefix: str = 'LOTTERY_'):
        self.env_prefix = env_prefix
        self.config_file = config_file or 'config.json'
        self._config_cache = {}
        
        # 初始化各模組配置
        self.database = DatabaseConfig()
        self.redis = RedisConfig()
        self.logging = LoggingConfig()
        self.api = APIConfig()
        self.prediction = PredictionConfig()
        self.system = SystemConfig()
        
        self._load_config()
        self._load_env_variables()
        
        # 設置日誌
        self._setup_logging()
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("配置管理器初始化完成")
    
    def _load_config(self):
        """從配置文件載入配置"""
        config_path = Path(self.config_file)
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 更新各模組配置
                self._update_config_from_dict(config_data)
                
            except Exception as e:
                print(f"載入配置文件失敗: {e}")
    
    def _load_env_variables(self):
        """從環境變數載入配置"""
        # 資料庫配置
        self.database.host = os.getenv(f'{self.env_prefix}DB_HOST', self.database.host)
        self.database.port = int(os.getenv(f'{self.env_prefix}DB_PORT', self.database.port))
        self.database.username = os.getenv(f'{self.env_prefix}DB_USERNAME', self.database.username)
        self.database.password = os.getenv(f'{self.env_prefix}DB_PASSWORD', self.database.password)
        self.database.database = os.getenv(f'{self.env_prefix}DB_DATABASE', self.database.database)
        
        # Redis配置
        self.redis.host = os.getenv(f'{self.env_prefix}REDIS_HOST', self.redis.host)
        self.redis.port = int(os.getenv(f'{self.env_prefix}REDIS_PORT', self.redis.port))
        self.redis.password = os.getenv(f'{self.env_prefix}REDIS_PASSWORD', self.redis.password)
        
        # 系統配置
        self.system.debug = os.getenv(f'{self.env_prefix}DEBUG', 'false').lower() == 'true'
        self.system.secret_key = os.getenv(f'{self.env_prefix}SECRET_KEY', self.system.secret_key)
        
        # 日誌配置
        self.logging.level = os.getenv(f'{self.env_prefix}LOG_LEVEL', self.logging.level)
    
    def _update_config_from_dict(self, config_data: Dict[str, Any]):
        """從字典更新配置"""
        for section, values in config_data.items():
            if hasattr(self, section) and isinstance(values, dict):
                config_obj = getattr(self, section)
                for key, value in values.items():
                    if hasattr(config_obj, key):
                        setattr(config_obj, key, value)
    
    def _setup_logging(self):
        """設置日誌配置"""
        # 確保日誌目錄存在
        os.makedirs(self.logging.log_dir, exist_ok=True)
        
        # 配置日誌格式
        formatter = logging.Formatter(self.logging.format)
        
        # 清除現有處理器
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        handlers = []
        
        # 文件處理器
        if self.logging.file_output:
            from logging.handlers import RotatingFileHandler
            file_handler = RotatingFileHandler(
                os.path.join(self.logging.log_dir, 'app.log'),
                maxBytes=self.logging.file_max_bytes,
                backupCount=self.logging.file_backup_count,
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            handlers.append(file_handler)
        
        # 控制台處理器
        if self.logging.console_output:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            handlers.append(console_handler)
        
        # 配置根日誌器
        logging.basicConfig(
            level=getattr(logging, self.logging.level.upper()),
            handlers=handlers,
            force=True
        )
    
    def get(self, key: str, default: Any = None) -> Any:
        """獲取配置值，支持點號分隔的路徑"""
        if key in self._config_cache:
            return self._config_cache[key]
        
        parts = key.split('.')
        value = self
        
        try:
            for part in parts:
                value = getattr(value, part)
            
            self._config_cache[key] = value
            return value
        except AttributeError:
            return default
    
    def set(self, key: str, value: Any):
        """設置配置值"""
        parts = key.split('.')
        obj = self
        
        # 導航到父對象
        for part in parts[:-1]:
            obj = getattr(obj, part)
        
        # 設置值
        setattr(obj, parts[-1], value)
        
        # 清除緩存
        if key in self._config_cache:
            del self._config_cache[key]
    
    def save_config(self, file_path: Optional[str] = None):
        """保存配置到文件"""
        file_path = file_path or self.config_file
        
        config_data = {
            'database': self.database.__dict__,
            'redis': self.redis.__dict__,
            'logging': self.logging.__dict__,
            'api': self.api.__dict__,
            'prediction': self.prediction.__dict__,
            'system': self.system.__dict__
        }
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置已保存到: {file_path}")
        except Exception as e:
            self.logger.error(f"保存配置失敗: {e}")
            raise
    
    def validate_config(self) -> Dict[str, list]:
        """驗證配置的有效性"""
        errors = {}
        
        # 驗證資料庫配置
        db_errors = []
        if not self.database.host:
            db_errors.append("資料庫主機不能為空")
        if not (1 <= self.database.port <= 65535):
            db_errors.append("資料庫端口必須在1-65535範圍內")
        if not self.database.database:
            db_errors.append("資料庫名稱不能為空")
        
        if db_errors:
            errors['database'] = db_errors
        
        # 驗證Redis配置
        redis_errors = []
        if not self.redis.host:
            redis_errors.append("Redis主機不能為空")
        if not (1 <= self.redis.port <= 65535):
            redis_errors.append("Redis端口必須在1-65535範圍內")
        
        if redis_errors:
            errors['redis'] = redis_errors
        
        # 驗證預測配置
        pred_errors = []
        if not (1 <= self.prediction.default_candidates_count <= self.prediction.max_candidates_count):
            pred_errors.append("默認候選數量必須在有效範圍內")
        if not (self.prediction.min_confidence_threshold <= self.prediction.default_min_confidence <= self.prediction.max_confidence_threshold):
            pred_errors.append("默認最小信心度必須在有效範圍內")
        
        if pred_errors:
            errors['prediction'] = pred_errors
        
        return errors
    
    def get_database_url(self) -> str:
        """獲取資料庫連接URL"""
        if self.database.password:
            return f"mysql+pymysql://{self.database.username}:{self.database.password}@{self.database.host}:{self.database.port}/{self.database.database}?charset={self.database.charset}"
        else:
            return f"mysql+pymysql://{self.database.username}@{self.database.host}:{self.database.port}/{self.database.database}?charset={self.database.charset}"
    
    def get_redis_url(self) -> str:
        """獲取Redis連接URL"""
        if self.redis.password:
            return f"redis://:{self.redis.password}@{self.redis.host}:{self.redis.port}/{self.redis.db}"
        else:
            return f"redis://{self.redis.host}:{self.redis.port}/{self.redis.db}"
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"ConfigManager(debug={self.system.debug}, db_host={self.database.host})"


# 全局配置實例
config_manager = ConfigManager()