#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試模組
提供單元測試和集成測試
"""

import unittest
import tempfile
import os
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# 導入測試目標
from config_manager import ConfigManager
from cache_manager import MemoryCache, CacheManager, create_cache_manager
from middleware import APIResponse, RateLimiter, PerformanceMonitor, ErrorHandler
from services import PredictionService, AnalysisService, DataService, ServiceContainer
from exceptions import (
    ValidationError, BusinessLogicError, DatabaseError, 
    LotteryTypeError, PredictionError, get_exception_handler
)


class TestConfigManager(unittest.TestCase):
    """配置管理器測試"""
    
    def setUp(self):
        self.config_manager = ConfigManager()
    
    def test_default_config(self):
        """測試默認配置"""
        self.assertEqual(self.config_manager.get('app.host'), '0.0.0.0')
        self.assertEqual(self.config_manager.get('app.port'), 5003)
        self.assertFalse(self.config_manager.get('app.debug'))
    
    def test_set_and_get_config(self):
        """測試設置和獲取配置"""
        self.config_manager.set('test.key', 'test_value')
        self.assertEqual(self.config_manager.get('test.key'), 'test_value')
    
    def test_nested_config(self):
        """測試嵌套配置"""
        self.config_manager.set('database.host', 'localhost')
        self.config_manager.set('database.port', 5432)
        
        self.assertEqual(self.config_manager.get('database.host'), 'localhost')
        self.assertEqual(self.config_manager.get('database.port'), 5432)
    
    def test_config_validation(self):
        """測試配置驗證"""
        # 測試有效配置
        valid_config = {
            'app': {'host': '127.0.0.1', 'port': 8000},
            'database': {'path': '/tmp/test.db'}
        }
        self.assertTrue(self.config_manager.validate_config(valid_config))
        
        # 測試無效配置
        invalid_config = {
            'app': {'port': 'invalid_port'}  # 端口應該是數字
        }
        self.assertFalse(self.config_manager.validate_config(invalid_config))


class TestCacheManager(unittest.TestCase):
    """緩存管理器測試"""
    
    def setUp(self):
        self.memory_cache = MemoryCache(max_size=10)
        self.cache_manager = CacheManager(self.memory_cache)
    
    def test_memory_cache_basic_operations(self):
        """測試內存緩存基本操作"""
        # 測試設置和獲取
        self.assertTrue(self.memory_cache.set('key1', 'value1'))
        self.assertEqual(self.memory_cache.get('key1'), 'value1')
        
        # 測試不存在的key
        self.assertIsNone(self.memory_cache.get('nonexistent'))
        
        # 測試刪除
        self.assertTrue(self.memory_cache.delete('key1'))
        self.assertIsNone(self.memory_cache.get('key1'))
    
    def test_memory_cache_ttl(self):
        """測試TTL功能"""
        import time
        
        # 設置1秒TTL
        self.memory_cache.set('ttl_key', 'ttl_value', ttl=1)
        self.assertEqual(self.memory_cache.get('ttl_key'), 'ttl_value')
        
        # 等待過期
        time.sleep(1.1)
        self.assertIsNone(self.memory_cache.get('ttl_key'))
    
    def test_memory_cache_lru_eviction(self):
        """測試LRU淘汰策略"""
        # 填滿緩存
        for i in range(10):
            self.memory_cache.set(f'key{i}', f'value{i}')
        
        # 添加新項目，應該淘汰最舊的
        self.memory_cache.set('new_key', 'new_value')
        
        # 檢查緩存大小
        self.assertEqual(len(self.memory_cache.cache), 10)
        
        # 最舊的key應該被淘汰
        self.assertIsNone(self.memory_cache.get('key0'))
        self.assertEqual(self.memory_cache.get('new_key'), 'new_value')
    
    def test_cache_manager_get_or_set(self):
        """測試get_or_set方法"""
        def expensive_function():
            return 'computed_value'
        
        # 第一次調用應該執行函數
        result1 = self.cache_manager.get_or_set('compute_key', expensive_function)
        self.assertEqual(result1, 'computed_value')
        
        # 第二次調用應該從緩存獲取
        result2 = self.cache_manager.get_or_set('compute_key', lambda: 'new_value')
        self.assertEqual(result2, 'computed_value')  # 應該是緩存的值
    
    def test_cache_stats(self):
        """測試緩存統計"""
        # 執行一些操作
        self.memory_cache.set('key1', 'value1')
        self.memory_cache.get('key1')  # hit
        self.memory_cache.get('nonexistent')  # miss
        
        stats = self.memory_cache.get_stats()
        self.assertEqual(stats['hits'], 1)
        self.assertEqual(stats['misses'], 1)
        self.assertEqual(stats['total_keys'], 1)


class TestAPIResponse(unittest.TestCase):
    """API響應測試"""
    
    def test_success_response(self):
        """測試成功響應"""
        data = {'key': 'value'}
        response = APIResponse.success(data, 'Success message')
        
        self.assertTrue(response.success)
        self.assertEqual(response.data, data)
        self.assertEqual(response.message, 'Success message')
        self.assertIsNone(response.error_code)
    
    def test_error_response(self):
        """測試錯誤響應"""
        response = APIResponse.error('Error message', 'ERROR_CODE')
        
        self.assertFalse(response.success)
        self.assertEqual(response.message, 'Error message')
        self.assertEqual(response.error_code, 'ERROR_CODE')
    
    def test_response_to_dict(self):
        """測試響應轉字典"""
        response = APIResponse.success({'test': 'data'})
        response_dict = response.to_dict()
        
        self.assertIn('success', response_dict)
        self.assertIn('data', response_dict)
        self.assertIn('timestamp', response_dict)
        self.assertTrue(response_dict['success'])


class TestRateLimiter(unittest.TestCase):
    """速率限制器測試"""
    
    def setUp(self):
        self.rate_limiter = RateLimiter()
    
    def test_rate_limiting(self):
        """測試速率限制"""
        client_id = 'test_client'
        
        # 在限制內的請求應該通過
        for i in range(5):
            self.assertTrue(self.rate_limiter.is_allowed(client_id, limit=10, window=60))
        
        # 超過限制的請求應該被拒絕
        for i in range(10):
            self.rate_limiter.is_allowed(client_id, limit=10, window=60)
        
        self.assertFalse(self.rate_limiter.is_allowed(client_id, limit=10, window=60))
    
    def test_different_clients(self):
        """測試不同客戶端的獨立限制"""
        client1 = 'client1'
        client2 = 'client2'
        
        # 客戶端1達到限制
        for i in range(10):
            self.rate_limiter.is_allowed(client1, limit=10, window=60)
        
        # 客戶端1被限制，但客戶端2不受影響
        self.assertFalse(self.rate_limiter.is_allowed(client1, limit=10, window=60))
        self.assertTrue(self.rate_limiter.is_allowed(client2, limit=10, window=60))


class TestPerformanceMonitor(unittest.TestCase):
    """性能監控器測試"""
    
    def setUp(self):
        self.monitor = PerformanceMonitor()
    
    def test_record_request(self):
        """測試記錄請求"""
        import time
        
        start_time = time.time()
        time.sleep(0.01)  # 模擬處理時間
        end_time = time.time()
        
        self.monitor.record_request('/test', 'GET', start_time, end_time, 200)
        
        stats = self.monitor.get_stats()
        self.assertEqual(stats['total_requests'], 1)
        self.assertGreater(stats['avg_response_time'], 0)
    
    def test_multiple_requests(self):
        """測試多個請求的統計"""
        import time
        
        # 記錄多個請求
        for i in range(5):
            start_time = time.time()
            time.sleep(0.001)
            end_time = time.time()
            self.monitor.record_request(f'/test{i}', 'GET', start_time, end_time, 200)
        
        stats = self.monitor.get_stats()
        self.assertEqual(stats['total_requests'], 5)
        self.assertIn('endpoints', stats)


class TestExceptions(unittest.TestCase):
    """異常處理測試"""
    
    def test_validation_error(self):
        """測試驗證錯誤"""
        error = ValidationError("Invalid value", field="test_field", value="invalid")
        
        self.assertEqual(error.error_detail.message, "Invalid value")
        self.assertEqual(error.error_detail.details['field'], "test_field")
        self.assertEqual(error.error_detail.details['value'], "invalid")
    
    def test_lottery_type_error(self):
        """測試彩票類型錯誤"""
        valid_types = ['lotto649', 'dailycash', 'powercolor']
        error = LotteryTypeError('invalid_type', valid_types)
        
        self.assertIn('invalid_type', error.error_detail.message)
        self.assertEqual(error.error_detail.details['lottery_type'], 'invalid_type')
        self.assertEqual(error.error_detail.details['valid_types'], valid_types)
    
    def test_prediction_error(self):
        """測試預測錯誤"""
        error = PredictionError("Prediction failed", lottery_type="lotto649", method="neural_network")
        
        self.assertEqual(error.error_detail.message, "Prediction failed")
        self.assertEqual(error.error_detail.details['lottery_type'], "lotto649")
        self.assertEqual(error.error_detail.details['method'], "neural_network")
    
    def test_exception_handler(self):
        """測試異常處理器"""
        handler = get_exception_handler()
        
        # 測試自定義異常
        custom_error = ValidationError("Test error")
        result = handler.handle_exception(custom_error)
        
        self.assertIn('code', result)
        self.assertIn('message', result)
        self.assertEqual(result['message'], "Test error")
        
        # 測試標準異常
        std_error = ValueError("Standard error")
        result = handler.handle_exception(std_error)
        
        self.assertIn('code', result)
        self.assertIn('message', result)


class TestServices(unittest.TestCase):
    """服務層測試"""
    
    def setUp(self):
        # 創建模擬對象
        self.mock_db_manager = Mock()
        self.mock_cache_manager = Mock()
        self.mock_predictor = Mock()
        self.mock_integrator = Mock()
        
        # 創建服務實例
        self.prediction_service = PredictionService(
            self.mock_db_manager,
            self.mock_predictor,
            self.mock_integrator,
            self.mock_cache_manager
        )
    
    def test_prediction_service_get_latest(self):
        """測試獲取最新預測"""
        # 設置模擬返回值
        mock_predictions = [
            {'id': 1, 'period': '2024001', 'predicted_numbers': [1, 2, 3, 4, 5, 6]}
        ]
        self.mock_db_manager.get_latest_predictions.return_value = mock_predictions
        
        # 調用服務方法
        result = self.prediction_service.get_latest_predictions('lotto649')
        
        # 驗證結果
        self.assertTrue(result.success)
        self.assertIn('predictions', result.data)
        self.assertEqual(len(result.data['predictions']), 1)
    
    def test_service_container(self):
        """測試服務容器"""
        container = ServiceContainer()
        
        # 註冊服務
        mock_service = Mock()
        container.register('test_service', mock_service)
        
        # 獲取服務
        retrieved_service = container.get('test_service')
        self.assertEqual(retrieved_service, mock_service)
        
        # 獲取不存在的服務
        nonexistent_service = container.get('nonexistent')
        self.assertIsNone(nonexistent_service)
        
        # 列出服務
        services = container.list_services()
        self.assertIn('test_service', services)


class TestIntegration(unittest.TestCase):
    """集成測試"""
    
    def setUp(self):
        # 創建臨時數據庫
        self.temp_db = tempfile.NamedTemporaryFile(delete=False)
        self.temp_db.close()
        
        # 創建測試配置
        self.test_config = {
            'database': {'path': self.temp_db.name},
            'app': {'debug': True, 'port': 5004},
            'cache': {'enabled': True, 'default_ttl': 300}
        }
    
    def tearDown(self):
        # 清理臨時文件
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
    
    def test_cache_integration(self):
        """測試緩存集成"""
        # 創建緩存管理器
        cache_manager = create_cache_manager(memory_max_size=100)
        
        # 測試基本操作
        self.assertTrue(cache_manager.set('test_key', {'data': 'test'}))
        result = cache_manager.get('test_key')
        self.assertEqual(result['data'], 'test')
        
        # 測試get_or_set
        def compute_value():
            return {'computed': True}
        
        result = cache_manager.get_or_set('compute_key', compute_value)
        self.assertTrue(result['computed'])
        
        # 再次調用應該返回緩存值
        result2 = cache_manager.get_or_set('compute_key', lambda: {'computed': False})
        self.assertTrue(result2['computed'])  # 應該是緩存的值
    
    @patch('services.DBManager')
    def test_service_integration(self, mock_db_class):
        """測試服務集成"""
        # 設置模擬
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        
        # 創建服務容器
        container = ServiceContainer()
        
        # 創建模擬服務
        mock_prediction_service = Mock()
        mock_analysis_service = Mock()
        
        container.register('prediction', mock_prediction_service)
        container.register('analysis', mock_analysis_service)
        
        # 測試服務獲取
        pred_service = container.get_prediction_service()
        analysis_service = container.get_analysis_service()
        
        self.assertEqual(pred_service, mock_prediction_service)
        self.assertEqual(analysis_service, mock_analysis_service)


if __name__ == '__main__':
    # 運行測試
    unittest.main(verbosity=2)