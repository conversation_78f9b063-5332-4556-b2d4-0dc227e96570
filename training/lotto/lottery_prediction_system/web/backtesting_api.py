#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回測驗證 API - 提供 Web 接口來執行回測驗證
"""

import os
import sys
import logging
from flask import Blueprint, jsonify, request

# 添加父目錄到路徑，以便導入回測模組
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from backtesting_validator import BacktestingValidator, run_comprehensive_backtest
except ImportError as e:
    logging.error(f"無法導入回測模組: {e}")
    BacktestingValidator = None
    run_comprehensive_backtest = None

logger = logging.getLogger('backtesting_api')

# 創建 Blueprint
backtesting_bp = Blueprint('backtesting', __name__, url_prefix='/api/backtesting')

@backtesting_bp.route('/method1/<lottery_type>', methods=['POST'])
def run_method1_backtest(lottery_type):
    """執行方法一回測 - 逐期回測"""
    try:
        if not BacktestingValidator:
            return jsonify({
                'success': False,
                'error': '回測模組未正確載入'
            }), 500
        
        data = request.get_json() or {}
        start_period = data.get('start_period', '114000066')
        num_periods = data.get('num_periods', 10)
        
        # 驗證彩票類型
        valid_types = ['powercolor', 'lotto649', 'dailycash']
        if lottery_type not in valid_types:
            return jsonify({
                'success': False,
                'error': f'不支援的彩票類型: {lottery_type}'
            }), 400
        
        # 初始化驗證器
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'data', 'lottery_data.db')
        validator = BacktestingValidator(db_path)
        
        # 執行方法一回測
        logger.info(f"執行方法一回測: {lottery_type}, 起始期號: {start_period}, 期數: {num_periods}")
        result = validator.run_method_1_backtest(lottery_type, start_period, num_periods)
        
        if result.get('success'):
            return jsonify({
                'success': True,
                'data': result,
                'message': '方法一回測完成'
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', '回測失敗')
            }), 500
            
    except Exception as e:
        logger.error(f"方法一回測API錯誤: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'回測執行失敗: {str(e)}'
        }), 500

@backtesting_bp.route('/method2/<lottery_type>', methods=['POST'])
def run_method2_backtest(lottery_type):
    """執行方法二回測 - 歷史校正"""
    try:
        if not BacktestingValidator:
            return jsonify({
                'success': False,
                'error': '回測模組未正確載入'
            }), 500
        
        data = request.get_json() or {}
        calibration_start = data.get('calibration_start', '114000035')
        validation_periods = data.get('validation_periods', 20)
        
        # 驗證彩票類型
        valid_types = ['powercolor', 'lotto649', 'dailycash']
        if lottery_type not in valid_types:
            return jsonify({
                'success': False,
                'error': f'不支援的彩票類型: {lottery_type}'
            }), 400
        
        # 初始化驗證器
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'data', 'lottery_data.db')
        validator = BacktestingValidator(db_path)
        
        # 執行方法二回測
        logger.info(f"執行方法二回測: {lottery_type}, 校正起始: {calibration_start}, 驗證期數: {validation_periods}")
        result = validator.run_method_2_backtest(lottery_type, calibration_start, validation_periods)
        
        if result.get('success'):
            return jsonify({
                'success': True,
                'data': result,
                'message': '方法二回測完成'
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', '回測失敗')
            }), 500
            
    except Exception as e:
        logger.error(f"方法二回測API錯誤: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'回測執行失敗: {str(e)}'
        }), 500

@backtesting_bp.route('/comprehensive/<lottery_type>', methods=['POST'])
def run_comprehensive_backtest_api(lottery_type):
    """執行綜合回測 - 同時執行兩種方法並比較"""
    try:
        if not run_comprehensive_backtest:
            return jsonify({
                'success': False,
                'error': '綜合回測功能未正確載入'
            }), 500
        
        # 驗證彩票類型
        valid_types = ['powercolor', 'lotto649', 'dailycash']
        if lottery_type not in valid_types:
            return jsonify({
                'success': False,
                'error': f'不支援的彩票類型: {lottery_type}'
            }), 400
        
        # 執行綜合回測
        logger.info(f"執行綜合回測: {lottery_type}")
        result = run_comprehensive_backtest(lottery_type)
        
        if result.get('success'):
            return jsonify({
                'success': True,
                'data': result,
                'message': '綜合回測完成'
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', '綜合回測失敗')
            }), 500
            
    except Exception as e:
        logger.error(f"綜合回測API錯誤: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'綜合回測執行失敗: {str(e)}'
        }), 500

@backtesting_bp.route('/status', methods=['GET'])
def backtest_status():
    """檢查回測系統狀態"""
    try:
        # 檢查數據庫連接
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'data', 'lottery_data.db')
        db_exists = os.path.exists(db_path)
        
        # 檢查模組載入狀態
        modules_loaded = BacktestingValidator is not None and run_comprehensive_backtest is not None
        
        status = {
            'system_ready': db_exists and modules_loaded,
            'database_available': db_exists,
            'modules_loaded': modules_loaded,
            'database_path': db_path,
            'supported_lotteries': ['powercolor', 'lotto649', 'dailycash']
        }
        
        return jsonify({
            'success': True,
            'data': status
        })
        
    except Exception as e:
        logger.error(f"狀態檢查錯誤: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'狀態檢查失敗: {str(e)}'
        }), 500

@backtesting_bp.route('/validate-prediction/<lottery_type>/<period>', methods=['POST'])
def validate_single_prediction(lottery_type, period):
    """驗證單一期號的預測準確度"""
    try:
        if not BacktestingValidator:
            return jsonify({
                'success': False,
                'error': '回測模組未正確載入'
            }), 500
        
        # 驗證彩票類型
        valid_types = ['powercolor', 'lotto649', 'dailycash']
        if lottery_type not in valid_types:
            return jsonify({
                'success': False,
                'error': f'不支援的彩票類型: {lottery_type}'
            }), 400
        
        # 初始化驗證器
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'data', 'lottery_data.db')
        validator = BacktestingValidator(db_path)
        
        # 獲取該期的實際開獎結果
        actual_data = validator._get_historical_data_for_backtest(lottery_type, period, 1)
        if not actual_data:
            return jsonify({
                'success': False,
                'error': f'找不到期號 {period} 的開獎數據'
            }), 404
        
        target_period_data = actual_data[0]
        if target_period_data['period'] != int(period):
            return jsonify({
                'success': False,
                'error': f'期號不符: 期望 {period}, 實際 {target_period_data["period"]}'
            }), 400
        
        # 獲取用於預測的歷史數據（該期之前的數據）
        training_data = validator._get_historical_data_for_backtest(lottery_type, str(int(period) - 1), 50)
        if not training_data:
            return jsonify({
                'success': False,
                'error': f'找不到期號 {period} 之前的歷史數據'
            }), 404
        
        # 進行預測
        prediction = validator._make_prediction_for_period(lottery_type, training_data, period)
        if not prediction:
            return jsonify({
                'success': False,
                'error': '預測失敗'
            }), 500
        
        # 評估準確度
        accuracy = validator._evaluate_prediction_accuracy(prediction, target_period_data)
        
        return jsonify({
            'success': True,
            'data': {
                'period': period,
                'lottery_type': lottery_type,
                'prediction': prediction,
                'actual_result': target_period_data,
                'accuracy_evaluation': accuracy,
                'validation_timestamp': validator._calculate_backtest_summary({'total_tests': 1, 'partial_matches': [accuracy['main_numbers_match_count']], 'special_number_hits': 1 if accuracy['special_number_match'] else 0, 'confidence_scores': [prediction['confidence']]}, lottery_type)
            },
            'message': f'期號 {period} 預測驗證完成'
        })
        
    except Exception as e:
        logger.error(f"單一預測驗證錯誤: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'預測驗證失敗: {str(e)}'
        }), 500