#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phase 3 功能整合模塊
將Phase 3高級功能整合到Web應用中
"""

import os
import sys
import logging
from typing import Dict, Any, Optional

# 添加項目根目錄到Python路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logger = logging.getLogger(__name__)

class Phase3Integration:
    """Phase 3功能整合類"""
    
    def __init__(self, db_manager=None):
        self.db_manager = db_manager
        self.components = {}
        self.is_available = False
        self._initialize_phase3_components()
    
    def _initialize_phase3_components(self):
        """初始化Phase 3組件"""
        try:
            # 導入Phase 3核心模塊
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from phase3 import universal_prediction_framework as upf
            from phase3 import prediction_tracking_system as pts
            from phase3 import realtime_data_manager as rdm
            from phase3 import accuracy_assessment_engine as aae
            from phase3 import auto_prediction_scheduler as aps
            from phase3 import visualization_reports as vr
            
            UniversalPredictor = upf.UniversalPredictor
            PredictionTracker = pts.PredictionTracker
            RealTimeDataManager = rdm.RealTimeDataManager
            AccuracyAssessmentEngine = aae.AccuracyAssessmentEngine
            AutoPredictionScheduler = aps.AutoPredictionScheduler
            VisualizationReportGenerator = vr.VisualizationReportGenerator
            
            # 初始化組件
            self.components = {
                'universal_predictor': UniversalPredictor(),
                'prediction_tracker': PredictionTracker(self.db_manager) if self.db_manager else None,
                'realtime_manager': RealTimeDataManager(self.db_manager) if self.db_manager else None,
                'accuracy_engine': AccuracyAssessmentEngine(self.db_manager) if self.db_manager else None,
                'auto_scheduler': AutoPredictionScheduler(self.db_manager) if self.db_manager else None,
                'visualization': VisualizationReportGenerator(self.db_manager) if self.db_manager else None
            }
            
            # 移除None值
            self.components = {k: v for k, v in self.components.items() if v is not None}
            
            self.is_available = len(self.components) > 0
            if self.is_available:
                logger.info(f"✅ Phase 3功能整合成功，已載入 {len(self.components)} 個組件")
            else:
                logger.warning("⚠️ Phase 3功能部分載入")
                
        except ImportError as e:
            logger.error(f"❌ Phase 3模塊導入失敗: {e}")
            self.is_available = False
        except Exception as e:
            logger.error(f"❌ Phase 3初始化失敗: {e}")
            self.is_available = False
    
    def get_component(self, component_name: str):
        """獲取Phase 3組件"""
        return self.components.get(component_name)
    
    def universal_predict(self, lottery_type: str, **kwargs) -> Dict[str, Any]:
        """使用通用預測框架進行預測"""
        if not self.is_available or 'universal_predictor' not in self.components:
            return {
                'success': False,
                'error': 'Phase 3通用預測框架不可用'
            }
        
        try:
            predictor = self.components['universal_predictor']
            # 調用通用預測
            result = predictor.predict(lottery_type, **kwargs)
            
            # 如果有追蹤系統，記錄預測
            if 'prediction_tracker' in self.components:
                tracker = self.components['prediction_tracker']
                tracker.track_prediction(result)
            
            return {
                'success': True,
                'data': result
            }
        except Exception as e:
            logger.error(f"通用預測執行失敗: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_tracking_stats(self, lottery_type: Optional[str] = None) -> Dict[str, Any]:
        """獲取預測追蹤統計"""
        if not self.is_available or 'prediction_tracker' not in self.components:
            return {
                'success': False,
                'error': 'Phase 3追蹤系統不可用'
            }
        
        try:
            tracker = self.components['prediction_tracker']
            stats = tracker.get_statistics(lottery_type)
            return {
                'success': True,
                'data': stats
            }
        except Exception as e:
            logger.error(f"獲取追蹤統計失敗: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_accuracy_assessment(self, lottery_type: Optional[str] = None) -> Dict[str, Any]:
        """獲取準確度評估"""
        if not self.is_available or 'accuracy_engine' not in self.components:
            return {
                'success': False,
                'error': 'Phase 3準確度評估引擎不可用'
            }
        
        try:
            engine = self.components['accuracy_engine']
            assessment = engine.assess_accuracy(lottery_type)
            return {
                'success': True,
                'data': assessment
            }
        except Exception as e:
            logger.error(f"獲取準確度評估失敗: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def trigger_realtime_update(self) -> Dict[str, Any]:
        """觸發實時數據更新"""
        if not self.is_available or 'realtime_manager' not in self.components:
            return {
                'success': False,
                'error': 'Phase 3實時數據管理器不可用'
            }
        
        try:
            manager = self.components['realtime_manager']
            result = manager.update_all_data()
            return {
                'success': True,
                'data': result
            }
        except Exception as e:
            logger.error(f"實時數據更新失敗: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """獲取調度器狀態"""
        if not self.is_available or 'auto_scheduler' not in self.components:
            return {
                'success': False,
                'error': 'Phase 3自動調度器不可用'
            }
        
        try:
            scheduler = self.components['auto_scheduler']
            status = scheduler.get_status()
            return {
                'success': True,
                'data': status
            }
        except Exception as e:
            logger.error(f"獲取調度器狀態失敗: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def generate_visualization_report(self, report_type: str, lottery_type: Optional[str] = None) -> Dict[str, Any]:
        """生成可視化報告"""
        if not self.is_available or 'visualization' not in self.components:
            return {
                'success': False,
                'error': 'Phase 3可視化報告生成器不可用'
            }
        
        try:
            generator = self.components['visualization']
            report = generator.generate_report(report_type, lottery_type)
            return {
                'success': True,
                'data': report
            }
        except Exception as e:
            logger.error(f"生成可視化報告失敗: {e}")
            return {
                'success': False,
                'error': str(e)
            }


# 全局Phase 3整合實例
_phase3_integration = None

def get_phase3_integration(db_manager=None) -> Phase3Integration:
    """獲取Phase 3整合實例（單例模式）"""
    global _phase3_integration
    if _phase3_integration is None:
        _phase3_integration = Phase3Integration(db_manager)
    return _phase3_integration