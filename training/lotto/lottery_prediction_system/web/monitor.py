#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
監控腳本
監控彩票預測系統的健康狀態和性能
"""

import os
import sys
import time
import json
import psutil
import requests
import sqlite3
import logging
import smtplib
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart


@dataclass
class HealthCheck:
    """健康檢查結果"""
    name: str
    status: str  # 'healthy', 'warning', 'critical'
    message: str
    timestamp: datetime
    details: Dict[str, Any] = None
    
    def to_dict(self) -> Dict:
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result


@dataclass
class SystemMetrics:
    """系統指標"""
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_io: Dict[str, int]
    process_count: int
    timestamp: datetime
    
    def to_dict(self) -> Dict:
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result


@dataclass
class ApplicationMetrics:
    """應用程序指標"""
    response_time: float
    status_code: int
    database_size: int
    active_connections: int
    cache_hit_rate: float
    error_rate: float
    timestamp: datetime
    
    def to_dict(self) -> Dict:
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result


class SystemMonitor:
    """系統監控器"""
    
    def __init__(self):
        self.logger = self._setup_logging()
    
    def _setup_logging(self) -> logging.Logger:
        """設置日誌"""
        logger = logging.getLogger('SystemMonitor')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def get_system_metrics(self) -> SystemMetrics:
        """獲取系統指標"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 內存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁盤使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # 網絡IO
            network = psutil.net_io_counters()
            network_io = {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            }
            
            # 進程數量
            process_count = len(psutil.pids())
            
            return SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                disk_percent=disk_percent,
                network_io=network_io,
                process_count=process_count,
                timestamp=datetime.now()
            )
        except Exception as e:
            self.logger.error(f"獲取系統指標失敗: {e}")
            raise
    
    def check_process_health(self, pid: int) -> HealthCheck:
        """檢查進程健康狀態"""
        try:
            if not psutil.pid_exists(pid):
                return HealthCheck(
                    name="process_health",
                    status="critical",
                    message=f"進程 {pid} 不存在",
                    timestamp=datetime.now()
                )
            
            process = psutil.Process(pid)
            
            # 檢查進程狀態
            if process.status() == psutil.STATUS_ZOMBIE:
                return HealthCheck(
                    name="process_health",
                    status="critical",
                    message=f"進程 {pid} 是殭屍進程",
                    timestamp=datetime.now()
                )
            
            # 檢查CPU和內存使用
            cpu_percent = process.cpu_percent()
            memory_percent = process.memory_percent()
            
            details = {
                'pid': pid,
                'status': process.status(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'create_time': process.create_time()
            }
            
            # 判斷健康狀態
            if cpu_percent > 80 or memory_percent > 80:
                status = "warning"
                message = f"進程 {pid} 資源使用率較高 (CPU: {cpu_percent:.1f}%, Memory: {memory_percent:.1f}%)"
            else:
                status = "healthy"
                message = f"進程 {pid} 運行正常"
            
            return HealthCheck(
                name="process_health",
                status=status,
                message=message,
                timestamp=datetime.now(),
                details=details
            )
        except Exception as e:
            return HealthCheck(
                name="process_health",
                status="critical",
                message=f"檢查進程 {pid} 失敗: {e}",
                timestamp=datetime.now()
            )
    
    def check_disk_space(self, path: str = '/', threshold: float = 85.0) -> HealthCheck:
        """檢查磁盤空間"""
        try:
            disk = psutil.disk_usage(path)
            used_percent = (disk.used / disk.total) * 100
            
            details = {
                'path': path,
                'total': disk.total,
                'used': disk.used,
                'free': disk.free,
                'used_percent': used_percent
            }
            
            if used_percent >= threshold:
                status = "critical"
                message = f"磁盤空間不足: {used_percent:.1f}% (閾值: {threshold}%)"
            elif used_percent >= threshold * 0.8:
                status = "warning"
                message = f"磁盤空間使用率較高: {used_percent:.1f}%"
            else:
                status = "healthy"
                message = f"磁盤空間正常: {used_percent:.1f}%"
            
            return HealthCheck(
                name="disk_space",
                status=status,
                message=message,
                timestamp=datetime.now(),
                details=details
            )
        except Exception as e:
            return HealthCheck(
                name="disk_space",
                status="critical",
                message=f"檢查磁盤空間失敗: {e}",
                timestamp=datetime.now()
            )


class ApplicationMonitor:
    """應用程序監控器"""
    
    def __init__(self, base_url: str = "http://localhost:5003", db_path: str = None):
        self.base_url = base_url.rstrip('/')
        self.db_path = db_path
        self.logger = self._setup_logging()
    
    def _setup_logging(self) -> logging.Logger:
        """設置日誌"""
        logger = logging.getLogger('ApplicationMonitor')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def check_api_health(self, timeout: int = 10) -> HealthCheck:
        """檢查API健康狀態"""
        try:
            start_time = time.time()
            response = requests.get(
                f"{self.base_url}/api/system_status",
                timeout=timeout
            )
            response_time = time.time() - start_time
            
            details = {
                'url': f"{self.base_url}/api/system_status",
                'status_code': response.status_code,
                'response_time': response_time,
                'headers': dict(response.headers)
            }
            
            if response.status_code == 200:
                if response_time > 5.0:
                    status = "warning"
                    message = f"API響應較慢: {response_time:.2f}秒"
                else:
                    status = "healthy"
                    message = f"API運行正常 (響應時間: {response_time:.2f}秒)"
            else:
                status = "critical"
                message = f"API返回錯誤狀態碼: {response.status_code}"
            
            return HealthCheck(
                name="api_health",
                status=status,
                message=message,
                timestamp=datetime.now(),
                details=details
            )
        except requests.exceptions.Timeout:
            return HealthCheck(
                name="api_health",
                status="critical",
                message=f"API請求超時 (>{timeout}秒)",
                timestamp=datetime.now()
            )
        except requests.exceptions.ConnectionError:
            return HealthCheck(
                name="api_health",
                status="critical",
                message="無法連接到API服務",
                timestamp=datetime.now()
            )
        except Exception as e:
            return HealthCheck(
                name="api_health",
                status="critical",
                message=f"API健康檢查失敗: {e}",
                timestamp=datetime.now()
            )
    
    def check_database_health(self) -> HealthCheck:
        """檢查數據庫健康狀態"""
        if not self.db_path or not os.path.exists(self.db_path):
            return HealthCheck(
                name="database_health",
                status="critical",
                message="數據庫文件不存在",
                timestamp=datetime.now()
            )
        
        try:
            # 檢查數據庫文件大小
            db_size = os.path.getsize(self.db_path)
            
            # 嘗試連接數據庫
            conn = sqlite3.connect(self.db_path, timeout=5)
            cursor = conn.cursor()
            
            # 檢查數據庫完整性
            cursor.execute("PRAGMA integrity_check")
            integrity_result = cursor.fetchone()[0]
            
            # 獲取表信息
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            # 檢查連接數（SQLite不支持，使用文件鎖定狀態）
            cursor.execute("PRAGMA database_list")
            db_info = cursor.fetchall()
            
            conn.close()
            
            details = {
                'db_path': self.db_path,
                'db_size': db_size,
                'integrity': integrity_result,
                'tables': tables,
                'db_info': db_info
            }
            
            if integrity_result == "ok":
                status = "healthy"
                message = f"數據庫運行正常 (大小: {db_size / 1024 / 1024:.1f}MB)"
            else:
                status = "critical"
                message = f"數據庫完整性檢查失敗: {integrity_result}"
            
            return HealthCheck(
                name="database_health",
                status=status,
                message=message,
                timestamp=datetime.now(),
                details=details
            )
        except Exception as e:
            return HealthCheck(
                name="database_health",
                status="critical",
                message=f"數據庫健康檢查失敗: {e}",
                timestamp=datetime.now()
            )
    
    def get_application_metrics(self) -> ApplicationMetrics:
        """獲取應用程序指標"""
        try:
            # API響應時間
            start_time = time.time()
            response = requests.get(f"{self.base_url}/api/system_status", timeout=10)
            response_time = time.time() - start_time
            
            # 數據庫大小
            db_size = os.path.getsize(self.db_path) if self.db_path and os.path.exists(self.db_path) else 0
            
            # 模擬其他指標（實際應用中應該從真實數據源獲取）
            active_connections = 1  # SQLite通常是單連接
            cache_hit_rate = 0.85  # 示例值
            error_rate = 0.01  # 示例值
            
            return ApplicationMetrics(
                response_time=response_time,
                status_code=response.status_code,
                database_size=db_size,
                active_connections=active_connections,
                cache_hit_rate=cache_hit_rate,
                error_rate=error_rate,
                timestamp=datetime.now()
            )
        except Exception as e:
            self.logger.error(f"獲取應用程序指標失敗: {e}")
            raise


class AlertManager:
    """告警管理器"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.logger = self._setup_logging()
    
    def _setup_logging(self) -> logging.Logger:
        """設置日誌"""
        logger = logging.getLogger('AlertManager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def should_alert(self, health_check: HealthCheck) -> bool:
        """判斷是否需要告警"""
        # 只對critical和warning狀態告警
        if health_check.status not in ['critical', 'warning']:
            return False
        
        # 可以添加更複雜的告警邏輯，如頻率限制等
        return True
    
    def send_email_alert(self, health_check: HealthCheck) -> bool:
        """發送郵件告警"""
        email_config = self.config.get('email', {})
        if not email_config.get('enabled', False):
            return False
        
        try:
            # 創建郵件
            msg = MIMEMultipart()
            msg['From'] = email_config['from']
            msg['To'] = ', '.join(email_config['to'])
            msg['Subject'] = f"[{health_check.status.upper()}] 彩票預測系統告警 - {health_check.name}"
            
            # 郵件內容
            body = f"""
            告警詳情:
            
            檢查項目: {health_check.name}
            狀態: {health_check.status}
            消息: {health_check.message}
            時間: {health_check.timestamp}
            
            詳細信息:
            {json.dumps(health_check.details, indent=2, ensure_ascii=False) if health_check.details else '無'}
            """
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # 發送郵件
            server = smtplib.SMTP(email_config['smtp_host'], email_config['smtp_port'])
            if email_config.get('use_tls', True):
                server.starttls()
            if email_config.get('username') and email_config.get('password'):
                server.login(email_config['username'], email_config['password'])
            
            server.send_message(msg)
            server.quit()
            
            self.logger.info(f"郵件告警已發送: {health_check.name}")
            return True
        except Exception as e:
            self.logger.error(f"發送郵件告警失敗: {e}")
            return False
    
    def send_webhook_alert(self, health_check: HealthCheck) -> bool:
        """發送Webhook告警"""
        webhook_config = self.config.get('webhook', {})
        if not webhook_config.get('enabled', False):
            return False
        
        try:
            payload = {
                'alert_type': 'health_check',
                'severity': health_check.status,
                'title': f"彩票預測系統告警 - {health_check.name}",
                'message': health_check.message,
                'timestamp': health_check.timestamp.isoformat(),
                'details': health_check.details
            }
            
            response = requests.post(
                webhook_config['url'],
                json=payload,
                headers=webhook_config.get('headers', {}),
                timeout=10
            )
            
            if response.status_code == 200:
                self.logger.info(f"Webhook告警已發送: {health_check.name}")
                return True
            else:
                self.logger.error(f"Webhook告警發送失敗: {response.status_code}")
                return False
        except Exception as e:
            self.logger.error(f"發送Webhook告警失敗: {e}")
            return False
    
    def handle_alert(self, health_check: HealthCheck):
        """處理告警"""
        if not self.should_alert(health_check):
            return
        
        self.logger.warning(f"告警觸發: {health_check.name} - {health_check.message}")
        
        # 發送郵件告警
        self.send_email_alert(health_check)
        
        # 發送Webhook告警
        self.send_webhook_alert(health_check)


class MonitoringService:
    """監控服務"""
    
    def __init__(self, config_path: str = None):
        self.config = self._load_config(config_path)
        self.system_monitor = SystemMonitor()
        self.app_monitor = ApplicationMonitor(
            base_url=self.config.get('app_url', 'http://localhost:5003'),
            db_path=self.config.get('db_path')
        )
        self.alert_manager = AlertManager(self.config.get('alerts', {}))
        self.logger = self._setup_logging()
    
    def _load_config(self, config_path: str) -> Dict:
        """加載配置"""
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                pass
        
        # 默認配置
        return {
            'app_url': 'http://localhost:5003',
            'db_path': '../lottery_data.db',
            'check_interval': 60,
            'alerts': {
                'email': {'enabled': False},
                'webhook': {'enabled': False}
            }
        }
    
    def _setup_logging(self) -> logging.Logger:
        """設置日誌"""
        logger = logging.getLogger('MonitoringService')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # 控制台處理器
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
            
            # 文件處理器
            file_handler = logging.FileHandler('monitor.log')
            file_handler.setFormatter(console_formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def run_health_checks(self) -> List[HealthCheck]:
        """運行健康檢查"""
        checks = []
        
        try:
            # 檢查API健康狀態
            api_check = self.app_monitor.check_api_health()
            checks.append(api_check)
            self.alert_manager.handle_alert(api_check)
            
            # 檢查數據庫健康狀態
            db_check = self.app_monitor.check_database_health()
            checks.append(db_check)
            self.alert_manager.handle_alert(db_check)
            
            # 檢查磁盤空間
            disk_check = self.system_monitor.check_disk_space()
            checks.append(disk_check)
            self.alert_manager.handle_alert(disk_check)
            
            # 檢查進程健康狀態（如果有PID文件）
            pid_file = Path('app.pid')
            if pid_file.exists():
                try:
                    with open(pid_file, 'r') as f:
                        pid = int(f.read().strip())
                    process_check = self.system_monitor.check_process_health(pid)
                    checks.append(process_check)
                    self.alert_manager.handle_alert(process_check)
                except Exception as e:
                    self.logger.error(f"檢查進程健康狀態失敗: {e}")
        
        except Exception as e:
            self.logger.error(f"運行健康檢查失敗: {e}")
        
        return checks
    
    def collect_metrics(self) -> Dict:
        """收集指標"""
        metrics = {}
        
        try:
            # 系統指標
            system_metrics = self.system_monitor.get_system_metrics()
            metrics['system'] = system_metrics.to_dict()
            
            # 應用程序指標
            app_metrics = self.app_monitor.get_application_metrics()
            metrics['application'] = app_metrics.to_dict()
        
        except Exception as e:
            self.logger.error(f"收集指標失敗: {e}")
        
        return metrics
    
    def run_once(self) -> Dict:
        """運行一次監控檢查"""
        self.logger.info("開始監控檢查...")
        
        result = {
            'timestamp': datetime.now().isoformat(),
            'health_checks': [],
            'metrics': {},
            'summary': {'healthy': 0, 'warning': 0, 'critical': 0}
        }
        
        # 運行健康檢查
        health_checks = self.run_health_checks()
        result['health_checks'] = [check.to_dict() for check in health_checks]
        
        # 統計健康狀態
        for check in health_checks:
            result['summary'][check.status] += 1
        
        # 收集指標
        result['metrics'] = self.collect_metrics()
        
        self.logger.info(f"監控檢查完成 - 健康: {result['summary']['healthy']}, 警告: {result['summary']['warning']}, 嚴重: {result['summary']['critical']}")
        
        return result
    
    def run_continuous(self):
        """持續運行監控"""
        interval = self.config.get('check_interval', 60)
        self.logger.info(f"開始持續監控 (間隔: {interval}秒)")
        
        try:
            while True:
                self.run_once()
                time.sleep(interval)
        except KeyboardInterrupt:
            self.logger.info("監控服務已停止")
        except Exception as e:
            self.logger.error(f"監控服務異常: {e}")


def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description='彩票預測系統監控工具')
    parser.add_argument('--config', help='配置文件路徑')
    parser.add_argument('--once', action='store_true', help='只運行一次檢查')
    parser.add_argument('--output', help='輸出結果到文件')
    
    args = parser.parse_args()
    
    # 創建監控服務
    service = MonitoringService(args.config)
    
    if args.once:
        # 運行一次檢查
        result = service.run_once()
        
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"結果已保存到: {args.output}")
        else:
            print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        # 持續運行
        service.run_continuous()


if __name__ == '__main__':
    main()