#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增強彩票預測系統 - Web應用
基於成功率的Web界面，替代信心指數系統
"""

import os
import sys
from flask import Flask, render_template, request, jsonify, redirect, url_for
from datetime import datetime
import json
import traceback
import pandas as pd
import numpy as np

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.db_manager import DBManager as DatabaseManager
from data.feature_engineering import FeatureEngineer
from prediction.enhanced_lottery_predictor import EnhancedLotteryPredictor
from display.prediction_display import PredictionDisplay

# 導入威力彩歷史資料管理器
sys.path.append(os.path.join(os.path.dirname(__file__)))
from powercolor_history import PowercolorHistoryManager

app = Flask(__name__)
app.secret_key = 'enhanced_lottery_prediction_system_2024'

# 全局變量
db_manager = None
enhanced_predictor = None
feature_engineer = None
display = None
powercolor_history_manager = None

def initialize_components():
    """
    初始化系統組件
    """
    global db_manager, enhanced_predictor, feature_engineer, display, powercolor_history_manager
    
    try:
        # 使用絕對路徑確保數據庫路徑正確
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        db_manager = DatabaseManager(db_path)
        enhanced_predictor = EnhancedLotteryPredictor(db_manager)
        feature_engineer = FeatureEngineer()
        display = PredictionDisplay()
        powercolor_history_manager = PowercolorHistoryManager()
        print("系統組件初始化完成")
    except Exception as e:
        print(f"系統組件初始化失敗：{str(e)}")
        raise

@app.route('/')
def index():
    """
    主頁
    """
    return render_template('enhanced_index.html')

@app.route('/predict')
def predict_page():
    """
    預測頁面
    """
    return render_template('enhanced_predict.html')

@app.route('/api/predict', methods=['POST'])
def api_predict():
    """
    API: 執行預測
    """
    try:
        data = request.get_json()
        lottery_type = data.get('lottery_type', 'powercolor')
        methods = data.get('methods', None)
        period = data.get('period', None)
        
        # 如果沒有提供期數，自動獲取下一期期數
        if not period:
            period = db_manager.get_next_period(lottery_type)
            if not period:
                return jsonify({
                    'success': False,
                    'error': f'無法獲取{get_lottery_name(lottery_type)}的下一期期數'
                })
        
        # 載入歷史數據
        df = db_manager.load_lottery_data(lottery_type=lottery_type)
        if df.empty:
            return jsonify({
                'success': False,
                'error': f'沒有找到{get_lottery_name(lottery_type)}的歷史數據'
            })
        
        # 特徵工程
        features = feature_engineer.create_features(df, lottery_type)
        if features is None or len(features) == 0:
            return jsonify({
                'success': False,
                'error': '特徵工程失敗'
            })
        
        # 執行預測
        result = enhanced_predictor.predict_with_success_rate(
            df=df,
            features=features[-1:],
            lottery_type=lottery_type,
            methods=methods,
            period=period
        )
        
        if not result:
            return jsonify({
                'success': False,
                'error': '預測失敗'
            })
        
        # 獲取成功率信息
        success_rates = enhanced_predictor.get_method_success_rates(lottery_type)
        best_method = enhanced_predictor.get_best_prediction_method(lottery_type)
        
        return jsonify({
            'success': True,
            'result': result,
            'success_rates': success_rates,
            'best_method': best_method,
            'lottery_name': get_lottery_name(lottery_type),
            'predicted_period': period
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'預測過程中發生錯誤：{str(e)}'
        })

@app.route('/api/success_rates/<lottery_type>')
def api_success_rates(lottery_type):
    """
    API: 獲取成功率信息
    """
    try:
        days = request.args.get('days', 30, type=int)
        
        # 獲取成功率
        success_rates = enhanced_predictor.get_method_success_rates(lottery_type, days)
        
        # 獲取最佳方法
        best_method = enhanced_predictor.get_best_prediction_method(lottery_type, days)
        
        return jsonify({
            'success': True,
            'success_rates': success_rates,
            'best_method': best_method,
            'days': days,
            'lottery_name': get_lottery_name(lottery_type)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'獲取成功率時發生錯誤：{str(e)}'
        })

@app.route('/api/performance/<lottery_type>')
def api_performance(lottery_type):
    """
    API: 獲取性能分析
    """
    try:
        days = request.args.get('days', 30, type=int)
        
        # 獲取性能分析
        performance = enhanced_predictor.analyze_prediction_performance(lottery_type, days)
        
        return jsonify({
            'success': True,
            'performance': performance,
            'lottery_name': get_lottery_name(lottery_type)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'性能分析時發生錯誤：{str(e)}'
        })

@app.route('/api/history')
def api_history():
    """
    API: 獲取歷史預測記錄（支持分頁和篩選）
    """
    try:
        # 獲取查詢參數
        lottery_type = request.args.get('lottery_type', 'powercolor')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        
        # 載入預測記錄
        records_df = db_manager.load_prediction_records(
            lottery_type=lottery_type,
            limit=per_page
        )
        
        # 轉換DataFrame為字典列表
        records = records_df.to_dict('records') if not records_df.empty else []
        
        # 簡化的分頁信息
        total_records = len(records)
        total_pages = 1  # 簡化為單頁
        
        # 簡化的統計信息
        statistics = {
            'total_predictions': total_records,
            'lottery_type': lottery_type
        }
        
        return jsonify({
            'success': True,
            'records': records,
            'pagination': {
                'current_page': page,
                'per_page': per_page,
                'total_records': total_records,
                'total_pages': total_pages
            },
            'statistics': statistics
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'載入歷史記錄時發生錯誤：{str(e)}'
        })

@app.route('/api/history/<lottery_type>')
def api_history_by_type(lottery_type):
    """
    API: 獲取特定彩票類型的歷史記錄
    """
    try:
        limit = request.args.get('limit', 20, type=int)
        
        # 載入預測記錄
        records_df = db_manager.load_prediction_records(
            lottery_type=lottery_type,
            limit=limit
        )
        
        # 轉換DataFrame為字典列表
        records = records_df.to_dict('records') if not records_df.empty else []
        
        return jsonify({
            'success': True,
            'records': records,
            'lottery_name': get_lottery_name(lottery_type)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'載入歷史記錄時發生錯誤：{str(e)}'
        })

@app.route('/api/latest_predictions/<lottery_type>')
def api_latest_predictions(lottery_type):
    """
    API: 獲取最新預測記錄
    """
    try:
        limit = request.args.get('limit', 3, type=int)
        
        # 載入最新預測記錄
        records_df = db_manager.load_prediction_records(
            lottery_type=lottery_type,
            limit=limit
        )
        
        # 轉換DataFrame為字典列表
        records = records_df.to_dict('records') if not records_df.empty else []
        
        return jsonify({
            'success': True,
            'predictions': records,
            'lottery_name': get_lottery_name(lottery_type)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'獲取最新預測記錄時發生錯誤：{str(e)}',
            'predictions': []
        })

@app.route('/predictions')
def predictions():
    """
    預測記錄頁面
    """
    lottery_type = request.args.get('type', 'powercolor')
    limit = request.args.get('limit', 20, type=int)
    
    try:
        # 載入預測記錄
        records_df = db_manager.load_prediction_records(
            lottery_type=lottery_type,
            limit=limit
        )
        
        # 轉換DataFrame為字典列表
        records = records_df.to_dict('records') if not records_df.empty else []
        
        return render_template(
            'predictions.html',
            records=records,
            lottery_type=lottery_type,
            lottery_name=get_lottery_name(lottery_type)
        )
        
    except Exception as e:
        return render_template(
            'predictions.html',
            records=[],
            lottery_type=lottery_type,
            lottery_name=get_lottery_name(lottery_type),
            error=f'載入預測記錄時發生錯誤：{str(e)}'
        )

@app.route('/analysis')
def analysis_page():
    """
    分析頁面
    """
    return render_template('enhanced_analysis.html')

@app.route('/performance')
def performance_page():
    """
    性能分析頁面
    """
    return render_template('enhanced_performance.html')

@app.route('/history')
def history_page():
    """
    歷史記錄頁面
    """
    return render_template('enhanced_history.html')

@app.route('/periods')
def periods_page():
    """
    期數管理頁面
    """
    return render_template('periods_management.html')

# 歷史資料查詢功能已整合到開獎結果頁面 (/results)

@app.route('/results')
def results_page():
    """
    開獎結果查詢頁面
    """
    return render_template('results.html')

# 移除重複的歷史資料查詢頁面，功能已整合到 /results

@app.route('/api/compare_prediction/<lottery_type>/<period>')
def api_compare_prediction(lottery_type, period):
    """
    API: 比對預測結果與實際開獎
    """
    try:
        # 獲取預測結果
        prediction_data = db_manager.get_prediction_by_period(period, lottery_type)
        
        # 獲取實際開獎結果
        actual_data = db_manager.get_period_data(period, lottery_type)
        
        if not prediction_data:
            return jsonify({
                'success': False,
                'error': f'找不到期號 {period} 的預測資料'
            })
        
        if not actual_data:
            return jsonify({
                'success': False,
                'error': f'找不到期號 {period} 的開獎資料'
            })
        
        # 進行比對分析
        comparison_result = {
            'period': period,
            'lottery_type': lottery_type,
            'lottery_name': get_lottery_name(lottery_type),
            'prediction': prediction_data,
            'actual': actual_data,
            'comparison': {}
        }
        
        # 根據彩票類型進行不同的比對
        if lottery_type == 'powercolor':
            pred_numbers = [prediction_data.get(f'PredA{i}', 0) for i in range(1, 7)]
            actual_numbers = [actual_data.get(f'Anumber{i}', 0) for i in range(1, 7)]
            pred_special = prediction_data.get('PredSpecial', 0)
            actual_special = actual_data.get('Second_district', 0)
            
            matched_numbers = len(set(pred_numbers) & set(actual_numbers))
            special_matched = pred_special == actual_special
            
            comparison_result['comparison'] = {
                'matched_numbers': matched_numbers,
                'total_numbers': 6,
                'special_matched': special_matched,
                'prediction_numbers': pred_numbers,
                'actual_numbers': actual_numbers,
                'prediction_special': pred_special,
                'actual_special': actual_special
            }
            
        elif lottery_type == 'lotto649':
            pred_numbers = [prediction_data.get(f'PredA{i}', 0) for i in range(1, 7)]
            actual_numbers = [actual_data.get(f'Anumber{i}', 0) for i in range(1, 7)]
            pred_special = prediction_data.get('PredSpecial', 0)
            actual_special = actual_data.get('SpecialNumber', 0)
            
            matched_numbers = len(set(pred_numbers) & set(actual_numbers))
            special_matched = pred_special == actual_special
            
            comparison_result['comparison'] = {
                'matched_numbers': matched_numbers,
                'total_numbers': 6,
                'special_matched': special_matched,
                'prediction_numbers': pred_numbers,
                'actual_numbers': actual_numbers,
                'prediction_special': pred_special,
                'actual_special': actual_special
            }
            
        elif lottery_type == 'dailycash':
            pred_numbers = [prediction_data.get(f'PredA{i}', 0) for i in range(1, 6)]
            actual_numbers = [actual_data.get(f'Anumber{i}', 0) for i in range(1, 6)]
            
            matched_numbers = len(set(pred_numbers) & set(actual_numbers))
            
            comparison_result['comparison'] = {
                'matched_numbers': matched_numbers,
                'total_numbers': 5,
                'prediction_numbers': pred_numbers,
                'actual_numbers': actual_numbers
            }
        
        return jsonify({
            'success': True,
            'data': comparison_result
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'比對預測結果時發生錯誤：{str(e)}'
        })

@app.route('/api/verify_prediction', methods=['POST'])
def api_verify_prediction():
    """
    API: 驗證預測結果
    """
    try:
        data = request.get_json()
        prediction_result = data.get('prediction_result', {})
        actual_numbers = data.get('actual_numbers', [])
        special_number = data.get('special_number', None)
        
        # 記錄並驗證預測結果
        success = enhanced_predictor.record_prediction_result(
            prediction_result=prediction_result,
            actual_numbers=actual_numbers,
            special_number=special_number
        )
        
        return jsonify({
            'success': success,
            'message': '預測結果已記錄並驗證' if success else '驗證失敗'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'驗證預測結果時發生錯誤：{str(e)}'
        })

@app.route('/api/system_status')
def api_system_status():
    """
    API: 獲取系統狀態
    """
    try:
        status = {
            'system_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'database_connected': db_manager is not None,
            'predictor_ready': enhanced_predictor is not None,
            'supported_lotteries': ['powercolor', 'lotto649', 'dailycash'],
            'available_methods': ['ml', 'board_path', 'integrated', 'ensemble']
        }
        
        # 獲取各彩票類型的最新成功率
        lottery_status = {}
        for lottery_type in ['powercolor', 'lotto649', 'dailycash']:
            try:
                success_rates = enhanced_predictor.get_method_success_rates(lottery_type, 7)  # 7天
                best_method = enhanced_predictor.get_best_prediction_method(lottery_type, 7)
                lottery_status[lottery_type] = {
                    'success_rates': success_rates,
                    'best_method': best_method,
                    'name': get_lottery_name(lottery_type)
                }
            except:
                lottery_status[lottery_type] = {
                    'success_rates': {},
                    'best_method': 'ml',
                    'name': get_lottery_name(lottery_type)
                }
        
        status['lottery_status'] = lottery_status
        
        return jsonify({
            'success': True,
            'status': status
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'獲取系統狀態時發生錯誤：{str(e)}'
        })

@app.route('/api/periods/<lottery_type>')
def api_get_periods(lottery_type):
    """
    API: 獲取期號列表
    """
    try:
        limit = request.args.get('limit', 50, type=int)
        periods = db_manager.get_periods_list(lottery_type, limit)
        
        return jsonify({
            'success': True,
            'periods': periods,
            'lottery_name': get_lottery_name(lottery_type)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'獲取期號列表時發生錯誤：{str(e)}'
        })

@app.route('/api/next_period/<lottery_type>')
def api_get_next_period(lottery_type):
    """
    API: 獲取下一期號
    """
    try:
        next_period = db_manager.get_next_period(lottery_type)
        latest_period = db_manager.get_latest_period(lottery_type)
        
        return jsonify({
            'success': True,
            'next_period': next_period,
            'latest_period': latest_period,
            'lottery_name': get_lottery_name(lottery_type)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'獲取下一期號時發生錯誤：{str(e)}'
        })

@app.route('/api/period_data/<lottery_type>/<period>')
def api_get_period_data(lottery_type, period):
    """
    API: 獲取指定期號的開獎資料
    """
    try:
        period_data = db_manager.get_period_data(period, lottery_type)
        
        if period_data:
            return jsonify({
                'success': True,
                'data': period_data,
                'lottery_name': get_lottery_name(lottery_type)
            })
        else:
            return jsonify({
                'success': False,
                'error': f'期號 {period} 的資料不存在'
            })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'獲取期號資料時發生錯誤：{str(e)}'
        })

@app.route('/api/results')
def api_get_results():
    """
    API: 獲取開獎結果列表
    """
    try:
        # 獲取查詢參數
        lottery_type = request.args.get('lottery_type', 'powercolor')
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 20, type=int)
        period = request.args.get('period', type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # 載入歷史數據
        df = db_manager.load_lottery_data(lottery_type=lottery_type)
        
        if df.empty:
            return jsonify({
                'success': True,
                'results': [],
                'statistics': {},
                'pagination': {'current_page': 1, 'total_pages': 1}
            })
        
        # 應用篩選條件
        filtered_df = df.copy()
        
        if period:
            filtered_df = filtered_df[filtered_df['Period'] == period]
        
        if start_date:
            start_date_obj = pd.to_datetime(start_date)
            filtered_df = filtered_df[pd.to_datetime(filtered_df['Sdate']) >= start_date_obj]
        
        if end_date:
            end_date_obj = pd.to_datetime(end_date)
            filtered_df = filtered_df[pd.to_datetime(filtered_df['Sdate']) <= end_date_obj]
        
        # 按期號降序排列
        filtered_df = filtered_df.sort_values('Period', ascending=False)
        
        # 計算分頁
        total_records = len(filtered_df)
        total_pages = max(1, (total_records + limit - 1) // limit)
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        
        page_df = filtered_df.iloc[start_idx:end_idx]
        
        # 轉換為結果格式
        results = []
        for _, row in page_df.iterrows():
            result = {
                'period': int(row['Period']),
                'date': row['Sdate'].strftime('%Y-%m-%d') if pd.notna(row['Sdate']) else None
            }
            
            # 根據彩票類型格式化號碼
            if lottery_type == 'powercolor':
                result['numbers'] = {
                    'first_area': [int(row[f'Anumber{i}']) for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])],
                    'second_area': int(row['Second_district']) if pd.notna(row['Second_district']) else 0
                }
            elif lottery_type == 'lotto649':
                result['numbers'] = {
                    'main_numbers': [int(row[f'Anumber{i}']) for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])],
                    'special_number': int(row['SpecialNumber']) if pd.notna(row['SpecialNumber']) else 0
                }
            elif lottery_type == 'dailycash':
                result['numbers'] = {
                    'numbers': [int(row[f'Anumber{i}']) for i in range(1, 6) if pd.notna(row[f'Anumber{i}'])]
                }
            
            results.append(result)
        
        # 計算統計資訊
        statistics = calculate_results_statistics(filtered_df, lottery_type)
        
        return jsonify({
            'success': True,
            'results': results,
            'statistics': statistics,
            'pagination': {
                'current_page': page,
                'total_pages': total_pages,
                'total_records': total_records
            },
            'lottery_name': get_lottery_name(lottery_type)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'獲取開獎結果時發生錯誤：{str(e)}'
        })

@app.route('/api/clear_predictions', methods=['POST'])
def api_clear_predictions():
    """
    API: 清理預測記錄
    """
    try:
        data = request.get_json()
        lottery_type = data.get('lottery_type', 'powercolor')
        
        # 這裡需要實現清理預測記錄的功能
        # 暫時返回成功，實際實現需要在db_manager中添加相應方法
        
        return jsonify({
            'success': True,
            'message': f'已清理 {get_lottery_name(lottery_type)} 的所有預測記錄'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'清理預測記錄時發生錯誤：{str(e)}'
        })

@app.route('/api/sequential_predict', methods=['POST'])
def api_sequential_predict():
    """
    API: 順序預測
    """
    try:
        data = request.get_json()
        lottery_type = data.get('lottery_type', 'powercolor')
        start_period = data.get('start_period')
        end_period = data.get('end_period')
        
        if not start_period:
            return jsonify({
                'success': False,
                'error': '請提供開始期號'
            })
        
        # 這裡需要實現順序預測的功能
        # 暫時返回成功，實際實現需要調用之前創建的腳本
        
        return jsonify({
            'success': True,
            'message': f'已開始從期號 {start_period} 進行順序預測',
            'start_period': start_period,
            'end_period': end_period or '最新期'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'順序預測時發生錯誤：{str(e)}'
        })

# 威力彩歷史資料API端點
@app.route('/api/powercolor/history')
def api_powercolor_history():
    """
    API: 獲取威力彩歷史資料
    """
    try:
        # 獲取查詢參數
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        limit = request.args.get('limit', type=int)
        period_start = request.args.get('period_start', type=int)
        period_end = request.args.get('period_end', type=int)
        
        period_range = None
        if period_start and period_end:
            period_range = [period_start, period_end]
        
        # 獲取歷史資料
        df = powercolor_history_manager.get_history_data(
            start_date=start_date,
            end_date=end_date,
            limit=limit,
            period_range=period_range
        )
        
        # 轉換為JSON格式
        records = []
        for _, row in df.iterrows():
            record = {
                'period': int(row['Period']),
                'date': row['Sdate'].strftime('%Y-%m-%d'),
                'numbers': {
                    'first_area': [int(row[f'Anumber{i}']) for i in range(1, 7)],
                    'second_area': int(row['Second_district'])
                }
            }
            records.append(record)
        
        return jsonify({
            'success': True,
            'total_records': len(records),
            'records': records
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'獲取歷史資料錯誤: {str(e)}'
        })

@app.route('/api/powercolor/statistics')
def api_powercolor_statistics():
    """
    API: 獲取威力彩統計分析
    """
    try:
        analysis_period = request.args.get('period', 100, type=int)
        
        # 獲取統計分析
        stats = powercolor_history_manager.get_number_statistics(analysis_period=analysis_period)
        
        return jsonify({
            'success': True,
            'statistics': stats
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'統計分析錯誤: {str(e)}'
        })

@app.route('/api/powercolor/patterns')
def api_powercolor_patterns():
    """
    API: 獲取威力彩模式分析
    """
    try:
        analysis_period = request.args.get('period', 50, type=int)
        
        # 獲取模式分析
        patterns = powercolor_history_manager.get_pattern_analysis(analysis_period=analysis_period)
        
        return jsonify({
            'success': True,
            'patterns': patterns
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'模式分析錯誤: {str(e)}'
        })

@app.route('/api/powercolor/trends')
def api_powercolor_trends():
    """
    API: 獲取威力彩趨勢分析
    """
    try:
        periods = request.args.get('periods', 20, type=int)
        
        # 獲取趨勢分析
        trends = powercolor_history_manager.get_recent_trends(periods=periods)
        
        return jsonify({
            'success': True,
            'trends': trends
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'趨勢分析錯誤: {str(e)}'
        })

@app.route('/api/powercolor/search')
def api_powercolor_search():
    """
    API: 根據號碼搜尋威力彩歷史記錄
    """
    try:
        numbers_str = request.args.get('numbers', '')
        search_type = request.args.get('search_type', 'contains')
        
        if not numbers_str:
            return jsonify({
                'success': False,
                'error': '請提供要搜尋的號碼'
            })
        
        # 解析號碼
        try:
            numbers = [int(x.strip()) for x in numbers_str.split(',') if x.strip()]
        except ValueError:
            return jsonify({
                'success': False,
                'error': '號碼格式錯誤，請使用逗號分隔的數字'
            })
        
        # 搜尋歷史記錄
        df = powercolor_history_manager.search_by_numbers(numbers, search_type)
        
        # 轉換為JSON格式
        records = []
        for _, row in df.iterrows():
            record = {
                'period': int(row['Period']),
                'date': row['Sdate'].strftime('%Y-%m-%d'),
                'numbers': {
                    'first_area': [int(row[f'Anumber{i}']) for i in range(1, 7)],
                    'second_area': int(row['Second_district'])
                }
            }
            records.append(record)
        
        return jsonify({
            'success': True,
            'search_numbers': numbers,
            'search_type': search_type,
            'total_matches': len(records),
            'records': records
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'搜尋錯誤: {str(e)}'
        })

def calculate_history_statistics(filters):
    """
    計算歷史記錄統計信息
    
    Args:
        filters: 篩選條件
        
    Returns:
        統計信息字典
    """
    try:
        # 獲取所有記錄
        lottery_type = filters.get('lottery_type', 'powercolor')
        all_records_df = db_manager.load_prediction_records(
            lottery_type=lottery_type,
            limit=None  # 獲取所有記錄
        )
        
        # 將DataFrame轉換為字典列表以便處理
        if all_records_df.empty:
            all_records = []
        else:
            all_records = all_records_df.to_dict('records')
        
        if not all_records:
            return {
                'total_predictions': 0,
                'successful_predictions': 0,
                'average_success_rate': 0.0,
                'best_method': 'N/A'
            }
        
        total_predictions = len(all_records)
        successful_predictions = 0
        total_success_rate = 0.0
        method_stats = {}
        
        for record in all_records:
            # 計算成功預測數量
            if record.get('verification_status') == 'verified':
                success_rate = record.get('success_rate', 0.0)
                if success_rate >= 0.6:  # 成功率閾值
                    successful_predictions += 1
                total_success_rate += success_rate
            
            # 統計各方法的表現
            method = record.get('prediction_method', 'unknown')
            if method not in method_stats:
                method_stats[method] = {'count': 0, 'success_rate': 0.0}
            method_stats[method]['count'] += 1
            method_stats[method]['success_rate'] += record.get('success_rate', 0.0)
        
        # 計算平均成功率
        verified_count = sum(1 for r in all_records if r.get('verification_status') == 'verified')
        average_success_rate = total_success_rate / verified_count if verified_count > 0 else 0.0
        
        # 找出最佳方法
        best_method = 'N/A'
        best_avg_rate = 0.0
        for method, stats in method_stats.items():
            avg_rate = stats['success_rate'] / stats['count'] if stats['count'] > 0 else 0.0
            if avg_rate > best_avg_rate:
                best_avg_rate = avg_rate
                best_method = method
        
        return {
            'total_predictions': total_predictions,
            'successful_predictions': successful_predictions,
            'average_success_rate': average_success_rate,
            'best_method': best_method
        }
        
    except Exception as e:
        print(f"計算統計信息時發生錯誤：{str(e)}")
        return {
            'total_predictions': 0,
            'successful_predictions': 0,
            'average_success_rate': 0.0,
            'best_method': 'N/A'
        }

def calculate_results_statistics(df, lottery_type):
    """
    計算開獎結果統計信息
    """
    try:
        if df.empty:
            return {}
        
        total_draws = len(df)
        
        # 日期範圍
        if 'Sdate' in df.columns and not df['Sdate'].empty:
            min_date = df['Sdate'].min().strftime('%Y-%m-%d')
            max_date = df['Sdate'].max().strftime('%Y-%m-%d')
            date_range = f"{min_date} ~ {max_date}"
        else:
            date_range = "無資料"
        
        # 計算號碼出現頻率
        number_counts = {}
        
        if lottery_type == 'powercolor':
            # 統計第一區號碼
            for i in range(1, 7):
                col = f'Anumber{i}'
                if col in df.columns:
                    for num in df[col].dropna():
                        number_counts[int(num)] = number_counts.get(int(num), 0) + 1
        elif lottery_type == 'lotto649':
            # 統計一般號碼
            for i in range(1, 7):
                col = f'Anumber{i}'
                if col in df.columns:
                    for num in df[col].dropna():
                        number_counts[int(num)] = number_counts.get(int(num), 0) + 1
        elif lottery_type == 'dailycash':
            # 統計號碼
            for i in range(1, 6):
                col = f'Anumber{i}'
                if col in df.columns:
                    for num in df[col].dropna():
                        number_counts[int(num)] = number_counts.get(int(num), 0) + 1
        
        # 找出最常和最少出現的號碼
        if number_counts:
            most_frequent_item = max(number_counts.items(), key=lambda x: x[1])
            least_frequent_item = min(number_counts.items(), key=lambda x: x[1])
            most_frequent_number = most_frequent_item[0]
            most_frequent_count = most_frequent_item[1]
            least_frequent_number = least_frequent_item[0]
            least_frequent_count = least_frequent_item[1]
        else:
            most_frequent_number = "-"
            most_frequent_count = 0
            least_frequent_number = "-"
            least_frequent_count = 0
        
        return {
            'total_draws': total_draws,
            'date_range': date_range,
            'most_frequent_number': most_frequent_number,
            'most_frequent_count': most_frequent_count,
            'least_frequent_number': least_frequent_number,
            'least_frequent_count': least_frequent_count,
            'number_frequency': number_counts
        }
        
    except Exception as e:
        return {
            'total_draws': 0,
            'date_range': "無資料",
            'most_frequent_number': "-",
            'least_frequent_number': "-",
            'number_frequency': {}
        }

def get_lottery_name(lottery_type):
    """
    獲取彩票類型中文名稱
    
    Args:
        lottery_type: 彩票類型代碼
        
    Returns:
        中文名稱
    """
    names = {
        'powercolor': '威力彩',
        'lotto649': '大樂透',
        'dailycash': '今彩539'
    }
    return names.get(lottery_type, lottery_type)

@app.errorhandler(404)
def not_found_error(error):
    return render_template('error.html', 
                         error_code=404, 
                         error_message='頁面不存在'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('error.html', 
                         error_code=500, 
                         error_message='服務器內部錯誤'), 500

if __name__ == '__main__':
    try:
        # 初始化組件
        initialize_components()
        
        # 啟動應用
        print("=== 增強彩票預測系統 Web 應用 ===")
        print("系統特色：")
        print("• 基於歷史成功率的預測評估")
        print("• 自動選擇最佳預測方法")
        print("• 減少預測選項，提高準確性")
        print("• 實時性能監控")
        print("="*50)
        
        app.run(host='0.0.0.0', port=5002, debug=False)
        
    except Exception as e:
        print(f"應用啟動失敗：{str(e)}")
        traceback.print_exc()