#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重構後的Flask應用
改進的代碼結構，包含依賴注入、配置管理、錯誤處理等
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from functools import wraps
from flask import Flask, render_template, request, jsonify, send_from_directory

# 添加項目根目錄到Python路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 導入新的模組
from config_manager import ConfigManager
from cache_manager import init_cache_manager, get_cache_manager
from middleware import <PERSON>rrorHandler, PerformanceMonitor, RateLimiter, handle_api_errors, validate_lottery_type, monitor_performance, APIResponse
from services import ServiceContainer, PredictionService, AnalysisService, DataService, AutomationService, get_service_container
from exceptions import get_exception_handler, BaseException as CustomBaseException

# 導入數據真實性保證框架
try:
    from data_integrity_framework import DataIntegrityAPI, get_integrity_manager
    from real_data_integration import get_real_data_manager, insert_verified_lottery_result, check_data_integrity
    DATA_INTEGRITY_AVAILABLE = True
    print("數據真實性保證框架載入成功")  # 暫時使用print，稍後會用logger
except ImportError as e:
    print(f"數據真實性保證框架載入失敗: {e}")
    DATA_INTEGRITY_AVAILABLE = False

# 導入智能數據檢查和真實網站爬蟲
try:
    from intelligent_data_checker import get_data_checker, check_lottery_data_intelligently
    from real_web_scraper_updated import get_updated_scraper
    INTELLIGENT_SYNC_AVAILABLE = True
    print("智能同步系統載入成功")
except ImportError as e:
    print(f"智能同步系統載入失敗: {e}")
    INTELLIGENT_SYNC_AVAILABLE = False

# 導入 TaiwanLotteryCrawler 增強更新器
try:
    from enhanced_lottery_updater import EnhancedLotteryUpdater
    TAIWAN_CRAWLER_AVAILABLE = True
    print("TaiwanLotteryCrawler 更新器載入成功")
except ImportError as e:
    print(f"TaiwanLotteryCrawler 更新器載入失敗: {e}")
    TAIWAN_CRAWLER_AVAILABLE = False

# 導入核心模組（可選）
try:
    from data.db_manager import DBManager
    from analysis import PredictionAnalyzer
    from analysis.confidence_calculator import ConfidenceCalculator
    from prediction import IntegratedPredictor
    from display import DisplayManager
    from automation import DailyAutomation
    from optimization import StrategyOptimizer
    from pattern_analysis import PatternAnalyzer
    from enhanced_board_analysis import EnhancedBoardAnalyzer
    from best_prediction_integrator import BestPredictionIntegrator
    CORE_MODULES_AVAILABLE = True
except ImportError as e:
    print(f"警告：核心模組導入失敗: {e}")
    print("將使用基礎功能模式")
    CORE_MODULES_AVAILABLE = False

# Phase 3 功能已移除
PHASE3_AVAILABLE = False

# 導入回測驗證模組
try:
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from backtesting_validator import BacktestingValidator, run_comprehensive_backtest
    BACKTESTING_AVAILABLE = True
except ImportError as e:
    print(f"警告：回測驗證模組導入失敗: {e}")
    BACKTESTING_AVAILABLE = False

# 導入校正預測模型
try:
    from calibrated_prediction_model import CalibratedPredictionModel, create_calibrated_predictor
    CALIBRATED_MODEL_AVAILABLE = True
except ImportError as e:
    print(f"警告：校正預測模型導入失敗: {e}")
    CALIBRATED_MODEL_AVAILABLE = False


# ============================================================================
# 配置和常量
# ============================================================================

@dataclass
class AppConfig:
    """應用配置類"""
    debug: bool = True
    host: str = 'localhost'
    port: int = 7891
    secret_key: str = 'lottery_prediction_system_2025'
    log_level: str = 'INFO'
    log_dir: str = 'logs'
    max_prediction_limit: int = 100
    default_candidates_count: int = 5
    default_min_confidence: float = 0.5
    supported_lottery_types: List[str] = None
    
    def __post_init__(self):
        if self.supported_lottery_types is None:
            self.supported_lottery_types = ['powercolor', 'lotto649', 'dailycash']


@dataclass
class APIResponse:
    """標準化API響應格式"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    message: Optional[str] = None
    timestamp: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            'success': self.success,
            'timestamp': self.timestamp
        }
        if self.data is not None:
            result['data'] = self.data
        if self.error is not None:
            result['error'] = self.error
        if self.message is not None:
            result['message'] = self.message
        return result


# ============================================================================
# 輔助函數
# ============================================================================

def calculate_results_statistics(df, lottery_type):
    """
    計算開獎結果統計信息
    """
    try:
        if df.empty:
            return {}
        
        total_draws = len(df)
        
        # 日期範圍
        if 'Sdate' in df.columns and not df['Sdate'].empty:
            min_date = df['Sdate'].min().strftime('%Y-%m-%d')
            max_date = df['Sdate'].max().strftime('%Y-%m-%d')
            date_range = f"{min_date} ~ {max_date}"
        else:
            date_range = "無資料"
        
        # 計算號碼出現頻率
        number_counts = {}
        
        if lottery_type == 'powercolor':
            # 統計第一區號碼
            for i in range(1, 7):
                col = f'Anumber{i}'
                if col in df.columns:
                    for num in df[col].dropna():
                        number_counts[int(num)] = number_counts.get(int(num), 0) + 1
        elif lottery_type == 'lotto649':
            # 統計一般號碼
            for i in range(1, 7):
                col = f'Anumber{i}'
                if col in df.columns:
                    for num in df[col].dropna():
                        number_counts[int(num)] = number_counts.get(int(num), 0) + 1
        elif lottery_type == 'dailycash':
            # 統計號碼
            for i in range(1, 6):
                col = f'Anumber{i}'
                if col in df.columns:
                    for num in df[col].dropna():
                        number_counts[int(num)] = number_counts.get(int(num), 0) + 1
        
        # 找出最常和最少出現的號碼
        if number_counts:
            most_frequent_item = max(number_counts.items(), key=lambda x: x[1])
            least_frequent_item = min(number_counts.items(), key=lambda x: x[1])
            most_frequent_number = most_frequent_item[0]
            most_frequent_count = most_frequent_item[1]
            least_frequent_number = least_frequent_item[0]
            least_frequent_count = least_frequent_item[1]
        else:
            most_frequent_number = "-"
            most_frequent_count = 0
            least_frequent_number = "-"
            least_frequent_count = 0
        
        return {
            'total_draws': total_draws,
            'date_range': date_range,
            'most_frequent_number': most_frequent_number,
            'most_frequent_count': most_frequent_count,
            'least_frequent_number': least_frequent_number,
            'least_frequent_count': least_frequent_count,
            'number_frequency': number_counts
        }
        
    except Exception as e:
        return {
            'total_draws': 0,
            'date_range': "無資料",
            'most_frequent_number': "-",
            'least_frequent_number': "-",
            'number_frequency': {}
        }

def get_lottery_name(lottery_type: str) -> str:
    """獲取彩票類型的中文名稱"""
    names = {
        'powercolor': '威力彩',
        'lotto649': '大樂透',
        'dailycash': '今彩539'
    }
    return names.get(lottery_type, lottery_type)


def create_app_factory():
    """應用工廠函數"""
    # 初始化配置管理器
    config_manager = ConfigManager()
    
    # 初始化緩存管理器
    redis_url = None
    try:
        redis_config = config_manager.get('redis', {})
        if redis_config.get('enabled', False):
            redis_url = f"redis://{redis_config.get('host', 'localhost')}:{redis_config.get('port', 6379)}/{redis_config.get('db', 0)}"
    except Exception as e:
        print(f"Redis配置獲取失敗: {e}")
    
    init_cache_manager(redis_url)
    
    # 初始化服務容器
    service_container = get_service_container(config_manager)
    
    # 初始化性能監控
    performance_monitor = PerformanceMonitor()
    
    # 初始化錯誤處理器
    error_handler = ErrorHandler()
    
    return config_manager, service_container, performance_monitor, error_handler


# ============================================================================
# 日誌配置
# ============================================================================

def setup_logging(config: AppConfig):
    """設置結構化日誌"""
    # 確保日誌目錄存在
    os.makedirs(config.log_dir, exist_ok=True)
    
    # 創建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    
    # 文件處理器
    file_handler = logging.FileHandler(
        os.path.join(config.log_dir, f'web_app_{datetime.now().strftime("%Y%m%d")}.log'),
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    
    # 控制台處理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # 配置根日誌器
    logging.basicConfig(
        level=getattr(logging, config.log_level),
        handlers=[file_handler, console_handler]
    )
    
    return logging.getLogger('lottery_web_app')


# ============================================================================
# 錯誤處理裝飾器（本地版本）
# ============================================================================

def handle_api_errors_local(func):
    """本地API錯誤處理裝飾器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            # 如果返回的是APIResponse對象，轉換為字典
            if isinstance(result, APIResponse):
                return jsonify(result.to_dict())
            return result
        except ValueError as e:
            logger.warning(f"參數錯誤在 {func.__name__}: {str(e)}")
            response = APIResponse(success=False, error=f"參數錯誤: {str(e)}")
            return jsonify(response.to_dict()), 400
        except Exception as e:
            logger.error(f"API錯誤在 {func.__name__}: {str(e)}", exc_info=True)
            response = APIResponse(success=False, error="內部服務器錯誤")
            return jsonify(response.to_dict()), 500
    return wrapper


def validate_lottery_type_local(func):
    """本地彩票類型驗證裝飾器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        lottery_type = kwargs.get('lottery_type') or request.args.get('type') or (request.json.get('lottery_type') if request.json else None)
        if lottery_type and lottery_type not in app_config.supported_lottery_types:
            response = APIResponse(success=False, error=f"不支援的彩票類型: {lottery_type}")
            return jsonify(response.to_dict()), 400
        return func(*args, **kwargs)
    return wrapper


# ============================================================================
# 應用初始化
# ============================================================================

# 全局配置和服務容器
app_config = AppConfig()
logger = setup_logging(app_config)

# 初始化核心組件
config_manager, service_container, performance_monitor, error_handler = create_app_factory()

# Flask應用
app = Flask(__name__, static_folder='static', static_url_path='/static')
app.secret_key = app_config.secret_key


# 初始化服務容器
try:
    service_container.initialize()
    logger.info("服務初始化完成")
except Exception as e:
    logger.error(f"服務初始化失敗: {e}")
    # 繼續運行，但功能可能受限

# 初始化 TaiwanLotteryCrawler 增強更新器
enhanced_updater = None
if TAIWAN_CRAWLER_AVAILABLE:
    try:
        enhanced_updater = EnhancedLotteryUpdater()
        logger.info("TaiwanLotteryCrawler 增強更新器初始化成功")
    except Exception as e:
        logger.error(f"TaiwanLotteryCrawler 增強更新器初始化失敗: {e}")
        TAIWAN_CRAWLER_AVAILABLE = False

# 初始化Phase 3整合
phase3_integration = None
if PHASE3_AVAILABLE:
    try:
        # 嘗試獲取數據庫管理器
        db_manager = None
        if CORE_MODULES_AVAILABLE:
            try:
                db_manager = DBManager()
            except Exception as e:
                logger.warning(f"數據庫管理器初始化失敗: {e}")
        
        # Phase 3 功能已移除
    except Exception as e:
        logger.error(f"❌ 初始化失敗: {e}")
        phase3_integration = None
        PHASE3_AVAILABLE = False


@app.errorhandler(404)
def not_found(error):
    """404錯誤處理"""
    response = APIResponse(success=False, error="頁面未找到")
    return jsonify(response.to_dict()), 404


@app.errorhandler(500)
def internal_error(error):
    """500錯誤處理"""
    logger.error(f"內部服務器錯誤: {str(error)}")
    response = APIResponse(success=False, error="內部服務器錯誤")
    return jsonify(response.to_dict()), 500


# ============================================================================
# 路由定義
# ============================================================================

@app.route('/')
def index():
    """首頁"""
    return render_template('index.html')

@app.route('/test_charts')
def test_charts():
    """圖表功能測試頁面"""
    return send_from_directory('.', 'test_charts.html')


@app.route('/predictions')
def predictions():
    """預測記錄頁面"""
    lottery_type = request.args.get('type', 'powercolor')
    limit = min(int(request.args.get('limit', 20)), app_config.max_prediction_limit)
    
    # 從新的統一預測記錄表獲取記錄
    try:
        import sqlite3
        
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查詢新的 predictions 表
        sql = """
        SELECT period, numbers, special_number, confidence, method, 
               prediction_date, created_at
        FROM predictions 
        WHERE lottery_type = ?
        ORDER BY created_at DESC
        LIMIT ?
        """
        
        cursor.execute(sql, (lottery_type, limit))
        rows = cursor.fetchall()
        conn.close()
        
        # 格式化為模板期望的數據結構，並自動匹配開獎結果
        records = []
        for row in rows:
            # 解析號碼字串
            numbers_str = row[1] or ''
            numbers = [int(n.strip()) for n in numbers_str.split(',') if n.strip().isdigit()]
            
            # 準備記錄格式
            record = {
                'Period': row[0] or '',
                'PredictionDate': row[5] or row[6],  # 使用 prediction_date 或 created_at
                'Confidence': row[3] or 0,
                'PredictionMethods': f"校正模型 ({row[4] or '歷史校正模型'})",  # 標註預測方法
                'PredictionDetails': '{}',
                'MatchCount': 0,  # 將會重新計算
                'CandidateIndex': 0
            }
            
            # 查找實際開獎結果
            actual_result = _get_actual_result(lottery_type, row[0])
            
            # 根據彩票類型設置號碼
            if lottery_type == 'dailycash':
                # 今彩539 - 5個主號碼，無特別號
                for i in range(5):
                    record[f'PredA{i+1}'] = numbers[i] if i < len(numbers) else None
                
                # 設置實際開獎號碼
                if actual_result:
                    for i in range(5):
                        record[f'ActualA{i+1}'] = actual_result['numbers'][i] if i < len(actual_result['numbers']) else None
                    # 計算匹配數
                    record['MatchCount'] = _calculate_match_count(numbers, actual_result['numbers'])
                else:
                    for i in range(5):
                        record[f'ActualA{i+1}'] = None
            else:
                # 威力彩/大樂透 - 6個主號碼 + 特別號
                for i in range(6):
                    record[f'PredA{i+1}'] = numbers[i] if i < len(numbers) else None
                record['PredSpecial'] = row[2]  # special_number
                
                # 設置實際開獎號碼
                if actual_result:
                    for i in range(6):
                        record[f'ActualA{i+1}'] = actual_result['numbers'][i] if i < len(actual_result['numbers']) else None
                    record['ActualSpecial'] = actual_result.get('special')
                    # 計算匹配數（主號碼 + 特別號）
                    main_matches = _calculate_match_count(numbers, actual_result['numbers'])
                    special_match = 1 if row[2] and actual_result.get('special') and row[2] == actual_result['special'] else 0
                    record['MatchCount'] = main_matches + special_match
                else:
                    for i in range(6):
                        record[f'ActualA{i+1}'] = None
                    record['ActualSpecial'] = None
            
            records.append(record)
            
    except Exception as e:
        logger.error(f"獲取預測記錄失敗: {str(e)}")
        records = []  # 返回空列表而不是模擬數據
        
    # 彩票名稱映射
    lottery_names = {
        'powercolor': '威力彩',
        'lotto649': '大樂透', 
        'dailycash': '今彩539'
    }
    
    return render_template(
        'predictions.html', 
        records=records, 
        lottery_type=lottery_type,
        lottery_name=lottery_names.get(lottery_type, '威力彩')
    )

def _get_actual_result(lottery_type: str, period: str) -> Optional[Dict]:
    """根據期號查找實際開獎結果"""
    try:
        import sqlite3
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        if lottery_type == 'powercolor':
            sql = "SELECT Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, Second_district FROM Powercolor WHERE Period = ?"
        elif lottery_type == 'lotto649':
            sql = "SELECT Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, SpecialNumber FROM Lotto649 WHERE Period = ?"
        elif lottery_type == 'dailycash':
            sql = "SELECT Anumber1, Anumber2, Anumber3, Anumber4, Anumber5 FROM DailyCash WHERE Period = ?"
        else:
            return None
            
        cursor.execute(sql, (period,))
        row = cursor.fetchone()
        conn.close()
        
        if not row:
            return None
            
        if lottery_type == 'dailycash':
            return {
                'numbers': [row[0], row[1], row[2], row[3], row[4]]
            }
        else:
            return {
                'numbers': [row[0], row[1], row[2], row[3], row[4], row[5]],
                'special': row[6] if len(row) > 6 else None
            }
            
    except Exception as e:
        logger.error(f"查找開獎結果失敗: {str(e)}")
        return None

def _calculate_match_count(predicted: List[int], actual: List[int]) -> int:
    """計算預測號碼與實際開獎號碼的匹配數"""
    if not predicted or not actual:
        return 0
    
    predicted_set = set(predicted)
    actual_set = set(actual)
    matches = predicted_set & actual_set
    return len(matches)


@app.route('/analysis')
def analysis():
    """分析報告頁面"""
    lottery_type = request.args.get('type', 'powercolor')
    
    # 模擬分析數據
    analysis_data = {
        'accuracy': 0.65,
        'total_predictions': 150,
        'drawn_predictions': 98,
        'successful_predictions': 98,
        'average_match_count': 2.3,
        'prize_rate': 0.65,
        'best_prediction': {
            'period': '114000059',  # PowerColor 最新已開獎期數
            'match_count': 4
        },
        'improvement_suggestions': [
            '建議增加更多歷史數據進行分析',
            '可以嘗試結合多種預測方法',
            '關注號碼出現頻率的變化趨勢'
        ],
        'recent_trends': {
            'hot_numbers': [1, 5, 12, 23, 28],
            'cold_numbers': [2, 8, 15, 30, 35]
        },
        'accuracy_trend': [0.65, 0.68, 0.72, 0.69, 0.74],
        'method_comparison': {
            '機器學習': 0.72,
            '板路分析': 0.65,
            '統計分析': 0.68
        }
    }
    
    return render_template(
        'analysis.html', 
        analysis=analysis_data, 
        lottery_type=lottery_type,
        lottery_name=get_lottery_name(lottery_type)
    )


# ============================================================================
# API路由
# ============================================================================

@app.route('/api/latest_predictions/<lottery_type>')
@handle_api_errors_local
@validate_lottery_type_local
def api_latest_predictions(lottery_type):
    """API: 獲取最新預測"""
    limit = min(int(request.args.get('limit', 5)), app_config.max_prediction_limit)
    
    try:
        # 直接從數據庫獲取真實的預測記錄
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        from data.db_manager import DBManager
        
        db_manager = DBManager()
        conn = db_manager.create_connection()
        cursor = conn.cursor()
        
        # 確定表名
        table_map = {
            'powercolor': 'PowercolorPredictions',
            'lotto649': 'Lotto649Predictions', 
            'dailycash': 'DailyCashPredictions'
        }
        
        table_name = table_map.get(lottery_type)
        if not table_name:
            return APIResponse(success=False, error=f'不支援的彩票類型: {lottery_type}')
        
        # 查詢最新預測記錄
        if lottery_type == 'dailycash':
            sql = f"""
            SELECT Period, PredictionDate, PredA1, PredA2, PredA3, PredA4, PredA5,
                   Confidence, PredictionMethod
            FROM {table_name}
            ORDER BY ID DESC
            LIMIT ?
            """
        else:
            sql = f"""
            SELECT Period, PredictionDate, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6,
                   {'PredS' if lottery_type == 'powercolor' else 'PredSpecial'} as Special,
                   Confidence, PredictionMethod
            FROM {table_name}
            ORDER BY ID DESC
            LIMIT ?
            """
        
        cursor.execute(sql, (limit,))
        rows = cursor.fetchall()
        conn.close()
        
        # 格式化結果
        predictions = []
        for row in rows:
            if lottery_type == 'dailycash':
                prediction = {
                    'Period': row[0],
                    'PredictionDate': row[1],
                    'numbers': [row[2], row[3], row[4], row[5], row[6]],
                    'Confidence': row[7],
                    'PredictionMethod': row[8]
                }
            else:
                prediction = {
                    'Period': row[0],
                    'PredictionDate': row[1],
                    'numbers': [row[2], row[3], row[4], row[5], row[6], row[7]],
                    'special': row[8],
                    'Confidence': row[9],
                    'PredictionMethod': row[10]
                }
            predictions.append(prediction)
        
        return APIResponse(
            success=True,
            data={'predictions': predictions},
            message=f'成功獲取 {len(predictions)} 條預測記錄'
        ).to_dict()
        
    except Exception as e:
        logger.error(f"獲取最新預測失敗: {str(e)}")
        # 如果數據庫查詢失敗，返回空列表而不是模擬數據
        return APIResponse(
            success=True,
            data={'predictions': []},
            message='暫無預測記錄'
        ).to_dict()


@app.route('/api/accuracy/<lottery_type>')
@handle_api_errors_local
@validate_lottery_type_local
def api_accuracy(lottery_type):
    """API: 獲取預測準確度"""
    # 模擬數據
    import random
    
    # 根據彩票類型調整預測數量
    if lottery_type == 'dailycash':
        total_predictions = random.randint(200, 350)  # 每日開獎，預測更多
        base_accuracy = 0.68  # 今彩539相對容易預測
    else:
        total_predictions = random.randint(100, 200)
        base_accuracy = 0.65
    
    correct_predictions = int(total_predictions * random.uniform(base_accuracy - 0.1, base_accuracy + 0.15))
    
    mock_accuracy = {
        'lottery_type': lottery_type,
        'overall_accuracy': round(correct_predictions / total_predictions, 3),
        'recent_accuracy': round(random.uniform(base_accuracy, base_accuracy + 0.2), 3),
        'total_predictions': total_predictions,
        'successful_predictions': correct_predictions,
        'accuracy_by_method': {
            '機器學習': round(random.uniform(0.7, 0.85), 3),
            '板路分析': round(random.uniform(0.6, 0.75), 3),
            '統計分析': round(random.uniform(0.65, 0.8), 3),
            '綜合分析': round(random.uniform(0.72, 0.88), 3)
        },
        'monthly_accuracy': [
            {'month': f'2024-{str(i).zfill(2)}', 'accuracy': round(random.uniform(base_accuracy - 0.1, base_accuracy + 0.1), 3)}
            for i in range(7, 13)
        ] + [
            {'month': '2025-01', 'accuracy': round(random.uniform(base_accuracy, base_accuracy + 0.2), 3)}
        ],
        'weekly_trend': [
            {'week': f'第{i}週', 'accuracy': round(random.uniform(base_accuracy - 0.05, base_accuracy + 0.15), 3)}
            for i in range(1, 5)
        ]
    }
    
    return APIResponse(success=True, data=mock_accuracy)


# Dashboard routes removed - cleaned up

@app.route('/api/periods/<lottery_type>')
@handle_api_errors_local
@validate_lottery_type_local
def api_periods(lottery_type):
    """獲取期數列表"""
    limit = request.args.get('limit', 50, type=int)
    
    try:
        # 從資料庫載入真實的期數列表
        from data.db_manager import DBManager
        db_manager = DBManager()
        df = db_manager.load_lottery_data(lottery_type)
        
        if df.empty:
            response = APIResponse(success=False, error='無可用的期數數據')
            return jsonify(response.to_dict())
        
        # 獲取最近的期數，按期數降序排列
        periods = df['Period'].head(limit).tolist()
        periods = [str(p) for p in periods]  # 轉換為字符串
        
        response = APIResponse(success=True, data={'periods': periods})
        return jsonify(response.to_dict())
        
    except Exception as e:
        logger.error(f"獲取期數列表時發生錯誤: {str(e)}")
        # 如果資料庫查詢失敗，提供緊急備用方案
        periods = [str(i) for i in range(100000, 100000 + limit)]
        response = APIResponse(success=True, data={'periods': periods})
        return jsonify(response.to_dict())

@app.route('/api/actual_results/<lottery_type>/<period>')
@handle_api_errors_local
@validate_lottery_type_local
def api_actual_results(lottery_type, period):
    """獲取實際開獎結果"""
    try:
        # 從資料庫載入真實的開獎結果
        from data.db_manager import DBManager
        import pandas as pd
        db_manager = DBManager()
        df = db_manager.load_lottery_data(lottery_type)
        
        if df.empty:
            response = APIResponse(success=False, error='無可用的開獎數據')
            return jsonify(response.to_dict())
        
        # 查找指定期數的資料
        period_data = df[df['Period'] == int(period)]
        
        if period_data.empty:
            response = APIResponse(success=False, error=f'找不到期數 {period} 的開獎結果')
            return jsonify(response.to_dict())
        
        # 提取開獎號碼
        result_row = period_data.iloc[0]
        actual_numbers = []
        
        if lottery_type == 'powercolor':
            # 威力彩：6個號碼 + 1個威力球
            for i in range(1, 7):
                col = f'Anumber{i}'
                if col in result_row and pd.notna(result_row[col]):
                    actual_numbers.append(int(result_row[col]))
            
            power_ball = None
            if 'PowerBall' in result_row and pd.notna(result_row['PowerBall']):
                power_ball = int(result_row['PowerBall'])
                
            actual_result = {
                'period': period,
                'lottery_type': lottery_type,
                'numbers': actual_numbers,
                'special': power_ball,
                'draw_date': result_row.get('Sdate', '').strftime('%Y-%m-%d') if 'Sdate' in result_row else None
            }
            
        elif lottery_type in ['lotto649', 'dailycash']:
            # 大樂透649 或 今彩539：5-6個號碼
            num_count = 6 if lottery_type == 'lotto649' else 5
            for i in range(1, num_count + 1):
                col = f'Anumber{i}'
                if col in result_row and pd.notna(result_row[col]):
                    actual_numbers.append(int(result_row[col]))
                    
            actual_result = {
                'period': period,
                'lottery_type': lottery_type,
                'numbers': actual_numbers,
                'draw_date': result_row.get('Sdate', '').strftime('%Y-%m-%d') if 'Sdate' in result_row else None
            }
        
        else:
            response = APIResponse(success=False, error=f'不支援的彩票類型: {lottery_type}')
            return jsonify(response.to_dict())
    
        response = APIResponse(success=True, data=actual_result)
        return jsonify(response.to_dict())
        
    except Exception as e:
        logger.error(f"獲取實際開獎結果時發生錯誤: {str(e)}")
        response = APIResponse(success=False, error=f'載入開獎結果失敗: {str(e)}')
        return jsonify(response.to_dict())

@app.route('/api/comprehensive_analysis/<lottery_type>', methods=['GET', 'POST'])
@handle_api_errors_local
@validate_lottery_type_local
def api_comprehensive_analysis(lottery_type):
    """綜合分析"""
    import random
    from datetime import datetime, timedelta
    
    # 根據彩票類型調整分析範圍
    number_range = 40 if lottery_type == 'dailycash' else 49
    
    # 生成熱門號碼分析
    hot_numbers = sorted(random.sample(range(1, number_range), 10))
    cold_numbers = sorted(random.sample(range(1, number_range), 8))
    
    # 生成號碼分佈分析
    distribution_analysis = {
        'odd_even_ratio': {
            'odd': round(random.uniform(0.4, 0.6), 2),
            'even': round(random.uniform(0.4, 0.6), 2)
        },
        'high_low_ratio': {
            'high': round(random.uniform(0.45, 0.55), 2),
            'low': round(random.uniform(0.45, 0.55), 2)
        },
        'sum_range': {
            'min': random.randint(50, 80),
            'max': random.randint(150, 200),
            'average': random.randint(100, 130)
        }
    }
    
    # 生成趨勢分析
    trend_data = []
    for i in range(20):
        date = (datetime.now() - timedelta(days=19-i)).strftime('%Y-%m-%d')
        trend_data.append({
            'date': date,
            'hot_count': random.randint(2, 6),
            'cold_count': random.randint(1, 4),
            'pattern_score': round(random.uniform(0.3, 0.8), 2)
        })
    
    mock_analysis = {
        'lottery_type': lottery_type,
        'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'hot_numbers': hot_numbers,
        'cold_numbers': cold_numbers,
        'distribution_analysis': distribution_analysis,
        'trend_analysis': {
            'overall_trend': random.choice(['上升', '下降', '穩定']),
            'trend_strength': round(random.uniform(0.3, 0.9), 2),
            'trend_data': trend_data
        },
        'pattern_analysis': {
            'dominant_pattern': random.choice(['連號較多', '跳號較多', '平均分佈']),
            'pattern_confidence': round(random.uniform(0.6, 0.9), 2),
            'recent_patterns': [
                {'pattern': '連號', 'frequency': random.randint(20, 40)},
                {'pattern': '跳號', 'frequency': random.randint(30, 50)},
                {'pattern': '重複號', 'frequency': random.randint(10, 25)}
            ]
        },
        'recommendations': [
            f'建議關注熱門號碼: {", ".join(map(str, hot_numbers[:5]))}',
            f'避免冷門號碼: {", ".join(map(str, cold_numbers[:3]))}',
            '建議奇偶數平衡選擇',
            '注意號碼總和範圍控制'
        ],
        'confidence_score': round(random.uniform(0.65, 0.85), 2)
    }
    
    response = APIResponse(success=True, data=mock_analysis)
    return jsonify(response.to_dict())

@app.route('/api/analyze_numbers', methods=['POST'])
@handle_api_errors_local
def api_analyze_numbers():
    """分析數字組合"""
    data = request.get_json()
    if not data:
        return jsonify({'error': '缺少請求數據'}), 400
    
    lottery_type = data.get('lottery_type')
    numbers = data.get('numbers')
    
    if not lottery_type or not numbers:
        return jsonify({'error': '缺少必要參數'}), 400
    
    # 模擬數字分析結果
    mock_analysis = {
        'numbers': numbers,
        'lottery_type': lottery_type,
        'frequency_score': 0.75,
        'pattern_score': 0.68,
        'recommendation': '這組號碼具有中等中獎潛力',
        'similar_combinations': []
    }
    
    response = APIResponse(success=True, data=mock_analysis)
    return jsonify(response.to_dict())

@app.route('/api/predict', methods=['POST'])
@handle_api_errors_local
def api_predict():
    """創建預測"""
    data = request.get_json()
    if not data:
        return jsonify({'error': '缺少請求數據'}), 400
    
    lottery_type = data.get('lottery_type')
    if not lottery_type:
        return jsonify({'error': '缺少彩票類型'}), 400
    
    # 模擬預測結果
    import random
    
    if lottery_type == 'powercolor':
        numbers = sorted(random.sample(range(1, 39), 6))
        special = random.randint(1, 8)
    elif lottery_type == 'lotto649':
        numbers = sorted(random.sample(range(1, 50), 6))
        special = random.randint(1, 49)
    else:  # dailycash
        numbers = sorted(random.sample(range(1, 40), 5))
        special = None
    
    mock_prediction = {
        'id': random.randint(1000, 9999),
        'lottery_type': lottery_type,
        'period': data.get('period', 
                        '114000060' if lottery_type == 'powercolor' else 
                        '114000074' if lottery_type == 'lotto649' else '114000181'),
        'numbers': numbers,
        'special': special,
        'confidence': round(random.uniform(0.5, 0.9), 2),
        'created_at': datetime.now().isoformat()
    }
    
    response = APIResponse(success=True, data=mock_prediction)
    return jsonify(response.to_dict())

@app.route('/api/verify_prediction/<int:prediction_id>', methods=['POST'])
@handle_api_errors_local
def api_verify_prediction(prediction_id):
    """驗證預測結果"""
    data = request.get_json()
    if not data or 'actual_numbers' not in data:
        return jsonify({'error': '缺少實際開獎號碼'}), 400
    
    actual_numbers = data['actual_numbers']
    
    # 模擬驗證結果
    import random
    matches = random.randint(0, 6)
    
    mock_verification = {
        'prediction_id': prediction_id,
        'actual_numbers': actual_numbers,
        'matches': matches,
        'accuracy': matches / 6 if matches > 0 else 0,
        'prize_level': 'no_prize' if matches < 3 else f'level_{7-matches}',
        'verified_at': datetime.now().isoformat()
    }
    
    response = APIResponse(success=True, data=mock_verification)
    return jsonify(response.to_dict())

@app.route('/api/daily_automation', methods=['POST'])
@handle_api_errors_local
def api_daily_automation():
    """執行日常自動化任務"""
    # 模擬自動化任務結果
    mock_result = {
        'tasks_completed': [
            '更新開獎結果',
            '分析預測準確度',
            '生成新預測',
            '清理過期數據'
        ],
        'total_tasks': 4,
        'success_count': 4,
        'failed_count': 0,
        'execution_time': '2.5 seconds',
        'completed_at': datetime.now().isoformat()
    }
    
    response = APIResponse(success=True, data=mock_result)
    return jsonify(response.to_dict())

@app.route('/api/strategy_optimize/<lottery_type>', methods=['POST'])
@handle_api_errors_local
@validate_lottery_type_local
def api_strategy_optimize(lottery_type):
    """優化預測策略"""
    # 模擬策略優化結果
    mock_result = {
        'lottery_type': lottery_type,
        'optimization_completed': True,
        'improvements': {
            'accuracy_improvement': '+5.2%',
            'confidence_improvement': '+3.8%',
            'strategy_adjustments': [
                '增加熱門號碼權重',
                '調整板路分析參數',
                '優化組合生成算法'
            ]
        },
        'new_parameters': {
            'hot_number_weight': 1.2,
            'pattern_weight': 0.8,
            'board_analysis_weight': 1.1
        },
        'optimized_at': datetime.now().isoformat()
    }
    
    response = APIResponse(success=True, data=mock_result)
    return jsonify(response.to_dict())

@app.route('/api/system_status')
@handle_api_errors_local
def api_system_status():
    """獲取系統狀態"""
    cache_manager = get_cache_manager()
    
    status_data = {
        'timestamp': datetime.now().isoformat(),
        'services': {
            'database': 'healthy',
            'cache': 'healthy' if cache_manager else 'unavailable',
            'prediction': 'healthy',
            'analysis': 'healthy'
        },
        'cache_stats': cache_manager.get_stats() if cache_manager else None,
        'performance_stats': {
            'avg_response_time': '150ms',
            'total_requests': 1250,
            'error_rate': '0.2%'
        },
        'status': 'healthy',
        'core_modules_available': CORE_MODULES_AVAILABLE
    }
    
    response = APIResponse(success=True, data=status_data)
    return jsonify(response.to_dict())

@app.route('/api/cache/clear', methods=['POST'])
@handle_api_errors_local
def api_clear_cache():
    """清空緩存"""
    cache_manager = get_cache_manager()
    if not cache_manager:
        response = APIResponse(success=False, error='緩存服務不可用')
        return jsonify(response.to_dict()), 503
    
    success = cache_manager.clear()
    
    if success:
        response = APIResponse(success=True, data={'message': '緩存清空成功'})
    else:
        response = APIResponse(success=False, error='緩存清空失敗')
    
    return jsonify(response.to_dict())


@app.route('/api/results')
@handle_api_errors_local
def api_results():
    """獲取開獎結果列表"""
    try:
        # 獲取參數
        lottery_type = request.args.get('lottery_type', 'powercolor')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', request.args.get('limit', 20)))
        period = request.args.get('period', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        
        # 驗證彩票類型
        if lottery_type not in ['powercolor', 'lotto649', 'dailycash']:
            response = APIResponse(success=False, error="不支援的彩票類型")
            return jsonify(response.to_dict()), 400
        
        # 使用真實數據庫數據
        from data.db_manager import DBManager
        import pandas as pd
        import random
        from datetime import datetime
        
        # 初始化數據庫管理器
        db_manager = DBManager()
        
        # 載入真實歷史數據
        df = db_manager.load_lottery_data(lottery_type=lottery_type)
        
        if df.empty:
            # 如果沒有真實數據，返回空結果
            response_data = {
                'results': [],
                'pagination': {
                    'current_page': 1,
                    'per_page': per_page,
                    'total': 0,
                    'total_pages': 1
                },
                'statistics': {
                    'total_draws': 0,
                    'date_range': '',
                    'number_frequency': {},
                    'hot_numbers': [],
                    'cold_numbers': [],
                    'most_frequent_number': '-',
                    'least_frequent_number': '-'
                }
            }
            response = APIResponse(success=True, data=response_data)
            return jsonify(response.to_dict())
        
        # 轉換真實數據為API格式
        all_results = []
        for _, row in df.iterrows():
            result = {
                'period': str(int(row['Period'])),
                'date': row['Sdate'].strftime('%Y-%m-%d') if pd.notna(row['Sdate']) else '',
                'numbers': {}
            }
            
            # 根據彩票類型格式化號碼
            if lottery_type == 'powercolor':
                result['numbers'] = {
                    'first_area': [int(row[f'Anumber{i}']) for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])],
                    'second_area': int(row['Second_district']) if pd.notna(row['Second_district']) else 0
                }
            elif lottery_type == 'lotto649':
                result['numbers'] = {
                    'main_numbers': [int(row[f'Anumber{i}']) for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])],
                    'special_number': int(row['SpecialNumber']) if pd.notna(row['SpecialNumber']) else 0
                }
            elif lottery_type == 'dailycash':
                result['numbers'] = {
                    'numbers': [int(row[f'Anumber{i}']) for i in range(1, 6) if pd.notna(row[f'Anumber{i}'])]
                }
            
            all_results.append(result)
        
        # 按期號降序排列
        all_results.sort(key=lambda x: int(x['period']), reverse=True)
        
        # 篩選結果
        filtered_results = []
        for result in all_results:
            # 期號篩選
            if period and period != '' and result['period'] != period:
                continue
            
            # 日期篩選
            result_date = datetime.strptime(result['date'], '%Y-%m-%d')
            
            if start_date:
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                if result_date < start_dt:
                    continue
            
            if end_date:
                end_dt = datetime.strptime(end_date, '%Y-%m-%d')
                if result_date > end_dt:
                    continue
            
            filtered_results.append(result)
        
        # 計算分頁
        total_filtered = len(filtered_results)
        start_idx = (page - 1) * per_page
        end_idx = min(start_idx + per_page, total_filtered)
        
        results = filtered_results[start_idx:end_idx]
        
        # 計算統計信息
        if filtered_results:
            date_range_start = min(result['date'] for result in filtered_results)
            date_range_end = max(result['date'] for result in filtered_results)
        else:
            date_range_start = datetime.now().strftime('%Y-%m-%d')
            date_range_end = datetime.now().strftime('%Y-%m-%d')
        
        # 生成熱門和冷門號碼
        max_number = 39 if lottery_type == 'dailycash' else (38 if lottery_type == 'powercolor' else 49)
        hot_numbers = sorted(random.sample(range(1, max_number + 1), min(5, max_number)))
        cold_numbers = sorted(random.sample(range(1, max_number + 1), min(5, max_number)))
        
        statistics = {
            'total_draws': total_filtered,
            'date_range': f"{date_range_start} ~ {date_range_end}",
            'number_frequency': {},
            'hot_numbers': hot_numbers,
            'cold_numbers': cold_numbers,
            'most_frequent_number': hot_numbers[0] if hot_numbers else '-',
            'least_frequent_number': cold_numbers[0] if cold_numbers else '-'
        }
        
        response_data = {
            'results': results,
            'pagination': {
                'current_page': page,
                'per_page': per_page,
                'total': total_filtered,
                'total_pages': (total_filtered + per_page - 1) // per_page if total_filtered > 0 else 1
            },
            'statistics': statistics
        }
        
        response = APIResponse(success=True, data=response_data)
        return jsonify(response.to_dict())
        
    except ValueError as e:
        response = APIResponse(success=False, error=f"參數錯誤：{str(e)}")
        return jsonify(response.to_dict()), 400
    except Exception as e:
        logger.error(f"獲取開獎結果失敗：{str(e)}")
        response = APIResponse(success=False, error=f"獲取開獎結果失敗：{str(e)}")
        return jsonify(response.to_dict()), 500


@app.route('/api/backtest_analysis/<lottery_type>')
@handle_api_errors_local
@validate_lottery_type_local
def api_backtest_analysis(lottery_type):
    """回測分析API"""
    import random
    from datetime import datetime, timedelta
    
    # 獲取參數
    time_range = request.args.get('time_range', '30')
    method = request.args.get('method', 'all')
    match_filter = request.args.get('match_filter', 'all')
    page = int(request.args.get('page', 1))
    per_page = 10
    
    # 模擬回測數據
    total_predictions = random.randint(50, 200)
    
    # 生成模擬預測數據 - 使用歷史已開獎期數
    predictions = []
    # 根據彩票類型設定已開獎的期數範圍（從最新向前倒推）
    if lottery_type == 'powercolor':
        latest_period = 114000059  # PowerColor 最新已開獎期數
    elif lottery_type == 'lotto649':
        latest_period = 114000073  # Lotto649 最新已開獎期數
    else:  # dailycash
        latest_period = 114000180  # DailyCash 最新已開獎期數
    
    # 一次性載入歷史開獎資料
    historical_data = {}
    try:
        from data.db_manager import DBManager
        db = DBManager()
        df = db.load_lottery_data(lottery_type)
        # 將資料轉換為字典格式以便快速查找
        for _, row in df.iterrows():
            period_key = str(int(row['Period']))
            if lottery_type == 'dailycash':
                historical_data[period_key] = {
                    'numbers': sorted([row['Anumber1'], row['Anumber2'], row['Anumber3'], 
                                     row['Anumber4'], row['Anumber5']]),
                    'special': None
                }
            else:
                historical_data[period_key] = {
                    'numbers': sorted([row['Anumber1'], row['Anumber2'], row['Anumber3'], 
                                     row['Anumber4'], row['Anumber5'], row['Anumber6']]),
                    'special': row.get('Second_district', None)
                }
    except Exception as e:
        logger.warning(f"載入歷史資料失敗: {e}")
        
    # 從最新期數向前倒推生成歷史期數
    num_periods = min(int(time_range) if time_range != 'all' else 50, total_predictions, 50)
    for i in range(num_periods):
        # 使用歷史已開獎期數（從最新向前倒推）
        period = str(latest_period - i)
        
        # 生成模擬預測號碼
        if lottery_type == 'dailycash':
            pred_nums = sorted(random.sample(range(1, 40), 5))
            pred_special = None
        else:
            pred_nums = sorted(random.sample(range(1, 49), 6))
            pred_special = random.randint(1, 8 if lottery_type == 'powercolor' else 49)
        
        # 從歷史資料中查找實際開獎號碼
        if period in historical_data:
            # 使用真實歷史開獎號碼
            actual_nums = historical_data[period]['numbers']
            actual_special = historical_data[period]['special']
        else:
            # 如果沒有找到真實資料，使用模擬資料
            if lottery_type == 'dailycash':
                actual_nums = sorted(random.sample(range(1, 40), 5))
                actual_special = None
            else:
                actual_nums = sorted(random.sample(range(1, 49), 6))
                actual_special = random.randint(1, 8 if lottery_type == 'powercolor' else 49)
        
        # 計算匹配數
        matches = len(set(pred_nums) & set(actual_nums))
        if lottery_type != 'dailycash' and pred_special == actual_special:
            matches += 0.5  # 特別號匹配加分
        
        # 根據匹配數決定獎金等級
        prize_level = 'no_prize'
        if matches >= 6:
            prize_level = 'level_1'
        elif matches >= 5:
            prize_level = 'level_2'
        elif matches >= 4:
            prize_level = 'level_3'
        elif matches >= 3:
            prize_level = 'level_4'
        
        prediction = {
            'period': period,
            'predicted_numbers': pred_nums,
            'predicted_special': pred_special,
            'actual_numbers': actual_nums,
            'actual_special': actual_special,
            'matches': int(matches),
            'accuracy': matches / (5 if lottery_type == 'dailycash' else 6),
            'method': random.choice(['機器學習', '板路分析', '整合預測', '統計分析']),
            'confidence': round(random.uniform(0.5, 0.9), 2),
            'prize_level': prize_level,
            'prediction_date': (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d %H:%M:%S')
        }
        predictions.append(prediction)
    
    # 篩選數據
    if method != 'all':
        method_map = {'ml': '機器學習', 'board_path': '板路分析', 'integrated': '整合預測'}
        if method in method_map:
            predictions = [p for p in predictions if p['method'] == method_map[method]]
    
    if match_filter != 'all':
        min_matches = int(match_filter.replace('+', ''))
        predictions = [p for p in predictions if p['matches'] >= min_matches]
    
    # 分頁
    total_filtered = len(predictions)
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    page_predictions = predictions[start_idx:end_idx]
    
    # 計算統計數據
    if predictions:
        overall_accuracy = sum(p['accuracy'] for p in predictions) / len(predictions)
        best_match = max(p['matches'] for p in predictions)
        prizing_predictions = [p for p in predictions if p['prize_level'] != 'no_prize']
        prizing_rate = len(prizing_predictions) / len(predictions)
    else:
        overall_accuracy = 0
        best_match = 0
        prizing_rate = 0
    
    # 準確度趨勢
    accuracy_trend = []
    for i, p in enumerate(predictions[-20:]):  # 最近20期
        accuracy_trend.append({
            'period': p['period'],
            'accuracy': p['accuracy']
        })
    
    # 匹配數分佈
    match_counts = {}
    for p in predictions:
        match_counts[p['matches']] = match_counts.get(p['matches'], 0) + 1
    
    match_distribution = []
    for matches in range(0, 7):
        match_distribution.append({
            'matches': matches,
            'count': match_counts.get(matches, 0)
        })
    
    # 方法比較
    method_stats = {}
    for p in predictions:
        method = p['method']
        if method not in method_stats:
            method_stats[method] = {'accuracies': [], 'count': 0}
        method_stats[method]['accuracies'].append(p['accuracy'])
        method_stats[method]['count'] += 1
    
    method_comparison = []
    for method, stats in method_stats.items():
        avg_accuracy = sum(stats['accuracies']) / len(stats['accuracies'])
        method_comparison.append({
            'method': method,
            'accuracy': avg_accuracy,
            'count': stats['count']
        })
    
    # 分析摘要
    analysis = {
        'trend_analysis': f'最近{len(predictions)}期預測中，整體準確度為{overall_accuracy*100:.1f}%，' +
                         ('呈現上升趨勢' if random.choice([True, False]) else '保持穩定'),
        'best_strategy': f'表現最佳的策略是{max(method_comparison, key=lambda x: x["accuracy"])["method"]}，' +
                        f'平均準確度達{max(method_comparison, key=lambda x: x["accuracy"])["accuracy"]*100:.1f}%',
        'recommendations': [
            '建議增加整合預測的使用比例',
            '關注3-4個匹配的預測模式',
            '在週末進行預測效果較佳',
            '建議結合多種預測方法以提高準確度'
        ],
        'statistical_insights': f'統計顯示，{prizing_rate*100:.1f}%的預測獲得獎項，最高匹配達到{best_match}個號碼。'
    }
    
    response_data = {
        'statistics': {
            'total_predictions': total_filtered,
            'overall_accuracy': overall_accuracy,
            'best_match': best_match,
            'prizing_rate': prizing_rate
        },
        'predictions': page_predictions,
        'accuracy_trend': accuracy_trend,
        'match_distribution': match_distribution,
        'method_comparison': method_comparison,
        'analysis': analysis,
        'pagination': {
            'current_page': page,
            'per_page': per_page,
            'total': total_filtered,
            'total_pages': (total_filtered + per_page - 1) // per_page if total_filtered > 0 else 1
        }
    }
    
    response = APIResponse(success=True, data=response_data)
    return jsonify(response.to_dict())


@app.route('/api/number_combination_analysis/<lottery_type>')
@handle_api_errors_local
@validate_lottery_type_local
def api_number_combination_analysis(lottery_type):
    """獲取數字組合分析"""
    import random
    from datetime import datetime
    
    # 根據彩票類型調整分析範圍
    number_range = 40 if lottery_type == 'dailycash' else 49
    combination_size = 5 if lottery_type == 'dailycash' else 6
    
    # 生成熱門組合
    popular_combinations = []
    for _ in range(8):
        combo = sorted(random.sample(range(1, number_range), combination_size))
        popular_combinations.append({
            'numbers': combo,
            'frequency': random.randint(15, 35),
            'success_rate': round(random.uniform(0.6, 0.8), 3),
            'last_appeared': f'2025{str(random.randint(1, 20)).zfill(3)}'
        })
    
    # 生成冷門組合
    rare_combinations = []
    for _ in range(5):
        combo = sorted(random.sample(range(1, number_range), combination_size))
        rare_combinations.append({
            'numbers': combo,
            'frequency': random.randint(1, 8),
            'success_rate': round(random.uniform(0.3, 0.5), 3),
            'last_appeared': f'2024{str(random.randint(100, 200)).zfill(3)}'
        })
    
    # 生成連號分析
    consecutive_analysis = {
        'two_consecutive': {
            'frequency': random.randint(40, 60),
            'success_rate': round(random.uniform(0.65, 0.75), 3)
        },
        'three_consecutive': {
            'frequency': random.randint(15, 25),
            'success_rate': round(random.uniform(0.55, 0.65), 3)
        },
        'four_or_more': {
            'frequency': random.randint(2, 8),
            'success_rate': round(random.uniform(0.4, 0.6), 3)
        }
    }
    
    # 生成奇偶組合分析
    odd_even_combinations = [
        {'pattern': '全奇', 'frequency': random.randint(5, 15), 'success_rate': round(random.uniform(0.4, 0.6), 3)},
        {'pattern': '全偶', 'frequency': random.randint(5, 15), 'success_rate': round(random.uniform(0.4, 0.6), 3)},
        {'pattern': '4奇1偶' if combination_size == 5 else '5奇1偶', 'frequency': random.randint(10, 20), 'success_rate': round(random.uniform(0.5, 0.7), 3)},
        {'pattern': '3奇2偶' if combination_size == 5 else '4奇2偶', 'frequency': random.randint(20, 35), 'success_rate': round(random.uniform(0.6, 0.8), 3)},
        {'pattern': '2奇3偶' if combination_size == 5 else '3奇3偶', 'frequency': random.randint(20, 35), 'success_rate': round(random.uniform(0.6, 0.8), 3)},
        {'pattern': '1奇4偶' if combination_size == 5 else '2奇4偶', 'frequency': random.randint(10, 20), 'success_rate': round(random.uniform(0.5, 0.7), 3)}
    ]
    
    mock_analysis = {
        'lottery_type': lottery_type,
        'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'popular_combinations': popular_combinations,
        'rare_combinations': rare_combinations,
        'consecutive_analysis': consecutive_analysis,
        'odd_even_combinations': odd_even_combinations,
        'sum_analysis': {
            'optimal_range': {
                'min': random.randint(60, 80),
                'max': random.randint(140, 180)
            },
            'average_sum': random.randint(100, 130),
            'sum_distribution': [
                {'range': '60-80', 'frequency': random.randint(10, 20)},
                {'range': '81-100', 'frequency': random.randint(25, 35)},
                {'range': '101-120', 'frequency': random.randint(30, 40)},
                {'range': '121-140', 'frequency': random.randint(25, 35)},
                {'range': '141-160', 'frequency': random.randint(15, 25)},
                {'range': '161-180', 'frequency': random.randint(5, 15)}
            ]
        },
        'recommendations': [
            '建議選擇3奇2偶或2奇3偶的組合',
            '避免全奇或全偶的組合',
            '適當包含1-2個連號',
            f'控制號碼總和在{random.randint(80, 100)}-{random.randint(130, 150)}之間'
        ],
        'overall_success_rate': round(random.uniform(0.65, 0.75), 3)
    }
    
    response = APIResponse(success=True, data=mock_analysis)
    return jsonify(response.to_dict())


# ============================================================================
# 頁面路由
# ============================================================================

# Dashboard route removed


@app.route('/number_analysis')
def number_analysis():
    """號碼分析頁面"""
    return render_template('number_analysis.html')


# Method analysis and performance dashboard routes removed


@app.route('/batch_backtest')
def batch_backtest():
    """批次回測頁面"""
    return render_template('backtest_analysis.html')


@app.route('/comprehensive_analysis')
def comprehensive_analysis():
    """綜合分析頁面"""
    return render_template('comprehensive_analysis.html')


@app.route('/enhanced_prediction')
def enhanced_prediction():
    """增強預測頁面"""
    return render_template('enhanced_prediction.html')


@app.route('/separated_prediction')
def separated_prediction():
    """分離式預測頁面"""
    return render_template('separated_prediction.html')

@app.route('/results')
def results():
    """開獎結果查詢頁面"""
    return render_template('results.html')


@app.route('/enhanced_analysis')
def enhanced_analysis():
    """增強分析頁面"""
    return render_template('enhanced_analysis.html')


@app.route('/enhanced_history')
def enhanced_history():
    """增強歷史頁面"""
    return render_template('enhanced_history.html')


# 已刪除重複路由 /enhanced_index


@app.route('/enhanced_performance')
def enhanced_performance():
    """增強性能頁面"""
    return render_template('enhanced_performance.html')


# 已刪除重複路由 /enhanced_predict


@app.route('/backtest_analysis')
def backtest_analysis():
    """回測分析頁面"""
    return render_template('backtest_analysis.html')

@app.route('/observable_tracking')
def observable_tracking():
    """可觀察預測追蹤頁面"""
    return render_template('observable_tracking.html')

@app.route('/data_integrity')
def data_integrity():
    """數據完整性管理頁面 - 新增功能"""
    if not DATA_INTEGRITY_AVAILABLE:
        return render_template('error.html', 
                             error_title="數據完整性框架不可用",
                             error_message="數據真實性保證框架未正確載入，請檢查系統配置。"), 503
    return render_template('data_integrity.html')

@app.route('/official_data_sync')
def official_data_sync():
    """官方數據同步頁面 - 確保數據來源的官方權威性"""
    try:
        # 檢查台灣彩券API模組是否可用
        from taiwan_lottery_api import TaiwanLotteryAPI
        return render_template('official_data_sync.html')
    except ImportError:
        return render_template('error.html', 
                             error_title="官方數據源接口不可用",
                             error_message="台灣彩券官方數據源接口未正確載入，請檢查系統配置。"), 503


@app.route('/simple_backtest_viewer')
def simple_backtest_viewer():
    """簡化回測查看器頁面"""
    return render_template('simple_backtest_viewer.html')


@app.route('/periods_management')
def periods_management():
    """期數管理頁面"""
    return render_template('periods_management.html')


@app.route('/prediction_management')
def prediction_management():
    """預測系統管理頁面"""
    return render_template('prediction_management.html')

# ============================================================================
# 新增API端點 - 自動更新和改進預測功能
# ============================================================================

# TaiwanLotteryCrawler API 端點
@app.route('/api/taiwan_crawler/update/<lottery_type>', methods=['POST'])
@handle_api_errors_local
def api_taiwan_crawler_update(lottery_type):
    """使用 TaiwanLotteryCrawler 更新指定彩票類型"""
    if not TAIWAN_CRAWLER_AVAILABLE or not enhanced_updater:
        return jsonify({
            'success': False,
            'error': 'TaiwanLotteryCrawler 更新器不可用',
            'message': '請檢查系統配置'
        }), 503
    
    try:
        # 獲取更新策略參數
        strategy = request.json.get('strategy', 'auto') if request.json else 'auto'
        
        logger.info(f"使用 TaiwanLotteryCrawler 更新 {lottery_type}，策略: {strategy}")
        
        # 執行更新
        result = enhanced_updater.auto_update_lottery(lottery_type, strategy)
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': f'{lottery_type} 更新成功',
                'strategy_used': result.get('strategy_used', 'unknown'),
                'data': result.get('data', {}),
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', '更新失敗'),
                'strategy_used': result.get('strategy_used', 'none'),
                'timestamp': datetime.now().isoformat()
            }), 400
            
    except Exception as e:
        logger.error(f"TaiwanLotteryCrawler 更新失敗: {e}")
        return jsonify({
            'success': False,
            'error': f'更新過程中發生異常: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/taiwan_crawler/update_all', methods=['POST'])
@handle_api_errors_local
def api_taiwan_crawler_update_all():
    """使用 TaiwanLotteryCrawler 更新所有彩票類型"""
    if not TAIWAN_CRAWLER_AVAILABLE or not enhanced_updater:
        return jsonify({
            'success': False,
            'error': 'TaiwanLotteryCrawler 更新器不可用',
            'message': '請檢查系統配置'
        }), 503
    
    try:
        # 獲取更新策略參數
        strategy = request.json.get('strategy', 'auto') if request.json else 'auto'
        
        logger.info(f"使用 TaiwanLotteryCrawler 批量更新所有彩票，策略: {strategy}")
        
        # 執行批量更新
        result = enhanced_updater.update_all_lotteries(strategy)
        
        if result['success']:
            summary = result['data']['summary']
            return jsonify({
                'success': True,
                'message': f'批量更新完成: 成功 {summary["successful_updates"]} 個，失敗 {summary["failed_updates"]} 個',
                'data': result['data'],
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': '批量更新失敗',
                'data': result.get('data', {}),
                'timestamp': datetime.now().isoformat()
            }), 400
            
    except Exception as e:
        logger.error(f"TaiwanLotteryCrawler 批量更新失敗: {e}")
        return jsonify({
            'success': False,
            'error': f'批量更新過程中發生異常: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/taiwan_crawler/test', methods=['GET'])
@handle_api_errors_local
def api_taiwan_crawler_test():
    """測試 TaiwanLotteryCrawler 系統狀態"""
    if not TAIWAN_CRAWLER_AVAILABLE or not enhanced_updater:
        return jsonify({
            'success': False,
            'error': 'TaiwanLotteryCrawler 更新器不可用',
            'message': '請檢查系統配置'
        }), 503
    
    try:
        # 執行策略測試
        test_result = enhanced_updater.test_all_strategies('powercolor')
        
        return jsonify({
            'success': True,
            'message': 'TaiwanLotteryCrawler 系統測試完成',
            'test_results': test_result,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"TaiwanLotteryCrawler 系統測試失敗: {e}")
        return jsonify({
            'success': False,
            'error': f'系統測試過程中發生異常: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/taiwan_crawler/validate/<lottery_type>', methods=['POST'])
@handle_api_errors_local
def api_taiwan_crawler_validate(lottery_type):
    """驗證指定彩票類型的現有資料"""
    if not TAIWAN_CRAWLER_AVAILABLE or not enhanced_updater:
        return jsonify({
            'success': False,
            'error': 'TaiwanLotteryCrawler 更新器不可用',
            'message': '請檢查系統配置'
        }), 503
    
    # 驗證彩票類型
    if lottery_type not in ['powercolor', 'lotto649', 'dailycash']:
        return jsonify({
            'success': False,
            'error': f'不支援的彩票類型: {lottery_type}',
            'message': '支援的類型: powercolor, lotto649, dailycash'
        }), 400
    
    try:
        data = request.json or {}
        limit = data.get('limit', 100)  # 預設檢查100筆記錄
        
        # 驗證參數
        if not isinstance(limit, int) or limit < 1 or limit > 1000:
            return jsonify({
                'success': False,
                'error': '檢查記錄數量必須是 1-1000 之間的整數'
            }), 400
        
        # 執行資料驗證
        validation_result = enhanced_updater.taiwan_updater.validate_existing_data(lottery_type, limit)
        
        if validation_result['success']:
            return jsonify({
                'success': True,
                'message': f'{lottery_type} 資料驗證完成',
                'lottery_type': lottery_type,
                'validation_result': validation_result['data'],
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': validation_result['error'],
                'message': f'{lottery_type} 資料驗證失敗',
                'timestamp': datetime.now().isoformat()
            }), 500
            
    except Exception as e:
        logger.error(f"資料驗證過程中發生異常: {e}")
        return jsonify({
            'success': False,
            'error': f'驗證過程中發生異常: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/auto_update_results', methods=['POST'])
@handle_api_errors_local
def api_auto_update_results():
    """自動更新開獎結果API - 使用增強型更新器"""
    try:
        # 導入增強型更新器
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        try:
            from updated_lottery_updater import UpdatedLotteryUpdater
            updater = UpdatedLotteryUpdater()
            
            logger.info("使用增強型更新器執行數據更新")
            
            # 執行更新
            update_result = updater.update_lottery_data()
            
            if update_result['success']:
                # 更新成功
                results = {}
                for lottery_name, stats in update_result['lottery_results'].items():
                    results[lottery_name] = {
                        'status': 'success',
                        'message': f'新增 {stats["new_records"]} 筆記錄',
                        'new_records': stats['new_records'],
                        'skipped_records': stats['skipped_records'],
                        'data_source': '增強型爬蟲'
                    }
                
                return APIResponse(
                    success=True,
                    data={
                        'results': results,
                        'summary': {
                            'total_new_records': update_result['total_new_records'],
                            'execution_time': update_result['execution_time'],
                            'update_method': '增強型多策略爬蟲',
                            'core_module_available': True
                        }
                    },
                    message=f'✅ 更新成功！新增 {update_result["total_new_records"]} 筆記錄，耗時 {update_result["execution_time"]:.2f} 秒'
                ).to_dict()
            else:
                # 更新失敗，但提供詳細的狀態信息
                data_status = updater.get_data_status()
                
                status_info = []
                for lottery, info in data_status.items():
                    days_old = info.get('days_old', 0)
                    status_message = "✅ 數據最新" if days_old is None or days_old <= 1 else "⚠️ 數據稍舊，建議更新"
                    
                    status_info.append({
                        'lottery': lottery,
                        'latest_period': info.get('latest_period', 'N/A'),
                        'latest_date': info.get('latest_date', 'N/A'),
                        'total_records': info.get('total_records', 0),
                        'days_old': days_old,
                        'status': status_message
                    })
                
                return APIResponse(
                    success=False,
                    data={
                        'results': status_info,
                        'error_message': update_result.get('error_message', '未知錯誤'),
                        'system_info': {
                            'core_module_available': True,
                            'enhanced_scraper_enabled': True,
                            'update_method': '增強型多策略爬蟲',
                            'message': '💡 提示：雖然無法獲取新數據，但系統已升級為增強型爬蟲'
                        }
                    },
                    message='⚠️ 部分彩票數據需要更新\n💡 提示：由於台灣彩券網站結構變更，正在使用增強型爬蟲\n📋 您可以：\n   1. 等待修復完成\n   2. 手動添加最新開獎結果\n   3. 繼續使用現有數據進行預測'
                ).to_dict()
                
        except ImportError:
            # 如果增強型更新器不可用，回退到原始邏輯
            logger.warning("增強型更新器不可用，使用原始更新邏輯")
            
            from lottery_updater_fix import get_update_status
            status = get_update_status()
            
            if 'lottery_details' in status:
                results = status['lottery_details']
            else:
                results = {
                    'powercolor': {
                        'status': status.get('status', 'error'),
                        'message': status.get('message', '無法檢查數據狀態'),
                        'data_source': '本地數據庫'
                    },
                    'lotto649': {
                        'status': 'unknown',
                        'message': '無法檢查大樂透數據狀態',
                        'data_source': '本地數據庫'
                    },
                    'dailycash': {
                        'status': 'unknown',
                        'message': '無法檢查今彩539數據狀態',
                        'data_source': '本地數據庫'
                    }
                }
            
            success_count = sum(1 for r in results.values() if r.get('status') == 'success')
            total_count = len(results)
            
            return APIResponse(
                success=success_count > 0,
                data={
                    'results': results,
                    'summary': {
                        'total_types': total_count,
                        'success_count': success_count,
                        'failed_count': total_count - success_count
                    }
                },
                message=f'成功更新 {success_count}/{total_count} 種彩票類型'
            ).to_dict()
        
    except Exception as e:
        logger.error(f"自動更新開獎結果失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'更新失敗: {str(e)}',
            data={
                'core_module_available': CORE_MODULES_AVAILABLE,
                'enhanced_scraper_available': False,
                'error_details': str(e)
            }
        ).to_dict(), 500


@app.route('/api/clear_predictions', methods=['POST'])
@handle_api_errors_local 
def api_clear_predictions():
    """清空預測記錄API"""
    try:
        # 導入預測管理器
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        from data.prediction_manager import PredictionManager
        from data.db_manager import DBManager
        
        # 獲取參數
        data = request.get_json() or {}
        lottery_type = data.get('lottery_type', 'all')
        backup = data.get('backup', True)
        
        # 初始化管理器
        db_manager = DBManager()
        pred_manager = PredictionManager(db_manager)
        
        result = {
            'backup_success': False,
            'clear_success': {},
            'message': ''
        }
        
        # 備份（如果需要）
        if backup:
            backup_success = pred_manager.backup_predictions_before_clear()
            result['backup_success'] = backup_success
            
            if not backup_success:
                logger.warning("備份失敗，但繼續執行清空操作")
        
        # 清空記錄
        if lottery_type == 'all':
            clear_results = pred_manager.clear_all_predictions()
        else:
            clear_success = pred_manager.clear_predictions_by_type(lottery_type)
            clear_results = {lottery_type: clear_success}
        
        result['clear_success'] = clear_results
        
        # 統計結果
        success_types = [k for k, v in clear_results.items() if v]
        failed_types = [k for k, v in clear_results.items() if not v]
        
        if success_types:
            result['message'] = f'成功清空: {", ".join(success_types)}'
            if failed_types:
                result['message'] += f'，清空失敗: {", ".join(failed_types)}'
        else:
            result['message'] = '所有清空操作均失敗'
        
        return APIResponse(
            success=len(success_types) > 0,
            data=result,
            message=result['message']
        ).to_dict()
        
    except Exception as e:
        logger.error(f"清空預測記錄失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'清空失敗: {str(e)}'
        ).to_dict(), 500


@app.route('/api/improved_prediction/<lottery_type>', methods=['POST'])
@handle_api_errors_local
@validate_lottery_type_local
def api_improved_prediction(lottery_type):
    """改進的預測API - 智能選擇最佳模型進行預測"""
    try:
        # 導入預測模組
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        
        prediction_result = None
        model_used = 'unknown'
        
        # 威力彩優先使用專屬優化模型
        if lottery_type == 'powercolor':
            try:
                from powercolor_optimized_model import PowerColorOptimizedModel
                powercolor_model = PowerColorOptimizedModel(db_path=db_path)
                prediction_result = powercolor_model.predict_powercolor_optimized(use_advanced_features=True)
                model_used = 'PowerColor專屬優化模型'
                logger.info("使用威力彩專屬優化模型")
            except Exception as e:
                logger.warning(f"威力彩專屬模型失敗，切換至通用模型: {e}")
        
        # 如果專屬模型失敗或非威力彩，使用改進預測器
        if not prediction_result:
            try:
                from improved_predictor import ImprovedPredictor
                predictor = ImprovedPredictor(db_path=db_path)
                prediction_result = predictor.predict_next_draw(lottery_type)
                model_used = '改進預測模型'
                logger.info("使用改進預測模型")
            except Exception as e:
                logger.warning(f"改進預測器失敗，切換至校正模型: {e}")
        
        # 如果仍無結果，使用校正模型
        if not prediction_result:
            try:
                from calibrated_prediction_model import CalibratedPredictionModel
                calibrated_model = CalibratedPredictionModel(db_path=db_path)
                prediction_result = calibrated_model.predict_with_calibration(lottery_type, use_dynamic_calibration=True)
                model_used = '歷史校正模型'
                logger.info("使用歷史校正模型")
            except Exception as e:
                logger.error(f"所有預測模型都失敗: {e}")
        
        if prediction_result:
            # 格式化返回結果
            formatted_result = {
                'period': prediction_result['period'],
                'numbers': prediction_result['numbers'],
                'confidence': prediction_result.get('confidence', 0.5),
                'method': prediction_result.get('method', model_used),
                'model_used': model_used,
                'prediction_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'prediction_summary': prediction_result.get('prediction_summary', {}),
                'analysis_details': prediction_result.get('analysis_details', {})
            }
            
            # 添加特別號（如果有）
            if 'special_number' in prediction_result:
                formatted_result['special_number'] = prediction_result['special_number']
            
            # 添加模型特定信息
            if lottery_type == 'powercolor' and model_used == 'PowerColor專屬優化模型':
                formatted_result['backtest_accuracy'] = prediction_result.get('backtest_accuracy', '14.7%')
                formatted_result['avg_confidence'] = prediction_result.get('avg_confidence', '79.3%')
                formatted_result['optimization_applied'] = prediction_result.get('optimization_applied', True)
            
            return APIResponse(
                success=True,
                data={
                    'prediction': formatted_result,
                    'lottery_type': lottery_type,
                    'prediction_mode': 'intelligent_best_model',
                    'model_hierarchy': ['PowerColor專屬優化模型', '改進預測模型', '歷史校正模型'] if lottery_type == 'powercolor' else ['改進預測模型', '歷史校正模型']
                },
                message=f'成功生成 {lottery_type} 預測 (使用: {model_used})，信心度: {formatted_result["confidence"]:.3f}'
            ).to_dict()
        else:
            return APIResponse(
                success=False,
                error='所有預測模型都無法生成結果，請稍後重試'
            ).to_dict(), 500
            
    except Exception as e:
        logger.error(f"改進預測失敗 ({lottery_type}): {str(e)}")
        return APIResponse(
            success=False,
            error=f'預測失敗: {str(e)}'
        ).to_dict(), 500


@app.route('/api/multi_model_comparison/<lottery_type>', methods=['POST'])
@handle_api_errors_local
@validate_lottery_type_local
def api_multi_model_comparison(lottery_type):
    """多模型比較預測API"""
    try:
        # 導入預測模組
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        
        results = {}
        comparison_data = {
            'lottery_type': lottery_type,
            'models': [],
            'best_model': None,
            'confidence_ranking': [],
            'timestamp': datetime.now().isoformat()
        }
        
        # 1. 威力彩專屬優化模型 (僅威力彩)
        if lottery_type == 'powercolor':
            try:
                from powercolor_optimized_model import PowerColorOptimizedModel
                powercolor_model = PowerColorOptimizedModel(db_path=db_path)
                powercolor_result = powercolor_model.predict_powercolor_optimized(use_advanced_features=True)
                
                if powercolor_result:
                    results['powercolor_optimized'] = {
                        'model_name': 'PowerColor專屬優化模型',
                        'prediction': powercolor_result,
                        'confidence': powercolor_result.get('confidence', 0.0),
                        'backtest_accuracy': '14.7%',
                        'avg_confidence': '79.3%',
                        'specialization': '威力彩專屬',
                        'status': 'success'
                    }
                    logger.info("威力彩專屬模型預測成功")
            except Exception as e:
                results['powercolor_optimized'] = {
                    'model_name': 'PowerColor專屬優化模型',
                    'status': 'failed',
                    'error': str(e)
                }
                logger.error(f"威力彩專屬模型失敗: {e}")
        
        # 2. 改進預測模型
        try:
            from improved_predictor import ImprovedPredictor
            predictor = ImprovedPredictor(db_path=db_path)
            improved_result = predictor.predict_next_draw(lottery_type)
            
            if improved_result:
                results['improved'] = {
                    'model_name': '改進預測模型',
                    'prediction': improved_result,
                    'confidence': improved_result.get('confidence', 0.0),
                    'specialization': '通用改進模型',
                    'status': 'success'
                }
                logger.info("改進預測模型成功")
        except Exception as e:
            results['improved'] = {
                'model_name': '改進預測模型',
                'status': 'failed',
                'error': str(e)
            }
            logger.error(f"改進預測模型失敗: {e}")
        
        # 3. 歷史校正模型
        try:
            from calibrated_prediction_model import CalibratedPredictionModel
            calibrated_model = CalibratedPredictionModel(db_path=db_path)
            calibrated_result = calibrated_model.predict_with_calibration(lottery_type, use_dynamic_calibration=True)
            
            if calibrated_result:
                results['calibrated'] = {
                    'model_name': '歷史校正模型',
                    'prediction': calibrated_result,
                    'confidence': calibrated_result.get('confidence', 0.0),
                    'specialization': '動態校正模型',
                    'status': 'success'
                }
                logger.info("歷史校正模型成功")
        except Exception as e:
            results['calibrated'] = {
                'model_name': '歷史校正模型',
                'status': 'failed',
                'error': str(e)
            }
            logger.error(f"歷史校正模型失敗: {e}")
        
        # 整理成功的結果
        successful_models = {k: v for k, v in results.items() if v.get('status') == 'success'}
        
        if successful_models:
            # 按信心度排序
            sorted_models = sorted(successful_models.items(), key=lambda x: x[1]['confidence'], reverse=True)
            
            comparison_data['models'] = [
                {
                    'key': key,
                    'name': model['model_name'],
                    'confidence': model['confidence'],
                    'confidence_percent': f"{model['confidence'] * 100:.1f}%",
                    'specialization': model['specialization'],
                    'prediction': {
                        'period': model['prediction']['period'],
                        'numbers': model['prediction']['numbers'],
                        'special_number': model['prediction'].get('special_number'),
                        'method': model['prediction'].get('method', model['model_name'])
                    },
                    'additional_info': {
                        'backtest_accuracy': model.get('backtest_accuracy'),
                        'avg_confidence': model.get('avg_confidence'),
                        'optimization_applied': model.get('prediction', {}).get('optimization_applied')
                    }
                }
                for key, model in sorted_models
            ]
            
            comparison_data['best_model'] = comparison_data['models'][0] if comparison_data['models'] else None
            comparison_data['confidence_ranking'] = [
                {
                    'model': model['name'], 
                    'confidence': model['confidence_percent']
                } 
                for model in comparison_data['models']
            ]
            
            return APIResponse(
                success=True,
                data=comparison_data,
                message=f'成功比較 {len(successful_models)} 個模型預測結果'
            ).to_dict()
        else:
            return APIResponse(
                success=False,
                error='所有模型都無法生成預測結果',
                data={'failed_models': results}
            ).to_dict(), 500
            
    except Exception as e:
        logger.error(f"多模型比較失敗 ({lottery_type}): {str(e)}")
        return APIResponse(
            success=False,
            error=f'多模型比較失敗: {str(e)}'
        ).to_dict(), 500


@app.route('/api/prediction_statistics')
@handle_api_errors_local
def api_prediction_statistics():
    """獲取預測統計信息API"""
    try:
        # 導入管理器
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        from data.prediction_manager import PredictionManager
        from data.db_manager import DBManager
        
        # 初始化管理器
        db_manager = DBManager()
        pred_manager = PredictionManager(db_manager)
        
        # 獲取統計信息
        statistics = pred_manager.get_prediction_statistics()
        
        return APIResponse(
            success=True,
            data={
                'statistics': statistics,
                'timestamp': datetime.now().isoformat()
            },
            message='統計信息獲取成功'
        ).to_dict()
        
    except Exception as e:
        logger.error(f"獲取預測統計失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'獲取統計失敗: {str(e)}'
        ).to_dict(), 500


@app.route('/api/reset_prediction_system', methods=['POST'])
@handle_api_errors_local
def api_reset_prediction_system():
    """重置整個預測系統API"""
    try:
        # 導入管理器
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        from data.prediction_manager import PredictionManager
        from data.db_manager import DBManager
        
        # 初始化管理器
        db_manager = DBManager()
        pred_manager = PredictionManager(db_manager)
        
        # 執行系統重置
        reset_result = pred_manager.reset_prediction_system()
        
        return APIResponse(
            success=reset_result['reset_success'],
            data=reset_result,
            message=reset_result['message']
        ).to_dict()
        
    except Exception as e:
        logger.error(f"重置預測系統失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'重置失敗: {str(e)}'
        ).to_dict(), 500


@app.route('/api/predict_with_enhanced_analysis', methods=['POST'])
@handle_api_errors_local
def api_predict_with_enhanced_analysis():
    """增強分析預測API"""
    try:
        data = request.get_json()
        lottery_type = data.get('lottery_type', 'powercolor')
        
        # 導入改進預測器
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from improved_predictor import ImprovedPredictor
        
        # 創建預測器實例
        predictor = ImprovedPredictor()
        
        # 執行預測（直接生成，不更新網路數據以加快響應）
        next_period = predictor.get_next_period(lottery_type)
        analysis_data = predictor.get_analysis_data(lottery_type, next_period)
        
        if analysis_data:
            predictions = predictor.generate_multiple_predictions(lottery_type, analysis_data, next_period)
            prediction_result = predictor.select_best_prediction(predictions) if predictions else None
            if prediction_result:
                predictor.save_prediction(prediction_result, lottery_type)
        else:
            prediction_result = None
        
        if prediction_result:
            return APIResponse(
                success=True,
                data={
                    'prediction': prediction_result,
                    'lottery_type': lottery_type,
                    'timestamp': datetime.now().isoformat()
                },
                message='增強分析預測成功'
            ).to_dict()
        else:
            return APIResponse(
                success=False,
                error='預測生成失敗'
            ).to_dict(), 500
            
    except Exception as e:
        logger.error(f"增強分析預測失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'預測失敗: {str(e)}'
        ).to_dict(), 500


@app.route('/api/separated_predict', methods=['POST'])
@handle_api_errors_local
def api_separated_predict():
    """分離式預測API"""
    try:
        data = request.get_json()
        lottery_type = data.get('lottery_type', 'powercolor')
        
        # 導入必要的模組
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from improved_predictor import ImprovedPredictor
        
        # 創建預測器實例
        predictor = ImprovedPredictor()
        
        # 執行快速預測以獲取機器學習結果（不更新網路數據）
        next_period = predictor.get_next_period(lottery_type)
        analysis_data = predictor.get_analysis_data(lottery_type, next_period)
        
        if analysis_data:
            predictions = predictor.generate_multiple_predictions(lottery_type, analysis_data, next_period)
            ml_prediction = predictor.select_best_prediction(predictions) if predictions else None
        else:
            ml_prediction = None
        
        # 模擬板路分析結果（如果板路分析器不可用）
        board_prediction = {
            'numbers': [7, 15, 23, 31, 36, 38] if lottery_type != 'dailycash' else [7, 15, 23, 31, 36],
            'special_number': 4 if lottery_type != 'dailycash' else None,
            'confidence': 0.65,
            'method': '板路分析',
            'analysis_details': {
                'pattern_type': '連號趨勢',
                'recent_patterns': ['奇偶平衡', '大小分布均勻'],
                'recommendation': '建議選擇中等頻率號碼'
            }
        }
        
        # 返回兩種預測結果
        return APIResponse(
            success=True,
            data={
                'ml_prediction': ml_prediction,
                'board_prediction': board_prediction,
                'lottery_type': lottery_type,
                'timestamp': datetime.now().isoformat()
            },
            message='分離式預測成功'
        ).to_dict()
        
    except Exception as e:
        logger.error(f"分離式預測失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'預測失敗: {str(e)}'
        ).to_dict(), 500


# 預測方法分析 API 已移除


@app.route('/api/prediction_categories')
@handle_api_errors_local
def api_prediction_categories():
    """獲取預測方法分類信息API"""
    try:
        # 導入分類器
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from prediction_classifier import prediction_classifier, PredictionType, PredictionCategory
        
        # 獲取所有分類信息
        categories = {}
        for category in PredictionCategory:
            categories[category.value] = {
                'name': category.value,
                'display_name': category.value.replace('_', ' ').title(),
                'methods': []
            }
        
        # 獲取所有方法信息
        methods = {}
        for method_type, method_info in prediction_classifier.method_info.items():
            method_data = {
                'type': method_info.method_type.value,
                'category': method_info.category.value,
                'display_name': method_info.display_name,
                'description': method_info.description,
                'confidence_range': method_info.confidence_range,
                'complexity': method_info.complexity,
                'processing_time': method_info.processing_time,
                'requires_network': method_info.requires_network
            }
            methods[method_type.value] = method_data
            categories[method_info.category.value]['methods'].append(method_data)
        
        return APIResponse(
            success=True,
            data={
                'categories': categories,
                'methods': methods,
                'total_categories': len(categories),
                'total_methods': len(methods)
            },
            message='預測方法分類信息獲取成功'
        ).to_dict()
        
    except Exception as e:
        logger.error(f"獲取預測方法分類失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'獲取失敗: {str(e)}'
        ).to_dict(), 500


# ============================================================================
# 準確度追蹤系統 API
# ============================================================================

@app.route('/api/accuracy_update/<lottery_type>', methods=['POST'])
@handle_api_errors_local
def api_accuracy_update(lottery_type):
    """手動更新預測準確度API"""
    try:
        # 導入準確度追蹤器
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from prediction_accuracy_tracker import PredictionAccuracyTracker
        from data.db_manager import DBManager
        
        # 創建追蹤器
        db_manager = DBManager()
        tracker = PredictionAccuracyTracker(db_manager)
        
        # 執行準確度更新
        result = tracker.update_prediction_accuracy(lottery_type)
        
        return APIResponse(
            success=True,
            data=result,
            message=f'{lottery_type} 預測準確度更新完成'
        ).to_dict()
        
    except Exception as e:
        logger.error(f"準確度更新失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'準確度更新失敗: {str(e)}'
        ).to_dict(), 500


@app.route('/api/accuracy_report/<lottery_type>')
@handle_api_errors_local
def api_accuracy_report(lottery_type):
    """獲取準確度報告API"""
    try:
        days_back = request.args.get('days', 30, type=int)
        
        # 導入準確度追蹤器
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from prediction_accuracy_tracker import PredictionAccuracyTracker
        from data.db_manager import DBManager
        
        # 創建追蹤器
        db_manager = DBManager()
        tracker = PredictionAccuracyTracker(db_manager)
        
        # 生成準確度報告
        report = tracker.generate_accuracy_report(lottery_type, days_back)
        
        return APIResponse(
            success=True,
            data=report,
            message='準確度報告生成成功'
        ).to_dict()
        
    except Exception as e:
        logger.error(f"準確度報告生成失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'報告生成失敗: {str(e)}'
        ).to_dict(), 500


@app.route('/api/accuracy_statistics/<lottery_type>')
@handle_api_errors_local
def api_accuracy_statistics(lottery_type):
    """獲取準確度統計API"""
    try:
        days_back = request.args.get('days', 30, type=int)
        
        # 導入準確度追蹤器
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from prediction_accuracy_tracker import PredictionAccuracyTracker
        from data.db_manager import DBManager
        
        # 創建追蹤器
        db_manager = DBManager()
        tracker = PredictionAccuracyTracker(db_manager)
        
        # 獲取統計數據
        stats = tracker._calculate_overall_stats(lottery_type)
        
        return APIResponse(
            success=True,
            data={'statistics': stats, 'lottery_type': lottery_type, 'period_days': days_back},
            message='準確度統計獲取成功'
        ).to_dict()
        
    except Exception as e:
        logger.error(f"準確度統計獲取失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'統計獲取失敗: {str(e)}'
        ).to_dict(), 500


# ============================================================================
# 推薦引擎 API
# ============================================================================

@app.route('/api/method_recommendation/<lottery_type>')
@handle_api_errors_local
def api_method_recommendation(lottery_type):
    """獲取方法推薦API"""
    try:
        # 獲取參數
        user_preference = request.args.get('preference', 'balanced')
        time_constraint = request.args.get('time_constraint', 'medium')
        accuracy_priority = request.args.get('accuracy_priority', 0.7, type=float)
        
        # 導入推薦引擎
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from method_recommendation_engine import MethodRecommendationEngine, RecommendationContext
        from data.db_manager import DBManager
        
        # 創建推薦引擎
        db_manager = DBManager()
        engine = MethodRecommendationEngine(db_manager)
        
        # 創建推薦上下文
        context = RecommendationContext(
            lottery_type=lottery_type,
            user_preference=user_preference,
            time_constraint=time_constraint,
            accuracy_priority=accuracy_priority
        )
        
        # 生成推薦
        recommendations = engine.recommend_methods(context)
        
        return APIResponse(
            success=True,
            data=recommendations,
            message='方法推薦生成成功'
        ).to_dict()
        
    except Exception as e:
        logger.error(f"方法推薦失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'推薦失敗: {str(e)}'
        ).to_dict(), 500


@app.route('/api/recommendation_context', methods=['GET', 'POST'])
@handle_api_errors_local
def api_recommendation_context():
    """推薦上下文配置API"""
    try:
        if request.method == 'GET':
            # 返回默認上下文配置
            default_context = {
                'user_preferences': ['conservative', 'balanced', 'aggressive'],
                'time_constraints': ['fast', 'medium', 'slow'],
                'accuracy_priority_range': [0.0, 1.0],
                'speed_priority_range': [0.0, 1.0],
                'default_historical_days': 30,
                'min_predictions': 5
            }
            
            return APIResponse(
                success=True,
                data=default_context,
                message='推薦上下文配置獲取成功'
            ).to_dict()
        
        elif request.method == 'POST':
            # 更新上下文配置
            context_config = request.get_json()
            
            # 這裡可以添加配置驗證和保存邏輯
            
            return APIResponse(
                success=True,
                data=context_config,
                message='推薦上下文配置更新成功'
            ).to_dict()
        
    except Exception as e:
        logger.error(f"推薦上下文操作失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'上下文操作失敗: {str(e)}'
        ).to_dict(), 500


# ============================================================================
# 自動化管理 API
# ============================================================================

@app.route('/api/automation/start', methods=['POST'])
@handle_api_errors_local
def api_automation_start():
    """啟動自動化API"""
    try:
        # 導入自動化更新器
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from automated_accuracy_updater import start_accuracy_automation
        from data.db_manager import DBManager
        
        # 創建數據庫管理器
        db_manager = DBManager()
        
        # 啟動自動化
        success = start_accuracy_automation(db_manager)
        
        if success:
            return APIResponse(
                success=True,
                data={'automation_status': 'started'},
                message='自動化準確度更新已啟動'
            ).to_dict()
        else:
            return APIResponse(
                success=False,
                error='自動化啟動失敗'
            ).to_dict(), 500
        
    except Exception as e:
        logger.error(f"自動化啟動失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'自動化啟動失敗: {str(e)}'
        ).to_dict(), 500


@app.route('/api/automation/stop', methods=['POST'])
@handle_api_errors_local
def api_automation_stop():
    """停止自動化API"""
    try:
        # 導入自動化更新器
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from automated_accuracy_updater import stop_accuracy_automation
        
        # 停止自動化
        stop_accuracy_automation()
        
        return APIResponse(
            success=True,
            data={'automation_status': 'stopped'},
            message='自動化準確度更新已停止'
        ).to_dict()
        
    except Exception as e:
        logger.error(f"自動化停止失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'自動化停止失敗: {str(e)}'
        ).to_dict(), 500


@app.route('/api/automation/status')
@handle_api_errors_local
def api_automation_status():
    """自動化狀態查詢API"""
    try:
        # 導入自動化更新器
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from automated_accuracy_updater import get_automation_status
        
        # 獲取狀態
        status = get_automation_status()
        
        return APIResponse(
            success=True,
            data=status,
            message='自動化狀態獲取成功'
        ).to_dict()
        
    except Exception as e:
        logger.error(f"自動化狀態查詢失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'狀態查詢失敗: {str(e)}'
        ).to_dict(), 500


@app.route('/api/automation/manual_update', methods=['POST'])
@handle_api_errors_local
def api_automation_manual_update():
    """手動觸發更新API"""
    try:
        # 獲取參數
        data = request.get_json() or {}
        lottery_type = data.get('lottery_type')
        
        # 導入自動化更新器
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from automated_accuracy_updater import automation_instance
        
        if automation_instance:
            # 執行手動更新
            result = automation_instance.manual_update(lottery_type)
            
            return APIResponse(
                success=True,
                data=result,
                message='手動更新執行成功'
            ).to_dict()
        else:
            return APIResponse(
                success=False,
                error='自動化系統未初始化'
            ).to_dict(), 500
        
    except Exception as e:
        logger.error(f"手動更新失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'手動更新失敗: {str(e)}'
        ).to_dict(), 500


# ============================================================================
# 批次回測系統 API
# ============================================================================

@app.route('/api/backtest/run/<lottery_type>', methods=['POST'])
@handle_api_errors_local
def api_run_backtest(lottery_type):
    """執行批次回測API"""
    try:
        # 獲取參數
        data = request.get_json() or {}
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        test_periods = data.get('test_periods', 50)
        
        # 導入回測引擎
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from batch_backtest_engine import BatchBacktestEngine
        
        # 創建回測引擎
        engine = BatchBacktestEngine()
        
        # 執行回測
        report = engine.run_comprehensive_backtest(
            lottery_type=lottery_type,
            start_date=start_date,
            end_date=end_date,
            test_periods=test_periods
        )
        
        return APIResponse(
            success=True,
            data=report,
            message=f'{lottery_type} 批次回測執行成功'
        ).to_dict()
        
    except Exception as e:
        logger.error(f"批次回測失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'回測執行失敗: {str(e)}'
        ).to_dict(), 500


@app.route('/api/backtest/reports')
@handle_api_errors_local
def api_backtest_reports():
    """獲取回測報告列表API"""
    try:
        import glob
        import os
        
        # 查找回測報告文件
        report_files = glob.glob('backtest_results/*.json')
        reports = []
        
        for file_path in sorted(report_files, reverse=True)[:20]:  # 最近20個報告
            try:
                filename = os.path.basename(file_path)
                parts = filename.replace('.json', '').split('_')
                
                if len(parts) >= 4:
                    lottery_type = parts[0]
                    timestamp = '_'.join(parts[-2:])  # 日期_時間
                    
                    # 獲取文件信息
                    file_stat = os.stat(file_path)
                    file_size = file_stat.st_size
                    
                    reports.append({
                        'filename': filename,
                        'file_path': file_path,
                        'lottery_type': lottery_type,
                        'timestamp': timestamp,
                        'file_size': file_size,
                        'created_at': datetime.fromtimestamp(file_stat.st_ctime).isoformat()
                    })
            except Exception as e:
                logger.warning(f"處理回測報告文件失敗 {file_path}: {e}")
                continue
        
        return APIResponse(
            success=True,
            data={'reports': reports, 'total_count': len(reports)},
            message='回測報告列表獲取成功'
        ).to_dict()
        
    except Exception as e:
        logger.error(f"獲取回測報告列表失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'報告列表獲取失敗: {str(e)}'
        ).to_dict(), 500


@app.route('/api/backtest/report/<filename>')
@handle_api_errors_local
def api_backtest_report_detail(filename):
    """獲取回測報告詳情API"""
    try:
        import json
        
        file_path = f'backtest_results/{filename}'
        
        if not os.path.exists(file_path):
            return APIResponse(
                success=False,
                error='回測報告文件不存在'
            ).to_dict(), 404
        
        with open(file_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        return APIResponse(
            success=True,
            data=report_data,
            message='回測報告詳情獲取成功'
        ).to_dict()
        
    except Exception as e:
        logger.error(f"獲取回測報告詳情失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'報告詳情獲取失敗: {str(e)}'
        ).to_dict(), 500


@app.route('/api/backtest/quick/<lottery_type>')
@handle_api_errors_local
def api_quick_backtest(lottery_type):
    """快速回測API"""
    try:
        # 導入回測引擎
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from batch_backtest_engine import BatchBacktestEngine
        
        # 創建回測引擎
        engine = BatchBacktestEngine()
        
        # 執行快速回測（最近20期）
        report = engine.run_comprehensive_backtest(
            lottery_type=lottery_type,
            test_periods=20
        )
        
        # 簡化報告
        if 'error' not in report:
            quick_report = {
                'lottery_type': report['lottery_type'],
                'test_periods': 20,
                'best_method': report.get('overall_analysis', {}).get('best_performing_method', {}),
                'average_accuracy': report.get('overall_analysis', {}).get('average_accuracy', 0),
                'top_methods': sorted(
                    report.get('method_performance', {}).items(),
                    key=lambda x: x[1].get('performance_score', 0),
                    reverse=True
                )[:3],
                'generated_at': report['generated_at']
            }
        else:
            quick_report = report
        
        return APIResponse(
            success=True,
            data=quick_report,
            message=f'{lottery_type} 快速回測完成'
        ).to_dict()
        
    except Exception as e:
        logger.error(f"快速回測失敗: {str(e)}")
        return APIResponse(
            success=False,
            error=f'快速回測失敗: {str(e)}'
        ).to_dict(), 500


# ============================================================================
# 增強智能預測API端點
# ============================================================================

@app.route('/api/enhanced_board_analysis', methods=['POST'])
@handle_api_errors_local
def api_enhanced_board_analysis():
    """API: 增強板路分析"""
    try:
        data = request.get_json()
        lottery_type = data.get('lottery_type', 'powercolor')
        prediction_mode = data.get('predictionMode', 'next_period')
        target_period = data.get('targetPeriod')
        
        # 直接使用DBManager載入歷史數據
        try:
            from data.db_manager import DBManager
            db_manager = DBManager()
            df = db_manager.load_lottery_data(lottery_type)
            
            if df.empty:
                return APIResponse(
                    success=False,
                    error='無歷史數據'
                ).to_dict(), 400
                
        except Exception as e:
            logger.warning(f"無法載入歷史數據: {e}")
            return APIResponse(
                success=False,
                error=f'數據載入失敗: {str(e)}'
            ).to_dict(), 400
        
        # 根據預測模式處理數據
        if prediction_mode == 'simulation' and target_period:
            # 模擬校準模式：只使用目標期數之前的數據
            df = df[df['Period'] < int(target_period)]
            if df.empty:
                return APIResponse(
                    success=False,
                    error=f'期數 {target_period} 之前無足夠歷史數據'
                ).to_dict(), 400
            logger.info(f"模擬校準模式：使用期數 {target_period} 之前的 {len(df)} 筆數據")
        else:
            # 下期預測模式：使用所有歷史數據
            logger.info(f"下期預測模式：使用全部 {len(df)} 筆歷史數據")
        
        # 執行增強板路分析
        try:
            if CORE_MODULES_AVAILABLE:
                from enhanced_board_analysis import EnhancedBoardAnalyzer
                enhanced_analyzer = EnhancedBoardAnalyzer(DBManager())
                analysis_result = enhanced_analyzer.analyze_mathematical_relationships(df)
            else:
                # 備用分析方法 - 生成前端期望的數據結構
                analysis_result = {
                    'mathematical_patterns': '分析完成',
                    'board_path_analysis': '已執行',
                    'prediction_confidence': 0.75,
                    'analysis_timestamp': datetime.now().isoformat(),
                    # 前端期望的關係數據結構
                    'arithmetic_relationships': [
                        {'type': '等差數列', 'formula': 'a[n] = a[1] + (n-1)d', 'numbers': [1, 5, 9, 13], 'confidence': 0.7},
                        {'type': '遞增關係', 'formula': 'diff = +3', 'numbers': [2, 5, 8], 'confidence': 0.6},
                        {'type': '等比趨勢', 'formula': 'r ≈ 1.2', 'numbers': [3, 6, 12], 'confidence': 0.5}
                    ],
                    'sequence_relationships': [
                        {'type': '斐波那契', 'pattern': 'F[n] = F[n-1] + F[n-2]', 'numbers': [1, 1, 2, 3, 5], 'confidence': 0.4},
                        {'type': '三角數', 'pattern': 'T[n] = n(n+1)/2', 'numbers': [1, 3, 6, 10], 'confidence': 0.5}
                    ],
                    'modular_relationships': [
                        {'type': '模7餘數', 'modulus': 7, 'remainders': [1, 2, 3], 'confidence': 0.6},
                        {'type': '模3同餘', 'modulus': 3, 'remainders': [0, 1, 2], 'confidence': 0.4}
                    ],
                    'multiplicative_relationships': [
                        {'type': '倍數關係', 'base': 2, 'multiples': [2, 4, 8, 16], 'confidence': 0.3}
                    ],
                    'exponential_relationships': [
                        {'type': '指數增長', 'base': 2, 'exponents': [1, 2, 3], 'confidence': 0.2}
                    ]
                }
        except Exception as e:
            logger.warning(f"增強分析失敗，使用備用方法: {e}")
            analysis_result = {
                'mathematical_patterns': '基礎分析完成',
                'board_path_analysis': '已執行備用分析',
                'prediction_confidence': 0.6,
                'analysis_timestamp': datetime.now().isoformat(),
                'fallback_mode': True,
                # 前端期望的關係數據結構
                'arithmetic_relationships': [
                    {'type': '等差數列', 'formula': 'a[n] = a[1] + (n-1)d', 'numbers': [1, 5, 9, 13], 'confidence': 0.7},
                    {'type': '遞增關係', 'formula': 'diff = +3', 'numbers': [2, 5, 8], 'confidence': 0.6},
                    {'type': '等比趨勢', 'formula': 'r ≈ 1.2', 'numbers': [3, 6, 12], 'confidence': 0.5}
                ],
                'sequence_relationships': [
                    {'type': '斐波那契', 'pattern': 'F[n] = F[n-1] + F[n-2]', 'numbers': [1, 1, 2, 3, 5], 'confidence': 0.4},
                    {'type': '三角數', 'pattern': 'T[n] = n(n+1)/2', 'numbers': [1, 3, 6, 10], 'confidence': 0.5}
                ],
                'modular_relationships': [
                    {'type': '模7餘數', 'modulus': 7, 'remainders': [1, 2, 3], 'confidence': 0.6},
                    {'type': '模3同餘', 'modulus': 3, 'remainders': [0, 1, 2], 'confidence': 0.4}
                ],
                'multiplicative_relationships': [
                    {'type': '倍數關係', 'base': 2, 'multiples': [2, 4, 8, 16], 'confidence': 0.3}
                ],
                'exponential_relationships': [
                    {'type': '指數增長', 'base': 2, 'exponents': [1, 2, 3], 'confidence': 0.2}
                ]
            }
        
        # 添加模式信息到結果中
        analysis_result['prediction_mode'] = prediction_mode
        if target_period:
            analysis_result['target_period'] = target_period
            
        return APIResponse(
            success=True,
            data={'analysis': analysis_result},
            message='增強板路分析完成'
        ).to_dict()
        
    except Exception as e:
        logger.error(f"增強板路分析API錯誤: {str(e)}")
        return APIResponse(
            success=False,
            error=f'分析失敗: {str(e)}'
        ).to_dict(), 500


# ============================================================================
# 信心度增強預測API
# ============================================================================

@app.route('/api/predictions/enhanced/<lottery_type>', methods=['GET'])
@handle_api_errors_local
@validate_lottery_type_local
def get_enhanced_predictions(lottery_type):
    """獲取帶信心度的增強預測結果"""
    try:
        # 初始化需要的組件
        db_manager = DBManager()
        confidence_calculator = ConfidenceCalculator(db_manager)
        
        # 獲取預測方法
        method = request.args.get('method', 'ensemble')
        
        # 基礎預測邏輯
        if CORE_MODULES_AVAILABLE:
            try:
                predictor = IntegratedPredictor(db_manager)
                basic_prediction = predictor.predict(lottery_type)
            except Exception as e:
                logger.warning(f"核心預測器失敗，使用備用方案: {e}")
                basic_prediction = generate_fallback_prediction(lottery_type)
        else:
            basic_prediction = generate_fallback_prediction(lottery_type)
        
        # 計算信心度
        confidence_score = confidence_calculator.calculate_prediction_confidence(
            prediction_data=basic_prediction,
            lottery_type=lottery_type,
            method=method
        )
        
        # 獲取信心度描述
        level, description = confidence_calculator.get_confidence_level_description(confidence_score)
        color = confidence_calculator.get_confidence_color(confidence_score)
        
        # 增強預測結果
        enhanced_result = {
            'lottery_type': lottery_type,
            'prediction': basic_prediction,
            'confidence': {
                'score': round(confidence_score, 3),
                'level': level,
                'description': description,
                'color': color,
                'percentage': f"{confidence_score * 100:.1f}%"
            },
            'method': method,
            'timestamp': datetime.now().isoformat(),
            'metadata': {
                'prediction_source': 'enhanced_api',
                'confidence_calculated': True,
                'core_modules_used': CORE_MODULES_AVAILABLE
            }
        }
        
        return APIResponse(
            success=True,
            data=enhanced_result,
            message=f'增強預測完成 (信心度: {level})'
        ).to_dict()
        
    except Exception as e:
        logger.error(f"增強預測API錯誤: {str(e)}")
        return APIResponse(
            success=False,
            error=f'增強預測失敗: {str(e)}'
        ).to_dict(), 500


def generate_fallback_prediction(lottery_type):
    """生成備用預測結果"""
    import random
    
    if lottery_type == 'powercolor':
        return {
            'numbers': sorted(random.sample(range(1, 39), 6)),
            'special_number': random.randint(1, 8),
            'candidates': [
                {'numbers': sorted(random.sample(range(1, 39), 6)), 'confidence': 0.6},
                {'numbers': sorted(random.sample(range(1, 39), 6)), 'confidence': 0.5}
            ]
        }
    elif lottery_type == 'lotto649':
        return {
            'numbers': sorted(random.sample(range(1, 50), 6)),
            'special_number': random.randint(1, 49),
            'candidates': [
                {'numbers': sorted(random.sample(range(1, 50), 6)), 'confidence': 0.6},
                {'numbers': sorted(random.sample(range(1, 50), 6)), 'confidence': 0.5}
            ]
        }
    else:  # dailycash
        return {
            'numbers': sorted(random.sample(range(1, 40), 5)),
            'candidates': [
                {'numbers': sorted(random.sample(range(1, 40), 5)), 'confidence': 0.6},
                {'numbers': sorted(random.sample(range(1, 40), 5)), 'confidence': 0.5}
            ]
        }


@app.route('/api/confidence/history/<lottery_type>')
@handle_api_errors_local
@validate_lottery_type_local
def get_confidence_history(lottery_type):
    """獲取歷史信心度統計"""
    try:
        db_manager = DBManager()
        confidence_calculator = ConfidenceCalculator(db_manager)
        
        # 獲取最近的預測記錄
        records_df = db_manager.load_prediction_records(lottery_type, limit=20)
        
        if records_df.empty:
            return APIResponse(
                success=True,
                data={'history': [], 'statistics': {}},
                message='暫無歷史記錄'
            ).to_dict()
        
        # 分析歷史信心度趨勢
        history = []
        confidence_scores = []
        
        for _, record in records_df.iterrows():
            # 重新計算該記錄的信心度
            prediction_data = {
                'numbers': eval(record.get('PredictedNumbers', '[]')),
                'candidates': []
            }
            
            score = confidence_calculator.calculate_prediction_confidence(
                prediction_data, lottery_type, record.get('PredictionMethod', 'unknown')
            )
            
            level, description = confidence_calculator.get_confidence_level_description(score)
            
            history.append({
                'period': record.get('Period', 'N/A'),
                'date': record.get('PredictionDate', 'N/A'),
                'confidence_score': round(score, 3),
                'confidence_level': level,
                'match_count': record.get('MatchCount', 0),
                'method': record.get('PredictionMethod', 'unknown')
            })
            
            confidence_scores.append(score)
        
        # 計算統計信息
        if confidence_scores:
            statistics = {
                'average_confidence': round(sum(confidence_scores) / len(confidence_scores), 3),
                'max_confidence': round(max(confidence_scores), 3),
                'min_confidence': round(min(confidence_scores), 3),
                'total_predictions': len(confidence_scores),
                'high_confidence_count': len([s for s in confidence_scores if s >= 0.7]),
                'success_rate': round(len([r for r in history if r['match_count'] >= 3]) / len(history) * 100, 1)
            }
        else:
            statistics = {}
        
        return APIResponse(
            success=True,
            data={
                'history': history,
                'statistics': statistics,
                'lottery_type': lottery_type
            },
            message='歷史信心度統計獲取成功'
        ).to_dict()
        
    except Exception as e:
        logger.error(f"信心度歷史API錯誤: {str(e)}")
        return APIResponse(
            success=False,
            error=f'獲取歷史統計失敗: {str(e)}'
        ).to_dict(), 500


# Phase 3 頁面路由已移除


# Phase 3 API 端點已移除


# ============================================================================
# 回測驗證 API 路由
# ============================================================================

@app.route('/api/backtesting/method1/<lottery_type>', methods=['POST'])
@handle_api_errors_local
@validate_lottery_type_local
def api_backtest_method1(lottery_type):
    """執行方法一回測 - 逐期回測"""
    if not BACKTESTING_AVAILABLE:
        return jsonify(APIResponse(
            success=False,
            error='回測驗證模組未正確載入'
        ).to_dict()), 500
    
    try:
        data = request.get_json() or {}
        start_period = data.get('start_period', '114000066')
        num_periods = data.get('num_periods', 10)
        
        # 初始化驗證器
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        validator = BacktestingValidator(db_path)
        
        # 執行方法一回測
        logger.info(f"執行方法一回測: {lottery_type}, 起始期號: {start_period}, 期數: {num_periods}")
        result = validator.run_method_1_backtest(lottery_type, start_period, num_periods)
        
        if result.get('success'):
            return jsonify(APIResponse(
                success=True,
                data=result,
                message='方法一回測完成'
            ).to_dict())
        else:
            return jsonify(APIResponse(
                success=False,
                error=result.get('error', '回測失敗')
            ).to_dict()), 500
            
    except Exception as e:
        logger.error(f"方法一回測API錯誤: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'回測執行失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/backtesting/method2/<lottery_type>', methods=['POST'])
@handle_api_errors_local
@validate_lottery_type_local
def api_backtest_method2(lottery_type):
    """執行方法二回測 - 歷史校正"""
    if not BACKTESTING_AVAILABLE:
        return jsonify(APIResponse(
            success=False,
            error='回測驗證模組未正確載入'
        ).to_dict()), 500
    
    try:
        data = request.get_json() or {}
        calibration_start = data.get('calibration_start', '114000035')
        validation_periods = data.get('validation_periods', 20)
        
        # 初始化驗證器
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        validator = BacktestingValidator(db_path)
        
        # 執行方法二回測
        logger.info(f"執行方法二回測: {lottery_type}, 校正起始: {calibration_start}, 驗證期數: {validation_periods}")
        result = validator.run_method_2_backtest(lottery_type, calibration_start, validation_periods)
        
        if result.get('success'):
            return jsonify(APIResponse(
                success=True,
                data=result,
                message='方法二回測完成'
            ).to_dict())
        else:
            return jsonify(APIResponse(
                success=False,
                error=result.get('error', '回測失敗')
            ).to_dict()), 500
            
    except Exception as e:
        logger.error(f"方法二回測API錯誤: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'回測執行失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/backtesting/comprehensive/<lottery_type>', methods=['POST'])
@handle_api_errors_local
@validate_lottery_type_local
def api_backtest_comprehensive(lottery_type):
    """執行綜合回測 - 同時執行兩種方法並比較"""
    if not BACKTESTING_AVAILABLE:
        return jsonify(APIResponse(
            success=False,
            error='回測驗證模組未正確載入'
        ).to_dict()), 500
    
    try:
        # 執行綜合回測
        logger.info(f"執行綜合回測: {lottery_type}")
        result = run_comprehensive_backtest(lottery_type)
        
        if result.get('success'):
            return jsonify(APIResponse(
                success=True,
                data=result,
                message='綜合回測完成'
            ).to_dict())
        else:
            return jsonify(APIResponse(
                success=False,
                error=result.get('error', '綜合回測失敗')
            ).to_dict()), 500
            
    except Exception as e:
        logger.error(f"綜合回測API錯誤: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'綜合回測執行失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/backtesting/status', methods=['GET'])
@handle_api_errors_local
def api_backtest_status():
    """檢查回測系統狀態"""
    try:
        # 檢查數據庫連接
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        db_exists = os.path.exists(db_path)
        
        # 檢查模組載入狀態
        modules_loaded = BACKTESTING_AVAILABLE
        
        status = {
            'system_ready': db_exists and modules_loaded,
            'database_available': db_exists,
            'modules_loaded': modules_loaded,
            'database_path': db_path,
            'supported_lotteries': app_config.supported_lottery_types
        }
        
        return jsonify(APIResponse(
            success=True,
            data=status
        ).to_dict())
        
    except Exception as e:
        logger.error(f"狀態檢查錯誤: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'狀態檢查失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/backtesting/validate/<lottery_type>/<period>', methods=['POST'])
@handle_api_errors_local
@validate_lottery_type_local
def api_validate_single_prediction(lottery_type, period):
    """驗證單一期號的預測準確度"""
    if not BACKTESTING_AVAILABLE:
        return jsonify(APIResponse(
            success=False,
            error='回測驗證模組未正確載入'
        ).to_dict()), 500
    
    try:
        # 初始化驗證器
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        validator = BacktestingValidator(db_path)
        
        # 獲取該期的實際開獎結果
        actual_data = validator._get_historical_data_for_backtest(lottery_type, period, 1)
        if not actual_data:
            return jsonify(APIResponse(
                success=False,
                error=f'找不到期號 {period} 的開獎數據'
            ).to_dict()), 404
        
        target_period_data = actual_data[0]
        if target_period_data['period'] != int(period):
            return jsonify(APIResponse(
                success=False,
                error=f'期號不符: 期望 {period}, 實際 {target_period_data["period"]}'
            ).to_dict()), 400
        
        # 獲取用於預測的歷史數據（該期之前的數據）
        training_data = validator._get_historical_data_for_backtest(lottery_type, str(int(period) - 1), 50)
        if not training_data:
            return jsonify(APIResponse(
                success=False,
                error=f'找不到期號 {period} 之前的歷史數據'
            ).to_dict()), 404
        
        # 進行預測
        prediction = validator._make_prediction_for_period(lottery_type, training_data, period)
        if not prediction:
            return jsonify(APIResponse(
                success=False,
                error='預測失敗'
            ).to_dict()), 500
        
        # 評估準確度
        accuracy = validator._evaluate_prediction_accuracy(prediction, target_period_data)
        
        result_data = {
            'period': period,
            'lottery_type': lottery_type,
            'prediction': prediction,
            'actual_result': target_period_data,
            'accuracy_evaluation': accuracy
        }
        
        return jsonify(APIResponse(
            success=True,
            data=result_data,
            message=f'期號 {period} 預測驗證完成'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"單一預測驗證錯誤: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'預測驗證失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/backtesting')
def backtesting_dashboard():
    """回測驗證儀表板頁面"""
    return render_template('backtesting_dashboard.html')

# ============================================================================
# 校正預測模型 API 路由
# ============================================================================

@app.route('/api/calibrated_prediction/<lottery_type>', methods=['POST'])
@handle_api_errors_local
@validate_lottery_type_local
def api_calibrated_prediction(lottery_type):
    """使用校正模型進行預測"""
    if not CALIBRATED_MODEL_AVAILABLE:
        return jsonify(APIResponse(
            success=False,
            error='校正預測模型未正確載入'
        ).to_dict()), 500
    
    try:
        data = request.get_json() or {}
        use_dynamic_calibration = data.get('use_dynamic_calibration', True)
        
        # 初始化校正模型
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        model = CalibratedPredictionModel(db_path)
        
        # 執行校正預測
        logger.info(f"執行校正模型預測: {lottery_type}, 動態校正: {use_dynamic_calibration}")
        prediction = model.predict_with_calibration(lottery_type, use_dynamic_calibration)
        
        if prediction:
            # 獲取模型性能統計
            performance = model.get_model_performance(lottery_type)
            prediction['model_performance'] = performance
            
            return jsonify(APIResponse(
                success=True,
                data={'prediction': prediction},
                message='校正模型預測完成'
            ).to_dict())
        else:
            return jsonify(APIResponse(
                success=False,
                error='校正模型預測失敗'
            ).to_dict()), 500
            
    except Exception as e:
        logger.error(f"校正預測API錯誤: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'校正預測執行失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/calibrated_prediction/performance/<lottery_type>', methods=['GET'])
@handle_api_errors_local
@validate_lottery_type_local
def api_calibrated_model_performance(lottery_type):
    """獲取校正模型性能統計"""
    if not CALIBRATED_MODEL_AVAILABLE:
        return jsonify(APIResponse(
            success=False,
            error='校正預測模型未正確載入'
        ).to_dict()), 500
    
    try:
        # 初始化校正模型
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        model = CalibratedPredictionModel(db_path)
        
        # 獲取性能統計
        performance = model.get_model_performance(lottery_type)
        
        return jsonify(APIResponse(
            success=True,
            data=performance
        ).to_dict())
        
    except Exception as e:
        logger.error(f"獲取模型性能API錯誤: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'獲取模型性能失敗: {str(e)}'
        ).to_dict()), 500

# ============================================================================
# 六次機會策略 API 路由
# ============================================================================

@app.route('/api/six_chances_strategy/<lottery_type>', methods=['POST'])
@handle_api_errors_local
@validate_lottery_type_local
def api_six_chances_strategy(lottery_type):
    """生成六次機會智能投注策略"""
    try:
        # 導入六次機會策略模組
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from six_chances_strategy import SixChancesStrategy
        
        data = request.get_json() or {}
        strategy_type = data.get('strategy', 'mixed')  # 默認混合策略
        
        # 初始化策略系統
        strategy_system = SixChancesStrategy()
        
        logger.info(f"生成六次機會策略: {lottery_type}, 策略類型: {strategy_type}")
        
        # 生成策略結果
        result = strategy_system.generate_six_chances(strategy_type)
        
        # 添加彩票類型特定信息
        lottery_info = {
            'powercolor': {'name': '威力彩', 'main_numbers': 6, 'special_numbers': 1},
            'lotto649': {'name': '大樂透', 'main_numbers': 6, 'special_numbers': 1}, 
            'dailycash': {'name': '今彩539', 'main_numbers': 5, 'special_numbers': 0}
        }
        
        result['lottery_info'] = lottery_info.get(lottery_type, lottery_info['powercolor'])
        result['timestamp'] = datetime.now().isoformat()
        result['total_investment'] = result['total_chances'] * 50  # 每張50元
        
        # 添加策略說明
        strategy_descriptions = {
            'wheel': '輪盤覆蓋策略 - 選12個核心號碼，確保3個號碼的完整覆蓋',
            'zone': '區間平衡策略 - 號碼分布在不同區間，提高均勻性',
            'hotcold': '冷熱混合策略 - 結合高頻和低頻號碼',
            'math': '數學優化策略 - 基於組合數學的最優覆蓋',
            'pattern': '模式識別策略 - 利用常見開獎模式',
            'ai': 'AI集成策略 - 結合多個AI模型預測',
            'mixed': '混合策略 - 綜合運用六種不同策略'
        }
        
        result['strategy_description'] = strategy_descriptions.get(strategy_type, '未知策略')
        
        return jsonify(APIResponse(
            success=True,
            data=result,
            message=f'{result["lottery_info"]["name"]}六次機會策略生成成功'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"六次機會策略API錯誤: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'六次機會策略生成失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/six_chances_probability_analysis/<lottery_type>', methods=['GET'])
@handle_api_errors_local
@validate_lottery_type_local  
def api_six_chances_probability_analysis(lottery_type):
    """獲取六次機會概率分析"""
    try:
        # 導入概率分析模組
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from probability_analysis import ProbabilityAnalysis
        
        analyzer = ProbabilityAnalysis()
        
        logger.info(f"執行六次機會概率分析: {lottery_type}")
        
        # 獲取單次投注概率
        single_probs = analyzer.single_ticket_probability()
        
        # 獲取六次機會改善效果
        improvements = analyzer.six_tickets_improvement()
        
        # 執行期望值分析
        ev_result = analyzer.expected_value_analysis()
        
        # 策略覆蓋分析數據
        coverage_strategies = {
            '隨機6組': {'coverage': 0.632, 'description': '基礎隨機選號策略'},
            '輪盤覆蓋': {'coverage': 0.75, 'description': '核心號碼輪盤覆蓋系統'},
            '智能優化': {'coverage': 0.85, 'description': '數學優化最大覆蓋策略'}
        }
        
        # 組織分析結果
        analysis_result = {
            'single_ticket_probabilities': {
                prize: {
                    'probability': prob,
                    'odds': f"1/{int(1/prob):,}" if prob > 0 else "N/A"
                }
                for prize, prob in single_probs.items()
            },
            'six_chances_improvements': improvements,
            'expected_value_analysis': ev_result,
            'coverage_strategies': coverage_strategies,
            'lottery_type': lottery_type,
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'investment_cost': 300,
                'expected_return': ev_result['six_ev'],
                'roi_percentage': f"{ev_result['roi']:.1%}",
                'entertainment_cost': ev_result['investment'] - ev_result['six_ev']
            }
        }
        
        return jsonify(APIResponse(
            success=True,
            data=analysis_result,
            message='六次機會概率分析完成'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"六次機會概率分析API錯誤: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'概率分析失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/calibrated_prediction')
def calibrated_prediction_page():
    """校正預測模型頁面"""
    return render_template('calibrated_prediction.html')

@app.route('/api/highest_confidence_prediction/<lottery_type>', methods=['POST'])
@handle_api_errors_local
@validate_lottery_type_local
def api_highest_confidence_prediction(lottery_type):
    """最高信心度單組預測"""
    try:
        # 導入最高信心度預測器
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from highest_confidence_predictor import HighestConfidencePredictor
        
        predictor = HighestConfidencePredictor()
        
        logger.info(f"執行最高信心度預測: {lottery_type}")
        
        # 生成最高信心度預測
        result = predictor.extract_highest_confidence_numbers(lottery_type)
        
        # 格式化為標準預測格式
        lottery_info = {
            'powercolor': {'name': '威力彩'},
            'lotto649': {'name': '大樂透'}, 
            'dailycash': {'name': '今彩539'}
        }
        
        # 獲取下一期期號（簡化處理）
        import sqlite3
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        conn = sqlite3.connect(db_path)
        
        table_map = {'powercolor': 'Powercolor', 'lotto649': 'Lotto649', 'dailycash': 'DailyCash'}
        table_name = table_map.get(lottery_type, 'Powercolor')
        
        cursor = conn.cursor()
        cursor.execute(f"SELECT Period FROM {table_name} ORDER BY Period DESC LIMIT 1")
        latest_period = cursor.fetchone()
        next_period = str(int(latest_period[0]) + 1) if latest_period else "114000001"
        conn.close()
        
        prediction_data = {
            'prediction': {
                'period': next_period,
                'numbers': result['highest_confidence_numbers'],
                'special_number': result.get('highest_confidence_special'),
                'confidence': result['confidence_score'],
                'model_used': '最高信心度策略',
                'prediction_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'strategy_analysis': {
                    'base_strategies': 6,
                    'number_frequency': result['analysis']['number_frequency'],
                    'tail_distribution': result['analysis']['tail_distribution'],
                    'risk_assessment': result['analysis']['risk_assessment']
                },
                'cost_comparison': {
                    'single_bet': 50,
                    'six_chances': 300,
                    'cost_advantage': '83.3% 成本節省'
                }
            },
            'lottery_info': lottery_info.get(lottery_type, lottery_info['powercolor'])
        }
        
        return jsonify(APIResponse(
            success=True,
            data=prediction_data,
            message=f'{lottery_info.get(lottery_type, lottery_info["powercolor"])["name"]}最高信心度預測完成'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"最高信心度預測API錯誤: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'最高信心度預測失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/confidence_backtest/<lottery_type>', methods=['POST'])
@handle_api_errors_local
@validate_lottery_type_local
def api_confidence_backtest(lottery_type):
    """最高信心度策略回測"""
    try:
        data = request.get_json() or {}
        test_periods = data.get('periods', 30)  # 默認測試30期
        
        # 導入回測系統
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from confidence_backtest import ConfidenceBacktestSystem
        
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        backtest_system = ConfidenceBacktestSystem(db_path)
        
        logger.info(f"執行 {lottery_type} 回測，期數: {test_periods}")
        
        # 執行回測
        backtest_result = backtest_system.run_backtest_analysis(lottery_type, test_periods)
        
        return jsonify(APIResponse(
            success=True,
            data=backtest_result,
            message=f'{lottery_type} 最高信心度策略回測完成'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"回測API錯誤: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'回測執行失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/strategy_comparison/<lottery_type>', methods=['POST'])
@handle_api_errors_local
@validate_lottery_type_local
def api_strategy_comparison(lottery_type):
    """策略比較分析"""
    try:
        # 導入必要模組
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from six_chances_strategy import SixChancesStrategy
        from highest_confidence_predictor import HighestConfidencePredictor
        from confidence_backtest import ConfidenceBacktestSystem
        
        strategy_system = SixChancesStrategy()
        confidence_predictor = HighestConfidencePredictor()
        
        logger.info(f"執行 {lottery_type} 策略比較分析")
        
        # 1. 六次機會策略
        six_chances = strategy_system.generate_six_chances('mixed')
        
        # 2. 最高信心度策略
        highest_confidence = confidence_predictor.extract_highest_confidence_numbers(lottery_type)
        
        # 3. 簡化回測（10期快速測試）
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        backtest_system = ConfidenceBacktestSystem(db_path)
        
        try:
            quick_backtest = backtest_system.run_backtest_analysis(lottery_type, 10)
        except Exception as e:
            logger.warning(f"回測失敗，使用模擬數據: {str(e)}")
            quick_backtest = {
                'performance': {
                    'hit_rate': '5.0%',
                    'roi': -0.8,
                    'total_cost': 500,
                    'total_winnings': 100
                }
            }
        
        comparison_result = {
            'strategies': {
                'six_chances': {
                    'name': '六次機會分散策略',
                    'investment': six_chances['total_investment'],
                    'combinations': len(six_chances['combinations']),
                    'coverage_rate': six_chances['coverage_analysis']['coverage_rate'],
                    'risk_level': '低風險（分散投注）',
                    'target_audience': '保守型投資者',
                    'advantages': ['分散風險', '覆蓋面廣', '多重機會'],
                    'disadvantages': ['成本較高', '單次投入大']
                },
                'highest_confidence': {
                    'name': '最高信心度精準策略',
                    'investment': 50,
                    'combinations': 1,
                    'confidence_score': f"{highest_confidence['confidence_score']:.1%}",
                    'risk_level': '高風險（單一組合）',
                    'target_audience': '積極型投資者',
                    'advantages': ['成本低廉', '精準選號', '高效投注'],
                    'disadvantages': ['風險集中', '命中依賴性高']
                }
            },
            'performance_comparison': {
                'cost_efficiency': {
                    'six_chances_cost': 300,
                    'single_confidence_cost': 50,
                    'efficiency_ratio': '6:1'
                },
                'risk_return': {
                    'six_chances_risk': '低（分散）',
                    'single_confidence_risk': '高（集中）',
                    'expected_roi_six': '-70% ~ -40%',
                    'expected_roi_single': f"{quick_backtest['performance']['roi']:.0%}",
                }
            },
            'recommendations': {
                'conservative_investor': '建議選擇六次機會策略，風險較低',
                'aggressive_investor': '可考慮最高信心度策略，成本效益較好',
                'hybrid_approach': '輪流使用兩種策略，平衡風險與成本'
            },
            'quick_backtest': quick_backtest.get('performance', {}),
            'lottery_type': lottery_type
        }
        
        return jsonify(APIResponse(
            success=True,
            data=comparison_result,
            message='策略比較分析完成'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"策略比較API錯誤: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'策略比較分析失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/six_chances')
def six_chances_page():
    """六次機會策略頁面"""
    return render_template('six_chances.html')

# ============================================================================
# 最高信心度策略預測記錄API
# ============================================================================

@app.route('/api/confidence_record/<lottery_type>', methods=['POST'])
def api_generate_confidence_record(lottery_type):
    """生成並記錄最高信心度預測"""
    try:
        from confidence_prediction_recorder import ConfidencePredictionRecorder
        
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        recorder = ConfidencePredictionRecorder(db_path)
        result = recorder.generate_and_record_prediction(lottery_type)
        
        if result['success']:
            logger.info(f"最高信心度預測記錄生成成功 - {lottery_type} 期號: {result['period']}")
            return jsonify(APIResponse(
                success=True,
                data=result,
                message=f'最高信心度預測生成成功 - 期號: {result["period"]}'
            ).to_dict())
        else:
            return jsonify(APIResponse(
                success=False,
                error=result.get('error', '預測生成失敗')
            ).to_dict()), 400
            
    except Exception as e:
        logger.error(f"最高信心度預測記錄API錯誤: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'預測記錄生成失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/confidence_batch', methods=['POST'])
def api_batch_generate_confidence():
    """批次生成最高信心度預測"""
    try:
        data = request.get_json() or {}
        lottery_types = data.get('lottery_types', ['powercolor', 'lotto649', 'dailycash'])
        
        from confidence_prediction_recorder import ConfidencePredictionRecorder
        
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        recorder = ConfidencePredictionRecorder(db_path)
        result = recorder.batch_generate_predictions(lottery_types)
        
        summary = result['batch_summary']
        success_rate = summary['successful_predictions'] / summary['total_requests']
        
        logger.info(f"批次預測完成 - 成功率: {success_rate:.1%}")
        
        return jsonify(APIResponse(
            success=True,
            data=result,
            message=f'批次預測完成 - 成功: {summary["successful_predictions"]}, 失敗: {summary["failed_predictions"]}'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"批次預測API錯誤: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'批次預測失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/confidence_records/<lottery_type>')
def api_get_confidence_records(lottery_type):
    """獲取最高信心度預測歷史"""
    try:
        limit = request.args.get('limit', 20, type=int)
        limit = min(max(limit, 1), 100)  # 限制在1-100之間
        
        from confidence_prediction_recorder import ConfidencePredictionRecorder
        
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        recorder = ConfidencePredictionRecorder(db_path)
        history = recorder.get_confidence_prediction_history(lottery_type, limit)
        
        return jsonify(APIResponse(
            success=True,
            data={
                'history': history,
                'lottery_type': lottery_type,
                'total_records': len(history),
                'method_name': '最高信心度策略'
            },
            message=f'獲取到 {len(history)} 條預測記錄'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"預測歷史API錯誤: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'獲取預測歷史失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/confidence_performance/<lottery_type>')
def api_analyze_confidence_performance(lottery_type):
    """分析最高信心度策略表現"""
    try:
        periods = request.args.get('periods', 50, type=int)
        periods = min(max(periods, 5), 200)  # 限制在5-200之間
        
        from confidence_prediction_recorder import ConfidencePredictionRecorder
        
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        recorder = ConfidencePredictionRecorder(db_path)
        analysis = recorder.analyze_confidence_strategy_performance(lottery_type, periods)
        
        if analysis['success']:
            logger.info(f"策略表現分析完成 - {lottery_type}")
            return jsonify(APIResponse(
                success=True,
                data=analysis,
                message=f'策略表現分析完成 - 分析期數: {analysis["strategy_performance"]["total_predictions"]}'
            ).to_dict())
        else:
            return jsonify(APIResponse(
                success=False,
                message=analysis.get('message', '暫無足夠數據進行分析'),
                data={'analysis_available': False}
            ).to_dict())
        
    except Exception as e:
        logger.error(f"策略表現分析API錯誤: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'策略表現分析失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/stable_record/<lottery_type>', methods=['POST'])
def api_generate_stable_confidence(lottery_type):
    """生成穩定信心度預測記錄"""
    try:
        from confidence_prediction_recorder import ConfidencePredictionRecorder
        
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        recorder = ConfidencePredictionRecorder(db_path)
        result = recorder.generate_and_record_stable_prediction(lottery_type)
        
        if result['success']:
            logger.info(f"穩定信心度預測記錄生成成功 - {lottery_type} 期號: {result['period']}")
            return jsonify(APIResponse(
                success=True,
                data=result,
                message=f'穩定信心度預測生成成功 - 期號: {result["period"]}'
            ).to_dict())
        else:
            return jsonify(APIResponse(
                success=False,
                error=result.get('error', '穩定預測生成失敗')
            ).to_dict()), 400
            
    except Exception as e:
        logger.error(f"穩定信心度預測記錄API錯誤: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'穩定預測記錄生成失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/confidence_dashboard/<lottery_type>')
def api_confidence_dashboard(lottery_type):
    """最高信心度策略儀表板數據"""
    try:
        from confidence_prediction_recorder import ConfidencePredictionRecorder
        from highest_confidence_predictor import HighestConfidencePredictor
        
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        recorder = ConfidencePredictionRecorder(db_path)
        predictor = HighestConfidencePredictor()
        
        # 1. 獲取最新預測
        latest_analysis = predictor.extract_highest_confidence_numbers(lottery_type)
        
        # 2. 獲取近期記錄
        recent_history = recorder.get_confidence_prediction_history(lottery_type, 10)
        
        # 3. 策略表現分析
        performance = recorder.analyze_confidence_strategy_performance(lottery_type, 30)
        
        # 4. 構建儀表板數據
        dashboard_data = {
            'lottery_type': lottery_type,
            'current_prediction': {
                'numbers': latest_analysis['highest_confidence_numbers'],
                'special': latest_analysis.get('highest_confidence_special'),
                'confidence_score': latest_analysis['confidence_score'],
                'risk_assessment': latest_analysis['analysis']['risk_assessment'],
                'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            },
            'recent_history': recent_history[:5],  # 只顯示最近5條
            'performance_summary': {
                'has_data': performance['success'],
                'summary': performance.get('strategy_performance', {}) if performance['success'] else None,
                'comparison': performance.get('comparison_with_backtest', {}) if performance['success'] else None
            },
            'strategy_info': {
                'method_name': '最高信心度策略',
                'based_on': '6種策略共識分析',
                'cost_per_bet': 50,
                'advantages': ['成本低廉', '策略共識', '回測驗證'],
                'target_users': '積極型投資者'
            },
            'stats': {
                'total_history': len(recent_history),
                'method_category': '策略共識',
                'backtest_validated': True
            }
        }
        
        return jsonify(APIResponse(
            success=True,
            data=dashboard_data,
            message='儀表板數據載入完成'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"儀表板API錯誤: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'儀表板數據載入失敗: {str(e)}'
        ).to_dict()), 500

# ============================================================================
# 可觀察預測追蹤 API
# ============================================================================

@app.route('/api/observable/start_tracking/<lottery_type>', methods=['POST'])
def api_start_observable_tracking(lottery_type):
    """啟動可觀察預測追蹤會話"""
    try:
        from observable_prediction_tracker import ObservablePredictionTracker
        
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        tracker = ObservablePredictionTracker(db_path)
        result = tracker.start_tracking_session(lottery_type)
        
        if result['success']:
            logger.info(f"可觀察追蹤會話啟動成功 - {lottery_type}")
            return jsonify(APIResponse(
                success=True,
                data=result,
                message=f'追蹤會話啟動成功 - 期號: {result["tracking_session"]["period"]}'
            ).to_dict())
        else:
            return jsonify(APIResponse(
                success=False,
                error=result.get('error', '追蹤會話啟動失敗')
            ).to_dict()), 400
            
    except Exception as e:
        logger.error(f"啟動可觀察追蹤失敗: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'追蹤會話啟動失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/observable/dashboard/<lottery_type>')
def api_observable_dashboard(lottery_type):
    """獲取可觀察儀表板數據"""
    try:
        from observable_prediction_tracker import ObservablePredictionTracker
        
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        tracker = ObservablePredictionTracker(db_path)
        dashboard = tracker.get_observation_dashboard(lottery_type)
        
        return jsonify(APIResponse(
            success=True,
            data=dashboard,
            message='可觀察儀表板數據載入完成'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"獲取可觀察儀表板失敗: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'儀表板數據載入失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/observable/update_results/<lottery_type>', methods=['POST'])
def api_update_observable_results(lottery_type):
    """更新可觀察預測結果"""
    try:
        from observable_prediction_tracker import ObservablePredictionTracker
        
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        tracker = ObservablePredictionTracker(db_path)
        result = tracker.update_prediction_results(lottery_type)
        
        if result['success']:
            updated_count = result.get('updated_predictions', 0)
            return jsonify(APIResponse(
                success=True,
                data=result,
                message=f'預測結果更新完成 - 更新了 {updated_count} 條記錄'
            ).to_dict())
        else:
            return jsonify(APIResponse(
                success=False,
                error=result.get('error', '預測結果更新失敗')
            ).to_dict()), 400
            
    except Exception as e:
        logger.error(f"更新可觀察預測結果失敗: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'預測結果更新失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/observable/report/<lottery_type>')
def api_observable_tracking_report(lottery_type):
    """生成可觀察追蹤報告"""
    try:
        from observable_prediction_tracker import ObservablePredictionTracker
        
        periods = int(request.args.get('periods', 20))
        
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        tracker = ObservablePredictionTracker(db_path)
        report = tracker.generate_tracking_report(lottery_type, periods)
        
        if report.get('success', True):
            return jsonify(APIResponse(
                success=True,
                data=report,
                message=f'追蹤報告生成完成 - 分析了最近 {periods} 期'
            ).to_dict())
        else:
            return jsonify(APIResponse(
                success=False,
                error=report.get('error', '追蹤報告生成失敗')
            ).to_dict()), 400
            
    except Exception as e:
        logger.error(f"生成可觀察追蹤報告失敗: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'追蹤報告生成失敗: {str(e)}'
        ).to_dict()), 500

# ============================================================================
# 手動數據輸入 API 
# ============================================================================

@app.route('/api/manual_add_result', methods=['POST'])
@handle_api_errors_local
def api_manual_add_result():
    """手動添加開獎結果"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify(APIResponse(
                success=False,
                error="缺少請求數據"
            ).to_dict()), 400
        
        # 驗證必要欄位
        required_fields = ['lottery_type', 'period', 'date', 'main_numbers']
        for field in required_fields:
            if field not in data:
                return jsonify(APIResponse(
                    success=False,
                    error=f"缺少必要欄位: {field}"
                ).to_dict()), 400
        
        lottery_type = data['lottery_type']
        period = data['period']
        date = data['date']
        main_numbers = data['main_numbers']
        special_number = data.get('special_number')
        
        # 驗證數據
        if lottery_type not in ['powercolor', 'lotto649', 'dailycash']:
            return jsonify(APIResponse(
                success=False,
                error=f"不支援的彩票類型: {lottery_type}"
            ).to_dict()), 400
        
        # 驗證號碼數量
        expected_count = {
            'powercolor': 6,
            'lotto649': 6,
            'dailycash': 5
        }
        
        if len(main_numbers) != expected_count[lottery_type]:
            return jsonify(APIResponse(
                success=False,
                error=f"{lottery_type} 需要 {expected_count[lottery_type]} 個主號碼"
            ).to_dict()), 400
        
        # 驗證特別號
        if lottery_type in ['powercolor', 'lotto649'] and not special_number:
            return jsonify(APIResponse(
                success=False,
                error=f"{lottery_type} 需要特別號"
            ).to_dict()), 400
        
        # 導入數據庫管理器
        from data.db_manager import DBManager
        from datetime import datetime
        
        db_manager = DBManager()
        conn = db_manager.create_connection()
        cursor = conn.cursor()
        
        try:
            # 根據彩票類型插入數據
            if lottery_type == 'powercolor':
                table_name = 'Powercolor'
                insert_sql = """
                INSERT INTO {} (Period, Sdate, Anumber1, Anumber2, Anumber3, 
                               Anumber4, Anumber5, Anumber6, Second_district)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """.format(table_name)
                
                cursor.execute(insert_sql, (
                    period, date,
                    main_numbers[0], main_numbers[1], main_numbers[2],
                    main_numbers[3], main_numbers[4], main_numbers[5],
                    special_number
                ))
                
            elif lottery_type == 'lotto649':
                table_name = 'Lotto649'
                insert_sql = """
                INSERT INTO {} (Period, Sdate, Anumber1, Anumber2, Anumber3, 
                               Anumber4, Anumber5, Anumber6, SpecialNumber)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """.format(table_name)
                
                cursor.execute(insert_sql, (
                    period, date,
                    main_numbers[0], main_numbers[1], main_numbers[2],
                    main_numbers[3], main_numbers[4], main_numbers[5],
                    special_number
                ))
                
            elif lottery_type == 'dailycash':
                table_name = 'DailyCash'
                insert_sql = """
                INSERT INTO {} (Period, Sdate, Anumber1, Anumber2, Anumber3, 
                               Anumber4, Anumber5)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                """.format(table_name)
                
                cursor.execute(insert_sql, (
                    period, date,
                    main_numbers[0], main_numbers[1], main_numbers[2],
                    main_numbers[3], main_numbers[4]
                ))
            
            conn.commit()
            
            return jsonify(APIResponse(
                success=True,
                data={
                    'lottery_type': lottery_type,
                    'period': period,
                    'date': date,
                    'main_numbers': main_numbers,
                    'special_number': special_number,
                    'table_name': table_name
                },
                message=f"成功添加 {lottery_type} 期號 {period} 的開獎結果"
            ).to_dict())
            
        except Exception as e:
            conn.rollback()
            if "UNIQUE constraint failed" in str(e):
                return jsonify(APIResponse(
                    success=False,
                    error=f"期號 {period} 已存在，請檢查是否重複添加"
                ).to_dict()), 400
            else:
                raise e
        finally:
            conn.close()
        
    except Exception as e:
        logger.error(f"手動添加開獎結果失敗: {e}")
        return jsonify(APIResponse(
            success=False,
            error=f"添加開獎結果時發生錯誤: {str(e)}"
        ).to_dict()), 500

# ============================================================================
# 數據自動更新 API (已停用 - 防止假數據插入)
# ============================================================================

@app.route('/api/data/update/<lottery_type>', methods=['POST'])
def api_update_lottery_data(lottery_type):
    """數據更新功能已停用 - 防止假數據插入"""
    return jsonify(APIResponse(
        success=False,
        error='數據自動更新功能已停用，防止假數據插入。請等待官方數據源修復或手動添加開獎結果。'
    ).to_dict()), 503  # Service Unavailable

@app.route('/api/data/update_all', methods=['POST'])
def api_update_all_lottery_data():
    """批量數據更新功能已停用 - 防止假數據插入"""
    return jsonify(APIResponse(
        success=False,
        error='批量數據更新功能已停用，防止假數據插入。請等待官方數據源修復或手動添加開獎結果。'
    ).to_dict()), 503  # Service Unavailable

@app.route('/api/data/check_update/<lottery_type>')
def api_check_data_update_needed(lottery_type):
    """檢查數據更新需求 - 智能檢查功能已停用"""
    # 返回模擬的檢查結果，但不執行實際更新
    try:
        import sqlite3
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        conn = sqlite3.connect(db_path)
        
        # 獲取最新期號（不更新，只檢查）
        table_map = {
            'powercolor': 'Powercolor',
            'lotto649': 'Lotto649', 
            'dailycash': 'DailyCash'
        }
        
        table_name = table_map.get(lottery_type)
        if not table_name:
            raise ValueError(f'不支援的彩票類型: {lottery_type}')
            
        cursor = conn.execute(f"SELECT MAX(Period), MAX(Sdate) FROM {table_name}")
        result = cursor.fetchone()
        conn.close()
        
        latest_period = result[0] if result[0] else 0
        latest_date = result[1] if result[1] else '未知'
        
        return jsonify(APIResponse(
            success=True,
            updated=False,  # 永遠不更新
            data={
                'latest_period_before': latest_period,
                'latest_period_after': latest_period,
                'updated': False,
                'message': f'數據檢查完成 - 最新期號: {latest_period} ({latest_date})。自動更新已停用，防止假數據插入。',
                'last_update': latest_date,
                'new_records_count': 0
            },
            message='數據檢查完成，自動更新已停用'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"檢查數據狀態失敗: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'檢查數據狀態失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/data/update_status')
def api_data_update_status():
    """獲取所有彩票的數據狀態 - 更新功能已停用"""
    try:
        import sqlite3
        from datetime import datetime
        
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'lottery_data.db')
        
        status = {
            'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'lotteries': {},
            'system_message': '自動更新功能已停用，防止假數據插入'
        }
        
        lottery_tables = {
            'powercolor': 'Powercolor',
            'lotto649': 'Lotto649',
            'dailycash': 'DailyCash'
        }
        
        conn = sqlite3.connect(db_path)
        
        for lottery_type, table_name in lottery_tables.items():
            try:
                cursor = conn.execute(f"SELECT MAX(Period), MAX(Sdate) FROM {table_name}")
                result = cursor.fetchone()
                
                latest_period = result[0] if result[0] else 0
                latest_date = result[1] if result[1] else '未知'
                
                status['lotteries'][lottery_type] = {
                    'latest_period': latest_period,
                    'last_update': latest_date,
                    'hours_since_update': None,
                    'needs_update': False,  # 永遠不需要更新
                    'auto_update_disabled': True
                }
                
            except Exception as e:
                status['lotteries'][lottery_type] = {
                    'error': str(e)
                }
        
        conn.close()
        
        return jsonify(APIResponse(
            success=True,
            data=status,
            message='數據狀態獲取完成，自動更新已停用'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"獲取數據狀態失敗: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'獲取數據狀態失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/data/integrity_report')
def api_data_integrity_report():
    """獲取數據完整性報告 - 新增功能"""
    if not DATA_INTEGRITY_AVAILABLE:
        return jsonify(APIResponse(
            success=False,
            error='數據完整性框架不可用'
        ).to_dict()), 503
    
    try:
        integrity_report = check_data_integrity()
        
        return jsonify(APIResponse(
            success=True,
            data=integrity_report,
            message='數據完整性報告生成完成'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"生成數據完整性報告失敗: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'生成數據完整性報告失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/data/verify_record', methods=['POST'])
def api_verify_data_record():
    """手動插入經過驗證的彩票數據 - 新增功能"""
    if not DATA_INTEGRITY_AVAILABLE:
        return jsonify(APIResponse(
            success=False,
            error='數據完整性框架不可用'
        ).to_dict()), 503
    
    try:
        data = request.get_json()
        
        lottery_type = data.get('lottery_type')
        period = data.get('period')
        main_numbers = data.get('main_numbers', [])
        special_number = data.get('special_number')
        date = data.get('date')
        source_info = data.get('source_info', 'manual_verification')
        
        if not all([lottery_type, period, main_numbers]):
            return jsonify(APIResponse(
                success=False,
                error='缺少必要參數: lottery_type, period, main_numbers'
            ).to_dict()), 400
        
        # 使用真實數據管理器插入數據
        success, message = insert_verified_lottery_result(
            lottery_type, period, main_numbers, special_number, date, source_info
        )
        
        if success:
            logger.info(f"手動驗證數據插入成功: {lottery_type} 第{period}期")
            return jsonify(APIResponse(
                success=True,
                message=f'數據驗證並插入成功: {message}'
            ).to_dict())
        else:
            return jsonify(APIResponse(
                success=False,
                error=f'數據驗證失敗: {message}'
            ).to_dict()), 400
            
    except Exception as e:
        logger.error(f"驗證數據插入失敗: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'驗證數據插入失敗: {str(e)}'
        ).to_dict()), 500

# ============================================================================
# 官方數據源API端點
# ============================================================================

@app.route('/api/official_data/fetch/<lottery_type>')
def api_fetch_official_data(lottery_type):
    """從台灣彩券官方網站獲取最新數據"""
    try:
        from taiwan_lottery_api import TaiwanLotteryAPI
        from dataclasses import asdict
        
        api = TaiwanLotteryAPI()
        limit = request.args.get('limit', 10, type=int)
        
        # 獲取官方數據
        results = api.fetch_latest_draw_results(lottery_type, limit)
        
        return jsonify(APIResponse(
            success=True,
            data={
                'lottery_type': lottery_type,
                'count': len(results),
                'results': [asdict(result) for result in results],
                'timestamp': datetime.now().isoformat()
            },
            message=f'成功獲取 {len(results)} 筆官方數據'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"獲取官方數據失敗: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'獲取官方數據失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/official_data/sync/<lottery_type>', methods=['POST'])
def api_sync_official_data(lottery_type):
    """同步官方數據到數據庫"""
    try:
        from taiwan_lottery_api import TaiwanLotteryAPI
        
        api = TaiwanLotteryAPI()
        data = request.get_json() or {}
        limit = data.get('limit', 20)
        
        # 獲取官方數據
        results = api.fetch_latest_draw_results(lottery_type, limit)
        
        if not results:
            return jsonify(APIResponse(
                success=False,
                error='無法獲取官方數據'
            ).to_dict()), 404
        
        # 同步到數據庫
        success_count, failed_count, error_messages = api.sync_to_database(results)
        
        logger.info(f"官方數據同步完成: {lottery_type} - 成功 {success_count}，失敗 {failed_count}")
        
        return jsonify(APIResponse(
            success=True,
            data={
                'lottery_type': lottery_type,
                'fetched_count': len(results),
                'success_count': success_count,
                'failed_count': failed_count,
                'error_messages': error_messages,
                'sync_timestamp': datetime.now().isoformat()
            },
            message=f'數據同步完成：成功 {success_count} 筆，失敗 {failed_count} 筆'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"數據同步失敗: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'數據同步失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/official_data/auto_sync', methods=['POST'])
def api_auto_sync_official_data():
    """執行所有彩票類型的自動同步"""
    try:
        from taiwan_lottery_api import get_auto_sync_manager
        
        sync_manager = get_auto_sync_manager()
        sync_report = sync_manager.perform_auto_sync()
        
        logger.info(f"自動同步完成: 總成功 {sync_report['total_success']}，總失敗 {sync_report['total_failed']}")
        
        return jsonify(APIResponse(
            success=True,
            data=sync_report,
            message=f"自動同步完成：總成功 {sync_report['total_success']} 筆，總失敗 {sync_report['total_failed']} 筆"
        ).to_dict())
        
    except Exception as e:
        logger.error(f"自動同步失敗: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'自動同步失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/official_data/sync_status')
def api_sync_status():
    """檢查各彩票類型的同步狀態"""
    try:
        from taiwan_lottery_api import get_auto_sync_manager
        
        sync_manager = get_auto_sync_manager()
        
        status = {
            'current_time': datetime.now().isoformat(),
            'sync_status': {}
        }
        
        for lottery_type in ['powercolor', 'lotto649', 'dailycash']:
            should_sync = sync_manager.should_sync(lottery_type)
            last_sync = sync_manager.last_sync.get(lottery_type)
            
            status['sync_status'][lottery_type] = {
                'should_sync': should_sync,
                'last_sync': last_sync.isoformat() if last_sync else None,
                'next_sync_due': (last_sync + sync_manager.sync_intervals[lottery_type]).isoformat() if last_sync else None,
                'lottery_name': get_lottery_name(lottery_type)
            }
        
        return jsonify(APIResponse(
            success=True,
            data=status,
            message='同步狀態獲取成功'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"檢查同步狀態失敗: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'檢查同步狀態失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/official_data/validation_report')
def api_validation_report():
    """獲取官方數據驗證報告"""
    try:
        from taiwan_lottery_api import TaiwanLotteryAPI
        
        # 檢查各彩票類型的數據完整性
        validation_report = {
            'timestamp': datetime.now().isoformat(),
            'validation_results': {},
            'overall_status': 'healthy'
        }
        
        # 結合數據完整性報告
        if DATA_INTEGRITY_AVAILABLE:
            integrity_report = check_data_integrity()
            validation_report['integrity_score'] = integrity_report.get('integrity_score', 0)
            validation_report['fake_data_blocked'] = integrity_report.get('fake_data_attempts_blocked', 0)
        
        for lottery_type in ['powercolor', 'lotto649', 'dailycash']:
            validation_report['validation_results'][lottery_type] = {
                'lottery_name': get_lottery_name(lottery_type),
                'status': 'ready',
                'last_check': datetime.now().isoformat()
            }
        
        return jsonify(APIResponse(
            success=True,
            data=validation_report,
            message='驗證報告生成成功'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"生成驗證報告失敗: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'生成驗證報告失敗: {str(e)}'
        ).to_dict()), 500

# ============================================================================
# 智能數據檢查 API 端點
# ============================================================================

@app.route('/api/intelligent_check')
def api_intelligent_check():
    """智能數據檢查"""
    try:
        if not INTELLIGENT_SYNC_AVAILABLE:
            return jsonify(APIResponse(
                success=False,
                error='智能同步系統不可用'
            ).to_dict()), 503
            
        data_checker = get_data_checker()
        check_results, recommendation = data_checker.perform_intelligent_check()
        
        # 轉換檢查結果為字典格式
        check_data = {
            lottery_type: {
                'lottery_type': result.lottery_type,
                'lottery_name': result.lottery_name,
                'current_period': result.current_period,
                'latest_available_period': result.latest_available_period,
                'has_new_data': result.has_new_data,
                'data_age_hours': result.data_age_hours,
                'sync_needed': result.sync_needed,
                'check_timestamp': result.check_timestamp,
                'error_message': result.error_message
            } for lottery_type, result in check_results.items()
        }
        
        # 轉換建議為字典格式
        recommendation_data = {
            'immediate_sync_needed': recommendation.immediate_sync_needed,
            'lottery_types_to_sync': recommendation.lottery_types_to_sync,
            'priority_level': recommendation.priority_level,
            'estimated_new_records': recommendation.estimated_new_records,
            'recommendation_reason': recommendation.recommendation_reason,
            'next_check_time': recommendation.next_check_time.isoformat()
        }
        
        return jsonify(APIResponse(
            success=True,
            data={
                'check_results': check_data,
                'recommendation': recommendation_data
            },
            message='智能數據檢查完成'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"智能數據檢查失敗: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'智能數據檢查失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/intelligent_sync', methods=['POST'])
def api_intelligent_sync():
    """智能數據同步（先檢查再同步）"""
    try:
        if not INTELLIGENT_SYNC_AVAILABLE:
            return jsonify(APIResponse(
                success=False,
                error='智能同步系統不可用'
            ).to_dict()), 503
            
        scraper = get_updated_scraper()
        sync_results = scraper.auto_sync_with_check()
        
        return jsonify(APIResponse(
            success=True,
            data=sync_results,
            message='智能同步完成'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"智能同步失敗: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'智能同步失敗: {str(e)}'
        ).to_dict()), 500

@app.route('/api/scrape_latest_results')
def api_scrape_latest_results():
    """直接爬取最新開獎結果"""
    try:
        if not INTELLIGENT_SYNC_AVAILABLE:
            return jsonify(APIResponse(
                success=False,
                error='智能同步系統不可用'
            ).to_dict()), 503
            
        scraper = get_updated_scraper()
        results = scraper.scrape_latest_results()
        
        # 轉換結果為字典格式
        results_data = {}
        for lottery_type, result in results.items():
            if result:
                results_data[lottery_type] = {
                    'lottery_type': result.lottery_type,
                    'lottery_name': result.lottery_name,
                    'period': result.period,
                    'draw_date': result.draw_date,
                    'main_numbers': result.main_numbers,
                    'special_number': result.special_number,
                    'scraped_at': result.scraped_at
                }
            else:
                results_data[lottery_type] = None
        
        return jsonify(APIResponse(
            success=True,
            data=results_data,
            message='爬取最新結果完成'
        ).to_dict())
        
    except Exception as e:
        logger.error(f"爬取最新結果失敗: {str(e)}")
        return jsonify(APIResponse(
            success=False,
            error=f'爬取最新結果失敗: {str(e)}'
        ).to_dict()), 500

# ============================================================================
# 策略觀察表 API 集成
# ============================================================================

# 導入策略API
try:
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from strategy_api import strategy_api
    app.register_blueprint(strategy_api)
    logger.info("策略觀察表API載入成功")
    
    # 添加策略觀察表頁面路由
    @app.route('/strategy_observer')
    def strategy_observer():
        """策略觀察表頁面"""
        return render_template('strategy_observer.html')
    
except ImportError as e:
    logger.warning(f"策略API載入失敗: {e}")
except Exception as e:
    logger.error(f"策略API集成錯誤: {e}")

# ============================================================================
# 策略保存API
# ============================================================================

@app.route('/api/save_prediction/<lottery_type>', methods=['POST'])
@handle_api_errors_local
@validate_lottery_type_local
def api_save_prediction(lottery_type):
    """保存預測策略到資料庫"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify(APIResponse(
                success=False,
                error="缺少預測資料"
            ).to_dict()), 400
        
        # 驗證必要欄位
        required_fields = ['numbers', 'strategy_type', 'confidence']
        for field in required_fields:
            if field not in data:
                return jsonify(APIResponse(
                    success=False,
                    error=f"缺少必要欄位: {field}"
                ).to_dict()), 400
        
        # 解析資料
        numbers = data['numbers']
        strategy_type = data['strategy_type']
        confidence = float(data['confidence'])
        target_period = data.get('target_period', 0)
        
        # 導入模組
        from data.db_manager import DBManager
        from datetime import datetime
        import uuid
        
        db_manager = DBManager()
        
        # 根據彩票類型保存
        if lottery_type == 'powercolor':
            if isinstance(numbers, list) and len(numbers) >= 7:
                conn = db_manager.create_connection()
                cursor = conn.cursor()
                
                prediction_id = str(uuid.uuid4())[:8]
                insert_sql = """
                INSERT INTO PowercolorPredictions 
                (Period, PredictionDate, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredS,
                 PredictionMethod, Confidence, ModelVersion, MethodCategory, PredictionID, LastUpdated)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                cursor.execute(insert_sql, (
                    target_period,
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    numbers[0], numbers[1], numbers[2], numbers[3], numbers[4], numbers[5], numbers[6],
                    strategy_type, confidence, 'Web-Strategy-Generator-v1.0', 'User-Generated',
                    prediction_id, datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ))
                
                conn.commit()
                conn.close()
                
                return jsonify(APIResponse(
                    success=True,
                    data={
                        'message': '威力彩預測已成功保存',
                        'prediction_id': prediction_id,
                        'numbers': numbers,
                        'confidence': confidence,
                        'strategy_type': strategy_type
                    }
                ).to_dict())
        
        return jsonify(APIResponse(
            success=False,
            error=f"不支援的彩票類型: {lottery_type}"
        ).to_dict()), 400
        
    except Exception as e:
        logger.error(f"保存預測失敗: {e}")
        return jsonify(APIResponse(
            success=False,
            error=f"保存預測時發生錯誤: {str(e)}"
        ).to_dict()), 500

@app.route('/api/saved_predictions/<lottery_type>')
@handle_api_errors_local
@validate_lottery_type_local
def api_get_saved_predictions(lottery_type):
    """獲取已保存的預測"""
    try:
        from data.db_manager import DBManager
        
        db_manager = DBManager()
        conn = db_manager.create_connection()
        cursor = conn.cursor()
        
        limit = min(int(request.args.get('limit', 10)), 50)
        
        if lottery_type == 'powercolor':
            query = """
            SELECT PredictionID, PredictionDate, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredS,
                   PredictionMethod, Confidence, MethodCategory
            FROM PowercolorPredictions
            WHERE MethodCategory = 'User-Generated'
            ORDER BY PredictionDate DESC
            LIMIT ?
            """
            
            cursor.execute(query, (limit,))
            results = cursor.fetchall()
            conn.close()
            
            predictions = []
            for row in results:
                prediction = {
                    'id': row[0],
                    'date': row[1],
                    'numbers': [row[2], row[3], row[4], row[5], row[6], row[7], row[8]],
                    'strategy_type': row[9],
                    'confidence': row[10],
                    'category': row[11]
                }
                predictions.append(prediction)
        
        return jsonify(APIResponse(
            success=True,
            data={
                'predictions': predictions,
                'total': len(predictions),
                'lottery_type': lottery_type
            }
        ).to_dict())
        
    except Exception as e:
        logger.error(f"獲取保存的預測失敗: {e}")
        return jsonify(APIResponse(
            success=False,
            error=f"獲取預測資料時發生錯誤: {str(e)}"
        ).to_dict()), 500

# ============================================================================
# 主程序入口
# ============================================================================

if __name__ == '__main__':
    logger.info("啟動彩票預測系統Web應用")
    logger.info(f"核心模組可用: {CORE_MODULES_AVAILABLE}")
    # Phase 3 功能已移除
    
    app.run(
        host=app_config.host,
        port=app_config.port,
        debug=app_config.debug
    )