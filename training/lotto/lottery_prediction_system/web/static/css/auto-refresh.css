/* 自動刷新功能樣式 */

.auto-refresh-controls {
    margin-bottom: 20px;
}

.auto-refresh-controls .card {
    border: 1px solid #e3f2fd;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f7ff 100%);
}

.auto-refresh-controls .form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

.auto-refresh-controls .form-check-label {
    font-weight: 500;
    color: #495057;
}

.auto-refresh-controls .form-select {
    border: 1px solid #ced4da;
    border-radius: 6px;
}

.auto-refresh-controls .form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.auto-refresh-controls .text-muted {
    font-size: 0.85rem;
}

.auto-refresh-controls .progress {
    border-radius: 2px;
    background-color: rgba(0, 0, 0, 0.1);
}

.auto-refresh-controls .progress-bar {
    transition: width 0.1s ease;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .auto-refresh-controls .row {
        text-align: center;
    }
    
    .auto-refresh-controls .col-6 {
        margin-top: 10px;
    }
    
    .auto-refresh-controls .form-check {
        justify-content: center;
        margin-bottom: 15px;
    }
}

/* 載入狀態指示器增強 */
.loading-indicator {
    display: inline-block;
    margin-left: 10px;
}

.loading-indicator .spinner-border {
    width: 1rem;
    height: 1rem;
    border-width: 2px;
}

/* 成功/錯誤狀態指示 */
.refresh-status {
    transition: all 0.3s ease;
}

.refresh-status.success {
    color: #28a745;
}

.refresh-status.error {
    color: #dc3545;
}

.refresh-status.warning {
    color: #ffc107;
}

/* 彩票號碼球的匹配狀態樣式 */
.number-ball.matched {
    background: linear-gradient(135deg, #28a745, #20c997);
    animation: pulse-match 1s ease-in-out;
}

@keyframes pulse-match {
    0% {
        transform: scale(1);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 6px 12px rgba(40, 167, 69, 0.4);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
}

/* 自動刷新通知樣式 */
.auto-refresh-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1060;
    min-width: 300px;
    max-width: 400px;
}

.auto-refresh-notification .alert {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 載入狀態覆蓋層 */
.refresh-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 100;
    border-radius: 15px;
}

.refresh-overlay.active {
    display: flex;
}

.refresh-overlay .spinner-border {
    width: 2rem;
    height: 2rem;
    color: #007bff;
}

/* 暗色主題支援 */
@media (prefers-color-scheme: dark) {
    .auto-refresh-controls .card {
        background: linear-gradient(135deg, #2a2d3a 0%, #1e1e2e 100%);
        border-color: #3a3d4a;
        color: #e8e9ea;
    }
    
    .auto-refresh-controls .form-check-label {
        color: #e8e9ea;
    }
    
    .auto-refresh-controls .form-select {
        background-color: #2a2d3a;
        border-color: #3a3d4a;
        color: #e8e9ea;
    }
    
    .auto-refresh-controls .text-muted {
        color: #b0b3b8 !important;
    }
}