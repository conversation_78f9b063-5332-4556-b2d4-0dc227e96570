/* 移動端響應式設計改善 */

/* 基礎響應式設計 */
@media (max-width: 1200px) {
    .main-container {
        margin: 10px;
        padding: 20px;
    }
}

@media (max-width: 992px) {
    .main-container {
        margin: 5px;
        padding: 15px;
    }
    
    .header-section {
        padding: 15px;
        margin-bottom: 25px;
    }
    
    .header-section h1 {
        font-size: 1.75rem;
    }
}

/* 平板設計 */
@media (max-width: 768px) {
    body {
        font-size: 14px;
    }
    
    .main-container {
        margin: 0;
        border-radius: 0;
        min-height: 100vh;
        padding: 10px;
    }
    
    .header-section {
        padding: 12px;
        margin-bottom: 20px;
        border-radius: 10px;
    }
    
    .header-section h1 {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }
    
    .header-section p {
        font-size: 0.9rem;
        margin-bottom: 0;
    }
    
    /* 彩票類型標籤 */
    .lottery-tabs .nav-link {
        padding: 10px 15px;
        margin: 2px;
        font-size: 0.9rem;
    }
    
    /* 搜索區域 */
    .search-section {
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .search-section .row > div {
        margin-bottom: 10px;
    }
    
    /* 結果卡片 */
    .result-card {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .result-card .row {
        text-align: center;
    }
    
    .result-card .col-md-4 {
        margin-top: 15px;
    }
    
    /* 號碼球 */
    .number-ball {
        width: 35px;
        height: 35px;
        font-size: 14px;
        margin: 1px;
    }
    
    /* 按鈕組 */
    .btn-group .btn {
        font-size: 0.8rem;
        padding: 6px 10px;
    }
    
    /* 分頁 */
    .pagination .page-link {
        padding: 6px 10px;
        font-size: 0.9rem;
    }
}

/* 手機設計 */
@media (max-width: 576px) {
    body {
        font-size: 13px;
    }
    
    .main-container {
        padding: 8px;
    }
    
    .header-section {
        padding: 10px;
        margin-bottom: 15px;
    }
    
    .header-section h1 {
        font-size: 1.25rem;
    }
    
    .header-section p {
        font-size: 0.8rem;
    }
    
    /* 標籤導航 */
    .lottery-tabs {
        margin-bottom: 20px;
    }
    
    .lottery-tabs .nav {
        flex-wrap: nowrap;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }
    
    .lottery-tabs .nav::-webkit-scrollbar {
        display: none;
    }
    
    .lottery-tabs .nav-link {
        white-space: nowrap;
        padding: 8px 12px;
        margin: 0 2px;
        font-size: 0.8rem;
        min-width: auto;
    }
    
    /* 搜索表單 */
    .search-section {
        padding: 12px;
    }
    
    .search-section .form-control,
    .search-section .form-select {
        font-size: 14px;
        padding: 8px 12px;
    }
    
    .search-section .btn {
        font-size: 0.8rem;
        padding: 8px 16px;
        width: 100%;
        margin-top: 10px;
    }
    
    /* 結果卡片 */
    .result-card {
        padding: 12px;
        margin-bottom: 12px;
        border-radius: 10px;
    }
    
    .period-badge {
        font-size: 0.8rem;
        padding: 6px 12px;
        display: inline-block;
        margin-bottom: 8px;
    }
    
    .date-info {
        font-size: 0.75rem;
        margin-bottom: 10px;
        color: #6c757d;
    }
    
    /* 號碼顯示 */
    .numbers-display {
        margin-bottom: 15px;
    }
    
    .number-ball {
        width: 30px;
        height: 30px;
        font-size: 12px;
        margin: 1px;
        line-height: 30px;
    }
    
    /* 操作按鈕 */
    .result-card .btn {
        font-size: 0.7rem;
        padding: 5px 8px;
        margin: 2px;
        border-radius: 15px;
        width: 48%;
    }
    
    .result-card .btn i {
        margin-right: 3px;
    }
    
    /* 統計區域 */
    .stats-section {
        margin-bottom: 20px;
    }
    
    .stats-section .card {
        margin-bottom: 10px;
    }
    
    .stats-section .card-body {
        padding: 10px;
        text-align: center;
    }
    
    /* 分頁 */
    .pagination {
        justify-content: center;
        margin-top: 20px;
    }
    
    .pagination .page-item:not(.active):not(.disabled) {
        display: none;
    }
    
    .pagination .page-item.active,
    .pagination .page-item:first-child,
    .pagination .page-item:last-child,
    .pagination .page-item:nth-child(2),
    .pagination .page-item:nth-last-child(2) {
        display: block;
    }
    
    .pagination .page-link {
        padding: 5px 8px;
        font-size: 0.8rem;
        border-radius: 15px;
        margin: 0 2px;
    }
    
    /* 載入動畫 */
    .loading-spinner {
        padding: 40px 20px;
    }
    
    .loading-spinner .spinner-border {
        width: 2rem;
        height: 2rem;
    }
    
    /* 錯誤信息 */
    .alert {
        font-size: 0.9rem;
        padding: 10px 15px;
        border-radius: 8px;
    }
    
    /* 模態框 */
    .modal-dialog {
        margin: 10px;
    }
    
    .modal-content {
        border-radius: 15px;
    }
    
    .modal-header {
        padding: 15px;
        border-bottom: 1px solid #dee2e6;
    }
    
    .modal-body {
        padding: 15px;
    }
    
    .modal-footer {
        padding: 10px 15px;
        border-top: 1px solid #dee2e6;
    }
}

/* 極小屏幕設計 */
@media (max-width: 480px) {
    .main-container {
        padding: 5px;
    }
    
    .header-section h1 {
        font-size: 1.1rem;
    }
    
    .lottery-tabs .nav-link {
        padding: 6px 8px;
        font-size: 0.75rem;
    }
    
    .number-ball {
        width: 28px;
        height: 28px;
        font-size: 11px;
        line-height: 28px;
    }
    
    .result-card .btn {
        font-size: 0.65rem;
        padding: 4px 6px;
    }
}

/* 橫屏模式優化 */
@media (max-height: 500px) and (orientation: landscape) {
    .main-container {
        padding: 5px;
    }
    
    .header-section {
        padding: 8px;
        margin-bottom: 10px;
    }
    
    .header-section h1 {
        font-size: 1.2rem;
        margin-bottom: 2px;
    }
    
    .header-section p {
        font-size: 0.7rem;
    }
    
    .search-section {
        padding: 8px;
        margin-bottom: 10px;
    }
    
    .result-card {
        padding: 8px;
        margin-bottom: 8px;
    }
}

/* 觸摸友好的設計 */
@media (pointer: coarse) {
    .btn,
    .form-control,
    .form-select,
    .nav-link,
    .page-link {
        min-height: 44px;
        touch-action: manipulation;
    }
    
    .number-ball {
        min-width: 44px;
        min-height: 44px;
    }
    
    .lottery-tabs .nav-link {
        min-height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* 高 DPI 螢幕優化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .number-ball,
    .period-badge,
    .btn {
        border-width: 0.5px;
    }
    
    .card {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }
}

/* 無障礙設計 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 焦點可見性 */
.btn:focus,
.form-control:focus,
.form-select:focus,
.nav-link:focus {
    outline: 2px solid #0066cc;
    outline-offset: 2px;
}