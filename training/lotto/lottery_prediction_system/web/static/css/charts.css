/* 圖表視覺化樣式 */

.charts-container {
    margin-bottom: 30px;
}

.charts-container .card {
    border: 1px solid #e3f2fd;
    background: linear-gradient(135deg, #fafbff 0%, #f5f7ff 100%);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.charts-container .card-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-bottom: none;
    border-radius: 0.375rem 0.375rem 0 0;
}

.charts-container .card-title {
    font-weight: 600;
    margin-bottom: 0;
}

.charts-container .btn-group .btn {
    font-weight: 500;
    border-width: 2px;
    transition: all 0.3s ease;
    font-size: 0.875rem;
    padding: 6px 12px;
}

.charts-container .btn-outline-primary {
    color: #ffffff;
    border-color: rgba(255, 255, 255, 0.5);
}

.charts-container .btn-outline-primary:hover,
.charts-container .btn-outline-primary.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.8);
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 圖表容器 */
#mainChart {
    max-height: 400px;
    width: 100% !important;
    height: 400px !important;
}

.chart-loading {
    color: #6c757d;
    font-weight: 500;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.chart-loading .spinner-border {
    width: 2.5rem;
    height: 2.5rem;
    margin-bottom: 1rem;
}

/* 統計卡片 */
.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #495057;
    margin-bottom: 8px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 圖表動畫 */
@keyframes chartFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#mainChart {
    animation: chartFadeIn 0.5s ease-out;
}

/* 圖表標題樣式 */
.charts-container .card-body {
    padding: 1.5rem;
}

/* 載入狀態 */
.chart-loading {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 10px;
    margin: 20px 0;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .charts-container .btn-group {
        width: 100%;
        margin-top: 15px;
    }
    
    .charts-container .btn-group .btn {
        flex: 1;
        font-size: 0.8rem;
        padding: 8px 6px;
    }
    
    .charts-container .card-header .row {
        text-align: center;
    }
    
    .charts-container .card-header .col-md-6:first-child {
        margin-bottom: 15px;
    }
    
    #mainChart {
        height: 300px !important;
    }
    
    .stat-card {
        margin-bottom: 20px;
        padding: 15px;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .charts-container .btn-group .btn {
        font-size: 0.75rem;
        padding: 6px 4px;
    }
    
    .stat-value {
        font-size: 1.25rem;
    }
    
    .stat-label {
        font-size: 0.75rem;
    }
}

/* 圖表工具提示自定義 */
.chartjs-tooltip {
    opacity: 1;
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: 500;
    pointer-events: none;
    transform: translate(-50%, -100%);
    margin-top: -10px;
}

.chartjs-tooltip:after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid rgba(0, 0, 0, 0.8);
}

/* 圖表圖例樣式 */
.chart-legend {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 15px;
}

.chart-legend-item {
    display: flex;
    align-items: center;
    margin: 5px 15px;
    font-size: 0.875rem;
    color: #495057;
}

.chart-legend-color {
    width: 16px;
    height: 16px;
    border-radius: 2px;
    margin-right: 8px;
}

/* 暗色主題支援 */
@media (prefers-color-scheme: dark) {
    .charts-container .card {
        background: linear-gradient(135deg, #2a2d3a 0%, #1e1e2e 100%);
        border-color: #3a3d4a;
        color: #e8e9ea;
    }
    
    .stat-card {
        background: #2a2d3a;
        border-color: #3a3d4a;
        color: #e8e9ea;
    }
    
    .stat-value {
        color: #e8e9ea;
    }
    
    .stat-label {
        color: #b0b3b8;
    }
    
    .chart-loading {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
        color: #e8e9ea;
    }
}