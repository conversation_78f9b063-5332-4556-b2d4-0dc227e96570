/* 信心度顯示樣式 */

.confidence-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #dee2e6;
    position: relative;
    overflow: hidden;
}

.confidence-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
    opacity: 0.7;
}

.confidence-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.confidence-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.confidence-score {
    display: flex;
    align-items: center;
    gap: 10px;
}

.confidence-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.confidence-badge.success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.confidence-badge.info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
    color: white;
}

.confidence-badge.warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
}

.confidence-badge.danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: white;
}

.confidence-percentage {
    font-size: 1.2rem;
    font-weight: bold;
    color: #495057;
}

.confidence-description {
    margin-top: 10px;
    padding: 10px 15px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    border-left: 4px solid var(--confidence-color, #6c757d);
    font-size: 0.9rem;
    color: #6c757d;
}

.confidence-meter {
    margin-top: 15px;
}

.confidence-progress {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.confidence-progress-bar {
    height: 100%;
    border-radius: 4px;
    transition: width 0.8s ease-in-out, background-color 0.3s ease;
    position: relative;
}

.confidence-progress-bar.success {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.confidence-progress-bar.info {
    background: linear-gradient(90deg, #17a2b8, #6f42c1);
}

.confidence-progress-bar.warning {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.confidence-progress-bar.danger {
    background: linear-gradient(90deg, #dc3545, #e83e8c);
}

.confidence-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, 
        transparent 25%, 
        rgba(255, 255, 255, 0.2) 25%, 
        rgba(255, 255, 255, 0.2) 50%, 
        transparent 50%, 
        transparent 75%, 
        rgba(255, 255, 255, 0.2) 75%);
    background-size: 20px 20px;
    animation: move 1s linear infinite;
}

@keyframes move {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 20px 20px;
    }
}

.confidence-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
    font-size: 0.75rem;
    color: #6c757d;
}

.confidence-factors {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

.confidence-factors-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
}

.factor-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    font-size: 0.8rem;
}

.factor-name {
    color: #6c757d;
}

.factor-value {
    font-weight: 600;
    color: #495057;
}

/* 預測結果卡片中的信心度 */
.prediction-card .confidence-indicator {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 0.7rem;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.confidence-indicator.success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.confidence-indicator.info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
}

.confidence-indicator.warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.confidence-indicator.danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
}

.confidence-value {
    font-size: 1rem;
    line-height: 1;
    margin-bottom: 2px;
}

.confidence-label {
    font-size: 0.6rem;
    line-height: 1;
    opacity: 0.9;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .confidence-container {
        padding: 15px;
        margin: 15px 0;
    }
    
    .confidence-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .confidence-score {
        align-self: stretch;
        justify-content: space-between;
    }
    
    .confidence-badge {
        font-size: 0.75rem;
        padding: 4px 8px;
    }
    
    .confidence-percentage {
        font-size: 1rem;
    }
    
    .prediction-card .confidence-indicator {
        width: 50px;
        height: 50px;
        top: 10px;
        right: 10px;
    }
    
    .confidence-value {
        font-size: 0.8rem;
    }
    
    .confidence-label {
        font-size: 0.5rem;
    }
}

@media (max-width: 576px) {
    .confidence-container {
        padding: 12px;
    }
    
    .confidence-title {
        font-size: 1rem;
    }
    
    .confidence-description {
        padding: 8px 12px;
        font-size: 0.8rem;
    }
    
    .factor-item {
        font-size: 0.75rem;
    }
}

/* 動畫效果 */
.confidence-container {
    animation: confidenceFadeIn 0.5s ease-out;
}

@keyframes confidenceFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.confidence-badge {
    transition: all 0.3s ease;
}

.confidence-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}