/* 數據匯出功能樣式 */

.export-controls {
    margin-bottom: 20px;
}

.export-controls .card {
    border: 1px solid #e8f5e8;
    background: linear-gradient(135deg, #f8fff8 0%, #f0f8f0 100%);
}

.export-controls .card-header {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border-bottom: none;
    border-radius: 0.375rem 0.375rem 0 0;
}

.export-controls .card-title {
    font-weight: 600;
    margin-bottom: 0;
}

.export-controls .btn-group .btn {
    font-weight: 500;
    border-width: 2px;
    transition: all 0.3s ease;
}

.export-controls .btn-outline-success:hover {
    background-color: #28a745;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.export-controls .btn-outline-primary:hover {
    background-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.export-controls .btn-outline-info:hover {
    background-color: #17a2b8;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
}

.export-controls .form-check-label {
    font-weight: 500;
    color: #495057;
}

.export-controls .form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

/* 載入覆蓋層 */
.export-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1070;
}

.export-loading-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    min-width: 200px;
}

.export-loading-text {
    font-weight: 500;
    color: #495057;
    font-size: 16px;
}

/* 通知樣式 */
.export-notification {
    position: relative;
    z-index: 1060;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: none;
    font-weight: 500;
}

.export-notification i {
    margin-right: 8px;
}

/* 按鈕載入狀態 */
.btn.loading {
    position: relative;
    color: transparent !important;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* 響應式設計 */
@media (max-width: 768px) {
    .export-controls .btn-group {
        width: 100%;
        margin-bottom: 15px;
    }
    
    .export-controls .btn-group .btn {
        flex: 1;
        font-size: 0.875rem;
        padding: 8px 12px;
    }
    
    .export-controls .row {
        text-align: center;
    }
    
    .export-loading-content {
        margin: 20px;
        padding: 20px;
    }
}

/* 檔案類型圖示 */
.btn i.fa-file-csv {
    color: #28a745;
}

.btn i.fa-file-excel {
    color: #007bff;
}

.btn i.fa-file-code {
    color: #17a2b8;
}

/* 懸停效果 */
.export-controls .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

/* 成功動畫 */
@keyframes exportSuccess {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.export-success {
    animation: exportSuccess 0.6s ease-in-out;
}

/* 暗色主題支援 */
@media (prefers-color-scheme: dark) {
    .export-controls .card {
        background: linear-gradient(135deg, #1a2f1a 0%, #1e2e1e 100%);
        border-color: #2a4a2a;
        color: #e8e9ea;
    }
    
    .export-controls .form-check-label {
        color: #e8e9ea;
    }
    
    .export-loading-content {
        background: #2a2d3a;
        color: #e8e9ea;
    }
    
    .export-loading-text {
        color: #e8e9ea;
    }
}