/**
 * 數據匯出功能模組
 * 支援 CSV, Excel, JSON 格式匯出
 */

class DataExporter {
    constructor() {
        this.init();
    }
    
    init() {
        this.createExportControls();
        this.bindEvents();
    }
    
    createExportControls() {
        // 創建匯出控制界面
        const exportContainer = document.createElement('div');
        exportContainer.className = 'export-controls';
        exportContainer.innerHTML = `
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-download"></i> 數據匯出
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-success btn-sm" id="exportCSV">
                                    <i class="fas fa-file-csv"></i> CSV
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" id="exportExcel">
                                    <i class="fas fa-file-excel"></i> Excel
                                </button>
                                <button type="button" class="btn btn-outline-info btn-sm" id="exportJSON">
                                    <i class="fas fa-file-code"></i> JSON
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeStatistics" checked>
                                <label class="form-check-label" for="includeStatistics">
                                    包含統計資料
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-12">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i> 
                                匯出當前篩選條件下的所有數據
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 插入到自動刷新控制之後
        const refreshControls = document.querySelector('.auto-refresh-controls');
        if (refreshControls) {
            refreshControls.parentNode.insertBefore(exportContainer, refreshControls.nextSibling);
        } else {
            // 如果沒有自動刷新控制，插入到搜索區域之前
            const searchSection = document.querySelector('.search-section');
            if (searchSection) {
                searchSection.parentNode.insertBefore(exportContainer, searchSection);
            }
        }
    }
    
    bindEvents() {
        // CSV 匯出
        const csvBtn = document.getElementById('exportCSV');
        if (csvBtn) {
            csvBtn.addEventListener('click', () => this.exportCSV());
        }
        
        // Excel 匯出
        const excelBtn = document.getElementById('exportExcel');
        if (excelBtn) {
            excelBtn.addEventListener('click', () => this.exportExcel());
        }
        
        // JSON 匯出
        const jsonBtn = document.getElementById('exportJSON');
        if (jsonBtn) {
            jsonBtn.addEventListener('click', () => this.exportJSON());
        }
    }
    
    async getCurrentData() {
        try {
            // 獲取當前的篩選參數
            const currentLotteryType = window.currentLotteryType || 'powercolor';
            const params = new URLSearchParams({
                lottery_type: currentLotteryType,
                page: 1,
                limit: 1000  // 獲取更多數據用於匯出
            });
            
            // 添加搜尋條件
            const periodSearch = document.getElementById('periodSearch')?.value;
            const startDate = document.getElementById('startDate')?.value;
            const endDate = document.getElementById('endDate')?.value;
            
            if (periodSearch) params.append('period', periodSearch);
            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);
            
            const response = await fetch(`/api/results?${params.toString()}`);
            const data = await response.json();
            
            if (data.success) {
                return data.data;
            } else {
                throw new Error(data.error || '獲取數據失敗');
            }
        } catch (error) {
            console.error('獲取數據錯誤:', error);
            this.showNotification('獲取數據失敗: ' + error.message, 'error');
            return null;
        }
    }
    
    async exportCSV() {
        this.showLoading('正在匯出 CSV...');
        
        const data = await this.getCurrentData();
        if (!data) {
            this.hideLoading();
            return;
        }
        
        try {
            let csvContent = this.convertToCSV(data);
            this.downloadFile(csvContent, `lottery_results_${this.getDateString()}.csv`, 'text/csv');
            this.showNotification('CSV 匯出成功！', 'success');
        } catch (error) {
            console.error('CSV 匯出錯誤:', error);
            this.showNotification('CSV 匯出失敗: ' + error.message, 'error');
        }
        
        this.hideLoading();
    }
    
    async exportExcel() {
        this.showLoading('正在匯出 Excel...');
        
        const data = await this.getCurrentData();
        if (!data) {
            this.hideLoading();
            return;
        }
        
        try {
            // 簡化的 Excel 匯出（實際上是 CSV 格式，但可以用 Excel 打開）
            let excelContent = this.convertToCSV(data);
            this.downloadFile(excelContent, `lottery_results_${this.getDateString()}.xlsx`, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            this.showNotification('Excel 匯出成功！', 'success');
        } catch (error) {
            console.error('Excel 匯出錯誤:', error);
            this.showNotification('Excel 匯出失敗: ' + error.message, 'error');
        }
        
        this.hideLoading();
    }
    
    async exportJSON() {
        this.showLoading('正在匯出 JSON...');
        
        const data = await this.getCurrentData();
        if (!data) {
            this.hideLoading();
            return;
        }
        
        try {
            const jsonContent = JSON.stringify(data, null, 2);
            this.downloadFile(jsonContent, `lottery_results_${this.getDateString()}.json`, 'application/json');
            this.showNotification('JSON 匯出成功！', 'success');
        } catch (error) {
            console.error('JSON 匯出錯誤:', error);
            this.showNotification('JSON 匯出失敗: ' + error.message, 'error');
        }
        
        this.hideLoading();
    }
    
    convertToCSV(data) {
        const results = data.results || [];
        const includeStats = document.getElementById('includeStatistics')?.checked;
        
        if (results.length === 0) {
            throw new Error('沒有數據可供匯出');
        }
        
        // 確定彩票類型以設置正確的標題
        const lotteryType = window.currentLotteryType || 'powercolor';
        let headers = ['期號', '開獎日期'];
        
        if (lotteryType === 'powercolor') {
            headers = headers.concat(['第一區-1', '第一區-2', '第一區-3', '第一區-4', '第一區-5', '第一區-6', '第二區']);
        } else if (lotteryType === 'lotto649') {
            headers = headers.concat(['號碼-1', '號碼-2', '號碼-3', '號碼-4', '號碼-5', '號碼-6', '特別號']);
        } else if (lotteryType === 'dailycash') {
            headers = headers.concat(['號碼-1', '號碼-2', '號碼-3', '號碼-4', '號碼-5']);
        }
        
        let csvRows = [headers.join(',')];
        
        results.forEach(result => {
            let row = [result.period, result.date];
            
            if (lotteryType === 'powercolor') {
                const firstArea = result.numbers.first_area || [];
                const secondArea = result.numbers.second_area || 0;
                row = row.concat(firstArea);
                while (row.length < 8) row.push(''); // 補足6個第一區號碼
                row.push(secondArea);
            } else if (lotteryType === 'lotto649') {
                const mainNumbers = result.numbers.main_numbers || [];
                const specialNumber = result.numbers.special_number || 0;
                row = row.concat(mainNumbers);
                while (row.length < 8) row.push(''); // 補足6個主號碼
                row.push(specialNumber);
            } else if (lotteryType === 'dailycash') {
                const numbers = result.numbers.numbers || [];
                row = row.concat(numbers);
                while (row.length < 7) row.push(''); // 補足5個號碼
            }
            
            csvRows.push(row.join(','));
        });
        
        // 如果包含統計資料
        if (includeStats && data.statistics) {
            csvRows.push('');
            csvRows.push('統計資料');
            csvRows.push(`總開獎次數,${data.statistics.total_draws || 0}`);
            csvRows.push(`日期範圍,${data.statistics.date_range || ''}`);
            
            if (data.statistics.hot_numbers && data.statistics.hot_numbers.length > 0) {
                csvRows.push(`熱門號碼,${data.statistics.hot_numbers.join(' ')}`);
            }
            
            if (data.statistics.cold_numbers && data.statistics.cold_numbers.length > 0) {
                csvRows.push(`冷門號碼,${data.statistics.cold_numbers.join(' ')}`);
            }
        }
        
        return csvRows.join('\\n');
    }
    
    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = window.URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.style.display = 'none';
        
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        window.URL.revokeObjectURL(url);
    }
    
    getDateString() {
        const now = new Date();
        return now.toISOString().split('T')[0].replace(/-/g, '');
    }
    
    showLoading(message = '正在處理...') {
        // 創建載入覆蓋層
        let overlay = document.getElementById('exportLoadingOverlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'exportLoadingOverlay';
            overlay.className = 'export-loading-overlay';
            overlay.innerHTML = `
                <div class="export-loading-content">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">載入中...</span>
                    </div>
                    <div class="mt-2 export-loading-text">${message}</div>
                </div>
            `;
            document.body.appendChild(overlay);
        } else {
            overlay.querySelector('.export-loading-text').textContent = message;
            overlay.style.display = 'flex';
        }
    }
    
    hideLoading() {
        const overlay = document.getElementById('exportLoadingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }
    
    showNotification(message, type = 'info') {
        const alertClass = type === 'error' ? 'alert-danger' : 
                          type === 'success' ? 'alert-success' : 'alert-info';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show export-notification`;
        alert.innerHTML = `
            <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : 
                              type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // 插入到頁面頂部
        const container = document.querySelector('.main-container') || document.body;
        container.insertBefore(alert, container.firstChild);
        
        // 5秒後自動消失
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
}

// 等待頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', () => {
    // 只在 results 頁面初始化
    if (document.querySelector('.result-card') !== null || 
        document.getElementById('resultsContainer') !== null ||
        window.location.pathname.includes('results')) {
        
        window.dataExporter = new DataExporter();
        console.log('數據匯出功能已初始化');
    }
});

// 導出供其他模組使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataExporter;
}