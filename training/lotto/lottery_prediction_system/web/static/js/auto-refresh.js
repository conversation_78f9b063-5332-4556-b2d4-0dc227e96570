/**
 * 自動刷新功能模組
 * 為彩票系統添加實時數據更新功能
 */

class AutoRefresh {
    constructor() {
        this.isEnabled = false;
        this.interval = null;
        this.refreshRate = 30000; // 30秒
        this.lastUpdateTime = null;
        this.errorCount = 0;
        this.maxErrors = 3;
        
        this.init();
    }
    
    init() {
        this.createControls();
        this.bindEvents();
        this.loadSettings();
    }
    
    createControls() {
        // 創建自動刷新控制界面
        const refreshContainer = document.createElement('div');
        refreshContainer.className = 'auto-refresh-controls';
        refreshContainer.innerHTML = `
            <div class="card mb-3">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="autoRefreshToggle">
                                <label class="form-check-label" for="autoRefreshToggle">
                                    <i class="fas fa-sync-alt"></i> 自動刷新數據
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="row align-items-center">
                                <div class="col-6">
                                    <select class="form-select form-select-sm" id="refreshRate">
                                        <option value="15000">15秒</option>
                                        <option value="30000" selected>30秒</option>
                                        <option value="60000">1分鐘</option>
                                        <option value="300000">5分鐘</option>
                                    </select>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted" id="lastUpdateInfo">
                                        <i class="fas fa-clock"></i> 未更新
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-12">
                            <div class="progress" style="height: 4px; display: none;" id="refreshProgress">
                                <div class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 插入到搜索區域之前
        const searchSection = document.querySelector('.search-section');
        if (searchSection) {
            searchSection.parentNode.insertBefore(refreshContainer, searchSection);
        }
    }
    
    bindEvents() {
        // 綁定切換開關
        const toggle = document.getElementById('autoRefreshToggle');
        if (toggle) {
            toggle.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.start();
                } else {
                    this.stop();
                }
            });
        }
        
        // 綁定刷新間隔選擇
        const rateSelect = document.getElementById('refreshRate');
        if (rateSelect) {
            rateSelect.addEventListener('change', (e) => {
                this.refreshRate = parseInt(e.target.value);
                this.saveSettings();
                if (this.isEnabled) {
                    this.restart();
                }
            });
        }
        
        // 頁面可見性變化時暫停/恢復刷新
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pause();
            } else {
                this.resume();
            }
        });
        
        // 窗口失去焦點時暫停刷新
        window.addEventListener('blur', () => this.pause());
        window.addEventListener('focus', () => this.resume());
    }
    
    start() {
        if (this.isEnabled) return;
        
        this.isEnabled = true;
        this.errorCount = 0;
        this.startInterval();
        this.updateUI();
        this.saveSettings();
        
        // 立即執行一次刷新
        this.refresh();
        
        console.log('自動刷新已啟動，間隔:', this.refreshRate + 'ms');
    }
    
    stop() {
        if (!this.isEnabled) return;
        
        this.isEnabled = false;
        this.clearInterval();
        this.updateUI();
        this.saveSettings();
        
        console.log('自動刷新已停止');
    }
    
    pause() {
        if (this.isEnabled && this.interval) {
            this.clearInterval();
            console.log('自動刷新已暫停');
        }
    }
    
    resume() {
        if (this.isEnabled && !this.interval) {
            this.startInterval();
            console.log('自動刷新已恢復');
        }
    }
    
    restart() {
        if (this.isEnabled) {
            this.clearInterval();
            this.startInterval();
        }
    }
    
    startInterval() {
        this.interval = setInterval(() => {
            this.refresh();
        }, this.refreshRate);
        
        this.startProgressBar();
    }
    
    clearInterval() {
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
        this.stopProgressBar();
    }
    
    startProgressBar() {
        const progressBar = document.querySelector('#refreshProgress .progress-bar');
        const progressContainer = document.getElementById('refreshProgress');
        
        if (!progressBar || !progressContainer) return;
        
        progressContainer.style.display = 'block';
        
        const startTime = Date.now();
        const updateProgress = () => {
            if (!this.isEnabled || !this.interval) {
                progressContainer.style.display = 'none';
                return;
            }
            
            const elapsed = Date.now() - startTime;
            const progress = Math.min((elapsed / this.refreshRate) * 100, 100);
            progressBar.style.width = progress + '%';
            
            if (progress < 100) {
                requestAnimationFrame(updateProgress);
            }
        };
        
        requestAnimationFrame(updateProgress);
    }
    
    stopProgressBar() {
        const progressContainer = document.getElementById('refreshProgress');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }
    }
    
    async refresh() {
        try {
            console.log('開始自動刷新數據...');
            
            // 1. 先嘗試更新數據庫中的最新開獎結果
            await this.updateLotteryData();
            
            // 2. 然後調用現有的 loadResults 函數重新載入頁面數據
            if (typeof loadResults === 'function') {
                await loadResults();
                this.onRefreshSuccess();
            } else {
                console.warn('loadResults 函數不存在');
            }
        } catch (error) {
            console.error('自動刷新錯誤:', error);
            this.onRefreshError(error);
        }
    }
    
    async updateLotteryData() {
        try {
            // 獲取當前頁面的彩票類型
            const lotteryType = this.getCurrentLotteryType();
            
            console.log(`更新 ${lotteryType} 開獎數據...`);
            
            // 調用智能檢查更新API (2小時內不重複更新)
            const response = await fetch(`/api/data/check_update/${lotteryType}?threshold_hours=2`);
            const result = await response.json();
            
            if (result.success && result.data.updated) {
                console.log(`${lotteryType} 數據更新成功:`, result.data);
                this.showUpdateNotification(`${lotteryType} 數據已更新 - ${result.data.message || ''}`);
                
                // 同時更新可觀察追蹤系統的結果
                await this.updateObservableResults(lotteryType);
            } else if (result.success && !result.data.updated) {
                console.log(`${lotteryType} 數據仍然新鮮:`, result.data.message);
            } else {
                console.warn(`${lotteryType} 數據更新失敗:`, result.error);
            }
            
        } catch (error) {
            console.error('更新彩票數據失敗:', error);
            // 不要因為數據更新失敗而中斷整個刷新流程
        }
    }
    
    async updateObservableResults(lotteryType) {
        try {
            console.log(`更新 ${lotteryType} 可觀察追蹤結果...`);
            
            const response = await fetch(`/api/observable/update_results/${lotteryType}`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                const updatedCount = result.data?.updated_predictions || 0;
                if (updatedCount > 0) {
                    console.log(`可觀察追蹤結果已更新 - ${updatedCount} 條記錄`);
                    this.showUpdateNotification(`預測結果已更新 - ${updatedCount} 條記錄命中情況已刷新`, 'success');
                }
            }
        } catch (error) {
            console.error('更新可觀察追蹤結果失敗:', error);
            // 不要因為這個失敗而中斷主要流程
        }
    }
    
    getCurrentLotteryType() {
        // 嘗試多種方式獲取當前彩票類型
        
        // 1. 從 URL 參數獲取
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('lottery_type')) {
            return urlParams.get('lottery_type');
        }
        if (urlParams.has('type')) {
            return urlParams.get('type');
        }
        
        // 2. 從全域變數獲取
        if (typeof currentLotteryType !== 'undefined') {
            return currentLotteryType;
        }
        
        // 3. 從 DOM 元素獲取
        const lotterySelect = document.getElementById('lotteryType');
        if (lotterySelect) {
            return lotterySelect.value;
        }
        
        // 4. 從活躍的頁籤獲取
        const activeTab = document.querySelector('.nav-link.active');
        if (activeTab) {
            const href = activeTab.getAttribute('href') || '';
            if (href.includes('powercolor')) return 'powercolor';
            if (href.includes('lotto649')) return 'lotto649';
            if (href.includes('dailycash')) return 'dailycash';
        }
        
        // 5. 預設值
        return 'powercolor';
    }
    
    showUpdateNotification(message, type = 'info') {
        // 創建更新通知
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show auto-refresh-notification`;
        alert.style.position = 'fixed';
        alert.style.top = '20px';
        alert.style.right = '20px';
        alert.style.zIndex = '9999';
        alert.style.maxWidth = '400px';
        alert.innerHTML = `
            <i class="fas fa-sync-alt"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // 插入到頁面
        document.body.appendChild(alert);
        
        // 3秒後自動消失
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
    }
    
    onRefreshSuccess() {
        this.errorCount = 0;
        this.lastUpdateTime = new Date();
        this.updateLastUpdateInfo();
        
        // 重置進度條
        setTimeout(() => {
            const progressBar = document.querySelector('#refreshProgress .progress-bar');
            if (progressBar) {
                progressBar.style.width = '0%';
            }
        }, 500);
    }
    
    onRefreshError(error) {
        this.errorCount++;
        
        if (this.errorCount >= this.maxErrors) {
            this.stop();
            this.showErrorNotification('自動刷新遇到連續錯誤，已自動停止');
        }
    }
    
    updateUI() {
        const toggle = document.getElementById('autoRefreshToggle');
        if (toggle) {
            toggle.checked = this.isEnabled;
        }
        
        const rateSelect = document.getElementById('refreshRate');
        if (rateSelect) {
            rateSelect.value = this.refreshRate;
            rateSelect.disabled = this.isEnabled;
        }
    }
    
    updateLastUpdateInfo() {
        const info = document.getElementById('lastUpdateInfo');
        if (info && this.lastUpdateTime) {
            const timeStr = this.lastUpdateTime.toLocaleTimeString('zh-TW');
            info.innerHTML = `<i class="fas fa-check-circle text-success"></i> 最後更新: ${timeStr}`;
        }
    }
    
    showErrorNotification(message) {
        // 創建錯誤提示
        const alert = document.createElement('div');
        alert.className = 'alert alert-warning alert-dismissible fade show';
        alert.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // 插入到頁面頂部
        const container = document.querySelector('.main-container');
        if (container) {
            container.insertBefore(alert, container.firstChild);
        }
        
        // 5秒後自動消失
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
    
    saveSettings() {
        const settings = {
            enabled: this.isEnabled,
            refreshRate: this.refreshRate
        };
        
        try {
            localStorage.setItem('autoRefreshSettings', JSON.stringify(settings));
        } catch (error) {
            console.warn('無法保存設定:', error);
        }
    }
    
    loadSettings() {
        try {
            const settings = JSON.parse(localStorage.getItem('autoRefreshSettings') || '{}');
            
            if (settings.enabled !== undefined) {
                this.isEnabled = settings.enabled;
            }
            
            if (settings.refreshRate !== undefined) {
                this.refreshRate = settings.refreshRate;
            }
            
            this.updateUI();
            
            // 如果之前是啟用狀態，重新啟動
            if (this.isEnabled) {
                setTimeout(() => this.start(), 1000);
            }
        } catch (error) {
            console.warn('無法載入設定:', error);
        }
    }
}

// 等待頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', () => {
    // 只在 results 頁面初始化
    if (document.querySelector('.result-card') !== null || 
        document.getElementById('resultsContainer') !== null ||
        window.location.pathname.includes('results')) {
        
        window.autoRefresh = new AutoRefresh();
        console.log('自動刷新功能已初始化');
    }
});

// 導出供其他模組使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AutoRefresh;
}