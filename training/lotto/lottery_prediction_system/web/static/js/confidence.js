/**
 * 信心度顯示和管理功能
 */

class ConfidenceManager {
    constructor() {
        this.apiBaseUrl = '/api';
        this.currentLotteryType = 'powercolor';
        this.confidenceHistory = [];
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // 頁面載入時自動獲取信心度資訊
        document.addEventListener('DOMContentLoaded', () => {
            this.loadEnhancedPrediction();
        });
    }

    /**
     * 載入增強預測結果（含信心度）
     */
    async loadEnhancedPrediction(lotteryType = null) {
        if (lotteryType) {
            this.currentLotteryType = lotteryType;
        }

        try {
            const response = await fetch(`${this.apiBaseUrl}/predictions/enhanced/${this.currentLotteryType}`);
            const data = await response.json();

            if (data.success) {
                this.displayEnhancedPrediction(data.data);
                return data.data;
            } else {
                console.error('獲取增強預測失敗:', data.error);
                return null;
            }
        } catch (error) {
            console.error('載入增強預測時發生錯誤:', error);
            return null;
        }
    }

    /**
     * 顯示增強預測結果
     */
    displayEnhancedPrediction(data) {
        // 更新預測號碼（如果有對應的容器）
        const numbersContainer = document.getElementById('predictionNumbers');
        if (numbersContainer && data.prediction) {
            this.renderPredictionNumbers(numbersContainer, data.prediction, this.currentLotteryType);
        }

        // 顯示或更新信心度
        this.displayConfidence(data.confidence);

        // 添加預測元數據
        this.displayPredictionMetadata(data);
    }

    /**
     * 渲染預測號碼
     */
    renderPredictionNumbers(container, prediction, lotteryType) {
        let html = '';

        if (lotteryType === 'powercolor') {
            html += '<div class="mb-3">';
            html += '<h6>第一區號碼</h6>';
            html += '<div class="numbers-display">';
            prediction.numbers.forEach(num => {
                html += `<div class="number-ball normal">${num}</div>`;
            });
            html += '</div></div>';

            if (prediction.special_number) {
                html += '<div class="mb-3">';
                html += '<h6>第二區號碼</h6>';
                html += '<div class="numbers-display">';
                html += `<div class="number-ball special">${prediction.special_number}</div>`;
                html += '</div></div>';
            }
        } else if (lotteryType === 'lotto649') {
            html += '<div class="mb-3">';
            html += '<h6>一般號碼</h6>';
            html += '<div class="numbers-display">';
            prediction.numbers.forEach(num => {
                html += `<div class="number-ball normal">${num}</div>`;
            });
            html += '</div></div>';

            if (prediction.special_number) {
                html += '<div class="mb-3">';
                html += '<h6>特別號</h6>';
                html += '<div class="numbers-display">';
                html += `<div class="number-ball special">${prediction.special_number}</div>`;
                html += '</div></div>';
            }
        } else if (lotteryType === 'dailycash') {
            html += '<div class="mb-3">';
            html += '<h6>開獎號碼</h6>';
            html += '<div class="numbers-display">';
            prediction.numbers.forEach(num => {
                html += `<div class="number-ball normal">${num}</div>`;
            });
            html += '</div></div>';
        }

        container.innerHTML = html;
    }

    /**
     * 顯示信心度資訊
     */
    displayConfidence(confidence) {
        const existingContainer = document.getElementById('confidenceContainer');
        if (existingContainer) {
            existingContainer.remove();
        }

        const container = document.createElement('div');
        container.id = 'confidenceContainer';
        container.className = 'confidence-container';
        
        const percentage = Math.round(confidence.score * 100);
        
        container.innerHTML = `
            <div class="confidence-header">
                <h5 class="confidence-title">
                    <i class="fas fa-chart-line"></i> 預測信心度
                </h5>
                <div class="confidence-score">
                    <span class="confidence-badge ${confidence.color}">${confidence.level}</span>
                    <span class="confidence-percentage">${confidence.percentage}</span>
                </div>
            </div>
            
            <div class="confidence-description" style="--confidence-color: var(--bs-${confidence.color})">
                <i class="fas fa-info-circle"></i> ${confidence.description}
            </div>
            
            <div class="confidence-meter">
                <div class="confidence-progress">
                    <div class="confidence-progress-bar ${confidence.color}" 
                         style="width: ${percentage}%"></div>
                </div>
                <div class="confidence-labels">
                    <span>低</span>
                    <span>中等</span>
                    <span>高</span>
                    <span>極高</span>
                </div>
            </div>
            
            <div class="confidence-factors">
                <div class="confidence-factors-title">
                    <i class="fas fa-cogs"></i> 評估因子
                </div>
                <div class="factor-item">
                    <span class="factor-name">歷史準確率</span>
                    <span class="factor-value">30%</span>
                </div>
                <div class="factor-item">
                    <span class="factor-name">模式強度</span>
                    <span class="factor-value">25%</span>
                </div>
                <div class="factor-item">
                    <span class="factor-name">數據一致性</span>
                    <span class="factor-value">20%</span>
                </div>
                <div class="factor-item">
                    <span class="factor-name">模型一致性</span>
                    <span class="factor-value">15%</span>
                </div>
                <div class="factor-item">
                    <span class="factor-name">時效性因子</span>
                    <span class="factor-value">10%</span>
                </div>
            </div>
        `;

        // 插入到合適的位置
        const targetContainer = document.getElementById('predictionResults') || 
                               document.getElementById('mainContent') || 
                               document.querySelector('.main-container');
        
        if (targetContainer) {
            const firstChild = targetContainer.firstElementChild;
            if (firstChild) {
                targetContainer.insertBefore(container, firstChild.nextSibling);
            } else {
                targetContainer.appendChild(container);
            }
        }
    }

    /**
     * 顯示預測元數據
     */
    displayPredictionMetadata(data) {
        const metadataContainer = document.getElementById('predictionMetadata');
        if (!metadataContainer) return;

        const timestamp = new Date(data.timestamp).toLocaleString('zh-TW');
        
        metadataContainer.innerHTML = `
            <div class="metadata-card">
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="fas fa-clock"></i> 預測時間: ${timestamp}
                        </small>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="fas fa-cog"></i> 預測方法: ${data.method}
                        </small>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="fas fa-database"></i> 核心模組: ${data.metadata.core_modules_used ? '已啟用' : '基礎模式'}
                        </small>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="fas fa-chart-bar"></i> 信心度計算: ${data.metadata.confidence_calculated ? '已完成' : '未計算'}
                        </small>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 載入信心度歷史統計
     */
    async loadConfidenceHistory(lotteryType = null) {
        if (lotteryType) {
            this.currentLotteryType = lotteryType;
        }

        try {
            const response = await fetch(`${this.apiBaseUrl}/confidence/history/${this.currentLotteryType}`);
            const data = await response.json();

            if (data.success) {
                this.confidenceHistory = data.data.history;
                this.displayConfidenceHistory(data.data);
                return data.data;
            } else {
                console.error('獲取信心度歷史失敗:', data.error);
                return null;
            }
        } catch (error) {
            console.error('載入信心度歷史時發生錯誤:', error);
            return null;
        }
    }

    /**
     * 顯示信心度歷史統計
     */
    displayConfidenceHistory(data) {
        const container = document.getElementById('confidenceHistoryContainer');
        if (!container) return;

        let html = `
            <div class="confidence-history-header">
                <h5><i class="fas fa-history"></i> 信心度歷史統計</h5>
            </div>
        `;

        // 統計資訊
        if (data.statistics && Object.keys(data.statistics).length > 0) {
            html += `
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-value">${data.statistics.average_confidence}</div>
                            <div class="stat-label">平均信心度</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-value">${data.statistics.success_rate}%</div>
                            <div class="stat-label">成功率</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-value">${data.statistics.high_confidence_count}</div>
                            <div class="stat-label">高信心度次數</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-value">${data.statistics.total_predictions}</div>
                            <div class="stat-label">總預測次數</div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 歷史記錄表格
        if (data.history && data.history.length > 0) {
            html += `
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>期號</th>
                                <th>日期</th>
                                <th>信心度</th>
                                <th>等級</th>
                                <th>命中數</th>
                                <th>方法</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            data.history.forEach(record => {
                const confidenceColor = this.getConfidenceColor(record.confidence_score);
                html += `
                    <tr>
                        <td>${record.period}</td>
                        <td>${record.date}</td>
                        <td>
                            <span class="badge bg-${confidenceColor}">${record.confidence_score}</span>
                        </td>
                        <td>${record.confidence_level}</td>
                        <td>
                            <span class="badge ${record.match_count >= 3 ? 'bg-success' : 'bg-secondary'}">
                                ${record.match_count}
                            </span>
                        </td>
                        <td><small>${record.method}</small></td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;
        } else {
            html += `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 暫無歷史記錄
                </div>
            `;
        }

        container.innerHTML = html;
    }

    /**
     * 根據信心度分數獲取顏色類別
     */
    getConfidenceColor(score) {
        if (score >= 0.7) return 'success';
        if (score >= 0.6) return 'info';
        if (score >= 0.5) return 'warning';
        return 'danger';
    }

    /**
     * 為預測卡片添加信心度指示器
     */
    addConfidenceIndicator(cardElement, confidence) {
        // 移除現有的指示器
        const existingIndicator = cardElement.querySelector('.confidence-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // 創建新的指示器
        const indicator = document.createElement('div');
        indicator.className = `confidence-indicator ${confidence.color}`;
        indicator.innerHTML = `
            <div class="confidence-value">${Math.round(confidence.score * 100)}%</div>
            <div class="confidence-label">${confidence.level}</div>
        `;

        // 確保卡片有相對定位
        cardElement.style.position = 'relative';
        cardElement.appendChild(indicator);
    }

    /**
     * 重新整理信心度資訊
     */
    async refreshConfidence() {
        const button = document.getElementById('refreshConfidenceBtn');
        if (button) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 計算中...';
        }

        try {
            await this.loadEnhancedPrediction();
            
            if (button) {
                button.innerHTML = '<i class="fas fa-sync"></i> 重新計算';
                
                // 顯示成功提示
                this.showToast('success', '信心度已重新計算');
            }
        } catch (error) {
            console.error('重新整理信心度失敗:', error);
            if (button) {
                button.innerHTML = '<i class="fas fa-sync"></i> 重新計算';
            }
            this.showToast('error', '重新計算失敗');
        } finally {
            if (button) {
                button.disabled = false;
            }
        }
    }

    /**
     * 顯示提示訊息
     */
    showToast(type, message) {
        // 簡單的提示實現，可以根據需要擴展
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check' : 'exclamation-triangle'}"></i>
            ${message}
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
}

// 創建全局實例
const confidenceManager = new ConfidenceManager();

// 暴露到全局作用域供其他腳本使用
window.ConfidenceManager = ConfidenceManager;
window.confidenceManager = confidenceManager;