/**
 * 圖表視覺化功能模組
 * 使用 Chart.js 進行數據視覺化
 */

class LotteryCharts {
    constructor() {
        this.charts = {};
        this.currentData = null;
        this.isLoading = false;
        this.dataCache = new Map(); // 添加數據緩存
        this.lastUpdate = 0; // 上次更新時間
        this.updateThrottle = 1000; // 更新節流時間（毫秒）
        this.init();
    }
    
    async init() {
        // 載入 Chart.js 庫
        await this.loadChartJS();
        this.createChartsContainer();
        this.bindEvents();
    }
    
    async loadChartJS() {
        return new Promise((resolve, reject) => {
            if (window.Chart) {
                resolve();
                return;
            }
            
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }
    
    createChartsContainer() {
        // 創建圖表容器
        const chartsContainer = document.createElement('div');
        chartsContainer.className = 'charts-container';
        chartsContainer.innerHTML = `
            <div class="card mb-3">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-chart-bar"></i> 數據視覺化
                            </h6>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary active" data-chart="frequency">
                                    號碼頻率
                                </button>
                                <button type="button" class="btn btn-outline-primary" data-chart="trends">
                                    趨勢分析
                                </button>
                                <button type="button" class="btn btn-outline-primary" data-chart="distribution">
                                    分佈圖
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <div class="chart-loading text-center py-5" id="chartLoading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">載入中...</span>
                                </div>
                                <div class="mt-2">正在生成圖表...</div>
                            </div>
                            <canvas id="mainChart" style="display: none;"></canvas>
                        </div>
                    </div>
                    <div class="row mt-3" id="chartStats" style="display: none;">
                        <div class="col-md-4">
                            <div class="stat-card">
                                <div class="stat-value" id="maxFrequency">-</div>
                                <div class="stat-label">最高頻率</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card">
                                <div class="stat-value" id="avgFrequency">-</div>
                                <div class="stat-label">平均頻率</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card">
                                <div class="stat-value" id="minFrequency">-</div>
                                <div class="stat-label">最低頻率</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 插入到結果容器之前
        const resultsContainer = document.getElementById('resultsContainer');
        if (resultsContainer) {
            resultsContainer.parentNode.insertBefore(chartsContainer, resultsContainer);
        }
    }
    
    bindEvents() {
        // 圖表切換按鈕
        const chartButtons = document.querySelectorAll('[data-chart]');
        chartButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                // 更新按鈕狀態
                chartButtons.forEach(btn => btn.classList.remove('active'));
                e.target.classList.add('active');
                
                // 切換圖表
                const chartType = e.target.getAttribute('data-chart');
                this.switchChart(chartType);
            });
        });
        
        // 監聽數據更新事件
        document.addEventListener('dataUpdated', () => {
            this.updateCharts();
        });
    }
    
    async getCurrentData() {
        try {
            const currentLotteryType = window.currentLotteryType || 'powercolor';
            const params = new URLSearchParams({
                lottery_type: currentLotteryType,
                page: 1,
                limit: 100  // 獲取足夠的數據進行分析
            });
            
            const response = await fetch(`/api/results?${params.toString()}`);
            const data = await response.json();
            
            if (data.success) {
                return data.data;
            } else {
                throw new Error(data.error || '獲取數據失敗');
            }
        } catch (error) {
            console.error('獲取數據錯誤:', error);
            return null;
        }
    }
    
    async updateCharts() {
        // 節流控制，避免頻繁更新
        const now = Date.now();
        if (now - this.lastUpdate < this.updateThrottle) {
            console.log('圖表更新被節流控制跳過');
            return;
        }
        this.lastUpdate = now;
        
        // 防止重複載入
        if (this.isLoading) {
            console.log('圖表正在更新中，跳過重複請求');
            return;
        }
        
        this.isLoading = true;
        this.showLoading();
        
        try {
            const data = await this.getCurrentData();
            if (!data || !data.results) {
                console.warn('無法獲取圖表數據');
                return;
            }
            
            // 獲取當前活動的圖表類型
            const activeButton = document.querySelector('[data-chart].active');
            const chartType = activeButton ? activeButton.getAttribute('data-chart') : 'frequency';
            
            // 使用 requestAnimationFrame 優化渲染性能
            requestAnimationFrame(async () => {
                await this.renderChart(chartType, data);
                console.log('圖表更新完成，類型:', chartType);
            });
            
        } catch (error) {
            console.error('圖表更新錯誤:', error);
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }
    
    async switchChart(chartType) {
        this.showLoading();
        
        const data = await this.getCurrentData();
        if (data) {
            await this.renderChart(chartType, data);
        }
        
        this.hideLoading();
    }
    
    async renderChart(chartType, data) {
        const canvas = document.getElementById('mainChart');
        const ctx = canvas.getContext('2d');
        
        // 銷毀現有圖表
        if (this.charts.main) {
            this.charts.main.destroy();
        }
        
        let chartConfig;
        
        switch (chartType) {
            case 'frequency':
                chartConfig = this.createFrequencyChart(data);
                break;
            case 'trends':
                chartConfig = this.createTrendsChart(data);
                break;
            case 'distribution':
                chartConfig = this.createDistributionChart(data);
                break;
            default:
                chartConfig = this.createFrequencyChart(data);
        }
        
        this.charts.main = new Chart(ctx, chartConfig);
        this.updateStats(chartType, data);
    }
    
    createFrequencyChart(data) {
        const frequency = this.calculateFrequency(data.results);
        const sortedNumbers = Object.keys(frequency).sort((a, b) => frequency[b] - frequency[a]);
        
        return {
            type: 'bar',
            data: {
                labels: sortedNumbers,
                datasets: [{
                    label: '出現次數',
                    data: sortedNumbers.map(num => frequency[num]),
                    backgroundColor: sortedNumbers.map((_, index) => {
                        const hue = (index * 360 / sortedNumbers.length) % 360;
                        return `hsla(${hue}, 70%, 60%, 0.8)`;
                    }),
                    borderColor: sortedNumbers.map((_, index) => {
                        const hue = (index * 360 / sortedNumbers.length) % 360;
                        return `hsla(${hue}, 70%, 50%, 1)`;
                    }),
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '號碼出現頻率統計'
                    },
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '出現次數'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '號碼'
                        }
                    }
                }
            }
        };
    }
    
    createTrendsChart(data) {
        const trends = this.calculateTrends(data.results);
        
        return {
            type: 'line',
            data: {
                labels: trends.labels,
                datasets: [{
                    label: '號碼出現趨勢',
                    data: trends.data,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '號碼出現趨勢分析'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '平均出現次數'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '時間期間'
                        }
                    }
                }
            }
        };
    }
    
    createDistributionChart(data) {
        const distribution = this.calculateDistribution(data.results);
        
        return {
            type: 'doughnut',
            data: {
                labels: distribution.labels,
                datasets: [{
                    data: distribution.data,
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0',
                        '#9966FF',
                        '#FF9F40'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '號碼範圍分佈'
                    }
                }
            }
        };
    }
    
    calculateFrequency(results) {
        const frequency = {};
        const lotteryType = window.currentLotteryType || 'powercolor';
        
        results.forEach(result => {
            let numbers = [];
            
            if (lotteryType === 'powercolor') {
                numbers = result.numbers.first_area || [];
            } else if (lotteryType === 'lotto649') {
                numbers = result.numbers.main_numbers || [];
            } else if (lotteryType === 'dailycash') {
                numbers = result.numbers.numbers || [];
            }
            
            numbers.forEach(num => {
                frequency[num] = (frequency[num] || 0) + 1;
            });
        });
        
        return frequency;
    }
    
    calculateTrends(results) {
        // 簡化的趨勢計算
        const recentResults = results.slice(0, 20);
        const labels = recentResults.map(result => result.period.toString());
        const data = recentResults.map(result => {
            const lotteryType = window.currentLotteryType || 'powercolor';
            let numbers = [];
            
            if (lotteryType === 'powercolor') {
                numbers = result.numbers.first_area || [];
            } else if (lotteryType === 'lotto649') {
                numbers = result.numbers.main_numbers || [];
            } else if (lotteryType === 'dailycash') {
                numbers = result.numbers.numbers || [];
            }
            
            return numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
        });
        
        return { labels: labels.reverse(), data: data.reverse() };
    }
    
    calculateDistribution(results) {
        const ranges = ['1-10', '11-20', '21-30', '31-40', '41-49'];
        const distribution = ranges.map(() => 0);
        
        results.forEach(result => {
            const lotteryType = window.currentLotteryType || 'powercolor';
            let numbers = [];
            
            if (lotteryType === 'powercolor') {
                numbers = result.numbers.first_area || [];
            } else if (lotteryType === 'lotto649') {
                numbers = result.numbers.main_numbers || [];
            } else if (lotteryType === 'dailycash') {
                numbers = result.numbers.numbers || [];
            }
            
            numbers.forEach(num => {
                const rangeIndex = Math.floor((num - 1) / 10);
                if (rangeIndex >= 0 && rangeIndex < distribution.length) {
                    distribution[rangeIndex]++;
                }
            });
        });
        
        return { labels: ranges, data: distribution };
    }
    
    updateStats(chartType, data) {
        const statsContainer = document.getElementById('chartStats');
        
        if (chartType === 'frequency') {
            const frequency = this.calculateFrequency(data.results);
            const frequencies = Object.values(frequency);
            
            document.getElementById('maxFrequency').textContent = Math.max(...frequencies);
            document.getElementById('avgFrequency').textContent = Math.round(frequencies.reduce((sum, f) => sum + f, 0) / frequencies.length);
            document.getElementById('minFrequency').textContent = Math.min(...frequencies);
            
            statsContainer.style.display = 'block';
        } else {
            statsContainer.style.display = 'none';
        }
    }
    
    showLoading() {
        document.getElementById('chartLoading').style.display = 'block';
        document.getElementById('mainChart').style.display = 'none';
    }
    
    hideLoading() {
        document.getElementById('chartLoading').style.display = 'none';
        document.getElementById('mainChart').style.display = 'block';
    }
}

// 等待頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', () => {
    // 只在 results 頁面初始化
    if (document.querySelector('.result-card') !== null || 
        document.getElementById('resultsContainer') !== null ||
        window.location.pathname.includes('results')) {
        
        window.lotteryCharts = new LotteryCharts();
        console.log('圖表功能已初始化');
        
        // 監聽數據載入完成事件
        const originalLoadResults = window.loadResults;
        if (originalLoadResults && typeof originalLoadResults === 'function') {
            window.loadResults = function(...args) {
                const result = originalLoadResults(...args);
                
                // 確保返回值是 Promise
                if (result && typeof result.then === 'function') {
                    return result.then((data) => {
                        // 使用防抖動延遲更新圖表，提升性能
                        clearTimeout(window.chartUpdateTimeout);
                        window.chartUpdateTimeout = setTimeout(() => {
                            if (window.lotteryCharts) {
                                try {
                                    console.log('開始更新圖表...');
                                    window.lotteryCharts.updateCharts();
                                    console.log('圖表更新完成');
                                } catch (error) {
                                    console.error('圖表更新失敗:', error);
                                }
                            }
                        }, 300); // 增加延遲時間減少頻繁更新
                        return data;
                    }).catch((error) => {
                        console.error('數據載入失敗，跳過圖表更新:', error);
                        throw error; // 重新拋出錯誤
                    });
                } else {
                    // 如果不是 Promise，直接觸發圖表更新
                    setTimeout(() => {
                        if (window.lotteryCharts) {
                            try {
                                window.lotteryCharts.updateCharts();
                            } catch (error) {
                                console.error('圖表更新失敗:', error);
                            }
                        }
                    }, 300);
                    return result;
                }
            };
        } else {
            console.warn('loadResults 函數不存在或不是函數類型');
        }
    }
});

// 導出供其他模組使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LotteryCharts;
}