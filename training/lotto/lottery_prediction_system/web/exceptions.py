#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
異常處理模組
定義統一的異常類型和錯誤處理機制
"""

import logging
import traceback
from typing import Dict, Any, Optional, List
from enum import Enum
from dataclasses import dataclass
from datetime import datetime


class ErrorCode(Enum):
    """錯誤代碼枚舉"""
    # 通用錯誤 (1000-1999)
    UNKNOWN_ERROR = 1000
    INVALID_REQUEST = 1001
    MISSING_PARAMETER = 1002
    INVALID_PARAMETER = 1003
    PERMISSION_DENIED = 1004
    RATE_LIMIT_EXCEEDED = 1005
    
    # 數據庫錯誤 (2000-2999)
    DATABASE_ERROR = 2000
    DATABASE_CONNECTION_ERROR = 2001
    DATABASE_QUERY_ERROR = 2002
    RECORD_NOT_FOUND = 2003
    DUPLICATE_RECORD = 2004
    
    # 業務邏輯錯誤 (3000-3999)
    BUSINESS_LOGIC_ERROR = 3000
    INVALID_LOTTERY_TYPE = 3001
    INVALID_PERIOD = 3002
    PREDICTION_FAILED = 3003
    ANALYSIS_FAILED = 3004
    VERIFICATION_FAILED = 3005
    
    # 外部服務錯誤 (4000-4999)
    EXTERNAL_SERVICE_ERROR = 4000
    API_TIMEOUT = 4001
    API_UNAVAILABLE = 4002
    CACHE_ERROR = 4003
    
    # 系統錯誤 (5000-5999)
    SYSTEM_ERROR = 5000
    CONFIGURATION_ERROR = 5001
    RESOURCE_EXHAUSTED = 5002
    SERVICE_UNAVAILABLE = 5003


@dataclass
class ErrorDetail:
    """錯誤詳情"""
    code: ErrorCode
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: Optional[datetime] = None
    trace_id: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        return {
            'code': self.code.value,
            'message': self.message,
            'details': self.details or {},
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'trace_id': self.trace_id
        }


class BaseException(Exception):
    """基礎異常類"""
    
    def __init__(self, 
                 message: str,
                 code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
                 details: Optional[Dict[str, Any]] = None,
                 cause: Optional[Exception] = None):
        super().__init__(message)
        self.error_detail = ErrorDetail(
            code=code,
            message=message,
            details=details
        )
        self.cause = cause
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def log_error(self, level: int = logging.ERROR):
        """記錄錯誤日誌"""
        error_info = {
            'error_code': self.error_detail.code.value,
            'message': self.error_detail.message,
            'details': self.error_detail.details,
            'trace_id': self.error_detail.trace_id
        }
        
        if self.cause:
            error_info['cause'] = str(self.cause)
            error_info['traceback'] = traceback.format_exception(
                type(self.cause), self.cause, self.cause.__traceback__
            )
        
        self.logger.log(level, f"異常發生: {error_info}")
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        return self.error_detail.to_dict()


class ValidationError(BaseException):
    """驗證錯誤"""
    
    def __init__(self, message: str, field: Optional[str] = None, 
                 value: Optional[Any] = None, **kwargs):
        details = kwargs.get('details', {})
        if field:
            details['field'] = field
        if value is not None:
            details['value'] = str(value)
        
        super().__init__(
            message=message,
            code=ErrorCode.INVALID_PARAMETER,
            details=details,
            **{k: v for k, v in kwargs.items() if k != 'details'}
        )


class BusinessLogicError(BaseException):
    """業務邏輯錯誤"""
    
    def __init__(self, message: str, code: ErrorCode = ErrorCode.BUSINESS_LOGIC_ERROR, **kwargs):
        super().__init__(message=message, code=code, **kwargs)


class DatabaseError(BaseException):
    """數據庫錯誤"""
    
    def __init__(self, message: str, code: ErrorCode = ErrorCode.DATABASE_ERROR, 
                 query: Optional[str] = None, **kwargs):
        details = kwargs.get('details', {})
        if query:
            details['query'] = query
        
        super().__init__(
            message=message,
            code=code,
            details=details,
            **{k: v for k, v in kwargs.items() if k != 'details'}
        )


class ExternalServiceError(BaseException):
    """外部服務錯誤"""
    
    def __init__(self, message: str, service_name: Optional[str] = None,
                 code: ErrorCode = ErrorCode.EXTERNAL_SERVICE_ERROR, **kwargs):
        details = kwargs.get('details', {})
        if service_name:
            details['service_name'] = service_name
        
        super().__init__(
            message=message,
            code=code,
            details=details,
            **{k: v for k, v in kwargs.items() if k != 'details'}
        )


class SystemError(BaseException):
    """系統錯誤"""
    
    def __init__(self, message: str, code: ErrorCode = ErrorCode.SYSTEM_ERROR, **kwargs):
        super().__init__(message=message, code=code, **kwargs)


class RateLimitError(BaseException):
    """速率限制錯誤"""
    
    def __init__(self, message: str = "請求頻率過高", 
                 limit: Optional[int] = None, 
                 window: Optional[int] = None, **kwargs):
        details = kwargs.get('details', {})
        if limit:
            details['limit'] = limit
        if window:
            details['window'] = window
        
        super().__init__(
            message=message,
            code=ErrorCode.RATE_LIMIT_EXCEEDED,
            details=details,
            **{k: v for k, v in kwargs.items() if k != 'details'}
        )


class LotteryTypeError(ValidationError):
    """彩票類型錯誤"""
    
    def __init__(self, lottery_type: str, valid_types: Optional[List[str]] = None):
        message = f"無效的彩票類型: {lottery_type}"
        details = {'lottery_type': lottery_type}
        
        if valid_types:
            message += f"，有效類型: {', '.join(valid_types)}"
            details['valid_types'] = valid_types
        
        super().__init__(
            message=message,
            code=ErrorCode.INVALID_LOTTERY_TYPE,
            details=details
        )


class PeriodError(ValidationError):
    """期數錯誤"""
    
    def __init__(self, period: str, lottery_type: Optional[str] = None):
        message = f"無效的期數: {period}"
        details = {'period': period}
        
        if lottery_type:
            message += f" (彩票類型: {lottery_type})"
            details['lottery_type'] = lottery_type
        
        super().__init__(
            message=message,
            code=ErrorCode.INVALID_PERIOD,
            details=details
        )


class PredictionError(BusinessLogicError):
    """預測錯誤"""
    
    def __init__(self, message: str, lottery_type: Optional[str] = None, 
                 method: Optional[str] = None, **kwargs):
        details = kwargs.get('details', {})
        if lottery_type:
            details['lottery_type'] = lottery_type
        if method:
            details['method'] = method
        
        super().__init__(
            message=message,
            code=ErrorCode.PREDICTION_FAILED,
            details=details,
            **{k: v for k, v in kwargs.items() if k != 'details'}
        )


class AnalysisError(BusinessLogicError):
    """分析錯誤"""
    
    def __init__(self, message: str, analysis_type: Optional[str] = None, **kwargs):
        details = kwargs.get('details', {})
        if analysis_type:
            details['analysis_type'] = analysis_type
        
        super().__init__(
            message=message,
            code=ErrorCode.ANALYSIS_FAILED,
            details=details,
            **{k: v for k, v in kwargs.items() if k != 'details'}
        )


class ConfigurationError(SystemError):
    """配置錯誤"""
    
    def __init__(self, message: str, config_key: Optional[str] = None, **kwargs):
        details = kwargs.get('details', {})
        if config_key:
            details['config_key'] = config_key
        
        super().__init__(
            message=message,
            code=ErrorCode.CONFIGURATION_ERROR,
            details=details,
            **{k: v for k, v in kwargs.items() if k != 'details'}
        )


class ExceptionHandler:
    """異常處理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._handlers = {}
    
    def register_handler(self, exception_type: type, handler_func):
        """註冊異常處理器"""
        self._handlers[exception_type] = handler_func
        self.logger.info(f"註冊異常處理器: {exception_type.__name__}")
    
    def handle_exception(self, exception: Exception) -> Dict[str, Any]:
        """處理異常"""
        # 記錄異常
        self.logger.error(f"處理異常: {type(exception).__name__}: {str(exception)}")
        
        # 如果是自定義異常
        if isinstance(exception, BaseException):
            exception.log_error()
            return exception.to_dict()
        
        # 檢查是否有註冊的處理器
        for exc_type, handler in self._handlers.items():
            if isinstance(exception, exc_type):
                try:
                    return handler(exception)
                except Exception as e:
                    self.logger.error(f"異常處理器執行失敗: {e}")
        
        # 默認處理
        return self._handle_unknown_exception(exception)
    
    def _handle_unknown_exception(self, exception: Exception) -> Dict[str, Any]:
        """處理未知異常"""
        error_detail = ErrorDetail(
            code=ErrorCode.UNKNOWN_ERROR,
            message=f"未知錯誤: {str(exception)}",
            details={
                'exception_type': type(exception).__name__,
                'traceback': traceback.format_exc()
            }
        )
        
        return error_detail.to_dict()


# 全局異常處理器實例
exception_handler = ExceptionHandler()

# 註冊標準異常處理器
def handle_value_error(exc: ValueError) -> Dict[str, Any]:
    """處理值錯誤"""
    return ValidationError(f"參數值錯誤: {str(exc)}").to_dict()

def handle_key_error(exc: KeyError) -> Dict[str, Any]:
    """處理鍵錯誤"""
    return ValidationError(f"缺少必要參數: {str(exc)}").to_dict()

def handle_type_error(exc: TypeError) -> Dict[str, Any]:
    """處理類型錯誤"""
    return ValidationError(f"參數類型錯誤: {str(exc)}").to_dict()

def handle_connection_error(exc: Exception) -> Dict[str, Any]:
    """處理連接錯誤"""
    return ExternalServiceError(
        "服務連接失敗",
        details={'error': str(exc)}
    ).to_dict()

# 註冊處理器
exception_handler.register_handler(ValueError, handle_value_error)
exception_handler.register_handler(KeyError, handle_key_error)
exception_handler.register_handler(TypeError, handle_type_error)

# 嘗試註冊數據庫相關異常處理器
try:
    import sqlite3
    def handle_sqlite_error(exc: sqlite3.Error) -> Dict[str, Any]:
        return DatabaseError(
            f"數據庫操作失敗: {str(exc)}",
            code=ErrorCode.DATABASE_QUERY_ERROR
        ).to_dict()
    
    exception_handler.register_handler(sqlite3.Error, handle_sqlite_error)
except ImportError:
    pass

try:
    import requests
    exception_handler.register_handler(requests.ConnectionError, handle_connection_error)
    exception_handler.register_handler(requests.Timeout, 
        lambda exc: ExternalServiceError(
            "請求超時", 
            code=ErrorCode.API_TIMEOUT,
            details={'error': str(exc)}
        ).to_dict()
    )
except ImportError:
    pass


def get_exception_handler() -> ExceptionHandler:
    """獲取全局異常處理器"""
    return exception_handler


def handle_exception_safely(func):
    """安全異常處理裝飾器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            return exception_handler.handle_exception(e)
    return wrapper