<!-- 統一的導航欄模板 -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" href="/">彩票預測系統</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if request.endpoint == 'index' else '' }}" href="/">首頁</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if request.endpoint == 'predictions' else '' }}" href="/predictions?type=powercolor">預測記錄</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if request.endpoint == 'results' else '' }}" href="/results">開獎結果</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if request.endpoint == 'analysis' else '' }}" href="/analysis">分析報告</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if request.endpoint == 'backtesting_dashboard' else '' }}" href="/backtesting">
                        <i class="fas fa-chart-line me-1"></i>回測驗證
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if request.endpoint == 'calibrated_prediction_page' else '' }}" href="/calibrated_prediction">
                        <i class="fas fa-brain me-1"></i>校正模型
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if request.endpoint == 'six_chances_page' else '' }}" href="/six_chances">
                        <i class="fas fa-dice-six me-1"></i>六次機會
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if request.endpoint == 'observable_tracking' else '' }}" href="/observable_tracking">
                        <i class="fas fa-eye me-1"></i>可觀察追蹤
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if request.endpoint == 'backtest_analysis' else '' }}" href="/backtest_analysis">回測分析</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if request.endpoint == 'prediction_management' else '' }}" href="/prediction_management">系統管理</a>
                </li>
            </ul>
        </div>
    </div>
</nav>