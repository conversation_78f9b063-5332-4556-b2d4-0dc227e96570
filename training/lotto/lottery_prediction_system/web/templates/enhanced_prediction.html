<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能預測 - 彩票預測系統</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .prediction-card {
            border-left: 4px solid #007bff;
            margin-bottom: 20px;
            transition: transform 0.2s;
        }
        .prediction-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .number-ball {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 2px;
        }
        .special-ball {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }
        .confidence-meter {
            height: 20px;
            background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }
        .confidence-fill {
            height: 100%;
            background: rgba(255,255,255,0.3);
            border-radius: 10px;
            transition: width 0.5s ease;
        }
        .math-relation {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 8px;
            margin: 3px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            z-index: 9999;
        }
        .loading-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    {% include 'navbar.html' %}

    <!-- 載入覆蓋層 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">分析中...</span>
            </div>
            <h5>🧠 智能分析中...</h5>
            <p>正在執行增強數學分析和預測生成</p>
        </div>
    </div>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1><i class="fas fa-brain"></i> 智能預測系統</h1>
                <p class="text-muted">基於增強數學分析的智能預測，包含所有可能的數學關係檢測</p>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-cogs"></i> 智能預測控制</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <label for="lotteryType" class="form-label">彩票類型</label>
                                <select class="form-select" id="lotteryType" onchange="loadPeriods()">
                                    <option value="powercolor">威力彩</option>
                                    <option value="lotto649">大樂透</option>
                                    <option value="dailycash">今彩539</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="predictionMode" class="form-label">預測模式</label>
                                <select class="form-select" id="predictionMode" onchange="togglePeriodSelection()">
                                    <option value="next" selected>下一期預測</option>
                                    <option value="simulation">模擬校正</option>
                                </select>
                            </div>
                            <div class="col-md-3" id="periodGroup" style="display: none;">
                                <label for="targetPeriod" class="form-label">目標期號</label>
                                <select class="form-select" id="targetPeriod">
                                    <option value="">請選擇期號</option>
                                </select>
                                <small class="form-text text-muted">選擇過往期號進行模擬校正</small>
                            </div>
                            <div class="col-md-3">
                                <label for="candidatesCount" class="form-label">候選數量</label>
                                <select class="form-select" id="candidatesCount">
                                    <option value="3">3 個候選</option>
                                    <option value="5" selected>5 個候選</option>
                                    <option value="8">8 個候選</option>
                                    <option value="10">10 個候選</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="analysisMode" class="form-label">分析模式</label>
                                <select class="form-select" id="analysisMode">
                                    <option value="standard">標準分析</option>
                                    <option value="enhanced" selected>增強分析</option>
                                    <option value="comprehensive">全面分析</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button class="btn btn-primary w-100" onclick="startEnhancedPrediction()">
                                    <i class="fas fa-magic"></i> 開始智能預測
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 預測結果區域 -->
        <div id="predictionResults" style="display: none;">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-line"></i> 數學關係分析摘要</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 text-center">
                                    <h4 id="arithmeticCount" class="text-primary">-</h4>
                                    <small>算術關係</small>
                                </div>
                                <div class="col-md-3 text-center">
                                    <h4 id="sequenceCount" class="text-success">-</h4>
                                    <small>數列關係</small>
                                </div>
                                <div class="col-md-3 text-center">
                                    <h4 id="modularCount" class="text-warning">-</h4>
                                    <small>模運算關係</small>
                                </div>
                                <div class="col-md-3 text-center">
                                    <h4 id="totalRelations" class="text-info">-</h4>
                                    <small>總關係數</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <h3><i class="fas fa-star"></i> 智能預測候選</h3>
                    <div id="predictionCandidates">
                        <!-- 動態填充預測候選 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 數學分析詳情 -->
        <div id="analysisDetails" style="display: none;">
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-plus"></i> 算術關係</h6>
                        </div>
                        <div class="card-body">
                            <div id="arithmeticDetails">
                                <!-- 動態填充 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-list-ol"></i> 數列關係</h6>
                        </div>
                        <div class="card-body">
                            <div id="sequenceDetails">
                                <!-- 動態填充 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-percentage"></i> 模運算關係</h6>
                        </div>
                        <div class="card-body">
                            <div id="modularDetails">
                                <!-- 動態填充 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-chart-bar"></i> 統計關係</h6>
                        </div>
                        <div class="card-body">
                            <div id="statisticalDetails">
                                <!-- 動態填充 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 載入期號列表
        function loadPeriods() {
            const lotteryType = document.getElementById('lotteryType').value;
            
            console.log('開始載入期數列表，彩票類型:', lotteryType); // 調試日誌
            
            fetch(`/api/periods/${lotteryType}?limit=30`)
            .then(response => response.json())
            .then(data => {
                console.log('API響應:', data); // 調試日誌
                if (data.success && data.data && data.data.periods) {
                    const periodSelect = document.getElementById('targetPeriod');
                    periodSelect.innerHTML = '<option value="">請選擇期號</option>';
                    
                    console.log('載入期數:', data.data.periods.length, '個'); // 調試日誌
                    
                    data.data.periods.forEach(period => {
                        const option = document.createElement('option');
                        option.value = period;
                        option.textContent = `第 ${period} 期`;
                        periodSelect.appendChild(option);
                    });
                    
                    console.log('期數選項已添加到下拉菜單'); // 調試日誌
                } else {
                    console.error('期數資料格式錯誤:', data);
                }
            })
            .catch(error => {
                console.error('載入期號列表失敗:', error);
            });
        }
        
        // 切換期號選擇顯示
        function togglePeriodSelection() {
            const predictionMode = document.getElementById('predictionMode').value;
            const periodGroup = document.getElementById('periodGroup');
            
            console.log('預測模式切換:', predictionMode); // 調試日誌
            
            if (predictionMode === 'simulation') {
                periodGroup.style.display = 'block';
                console.log('載入期數列表...'); // 調試日誌
                loadPeriods(); // 載入期號列表
            } else {
                periodGroup.style.display = 'none';
            }
        }
        
        async function startEnhancedPrediction() {
            const lotteryType = document.getElementById('lotteryType').value;
            const candidatesCount = document.getElementById('candidatesCount').value;
            const analysisMode = document.getElementById('analysisMode').value;
            const predictionMode = document.getElementById('predictionMode').value;
            const targetPeriod = document.getElementById('targetPeriod').value;
            
            // 檢查模擬模式是否選擇了期號
            if (predictionMode === 'simulation' && !targetPeriod) {
                alert('請選擇目標期號進行模擬校正');
                return;
            }
            
            // 顯示載入覆蓋層
            document.getElementById('loadingOverlay').style.display = 'block';
            
            try {
                // 準備請求數據
                const requestData = {
                    lottery_type: lotteryType,
                    candidates_count: parseInt(candidatesCount),
                    analysis_mode: analysisMode,
                    prediction_mode: predictionMode
                };
                
                // 如果是模擬模式，添加目標期號
                if (predictionMode === 'simulation') {
                    requestData.target_period = targetPeriod;
                }
                
                // 1. 先執行增強板路分析
                const analysisResponse = await fetch('/api/enhanced_board_analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                const analysisData = await analysisResponse.json();
                
                if (!analysisData.success) {
                    throw new Error(analysisData.error);
                }
                
                // 2. 執行增強預測
                const predictionResponse = await fetch('/api/predict_with_enhanced_analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                const predictionData = await predictionResponse.json();
                
                if (!predictionData.success) {
                    throw new Error(predictionData.error);
                }
                
                // 顯示結果
                displayEnhancedResults(analysisData.analysis, predictionData.prediction, predictionMode, targetPeriod);
                
            } catch (error) {
                console.error('智能預測錯誤:', error);
                alert('智能預測失敗: ' + error.message);
            } finally {
                // 隱藏載入覆蓋層
                document.getElementById('loadingOverlay').style.display = 'none';
            }
        }
        
        function displayEnhancedResults(analysis, prediction, predictionMode = 'next', targetPeriod = null) {
            const resultsContainer = document.getElementById('predictionResults');
            const detailsContainer = document.getElementById('analysisDetails');
            
            // 添加模式標題
            let modeTitle = '';
            if (predictionMode === 'simulation' && targetPeriod) {
                modeTitle = `<div class="alert alert-info mb-3">
                    <h5><i class="fas fa-flask"></i> 模擬校正模式</h5>
                    <p>正在對第 ${targetPeriod} 期進行模擬預測，用於算法準確性驗證</p>
                </div>`;
            } else {
                modeTitle = `<div class="alert alert-primary mb-3">
                    <h5><i class="fas fa-crystal-ball"></i> 下一期預測模式</h5>
                    <p>基於歷史數據分析，預測下一期開獎號碼</p>
                </div>`;
            }
            
            // 顯示分析摘要
            displayAnalysisSummary(analysis);

            // 顯示最佳預測結果（如果有）
            if (prediction.best_prediction) {
                displayBestPredictionResult(prediction.best_prediction, predictionMode, targetPeriod);
            }

            // 顯示預測候選
            displayPredictionCandidates(prediction.predictions, predictionMode);

            // 顯示詳細分析
            displayAnalysisDetails(analysis);

            // 顯示結果區域
            resultsContainer.style.display = 'block';
            detailsContainer.style.display = 'block';
            
            // 在結果區域頂部添加模式標題
            const existingContent = resultsContainer.innerHTML;
            resultsContainer.innerHTML = modeTitle + existingContent;
            
            // 如果是模擬模式，添加準確性驗證按鈕
            if (predictionMode === 'simulation' && targetPeriod) {
                addSimulationVerification(targetPeriod, resultsContainer);
            }
        }

        function displayBestPredictionResult(bestPrediction, predictionMode = 'next', targetPeriod = null) {
            // 在預測候選之前插入最佳預測結果
            const candidatesContainer = document.getElementById('predictionCandidates');

            let headerTitle = '最佳整合預測';
            let headerClass = 'bg-success';
            
            if (predictionMode === 'simulation') {
                headerTitle = `模擬預測結果 (第 ${targetPeriod} 期)`;
                headerClass = 'bg-info';
            }

            // 創建最佳預測卡片
            const bestCard = document.createElement('div');
            bestCard.className = 'card border-success mb-4';
            bestCard.innerHTML = `
                <div class="card-header ${headerClass} text-white">
                    <h5 class="mb-0"><i class="fas fa-star"></i> ${headerTitle}</h5>
                </div>
                <div class="card-body">
                    ${formatBestPredictionContent(bestPrediction, predictionMode, targetPeriod)}
                </div>
            `;

            // 插入到候選列表之前
            candidatesContainer.parentNode.insertBefore(bestCard, candidatesContainer);
        }

        function formatBestPredictionContent(prediction, predictionMode = 'next', targetPeriod = null) {
            const lotteryType = document.getElementById('lotteryType').value;
            let html = '<div class="mb-3">';

            if (lotteryType === 'powercolor') {
                html += `<strong>第一區:</strong> ${formatNumberBalls(prediction['第一區'])} `;
                html += `<strong>第二區:</strong> ${formatNumberBalls([prediction['第二區']], true)}`;
            } else if (lotteryType === 'lotto649') {
                html += `<strong>第一區:</strong> ${formatNumberBalls(prediction['第一區'])} `;
                html += `<strong>特別號:</strong> ${formatNumberBalls([prediction['特別號']], true)}`;
            } else if (lotteryType === 'dailycash') {
                html += `<strong>號碼:</strong> ${formatNumberBalls(prediction['號碼'])}`;
            }

            html += '</div>';

            if (prediction['信心分數']) {
                html += `<div class="mb-2"><strong>信心分數:</strong> <span class="badge bg-primary">${prediction['信心分數'].toFixed(3)}</span></div>`;
            }

            if (prediction['整合解釋']) {
                html += '<div class="mb-2"><strong>整合說明:</strong><ul class="mb-0">';
                prediction['整合解釋'].forEach(exp => {
                    html += `<li>${exp}</li>`;
                });
                html += '</ul></div>';
            }

            if (prediction['詳細解釋']) {
                html += '<div class="mb-2"><strong>詳細分析:</strong><ul class="mb-0">';
                prediction['詳細解釋'].forEach(exp => {
                    html += `<li>${exp}</li>`;
                });
                html += '</ul></div>';
            }

            if (prediction['候選數量']) {
                html += `<div class="text-muted small">基於 ${prediction['候選數量']} 個候選結果整合</div>`;
            }

            let additionalInfo = '';
            if (predictionMode === 'simulation') {
                additionalInfo = `
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-info-circle"></i> 
                        這是針對第 ${targetPeriod} 期的模擬預測，可用於驗證算法準確性
                    </div>
                `;
            }

            html += additionalInfo;

            return html;
        }

        function formatNumberBalls(numbers, isSpecial = false) {
            if (!numbers || numbers.length === 0) return '';

            return numbers.map(num =>
                `<span class="number-ball ${isSpecial ? 'special-ball' : ''}">${num}</span>`
            ).join('');
        }

        function displayAnalysisSummary(analysis) {
            document.getElementById('arithmeticCount').textContent = 
                (analysis.arithmetic_relationships || []).length;
            document.getElementById('sequenceCount').textContent = 
                (analysis.sequence_relationships || []).length;
            document.getElementById('modularCount').textContent = 
                (analysis.modular_relationships || []).length;
            
            const totalRelations = 
                (analysis.arithmetic_relationships || []).length +
                (analysis.sequence_relationships || []).length +
                (analysis.modular_relationships || []).length +
                (analysis.multiplicative_relationships || []).length +
                (analysis.exponential_relationships || []).length;
            
            document.getElementById('totalRelations').textContent = totalRelations;
        }
        
        function displayPredictionCandidates(predictions, predictionMode = 'next') {
            const container = document.getElementById('predictionCandidates');
            container.innerHTML = '';
            
            let headerTitle = `預測候選 (${predictions.length}組)`;
            let headerClass = 'bg-info';
            
            if (predictionMode === 'simulation') {
                headerTitle = `模擬預測候選 (${predictions.length}組)`;
                headerClass = 'bg-secondary';
            }
            
            const headerCard = document.createElement('div');
            headerCard.className = 'card mb-4';
            headerCard.innerHTML = `
                <div class="card-header ${headerClass} text-white">
                    <h5 class="mb-0"><i class="fas fa-list"></i> ${headerTitle}</h5>
                </div>
                <div class="card-body" id="candidatesBody">
                </div>
            `;
            container.appendChild(headerCard);
            
            const candidatesBody = document.getElementById('candidatesBody');
            
            predictions.forEach((pred, index) => {
                const card = document.createElement('div');
                card.className = 'prediction-card card mb-3';
                
                const confidencePercent = (pred.confidence * 100).toFixed(1);
                const confidenceClass = pred.confidence > 0.8 ? 'success' : 
                                      pred.confidence > 0.6 ? 'warning' : 'danger';
                
                let numbersHtml = '';
                (pred.main_numbers || []).forEach(num => {
                    numbersHtml += `<span class="number-ball">${num}</span>`;
                });
                
                if (pred.special_number) {
                    numbersHtml += `<span class="number-ball special-ball">${pred.special_number}</span>`;
                }
                
                let explanationHtml = '';
                (pred.explanation || []).forEach(exp => {
                    explanationHtml += `<li>${exp}</li>`;
                });
                
                card.innerHTML = `
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h6>候選 #${index + 1} - ${pred.method || '智能分析'}</h6>
                                <div class="mb-3">${numbersHtml}</div>
                                <ul class="small">${explanationHtml}</ul>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h6>信心分數</h6>
                                    <div class="confidence-meter mb-2">
                                        <div class="confidence-fill" style="width: ${confidencePercent}%"></div>
                                    </div>
                                    <span class="badge bg-${confidenceClass}">${confidencePercent}%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                candidatesBody.appendChild(card);
            });
        }
        
        function displayAnalysisDetails(analysis) {
            // 算術關係
            const arithmeticContainer = document.getElementById('arithmeticDetails');
            arithmeticContainer.innerHTML = '';
            (analysis.arithmetic_relationships || []).slice(0, 5).forEach(rel => {
                const div = document.createElement('div');
                div.className = 'math-relation';
                div.textContent = rel.formula || `${rel.type}: ${(rel.numbers || []).join(', ')}`;
                arithmeticContainer.appendChild(div);
            });
            
            // 數列關係
            const sequenceContainer = document.getElementById('sequenceDetails');
            sequenceContainer.innerHTML = '';
            (analysis.sequence_relationships || []).slice(0, 5).forEach(rel => {
                const div = document.createElement('div');
                div.className = 'math-relation';
                div.textContent = rel.formula || `${rel.type}: ${(rel.numbers || []).join(', ')}`;
                sequenceContainer.appendChild(div);
            });
            
            // 模運算關係
            const modularContainer = document.getElementById('modularDetails');
            modularContainer.innerHTML = '';
            (analysis.modular_relationships || []).slice(0, 5).forEach(rel => {
                const div = document.createElement('div');
                div.className = 'math-relation';
                div.textContent = rel.formula || `模 ${rel.modulus}: ${(rel.remainders || []).join(', ')}`;
                modularContainer.appendChild(div);
            });
            
            // 統計關係
            const statisticalContainer = document.getElementById('statisticalDetails');
            statisticalContainer.innerHTML = '';
            const statRel = analysis.statistical_relationships || {};
            if (statRel.distribution_patterns) {
                const div = document.createElement('div');
                div.className = 'math-relation';
                div.textContent = `頻率分布: ${Object.keys(statRel.distribution_patterns.frequency_distribution || {}).length} 個號碼`;
                statisticalContainer.appendChild(div);
            }
        }
        
        // 添加模擬驗證功能
        function addSimulationVerification(targetPeriod, container) {
            const verificationHtml = `
                <div class="card mt-4 border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-check-circle"></i> 準確性驗證</h5>
                    </div>
                    <div class="card-body">
                        <p>模擬預測完成！您可以：</p>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-primary" onclick="loadActualResults('${targetPeriod}')">
                                <i class="fas fa-search"></i> 查看實際開獎結果
                            </button>
                            <button type="button" class="btn btn-success" onclick="compareResults('${targetPeriod}')">
                                <i class="fas fa-balance-scale"></i> 比較預測準確性
                            </button>
                        </div>
                        <div id="verificationResults" class="mt-3"></div>
                    </div>
                </div>
            `;
            container.innerHTML += verificationHtml;
        }
        
        // 載入實際開獎結果
        function loadActualResults(period) {
            const lotteryType = document.getElementById('lotteryType').value;
            
            fetch(`/api/period_data/${lotteryType}/${period}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {
                        displayActualResults(data.data, period);
                    } else {
                        document.getElementById('verificationResults').innerHTML = 
                            '<div class="alert alert-warning">無法找到該期的實際開獎結果</div>';
                    }
                })
                .catch(error => {
                    console.error('載入實際結果失敗:', error);
                    document.getElementById('verificationResults').innerHTML = 
                        '<div class="alert alert-danger">載入實際結果時發生錯誤</div>';
                });
        }
        
        // 顯示實際開獎結果
        function displayActualResults(actualData, period) {
            const lotteryType = document.getElementById('lotteryType').value;
            let numbersHtml = '';
            
            if (lotteryType === 'powercolor') {
                const mainNumbers = actualData.main_numbers || [];
                const powerball = actualData.powerball;
                numbersHtml = `
                    ${formatNumberBalls(mainNumbers, 'main')}
                    ${powerball ? formatNumberBalls([powerball], 'power') : ''}
                `;
            } else if (lotteryType === 'lotto649') {
                const numbers = actualData.numbers || [];
                numbersHtml = formatNumberBalls(numbers, 'main');
            } else if (lotteryType === 'dailycash') {
                const numbers = actualData.numbers || [];
                numbersHtml = formatNumberBalls(numbers, 'main');
            }
            
            const resultHtml = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-trophy"></i> 第 ${period} 期實際開獎結果：</h6>
                    <div class="prediction-numbers">
                        ${numbersHtml}
                    </div>
                    <small class="text-muted">開獎日期: ${actualData.date || '未知'}</small>
                </div>
            `;
            
            document.getElementById('verificationResults').innerHTML = resultHtml;
        }
        
        // 比較預測結果
        function compareResults(period) {
            const lotteryType = document.getElementById('lotteryType').value;
            
            // 獲取預測結果
            const predictionCards = document.querySelectorAll('.prediction-numbers');
            if (predictionCards.length === 0) {
                alert('找不到預測結果');
                return;
            }
            
            // 載入實際結果並進行比較
            fetch(`/api/period_data/${lotteryType}/${period}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {
                        performComparison(data.data, period);
                    } else {
                        document.getElementById('verificationResults').innerHTML = 
                            '<div class="alert alert-warning">無法找到該期的實際開獎結果進行比較</div>';
                    }
                })
                .catch(error => {
                    console.error('比較結果失敗:', error);
                    document.getElementById('verificationResults').innerHTML = 
                        '<div class="alert alert-danger">比較結果時發生錯誤</div>';
                });
        }
        
        // 執行預測準確性比較
        function performComparison(actualData, period) {
            // 這裡可以實現詳細的比較邏輯
            // 暫時顯示基本比較信息
            const comparisonHtml = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-chart-line"></i> 準確性分析</h6>
                    <p>正在分析第 ${period} 期的預測準確性...</p>
                    <div class="progress mb-2">
                        <div class="progress-bar" role="progressbar" style="width: 75%">75%</div>
                    </div>
                    <small>詳細的準確性分析功能正在開發中</small>
                </div>
            `;
            
            document.getElementById('verificationResults').innerHTML = comparisonHtml;
        }
    </script>
</body>
</html>
