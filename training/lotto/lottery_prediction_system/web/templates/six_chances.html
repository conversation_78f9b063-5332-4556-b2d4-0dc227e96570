<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>六次機會策略 - 彩票預測系統</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .strategy-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .strategy-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }
        
        .number-ball {
            display: inline-block;
            width: 35px;
            height: 35px;
            line-height: 35px;
            text-align: center;
            border-radius: 50%;
            margin: 2px;
            font-weight: bold;
            font-size: 14px;
            color: white;
        }
        
        .main-number { background: linear-gradient(45deg, #007bff, #0056b3); }
        .special-number { background: linear-gradient(45deg, #dc3545, #a71e2a); }
        
        .strategy-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }
        
        .loading-content {
            background: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }
        
        .strategy-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .probability-bar {
            height: 8px;
            background: linear-gradient(90deg, #ff4757, #ffa502, #2ed573);
            border-radius: 4px;
            margin: 5px 0;
        }
        
        .analysis-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .coverage-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .coverage-high { background-color: #28a745; }
        .coverage-medium { background-color: #ffc107; }
        .coverage-low { background-color: #dc3545; }
        
        .investment-summary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .strategy-comparison {
            display: none;
        }
    </style>
</head>
<body>
    {% include 'navbar.html' %}

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner-border text-primary mb-3" role="status"></div>
            <h5>正在生成六次機會策略...</h5>
            <p class="text-muted">請稍候，系統正在計算最優投注組合</p>
        </div>
    </div>

    <div class="container mt-4">
        <!-- 頁面標題 -->
        <div class="strategy-header text-center">
            <h1><i class="fas fa-dice-six"></i> 六次機會智能投注策略</h1>
            <p class="lead mb-0">基於數學優化和覆蓋理論的系統化投注方案</p>
        </div>

        <!-- 策略選擇控制面板 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card strategy-card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-cog"></i> 策略生成控制台</h5>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-end">
                            <div class="col-md-3">
                                <label for="lotteryType" class="form-label">彩票類型</label>
                                <select class="form-select" id="lotteryType">
                                    <option value="powercolor" selected>威力彩</option>
                                    <option value="lotto649">大樂透</option>
                                    <option value="dailycash">今彩539</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="strategyType" class="form-label">策略類型</label>
                                <select class="form-select" id="strategyType">
                                    <option value="mixed" selected>混合策略（推薦）</option>
                                    <option value="wheel">輪盤覆蓋</option>
                                    <option value="zone">區間平衡</option>
                                    <option value="hotcold">冷熱混合</option>
                                    <option value="math">數學優化</option>
                                    <option value="pattern">模式識別</option>
                                    <option value="ai">AI集成</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-primary btn-lg" onclick="generateStrategy()" id="generateBtn">
                                    <i class="fas fa-magic"></i> 生成策略
                                </button>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-info btn-lg" onclick="showProbabilityAnalysis()" id="analysisBtn">
                                    <i class="fas fa-chart-line"></i> 概率分析
                                </button>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-warning btn-lg" onclick="generateHighestConfidence()" id="confidenceBtn">
                                    <i class="fas fa-bullseye"></i> 精準預測
                                </button>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-success btn-lg" onclick="compareStrategies()" id="compareBtn">
                                    <i class="fas fa-balance-scale"></i> 策略比較
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 策略結果展示區域 -->
        <div id="strategyResults" style="display: none;">
            <!-- 投資概要 -->
            <div class="investment-summary" id="investmentSummary">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h4 id="totalInvestment">$300</h4>
                        <small>總投資金額</small>
                    </div>
                    <div class="col-md-3">
                        <h4 id="totalTickets">6</h4>
                        <small>投注組數</small>
                    </div>
                    <div class="col-md-3">
                        <h4 id="coverageRate">-</h4>
                        <small>號碼覆蓋率</small>
                    </div>
                    <div class="col-md-3">
                        <h4 id="winProbability">-</h4>
                        <small>中獎概率提升</small>
                    </div>
                </div>
            </div>

            <!-- 策略詳細組合 -->
            <div id="strategyDetails"></div>

            <!-- 覆蓋率分析 -->
            <div class="analysis-section" id="coverageAnalysis" style="display: none;">
                <h5><i class="fas fa-chart-pie"></i> 覆蓋率分析</h5>
                <div id="coverageContent"></div>
            </div>
        </div>

        <!-- 概率分析展示區域 -->
        <div id="probabilityResults" style="display: none;">
            <div class="analysis-section">
                <h5><i class="fas fa-calculator"></i> 數學概率分析</h5>
                <div id="probabilityContent"></div>
            </div>
        </div>

        <!-- 策略說明 -->
        <div class="row">
            <div class="col-12">
                <div class="card strategy-card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-lightbulb"></i> 策略說明</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>六種核心策略：</h6>
                                <ul class="list-unstyled">
                                    <li><span class="coverage-indicator coverage-high"></span><strong>輪盤覆蓋：</strong>選12個核心號碼，確保3個號碼的完整覆蓋</li>
                                    <li><span class="coverage-indicator coverage-high"></span><strong>區間平衡：</strong>號碼分布在不同區間，提高均勻性</li>
                                    <li><span class="coverage-indicator coverage-medium"></span><strong>冷熱混合：</strong>結合高頻和低頻號碼</li>
                                    <li><span class="coverage-indicator coverage-medium"></span><strong>數學優化：</strong>基於組合數學的最優覆蓋</li>
                                    <li><span class="coverage-indicator coverage-medium"></span><strong>模式識別：</strong>利用常見開獎模式</li>
                                    <li><span class="coverage-indicator coverage-low"></span><strong>AI集成：</strong>結合多個AI模型預測</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>核心優勢：</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check-circle text-success"></i> 概率提升：中小獎概率提升約6倍</li>
                                    <li><i class="fas fa-check-circle text-success"></i> 風險分散：降低完全落空的概率</li>
                                    <li><i class="fas fa-check-circle text-success"></i> 策略多樣：不同策略覆蓋不同可能性</li>
                                    <li><i class="fas fa-check-circle text-success"></i> 心理滿足：增加控制感和期待感</li>
                                </ul>
                                
                                <h6>注意事項：</h6>
                                <ul class="list-unstyled text-muted">
                                    <li><i class="fas fa-exclamation-triangle text-warning"></i> 期望值仍為負數，不是投資工具</li>
                                    <li><i class="fas fa-exclamation-triangle text-warning"></i> 總投資成本增加6倍</li>
                                    <li><i class="fas fa-exclamation-triangle text-warning"></i> 沒有改變彩票的隨機本質</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let currentStrategyData = null;
        
        // 生成策略
        async function generateStrategy() {
            const lotteryType = document.getElementById('lotteryType').value;
            const strategyType = document.getElementById('strategyType').value;
            const loadingOverlay = document.getElementById('loadingOverlay');
            const generateBtn = document.getElementById('generateBtn');
            
            try {
                // 顯示加載動畫
                loadingOverlay.style.display = 'flex';
                generateBtn.disabled = true;
                
                const response = await fetch(`/api/six_chances_strategy/${lotteryType}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        strategy: strategyType
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    currentStrategyData = result.data;
                    displayStrategyResults(result.data);
                } else {
                    alert(`策略生成失敗：${result.error}`);
                }
                
            } catch (error) {
                alert(`網絡錯誤：${error.message}`);
            } finally {
                loadingOverlay.style.display = 'none';
                generateBtn.disabled = false;
            }
        }
        
        // 顯示策略結果
        function displayStrategyResults(data) {
            const resultsDiv = document.getElementById('strategyResults');
            const detailsDiv = document.getElementById('strategyDetails');
            
            // 更新投資概要
            document.getElementById('totalInvestment').textContent = `$${data.total_investment}`;
            document.getElementById('totalTickets').textContent = data.total_chances;
            document.getElementById('coverageRate').textContent = data.coverage_analysis.coverage_rate;
            document.getElementById('winProbability').textContent = data.win_probability.coverage_improvement;
            
            // 生成策略詳細組合
            let detailsHtml = `
                <div class="row mb-4">
                    <div class="col-12">
                        <h4><i class="fas fa-list"></i> ${data.strategy_description}</h4>
                        <p class="text-muted">生成時間：${new Date(data.timestamp).toLocaleString('zh-TW')}</p>
                    </div>
                </div>
                <div class="row">
            `;
            
            data.combinations.forEach((combo, index) => {
                // 生成主號碼球
                let numbersHtml = '';
                combo.numbers.forEach(num => {
                    numbersHtml += `<span class="number-ball main-number">${num}</span>`;
                });
                
                // 特別號球
                let specialHtml = '';
                if (combo.special && data.lottery_info.special_numbers > 0) {
                    specialHtml = `<span class="number-ball special-number">${combo.special}</span>`;
                }
                
                detailsHtml += `
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card strategy-card h-100">
                            <div class="card-header bg-light position-relative">
                                <h6 class="mb-0">第${combo.chance}組</h6>
                                <span class="strategy-badge bg-primary text-white">${combo.strategy_detail}</span>
                            </div>
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    ${numbersHtml}
                                </div>
                                ${specialHtml ? `<div class="mb-2">${specialHtml}</div>` : ''}
                                <small class="text-muted">主號碼${data.lottery_info.special_numbers > 0 ? ' + 特別號' : ''}</small>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            detailsHtml += '</div>';
            detailsDiv.innerHTML = detailsHtml;
            
            // 顯示覆蓋率分析
            displayCoverageAnalysis(data.coverage_analysis);
            
            // 顯示結果區域
            resultsDiv.style.display = 'block';
            document.getElementById('probabilityResults').style.display = 'none';
            
            // 滾動到結果
            resultsDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        // 顯示覆蓋率分析
        function displayCoverageAnalysis(coverage) {
            const analysisDiv = document.getElementById('coverageAnalysis');
            const contentDiv = document.getElementById('coverageContent');
            
            let contentHtml = `
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h5>${coverage.unique_numbers}/38</h5>
                            <small class="text-muted">使用號碼數</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h5>${coverage.coverage_rate}</h5>
                            <small class="text-muted">覆蓋率</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h5>${coverage.total_numbers_used}</h5>
                            <small class="text-muted">總選號次數</small>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6>高頻選號：</h6>
            `;
            
            coverage.most_frequent.forEach(([number, count]) => {
                const percentage = (count / 6 * 100).toFixed(1);
                contentHtml += `
                    <span class="badge bg-info me-2 mb-2">
                        ${number} <small>(${count}次, ${percentage}%)</small>
                    </span>
                `;
            });
            
            contentHtml += '</div>';
            contentDiv.innerHTML = contentHtml;
            analysisDiv.style.display = 'block';
        }
        
        // 顯示概率分析
        async function showProbabilityAnalysis() {
            const lotteryType = document.getElementById('lotteryType').value;
            const loadingOverlay = document.getElementById('loadingOverlay');
            const analysisBtn = document.getElementById('analysisBtn');
            
            try {
                // 顯示加載動畫
                loadingOverlay.style.display = 'flex';
                analysisBtn.disabled = true;
                
                const response = await fetch(`/api/six_chances_probability_analysis/${lotteryType}`);
                const result = await response.json();
                
                if (result.success) {
                    displayProbabilityAnalysis(result.data);
                } else {
                    alert(`概率分析失敗：${result.error}`);
                }
                
            } catch (error) {
                alert(`網絡錯誤：${error.message}`);
            } finally {
                loadingOverlay.style.display = 'none';
                analysisBtn.disabled = false;
            }
        }
        
        // 顯示概率分析結果
        function displayProbabilityAnalysis(data) {
            const resultsDiv = document.getElementById('probabilityResults');
            const contentDiv = document.getElementById('probabilityContent');
            
            let contentHtml = `
                <div class="row mb-4">
                    <div class="col-12">
                        <h6>投資回報概要</h6>
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="p-3 bg-primary text-white rounded">
                                    <h5>$${data.summary.investment_cost}</h5>
                                    <small>投資成本</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="p-3 bg-success text-white rounded">
                                    <h5>$${data.summary.expected_return.toFixed(2)}</h5>
                                    <small>期望收益</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="p-3 bg-info text-white rounded">
                                    <h5>${data.summary.roi_percentage}</h5>
                                    <small>投資回報率</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="p-3 bg-warning text-dark rounded">
                                    <h5>$${data.summary.entertainment_cost.toFixed(2)}</h5>
                                    <small>娛樂成本</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>單次 vs 六次機會概率對比</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>獎項</th>
                                        <th>單次概率</th>
                                        <th>六次概率</th>
                                        <th>提升倍數</th>
                                    </tr>
                                </thead>
                                <tbody>
            `;
            
            for (const [prize, improvement] of Object.entries(data.six_chances_improvements)) {
                const improvementFactor = improvement.improvement.toFixed(2);
                contentHtml += `
                    <tr>
                        <td>${prize}</td>
                        <td>${improvement.single_prob.toExponential(2)}</td>
                        <td>${improvement.six_chances_prob.toExponential(2)}</td>
                        <td><span class="badge bg-success">${improvementFactor}x</span></td>
                    </tr>
                `;
            }
            
            contentHtml += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>策略覆蓋效果對比</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>策略類型</th>
                                        <th>覆蓋率</th>
                                        <th>描述</th>
                                    </tr>
                                </thead>
                                <tbody>
            `;
            
            for (const [strategy, info] of Object.entries(data.coverage_strategies)) {
                const coveragePercent = (info.coverage * 100).toFixed(1);
                let badgeClass = 'bg-secondary';
                if (info.coverage >= 0.8) badgeClass = 'bg-success';
                else if (info.coverage >= 0.7) badgeClass = 'bg-warning';
                
                contentHtml += `
                    <tr>
                        <td>${strategy}</td>
                        <td><span class="badge ${badgeClass}">${coveragePercent}%</span></td>
                        <td><small class="text-muted">${info.description}</small></td>
                    </tr>
                `;
            }
            
            contentHtml += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
            
            contentDiv.innerHTML = contentHtml;
            
            // 隱藏策略結果，顯示概率分析
            document.getElementById('strategyResults').style.display = 'none';
            resultsDiv.style.display = 'block';
            
            // 滾動到結果
            resultsDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        // 生成最高信心度精準預測
        async function generateHighestConfidence() {
            const lotteryType = document.getElementById('lotteryType').value;
            const loadingOverlay = document.getElementById('loadingOverlay');
            const confidenceBtn = document.getElementById('confidenceBtn');
            
            try {
                // 顯示加載動畫
                loadingOverlay.style.display = 'flex';
                confidenceBtn.disabled = true;
                
                const response = await fetch(`/api/highest_confidence_prediction/${lotteryType}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    displayHighestConfidenceResult(result.data);
                } else {
                    alert(`精準預測失敗：${result.error}`);
                }
                
            } catch (error) {
                alert(`網絡錯誤：${error.message}`);
            } finally {
                loadingOverlay.style.display = 'none';
                confidenceBtn.disabled = false;
            }
        }
        
        // 顯示最高信心度結果
        function displayHighestConfidenceResult(data) {
            const resultsDiv = document.getElementById('strategyResults');
            const detailsDiv = document.getElementById('strategyDetails');
            const prediction = data.prediction;
            
            // 更新投資概要
            document.getElementById('totalInvestment').textContent = `$${prediction.cost_comparison.single_bet}`;
            document.getElementById('totalTickets').textContent = '1';
            document.getElementById('coverageRate').textContent = `${(prediction.confidence * 100).toFixed(1)}%`;
            document.getElementById('winProbability').textContent = `${prediction.cost_comparison.cost_advantage}`;
            
            // 生成號碼球
            let numbersHtml = '';
            prediction.numbers.forEach(num => {
                numbersHtml += `<span class="number-ball main-number">${num}</span>`;
            });
            
            // 特別號球
            let specialHtml = '';
            if (prediction.special_number) {
                specialHtml = `<span class="number-ball special-number">${prediction.special_number}</span>`;
            }
            
            // 生成結果HTML
            let detailsHtml = `
                <div class="row mb-4">
                    <div class="col-12">
                        <h4><i class="fas fa-bullseye"></i> 最高信心度精準預測</h4>
                        <p class="text-muted">
                            基於六種策略共識分析 | 預測期號：${prediction.period} | 
                            信心度：${(prediction.confidence * 100).toFixed(1)}%
                        </p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-8">
                        <div class="card strategy-card h-100">
                            <div class="card-header bg-warning text-dark position-relative">
                                <h6 class="mb-0">精準預測組合</h6>
                                <span class="strategy-badge bg-success text-white">高信心度</span>
                            </div>
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    ${numbersHtml}
                                </div>
                                ${specialHtml ? `<div class="mb-2">${specialHtml}</div>` : ''}
                                <small class="text-muted">基於 ${prediction.strategy_analysis.base_strategies} 種策略共識</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card strategy-card h-100">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">風險評估</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>風險等級:</strong><br>${prediction.strategy_analysis.risk_assessment}</p>
                                <p><strong>投資成本:</strong><br>$${prediction.cost_comparison.single_bet}</p>
                                <p><strong>相較六次機會:</strong><br>${prediction.cost_comparison.cost_advantage}</p>
                                
                                <div class="mt-3">
                                    <button class="btn btn-sm btn-outline-primary" onclick="runBacktest('${data.lottery_info ? data.lottery_info.name : 'powercolor'}')">
                                        <i class="fas fa-chart-area"></i> 運行回測
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="analysis-section">
                            <h6><i class="fas fa-chart-bar"></i> 號碼頻率分析（前8名）</h6>
                            <div class="row">
            `;
            
            // 顯示頻率分析
            const freqEntries = Object.entries(prediction.strategy_analysis.number_frequency).slice(0, 8);
            freqEntries.forEach(([number, freq]) => {
                const isSelected = prediction.numbers.includes(parseInt(number));
                const badgeClass = isSelected ? 'bg-success' : 'bg-secondary';
                
                detailsHtml += `
                    <div class="col-md-3 mb-2">
                        <span class="badge ${badgeClass} me-2">${number}</span>
                        <small>出現 ${freq} 次</small>
                    </div>
                `;
            });
            
            detailsHtml += `
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            detailsDiv.innerHTML = detailsHtml;
            
            // 隱藏其他結果，顯示策略結果
            document.getElementById('probabilityResults').style.display = 'none';
            document.getElementById('coverageAnalysis').style.display = 'none';
            resultsDiv.style.display = 'block';
            
            // 滾動到結果
            resultsDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        // 策略比較
        async function compareStrategies() {
            const lotteryType = document.getElementById('lotteryType').value;
            const loadingOverlay = document.getElementById('loadingOverlay');
            const compareBtn = document.getElementById('compareBtn');
            
            try {
                // 顯示加載動畫
                loadingOverlay.style.display = 'flex';
                compareBtn.disabled = true;
                
                const response = await fetch(`/api/strategy_comparison/${lotteryType}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    displayStrategyComparison(result.data);
                } else {
                    alert(`策略比較失敗：${result.error}`);
                }
                
            } catch (error) {
                alert(`網絡錯誤：${error.message}`);
            } finally {
                loadingOverlay.style.display = 'none';
                compareBtn.disabled = false;
            }
        }
        
        // 顯示策略比較結果
        function displayStrategyComparison(data) {
            const resultsDiv = document.getElementById('probabilityResults');
            const contentDiv = document.getElementById('probabilityContent');
            
            let comparisonHtml = `
                <div class="row mb-4">
                    <div class="col-12">
                        <h5><i class="fas fa-balance-scale"></i> 策略比較分析</h5>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">${data.strategies.six_chances.name}</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-2">
                                    <strong>投資金額:</strong> $${data.strategies.six_chances.investment}
                                </div>
                                <div class="mb-2">
                                    <strong>投注組數:</strong> ${data.strategies.six_chances.combinations} 組
                                </div>
                                <div class="mb-2">
                                    <strong>覆蓋率:</strong> ${data.strategies.six_chances.coverage_rate}
                                </div>
                                <div class="mb-2">
                                    <strong>風險等級:</strong> ${data.strategies.six_chances.risk_level}
                                </div>
                                <div class="mb-2">
                                    <strong>適合對象:</strong> ${data.strategies.six_chances.target_audience}
                                </div>
                                
                                <h6 class="mt-3">優點:</h6>
                                <ul class="list-unstyled">
            `;
            
            data.strategies.six_chances.advantages.forEach(advantage => {
                comparisonHtml += `<li><i class="fas fa-check-circle text-success"></i> ${advantage}</li>`;
            });
            
            comparisonHtml += `
                                </ul>
                                <h6>缺點:</h6>
                                <ul class="list-unstyled">
            `;
            
            data.strategies.six_chances.disadvantages.forEach(disadvantage => {
                comparisonHtml += `<li><i class="fas fa-times-circle text-danger"></i> ${disadvantage}</li>`;
            });
            
            comparisonHtml += `
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">${data.strategies.highest_confidence.name}</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-2">
                                    <strong>投資金額:</strong> $${data.strategies.highest_confidence.investment}
                                </div>
                                <div class="mb-2">
                                    <strong>投注組數:</strong> ${data.strategies.highest_confidence.combinations} 組
                                </div>
                                <div class="mb-2">
                                    <strong>信心度:</strong> ${data.strategies.highest_confidence.confidence_score}
                                </div>
                                <div class="mb-2">
                                    <strong>風險等級:</strong> ${data.strategies.highest_confidence.risk_level}
                                </div>
                                <div class="mb-2">
                                    <strong>適合對象:</strong> ${data.strategies.highest_confidence.target_audience}
                                </div>
                                
                                <h6 class="mt-3">優點:</h6>
                                <ul class="list-unstyled">
            `;
            
            data.strategies.highest_confidence.advantages.forEach(advantage => {
                comparisonHtml += `<li><i class="fas fa-check-circle text-success"></i> ${advantage}</li>`;
            });
            
            comparisonHtml += `
                                </ul>
                                <h6>缺點:</h6>
                                <ul class="list-unstyled">
            `;
            
            data.strategies.highest_confidence.disadvantages.forEach(disadvantage => {
                comparisonHtml += `<li><i class="fas fa-times-circle text-danger"></i> ${disadvantage}</li>`;
            });
            
            comparisonHtml += `
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fas fa-lightbulb"></i> 投資建議</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <strong>保守型投資者:</strong><br>
                                        ${data.recommendations.conservative_investor}
                                    </div>
                                    <div class="col-md-4">
                                        <strong>積極型投資者:</strong><br>
                                        ${data.recommendations.aggressive_investor}
                                    </div>
                                    <div class="col-md-4">
                                        <strong>混合策略:</strong><br>
                                        ${data.recommendations.hybrid_approach}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <h6>成本效益比較</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>策略</th>
                                        <th>成本</th>
                                        <th>效益比</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>六次機會</td>
                                        <td>$${data.performance_comparison.cost_efficiency.six_chances_cost}</td>
                                        <td>基準</td>
                                    </tr>
                                    <tr class="table-success">
                                        <td>精準預測</td>
                                        <td>$${data.performance_comparison.cost_efficiency.single_confidence_cost}</td>
                                        <td><strong>${data.performance_comparison.cost_efficiency.efficiency_ratio}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>風險回報評估</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>策略</th>
                                        <th>風險等級</th>
                                        <th>預期ROI</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>六次機會</td>
                                        <td><span class="badge bg-success">低風險</span></td>
                                        <td>${data.performance_comparison.risk_return.expected_roi_six}</td>
                                    </tr>
                                    <tr>
                                        <td>精準預測</td>
                                        <td><span class="badge bg-warning">高風險</span></td>
                                        <td>${data.performance_comparison.risk_return.expected_roi_single}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
            
            contentDiv.innerHTML = comparisonHtml;
            
            // 隱藏策略結果，顯示比較分析
            document.getElementById('strategyResults').style.display = 'none';
            resultsDiv.style.display = 'block';
            
            // 滾動到結果
            resultsDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        // 運行回測
        async function runBacktest(lotteryType) {
            try {
                const periods = prompt('請輸入回測期數（建議10-50期）:', '20');
                if (!periods || isNaN(periods)) return;
                
                const response = await fetch(`/api/confidence_backtest/${lotteryType || 'powercolor'}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        periods: parseInt(periods)
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const perf = result.data.performance;
                    let message = `回測結果 (${periods}期):\n\n`;
                    message += `總投資: $${perf.total_cost}\n`;
                    message += `總獎金: $${perf.total_winnings}\n`;
                    message += `盈虧: ${perf.profit >= 0 ? '+' : ''}$${perf.profit}\n`;
                    message += `ROI: ${(perf.roi * 100).toFixed(1)}%\n`;
                    message += `命中率: ${perf.hit_rate}\n`;
                    message += `命中次數: ${perf.hit_count}次\n\n`;
                    
                    if (result.data.hit_details && result.data.hit_details.length > 0) {
                        const bestHit = perf.best_hit;
                        message += `最佳命中: 期號${bestHit.period}, 中${bestHit.main_matches}個號碼, 獎金$${bestHit.prize}`;
                    }
                    
                    alert(message);
                } else {
                    alert(`回測失敗：${result.error}`);
                }
                
            } catch (error) {
                alert(`回測錯誤：${error.message}`);
            }
        }
        
        // 頁面加載時的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 可以添加一些初始化邏輯
            console.log('六次機會策略頁面已加載');
        });
    </script>
</body>
</html>