<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>預測方法分析 - 調試版本</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.875rem;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>預測方法分析 - 調試版本</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5>分析設置</h5>
                        <div class="mb-3">
                            <label for="lotteryType" class="form-label">彩票類型</label>
                            <select class="form-select" id="lotteryType">
                                <option value="powercolor">威力彩</option>
                                <option value="lotto649">大樂透</option>
                                <option value="dailycash">今彩539</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="analysisRange" class="form-label">分析時間範圍</label>
                            <select class="form-select" id="analysisRange">
                                <option value="7">最近7天</option>
                                <option value="30" selected>最近30天</option>
                                <option value="90">最近90天</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" onclick="debugAnalysis()">
                            調試分析
                        </button>
                        <button class="btn btn-secondary ms-2" onclick="clearLog()">
                            清除日志
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5>調試信息</h5>
                        <div id="debugLog" class="debug-info" style="height: 300px; overflow-y: auto;">
                            <div class="info">等待開始調試...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5>API響應結果</h5>
                        <pre id="apiResponse" class="debug-info" style="height: 400px; overflow-y: auto;">
等待API響應...
                        </pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            debugLog.appendChild(logEntry);
            debugLog.scrollTop = debugLog.scrollHeight;
        }

        function clearLog() {
            document.getElementById('debugLog').innerHTML = '<div class="info">日志已清除</div>';
            document.getElementById('apiResponse').textContent = '等待API響應...';
        }

        async function debugAnalysis() {
            const lotteryType = document.getElementById('lotteryType').value;
            const days = document.getElementById('analysisRange').value;
            
            log('開始調試分析...', 'info');
            log(`彩票類型: ${lotteryType}`, 'info');
            log(`時間範圍: ${days}天`, 'info');
            
            const url = `/api/prediction_method_analysis/${lotteryType}?days=${days}`;
            log(`請求URL: ${url}`, 'info');
            
            try {
                log('發送HTTP請求...', 'info');
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });
                
                log(`HTTP狀態: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                log(`響應頭Content-Type: ${response.headers.get('content-type')}`, 'info');
                
                if (!response.ok) {
                    const errorText = await response.text();
                    log(`錯誤響應內容: ${errorText}`, 'error');
                    document.getElementById('apiResponse').textContent = errorText;
                    return;
                }
                
                log('解析JSON響應...', 'info');
                const data = await response.json();
                
                log(`API成功: ${data.success}`, data.success ? 'success' : 'error');
                if (data.error) {
                    log(`API錯誤: ${data.error}`, 'error');
                }
                if (data.message) {
                    log(`API消息: ${data.message}`, 'info');
                }
                
                document.getElementById('apiResponse').textContent = JSON.stringify(data, null, 2);
                
                if (data.success && data.data) {
                    log(`返回數據鍵: ${Object.keys(data.data).join(', ')}`, 'success');
                    log(`總預測數: ${data.data.total_predictions || 'N/A'}`, 'success');
                    log(`分析完成！`, 'success');
                } else {
                    log('API返回了錯誤或空數據', 'error');
                }
                
            } catch (error) {
                log(`請求失敗: ${error.message}`, 'error');
                log(`錯誤詳情: ${error.stack}`, 'error');
                document.getElementById('apiResponse').textContent = `錯誤: ${error.message}\n\n${error.stack}`;
            }
        }

        // 頁面載入時自動測試
        document.addEventListener('DOMContentLoaded', function() {
            log('頁面載入完成，準備進行調試', 'success');
        });
    </script>
</body>
</html>