<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>號碼深度分析 - 彩票預測系統</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .number-input {
            width: 60px;
            text-align: center;
            margin: 2px;
        }
        .analysis-card {
            border-radius: 10px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        .analysis-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .pattern-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 0.8em;
            margin: 2px;
        }
        .consecutive-badge {
            background-color: #e3f2fd;
            color: #1976d2;
        }
        .hot-number {
            background-color: #ffebee;
            color: #d32f2f;
        }
        .cold-number {
            background-color: #e8f5e8;
            color: #388e3c;
        }
        .warm-number {
            background-color: #fff3e0;
            color: #f57c00;
        }
        .reason-item {
            padding: 10px;
            margin: 5px 0;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
            border-radius: 0 5px 5px 0;
        }
        .math-formula {
            font-family: 'Courier New', monospace;
            background-color: #f1f3f4;
            padding: 2px 6px;
            border-radius: 3px;
        }
        .probability-bar {
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        .probability-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
            transition: width 0.3s ease;
        }
        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 9999;
        }
        .loading-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    {% include 'navbar.html' %}

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-search"></i> 號碼分析設定</h5>
                    </div>
                    <div class="card-body">
                        <form id="analysisForm">
                            <div class="mb-3">
                                <label for="lotteryType" class="form-label">彩票類型</label>
                                <select class="form-select" id="lotteryType" name="lottery_type" onchange="updateNumberInputs()">
                                    <option value="powercolor">威力彩</option>
                                    <option value="lotto649">大樂透</option>
                                    <option value="dailycash">今彩539</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">輸入號碼</label>
                                <div id="numberInputs">
                                    <!-- 動態生成號碼輸入框 -->
                                </div>
                                <small class="text-muted">請輸入要分析的號碼組合</small>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-microscope"></i> 開始分析
                            </button>
                        </form>
                        
                        <!-- 快速分析示例 -->
                        <div class="mt-4">
                            <h6>快速分析示例</h6>
                            <button class="btn btn-outline-secondary btn-sm w-100 mb-2" onclick="analyzeExample([1,2,3,4,5,6])">
                                分析 1,2,3,4,5,6 (連號)
                            </button>
                            <button class="btn btn-outline-secondary btn-sm w-100 mb-2" onclick="analyzeExample([7,14,21,28,35,42])">
                                分析 7,14,21,28,35,42 (等差)
                            </button>
                            <button class="btn btn-outline-secondary btn-sm w-100" onclick="analyzeExample([1,4,9,16,25,36])">
                                分析 1,4,9,16,25,36 (平方)
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div id="analysisResults">
                    <div class="text-center text-muted">
                        <i class="fas fa-microscope fa-3x mb-3"></i>
                        <h4>號碼深度分析</h4>
                        <p>輸入號碼組合，我們將分析其出現的可能原因：</p>
                        <ul class="list-unstyled">
                            <li>🔗 連號模式分析</li>
                            <li>📐 數學關係探討</li>
                            <li>📊 歷史出現統計</li>
                            <li>🔄 板路關聯分析</li>
                            <li>📈 機率偏差檢測</li>
                            <li>🎯 出現原因推論</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 載入覆蓋層 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">分析中...</span>
            </div>
            <h5>深度分析中...</h5>
            <p>正在分析號碼模式、歷史數據和板路關聯</p>
        </div>
    </div>

    <!-- 分析結果模板 -->
    <template id="analysisTemplate">
        <div class="analysis-results">
            <!-- 基本資訊 -->
            <div class="analysis-card">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-info-circle"></i> 分析概覽</h5>
                </div>
                <div class="card-body" id="basicInfo">
                    <!-- 動態填充 -->
                </div>
            </div>

            <!-- 連號模式分析 -->
            <div class="analysis-card">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-link"></i> 連號模式分析</h5>
                </div>
                <div class="card-body" id="consecutiveAnalysis">
                    <!-- 動態填充 -->
                </div>
            </div>

            <!-- 數學關係分析 -->
            <div class="analysis-card">
                <div class="card-header bg-warning text-dark">
                    <h5><i class="fas fa-calculator"></i> 數學關係分析</h5>
                </div>
                <div class="card-body" id="mathematicalAnalysis">
                    <!-- 動態填充 -->
                </div>
            </div>

            <!-- 歷史出現分析 -->
            <div class="analysis-card">
                <div class="card-header bg-secondary text-white">
                    <h5><i class="fas fa-history"></i> 歷史出現分析</h5>
                </div>
                <div class="card-body" id="historicalAnalysis">
                    <!-- 動態填充 -->
                </div>
            </div>

            <!-- 板路關聯分析 -->
            <div class="analysis-card">
                <div class="card-header bg-dark text-white">
                    <h5><i class="fas fa-project-diagram"></i> 板路關聯分析</h5>
                </div>
                <div class="card-body" id="boardPathAnalysis">
                    <!-- 動態填充 -->
                </div>
            </div>

            <!-- 機率分析 -->
            <div class="analysis-card">
                <div class="card-header bg-danger text-white">
                    <h5><i class="fas fa-chart-bar"></i> 機率分析</h5>
                </div>
                <div class="card-body" id="probabilityAnalysis">
                    <!-- 動態填充 -->
                </div>
            </div>

            <!-- 出現原因推論 -->
            <div class="analysis-card">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-lightbulb"></i> 出現原因推論</h5>
                </div>
                <div class="card-body" id="reasonsAnalysis">
                    <!-- 動態填充 -->
                </div>
            </div>
        </div>
    </template>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 更新號碼輸入框
        function updateNumberInputs() {
            const lotteryType = document.getElementById('lotteryType').value;
            const container = document.getElementById('numberInputs');
            
            let count = 6; // 預設6個號碼
            if (lotteryType === 'dailycash') {
                count = 5;
            }
            
            let html = '';
            for (let i = 1; i <= count; i++) {
                html += `<input type="number" class="form-control number-input d-inline-block" 
                         id="num${i}" min="1" max="49" placeholder="${i}">`;
            }
            
            container.innerHTML = html;
        }

        // 分析示例
        function analyzeExample(numbers) {
            const lotteryType = document.getElementById('lotteryType').value;
            
            // 填入示例號碼
            for (let i = 0; i < numbers.length; i++) {
                const input = document.getElementById(`num${i + 1}`);
                if (input) {
                    input.value = numbers[i];
                }
            }
            
            // 執行分析
            analyzeNumbers(numbers);
        }

        // 執行號碼分析
        async function analyzeNumbers(numbers = null) {
            const lotteryType = document.getElementById('lotteryType').value;
            
            // 如果沒有提供號碼，從輸入框獲取
            if (!numbers) {
                numbers = [];
                const inputs = document.querySelectorAll('.number-input');
                for (let input of inputs) {
                    if (input.value) {
                        numbers.push(parseInt(input.value));
                    }
                }
            }
            
            if (numbers.length === 0) {
                alert('請輸入要分析的號碼');
                return;
            }
            
            // 顯示載入狀態
            document.getElementById('loadingOverlay').style.display = 'block';
            
            try {
                const response = await fetch('/api/analyze_numbers', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        lottery_type: lotteryType,
                        numbers: numbers
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    displayAnalysisResults(result.analysis);
                } else {
                    document.getElementById('analysisResults').innerHTML = 
                        `<div class="alert alert-danger">分析失敗: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('analysisResults').innerHTML = 
                    `<div class="alert alert-danger">網路錯誤: ${error.message}</div>`;
            } finally {
                document.getElementById('loadingOverlay').style.display = 'none';
            }
        }

        // 顯示分析結果
        function displayAnalysisResults(analysis) {
            const template = document.getElementById('analysisTemplate');
            const clone = template.content.cloneNode(true);
            
            // 填充基本資訊
            const basicInfo = clone.getElementById('basicInfo');
            basicInfo.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>分析號碼:</strong> ${analysis.target_numbers.join(', ')}</p>
                        <p><strong>彩票類型:</strong> ${getLotteryName(analysis.lottery_type)}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>分析時間:</strong> ${analysis.analysis_time}</p>
                        <p><strong>號碼數量:</strong> ${analysis.target_numbers.length} 個</p>
                    </div>
                </div>
            `;
            
            // 填充連號分析
            const consecutive = analysis.pattern_analysis.consecutive;
            const consecutiveDiv = clone.getElementById('consecutiveAnalysis');
            let consecutiveHtml = '';
            
            if (consecutive.has_consecutive) {
                consecutiveHtml += `<div class="alert alert-success">
                    <strong>發現連號模式！</strong><br>
                    最長連續: ${consecutive.max_consecutive_length} 個號碼<br>
                    連續組合: ${consecutive.consecutive_groups.map(group => group.join('-')).join(', ')}<br>
                    連號比例: ${consecutive.consecutive_percentage.toFixed(1)}%
                </div>`;
                
                if (consecutive.special_pattern) {
                    consecutiveHtml += `<div class="alert alert-warning">
                        <strong>特殊模式:</strong> ${consecutive.special_pattern}<br>
                        <small>${consecutive.pattern_significance}</small>
                    </div>`;
                }
            } else {
                consecutiveHtml = '<div class="alert alert-info">未發現明顯的連號模式</div>';
            }
            
            consecutiveDiv.innerHTML = consecutiveHtml;
            
            // 填充數學關係分析
            const math = analysis.mathematical_relationships;
            const mathDiv = clone.getElementById('mathematicalAnalysis');
            let mathHtml = '';
            
            // 數字和分析
            mathHtml += `<div class="mb-3">
                <h6>數字和分析</h6>
                <p>總和: <span class="math-formula">${math.sum_analysis.total_sum}</span> 
                   平均: <span class="math-formula">${math.sum_analysis.average.toFixed(1)}</span>
                   類別: <span class="pattern-badge">${math.sum_analysis.sum_category}</span></p>
            </div>`;
            
            // 等差數列
            if (math.arithmetic_sequences.length > 0) {
                mathHtml += '<div class="mb-3"><h6>等差數列</h6>';
                math.arithmetic_sequences.forEach(seq => {
                    mathHtml += `<p>數列: ${seq.numbers.join(' → ')} (公差: ${seq.common_difference})</p>`;
                });
                mathHtml += '</div>';
            }
            
            // 特殊關係
            if (math.special_relationships.length > 0) {
                mathHtml += '<div class="mb-3"><h6>特殊關係</h6><ul>';
                math.special_relationships.forEach(rel => {
                    mathHtml += `<li>${rel}</li>`;
                });
                mathHtml += '</ul></div>';
            }
            
            mathDiv.innerHTML = mathHtml;
            
            // 填充歷史分析
            const historical = analysis.historical_context;
            const historicalDiv = clone.getElementById('historicalAnalysis');
            let historicalHtml = '<div class="row">';
            
            // 個別號碼頻率
            historicalHtml += '<div class="col-md-6"><h6>號碼頻率</h6>';
            for (let [num, data] of Object.entries(historical.individual_frequencies)) {
                const hotColdClass = historical.cold_hot_analysis[num] === '熱號' ? 'hot-number' : 
                                   historical.cold_hot_analysis[num] === '冷號' ? 'cold-number' : 'warm-number';
                historicalHtml += `
                    <div class="mb-2">
                        <span class="pattern-badge ${hotColdClass}">${num}</span>
                        出現 ${data.frequency} 次 (${data.percentage.toFixed(1)}%)
                        <span class="small text-muted">${historical.cold_hot_analysis[num]}</span>
                    </div>
                `;
            }
            historicalHtml += '</div>';
            
            // 最近出現
            historicalHtml += '<div class="col-md-6"><h6>最近出現</h6>';
            for (let [num, period] of Object.entries(historical.recent_appearances)) {
                historicalHtml += `<p>號碼 ${num}: ${period || '未在近期出現'}</p>`;
            }
            historicalHtml += '</div></div>';
            
            historicalDiv.innerHTML = historicalHtml;
            
            // 填充板路分析
            const boardPath = analysis.board_path_connections;
            const boardPathDiv = clone.getElementById('boardPathAnalysis');
            let boardPathHtml = '';
            
            if (boardPath.follow_patterns.length > 0) {
                boardPathHtml += '<div class="mb-3"><h6>跟隨模式</h6>';
                boardPath.follow_patterns.slice(0, 5).forEach(pattern => {
                    boardPathHtml += `
                        <div class="reason-item">
                            號碼 ${pattern.previous_number} 出現後，號碼 ${pattern.target_number} 
                            跟隨出現 ${pattern.correlation_count} 次 
                            (關聯率: ${(pattern.correlation_rate * 100).toFixed(1)}%)
                        </div>
                    `;
                });
                boardPathHtml += '</div>';
            }
            
            if (boardPath.skip_patterns.length > 0) {
                boardPathHtml += '<div class="mb-3"><h6>跳號模式</h6>';
                boardPath.skip_patterns.forEach(pattern => {
                    boardPathHtml += `
                        <div class="reason-item">
                            號碼 ${pattern.number} 常間隔 ${pattern.common_interval} 期出現 
                            (頻率: ${pattern.frequency} 次)
                        </div>
                    `;
                });
                boardPathHtml += '</div>';
            }
            
            if (!boardPathHtml) {
                boardPathHtml = '<div class="alert alert-info">未發現明顯的板路關聯模式</div>';
            }
            
            boardPathDiv.innerHTML = boardPathHtml;
            
            // 填充機率分析
            const probability = analysis.probability_analysis;
            const probabilityDiv = clone.getElementById('probabilityAnalysis');
            let probabilityHtml = '';
            
            probabilityHtml += `<div class="mb-3">
                <h6>理論機率</h6>
                <p>單一號碼理論出現機率: ${(probability.theoretical_probability.percentage).toFixed(3)}%</p>
            </div>`;
            
            probabilityHtml += '<div class="mb-3"><h6>觀察機率 vs 理論機率</h6>';
            for (let [num, data] of Object.entries(probability.observed_probability)) {
                const deviation = data.deviation_percentage;
                const deviationClass = Math.abs(deviation) > 20 ? 'text-danger' : 
                                     Math.abs(deviation) > 10 ? 'text-warning' : 'text-success';
                
                probabilityHtml += `
                    <div class="mb-2">
                        <div class="d-flex justify-content-between">
                            <span>號碼 ${num}</span>
                            <span class="${deviationClass}">
                                ${data.percentage.toFixed(2)}% 
                                (${deviation > 0 ? '+' : ''}${deviation.toFixed(1)}%)
                            </span>
                        </div>
                        <div class="probability-bar">
                            <div class="probability-fill" style="width: ${Math.min(data.percentage * 10, 100)}%"></div>
                        </div>
                    </div>
                `;
            }
            probabilityHtml += '</div>';
            
            probabilityDiv.innerHTML = probabilityHtml;
            
            // 填充原因推論
            const reasonsDiv = clone.getElementById('reasonsAnalysis');
            let reasonsHtml = '';
            
            if (analysis.appearance_reasons.length > 0) {
                analysis.appearance_reasons.forEach(reason => {
                    reasonsHtml += `<div class="reason-item">${reason}</div>`;
                });
            } else {
                reasonsHtml = '<div class="alert alert-info">未找到明確的出現原因</div>';
            }
            
            reasonsDiv.innerHTML = reasonsHtml;
            
            // 替換結果容器
            document.getElementById('analysisResults').innerHTML = '';
            document.getElementById('analysisResults').appendChild(clone);
        }

        function getLotteryName(type) {
            const names = {
                'powercolor': '威力彩',
                'lotto649': '大樂透',
                'dailycash': '今彩539'
            };
            return names[type] || type;
        }

        // 表單提交處理
        document.getElementById('analysisForm').addEventListener('submit', function(e) {
            e.preventDefault();
            analyzeNumbers();
        });

        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateNumberInputs();

            // 檢查是否有從其他頁面傳來的分析請求
            const storedNumbers = localStorage.getItem('analyzeNumbers');
            const storedLotteryType = localStorage.getItem('analyzeLotteryType');

            if (storedNumbers && storedLotteryType) {
                try {
                    const numbers = JSON.parse(storedNumbers);

                    // 設定彩票類型
                    document.getElementById('lotteryType').value = storedLotteryType;
                    updateNumberInputs();

                    // 填入號碼
                    setTimeout(() => {
                        for (let i = 0; i < numbers.length; i++) {
                            const input = document.getElementById(`num${i + 1}`);
                            if (input) {
                                input.value = numbers[i];
                            }
                        }

                        // 自動執行分析
                        analyzeNumbers(numbers);

                        // 清除localStorage
                        localStorage.removeItem('analyzeNumbers');
                        localStorage.removeItem('analyzeLotteryType');
                    }, 100);

                } catch (error) {
                    console.error('解析儲存的號碼時出錯:', error);
                }
            }
        });
    </script>
</body>
</html>
