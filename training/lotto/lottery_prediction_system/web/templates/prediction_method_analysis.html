<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>預測方法分析 - 彩票預測系統</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .method-card {
            border-left: 4px solid #007bff;
            transition: transform 0.2s;
        }
        .method-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .category-statistical { border-left-color: #28a745; }
        .category-ai_ml { border-left-color: #007bff; }
        .category-traditional { border-left-color: #ffc107; }
        .category-combined { border-left-color: #dc3545; }
        .category-other { border-left-color: #6c757d; }
        
        .confidence-badge {
            font-size: 0.9em;
            padding: 0.3em 0.6em;
        }
        .complexity-simple { background-color: #d4edda; color: #155724; }
        .complexity-medium { background-color: #fff3cd; color: #856404; }
        .complexity-complex { background-color: #f8d7da; color: #721c24; }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    {% include 'navbar.html' %}
    
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-chart-line me-2"></i>預測方法分析
                </h1>
            </div>
        </div>
        
        <!-- 控制面板 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">分析設置</h5>
                        <div class="mb-3">
                            <label for="lotteryType" class="form-label">彩票類型</label>
                            <select class="form-select" id="lotteryType">
                                <option value="powercolor">威力彩</option>
                                <option value="lotto649">大樂透</option>
                                <option value="dailycash">今彩539</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="analysisRange" class="form-label">分析時間範圍</label>
                            <select class="form-select" id="analysisRange">
                                <option value="7">最近7天</option>
                                <option value="30" selected>最近30天</option>
                                <option value="90">最近90天</option>
                                <option value="365">最近一年</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" onclick="loadAnalysis()">
                            <i class="fas fa-chart-bar me-2"></i>開始分析
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="stats-card">
                    <h5><i class="fas fa-info-circle me-2"></i>分析概覽</h5>
                    <div id="overviewStats">
                        <p class="text-muted">請選擇分析參數並點擊開始分析</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 載入指示器 -->
        <div id="loadingIndicator" class="text-center my-5" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">分析中...</span>
            </div>
            <p class="mt-2">正在分析預測方法效果...</p>
        </div>
        
        <!-- 分析結果 -->
        <div id="analysisResults" style="display: none;">
            <!-- 方法分類統計 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-pie-chart me-2"></i>預測方法分類分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="categoryChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-bar-chart me-2"></i>各方法信心度對比</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="confidenceChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 方法詳細信息 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list me-2"></i>預測方法詳細分析</h5>
                        </div>
                        <div class="card-body">
                            <div id="methodDetails">
                                <!-- 動態載入方法詳細信息 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 時間趨勢 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-line me-2"></i>預測趨勢分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container" style="height: 400px;">
                                <canvas id="trendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 方法分類說明 -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-question-circle me-2"></i>預測方法分類說明</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-success">統計分析類 (Statistical)</h6>
                                <p class="small">基於歷史數據統計規律的預測方法，包括頻率分析、趨勢分析等。</p>
                                
                                <h6 class="text-primary">人工智能類 (AI/ML)</h6>
                                <p class="small">使用機器學習、神經網路等人工智能技術的預測方法。</p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-warning">傳統分析類 (Traditional)</h6>
                                <p class="small">傳統彩票分析方法，包括板路分析、冷熱號分析等。</p>
                                
                                <h6 class="text-danger">組合方法類 (Combined)</h6>
                                <p class="small">結合多種方法的綜合預測，通常具有更高的準確性。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let categoryChart, confidenceChart, trendChart;

        async function loadAnalysis() {
            const lotteryType = document.getElementById('lotteryType').value;
            const days = document.getElementById('analysisRange').value;
            
            // 顯示載入指示器
            document.getElementById('loadingIndicator').style.display = 'block';
            document.getElementById('analysisResults').style.display = 'none';
            
            try {
                console.log(`發送分析請求: /api/prediction_method_analysis/${lotteryType}?days=${days}`);
                
                const response = await fetch(`/api/prediction_method_analysis/${lotteryType}?days=${days}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });
                
                console.log('API響應狀態:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP錯誤: ${response.status} ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('API響應數據:', data);
                
                if (data.success) {
                    displayAnalysisResults(data.data);
                } else {
                    console.error('API返回錯誤:', data.error);
                    alert('分析失敗: ' + (data.error || '未知錯誤'));
                }
            } catch (error) {
                console.error('分析請求失敗詳情:', error);
                alert('分析請求失敗: ' + error.message + '，請稍後重試');
            } finally {
                document.getElementById('loadingIndicator').style.display = 'none';
            }
        }

        function displayAnalysisResults(analysisData) {
            // 更新概覽統計
            updateOverviewStats(analysisData);
            
            // 創建圖表
            createCategoryChart(analysisData);
            createConfidenceChart(analysisData);
            createTrendChart(analysisData);
            
            // 顯示方法詳細信息
            displayMethodDetails(analysisData);
            
            // 顯示結果區域
            document.getElementById('analysisResults').style.display = 'block';
        }

        function updateOverviewStats(data) {
            const stats = `
                <div class="row text-center">
                    <div class="col-4">
                        <h4 class="text-primary">${data.total_predictions || 0}</h4>
                        <small>總預測次數</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-success">${Object.keys(data.category_analysis?.category_summary || {}).length}</h4>
                        <small>使用方法類別</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-info">${data.analysis_period || 'N/A'}</h4>
                        <small>分析時間範圍</small>
                    </div>
                </div>
            `;
            document.getElementById('overviewStats').innerHTML = stats;
        }

        function createCategoryChart(data) {
            const ctx = document.getElementById('categoryChart').getContext('2d');
            
            if (categoryChart) {
                categoryChart.destroy();
            }
            
            const categoryData = data.category_analysis?.category_summary || {};
            const labels = Object.keys(categoryData);
            const counts = labels.map(label => categoryData[label].count);
            
            categoryChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels.map(label => getCategoryDisplayName(label)),
                    datasets: [{
                        data: counts,
                        backgroundColor: [
                            '#28a745', '#007bff', '#ffc107', '#dc3545', '#6c757d'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function createConfidenceChart(data) {
            const ctx = document.getElementById('confidenceChart').getContext('2d');
            
            if (confidenceChart) {
                confidenceChart.destroy();
            }
            
            const accuracyData = data.accuracy_metrics || {};
            const methods = Object.keys(accuracyData);
            const confidences = methods.map(method => accuracyData[method].avg_confidence || 0);
            
            confidenceChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: methods,
                    datasets: [{
                        label: '平均信心度',
                        data: confidences,
                        backgroundColor: 'rgba(0, 123, 255, 0.7)',
                        borderColor: 'rgba(0, 123, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 1
                        }
                    }
                }
            });
        }

        function createTrendChart(data) {
            const ctx = document.getElementById('trendChart').getContext('2d');
            
            if (trendChart) {
                trendChart.destroy();
            }
            
            const trendData = data.time_trends || {};
            const dates = trendData.dates || [];
            const counts = trendData.daily_prediction_count || [];
            const avgConfidences = trendData.daily_avg_confidence || [];
            
            trendChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [{
                        label: '每日預測數量',
                        data: counts,
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        yAxisID: 'y'
                    }, {
                        label: '平均信心度',
                        data: avgConfidences,
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '日期'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '預測數量'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '信心度'
                            },
                            min: 0,
                            max: 1,
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }

        function displayMethodDetails(data) {
            const accuracyData = data.accuracy_metrics || {};
            const categoryData = data.category_analysis?.category_summary || {};
            
            let html = '<div class="row">';
            
            Object.keys(accuracyData).forEach(method => {
                const methodInfo = accuracyData[method];
                const category = getMethodCategory(method, categoryData);
                
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card method-card category-${category}">
                            <div class="card-body">
                                <h6 class="card-title">${method}</h6>
                                <div class="mb-2">
                                    <span class="badge confidence-badge" style="background-color: ${getConfidenceColor(methodInfo.avg_confidence)}">
                                        信心度: ${(methodInfo.avg_confidence * 100).toFixed(1)}%
                                    </span>
                                </div>
                                <p class="card-text small">
                                    <strong>預測次數:</strong> ${methodInfo.total_predictions}<br>
                                    <strong>信心度範圍:</strong> ${(methodInfo.confidence_range[0] * 100).toFixed(1)}% - ${(methodInfo.confidence_range[1] * 100).toFixed(1)}%<br>
                                    <strong>標準差:</strong> ${(methodInfo.confidence_std * 100).toFixed(2)}%
                                </p>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            document.getElementById('methodDetails').innerHTML = html;
        }

        function getCategoryDisplayName(category) {
            const names = {
                'statistical': '統計分析',
                'ai_ml': '人工智能',
                'traditional': '傳統分析',
                'combined': '組合方法',
                'other': '其他'
            };
            return names[category] || category;
        }

        function getMethodCategory(method, categoryData) {
            for (const [category, info] of Object.entries(categoryData)) {
                if (info.methods.includes(method)) {
                    return category;
                }
            }
            return 'other';
        }

        function getConfidenceColor(confidence) {
            if (confidence >= 0.8) return '#28a745';
            if (confidence >= 0.7) return '#007bff';
            if (confidence >= 0.6) return '#ffc107';
            return '#dc3545';
        }

        // 頁面載入時自動執行分析
        document.addEventListener('DOMContentLoaded', function() {
            loadAnalysis();
        });
    </script>
</body>
</html>