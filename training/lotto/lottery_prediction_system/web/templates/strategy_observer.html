<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>策略觀察表 - 彩票預測系統</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .controls {
            background: #f8f9fa;
            padding: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .control-group label {
            font-weight: 600;
            color: #495057;
        }
        
        select, button {
            padding: 10px 15px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 600;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-generate {
            background: #28a745;
        }
        
        .btn-generate:hover {
            background: #218838;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .content {
            padding: 30px;
        }
        
        .strategy-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }
        
        .strategy-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .strategy-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .strategy-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            position: relative;
        }
        
        .strategy-header.rank-1 { background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%); color: #333; }
        .strategy-header.rank-2 { background: linear-gradient(135deg, #c0c0c0 0%, #e2e2e2 100%); color: #333; }
        .strategy-header.rank-3 { background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%); }
        
        .strategy-name {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .strategy-subtitle {
            opacity: 0.8;
            font-size: 0.9rem;
        }
        
        .rank-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255,255,255,0.2);
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .strategy-body {
            padding: 20px;
        }
        
        .metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .metric-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .metric-label {
            color: #6c757d;
            font-weight: 500;
        }
        
        .metric-value {
            font-weight: 700;
            color: #495057;
        }
        
        .accuracy-high { color: #28a745; }
        .accuracy-medium { color: #ffc107; }
        .accuracy-low { color: #dc3545; }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state h3 {
            margin-bottom: 10px;
            color: #495057;
        }
        
        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .control-group {
                flex-direction: column;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .strategy-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 策略觀察表</h1>
            <p>AI預測策略表現追蹤與分析平台</p>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label for="lotterySelect">彩票類型:</label>
                <select id="lotterySelect">
                    <option value="lotto649">大樂透</option>
                    <option value="powercolor">威力彩</option>
                    <option value="dailycash">今彩539</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="periodInput">目標期號:</label>
                <input type="text" id="periodInput" placeholder="例: 114000082" style="padding: 10px; border: 2px solid #dee2e6; border-radius: 8px;">
            </div>
            
            <button class="btn btn-generate" onclick="generatePredictions()">
                🤖 生成AI預測 (6組)
            </button>
            
            <button class="btn" onclick="refreshData()">
                🔄 刷新數據
            </button>
        </div>
        
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-number" id="totalStrategies">0</div>
                <div class="stat-label">策略總數</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalPredictions">0</div>
                <div class="stat-label">預測總數</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="bestAccuracy">0%</div>
                <div class="stat-label">最佳準確度</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgHitRate">0%</div>
                <div class="stat-label">平均命中率</div>
            </div>
        </div>
        
        <div class="content">
            <h2>📊 策略表現排行</h2>
            <div id="strategyGrid" class="strategy-grid">
                <div class="loading">
                    <p>🔄 載入策略數據中...</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let currentLotteryType = 'lotto649';
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            
            // 監聽彩票類型變更
            document.getElementById('lotterySelect').addEventListener('change', function() {
                currentLotteryType = this.value;
                refreshData();
            });
        });
        
        // 刷新數據
        async function refreshData() {
            try {
                showLoading();
                const response = await fetch(`/api/strategy/comparison?lottery_type=${currentLotteryType}`);
                const result = await response.json();
                
                if (result.success) {
                    updateStats(result.data);
                    renderStrategyCards(result.data.strategies);
                } else {
                    showError('獲取數據失敗: ' + result.error);
                }
            } catch (error) {
                showError('網路錯誤: ' + error.message);
            }
        }
        
        // 生成AI預測
        async function generatePredictions() {
            const periodInput = document.getElementById('periodInput');
            const targetPeriod = periodInput.value.trim();
            
            if (!targetPeriod) {
                alert('請輸入目標期號');
                return;
            }
            
            try {
                const response = await fetch('/api/strategy/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        lottery_type: currentLotteryType,
                        target_period: targetPeriod
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('✅ 成功生成6組AI預測！');
                    refreshData();
                } else {
                    alert('❌ 生成失敗: ' + result.error);
                }
            } catch (error) {
                alert('❌ 網路錯誤: ' + error.message);
            }
        }
        
        // 更新統計數據
        function updateStats(data) {
            document.getElementById('totalStrategies').textContent = data.strategies.length;
            document.getElementById('totalPredictions').textContent = data.total_predictions;
            
            if (data.strategies.length > 0) {
                const bestAccuracy = Math.max(...data.strategies.map(s => s.avg_accuracy));
                const avgHitRate = data.strategies.reduce((sum, s) => sum + s.hit_rate, 0) / data.strategies.length;
                
                document.getElementById('bestAccuracy').textContent = bestAccuracy.toFixed(1) + '%';
                document.getElementById('avgHitRate').textContent = avgHitRate.toFixed(1) + '%';
            }
        }
        
        // 渲染策略卡片
        function renderStrategyCards(strategies) {
            const grid = document.getElementById('strategyGrid');
            
            if (strategies.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <h3>📝 暫無策略數據</h3>
                        <p>點擊「生成AI預測」開始創建策略記錄</p>
                    </div>
                `;
                return;
            }
            
            const cardsHTML = strategies.map(strategy => {
                const accuracyClass = getAccuracyClass(strategy.avg_accuracy);
                const rankClass = `rank-${strategy.rank}`;
                
                return `
                    <div class="strategy-card">
                        <div class="strategy-header ${strategy.rank <= 3 ? rankClass : ''}">
                            <div class="strategy-name">${strategy.name}</div>
                            <div class="strategy-subtitle">AI智能預測策略</div>
                            <div class="rank-badge">排名 #${strategy.rank}</div>
                        </div>
                        <div class="strategy-body">
                            <div class="metric-row">
                                <span class="metric-label">預測次數</span>
                                <span class="metric-value">${strategy.predictions} 次</span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">命中率</span>
                                <span class="metric-value">${strategy.hit_rate.toFixed(1)}%</span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">平均準確度</span>
                                <span class="metric-value ${accuracyClass}">${strategy.avg_accuracy.toFixed(1)}%</span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">最佳表現</span>
                                <span class="metric-value">${strategy.best_accuracy.toFixed(1)}%</span>
                            </div>
                            <div class="metric-row">
                                <span class="metric-label">平均命中數</span>
                                <span class="metric-value">${strategy.avg_hit_count.toFixed(1)} 個</span>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
            
            grid.innerHTML = cardsHTML;
        }
        
        // 獲取準確度樣式類
        function getAccuracyClass(accuracy) {
            if (accuracy >= 20) return 'accuracy-high';
            if (accuracy >= 10) return 'accuracy-medium';
            return 'accuracy-low';
        }
        
        // 顯示載入狀態
        function showLoading() {
            document.getElementById('strategyGrid').innerHTML = `
                <div class="loading">
                    <p>🔄 載入策略數據中...</p>
                </div>
            `;
        }
        
        // 顯示錯誤
        function showError(message) {
            document.getElementById('strategyGrid').innerHTML = `
                <div class="empty-state">
                    <h3>❌ 載入失敗</h3>
                    <p>${message}</p>
                </div>
            `;
        }
    </script>
</body>
</html>