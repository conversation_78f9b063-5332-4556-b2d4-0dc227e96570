<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分離式預測 - 彩票預測系統</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .prediction-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        .prediction-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .ml-card {
            border-color: #007bff;
        }
        .board-path-card {
            border-color: #28a745;
        }
        .confidence-high {
            color: #28a745;
            font-weight: bold;
        }
        .confidence-medium {
            color: #ffc107;
            font-weight: bold;
        }
        .confidence-low {
            color: #dc3545;
            font-weight: bold;
        }
        .number-ball {
            display: inline-block;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            text-align: center;
            line-height: 35px;
            margin: 2px;
            font-weight: bold;
        }
        .special-ball {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }
        .loading-spinner {
            display: none;
        }
    </style>
</head>
<body>
    {% include 'navbar.html' %}

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-cogs"></i> 預測設定</h5>
                    </div>
                    <div class="card-body">
                        <form id="predictionForm">
                            <div class="mb-3">
                                <label for="lotteryType" class="form-label">彩票類型</label>
                                <select class="form-select" id="lotteryType" name="lottery_type">
                                    <option value="powercolor">威力彩</option>
                                    <option value="lotto649">大樂透</option>
                                    <option value="dailycash">今彩539</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="candidatesCount" class="form-label">候選數量</label>
                                <input type="range" class="form-range" id="candidatesCount" name="candidates_count" 
                                       min="1" max="10" value="5" oninput="updateCandidatesValue(this.value)">
                                <div class="text-center">
                                    <span id="candidatesValue">5</span> 個候選
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="minConfidence" class="form-label">最低信心分數</label>
                                <input type="range" class="form-range" id="minConfidence" name="min_confidence" 
                                       min="0.1" max="1.0" step="0.1" value="0.5" oninput="updateConfidenceValue(this.value)">
                                <div class="text-center">
                                    <span id="confidenceValue">0.5</span>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-magic"></i> 開始預測
                            </button>
                        </form>
                        
                        <div class="loading-spinner text-center mt-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">預測中...</span>
                            </div>
                            <div class="mt-2">預測中，請稍候...</div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="card mt-3">
                    <div class="card-header bg-success text-white">
                        <h6><i class="fas fa-bolt"></i> 快速操作</h6>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-outline-success btn-sm w-100 mb-2" onclick="runDailyAutomation('update')">
                            <i class="fas fa-download"></i> 更新開獎結果
                        </button>
                        <button class="btn btn-outline-info btn-sm w-100 mb-2" onclick="runDailyAutomation('analysis')">
                            <i class="fas fa-chart-bar"></i> 分析準確度
                        </button>
                        <button class="btn btn-outline-warning btn-sm w-100" onclick="optimizeStrategy()">
                            <i class="fas fa-cog"></i> 策略優化
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div id="resultsContainer">
                    <div class="text-center text-muted">
                        <i class="fas fa-magic fa-3x mb-3"></i>
                        <h4>選擇設定並開始預測</h4>
                        <p>分離式預測將同時運行機器學習和板路分析，提供獨立的預測結果和成功原因分析。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 結果模板 -->
    <template id="resultsTemplate">
        <div class="prediction-results">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> 預測資訊</h5>
                        <p><strong>彩票類型:</strong> <span id="resultLotteryType"></span></p>
                        <p><strong>預測期數:</strong> <span id="resultPeriod"></span></p>
                        <p><strong>預測時間:</strong> <span id="resultTime"></span></p>
                    </div>
                </div>
            </div>

            <!-- 機器學習預測 -->
            <div class="prediction-card ml-card">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-robot"></i> 機器學習預測</h5>
                </div>
                <div class="card-body" id="mlResults">
                    <!-- 動態填充 -->
                </div>
            </div>

            <!-- 板路分析預測 -->
            <div class="prediction-card board-path-card">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-chart-line"></i> 板路分析預測</h5>
                </div>
                <div class="card-body" id="boardPathResults">
                    <!-- 動態填充 -->
                </div>
            </div>

            <!-- 成功分析 -->
            <div class="prediction-card">
                <div class="card-header bg-warning text-dark">
                    <h5><i class="fas fa-lightbulb"></i> 成功分析</h5>
                </div>
                <div class="card-body" id="successAnalysis">
                    <!-- 動態填充 -->
                </div>
            </div>

            <!-- 比較摘要 -->
            <div class="prediction-card">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-balance-scale"></i> 預測方法比較</h5>
                </div>
                <div class="card-body" id="comparisonSummary">
                    <!-- 動態填充 -->
                </div>
            </div>
        </div>
    </template>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function updateCandidatesValue(value) {
            document.getElementById('candidatesValue').textContent = value;
        }

        function updateConfidenceValue(value) {
            document.getElementById('confidenceValue').textContent = value;
        }

        function getConfidenceClass(confidence) {
            if (confidence >= 0.8) return 'confidence-high';
            if (confidence >= 0.6) return 'confidence-medium';
            return 'confidence-low';
        }

        function getConfidenceIcon(confidence) {
            if (confidence >= 0.8) return '⭐';
            if (confidence >= 0.6) return '🔸';
            return '🔹';
        }

        function formatNumbers(numbers, isSpecial = false) {
            if (!numbers || numbers.length === 0) return '';

            return numbers.map(num =>
                `<span class="number-ball ${isSpecial ? 'special-ball' : ''}">${num}</span>`
            ).join('');
        }

        function displayBestPredictions(clone, bestPredictions, lotteryType) {
            let bestHtml = '<div class="mt-4"><h5 class="text-success"><i class="fas fa-star"></i> 最佳整合預測</h5>';

            if (bestPredictions.ml_best) {
                bestHtml += '<div class="card mb-3 border-success">';
                bestHtml += '<div class="card-header bg-success text-white">';
                bestHtml += '<h6 class="mb-0"><i class="fas fa-robot"></i> 機器學習最佳預測</h6>';
                bestHtml += '</div>';
                bestHtml += '<div class="card-body">';
                bestHtml += formatBestPrediction(bestPredictions.ml_best, lotteryType);
                bestHtml += '</div></div>';
            }

            if (bestPredictions.board_path_best) {
                bestHtml += '<div class="card mb-3 border-info">';
                bestHtml += '<div class="card-header bg-info text-white">';
                bestHtml += '<h6 class="mb-0"><i class="fas fa-chart-line"></i> 板路分析最佳預測</h6>';
                bestHtml += '</div>';
                bestHtml += '<div class="card-body">';
                bestHtml += formatBestPrediction(bestPredictions.board_path_best, lotteryType);
                bestHtml += '</div></div>';
            }

            bestHtml += '</div>';

            // 在機器學習預測之前插入最佳預測
            const mlSection = clone.querySelector('#mlPredictions');
            if (mlSection) {
                mlSection.insertAdjacentHTML('beforebegin', bestHtml);
            }
        }

        function formatBestPrediction(prediction, lotteryType) {
            let html = '<div class="mb-3">';

            if (lotteryType === 'powercolor') {
                html += `<strong>第一區:</strong> ${formatNumbers(prediction['第一區'])} `;
                html += `<strong>第二區:</strong> ${formatNumbers([prediction['第二區']], true)}`;
            } else if (lotteryType === 'lotto649') {
                html += `<strong>第一區:</strong> ${formatNumbers(prediction['第一區'])} `;
                html += `<strong>特別號:</strong> ${formatNumbers([prediction['特別號']], true)}`;
            } else if (lotteryType === 'dailycash') {
                html += `<strong>號碼:</strong> ${formatNumbers(prediction['號碼'])}`;
            }

            html += '</div>';

            if (prediction['信心分數']) {
                html += `<div class="mb-2"><strong>信心分數:</strong> <span class="badge bg-primary">${prediction['信心分數'].toFixed(3)}</span></div>`;
            }

            if (prediction['整合解釋']) {
                html += '<div class="mb-2"><strong>整合說明:</strong><ul class="mb-0">';
                prediction['整合解釋'].forEach(exp => {
                    html += `<li>${exp}</li>`;
                });
                html += '</ul></div>';
            }

            if (prediction['詳細解釋']) {
                html += '<div class="mb-2"><strong>詳細分析:</strong><ul class="mb-0">';
                prediction['詳細解釋'].forEach(exp => {
                    html += `<li>${exp}</li>`;
                });
                html += '</ul></div>';
            }

            if (prediction['候選數量']) {
                html += `<div class="text-muted small">基於 ${prediction['候選數量']} 個候選結果整合</div>`;
            }

            return html;
        }

        function displayResults(results) {
            const template = document.getElementById('resultsTemplate');
            const clone = template.content.cloneNode(true);

            // 填充基本資訊
            clone.getElementById('resultLotteryType').textContent = getLotteryName(results.lottery_type);
            clone.getElementById('resultPeriod').textContent = results.period;
            clone.getElementById('resultTime').textContent = results.prediction_time;

            // 顯示最佳整合預測結果
            if (results.best_predictions) {
                displayBestPredictions(clone, results.best_predictions, results.lottery_type);
            }
            
            // 填充機器學習結果
            const mlContainer = clone.getElementById('mlResults');
            if (results.ml_prediction && results.ml_prediction.candidates) {
                let mlHtml = `<p><strong>方法:</strong> ${results.ml_prediction.method}</p>`;
                mlHtml += `<p><strong>版本:</strong> ${results.ml_prediction.version}</p>`;
                mlHtml += `<p><strong>候選數量:</strong> ${results.ml_prediction.candidates.length}</p><hr>`;
                
                results.ml_prediction.candidates.slice(0, 3).forEach((candidate, index) => {
                    const confidence = candidate.confidence || 0;
                    mlHtml += `
                        <div class="mb-3 p-3 border rounded">
                            <h6>候選 #${index + 1} 
                                <span class="${getConfidenceClass(confidence)}">
                                    ${getConfidenceIcon(confidence)} (${confidence.toFixed(3)})
                                </span>
                            </h6>
                            <div class="mb-2">
                                <strong>號碼:</strong> ${formatNumbers(candidate.main_numbers)}
                                ${candidate.special_number ? formatNumbers([candidate.special_number], true) : ''}
                                <button class="btn btn-outline-primary btn-sm ms-2" onclick="analyzeNumbers(${JSON.stringify(candidate.main_numbers)}, '${results.lottery_type}')">
                                    <i class="fas fa-microscope"></i> 分析原因
                                </button>
                            </div>
                            ${candidate.explanation ? `
                                <div class="small text-muted">
                                    <strong>預測理由:</strong>
                                    <ul class="mb-0">
                                        ${candidate.explanation.slice(0, 3).map(reason => `<li>${reason}</li>`).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                        </div>
                    `;
                });
                mlContainer.innerHTML = mlHtml;
            } else {
                mlContainer.innerHTML = '<div class="text-danger">❌ 機器學習預測失敗或無結果</div>';
            }
            
            // 填充板路分析結果
            const bpContainer = clone.getElementById('boardPathResults');
            if (results.board_path_prediction && results.board_path_prediction.candidates) {
                let bpHtml = `<p><strong>方法:</strong> ${results.board_path_prediction.method}</p>`;
                bpHtml += `<p><strong>版本:</strong> ${results.board_path_prediction.version}</p>`;
                bpHtml += `<p><strong>候選數量:</strong> ${results.board_path_prediction.candidates.length}</p><hr>`;
                
                results.board_path_prediction.candidates.slice(0, 3).forEach((candidate, index) => {
                    const confidence = candidate.confidence || 0;
                    bpHtml += `
                        <div class="mb-3 p-3 border rounded">
                            <h6>候選 #${index + 1} 
                                <span class="${getConfidenceClass(confidence)}">
                                    ${getConfidenceIcon(confidence)} (${confidence.toFixed(3)})
                                </span>
                            </h6>
                            <div class="mb-2">
                                <strong>號碼:</strong> ${formatNumbers(candidate.main_numbers)}
                                ${candidate.special_number ? formatNumbers([candidate.special_number], true) : ''}
                                <button class="btn btn-outline-success btn-sm ms-2" onclick="analyzeNumbers(${JSON.stringify(candidate.main_numbers)}, '${results.lottery_type}')">
                                    <i class="fas fa-microscope"></i> 分析原因
                                </button>
                            </div>
                            ${candidate.explanation ? `
                                <div class="small text-muted">
                                    <strong>板路分析:</strong>
                                    <ul class="mb-0">
                                        ${candidate.explanation.slice(0, 3).map(reason => `<li>${reason}</li>`).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                        </div>
                    `;
                });
                bpContainer.innerHTML = bpHtml;
            } else {
                bpContainer.innerHTML = '<div class="text-danger">❌ 板路分析預測失敗或無結果</div>';
            }
            
            // 填充成功分析
            const saContainer = clone.getElementById('successAnalysis');
            if (results.success_analysis) {
                let saHtml = '';
                const sa = results.success_analysis;
                
                if (sa.ml_success_factors && sa.ml_success_factors.length > 0) {
                    saHtml += '<h6>🤖 機器學習成功因素:</h6><ul>';
                    sa.ml_success_factors.slice(0, 3).forEach(factor => {
                        saHtml += `<li>${factor}</li>`;
                    });
                    saHtml += '</ul>';
                }
                
                if (sa.board_path_success_factors && sa.board_path_success_factors.length > 0) {
                    saHtml += '<h6>📊 板路分析成功因素:</h6><ul>';
                    sa.board_path_success_factors.slice(0, 3).forEach(factor => {
                        saHtml += `<li>${factor}</li>`;
                    });
                    saHtml += '</ul>';
                }
                
                if (sa.common_success_patterns && sa.common_success_patterns.length > 0) {
                    saHtml += '<h6>🎯 共同成功模式:</h6><ul>';
                    sa.common_success_patterns.slice(0, 3).forEach(pattern => {
                        saHtml += `<li>${pattern}</li>`;
                    });
                    saHtml += '</ul>';
                }
                
                saContainer.innerHTML = saHtml || '<div class="text-muted">暫無足夠的成功案例進行分析</div>';
            } else {
                saContainer.innerHTML = '<div class="text-danger">❌ 成功分析失敗或無結果</div>';
            }
            
            // 填充比較摘要
            const csContainer = clone.getElementById('comparisonSummary');
            const mlCount = results.ml_prediction ? results.ml_prediction.candidates.length : 0;
            const bpCount = results.board_path_prediction ? results.board_path_prediction.candidates.length : 0;
            
            let csHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>機器學習候選數:</strong> ${mlCount}</p>
                        <p><strong>板路分析候選數:</strong> ${bpCount}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>💡 建議策略:</h6>
                        <ul class="small">
            `;
            
            if (mlCount > 0 && bpCount > 0) {
                csHtml += `
                    <li>兩種方法都有結果，建議綜合參考</li>
                    <li>可選擇信心分數較高的候選</li>
                    <li>注意兩種方法的共同推薦號碼</li>
                `;
            } else if (mlCount > 0) {
                csHtml += '<li>僅機器學習有結果，建議謹慎參考</li>';
            } else if (bpCount > 0) {
                csHtml += '<li>僅板路分析有結果，建議謹慎參考</li>';
            } else {
                csHtml += '<li>兩種方法都無結果，建議暫停投注</li>';
            }
            
            csHtml += '</ul></div></div>';
            csContainer.innerHTML = csHtml;
            
            // 替換容器內容
            document.getElementById('resultsContainer').innerHTML = '';
            document.getElementById('resultsContainer').appendChild(clone);
        }

        function getLotteryName(type) {
            const names = {
                'powercolor': '威力彩',
                'lotto649': '大樂透',
                'dailycash': '今彩539'
            };
            return names[type] || type;
        }

        // 表單提交處理
        document.getElementById('predictionForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {
                lottery_type: formData.get('lottery_type'),
                candidates_count: parseInt(formData.get('candidates_count')),
                min_confidence: parseFloat(formData.get('min_confidence'))
            };
            
            // 顯示載入狀態
            document.querySelector('.loading-spinner').style.display = 'block';
            document.getElementById('resultsContainer').innerHTML = '';
            
            try {
                const response = await fetch('/api/separated_predict', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    displayResults(result.results);
                } else {
                    document.getElementById('resultsContainer').innerHTML = 
                        `<div class="alert alert-danger">預測失敗: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('resultsContainer').innerHTML = 
                    `<div class="alert alert-danger">網路錯誤: ${error.message}</div>`;
            } finally {
                document.querySelector('.loading-spinner').style.display = 'none';
            }
        });

        // 每日自動化任務
        async function runDailyAutomation(taskType) {
            try {
                const response = await fetch('/api/daily_automation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({task_type: taskType})
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert(`✅ ${result.message}`);
                } else {
                    alert(`❌ 任務失敗: ${result.error}`);
                }
            } catch (error) {
                alert(`❌ 網路錯誤: ${error.message}`);
            }
        }

        // 策略優化
        async function optimizeStrategy() {
            const lotteryType = document.getElementById('lotteryType').value;

            try {
                const response = await fetch('/api/strategy_optimize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({lottery_type: lotteryType})
                });

                const result = await response.json();

                if (result.success) {
                    alert(`✅ ${getLotteryName(lotteryType)} 策略優化完成！\n\n新策略參數:\n- ML權重: ${result.strategy.ml_weight.toFixed(2)}\n- 板路權重: ${result.strategy.board_path_weight.toFixed(2)}\n- 候選數量: ${result.strategy.candidates_count}\n- 最低信心分數: ${result.strategy.min_confidence.toFixed(2)}`);
                } else {
                    alert(`❌ 策略優化失敗: ${result.error}`);
                }
            } catch (error) {
                alert(`❌ 網路錯誤: ${error.message}`);
            }
        }

        // 分析號碼組合
        function analyzeNumbers(numbers, lotteryType) {
            // 將號碼和彩票類型存儲到localStorage，然後跳轉到分析頁面
            localStorage.setItem('analyzeNumbers', JSON.stringify(numbers));
            localStorage.setItem('analyzeLotteryType', lotteryType);

            // 在新標籤頁打開分析頁面
            window.open('/number_analysis', '_blank');
        }
    </script>
</body>
</html>
