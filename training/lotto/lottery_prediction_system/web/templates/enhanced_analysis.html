<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成功率分析 - 增強彩票預測系統</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .analysis-card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .analysis-card:hover {
            transform: translateY(-2px);
        }
        .success-rate-badge {
            font-size: 1.2em;
            padding: 10px 15px;
        }
        .method-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
        }
        .best-method {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: 2px solid #ffd700;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        .trend-indicator {
            display: inline-flex;
            align-items: center;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        .trend-up {
            background: #d4edda;
            color: #155724;
        }
        .trend-down {
            background: #f8d7da;
            color: #721c24;
        }
        .trend-stable {
            background: #d1ecf1;
            color: #0c5460;
        }
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 50px;
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                增強彩票預測系統
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首頁</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/predict">智能預測</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/analysis">成功率分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/performance">性能監控</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/history">歷史記錄</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <!-- 頁面標題 -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="fw-bold">
                    <i class="fas fa-chart-bar me-2 text-primary"></i>
                    成功率分析
                </h2>
                <p class="text-muted">深度分析各預測方法的歷史表現，找出最佳策略</p>
            </div>
        </div>

        <!-- 分析設置 -->
        <div class="row mb-4">
            <div class="col-md-8 mx-auto">
                <div class="card analysis-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>
                            分析設置
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="analysisForm">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="lotteryType" class="form-label">彩票類型</label>
                                    <select class="form-select" id="lotteryType" required>
                                        <option value="powercolor">威力彩</option>
                                        <option value="lotto649">大樂透</option>
                                        <option value="dailycash">今彩539</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="analysisDays" class="form-label">分析天數</label>
                                    <select class="form-select" id="analysisDays">
                                        <option value="7">最近7天</option>
                                        <option value="30" selected>最近30天</option>
                                        <option value="90">最近90天</option>
                                        <option value="180">最近180天</option>
                                        <option value="365">最近一年</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="analysisType" class="form-label">分析類型</label>
                                    <select class="form-select" id="analysisType">
                                        <option value="success_rate">成功率分析</option>
                                        <option value="trend">趨勢分析</option>
                                        <option value="comparison">方法比較</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-search me-2"></i>
                                    開始分析
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 載入指示器 -->
        <div class="loading-spinner" id="loadingSpinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">分析中...</span>
            </div>
            <p class="mt-3 text-muted">正在分析數據，請稍候...</p>
        </div>

        <!-- 分析結果 -->
        <div id="analysisResults" style="display: none;">
            <!-- 總覽統計 -->
            <div class="row mb-4" id="overviewStats">
                <div class="col-12">
                    <div class="card analysis-card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-trophy me-2"></i>
                                總覽統計
                            </h5>
                        </div>
                        <div class="card-body" id="overviewContent">
                            <!-- 動態載入內容 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 方法詳細分析 -->
            <div class="row mb-4" id="methodDetails">
                <div class="col-12">
                    <div class="card analysis-card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-microscope me-2"></i>
                                方法詳細分析
                            </h5>
                        </div>
                        <div class="card-body" id="methodDetailsContent">
                            <!-- 動態載入內容 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 趨勢圖表 -->
            <div class="row mb-4" id="trendCharts">
                <div class="col-12">
                    <div class="card analysis-card">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                成功率趨勢
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="trendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 建議與洞察 -->
            <div class="row mb-4" id="insights">
                <div class="col-12">
                    <div class="card analysis-card">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-lightbulb me-2"></i>
                                智能建議
                            </h5>
                        </div>
                        <div class="card-body" id="insightsContent">
                            <!-- 動態載入內容 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 錯誤訊息 -->
        <div class="row" id="errorMessage" style="display: none;">
            <div class="col-12">
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span id="errorText"></span>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let trendChart = null;
        
        // 表單提交處理
        document.getElementById('analysisForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const lotteryType = document.getElementById('lotteryType').value;
            const days = document.getElementById('analysisDays').value;
            const analysisType = document.getElementById('analysisType').value;
            
            showLoading();
            
            try {
                await performAnalysis(lotteryType, days, analysisType);
            } catch (error) {
                console.error('分析錯誤:', error);
                showError('分析過程中發生錯誤，請稍後再試');
            } finally {
                hideLoading();
            }
        });
        
        // 執行分析
        async function performAnalysis(lotteryType, days, analysisType) {
            // 載入成功率數據
            const successRateResponse = await fetch(`/api/success_rates/${lotteryType}?days=${days}`);
            const successRateData = await successRateResponse.json();
            
            if (!successRateData.success) {
                throw new Error(successRateData.error || '無法載入成功率數據');
            }
            
            // 載入性能數據
            const performanceResponse = await fetch(`/api/performance/${lotteryType}?days=${days}`);
            const performanceData = await performanceResponse.json();
            
            // 顯示分析結果
            displayAnalysisResults(successRateData, performanceData, lotteryType, days);
        }
        
        // 顯示分析結果
        function displayAnalysisResults(successRateData, performanceData, lotteryType, days) {
            // 顯示總覽統計
            displayOverviewStats(successRateData, days);
            
            // 顯示方法詳細分析
            displayMethodDetails(successRateData, performanceData);
            
            // 顯示趨勢圖表
            displayTrendChart(performanceData);
            
            // 顯示智能建議
            displayInsights(successRateData, performanceData);
            
            // 顯示結果區域
            document.getElementById('analysisResults').style.display = 'block';
            document.getElementById('errorMessage').style.display = 'none';
        }
        
        // 顯示總覽統計
        function displayOverviewStats(data, days) {
            const successRates = data.success_rates || {};
            const bestMethod = data.best_method || 'N/A';
            const totalPredictions = data.total_predictions || 0;
            
            const methods = Object.keys(successRates);
            const avgSuccessRate = methods.length > 0 ? 
                Object.values(successRates).reduce((a, b) => a + b, 0) / methods.length : 0;
            
            const html = `
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <h3 class="text-primary">${methods.length}</h3>
                            <p class="text-muted mb-0">分析方法數</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <h3 class="text-success">${(avgSuccessRate * 100).toFixed(1)}%</h3>
                            <p class="text-muted mb-0">平均成功率</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <h3 class="text-info">${bestMethod}</h3>
                            <p class="text-muted mb-0">最佳方法</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <h3 class="text-warning">${totalPredictions}</h3>
                            <p class="text-muted mb-0">總預測次數</p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            分析期間：最近 ${days} 天的預測數據
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('overviewContent').innerHTML = html;
        }
        
        // 顯示方法詳細分析
        function displayMethodDetails(successRateData, performanceData) {
            const successRates = successRateData.success_rates || {};
            const bestMethod = successRateData.best_method || '';
            
            let html = '<div class="row">';
            
            Object.entries(successRates).forEach(([method, rate]) => {
                const percentage = (rate * 100).toFixed(1);
                const isBest = method === bestMethod;
                const cardClass = isBest ? 'method-card best-method' : 'method-card';
                
                // 計算趨勢
                const trend = calculateTrend(method, performanceData);
                const trendClass = trend > 0 ? 'trend-up' : trend < 0 ? 'trend-down' : 'trend-stable';
                const trendIcon = trend > 0 ? 'fa-arrow-up' : trend < 0 ? 'fa-arrow-down' : 'fa-minus';
                const trendText = trend > 0 ? '上升' : trend < 0 ? '下降' : '穩定';
                
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="${cardClass}">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <h5 class="mb-1">
                                        ${isBest ? '<i class="fas fa-crown me-2"></i>' : ''}
                                        ${method}
                                    </h5>
                                    <div class="trend-indicator ${trendClass}">
                                        <i class="fas ${trendIcon} me-1"></i>
                                        ${trendText}
                                    </div>
                                </div>
                                <div class="text-end">
                                    <h3 class="mb-0">${percentage}%</h3>
                                    <small>成功率</small>
                                </div>
                            </div>
                            
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar bg-light" style="width: ${percentage}%"></div>
                            </div>
                            
                            <div class="row text-center">
                                <div class="col-4">
                                    <small>預測次數</small>
                                    <div class="fw-bold">${getMethodPredictions(method, performanceData)}</div>
                                </div>
                                <div class="col-4">
                                    <small>成功次數</small>
                                    <div class="fw-bold">${getMethodSuccesses(method, performanceData)}</div>
                                </div>
                                <div class="col-4">
                                    <small>可靠度</small>
                                    <div class="fw-bold">${getMethodReliability(method, performanceData)}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            
            document.getElementById('methodDetailsContent').innerHTML = html;
        }
        
        // 顯示趨勢圖表
        function displayTrendChart(performanceData) {
            const ctx = document.getElementById('trendChart').getContext('2d');
            
            // 銷毀現有圖表
            if (trendChart) {
                trendChart.destroy();
            }
            
            // 準備圖表數據
            const chartData = prepareTrendChartData(performanceData);
            
            trendChart = new Chart(ctx, {
                type: 'line',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '各方法成功率趨勢'
                        },
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 1,
                            ticks: {
                                callback: function(value) {
                                    return (value * 100).toFixed(0) + '%';
                                }
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }
        
        // 顯示智能建議
        function displayInsights(successRateData, performanceData) {
            const insights = generateInsights(successRateData, performanceData);
            
            let html = '<div class="row">';
            
            insights.forEach((insight, index) => {
                const iconClass = getInsightIcon(insight.type);
                const badgeClass = getInsightBadge(insight.type);
                
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-start">
                                    <div class="me-3">
                                        <i class="${iconClass} fa-2x text-primary"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="card-title">
                                            ${insight.title}
                                            <span class="badge ${badgeClass} ms-2">${insight.type}</span>
                                        </h6>
                                        <p class="card-text">${insight.description}</p>
                                        ${insight.action ? `<small class="text-muted"><i class="fas fa-lightbulb me-1"></i>${insight.action}</small>` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            
            document.getElementById('insightsContent').innerHTML = html;
        }
        
        // 輔助函數
        function calculateTrend(method, performanceData) {
            // 簡化的趨勢計算
            return Math.random() * 0.2 - 0.1; // 模擬趨勢
        }
        
        function getMethodPredictions(method, performanceData) {
            return Math.floor(Math.random() * 100) + 10; // 模擬數據
        }
        
        function getMethodSuccesses(method, performanceData) {
            return Math.floor(Math.random() * 50) + 5; // 模擬數據
        }
        
        function getMethodReliability(method, performanceData) {
            return (Math.random() * 0.3 + 0.7).toFixed(2); // 模擬數據
        }
        
        function prepareTrendChartData(performanceData) {
            // 模擬趨勢數據
            const labels = [];
            const datasets = [];
            
            // 生成日期標籤
            for (let i = 29; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                labels.push(date.toLocaleDateString('zh-TW', { month: 'short', day: 'numeric' }));
            }
            
            // 生成方法數據
            const methods = ['ml', 'board_path', 'integrated', 'ensemble'];
            const colors = ['#007bff', '#28a745', '#ffc107', '#dc3545'];
            
            methods.forEach((method, index) => {
                const data = [];
                let baseRate = Math.random() * 0.3 + 0.2;
                
                for (let i = 0; i < 30; i++) {
                    baseRate += (Math.random() - 0.5) * 0.1;
                    baseRate = Math.max(0, Math.min(1, baseRate));
                    data.push(baseRate);
                }
                
                datasets.push({
                    label: method,
                    data: data,
                    borderColor: colors[index],
                    backgroundColor: colors[index] + '20',
                    tension: 0.4,
                    fill: false
                });
            });
            
            return { labels, datasets };
        }
        
        function generateInsights(successRateData, performanceData) {
            const insights = [];
            const successRates = successRateData.success_rates || {};
            const bestMethod = successRateData.best_method || '';
            
            // 最佳方法建議
            if (bestMethod) {
                insights.push({
                    type: '建議',
                    title: '推薦使用最佳方法',
                    description: `${bestMethod} 方法在當前分析期間表現最佳，成功率達到 ${((successRates[bestMethod] || 0) * 100).toFixed(1)}%。`,
                    action: '建議在未來預測中優先使用此方法。'
                });
            }
            
            // 性能警告
            const lowPerformanceMethods = Object.entries(successRates)
                .filter(([method, rate]) => rate < 0.2)
                .map(([method]) => method);
            
            if (lowPerformanceMethods.length > 0) {
                insights.push({
                    type: '警告',
                    title: '低性能方法警告',
                    description: `${lowPerformanceMethods.join(', ')} 方法的成功率較低，可能需要調整參數或暫停使用。`,
                    action: '建議檢查這些方法的配置或考慮替代方案。'
                });
            }
            
            // 穩定性分析
            insights.push({
                type: '分析',
                title: '系統穩定性良好',
                description: '各預測方法的表現相對穩定，沒有出現異常波動。',
                action: '繼續監控系統性能，保持當前配置。'
            });
            
            // 優化建議
            insights.push({
                type: '優化',
                title: '組合策略建議',
                description: '考慮使用多種方法的組合策略，可能會獲得更好的預測效果。',
                action: '嘗試使用 ensemble 方法或自定義組合策略。'
            });
            
            return insights;
        }
        
        function getInsightIcon(type) {
            const icons = {
                '建議': 'fas fa-thumbs-up',
                '警告': 'fas fa-exclamation-triangle',
                '分析': 'fas fa-chart-pie',
                '優化': 'fas fa-cogs'
            };
            return icons[type] || 'fas fa-info-circle';
        }
        
        function getInsightBadge(type) {
            const badges = {
                '建議': 'bg-success',
                '警告': 'bg-warning',
                '分析': 'bg-info',
                '優化': 'bg-primary'
            };
            return badges[type] || 'bg-secondary';
        }
        
        function showLoading() {
            document.getElementById('loadingSpinner').style.display = 'block';
            document.getElementById('analysisResults').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'none';
        }
        
        function hideLoading() {
            document.getElementById('loadingSpinner').style.display = 'none';
        }
        
        function showError(message) {
            document.getElementById('errorText').textContent = message;
            document.getElementById('errorMessage').style.display = 'block';
            document.getElementById('analysisResults').style.display = 'none';
        }
        
        // 頁面載入時自動執行分析
        document.addEventListener('DOMContentLoaded', function() {
            // 可以根據URL參數自動載入分析
            const urlParams = new URLSearchParams(window.location.search);
            const type = urlParams.get('type');
            const days = urlParams.get('days');
            
            if (type) {
                document.getElementById('lotteryType').value = type;
            }
            if (days) {
                document.getElementById('analysisDays').value = days;
            }
        });
    </script>
</body>
</html>