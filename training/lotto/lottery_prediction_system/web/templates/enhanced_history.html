<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>歷史記錄 - 增強彩票預測系統</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .history-card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .history-card:hover {
            transform: translateY(-2px);
        }
        .prediction-item {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            background: #fff;
        }
        .prediction-item.success {
            border-left: 5px solid #28a745;
            background: #f8fff9;
        }
        .prediction-item.partial {
            border-left: 5px solid #ffc107;
            background: #fffef8;
        }
        .prediction-item.failed {
            border-left: 5px solid #dc3545;
            background: #fff8f8;
        }
        .number-ball {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 2px;
            font-weight: bold;
            color: white;
            font-size: 0.9em;
        }
        .predicted-number {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }
        .actual-number {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        .matched-number {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            border: 2px solid #28a745;
        }
        .success-rate-badge {
            font-size: 1.1em;
            padding: 8px 15px;
        }
        .method-badge {
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }
        .filter-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }
        .verification-status {
            display: inline-flex;
            align-items: center;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
        }
        .verified {
            background: #d4edda;
            color: #155724;
        }
        .pending {
            background: #fff3cd;
            color: #856404;
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <!-- 載入遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="text-center text-white">
            <div class="spinner-border mb-3" role="status">
                <span class="visually-hidden">載入中...</span>
            </div>
            <h5>載入歷史記錄...</h5>
        </div>
    </div>

    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                增強彩票預測系統
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首頁</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/predict">智能預測</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analysis">成功率分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/performance">性能監控</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/history">歷史記錄</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <!-- 頁面標題 -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="fw-bold">
                    <i class="fas fa-history me-2 text-primary"></i>
                    預測歷史記錄
                </h2>
                <p class="text-muted">查看歷史預測結果，分析成功率趨勢</p>
            </div>
        </div>

        <!-- 統計總覽 -->
        <div class="row mb-4" id="statsOverview">
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <h3 class="mb-2" id="totalPredictions">0</h3>
                    <p class="mb-0">總預測次數</p>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #28a745, #20c997);">
                    <h3 class="mb-2" id="successfulPredictions">0</h3>
                    <p class="mb-0">成功預測</p>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #ffc107, #fd7e14);">
                    <h3 class="mb-2" id="averageSuccessRate">0%</h3>
                    <p class="mb-0">平均成功率</p>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #dc3545, #e83e8c);">
                    <h3 class="mb-2" id="bestMethod">N/A</h3>
                    <p class="mb-0">最佳方法</p>
                </div>
            </div>
        </div>

        <!-- 篩選器 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="filter-section">
                    <h5 class="mb-3">
                        <i class="fas fa-filter me-2"></i>
                        篩選條件
                    </h5>
                    <form id="filterForm">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="filterLotteryType" class="form-label">彩票類型</label>
                                <select class="form-select" id="filterLotteryType">
                                    <option value="">全部</option>
                                    <option value="powercolor">威力彩</option>
                                    <option value="lotto649">大樂透</option>
                                    <option value="dailycash">今彩539</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="filterMethod" class="form-label">預測方法</label>
                                <select class="form-select" id="filterMethod">
                                    <option value="">全部</option>
                                    <option value="ml">機器學習</option>
                                    <option value="board_path">板路分析</option>
                                    <option value="integrated">集成預測</option>
                                    <option value="ensemble">組合預測</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="filterStatus" class="form-label">驗證狀態</label>
                                <select class="form-select" id="filterStatus">
                                    <option value="">全部</option>
                                    <option value="verified">已驗證</option>
                                    <option value="pending">待驗證</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="filterDays" class="form-label">時間範圍</label>
                                <select class="form-select" id="filterDays">
                                    <option value="7">最近7天</option>
                                    <option value="30" selected>最近30天</option>
                                    <option value="90">最近90天</option>
                                    <option value="180">最近180天</option>
                                    <option value="365">最近一年</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="filterPerPage" class="form-label">每頁顯示</label>
                                <select class="form-select" id="filterPerPage">
                                    <option value="20">20條記錄</option>
                                    <option value="50" selected>50條記錄</option>
                                    <option value="100">100條記錄</option>
                                    <option value="200">200條記錄</option>
                                </select>
                            </div>
                            <div class="col-md-9 mb-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex align-items-end h-100">
                                    <small class="text-muted">提示：增加每頁顯示數量可以查看更多歷史記錄，但載入時間可能會稍長</small>
                                </div>
                            </div>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>
                                套用篩選
                            </button>
                            <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetFilters()">
                                <i class="fas fa-undo me-2"></i>
                                重置
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 歷史記錄列表 -->
        <div class="row">
            <div class="col-12">
                <div class="card history-card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            預測記錄
                        </h5>
                        <div>
                            <button class="btn btn-sm btn-outline-light" onclick="exportHistory()">
                                <i class="fas fa-download me-1"></i>
                                匯出
                            </button>
                            <button class="btn btn-sm btn-outline-light ms-2" onclick="refreshHistory()">
                                <i class="fas fa-sync-alt me-1"></i>
                                刷新
                            </button>
                        </div>
                    </div>
                    <div class="card-body" id="historyContent">
                        <!-- 動態載入歷史記錄 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 分頁 -->
        <div class="row mt-4" id="paginationSection" style="display: none;">
            <div class="col-12">
                <nav aria-label="歷史記錄分頁">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- 動態生成分頁 -->
                    </ul>
                </nav>
            </div>
        </div>

        <!-- 錯誤訊息 -->
        <div class="row" id="errorMessage" style="display: none;">
            <div class="col-12">
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span id="errorText"></span>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPage = 1;
        let totalPages = 1;
        let currentFilters = {};
        
        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadHistoryData();
        });
        
        // 篩選表單提交
        document.getElementById('filterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            currentPage = 1;
            loadHistoryData();
        });
        
        // 載入歷史數據
        async function loadHistoryData() {
            showLoading();
            
            try {
                // 獲取篩選條件
                currentFilters = {
                    lottery_type: document.getElementById('filterLotteryType').value,
                    method: document.getElementById('filterMethod').value,
                    status: document.getElementById('filterStatus').value,
                    days: document.getElementById('filterDays').value,
                    page: currentPage,
                    per_page: document.getElementById('filterPerPage') ? document.getElementById('filterPerPage').value : 50
                };
                
                // 構建查詢參數
                const params = new URLSearchParams();
                Object.entries(currentFilters).forEach(([key, value]) => {
                    if (value) params.append(key, value);
                });
                
                // 載入歷史記錄
                const response = await fetch(`/api/history?${params.toString()}`);
                const data = await response.json();
                
                if (data.success) {
                    displayHistoryData(data);
                    updateStatistics(data.statistics || {});
                    updatePagination(data.pagination || {});
                } else {
                    showError(data.error || '載入歷史記錄失敗');
                }
            } catch (error) {
                console.error('載入歷史記錄錯誤:', error);
                showError('載入歷史記錄時發生錯誤');
            } finally {
                hideLoading();
            }
        }
        
        // 顯示歷史數據
        function displayHistoryData(data) {
            const container = document.getElementById('historyContent');
            const records = data.records || [];
            
            if (records.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">沒有找到符合條件的記錄</h5>
                        <p class="text-muted">請調整篩選條件或稍後再試</p>
                    </div>
                `;
                return;
            }
            
            let html = '';
            
            records.forEach(record => {
                const statusClass = getStatusClass(record.verification_status);
                const successRate = (record.success_rate * 100).toFixed(1);
                
                html += `
                    <div class="prediction-item ${statusClass}">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <div>
                                        <h6 class="mb-1">
                                            ${getLotteryTypeName(record.lottery_type)} - 期號 ${record.period || 'N/A'}
                                            <span class="method-badge ms-2">${record.prediction_method}</span>
                                        </h6>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            ${formatDateTime(record.prediction_time)}
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge success-rate-badge ${getSuccessRateBadgeClass(record.success_rate)}">
                                            成功率 ${successRate}%
                                        </span>
                                        <div class="mt-1">
                                            <span class="verification-status ${getVerificationClass(record.verification_status)}">
                                                <i class="fas ${getVerificationIcon(record.verification_status)} me-1"></i>
                                                ${getVerificationText(record.verification_status)}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <h6>預測號碼：</h6>
                                    <div class="d-flex flex-wrap mb-2">
                                        ${renderPredictedNumbers(record.predicted_numbers, record.lottery_type)}
                                    </div>
                                </div>
                                
                                ${record.actual_numbers ? `
                                    <div class="mb-3">
                                        <h6>開獎號碼：</h6>
                                        <div class="d-flex flex-wrap mb-2">
                                            ${renderActualNumbers(record.actual_numbers, record.lottery_type)}
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <h6>中獎情況：</h6>
                                        <div class="d-flex flex-wrap">
                                            ${renderMatchedNumbers(record.predicted_numbers, record.actual_numbers, record.lottery_type)}
                                        </div>
                                        <small class="text-muted mt-1 d-block">
                                            命中 ${calculateMatches(record.predicted_numbers, record.actual_numbers, record.lottery_type)} 個號碼
                                        </small>
                                    </div>
                                ` : ''}
                                
                                ${record.explanation && record.explanation.length > 0 ? `
                                    <div>
                                        <h6>預測說明：</h6>
                                        <ul class="list-unstyled mb-0">
                                            ${record.explanation.map(exp => `<li><i class="fas fa-check-circle text-success me-2"></i>${exp}</li>`).join('')}
                                        </ul>
                                    </div>
                                ` : ''}
                            </div>
                            
                            <div class="col-md-4">
                                <div class="text-center">
                                    ${record.verification_status === 'verified' && record.actual_numbers ? `
                                        <div class="mb-3">
                                            <h4 class="${getResultClass(record.predicted_numbers, record.actual_numbers, record.lottery_type)}">
                                                ${getResultText(record.predicted_numbers, record.actual_numbers, record.lottery_type)}
                                            </h4>
                                        </div>
                                    ` : ''}
                                    
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-outline-primary btn-sm" onclick="viewDetails('${record.id}')">
                                            <i class="fas fa-eye me-1"></i>
                                            查看詳情
                                        </button>
                                        ${record.verification_status === 'pending' ? `
                                            <button class="btn btn-outline-warning btn-sm" onclick="verifyPrediction('${record.id}')">
                                                <i class="fas fa-check me-1"></i>
                                                驗證結果
                                            </button>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // 更新統計信息
        function updateStatistics(stats) {
            document.getElementById('totalPredictions').textContent = stats.total_predictions || 0;
            document.getElementById('successfulPredictions').textContent = stats.successful_predictions || 0;
            document.getElementById('averageSuccessRate').textContent = ((stats.average_success_rate || 0) * 100).toFixed(1) + '%';
            document.getElementById('bestMethod').textContent = stats.best_method || 'N/A';
        }
        
        // 更新分頁
        function updatePagination(pagination) {
            totalPages = pagination.total_pages || 1;
            currentPage = pagination.current_page || 1;
            
            if (totalPages <= 1) {
                document.getElementById('paginationSection').style.display = 'none';
                return;
            }
            
            const paginationContainer = document.getElementById('pagination');
            let html = '';
            
            // 上一頁
            html += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            `;
            
            // 頁碼
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                html += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                    </li>
                `;
            }
            
            // 下一頁
            html += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            `;
            
            paginationContainer.innerHTML = html;
            document.getElementById('paginationSection').style.display = 'block';
        }
        
        // 輔助函數
        function getStatusClass(status) {
            if (status === 'verified') return 'success';
            if (status === 'partial') return 'partial';
            return 'pending';
        }
        
        function getLotteryTypeName(type) {
            const names = {
                'powercolor': '威力彩',
                'lotto649': '大樂透',
                'dailycash': '今彩539'
            };
            return names[type] || type;
        }
        
        function formatDateTime(dateTime) {
            return new Date(dateTime).toLocaleString('zh-TW');
        }
        
        function getSuccessRateBadgeClass(rate) {
            if (rate >= 0.8) return 'bg-success';
            if (rate >= 0.6) return 'bg-warning';
            return 'bg-danger';
        }
        
        function getVerificationClass(status) {
            return status === 'verified' ? 'verified' : 'pending';
        }
        
        function getVerificationIcon(status) {
            return status === 'verified' ? 'fa-check-circle' : 'fa-clock';
        }
        
        function getVerificationText(status) {
            return status === 'verified' ? '已驗證' : '待驗證';
        }
        
        function renderPredictedNumbers(numbers, lotteryType) {
            // 根據彩票類型渲染預測號碼
            let html = '';
            
            if (lotteryType === 'powercolor') {
                const firstArea = numbers['第一區'] || [];
                const secondArea = numbers['第二區'] || 0;
                
                firstArea.forEach(num => {
                    html += `<div class="number-ball predicted-number">${num}</div>`;
                });
                html += `<div class="number-ball predicted-number" style="background: linear-gradient(135deg, #f093fb, #f5576c);">${secondArea}</div>`;
            } else if (lotteryType === 'lotto649') {
                const mainNumbers = numbers['一般號碼'] || [];
                const specialNumber = numbers['特別號'] || 0;
                
                mainNumbers.forEach(num => {
                    html += `<div class="number-ball predicted-number">${num}</div>`;
                });
                html += `<div class="number-ball predicted-number" style="background: linear-gradient(135deg, #f093fb, #f5576c);">${specialNumber}</div>`;
            } else if (lotteryType === 'dailycash') {
                const nums = numbers['號碼'] || [];
                nums.forEach(num => {
                    html += `<div class="number-ball predicted-number">${num}</div>`;
                });
            }
            
            return html;
        }
        
        function renderActualNumbers(numbers, lotteryType) {
            // 根據彩票類型渲染開獎號碼
            let html = '';
            
            if (lotteryType === 'powercolor') {
                const firstArea = numbers['第一區'] || [];
                const secondArea = numbers['第二區'] || 0;
                
                firstArea.forEach(num => {
                    html += `<div class="number-ball actual-number">${num}</div>`;
                });
                html += `<div class="number-ball actual-number" style="background: linear-gradient(135deg, #28a745, #20c997);">${secondArea}</div>`;
            } else if (lotteryType === 'lotto649') {
                const mainNumbers = numbers['一般號碼'] || [];
                const specialNumber = numbers['特別號'] || 0;
                
                mainNumbers.forEach(num => {
                    html += `<div class="number-ball actual-number">${num}</div>`;
                });
                html += `<div class="number-ball actual-number" style="background: linear-gradient(135deg, #28a745, #20c997);">${specialNumber}</div>`;
            } else if (lotteryType === 'dailycash') {
                const nums = numbers['號碼'] || [];
                nums.forEach(num => {
                    html += `<div class="number-ball actual-number">${num}</div>`;
                });
            }
            
            return html;
        }
        
        function renderMatchedNumbers(predicted, actual, lotteryType) {
            // 渲染中獎號碼（高亮顯示）
            let html = '';
            
            if (lotteryType === 'powercolor') {
                const predFirstArea = predicted['第一區'] || [];
                const actualFirstArea = actual['第一區'] || [];
                const predSecondArea = predicted['第二區'] || 0;
                const actualSecondArea = actual['第二區'] || 0;
                
                predFirstArea.forEach(num => {
                    const isMatched = actualFirstArea.includes(num);
                    const ballClass = isMatched ? 'matched-number' : 'predicted-number';
                    html += `<div class="number-ball ${ballClass}">${num}</div>`;
                });
                
                const secondMatched = predSecondArea === actualSecondArea;
                const secondClass = secondMatched ? 'matched-number' : 'predicted-number';
                html += `<div class="number-ball ${secondClass}" style="background: linear-gradient(135deg, #f093fb, #f5576c);">${predSecondArea}</div>`;
            }
            // 其他彩票類型的處理邏輯...
            
            return html;
        }
        
        function calculateMatches(predicted, actual, lotteryType) {
            // 計算中獎號碼數量
            let matches = 0;
            
            if (lotteryType === 'powercolor') {
                const predFirstArea = predicted['第一區'] || [];
                const actualFirstArea = actual['第一區'] || [];
                
                predFirstArea.forEach(num => {
                    if (actualFirstArea.includes(num)) matches++;
                });
                
                if (predicted['第二區'] === actual['第二區']) matches++;
            }
            // 其他彩票類型的處理邏輯...
            
            return matches;
        }
        
        function getResultClass(predicted, actual, lotteryType) {
            const matches = calculateMatches(predicted, actual, lotteryType);
            if (matches >= 4) return 'text-success';
            if (matches >= 2) return 'text-warning';
            return 'text-danger';
        }
        
        function getResultText(predicted, actual, lotteryType) {
            const matches = calculateMatches(predicted, actual, lotteryType);
            if (matches >= 4) return '預測成功';
            if (matches >= 2) return '部分命中';
            return '預測失敗';
        }
        
        // 事件處理函數
        function changePage(page) {
            if (page >= 1 && page <= totalPages && page !== currentPage) {
                currentPage = page;
                loadHistoryData();
            }
        }
        
        function resetFilters() {
            document.getElementById('filterForm').reset();
            document.getElementById('filterDays').value = '30';
            currentPage = 1;
            loadHistoryData();
        }
        
        function refreshHistory() {
            loadHistoryData();
        }
        
        function exportHistory() {
            // 實現匯出功能
            alert('匯出功能開發中...');
        }
        
        function viewDetails(recordId) {
            // 實現查看詳情功能
            alert(`查看記錄 ${recordId} 的詳情`);
        }
        
        function verifyPrediction(recordId) {
            // 實現驗證預測功能
            alert(`驗證記錄 ${recordId}`);
        }
        
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }
        
        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }
        
        function showError(message) {
            document.getElementById('errorText').textContent = message;
            document.getElementById('errorMessage').style.display = 'block';
        }
    </script>
</body>
</html>