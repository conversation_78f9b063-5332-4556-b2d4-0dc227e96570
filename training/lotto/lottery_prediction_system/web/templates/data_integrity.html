{% extends 'base.html' %}

{% block title %}數據完整性管理 - 彩票預測系統{% endblock %}

{% block extra_css %}
<style>
    .integrity-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .integrity-score {
        font-size: 3rem;
        font-weight: bold;
        margin: 10px 0;
    }
    
    .score-excellent { color: #28a745; }
    .score-good { color: #ffc107; }
    .score-fair { color: #fd7e14; }
    .score-poor { color: #dc3545; }
    
    .integrity-card {
        border-left: 4px solid #007bff;
        margin-bottom: 20px;
        transition: transform 0.2s ease;
    }
    
    .integrity-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    }
    
    .data-source-tag {
        display: inline-block;
        background: #e9ecef;
        color: #495057;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8em;
        margin: 2px;
    }
    
    .data-source-tag.official { background: #d4edda; color: #155724; }
    .data-source-tag.verified { background: #cce5ff; color: #004085; }
    .data-source-tag.manual { background: #fff3cd; color: #856404; }
    
    .fake-data-alert {
        background: linear-gradient(135deg, #f8d7da, #f5c6cb);
        border: 1px solid #f5c6cb;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .verification-form {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .recommendation-item {
        background: white;
        border-left: 4px solid #28a745;
        padding: 15px;
        margin: 10px 0;
        border-radius: 0 8px 8px 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .loading-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.7);
        z-index: 9999;
        align-items: center;
        justify-content: center;
    }
    
    .loading-content {
        background: white;
        padding: 30px;
        border-radius: 10px;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<!-- 載入覆蓋層 -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <div class="spinner-border text-primary" role="status"></div>
        <p class="mt-3">正在處理中...</p>
    </div>
</div>

<div class="container-fluid">
    <!-- 標題區域 -->
    <div class="integrity-header">
        <h1><i class="fas fa-shield-alt"></i> 數據完整性管理中心</h1>
        <p>確保所有數據都是真實、可靠、可追蹤的</p>
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="integrity-score" id="integrityScore">-</div>
                <small>整體完整性評分</small>
            </div>
            <div class="col-md-4">
                <div class="integrity-score" id="fakeDataBlocked">-</div>
                <small>已阻止假數據嘗試</small>
            </div>
            <div class="col-md-4">
                <div class="integrity-score" id="totalFingerprints">-</div>
                <small>數據指紋總數</small>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 控制面板 -->
        <div class="col-md-4">
            <div class="card integrity-card">
                <div class="card-header">
                    <h5><i class="fas fa-cogs"></i> 控制面板</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-primary w-100 mb-2" onclick="refreshIntegrityReport()">
                        <i class="fas fa-sync-alt"></i> 刷新完整性報告
                    </button>
                    <button class="btn btn-info w-100 mb-2" onclick="showVerificationForm()">
                        <i class="fas fa-plus-circle"></i> 手動添加驗證數據
                    </button>
                    <button class="btn btn-warning w-100 mb-2" onclick="showAuditTrail()">
                        <i class="fas fa-history"></i> 查看審計追蹤
                    </button>
                    <button class="btn btn-success w-100" onclick="exportIntegrityReport()">
                        <i class="fas fa-download"></i> 導出完整性報告
                    </button>
                </div>
            </div>

            <!-- 數據來源分佈 -->
            <div class="card integrity-card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> 數據來源分佈</h5>
                </div>
                <div class="card-body" id="sourceDistribution">
                    <p class="text-muted">載入中...</p>
                </div>
            </div>
        </div>

        <!-- 主要內容區域 -->
        <div class="col-md-8">
            <!-- 數據質量狀態 -->
            <div class="card integrity-card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> 數據質量狀態</h5>
                </div>
                <div class="card-body">
                    <div class="row" id="dataQualityStatus">
                        <div class="col-12">
                            <p class="text-muted">載入中...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 假數據檢測警報 -->
            <div class="fake-data-alert" id="fakeDataAlert" style="display: none;">
                <h6><i class="fas fa-exclamation-triangle text-danger"></i> 假數據檢測警報</h6>
                <p id="fakeDataMessage">檢測到假數據嘗試，系統已成功阻止。</p>
                <div id="fakeDataDetails"></div>
            </div>

            <!-- 改善建議 -->
            <div class="card integrity-card">
                <div class="card-header">
                    <h5><i class="fas fa-lightbulb"></i> 數據完整性建議</h5>
                </div>
                <div class="card-body" id="recommendations">
                    <p class="text-muted">載入中...</p>
                </div>
            </div>

            <!-- 數據驗證表單 -->
            <div class="verification-form" id="verificationForm" style="display: none;">
                <h6><i class="fas fa-plus-circle"></i> 手動添加驗證數據</h6>
                <form id="dataVerificationForm">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">彩票類型</label>
                            <select class="form-control" id="lotteryType" required>
                                <option value="powercolor">威力彩</option>
                                <option value="lotto649">大樂透</option>
                                <option value="dailycash">今彩539</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">期號</label>
                            <input type="number" class="form-control" id="period" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">開獎日期</label>
                            <input type="date" class="form-control" id="drawDate">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-8">
                            <label class="form-label">主號碼 (用逗號分隔)</label>
                            <input type="text" class="form-control" id="mainNumbers" 
                                   placeholder="例如: 5,12,18,25,30,35" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">特別號</label>
                            <input type="number" class="form-control" id="specialNumber">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">數據來源</label>
                            <input type="text" class="form-control" id="sourceInfo" 
                                   value="manual_verification" required>
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <button type="submit" class="btn btn-success me-2">
                                <i class="fas fa-check"></i> 驗證並添加
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="hideVerificationForm()">
                                <i class="fas fa-times"></i> 取消
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// 全域變數
let integrityData = {};

// 初始化頁面
document.addEventListener('DOMContentLoaded', function() {
    refreshIntegrityReport();
    
    // 設定表單提交事件
    document.getElementById('dataVerificationForm').addEventListener('submit', handleDataVerification);
});

// 刷新完整性報告
async function refreshIntegrityReport() {
    showLoading();
    
    try {
        const response = await fetch('/api/data/integrity_report');
        const data = await response.json();
        
        if (data.success) {
            integrityData = data.data;
            displayIntegrityReport(integrityData);
            
            // 檢查是否有假數據警報
            if (integrityData.fake_data_attempts_blocked > 0) {
                showFakeDataAlert(integrityData.fake_data_attempts_blocked);
            }
        } else {
            showAlert('danger', `載入完整性報告失敗: ${data.error}`);
        }
    } catch (error) {
        showAlert('danger', `請求失敗: ${error.message}`);
    } finally {
        hideLoading();
    }
}

// 顯示完整性報告
function displayIntegrityReport(data) {
    // 更新標題區域的分數
    const score = data.integrity_score || 0;
    document.getElementById('integrityScore').textContent = score + '/100';
    document.getElementById('integrityScore').className = 'integrity-score ' + getScoreClass(score);
    
    document.getElementById('fakeDataBlocked').textContent = data.fake_data_attempts_blocked || 0;
    document.getElementById('totalFingerprints').textContent = data.total_data_fingerprints || 0;
    
    // 顯示數據來源分佈
    displaySourceDistribution(data.source_distribution || {});
    
    // 顯示數據質量狀態
    displayDataQualityStatus(data);
    
    // 顯示改善建議
    displayRecommendations(data.recommendations || []);
}

// 獲取分數對應的CSS類
function getScoreClass(score) {
    if (score >= 90) return 'score-excellent';
    if (score >= 75) return 'score-good';
    if (score >= 60) return 'score-fair';
    return 'score-poor';
}

// 顯示數據來源分佈
function displaySourceDistribution(sources) {
    const container = document.getElementById('sourceDistribution');
    let html = '';
    
    if (Object.keys(sources).length === 0) {
        html = '<p class="text-muted">暫無數據來源記錄</p>';
    } else {
        for (const [source, count] of Object.entries(sources)) {
            const sourceClass = getSourceClass(source);
            html += `
                <div class="mb-2">
                    <span class="data-source-tag ${sourceClass}">${getSourceDisplayName(source)}</span>
                    <span class="float-end">${count} 筆</span>
                </div>
            `;
        }
    }
    
    container.innerHTML = html;
}

// 獲取來源對應的CSS類
function getSourceClass(source) {
    if (source.includes('official')) return 'official';
    if (source.includes('verified')) return 'verified';
    return 'manual';
}

// 獲取來源顯示名稱
function getSourceDisplayName(source) {
    const names = {
        'taiwan_lottery_official': '台灣彩券官方',
        'manual_verified_input': '手動驗證輸入',
        'historical_verified': '歷史驗證數據',
        'prediction_generated': '預測生成',
        'simulation_testing': '模擬測試'
    };
    return names[source] || source;
}

// 顯示數據質量狀態
function displayDataQualityStatus(data) {
    const container = document.getElementById('dataQualityStatus');
    const qualityLevel = data.data_quality_level || '未知';
    const lotteryStats = data.lottery_data_stats || {};
    
    let html = `
        <div class="col-md-6">
            <h6>整體質量等級</h6>
            <div class="alert alert-${getQualityAlertClass(qualityLevel)} mb-3">
                <i class="fas fa-award"></i> ${qualityLevel}
            </div>
        </div>
        <div class="col-md-6">
            <h6>生成時間</h6>
            <p class="text-muted">${data.timestamp || '未知'}</p>
        </div>
    `;
    
    if (Object.keys(lotteryStats).length > 0) {
        html += '<div class="col-12"><h6>彩票數據統計</h6><div class="row">';
        
        for (const [lottery, stats] of Object.entries(lotteryStats)) {
            html += `
                <div class="col-md-4 mb-2">
                    <div class="card">
                        <div class="card-body text-center">
                            <h6>${getLotteryDisplayName(lottery)}</h6>
                            <p class="mb-1"><strong>${stats.total_records || 0}</strong> 筆記錄</p>
                            <small class="text-muted">${stats.period_range || '無數據'}</small>
                        </div>
                    </div>
                </div>
            `;
        }
        
        html += '</div></div>';
    }
    
    container.innerHTML = html;
}

// 獲取質量等級對應的警報類型
function getQualityAlertClass(level) {
    if (level.includes('優秀')) return 'success';
    if (level.includes('良好')) return 'info';
    if (level.includes('一般')) return 'warning';
    return 'danger';
}

// 獲取彩票類型顯示名稱
function getLotteryDisplayName(lottery) {
    const names = {
        'powercolor': '威力彩',
        'lotto649': '大樂透',
        'dailycash': '今彩539'
    };
    return names[lottery] || lottery;
}

// 顯示改善建議
function displayRecommendations(recommendations) {
    const container = document.getElementById('recommendations');
    
    if (recommendations.length === 0) {
        container.innerHTML = '<p class="text-muted">系統運行良好，暫無改善建議。</p>';
        return;
    }
    
    let html = '';
    recommendations.forEach((rec, index) => {
        html += `
            <div class="recommendation-item">
                <i class="fas fa-arrow-right text-success me-2"></i>
                ${rec}
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// 顯示假數據警報
function showFakeDataAlert(count) {
    const alert = document.getElementById('fakeDataAlert');
    const message = document.getElementById('fakeDataMessage');
    
    message.textContent = `檢測到 ${count} 次假數據嘗試，系統已成功阻止。數據完整性得到保障。`;
    alert.style.display = 'block';
}

// 顯示數據驗證表單
function showVerificationForm() {
    document.getElementById('verificationForm').style.display = 'block';
    document.getElementById('drawDate').value = new Date().toISOString().split('T')[0];
}

// 隱藏數據驗證表單
function hideVerificationForm() {
    document.getElementById('verificationForm').style.display = 'none';
    document.getElementById('dataVerificationForm').reset();
}

// 處理數據驗證提交
async function handleDataVerification(event) {
    event.preventDefault();
    showLoading();
    
    try {
        const formData = {
            lottery_type: document.getElementById('lotteryType').value,
            period: parseInt(document.getElementById('period').value),
            main_numbers: document.getElementById('mainNumbers').value.split(',').map(n => parseInt(n.trim())),
            special_number: parseInt(document.getElementById('specialNumber').value) || null,
            date: document.getElementById('drawDate').value,
            source_info: document.getElementById('sourceInfo').value
        };
        
        const response = await fetch('/api/data/verify_record', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('success', '數據驗證並添加成功！');
            hideVerificationForm();
            
            // 刷新完整性報告
            setTimeout(() => refreshIntegrityReport(), 1000);
        } else {
            showAlert('danger', `數據驗證失敗: ${data.error}`);
        }
    } catch (error) {
        showAlert('danger', `提交失敗: ${error.message}`);
    } finally {
        hideLoading();
    }
}

// 顯示審計追蹤
function showAuditTrail() {
    showAlert('info', '審計追蹤功能開發中，敬請期待。');
}

// 導出完整性報告
function exportIntegrityReport() {
    if (!integrityData || Object.keys(integrityData).length === 0) {
        showAlert('warning', '請先載入完整性報告');
        return;
    }
    
    const reportData = JSON.stringify(integrityData, null, 2);
    const blob = new Blob([reportData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `data_integrity_report_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showAlert('success', '完整性報告已導出');
}

// 顯示載入狀態
function showLoading() {
    document.getElementById('loadingOverlay').style.display = 'flex';
}

// 隱藏載入狀態
function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

// 顯示警告訊息
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // 在頁面頂部插入警告
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    // 自動消失
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}
</script>
{% endblock %}