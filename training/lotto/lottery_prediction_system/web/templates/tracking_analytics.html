{% extends "base.html" %}

{% block title %}Phase 3 追蹤分析{% endblock %}

{% block extra_head %}
<style>
.tracking-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.analytics-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.analytics-card:hover {
    transform: translateY(-3px);
}

.filter-panel {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 1rem;
}

.chart-container {
    height: 300px;
    position: relative;
}

.loading-chart {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
    background: #f8f9fa;
    border-radius: 10px;
}

.strategy-comparison {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.strategy-item {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.strategy-ml { border-left-color: #007bff; }
.strategy-bp { border-left-color: #28a745; }
.strategy-stat { border-left-color: #ffc107; }
.strategy-cross { border-left-color: #dc3545; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 追蹤分析標題 -->
    <div class="tracking-header text-center">
        <h1><i class="fas fa-chart-line"></i> Phase 3 追蹤分析</h1>
        <p class="mb-0">預測性能監控 | 策略比較 | 趨勢分析</p>
    </div>

    <!-- 篩選面板 -->
    <div class="filter-panel">
        <div class="row">
            <div class="col-md-3">
                <label for="lotteryTypeFilter" class="form-label">彩票類型</label>
                <select class="form-select" id="lotteryTypeFilter">
                    <option value="">全部類型</option>
                    <option value="powercolor">威力彩</option>
                    <option value="lotto649">大樂透</option>
                    <option value="dailycash">今彩539</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="timeRangeFilter" class="form-label">時間範圍</label>
                <select class="form-select" id="timeRangeFilter">
                    <option value="7">最近7天</option>
                    <option value="30" selected>最近30天</option>
                    <option value="90">最近90天</option>
                    <option value="365">最近1年</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="strategyFilter" class="form-label">預測策略</label>
                <select class="form-select" id="strategyFilter">
                    <option value="">全部策略</option>
                    <option value="machine_learning">機器學習</option>
                    <option value="board_path">板路分析</option>
                    <option value="statistical">統計分析</option>
                    <option value="cross_learning">跨彩票學習</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button class="btn btn-primary w-100" onclick="updateAnalytics()">
                    <i class="fas fa-search"></i> 更新分析
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 左側：統計概覽 -->
        <div class="col-md-4">
            <!-- 總體統計 -->
            <div class="analytics-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-bar"></i> 總體統計</h5>
                </div>
                <div class="card-body">
                    <div class="stat-card">
                        <h3 id="totalPredictions">載入中...</h3>
                        <small>總預測次數</small>
                    </div>
                    <div class="stat-card">
                        <h3 id="overallAccuracy">載入中...</h3>
                        <small>整體準確度</small>
                    </div>
                    <div class="stat-card">
                        <h3 id="bestStrategy">載入中...</h3>
                        <small>最佳策略</small>
                    </div>
                </div>
            </div>

            <!-- 策略性能比較 -->
            <div class="analytics-card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-balance-scale"></i> 策略性能</h5>
                </div>
                <div class="card-body">
                    <div class="strategy-comparison" id="strategyComparison">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">載入中...</span>
                            </div>
                            <p class="mt-2">載入策略數據...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右側：圖表分析 -->
        <div class="col-md-8">
            <!-- 準確度趨勢圖 -->
            <div class="analytics-card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-line-chart"></i> 準確度趋势</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="accuracyTrendChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 策略比較圖 -->
            <div class="analytics-card">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-pie"></i> 策略表現分佈</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="strategyComparisonChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 預測結果分佈 -->
            <div class="analytics-card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-area"></i> 命中分佈</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="hitDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 詳細數據表 -->
    <div class="analytics-card">
        <div class="card-header bg-dark text-white">
            <h5 class="mb-0"><i class="fas fa-table"></i> 詳細預測記錄</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped" id="predictionRecordsTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>彩票類型</th>
                            <th>期數</th>
                            <th>預測策略</th>
                            <th>命中數</th>
                            <th>信心度</th>
                            <th>準確度</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="recordsTableBody">
                        <tr>
                            <td colspan="8" class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">載入中...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
let accuracyChart, strategyChart, hitChart;

$(document).ready(function() {
    initializeCharts();
    updateAnalytics();
});

function initializeCharts() {
    // 準確度趨勢圖
    const accuracyCtx = document.getElementById('accuracyTrendChart').getContext('2d');
    accuracyChart = new Chart(accuracyCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '準確度',
                data: [],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            }
        }
    });

    // 策略比較圖
    const strategyCtx = document.getElementById('strategyComparisonChart').getContext('2d');
    strategyChart = new Chart(strategyCtx, {
        type: 'doughnut',
        data: {
            labels: ['機器學習', '板路分析', '統計分析', '跨彩票學習'],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#007bff',
                    '#28a745', 
                    '#ffc107',
                    '#dc3545'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // 命中分佈圖
    const hitCtx = document.getElementById('hitDistributionChart').getContext('2d');
    hitChart = new Chart(hitCtx, {
        type: 'bar',
        data: {
            labels: ['0命中', '1命中', '2命中', '3命中', '4命中', '5命中', '6命中'],
            datasets: [{
                label: '次數',
                data: [],
                backgroundColor: 'rgba(102, 126, 234, 0.8)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function updateAnalytics() {
    const lotteryType = $('#lotteryTypeFilter').val();
    const timeRange = $('#timeRangeFilter').val();
    const strategy = $('#strategyFilter').val();

    // 更新統計概覽
    updateStatistics(lotteryType);
    
    // 更新策略性能比較
    updateStrategyComparison();
    
    // 更新圖表
    updateCharts();
    
    // 更新詳細記錄表
    updateRecordsTable(lotteryType, timeRange, strategy);
}

function updateStatistics(lotteryType) {
    // 模擬統計數據
    const stats = {
        totalPredictions: Math.floor(Math.random() * 500 + 100),
        overallAccuracy: (Math.random() * 0.3 + 0.6).toFixed(1) + '%',
        bestStrategy: ['機器學習', '板路分析', '跨彩票學習'][Math.floor(Math.random() * 3)]
    };

    $('#totalPredictions').text(stats.totalPredictions);
    $('#overallAccuracy').text(stats.overallAccuracy);
    $('#bestStrategy').text(stats.bestStrategy);
}

function updateStrategyComparison() {
    const strategies = [
        { name: '機器學習', accuracy: 72.3, class: 'strategy-ml' },
        { name: '板路分析', accuracy: 65.8, class: 'strategy-bp' },
        { name: '統計分析', accuracy: 68.1, class: 'strategy-stat' },
        { name: '跨彩票學習', accuracy: 78.9, class: 'strategy-cross' }
    ];

    let html = '';
    strategies.forEach(strategy => {
        html += `
            <div class="strategy-item ${strategy.class}">
                <h6>${strategy.name}</h6>
                <h4>${strategy.accuracy}%</h4>
                <small class="text-muted">準確度</small>
            </div>
        `;
    });

    $('#strategyComparison').html(html);
}

function updateCharts() {
    // 更新準確度趨勢圖
    const dates = [];
    const accuracyData = [];
    for (let i = 29; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        dates.push(date.toLocaleDateString());
        accuracyData.push(Math.random() * 20 + 60); // 60-80%範圍
    }
    
    accuracyChart.data.labels = dates;
    accuracyChart.data.datasets[0].data = accuracyData;
    accuracyChart.update();

    // 更新策略比較圖
    strategyChart.data.datasets[0].data = [72.3, 65.8, 68.1, 78.9];
    strategyChart.update();

    // 更新命中分佈圖
    hitChart.data.datasets[0].data = [
        Math.floor(Math.random() * 30 + 10),
        Math.floor(Math.random() * 40 + 20),
        Math.floor(Math.random() * 35 + 15),
        Math.floor(Math.random() * 25 + 10),
        Math.floor(Math.random() * 20 + 5),
        Math.floor(Math.random() * 15 + 3),
        Math.floor(Math.random() * 10 + 1)
    ];
    hitChart.update();
}

function updateRecordsTable(lotteryType, timeRange, strategy) {
    // 模擬預測記錄數據
    const records = [];
    const lotteryTypes = ['powercolor', 'lotto649', 'dailycash'];
    const strategies = ['機器學習', '板路分析', '統計分析', '跨彩票學習'];
    
    for (let i = 0; i < 20; i++) {
        const date = new Date();
        date.setDate(date.getDate() - Math.floor(Math.random() * 30));
        
        records.push({
            date: date.toLocaleDateString(),
            lotteryType: lotteryTypes[Math.floor(Math.random() * lotteryTypes.length)],
            period: String(240000 + Math.floor(Math.random() * 100)).padStart(6, '0'),
            strategy: strategies[Math.floor(Math.random() * strategies.length)],
            hitCount: Math.floor(Math.random() * 7),
            confidence: (Math.random() * 0.4 + 0.5).toFixed(2),
            accuracy: (Math.random() * 30 + 60).toFixed(1)
        });
    }

    let html = '';
    records.forEach((record, index) => {
        const lotteryNames = {
            'powercolor': '威力彩',
            'lotto649': '大樂透',
            'dailycash': '今彩539'
        };
        
        const accuracyClass = parseFloat(record.accuracy) >= 70 ? 'text-success' : 
                             parseFloat(record.accuracy) >= 60 ? 'text-warning' : 'text-danger';
        
        html += `
            <tr>
                <td>${record.date}</td>
                <td>${lotteryNames[record.lotteryType] || record.lotteryType}</td>
                <td>${record.period}</td>
                <td><span class="badge bg-primary">${record.strategy}</span></td>
                <td><strong>${record.hitCount}</strong></td>
                <td>${Math.round(record.confidence * 100)}%</td>
                <td class="${accuracyClass}"><strong>${record.accuracy}%</strong></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewDetails(${index})">
                        詳細
                    </button>
                </td>
            </tr>
        `;
    });

    $('#recordsTableBody').html(html);
}

function viewDetails(index) {
    // 顯示預測詳細信息
    alert('預測詳細信息功能待實現');
}
</script>
{% endblock %}