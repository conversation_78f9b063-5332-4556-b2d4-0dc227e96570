{% extends "base.html" %}

{% block title %}Phase 3 通用預測{% endblock %}

{% block extra_head %}
<style>
.phase3-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.prediction-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.prediction-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 1rem 1.5rem;
    font-weight: 600;
}

.strategy-selector {
    background: #f8f9ff;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 2px solid #e3e8ff;
}

.number-display {
    display: flex;
    gap: 8px;
    align-items: center;
    margin: 0.5rem 0;
}

.number-ball {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    font-size: 14px;
}

.main-number {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.special-number {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.confidence-meter {
    height: 6px;
    background: #e0e6ed;
    border-radius: 3px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.confidence-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.8s ease;
}

.confidence-high { background: linear-gradient(90deg, #00b09b, #96c93d); }
.confidence-medium { background: linear-gradient(90deg, #f2994a, #f2c94c); }
.confidence-low { background: linear-gradient(90deg, #ee5a24, #ffc048); }

.loading-spinner {
    display: none;
    text-align: center;
    padding: 2rem;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.feature-badge {
    display: inline-block;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    margin: 0.2rem;
    font-weight: 500;
}

.prediction-metadata {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.cross-learning-info {
    background: linear-gradient(135deg, #667eea20, #764ba220);
    border-left: 4px solid #667eea;
    padding: 1rem;
    margin-top: 1rem;
    border-radius: 0 8px 8px 0;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Phase 3 標題區域 -->
    <div class="phase3-header text-center">
        <h1><i class="fas fa-rocket"></i> Phase 3 通用預測框架</h1>
        <p class="mb-0">跨彩票學習技術 | 多算法融合 | 智能信心度評估</p>
    </div>

    <div class="row">
        <!-- 左側：預測控制面板 -->
        <div class="col-md-4">
            <div class="prediction-card">
                <div class="card-header">
                    <i class="fas fa-cog"></i> 預測配置
                </div>
                <div class="card-body">
                    <form id="universalPredictionForm">
                        <!-- 彩票類型選擇 -->
                        <div class="mb-3">
                            <label for="lotteryType" class="form-label">
                                <i class="fas fa-ticket-alt"></i> 彩票類型
                            </label>
                            <select class="form-select" id="lotteryType" name="lottery_type" required>
                                <option value="">請選擇彩票類型</option>
                                <option value="powercolor">威力彩</option>
                                <option value="lotto649">大樂透</option>
                                <option value="dailycash">今彩539</option>
                                <option value="all">全部彩票（跨彩票學習）</option>
                            </select>
                        </div>

                        <!-- 預測策略選擇 -->
                        <div class="strategy-selector">
                            <label class="form-label mb-3">
                                <i class="fas fa-brain"></i> 預測策略
                            </label>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="machine_learning" id="strategyML" checked>
                                <label class="form-check-label" for="strategyML">
                                    機器學習模型
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="board_path" id="strategyBP" checked>
                                <label class="form-check-label" for="strategyBP">
                                    板路分析
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="statistical" id="strategyStat" checked>
                                <label class="form-check-label" for="strategyStat">
                                    統計分析
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="cross_learning" id="strategyCL" checked>
                                <label class="form-check-label" for="strategyCL">
                                    跨彩票學習
                                </label>
                            </div>
                        </div>

                        <!-- 高級選項 -->
                        <div class="mb-3">
                            <label for="candidateCount" class="form-label">
                                <i class="fas fa-list-ol"></i> 候選組數
                            </label>
                            <select class="form-select" id="candidateCount" name="candidate_count">
                                <option value="3">3 組</option>
                                <option value="5" selected>5 組</option>
                                <option value="10">10 組</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="minConfidence" class="form-label">
                                <i class="fas fa-chart-line"></i> 最低信心度
                            </label>
                            <input type="range" class="form-range" id="minConfidence" name="min_confidence" 
                                   min="0.3" max="0.9" step="0.1" value="0.5">
                            <small class="text-muted">當前值: <span id="confidenceValue">50%</span></small>
                        </div>

                        <!-- 預測按鈕 -->
                        <button type="submit" class="btn btn-primary btn-lg w-100" id="predictBtn">
                            <i class="fas fa-magic"></i> 開始預測
                        </button>
                    </form>
                </div>
            </div>

            <!-- Phase 3 特性說明 -->
            <div class="prediction-card">
                <div class="card-header">
                    <i class="fas fa-star"></i> Phase 3 特性
                </div>
                <div class="card-body">
                    <div class="feature-badge">跨彩票學習</div>
                    <div class="feature-badge">多算法融合</div>
                    <div class="feature-badge">智能信心度</div>
                    <div class="feature-badge">實時優化</div>
                    <div class="feature-badge">策略適應</div>
                    <div class="feature-badge">性能追蹤</div>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i> 
                            Phase 3通用預測框架利用多種彩票間的相關性，
                            結合機器學習和統計分析，提供更準確的預測結果。
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右側：預測結果展示 -->
        <div class="col-md-8">
            <!-- 載入動畫 -->
            <div class="loading-spinner" id="loadingSpinner">
                <div class="spinner"></div>
                <p class="mt-2">Phase 3 預測引擎運算中...</p>
            </div>

            <!-- 預測結果容器 -->
            <div id="predictionResults" style="display: none;"></div>

            <!-- 空狀態 -->
            <div class="prediction-card text-center" id="emptyState">
                <div class="card-body py-5">
                    <i class="fas fa-magic fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">準備開始預測</h4>
                    <p class="text-muted">選擇彩票類型和預測策略，點擊「開始預測」按鈕</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 信心度滑桿更新
    $('#minConfidence').on('input', function() {
        const value = Math.round(this.value * 100);
        $('#confidenceValue').text(value + '%');
    });

    // 預測表單提交
    $('#universalPredictionForm').on('submit', function(e) {
        e.preventDefault();
        performUniversalPrediction();
    });

    function performUniversalPrediction() {
        // 顯示載入動畫
        $('#loadingSpinner').show();
        $('#predictionResults').hide();
        $('#emptyState').hide();
        $('#predictBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 預測中...');

        // 收集表單數據
        const formData = {
            lottery_type: $('#lotteryType').val(),
            candidate_count: parseInt($('#candidateCount').val()),
            min_confidence: parseFloat($('#minConfidence').val()),
            strategies: []
        };

        // 收集選中的策略
        $('input[type="checkbox"]:checked').each(function() {
            formData.strategies.push(this.value);
        });

        // 發送預測請求
        $.ajax({
            url: '/api/improved_prediction/' + formData.lottery_type,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({}),
            timeout: 60000,
            success: function(response) {
                if (response.success) {
                    // 轉換API響應格式以匹配顯示函數
                    const adaptedData = {
                        lottery_type: formData.lottery_type,
                        main_numbers: response.data.prediction.numbers,
                        special_number: response.data.prediction.special_number,
                        confidence: response.data.prediction.confidence,
                        period: response.data.prediction.period,
                        method: response.data.prediction.method,
                        timestamp: response.data.prediction.prediction_date || response.data.prediction.timestamp
                    };
                    
                    console.log('預測數據:', adaptedData); // 調試日誌
                    console.log('主號碼:', adaptedData.main_numbers); // 號碼調試
                    displayPredictionResults(adaptedData);
                } else {
                    showError('預測失敗: ' + (response.error || '未知錯誤'));
                }
            },
            error: function(xhr, status, error) {
                let errorMsg = '預測請求失敗';
                if (xhr.status === 503) {
                    errorMsg = 'Phase 3功能暫時不可用，請稍後再試';
                } else if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMsg = xhr.responseJSON.error;
                }
                showError(errorMsg);
            },
            complete: function() {
                $('#loadingSpinner').hide();
                $('#predictBtn').prop('disabled', false).html('<i class="fas fa-magic"></i> 開始預測');
            }
        });
    }

    function displayPredictionResults(data) {
        const resultsContainer = $('#predictionResults');
        resultsContainer.empty();

        // 檢查是否為跨彩票預測
        if (data.lottery_type === 'all' || Array.isArray(data.predictions)) {
            displayMultiplePredictions(data.predictions || [data]);
        } else {
            displaySinglePrediction(data);
        }

        resultsContainer.show();
    }

    function displaySinglePrediction(prediction) {
        const lotteryNames = {
            'powercolor': '威力彩',
            'lotto649': '大樂透',
            'dailycash': '今彩539'
        };

        let html = `
        <div class="prediction-card">
            <div class="card-header">
                <i class="fas fa-star"></i> ${lotteryNames[prediction.lottery_type] || prediction.lottery_type} 預測結果
                <span class="float-end">
                    <span class="badge bg-light text-dark">期號: ${prediction.period || 'N/A'}</span>
                </span>
            </div>
            <div class="card-body">
        `;

        // 顯示主預測結果
        if (prediction.main_numbers && prediction.main_numbers.length > 0) {
            html += `
                <div class="mb-3">
                    <h6><i class="fas fa-bullseye"></i> 推薦號碼</h6>
                    <div class="number-display">
            `;
            
            prediction.main_numbers.forEach(num => {
                html += `<span class="number-ball main-number">${num}</span>`;
            });

            if (prediction.special_number) {
                html += `<span class="mx-2">+</span><span class="number-ball special-number">${prediction.special_number}</span>`;
            }

            html += `</div>`;

            // 信心度顯示
            const confidence = prediction.confidence || 0.5;
            const confidenceClass = confidence >= 0.7 ? 'confidence-high' : 
                                  confidence >= 0.5 ? 'confidence-medium' : 'confidence-low';
            
            html += `
                    <div class="mt-2">
                        <small class="text-muted">信心度: ${Math.round(confidence * 100)}%</small>
                        <div class="confidence-meter">
                            <div class="confidence-fill ${confidenceClass}" style="width: ${confidence * 100}%"></div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 顯示候選結果
        if (prediction.candidates && prediction.candidates.length > 0) {
            html += `
                <div class="mb-3">
                    <h6><i class="fas fa-list"></i> 候選組合</h6>
            `;

            prediction.candidates.forEach((candidate, index) => {
                const confidence = candidate.confidence || 0.5;
                const confidenceClass = confidence >= 0.7 ? 'confidence-high' : 
                                      confidence >= 0.5 ? 'confidence-medium' : 'confidence-low';

                html += `
                    <div class="candidate-row mb-2 p-2 border rounded">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="number-display">
                `;
                
                candidate.main_numbers.forEach(num => {
                    html += `<span class="number-ball main-number" style="width:28px;height:28px;font-size:12px">${num}</span>`;
                });

                if (candidate.special_number) {
                    html += `<span class="mx-1">+</span><span class="number-ball special-number" style="width:28px;height:28px;font-size:12px">${candidate.special_number}</span>`;
                }

                html += `
                            </div>
                            <small class="text-muted">${Math.round(confidence * 100)}%</small>
                        </div>
                        <div class="confidence-meter mt-1" style="height:4px">
                            <div class="confidence-fill ${confidenceClass}" style="width: ${confidence * 100}%"></div>
                        </div>
                    </div>
                `;
            });

            html += `</div>`;
        }

        // 預測元數據
        html += `
                <div class="prediction-metadata">
                    <div class="row">
                        <div class="col-6">
                            <small><strong>預測方法:</strong><br>
                                <span class="badge bg-primary me-1">${prediction.method || '未知方法'}</span>
                            </small>
                        </div>
                        <div class="col-6">
                            <small><strong>預測時間:</strong><br>${prediction.timestamp ? new Date(prediction.timestamp).toLocaleString() : new Date().toLocaleString()}</small>
                        </div>
                    </div>
                </div>
        `;

        // 跨彩票學習信息
        if (prediction.cross_learning_info) {
            html += `
                <div class="cross-learning-info">
                    <h6><i class="fas fa-exchange-alt"></i> 跨彩票學習洞察</h6>
                    <small>${prediction.cross_learning_info}</small>
                </div>
            `;
        }

        html += `
            </div>
        </div>
        `;

        $('#predictionResults').html(html);
    }

    function displayMultiplePredictions(predictions) {
        let html = '';
        
        predictions.forEach(prediction => {
            // 複用單一預測的顯示邏輯
            // 這裡可以展開實現多預測結果的並排顯示
        });

        $('#predictionResults').html(html);
    }

    function showError(message) {
        const errorHtml = `
            <div class="prediction-card">
                <div class="card-body text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                    <h5 class="text-danger">預測失敗</h5>
                    <p class="text-muted">${message}</p>
                    <button class="btn btn-outline-primary" onclick="location.reload()">
                        <i class="fas fa-redo"></i> 重新嘗試
                    </button>
                </div>
            </div>
        `;
        $('#predictionResults').html(errorHtml).show();
    }
});
</script>
{% endblock %}