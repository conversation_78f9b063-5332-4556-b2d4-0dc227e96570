<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>預測結果測試頁面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h2>Phase 3 預測結果測試</h2>
        
        <button id="testBtn" class="btn btn-primary mb-3">測試威力彩預測</button>
        
        <div id="results" class="mt-3"></div>
        
        <div id="debug" class="mt-3">
            <h5>調試信息：</h5>
            <pre id="debugInfo"></pre>
        </div>
    </div>

    <script>
    $('#testBtn').click(function() {
        $('#results').html('<div class="spinner-border" role="status"></div> 預測中...');
        $('#debugInfo').text('發送API請求...');
        
        $.ajax({
            url: '/api/improved_prediction/powercolor',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({}),
            success: function(response) {
                $('#debugInfo').text(JSON.stringify(response, null, 2));
                
                if (response.success) {
                    const prediction = response.data.prediction;
                    
                    let html = `
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5>威力彩預測結果 - 期號: ${prediction.period}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <h6>推薦號碼:</h6>
                                    <div class="d-flex gap-2 mb-2">
                    `;
                    
                    // 顯示主號碼
                    prediction.numbers.forEach(num => {
                        html += `<span class="badge bg-primary fs-6" style="width:40px;height:40px;display:flex;align-items:center;justify-content:center;border-radius:50%;">${num}</span>`;
                    });
                    
                    // 顯示特別號
                    if (prediction.special_number) {
                        html += `<span class="mx-2">+</span>
                                <span class="badge bg-warning fs-6" style="width:40px;height:40px;display:flex;align-items:center;justify-content:center;border-radius:50%;">${prediction.special_number}</span>`;
                    }
                    
                    html += `
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <h6>信心度: ${Math.round(prediction.confidence * 100)}%</h6>
                                    <div class="progress">
                                        <div class="progress-bar bg-success" style="width: ${prediction.confidence * 100}%"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <small><strong>預測方法:</strong> ${prediction.method}</small>
                                </div>
                                <div class="col-md-6">
                                    <small><strong>預測時間:</strong> ${prediction.prediction_date}</small>
                                </div>
                            </div>
                        </div>
                    </div>`;
                    
                    $('#results').html(html);
                } else {
                    $('#results').html(`<div class="alert alert-danger">預測失敗: ${response.error}</div>`);
                }
            },
            error: function(xhr, status, error) {
                $('#debugInfo').text(`錯誤: ${status} - ${error}\n響應: ${xhr.responseText}`);
                $('#results').html(`<div class="alert alert-danger">API調用失敗: ${error}</div>`);
            }
        });
    });
    </script>
</body>
</html>