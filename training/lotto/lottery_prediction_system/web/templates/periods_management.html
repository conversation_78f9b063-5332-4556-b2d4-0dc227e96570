<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>期號管理 - 彩票預測系統</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .period-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .period-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .period-predicted {
            border-left: 4px solid #28a745;
        }
        .period-not-predicted {
            border-left: 4px solid #dc3545;
        }
        .period-partial {
            border-left: 4px solid #ffc107;
        }
        .year-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .stats-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
        }
        .filter-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    {% include 'navbar.html' %}

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-calendar-alt me-2"></i>期號管理
                </h1>
            </div>
        </div>

        <!-- 統計信息 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h3 id="totalPeriods">-</h3>
                        <p class="mb-0">總期數</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h3 id="predictedPeriods">-</h3>
                        <p class="mb-0">已預測期數</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h3 id="unpredictedPeriods">-</h3>
                        <p class="mb-0">未預測期數</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h3 id="predictionRate">-</h3>
                        <p class="mb-0">預測覆蓋率</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 篩選和操作區域 -->
        <div class="filter-section">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">彩票類型</label>
                    <select class="form-select" id="lotteryType">
                        <option value="powercolor">威力彩</option>
                        <option value="lotto649">大樂透</option>
                        <option value="dailycash">今彩539</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">年份篩選</label>
                    <select class="form-select" id="yearFilter">
                        <option value="all">所有年份</option>
                        <option value="2025">2025年</option>
                        <option value="2024">2024年</option>
                        <option value="2023">2023年</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">預測狀態</label>
                    <select class="form-select" id="predictionStatus">
                        <option value="all">全部</option>
                        <option value="predicted">已預測</option>
                        <option value="not_predicted">未預測</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">操作</label>
                    <div class="d-grid">
                        <button class="btn btn-primary" onclick="loadPeriods()">
                            <i class="fas fa-sync-alt me-1"></i>重新載入
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-md-6">
                    <button class="btn btn-success me-2" onclick="showSequentialPredictModal()">
                        <i class="fas fa-play me-1"></i>順序預測
                    </button>
                    <button class="btn btn-warning me-2" onclick="showClearPredictionsModal()">
                        <i class="fas fa-trash me-1"></i>清理預測記錄
                    </button>
                    <button class="btn btn-info" onclick="exportPeriodData()">
                        <i class="fas fa-download me-1"></i>匯出資料
                    </button>
                </div>
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchPeriod" placeholder="搜尋期號...">
                        <button class="btn btn-outline-secondary" onclick="searchPeriod()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 期號列表 -->
        <div id="periodsContainer">
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">載入中...</span>
                </div>
                <p class="mt-2">載入期號資料中...</p>
            </div>
        </div>

        <!-- 分頁 -->
        <nav aria-label="期號分頁" class="mt-4">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分頁按鈕將由JavaScript生成 -->
            </ul>
        </nav>
    </div>

    <!-- 順序預測模態框 -->
    <div class="modal fade" id="sequentialPredictModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">順序預測設定</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">開始期號</label>
                        <input type="text" class="form-control" id="startPeriod" placeholder="例如: 114000001">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">結束期號（可選）</label>
                        <input type="text" class="form-control" id="endPeriod" placeholder="留空表示預測到最新期">
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-1"></i>
                        順序預測將從指定期號開始，每期只使用該期之前的歷史資料進行預測。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="startSequentialPredict()">開始預測</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 清理預測記錄模態框 -->
    <div class="modal fade" id="clearPredictionsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">清理預測記錄</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        <strong>警告：</strong>此操作將刪除所有預測記錄，且無法復原！
                    </div>
                    <p>請確認您要清理的彩票類型：</p>
                    <select class="form-select" id="clearLotteryType">
                        <option value="powercolor">威力彩</option>
                        <option value="lotto649">大樂透</option>
                        <option value="dailycash">今彩539</option>
                    </select>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="clearPredictions()">確認清理</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPage = 1;
        let periodsPerPage = 50;
        let allPeriods = [];
        let allPredictions = [];
        let filteredPeriods = [];

        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadPeriods();
            
            // 綁定篩選器事件
            document.getElementById('lotteryType').addEventListener('change', loadPeriods);
            document.getElementById('yearFilter').addEventListener('change', applyFilters);
            document.getElementById('predictionStatus').addEventListener('change', applyFilters);
            
            // 綁定搜尋事件
            document.getElementById('searchPeriod').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchPeriod();
                }
            });
        });

        // 載入期號資料
        async function loadPeriods() {
            const lotteryType = document.getElementById('lotteryType').value;
            
            try {
                // 載入期號列表
                const periodsResponse = await fetch(`/api/periods/${lotteryType}?limit=1000`);
                const periodsData = await periodsResponse.json();
                
                if (periodsData.success) {
                    allPeriods = periodsData.periods.reverse(); // 轉為升序
                } else {
                    throw new Error(periodsData.error);
                }
                
                // 載入預測記錄
                const predictionsResponse = await fetch(`/api/history?lottery_type=${lotteryType}&per_page=1000`);
                const predictionsData = await predictionsResponse.json();
                
                if (predictionsData.success) {
                    allPredictions = predictionsData.records;
                } else {
                    allPredictions = [];
                }
                
                // 應用篩選器
                applyFilters();
                
            } catch (error) {
                console.error('載入期號資料失敗:', error);
                document.getElementById('periodsContainer').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        載入期號資料失敗：${error.message}
                    </div>
                `;
            }
        }

        // 應用篩選器
        function applyFilters() {
            const yearFilter = document.getElementById('yearFilter').value;
            const statusFilter = document.getElementById('predictionStatus').value;
            
            filteredPeriods = allPeriods.filter(period => {
                // 年份篩選
                if (yearFilter !== 'all') {
                    const yearCode = {
                        '2025': '114',
                        '2024': '113',
                        '2023': '112'
                    }[yearFilter];
                    
                    if (!String(period).startsWith(yearCode)) {
                        return false;
                    }
                }
                
                // 預測狀態篩選
                if (statusFilter !== 'all') {
                    const hasPrediction = allPredictions.some(pred => pred.period === String(period));
                    
                    if (statusFilter === 'predicted' && !hasPrediction) {
                        return false;
                    }
                    if (statusFilter === 'not_predicted' && hasPrediction) {
                        return false;
                    }
                }
                
                return true;
            });
            
            updateStatistics();
            renderPeriods();
        }

        // 更新統計信息
        function updateStatistics() {
            const totalPeriods = filteredPeriods.length;
            const predictedPeriods = filteredPeriods.filter(period => 
                allPredictions.some(pred => pred.period === String(period))
            ).length;
            const unpredictedPeriods = totalPeriods - predictedPeriods;
            const predictionRate = totalPeriods > 0 ? (predictedPeriods / totalPeriods * 100).toFixed(1) : 0;
            
            document.getElementById('totalPeriods').textContent = totalPeriods;
            document.getElementById('predictedPeriods').textContent = predictedPeriods;
            document.getElementById('unpredictedPeriods').textContent = unpredictedPeriods;
            document.getElementById('predictionRate').textContent = predictionRate + '%';
        }

        // 渲染期號列表
        function renderPeriods() {
            const startIndex = (currentPage - 1) * periodsPerPage;
            const endIndex = startIndex + periodsPerPage;
            const pageData = filteredPeriods.slice(startIndex, endIndex);
            
            if (pageData.length === 0) {
                document.getElementById('periodsContainer').innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">沒有符合條件的期號</h5>
                    </div>
                `;
                return;
            }
            
            // 按年份分組
            const groupedByYear = {};
            pageData.forEach(period => {
                const yearCode = String(period).substring(0, 3);
                const year = {
                    '114': '2025',
                    '113': '2024',
                    '112': '2023'
                }[yearCode] || '其他';
                
                if (!groupedByYear[year]) {
                    groupedByYear[year] = [];
                }
                groupedByYear[year].push(period);
            });
            
            let html = '';
            
            Object.keys(groupedByYear).sort().reverse().forEach(year => {
                const periods = groupedByYear[year];
                
                html += `
                    <div class="year-section p-3 mb-4">
                        <h4 class="mb-3">
                            <i class="fas fa-calendar me-2"></i>${year}年 (${periods.length} 期)
                        </h4>
                        <div class="row">
                `;
                
                periods.forEach(period => {
                    const hasPrediction = allPredictions.some(pred => pred.period === String(period));
                    const predictionCount = allPredictions.filter(pred => pred.period === String(period)).length;
                    
                    let statusClass = 'period-not-predicted';
                    let statusIcon = 'fas fa-times-circle text-danger';
                    let statusText = '未預測';
                    
                    if (hasPrediction) {
                        statusClass = 'period-predicted';
                        statusIcon = 'fas fa-check-circle text-success';
                        statusText = `已預測 (${predictionCount}組)`;
                    }
                    
                    html += `
                        <div class="col-md-4 col-lg-3 mb-3">
                            <div class="card period-card ${statusClass}" onclick="viewPeriodDetail('${period}')">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="card-title mb-0">第 ${period} 期</h6>
                                        <i class="${statusIcon}"></i>
                                    </div>
                                    <small class="text-muted">${statusText}</small>
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                html += `
                        </div>
                    </div>
                `;
            });
            
            document.getElementById('periodsContainer').innerHTML = html;
            renderPagination();
        }

        // 渲染分頁
        function renderPagination() {
            const totalPages = Math.ceil(filteredPeriods.length / periodsPerPage);
            
            if (totalPages <= 1) {
                document.getElementById('pagination').innerHTML = '';
                return;
            }
            
            let html = '';
            
            // 上一頁
            html += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            `;
            
            // 頁碼
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                html += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                    </li>
                `;
            }
            
            // 下一頁
            html += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            `;
            
            document.getElementById('pagination').innerHTML = html;
        }

        // 切換頁面
        function changePage(page) {
            const totalPages = Math.ceil(filteredPeriods.length / periodsPerPage);
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                renderPeriods();
            }
        }

        // 搜尋期號
        function searchPeriod() {
            const searchTerm = document.getElementById('searchPeriod').value.trim();
            if (searchTerm) {
                const foundIndex = filteredPeriods.findIndex(period => 
                    String(period).includes(searchTerm)
                );
                
                if (foundIndex !== -1) {
                    currentPage = Math.floor(foundIndex / periodsPerPage) + 1;
                    renderPeriods();
                    
                    // 高亮顯示找到的期號
                    setTimeout(() => {
                        const cards = document.querySelectorAll('.period-card');
                        cards.forEach(card => {
                            if (card.textContent.includes(searchTerm)) {
                                card.style.backgroundColor = '#fff3cd';
                                card.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            }
                        });
                    }, 100);
                } else {
                    alert('找不到指定的期號');
                }
            }
        }

        // 查看期號詳情
        function viewPeriodDetail(period) {
            window.open(`/history?period=${period}`, '_blank');
        }

        // 顯示順序預測模態框
        function showSequentialPredictModal() {
            const modal = new bootstrap.Modal(document.getElementById('sequentialPredictModal'));
            modal.show();
        }

        // 開始順序預測
        function startSequentialPredict() {
            const startPeriod = document.getElementById('startPeriod').value.trim();
            const endPeriod = document.getElementById('endPeriod').value.trim();
            
            if (!startPeriod) {
                alert('請輸入開始期號');
                return;
            }
            
            // 這裡應該調用後端API開始順序預測
            alert('順序預測功能開發中...');
        }

        // 顯示清理預測記錄模態框
        function showClearPredictionsModal() {
            const modal = new bootstrap.Modal(document.getElementById('clearPredictionsModal'));
            modal.show();
        }

        // 清理預測記錄
        function clearPredictions() {
            const lotteryType = document.getElementById('clearLotteryType').value;
            
            if (confirm('確定要清理所有預測記錄嗎？此操作無法復原！')) {
                // 這裡應該調用後端API清理預測記錄
                alert('清理功能開發中...');
            }
        }

        // 匯出期號資料
        function exportPeriodData() {
            const lotteryType = document.getElementById('lotteryType').value;
            const data = {
                lottery_type: lotteryType,
                periods: filteredPeriods,
                predictions: allPredictions,
                export_time: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${lotteryType}_periods_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>