{% extends 'base.html' %}

{% block title %}可觀察預測追蹤 - 彩票預測系統{% endblock %}

{% block extra_css %}
<style>
    .tracking-card {
        transition: all 0.3s ease;
        border-left: 4px solid #007bff;
    }
    
    .tracking-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    }
    
    .confidence-bar {
        height: 8px;
        border-radius: 4px;
        overflow: hidden;
    }
    
    .status-badge {
        font-size: 0.8em;
        padding: 0.25rem 0.5rem;
    }
    
    .insight-item {
        border-left: 3px solid #28a745;
        padding-left: 1rem;
        margin: 0.5rem 0;
    }
    
    .pattern-number {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        margin: 0.2rem;
        display: inline-block;
        font-weight: bold;
    }
    
    .dashboard-metric {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        text-align: center;
        border-radius: 12px;
        padding: 1.5rem;
    }
    
    .loading-spinner {
        display: none;
        width: 2rem;
        height: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-chart-line"></i> 可觀察預測追蹤系統</h2>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-primary" onclick="startTrackingSession()">
                    <i class="fas fa-play"></i> 啟動追蹤
                </button>
                <button type="button" class="btn btn-success" onclick="updateResults()">
                    <i class="fas fa-refresh"></i> 更新結果
                </button>
                <button type="button" class="btn btn-warning" onclick="updateLotteryData()">
                    <i class="fas fa-database"></i> 更新開獎數據
                </button>
                <button type="button" class="btn btn-info" onclick="generateReport()">
                    <i class="fas fa-file-alt"></i> 生成報告
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 彩票類型選擇 -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-cog"></i> 設定</h5>
            </div>
            <div class="card-body">
                <div class="form-group mb-3">
                    <label for="lotteryType">彩票類型</label>
                    <select id="lotteryType" class="form-select">
                        <option value="powercolor">威力彩</option>
                        <option value="lotto649">大樂透</option>
                        <option value="dailycash">今彩539</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="reportPeriods">報告分析期數</label>
                    <select id="reportPeriods" class="form-select">
                        <option value="10">最近10期</option>
                        <option value="20" selected>最近20期</option>
                        <option value="30">最近30期</option>
                        <option value="50">最近50期</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 當前追蹤狀態 -->
    <div class="col-md-8">
        <div class="card tracking-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-eye"></i> 當前追蹤狀態</h5>
                <span id="trackingStatus" class="badge bg-secondary">未啟動</span>
            </div>
            <div class="card-body" id="trackingInfo">
                <p class="text-muted">點擊「啟動追蹤」開始可觀察預測追蹤會話</p>
            </div>
        </div>
    </div>
</div>

<!-- 觀察儀表板 -->
<div class="row" id="observationDashboard" style="display: none;">
    <div class="col-12">
        <h4><i class="fas fa-dashboard"></i> 觀察儀表板</h4>
    </div>
    
    <!-- 關鍵指標 -->
    <div class="col-md-3">
        <div class="dashboard-metric">
            <div class="h3" id="totalPredictions">-</div>
            <div>總預測數</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="dashboard-metric">
            <div class="h3" id="successRate">-</div>
            <div>成功率</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="dashboard-metric">
            <div class="h3" id="avgConfidence">-</div>
            <div>平均信心度</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="dashboard-metric">
            <div class="h3" id="trendDirection">-</div>
            <div>趋势方向</div>
        </div>
    </div>
</div>

<!-- 當前預測詳情 -->
<div class="row mt-4" id="currentPrediction" style="display: none;">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-crystal-ball"></i> 當前預測</h5>
            </div>
            <div class="card-body" id="predictionDetails">
                <!-- 預測詳情將通過 JavaScript 動態載入 -->
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-lightbulb"></i> 觀察洞察</h5>
            </div>
            <div class="card-body" id="observationInsights">
                <!-- 洞察將通過 JavaScript 動態載入 -->
            </div>
        </div>
    </div>
</div>

<!-- 建議與推薦 -->
<div class="row mt-4" id="recommendations" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-thumbs-up"></i> 使用建議</h5>
            </div>
            <div class="card-body" id="recommendationContent">
                <!-- 建議將通過 JavaScript 動態載入 -->
            </div>
        </div>
    </div>
</div>

<!-- 追蹤報告 -->
<div class="row mt-4" id="trackingReport" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar"></i> 追蹤報告</h5>
            </div>
            <div class="card-body" id="reportContent">
                <!-- 報告內容將通過 JavaScript 動態載入 -->
            </div>
        </div>
    </div>
</div>

<!-- 載入指示器 -->
<div class="text-center mt-4" id="loadingIndicator" style="display: none;">
    <div class="spinner-border text-primary loading-spinner" role="status">
        <span class="visually-hidden">載入中...</span>
    </div>
    <p class="mt-2">處理中，請稍候...</p>
</div>

<script>
// 全域變數
let currentLotteryType = 'powercolor';
let isTrackingActive = false;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    loadObservationDashboard();
    
    // 監聽彩票類型變更
    document.getElementById('lotteryType').addEventListener('change', function() {
        currentLotteryType = this.value;
        loadObservationDashboard();
    });
});

// 啟動追蹤會話
async function startTrackingSession() {
    showLoading(true);
    
    try {
        const response = await fetch(`/api/observable/start_tracking/${currentLotteryType}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            displayTrackingSession(result.data);
            loadObservationDashboard();
            showAlert('success', '追蹤會話啟動成功！');
            isTrackingActive = true;
            updateTrackingStatus('追蹤中', 'success');
        } else {
            showAlert('danger', '追蹤會話啟動失敗: ' + result.error);
        }
    } catch (error) {
        showAlert('danger', '請求失敗: ' + error.message);
    } finally {
        showLoading(false);
    }
}

// 載入觀察儀表板
async function loadObservationDashboard() {
    try {
        const response = await fetch(`/api/observable/dashboard/${currentLotteryType}`);
        const result = await response.json();
        
        if (result.success) {
            displayObservationDashboard(result.data);
        }
    } catch (error) {
        console.error('載入儀表板失敗:', error);
    }
}

// 更新預測結果
async function updateResults() {
    showLoading(true);
    
    try {
        const response = await fetch(`/api/observable/update_results/${currentLotteryType}`, {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (result.success) {
            const updatedCount = result.data.updated_predictions || 0;
            showAlert('success', `預測結果更新完成，更新了 ${updatedCount} 條記錄`);
            loadObservationDashboard();
        } else {
            showAlert('info', result.data?.message || '暫無新結果需要更新');
        }
    } catch (error) {
        showAlert('danger', '更新失敗: ' + error.message);
    } finally {
        showLoading(false);
    }
}

// 更新開獎數據
async function updateLotteryData() {
    showLoading(true);
    
    try {
        const response = await fetch(`/api/data/check_update/${currentLotteryType}?threshold_hours=1`, {
            method: 'GET'
        });
        
        const result = await response.json();
        
        if (result.success) {
            if (result.data.updated) {
                const newRecords = result.data.new_records_count || 0;
                showAlert('success', `開獎數據更新完成！新增 ${newRecords} 筆記錄`);
                
                // 自動更新預測結果
                setTimeout(async () => {
                    await updateResults();
                }, 1000);
            } else {
                showAlert('info', '開獎數據已是最新：' + result.data.message);
            }
            
            // 顯示數據狀態
            displayDataUpdateStatus(result.data);
        } else {
            showAlert('danger', '數據更新失敗: ' + result.error);
        }
    } catch (error) {
        showAlert('danger', '數據更新請求失敗: ' + error.message);
    } finally {
        showLoading(false);
    }
}

// 顯示數據更新狀態
function displayDataUpdateStatus(updateData) {
    let statusMessage = '';
    let statusType = 'info';
    
    if (updateData.updated) {
        statusMessage = `✅ 數據已更新至期號 ${updateData.latest_period_after}`;
        statusType = 'success';
    } else {
        statusMessage = `ℹ️ 數據保持最新 (最後更新: ${updateData.last_update || '未知'})`;
        statusType = 'info';
    }
    
    // 創建狀態顯示區域
    let statusContainer = document.getElementById('dataUpdateStatus');
    if (!statusContainer) {
        statusContainer = document.createElement('div');
        statusContainer.id = 'dataUpdateStatus';
        statusContainer.className = 'mt-2';
        
        const trackingCard = document.querySelector('.tracking-card .card-body');
        if (trackingCard) {
            trackingCard.appendChild(statusContainer);
        }
    }
    
    statusContainer.innerHTML = `
        <div class="alert alert-${statusType} alert-sm mb-0">
            <small><i class="fas fa-database"></i> ${statusMessage}</small>
        </div>
    `;
}

// 生成追蹤報告
async function generateReport() {
    showLoading(true);
    const periods = document.getElementById('reportPeriods').value;
    
    try {
        const response = await fetch(`/api/observable/report/${currentLotteryType}?periods=${periods}`);
        const result = await response.json();
        
        if (result.success) {
            displayTrackingReport(result.data);
            showAlert('success', '追蹤報告生成完成');
        } else {
            showAlert('danger', '報告生成失敗: ' + result.error);
        }
    } catch (error) {
        showAlert('danger', '報告生成失敗: ' + error.message);
    } finally {
        showLoading(false);
    }
}

// 顯示追蹤會話資訊
function displayTrackingSession(data) {
    const session = data.tracking_session;
    const prediction = data.prediction_summary;
    
    const trackingInfo = document.getElementById('trackingInfo');
    trackingInfo.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>會話資訊</h6>
                <p><strong>會話ID:</strong> ${session.session_id}</p>
                <p><strong>期號:</strong> ${session.period}</p>
                <p><strong>預測時間:</strong> ${session.prediction_date}</p>
                <p><strong>狀態:</strong> <span class="badge bg-success">追蹤中</span></p>
            </div>
            <div class="col-md-6">
                <h6>預測號碼</h6>
                <div class="mb-2">
                    ${prediction.numbers.map(num => `<span class="lottery-number">${num}</span>`).join('')}
                    ${prediction.special ? `<span class="lottery-number special-number">${prediction.special}</span>` : ''}
                </div>
                <p><strong>信心度:</strong> ${(prediction.confidence * 100).toFixed(1)}%</p>
                <p><strong>穩定性:</strong> ${(prediction.stability * 100).toFixed(1)}%</p>
            </div>
        </div>
    `;
}

// 顯示觀察儀表板
function displayObservationDashboard(dashboardData) {
    // 顯示儀表板區域
    document.getElementById('observationDashboard').style.display = 'block';
    document.getElementById('currentPrediction').style.display = 'block';
    document.getElementById('recommendations').style.display = 'block';
    
    // 更新關鍵指標
    if (dashboardData.tracking_statistics) {
        const stats = dashboardData.tracking_statistics;
        document.getElementById('totalPredictions').textContent = stats.total_predictions || '-';
        document.getElementById('successRate').textContent = stats.success_rate || '-';
        document.getElementById('avgConfidence').textContent = stats.average_confidence || '-';
    }
    
    if (dashboardData.trend_analysis) {
        const trend = dashboardData.trend_analysis;
        const trendElement = document.getElementById('trendDirection');
        const trendText = {
            'improving': '上升',
            'stable': '穩定',
            'declining': '下降'
        }[trend.trend_direction] || '-';
        trendElement.textContent = trendText;
    }
    
    // 顯示當前預測
    if (dashboardData.current_prediction) {
        displayCurrentPrediction(dashboardData.current_prediction);
    }
    
    // 顯示觀察洞察
    if (dashboardData.observation_insights) {
        displayObservationInsights(dashboardData.observation_insights);
    }
    
    // 顯示建議
    if (dashboardData.actionable_recommendations) {
        displayRecommendations(dashboardData.actionable_recommendations);
    }
}

// 顯示當前預測詳情
function displayCurrentPrediction(prediction) {
    const predictionDetails = document.getElementById('predictionDetails');
    predictionDetails.innerHTML = `
        <div class="mb-3">
            <h6>期號 ${prediction.period}</h6>
            <div class="mb-2">
                ${prediction.numbers.map(num => `<span class="lottery-number">${num}</span>`).join('')}
                ${prediction.special ? `<span class="lottery-number special-number">${prediction.special}</span>` : ''}
            </div>
        </div>
        <div class="row">
            <div class="col-6">
                <small class="text-muted">信心度</small>
                <div class="confidence-bar bg-light">
                    <div class="bg-success" style="width: ${prediction.confidence}; height: 100%;"></div>
                </div>
            </div>
            <div class="col-6">
                <small class="text-muted">狀態</small>
                <div><span class="badge bg-info">${prediction.status}</span></div>
            </div>
        </div>
        <div class="mt-2">
            <small class="text-muted">預測日期: ${prediction.prediction_date}</small>
        </div>
        <div class="mt-1">
            <small class="text-muted">命中情況: ${prediction.hit_rate}</small>
        </div>
    `;
}

// 顯示觀察洞察
function displayObservationInsights(insights) {
    const insightsElement = document.getElementById('observationInsights');
    insightsElement.innerHTML = insights.map(insight => 
        `<div class="insight-item">${insight}</div>`
    ).join('');
}

// 顯示建議
function displayRecommendations(recommendations) {
    const recommendationContent = document.getElementById('recommendationContent');
    recommendationContent.innerHTML = `
        <ul class="list-unstyled">
            ${recommendations.map(rec => `<li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>${rec}</li>`).join('')}
        </ul>
    `;
}

// 顯示追蹤報告
function displayTrackingReport(reportData) {
    document.getElementById('trackingReport').style.display = 'block';
    const reportContent = document.getElementById('reportContent');
    
    reportContent.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>執行摘要</h6>
                <div class="mb-3">
                    <strong>分析期間:</strong> ${reportData.analysis_period}<br>
                    <strong>生成時間:</strong> ${reportData.generation_time}<br>
                </div>
                ${reportData.executive_summary ? `<div class="alert alert-info">${reportData.executive_summary}</div>` : ''}
            </div>
            <div class="col-md-6">
                <h6>性能指標</h6>
                ${reportData.performance_metrics ? Object.entries(reportData.performance_metrics).map(([key, value]) => 
                    `<div><strong>${key}:</strong> ${value}</div>`
                ).join('') : ''}
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <h6>追蹤洞察</h6>
                ${reportData.tracking_insights ? reportData.tracking_insights.map(insight => 
                    `<div class="insight-item">${insight}</div>`
                ).join('') : ''}
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <h6>策略建議</h6>
                ${reportData.strategic_recommendations ? reportData.strategic_recommendations.map(rec => 
                    `<div class="mb-2"><i class="fas fa-lightbulb text-warning me-2"></i>${rec}</div>`
                ).join('') : ''}
            </div>
        </div>
    `;
}

// 更新追蹤狀態
function updateTrackingStatus(status, type = 'secondary') {
    const statusElement = document.getElementById('trackingStatus');
    statusElement.textContent = status;
    statusElement.className = `badge bg-${type}`;
}

// 顯示載入指示器
function showLoading(show) {
    document.getElementById('loadingIndicator').style.display = show ? 'block' : 'none';
}

// 顯示警告訊息
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // 在頁面頂部插入警告
    const container = document.querySelector('.container');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    // 自動消失
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}
</script>
{% endblock %}