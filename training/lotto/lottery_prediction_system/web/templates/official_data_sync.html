{% extends 'base.html' %}

{% block title %}官方數據同步 - 彩票預測系統{% endblock %}

{% block extra_css %}
<style>
    .sync-header {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .sync-status-card {
        border-left: 4px solid #28a745;
        margin-bottom: 20px;
        transition: transform 0.2s ease;
    }
    
    .sync-status-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    }
    
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-ready { background-color: #28a745; }
    .status-sync-needed { background-color: #ffc107; }
    .status-syncing { background-color: #007bff; animation: pulse 1.5s infinite; }
    .status-error { background-color: #dc3545; }
    
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
    
    .sync-controls {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .data-source-info {
        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .progress-container {
        margin: 20px 0;
    }
    
    .sync-log {
        background: #2d3748;
        color: #e2e8f0;
        border-radius: 8px;
        padding: 15px;
        font-family: 'Courier New', monospace;
        max-height: 300px;
        overflow-y: auto;
        margin-top: 20px;
    }
    
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 20px;
    }
    
    .metric-card {
        text-align: center;
        padding: 20px;
        border-radius: 10px;
        margin: 10px 0;
    }
    
    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        margin: 10px 0;
    }
    
    .metric-success { background: linear-gradient(135deg, #d4edda, #c3e6cb); }
    .metric-warning { background: linear-gradient(135deg, #fff3cd, #ffeaa7); }
    .metric-info { background: linear-gradient(135deg, #cce5ff, #b8daff); }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 標題區域 -->
    <div class="sync-header">
        <h1><i class="fas fa-sync-alt"></i> 官方數據同步中心</h1>
        <p>從台灣彩券官方網站同步最新開獎數據，確保數據來源的權威性</p>
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="metric-card metric-success">
                    <div class="metric-value" id="totalSynced">-</div>
                    <small>已同步數據筆數</small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="metric-card metric-info">
                    <div class="metric-value" id="lastSyncTime">-</div>
                    <small>最後同步時間</small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="metric-card metric-warning">
                    <div class="metric-value" id="syncPending">-</div>
                    <small>待同步項目</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 同步控制面板 -->
        <div class="col-md-4">
            <div class="card sync-status-card">
                <div class="card-header">
                    <h5><i class="fas fa-control"></i> 同步控制</h5>
                </div>
                <div class="card-body">
                    <div class="sync-controls">
                        <button class="btn btn-success w-100 mb-2" onclick="startIntelligentSync()">
                            <i class="fas fa-brain"></i> 智能同步（先檢查）
                        </button>
                        <button class="btn btn-primary w-100 mb-2" onclick="performIntelligentCheck()">
                            <i class="fas fa-search"></i> 智能數據檢查
                        </button>
                        <button class="btn btn-info w-100 mb-2" onclick="checkSyncStatus()">
                            <i class="fas fa-refresh"></i> 檢查同步狀態
                        </button>
                        <button class="btn btn-secondary w-100 mb-2" onclick="startAutoSync()">
                            <i class="fas fa-play"></i> 傳統自動同步
                        </button>
                        <button class="btn btn-warning w-100 mb-2" onclick="showValidationReport()">
                            <i class="fas fa-check-circle"></i> 數據驗證報告
                        </button>
                        <button class="btn btn-primary w-100" onclick="downloadSyncReport()">
                            <i class="fas fa-download"></i> 下載同步報告
                        </button>
                    </div>
                    
                    <!-- 手動同步選項 -->
                    <h6 class="mt-3">手動同步單個彩票</h6>
                    <div class="btn-group-vertical w-100">
                        <button class="btn btn-outline-primary btn-sm" onclick="manualSync('powercolor')">
                            威力彩
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="manualSync('lotto649')">
                            大樂透
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="manualSync('dailycash')">
                            今彩539
                        </button>
                    </div>
                </div>
            </div>

            <!-- 數據來源資訊 -->
            <div class="card sync-status-card">
                <div class="card-header">
                    <h5><i class="fas fa-globe"></i> 數據來源</h5>
                </div>
                <div class="card-body">
                    <div class="data-source-info">
                        <h6><i class="fas fa-link"></i> 台灣彩券官方網站</h6>
                        <p class="small mb-2">主要數據源：www.taiwanlottery.com.tw</p>
                        <p class="small mb-0">備用數據源：m.taiwanlottery.com.tw</p>
                    </div>
                    <div class="mt-3">
                        <h6>同步間隔設定</h6>
                        <ul class="list-unstyled small">
                            <li>威力彩：每6小時</li>
                            <li>大樂透：每6小時</li>
                            <li>今彩539：每2小時</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 同步狀態顯示區域 -->
        <div class="col-md-8">
            <!-- 同步狀態卡片 -->
            <div class="card sync-status-card">
                <div class="card-header">
                    <h5><i class="fas fa-tachometer-alt"></i> 同步狀態監控</h5>
                </div>
                <div class="card-body">
                    <div class="row" id="syncStatusContainer">
                        <div class="col-12">
                            <p class="text-muted text-center">正在載入同步狀態...</p>
                        </div>
                    </div>
                    
                    <!-- 進度條容器 -->
                    <div class="progress-container" id="progressContainer" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" id="syncProgress" style="width: 0%">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 同步結果顯示 -->
            <div class="card sync-status-card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> 同步結果</h5>
                </div>
                <div class="card-body" id="syncResultsContainer">
                    <p class="text-muted">尚無同步結果</p>
                </div>
            </div>

            <!-- 載入狀態 -->
            <div class="loading-spinner" id="loadingSpinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">載入中...</span>
                </div>
                <p class="mt-2">正在執行同步操作...</p>
            </div>

            <!-- 同步日誌 -->
            <div class="sync-log" id="syncLog" style="display: none;">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <small class="text-info">同步日誌</small>
                    <button class="btn btn-sm btn-outline-light" onclick="clearLog()">清除</button>
                </div>
                <div id="logContent"></div>
            </div>
        </div>
    </div>
</div>

<script>
// 全域變數
let syncInProgress = false;
let logLines = [];

// 頁面初始化
document.addEventListener('DOMContentLoaded', function() {
    checkSyncStatus();
    // 每30秒自動更新狀態
    setInterval(checkSyncStatus, 30000);
});

// 檢查同步狀態
async function checkSyncStatus() {
    try {
        const response = await fetch('/api/official_data/sync_status');
        const data = await response.json();
        
        if (data.success) {
            displaySyncStatus(data.data);
            updateMetrics(data.data);
        } else {
            showAlert('error', `狀態檢查失敗: ${data.error}`);
        }
    } catch (error) {
        console.error('檢查同步狀態失敗:', error);
        showAlert('error', '無法檢查同步狀態');
    }
}

// 顯示同步狀態
function displaySyncStatus(statusData) {
    const container = document.getElementById('syncStatusContainer');
    let html = '';
    
    for (const [lotteryType, status] of Object.entries(statusData.sync_status)) {
        const statusClass = status.should_sync ? 'status-sync-needed' : 'status-ready';
        const statusText = status.should_sync ? '需要同步' : '已是最新';
        const lastSync = status.last_sync ? 
            new Date(status.last_sync).toLocaleString('zh-TW') : '從未同步';
        
        html += `
            <div class="col-md-4 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-title">
                            <span class="status-indicator ${statusClass}"></span>
                            ${status.lottery_name}
                        </h6>
                        <p class="card-text small">
                            狀態：${statusText}<br>
                            最後同步：${lastSync}
                        </p>
                        <button class="btn btn-sm btn-outline-primary" 
                                onclick="fetchOfficialData('${lotteryType}')">
                            檢視官方數據
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
    
    container.innerHTML = html;
}

// 更新指標
function updateMetrics(statusData) {
    // 計算待同步項目數量
    const pendingCount = Object.values(statusData.sync_status)
        .filter(status => status.should_sync).length;
    
    document.getElementById('syncPending').textContent = pendingCount;
    document.getElementById('lastSyncTime').textContent = 
        new Date(statusData.current_time).toLocaleString('zh-TW');
}

// 開始自動同步
async function startAutoSync() {
    if (syncInProgress) {
        showAlert('warning', '同步正在進行中，請稍候');
        return;
    }
    
    syncInProgress = true;
    showLoading(true);
    showProgress(true);
    addLog('開始自動同步所有彩票類型...');
    
    try {
        const response = await fetch('/api/official_data/auto_sync', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            addLog('自動同步完成');
            displaySyncResults(data.data);
            showAlert('success', data.message);
            // 更新狀態
            setTimeout(checkSyncStatus, 1000);
        } else {
            addLog(`自動同步失敗: ${data.error}`);
            showAlert('error', `自動同步失敗: ${data.error}`);
        }
    } catch (error) {
        console.error('自動同步失敗:', error);
        addLog(`自動同步錯誤: ${error.message}`);
        showAlert('error', '自動同步請求失敗');
    } finally {
        syncInProgress = false;
        showLoading(false);
        showProgress(false);
    }
}

// 手動同步單個彩票
async function manualSync(lotteryType) {
    if (syncInProgress) {
        showAlert('warning', '同步正在進行中，請稍候');
        return;
    }
    
    syncInProgress = true;
    showLoading(true);
    addLog(`開始同步 ${getLotteryName(lotteryType)}...`);
    
    try {
        const response = await fetch(`/api/official_data/sync/${lotteryType}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ limit: 20 })
        });
        
        const data = await response.json();
        
        if (data.success) {
            addLog(`${getLotteryName(lotteryType)} 同步完成`);
            displaySingleSyncResult(data.data);
            showAlert('success', data.message);
            setTimeout(checkSyncStatus, 1000);
        } else {
            addLog(`${getLotteryName(lotteryType)} 同步失敗: ${data.error}`);
            showAlert('error', data.error);
        }
    } catch (error) {
        console.error('手動同步失敗:', error);
        addLog(`${getLotteryName(lotteryType)} 同步錯誤: ${error.message}`);
        showAlert('error', '同步請求失敗');
    } finally {
        syncInProgress = false;
        showLoading(false);
    }
}

// 獲取官方數據
async function fetchOfficialData(lotteryType) {
    try {
        showLoading(true);
        const response = await fetch(`/api/official_data/fetch/${lotteryType}?limit=5`);
        const data = await response.json();
        
        if (data.success) {
            displayOfficialDataPreview(data.data);
        } else {
            showAlert('error', `獲取官方數據失敗: ${data.error}`);
        }
    } catch (error) {
        console.error('獲取官方數據失敗:', error);
        showAlert('error', '無法獲取官方數據');
    } finally {
        showLoading(false);
    }
}

// 顯示官方數據預覽
function displayOfficialDataPreview(data) {
    let html = `
        <div class="alert alert-info">
            <h6><i class="fas fa-database"></i> ${getLotteryName(data.lottery_type)} 官方數據預覽</h6>
            <p>獲取時間：${new Date(data.timestamp).toLocaleString('zh-TW')}</p>
            <p>數據筆數：${data.count} 筆</p>
        </div>
    `;
    
    if (data.results && data.results.length > 0) {
        html += '<div class="table-responsive"><table class="table table-sm table-striped">';
        html += '<thead><tr><th>期號</th><th>開獎日期</th><th>號碼</th><th>驗證雜湊</th></tr></thead><tbody>';
        
        data.results.slice(0, 3).forEach(result => {
            const numbers = result.main_numbers.join(', ');
            const special = result.special_number ? ` + ${result.special_number}` : '';
            html += `
                <tr>
                    <td>${result.period}</td>
                    <td>${result.draw_date}</td>
                    <td>${numbers}${special}</td>
                    <td><code>${result.verification_hash.substring(0, 8)}...</code></td>
                </tr>
            `;
        });
        
        html += '</tbody></table></div>';
    }
    
    document.getElementById('syncResultsContainer').innerHTML = html;
}

// 顯示同步結果
function displaySyncResults(results) {
    let html = `
        <div class="alert alert-success">
            <h6><i class="fas fa-check-circle"></i> 自動同步完成</h6>
            <p>開始時間：${new Date(results.start_time).toLocaleString('zh-TW')}</p>
            <p>結束時間：${new Date(results.end_time).toLocaleString('zh-TW')}</p>
            <p>總計：成功 ${results.total_success} 筆，失敗 ${results.total_failed} 筆</p>
        </div>
    `;
    
    if (results.results && Object.keys(results.results).length > 0) {
        html += '<h6>各彩票同步結果：</h6>';
        for (const [lotteryType, result] of Object.entries(results.results)) {
            html += `
                <div class="card mb-2">
                    <div class="card-body py-2">
                        <h6 class="card-title mb-1">${getLotteryName(lotteryType)}</h6>
                        <p class="card-text small mb-0">
                            獲取：${result.fetched} 筆 | 
                            成功：${result.success} 筆 | 
                            失敗：${result.failed} 筆
                        </p>
                        ${result.errors.length > 0 ? 
                            `<details class="mt-2">
                                <summary class="small text-danger">錯誤詳情</summary>
                                <ul class="small mt-1 mb-0">
                                    ${result.errors.map(err => `<li>${err}</li>`).join('')}
                                </ul>
                            </details>` : ''
                        }
                    </div>
                </div>
            `;
        }
    }
    
    document.getElementById('syncResultsContainer').innerHTML = html;
    document.getElementById('totalSynced').textContent = results.total_success;
}

// 顯示單個同步結果
function displaySingleSyncResult(result) {
    const html = `
        <div class="alert alert-info">
            <h6><i class="fas fa-sync"></i> ${getLotteryName(result.lottery_type)} 同步結果</h6>
            <p>同步時間：${new Date(result.sync_timestamp).toLocaleString('zh-TW')}</p>
            <p>獲取：${result.fetched_count} 筆 | 成功：${result.success_count} 筆 | 失敗：${result.failed_count} 筆</p>
            ${result.error_messages.length > 0 ? 
                `<details>
                    <summary class="text-danger">錯誤詳情</summary>
                    <ul class="mt-2 mb-0">
                        ${result.error_messages.map(err => `<li>${err}</li>`).join('')}
                    </ul>
                </details>` : ''
            }
        </div>
    `;
    
    document.getElementById('syncResultsContainer').innerHTML = html;
}

// 顯示驗證報告
async function showValidationReport() {
    try {
        const response = await fetch('/api/official_data/validation_report');
        const data = await response.json();
        
        if (data.success) {
            const report = data.data;
            let html = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-shield-check"></i> 數據驗證報告</h6>
                    <p>報告時間：${new Date(report.timestamp).toLocaleString('zh-TW')}</p>
                    <p>整體狀態：${report.overall_status === 'healthy' ? '正常' : '異常'}</p>
                    ${report.integrity_score ? 
                        `<p>完整性評分：${report.integrity_score}/100</p>` : ''
                    }
                    ${report.fake_data_blocked ? 
                        `<p class="text-warning">已阻止假數據：${report.fake_data_blocked} 次</p>` : ''
                    }
                </div>
            `;
            
            document.getElementById('syncResultsContainer').innerHTML = html;
        } else {
            showAlert('error', `無法生成驗證報告: ${data.error}`);
        }
    } catch (error) {
        console.error('獲取驗證報告失敗:', error);
        showAlert('error', '無法獲取驗證報告');
    }
}

// 下載同步報告
async function downloadSyncReport() {
    try {
        // 獲取最新的同步狀態和驗證報告
        const [statusResponse, validationResponse] = await Promise.all([
            fetch('/api/official_data/sync_status'),
            fetch('/api/official_data/validation_report')
        ]);
        
        const statusData = await statusResponse.json();
        const validationData = await validationResponse.json();
        
        const report = {
            generated_at: new Date().toISOString(),
            sync_status: statusData.success ? statusData.data : {},
            validation_report: validationData.success ? validationData.data : {},
            system_info: {
                user_agent: navigator.userAgent,
                timestamp: new Date().toISOString()
            }
        };
        
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `官方數據同步報告_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showAlert('success', '同步報告已下載');
        
    } catch (error) {
        console.error('下載報告失敗:', error);
        showAlert('error', '無法下載同步報告');
    }
}

// 工具函數
function getLotteryName(type) {
    const names = {
        'powercolor': '威力彩',
        'lotto649': '大樂透',
        'dailycash': '今彩539'
    };
    return names[type] || type;
}

function showLoading(show) {
    document.getElementById('loadingSpinner').style.display = show ? 'block' : 'none';
}

function showProgress(show) {
    document.getElementById('progressContainer').style.display = show ? 'block' : 'none';
    if (show) {
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 20;
            if (progress > 90) progress = 90;
            document.getElementById('syncProgress').style.width = progress + '%';
            
            if (!show) {
                clearInterval(interval);
                document.getElementById('syncProgress').style.width = '100%';
                setTimeout(() => {
                    document.getElementById('progressContainer').style.display = 'none';
                }, 500);
            }
        }, 200);
    }
}

function addLog(message) {
    const timestamp = new Date().toLocaleTimeString('zh-TW');
    logLines.push(`[${timestamp}] ${message}`);
    
    const logContainer = document.getElementById('syncLog');
    const logContent = document.getElementById('logContent');
    
    logContent.innerHTML = logLines.join('<br>');
    logContainer.style.display = 'block';
    
    // 自動滾動到底部
    logContainer.scrollTop = logContainer.scrollHeight;
    
    // 保持最多50行日誌
    if (logLines.length > 50) {
        logLines = logLines.slice(-50);
    }
}

function clearLog() {
    logLines = [];
    document.getElementById('logContent').innerHTML = '';
    document.getElementById('syncLog').style.display = 'none';
}

function showAlert(type, message) {
    const alertTypes = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    };
    
    const alert = document.createElement('div');
    alert.className = `alert ${alertTypes[type]} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alert);
    
    setTimeout(() => {
        if (alert.parentElement) {
            alert.remove();
        }
    }, 5000);
}

// 智能數據檢查和同步函數
async function performIntelligentCheck() {
    try {
        showLoading(true);
        addLog('開始執行智能數據檢查...');
        
        const response = await fetch('/api/intelligent_check');
        const data = await response.json();
        
        if (data.success) {
            addLog('智能數據檢查完成');
            displayIntelligentCheckResults(data.data);
            showAlert('success', '智能數據檢查完成');
        } else {
            addLog(`智能數據檢查失敗: ${data.error}`);
            showAlert('error', `智能數據檢查失敗: ${data.error}`);
        }
    } catch (error) {
        console.error('智能數據檢查失敗:', error);
        addLog(`智能數據檢查錯誤: ${error.message}`);
        showAlert('error', '智能數據檢查請求失敗');
    } finally {
        showLoading(false);
    }
}

function displayIntelligentCheckResults(data) {
    const container = document.getElementById('syncResultsContainer');
    const checkResults = data.check_results;
    const recommendation = data.recommendation;
    
    let html = `
        <div class="alert alert-info">
            <h6><i class="fas fa-brain"></i> 智能數據檢查結果</h6>
            <p><strong>檢查時間:</strong> ${new Date().toLocaleString('zh-TW')}</p>
            <p><strong>同步建議:</strong> ${recommendation.recommendation_reason}</p>
            <p><strong>優先級:</strong> <span class="badge bg-${recommendation.priority_level === 'high' ? 'danger' : recommendation.priority_level === 'medium' ? 'warning' : 'secondary'}">${recommendation.priority_level.toUpperCase()}</span></p>
            <p><strong>需要立即同步:</strong> ${recommendation.immediate_sync_needed ? '是' : '否'}</p>
            <p><strong>估算新記錄:</strong> ${recommendation.estimated_new_records} 筆</p>
            <p><strong>下次檢查時間:</strong> ${new Date(recommendation.next_check_time).toLocaleString('zh-TW')}</p>
        </div>
    `;
    
    html += '<h6>各彩票檢查詳情：</h6>';
    for (const [lotteryType, result] of Object.entries(checkResults)) {
        const statusClass = result.sync_needed ? 'warning' : 'success';
        const statusText = result.sync_needed ? '需要同步' : '無需同步';
        
        html += `
            <div class="card mb-2">
                <div class="card-body py-2">
                    <h6 class="card-title mb-1">
                        ${result.lottery_name}
                        <span class="badge bg-${statusClass}">${statusText}</span>
                    </h6>
                    <p class="card-text small mb-0">
                        當前期號: ${result.current_period} | 
                        期望期號: ${result.latest_available_period || '無'} | 
                        數據年齡: ${result.data_age_hours.toFixed(1)} 小時
                    </p>
                </div>
            </div>
        `;
    }
    
    container.innerHTML = html;
}

async function startIntelligentSync() {
    if (syncInProgress) {
        showAlert('warning', '同步正在進行中，請稍候');
        return;
    }
    
    syncInProgress = true;
    showLoading(true);
    showProgress(true);
    addLog('開始智能同步（先檢查後同步）...');
    
    try {
        const response = await fetch('/api/intelligent_sync', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            addLog('智能同步完成');
            displayIntelligentSyncResults(data.data);
            showAlert('success', '智能同步完成');
            // 更新狀態
            setTimeout(checkSyncStatus, 1000);
        } else {
            addLog(`智能同步失敗: ${data.error}`);
            showAlert('error', `智能同步失敗: ${data.error}`);
        }
    } catch (error) {
        console.error('智能同步失敗:', error);
        addLog(`智能同步錯誤: ${error.message}`);
        showAlert('error', '智能同步請求失敗');
    } finally {
        syncInProgress = false;
        showLoading(false);
        showProgress(false);
    }
}

function displayIntelligentSyncResults(results) {
    const container = document.getElementById('syncResultsContainer');
    
    let html = `
        <div class="alert alert-success">
            <h6><i class="fas fa-brain"></i> 智能同步結果</h6>
            <p>開始時間: ${new Date(results.start_time).toLocaleString('zh-TW')}</p>
            <p>結束時間: ${new Date(results.end_time).toLocaleString('zh-TW')}</p>
            <p>是否執行同步: ${results.sync_performed ? '是' : '否'}</p>
        </div>
    `;
    
    if (results.sync_performed && results.sync_results) {
        const syncResults = results.sync_results;
        html += `
            <h6>同步詳情：</h6>
            <p>成功同步: ${syncResults.total_synced || 0} 筆</p>
        `;
        
        if (syncResults.sync_results) {
            for (const [lotteryType, result] of Object.entries(syncResults.sync_results)) {
                html += `
                    <div class="card mb-2">
                        <div class="card-body py-2">
                            <h6 class="card-title mb-1">${getLotteryName(lotteryType)}</h6>
                            <p class="card-text small mb-0">
                                ${result.success ? '同步成功' : '同步失敗'} - ${result.message}
                            </p>
                        </div>
                    </div>
                `;
            }
        }
    } else {
        html += '<p>智能檢查後發現無新數據，未執行同步</p>';
    }
    
    container.innerHTML = html;
}
</script>
{% endblock %}