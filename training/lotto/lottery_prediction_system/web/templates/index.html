<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>彩票預測系統</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/confidence.css') }}" rel="stylesheet">
    <style>
        .card {
            margin-bottom: 20px;
        }
        .lottery-icon {
            font-size: 2rem;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    {% include 'navbar.html' %}

    <div class="container mt-4">
        <h1 class="mb-4">彩票預測系統</h1>
        
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title"><span class="lottery-icon">🎯</span>威力彩</h5>
                        <p class="card-text">查看威力彩的預測結果和歷史記錄</p>
                        <a href="/predictions?type=powercolor" class="btn btn-primary">預測記錄</a>
                        <a href="/analysis?type=powercolor" class="btn btn-outline-primary">分析報告</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title"><span class="lottery-icon">🎲</span>大樂透</h5>
                        <p class="card-text">查看大樂透的預測結果和歷史記錄</p>
                        <a href="/predictions?type=lotto649" class="btn btn-primary">預測記錄</a>
                        <a href="/analysis?type=lotto649" class="btn btn-outline-primary">分析報告</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title"><span class="lottery-icon">🎪</span>今彩539</h5>
                        <p class="card-text">查看今彩539的預測結果和歷史記錄</p>
                        <a href="/predictions?type=dailycash" class="btn btn-primary">預測記錄</a>
                        <a href="/analysis?type=dailycash" class="btn btn-outline-primary">分析報告</a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 新功能區域 -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">🚀 分離式預測</h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">機器學習和板路分析獨立運行，提供詳細的預測理由和成功分析。</p>
                        <a href="/separated_prediction" class="btn btn-success">立即體驗</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">📊 系統儀表板</h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">實時監控系統狀態、預測統計和快速操作。</p>
                        <a href="/dashboard" class="btn btn-info">查看儀表板</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">🔬 綜合分析</h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">深度分析預測成功原因、號碼出現模式、數學關係等全方位智能分析。</p>
                        <a href="/comprehensive_analysis" class="btn btn-warning">深度分析</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">🧠 智能預測</h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">基於增強數學分析的智能預測，包含所有可能的數學關係檢測。</p>
                        <a href="/enhanced_prediction" class="btn btn-danger">智能預測</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系統管理功能區域 -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card border-secondary">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">🛡️ 數據完整性管理</h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">監控數據真實性、阻止假數據、追蹤數據來源，確保所有數據都是可信的。</p>
                        <a href="/data_integrity" class="btn btn-secondary">完整性管理</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">🌐 官方數據同步</h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">從台灣彩券官方網站同步最新開獎數據，確保數據來源的官方權威性。</p>
                        <a href="/official_data_sync" class="btn btn-success">數據同步</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">👁️ 可觀察追蹤</h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">實時追蹤預測過程、分析成功因素、提供透明的預測驗證機制。</p>
                        <a href="/observable_tracking" class="btn btn-primary">觀察追蹤</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-chart-line"></i> 最新預測結果 (含信心度)</span>
                        <button class="btn btn-outline-light btn-sm" onclick="refreshPredictions()">
                            <i class="fas fa-sync"></i> 重新整理
                        </button>
                    </div>
                    <div class="card-body" id="mainContent">
                        <div id="latest-predictions">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">載入中...</span>
                                </div>
                                <p class="mt-2">正在載入最新預測結果...</p>
                            </div>
                        </div>
                        <div id="predictionMetadata" class="mt-3">
                            <!-- 預測元數據將在這裡顯示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 信心度歷史統計區域 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-history"></i> 信心度歷史統計</span>
                        <div class="btn-group">
                            <button class="btn btn-outline-light btn-sm" onclick="loadConfidenceHistory('powercolor')">威力彩</button>
                            <button class="btn btn-outline-light btn-sm" onclick="loadConfidenceHistory('lotto649')">大樂透</button>
                            <button class="btn btn-outline-light btn-sm" onclick="loadConfidenceHistory('dailycash')">今彩539</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="confidenceHistoryContainer">
                            <div class="text-center py-4">
                                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                <p class="text-muted">選擇彩票類型查看信心度歷史統計</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/confidence.js') }}"></script>
    <script>
        // 載入最新預測結果
        async function loadLatestPredictions() {
            try {
                const response = await fetch('/api/latest_predictions/powercolor?limit=3');
                const result = await response.json();
                
                if (!result.success || result.error) {
                    document.getElementById('latest-predictions').innerHTML = 
                        `<div class="alert alert-warning">無法載入預測: ${result.error || '未知錯誤'}</div>`;
                    return;
                }
                
                // 獲取實際的預測數據
                const predictions = result.data ? result.data.predictions : [];
                
                // 檢查predictions是否存在且為陣列
                if (!predictions || !Array.isArray(predictions)) {
                    document.getElementById('latest-predictions').innerHTML = 
                        `<div class="alert alert-warning">暫無預測數據</div>`;
                    return;
                }
                
                if (predictions.length === 0) {
                    document.getElementById('latest-predictions').innerHTML = 
                        `<div class="alert alert-info">暫無預測記錄</div>`;
                    return;
                }
                
                let html = '<div class="table-responsive"><table class="table table-striped">';
                html += '<thead><tr><th>期數</th><th>預測號碼</th><th>預測日期</th></tr></thead><tbody>';
                
                predictions.forEach(pred => {
                    // 支持兩種數據格式：舊格式(PredA1, PredA2...)和新格式(numbers, special)
                    let numbers, special, period, predictionDate;
                    
                    if (pred.numbers && Array.isArray(pred.numbers)) {
                        // 新格式
                        numbers = pred.numbers;
                        special = pred.special || '';
                        period = pred.period || pred.Period || '';
                        predictionDate = pred.created_at || pred.PredictionDate || '';
                    } else {
                        // 舊格式
                        numbers = [pred.PredA1, pred.PredA2, pred.PredA3, pred.PredA4, pred.PredA5, pred.PredA6].filter(n => n);
                        special = pred.PredSpecial || '';
                        period = pred.Period || pred.period || '';
                        predictionDate = pred.PredictionDate || pred.created_at || '';
                    }
                    
                    const numbersStr = numbers.join(', ');
                    const specialStr = special ? ` + ${special}` : '';
                    
                    html += `<tr>
                        <td>${period}</td>
                        <td>${numbersStr}${specialStr}</td>
                        <td>${predictionDate}</td>
                    </tr>`;
                });
                
                html += '</tbody></table></div>';
                document.getElementById('latest-predictions').innerHTML = html;
            } catch (error) {
                console.error('載入預測數據失敗:', error);
                document.getElementById('latest-predictions').innerHTML = 
                    `<div class="alert alert-danger">
                        <h6>載入失敗</h6>
                        <p>錯誤訊息: ${error.message}</p>
                        <small>請檢查網絡連接或稍後再試</small>
                    </div>`;
            }
        }
        
        // 使用增強預測功能載入帶信心度的預測結果
        async function loadEnhancedLatestPredictions() {
            try {
                // 載入威力彩的增強預測結果
                const enhancedData = await confidenceManager.loadEnhancedPrediction('powercolor');
                
                if (enhancedData) {
                    // 更新預測號碼顯示
                    displayPredictionWithConfidence(enhancedData);
                } else {
                    // 降級到基本預測
                    await loadLatestPredictions();
                }
            } catch (error) {
                console.error('載入增強預測失敗，降級到基本預測:', error);
                await loadLatestPredictions();
            }
        }
        
        // 顯示帶信心度的預測結果
        function displayPredictionWithConfidence(data) {
            const container = document.getElementById('latest-predictions');
            
            let html = `
                <div class="row">
                    <div class="col-md-8">
                        <h6><i class="fas fa-star"></i> ${getLotteryName(data.lottery_type)} 最新預測</h6>
                        <div id="predictionNumbers" class="mb-3">
                            <!-- 預測號碼將由 ConfidenceManager 渲染 -->
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-end">
                            <small class="text-muted">
                                <i class="fas fa-clock"></i> ${new Date(data.timestamp).toLocaleString('zh-TW')}
                            </small>
                        </div>
                    </div>
                </div>
            `;
            
            container.innerHTML = html;
            
            // 讓 ConfidenceManager 處理預測號碼和信心度顯示
            const numbersContainer = document.getElementById('predictionNumbers');
            if (numbersContainer) {
                confidenceManager.renderPredictionNumbers(numbersContainer, data.prediction, data.lottery_type);
            }
        }
        
        // 獲取彩票類型名稱
        function getLotteryName(type) {
            const names = {
                'powercolor': '威力彩',
                'lotto649': '大樂透',
                'dailycash': '今彩539'
            };
            return names[type] || type;
        }
        
        // 重新整理預測結果
        async function refreshPredictions() {
            const button = event.target;
            const originalText = button.innerHTML;
            
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 載入中...';
            
            try {
                await loadEnhancedLatestPredictions();
                
                // 顯示成功提示
                showToast('success', '預測結果已更新');
            } catch (error) {
                console.error('重新整理失敗:', error);
                showToast('error', '更新失敗，請稍後再試');
            } finally {
                button.disabled = false;
                button.innerHTML = originalText;
            }
        }
        
        // 載入信心度歷史統計
        async function loadConfidenceHistory(lotteryType) {
            // 更新按鈕狀態
            const buttons = document.querySelectorAll('.btn-group .btn-outline-light');
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            try {
                await confidenceManager.loadConfidenceHistory(lotteryType);
            } catch (error) {
                console.error('載入信心度歷史失敗:', error);
                showToast('error', '載入歷史統計失敗');
            }
        }
        
        // 顯示提示訊息
        function showToast(type, message) {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; animation: slideInRight 0.3s ease;';
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(toast);

            setTimeout(() => {
                if (toast.parentElement) {
                    toast.style.animation = 'slideOutRight 0.3s ease';
                    setTimeout(() => toast.remove(), 300);
                }
            }, 3000);
        }
        
        // 添加動畫樣式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
        
        // 頁面載入時執行增強預測載入
        document.addEventListener('DOMContentLoaded', function() {
            // 延遲執行以確保 ConfidenceManager 已初始化
            setTimeout(() => {
                loadEnhancedLatestPredictions();
            }, 500);
        });
    </script>
</body>
</html>