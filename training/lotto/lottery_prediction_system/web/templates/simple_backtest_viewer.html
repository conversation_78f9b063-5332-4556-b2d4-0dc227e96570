<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>簡化回測查看器 - 彩票預測系統</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .report-card {
            transition: transform 0.2s;
            cursor: pointer;
        }
        .report-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }
        .number-ball {
            display: inline-block;
            width: 25px;
            height: 25px;
            line-height: 25px;
            text-align: center;
            border-radius: 50%;
            margin-right: 2px;
            font-weight: bold;
            font-size: 11px;
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">📊 簡化回測查看器</h1>
        
        <!-- 載入中顯示 -->
        <div id="loading" class="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">載入中...</span>
            </div>
            <span class="ms-2">載入回測報告...</span>
        </div>
        
        <!-- 主要內容 -->
        <div id="mainContent" style="display: none;">
            <!-- 報告選擇 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <label for="reportSelect" class="form-label">選擇回測報告:</label>
                    <select class="form-select" id="reportSelect" onchange="loadSelectedReport()">
                        <option value="">請選擇報告...</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="lotteryFilter" class="form-label">彩票類型篩選:</label>
                    <select class="form-select" id="lotteryFilter" onchange="filterReports()">
                        <option value="">全部</option>
                        <option value="powercolor">威力彩</option>
                        <option value="lotto649">大樂透</option>
                        <option value="dailycash">今彩539</option>
                    </select>
                </div>
            </div>
            
            <!-- 報告詳情 -->
            <div id="reportDetails" style="display: none;">
                <!-- 基本統計 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">測試期數</h5>
                                <h3 id="totalPeriods">-</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">平均準確度</h5>
                                <h3 id="avgAccuracy">-</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">總匹配數</h5>
                                <h3 id="totalMatches">-</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">中獎率</h5>
                                <h3 id="winningRate">-</h3>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 配置資訊 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>回測配置</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <strong>彩票類型:</strong> <span id="gameType">-</span>
                            </div>
                            <div class="col-md-4">
                                <strong>預測方法:</strong> <span id="method">-</span>
                            </div>
                            <div class="col-md-4">
                                <strong>訓練窗口:</strong> <span id="trainingWindow">-</span> 期
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-12">
                                <strong>測試範圍:</strong> <span id="testPeriod">-</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 匹配數分佈圖 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>匹配數分佈</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="matchChart" height="100"></canvas>
                    </div>
                </div>
                
                <!-- 獎項分佈 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>獎項分佈</h5>
                    </div>
                    <div class="card-body">
                        <div id="prizeDistribution">
                            <!-- 動態生成獎項分佈 -->
                        </div>
                    </div>
                </div>
                
                <!-- 詳細結果表格 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>詳細結果 (前20筆)</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>期數</th>
                                        <th>預測號碼</th>
                                        <th>實際號碼</th>
                                        <th>匹配數</th>
                                        <th>準確度</th>
                                        <th>信心度</th>
                                    </tr>
                                </thead>
                                <tbody id="detailsTable">
                                    <tr>
                                        <td colspan="6" class="text-center">請選擇報告</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let allReports = [];
        let currentChart = null;
        
        // 頁面載入完成後初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadReportsList();
        });
        
        // 載入報告列表
        async function loadReportsList() {
            try {
                const response = await fetch('/api/backtest/reports');
                const data = await response.json();
                
                if (data.success) {
                    allReports = data.data.reports;
                    populateReportSelect(allReports);
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('mainContent').style.display = 'block';
                } else {
                    showError('載入報告列表失敗: ' + data.error);
                }
            } catch (error) {
                showError('載入報告列表時發生錯誤: ' + error.message);
            }
        }
        
        // 填充報告選擇器
        function populateReportSelect(reports) {
            const select = document.getElementById('reportSelect');
            select.innerHTML = '<option value="">請選擇報告...</option>';
            
            reports.forEach((report, index) => {
                const timestamp = report.timestamp;
                const formattedTime = `${timestamp.slice(0,4)}-${timestamp.slice(4,6)}-${timestamp.slice(6,8)} ${timestamp.slice(8,10)}:${timestamp.slice(10,12)}`;
                const option = document.createElement('option');
                option.value = index;
                option.textContent = `${report.lottery_type} (${formattedTime})`;
                select.appendChild(option);
            });
        }
        
        // 篩選報告
        function filterReports() {
            const filter = document.getElementById('lotteryFilter').value;
            let filteredReports = allReports;
            
            if (filter) {
                filteredReports = allReports.filter(report => report.lottery_type === filter);
            }
            
            populateReportSelect(filteredReports);
            document.getElementById('reportDetails').style.display = 'none';
        }
        
        // 載入選中的報告
        async function loadSelectedReport() {
            const selectIndex = document.getElementById('reportSelect').value;
            if (!selectIndex) {
                document.getElementById('reportDetails').style.display = 'none';
                return;
            }
            
            const report = allReports[selectIndex];
            if (!report) return;
            
            try {
                const response = await fetch(`/api/backtest/report/${report.filename}`);
                const data = await response.json();
                
                if (data.success) {
                    displayReportDetails(data.data);
                    document.getElementById('reportDetails').style.display = 'block';
                } else {
                    showError('載入報告詳情失敗: ' + data.error);
                }
            } catch (error) {
                showError('載入報告詳情時發生錯誤: ' + error.message);
            }
        }
        
        // 顯示報告詳情
        function displayReportDetails(reportData) {
            const config = reportData.config || {};
            const stats = reportData.statistics || {};
            
            // 基本統計
            document.getElementById('totalPeriods').textContent = stats.total_periods || 0;
            document.getElementById('avgAccuracy').textContent = (stats.accuracy || 0).toFixed(2) + '%';
            document.getElementById('totalMatches').textContent = stats.total_matches || 0;
            document.getElementById('winningRate').textContent = (stats.winning_rate || 0).toFixed(2) + '%';
            
            // 配置資訊
            document.getElementById('gameType').textContent = config.game_type || '未知';
            document.getElementById('method').textContent = config.method || '未知';
            document.getElementById('trainingWindow').textContent = config.training_window || 0;
            document.getElementById('testPeriod').textContent = config.test_period || '未知';
            
            // 匹配數分佈圖
            displayMatchChart(reportData.match_distribution || {});
            
            // 獎項分佈
            displayPrizeDistribution(reportData.prize_distribution || {});
            
            // 詳細結果表格
            displayDetailsTable(reportData.detailed_results || []);
        }
        
        // 顯示匹配數分佈圖
        function displayMatchChart(matchData) {
            const ctx = document.getElementById('matchChart').getContext('2d');
            
            if (currentChart) {
                currentChart.destroy();
            }
            
            const labels = [];
            const data = [];
            const colors = ['#dc3545', '#fd7e14', '#ffc107', '#20c997', '#198754', '#0d6efd', '#6610f2'];
            
            for (let i = 0; i <= 6; i++) {
                const key = `matches_${i}`;
                if (matchData[key] !== undefined) {
                    labels.push(`${i}個匹配`);
                    data.push(matchData[key]);
                }
            }
            
            currentChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: colors.slice(0, labels.length),
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
        
        // 顯示獎項分佈
        function displayPrizeDistribution(prizeData) {
            const container = document.getElementById('prizeDistribution');
            container.innerHTML = '';
            
            let hasWinnings = false;
            for (let i = 1; i <= 8; i++) {
                const key = `prize_${i}`;
                const count = prizeData[key] || 0;
                if (count > 0) {
                    hasWinnings = true;
                    const badge = document.createElement('span');
                    badge.className = 'badge bg-success me-2 mb-2';
                    badge.textContent = `第${i}獎: ${count}次`;
                    container.appendChild(badge);
                }
            }
            
            if (!hasWinnings) {
                container.innerHTML = '<div class="alert alert-info">此次回測未中任何獎項</div>';
            }
        }
        
        // 顯示詳細結果表格
        function displayDetailsTable(detailsData) {
            const tbody = document.getElementById('detailsTable');
            tbody.innerHTML = '';
            
            if (!detailsData || detailsData.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center">無詳細結果數據</td></tr>';
                return;
            }
            
            // 只顯示前20筆
            const displayData = detailsData.slice(0, 20);
            
            displayData.forEach(result => {
                const row = document.createElement('tr');
                
                // 格式化號碼顯示
                const formatNumbers = (numbers) => {
                    if (!numbers || !Array.isArray(numbers)) return '-';
                    return numbers.map(num => `<span class="number-ball">${num}</span>`).join('');
                };
                
                row.innerHTML = `
                    <td>${result.period || '-'}</td>
                    <td>${formatNumbers(result.predicted_numbers)}</td>
                    <td>${formatNumbers(result.actual_numbers)}</td>
                    <td><span class="badge bg-primary">${result.matches || 0}</span></td>
                    <td>${((result.accuracy || 0) * 100).toFixed(1)}%</td>
                    <td>${((result.confidence || 0) * 100).toFixed(1)}%</td>
                `;
                
                tbody.appendChild(row);
            });
        }
        
        // 顯示錯誤
        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('mainContent').innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <h4 class="alert-heading">載入失敗!</h4>
                    <p>${message}</p>
                </div>
            `;
            document.getElementById('mainContent').style.display = 'block';
        }
    </script>
</body>
</html>