<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>預測效果儀表板 - 彩票預測系統</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .dashboard-card {
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        .dashboard-card:hover {
            transform: translateY(-2px);
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .accuracy-high { border-left: 4px solid #28a745; }
        .accuracy-medium { border-left: 4px solid #ffc107; }
        .accuracy-low { border-left: 4px solid #dc3545; }
        
        .recommendation-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .performance-badge {
            font-size: 0.8em;
            padding: 0.3em 0.8em;
            border-radius: 20px;
        }
        
        .trend-up { color: #28a745; }
        .trend-down { color: #dc3545; }
        .trend-stable { color: #ffc107; }
    </style>
</head>
<body>
    {% include 'navbar.html' %}
    
    <div class="container-fluid mt-4">
        <!-- 頂部摘要卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6>總預測次數</h6>
                            <h3 id="totalPredictions">-</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="metric-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6>平均準確度</h6>
                            <h3 id="avgAccuracy">-</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-bullseye fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="metric-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6>最佳方法</h6>
                            <h3 id="bestMethod">-</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-trophy fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="metric-card" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333;">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6>驗證率</h6>
                            <h3 id="verificationRate">-</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主要內容區域 -->
        <div class="row">
            <!-- 左側：方法推薦 -->
            <div class="col-md-4">
                <div class="recommendation-card">
                    <h5><i class="fas fa-lightbulb me-2"></i>智能推薦</h5>
                    <div id="methodRecommendations">
                        <div class="text-center">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">載入中...</span>
                            </div>
                            <p class="mt-2">正在分析最佳方法...</p>
                        </div>
                    </div>
                </div>
                
                <!-- 控制面板 -->
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h6><i class="fas fa-cog me-2"></i>設定</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">彩票類型</label>
                            <select class="form-select" id="lotteryType">
                                <option value="powercolor">威力彩</option>
                                <option value="lotto649">大樂透</option>
                                <option value="dailycash">今彩539</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">分析時間</label>
                            <select class="form-select" id="analysisRange">
                                <option value="7">最近7天</option>
                                <option value="30" selected>最近30天</option>
                                <option value="90">最近90天</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">用戶偏好</label>
                            <select class="form-select" id="userPreference">
                                <option value="conservative">保守型</option>
                                <option value="balanced" selected>平衡型</option>
                                <option value="aggressive">積極型</option>
                            </select>
                        </div>
                        
                        <button class="btn btn-primary w-100" onclick="refreshDashboard()">
                            <i class="fas fa-sync me-2"></i>刷新數據
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 右側：圖表區域 -->
            <div class="col-md-8">
                <!-- 方法效果對比 -->
                <div class="card dashboard-card mb-4">
                    <div class="card-header">
                        <h6><i class="fas fa-chart-bar me-2"></i>方法效果對比</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="methodComparisonChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- 準確度趨勢 -->
                <div class="card dashboard-card mb-4">
                    <div class="card-header">
                        <h6><i class="fas fa-chart-line me-2"></i>準確度趨勢</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="accuracyTrendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 詳細方法表現 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h6><i class="fas fa-table me-2"></i>詳細方法表現</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>預測方法</th>
                                        <th>總預測次數</th>
                                        <th>平均準確度</th>
                                        <th>平均信心度</th>
                                        <th>驗證率</th>
                                        <th>最後使用</th>
                                        <th>推薦度</th>
                                    </tr>
                                </thead>
                                <tbody id="methodDetailsTable">
                                    <!-- 動態載入 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let methodComparisonChart, accuracyTrendChart;

        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshDashboard();
        });

        async function refreshDashboard() {
            const lotteryType = document.getElementById('lotteryType').value;
            const days = document.getElementById('analysisRange').value;
            const preference = document.getElementById('userPreference').value;
            
            try {
                // 並行載入數據
                const [analysisData, recommendationData] = await Promise.all([
                    loadAnalysisData(lotteryType, days),
                    loadRecommendationData(lotteryType, preference)
                ]);
                
                // 更新儀表板
                updateSummaryCards(analysisData);
                updateRecommendations(recommendationData);
                updateCharts(analysisData);
                updateMethodTable(analysisData);
                
            } catch (error) {
                console.error('儀表板刷新失敗:', error);
                alert('數據載入失敗，請稍後重試');
            }
        }

        async function loadAnalysisData(lotteryType, days) {
            try {
                const response = await fetch(`/api/prediction_method_analysis/${lotteryType}?days=${days}`);
                const data = await response.json();
                if (!data.success) throw new Error(data.error || '分析數據載入失敗');
                return data.data;
            } catch (error) {
                console.error('分析數據載入錯誤:', error);
                // 返回模擬數據以保持儀表板功能
                return {
                    total_predictions: 0,
                    accuracy_metrics: {},
                    confidence_analysis: { mean: 0 },
                    time_trends: { dates: [], daily_avg_confidence: [] }
                };
            }
        }

        async function loadRecommendationData(lotteryType, preference) {
            try {
                const response = await fetch(`/api/method_recommendation/${lotteryType}?preference=${preference}`);
                const data = await response.json();
                if (!data.success) throw new Error(data.error || '推薦數據載入失敗');
                return data.data;
            } catch (error) {
                console.error('推薦數據載入錯誤:', error);
                return { recommendations: [], summary: { note: '推薦數據暫時無法載入' } };
            }
        }

        function updateSummaryCards(data) {
            document.getElementById('totalPredictions').textContent = data.total_predictions || 0;
            
            const avgAccuracy = data.confidence_analysis?.mean || 0;
            document.getElementById('avgAccuracy').textContent = (avgAccuracy * 100).toFixed(1) + '%';
            
            const accuracyMetrics = data.accuracy_metrics || {};
            const bestMethod = Object.keys(accuracyMetrics)[0] || '無';
            document.getElementById('bestMethod').textContent = bestMethod;
            
            // 計算驗證率（這裡需要實際數據）
            document.getElementById('verificationRate').textContent = '75%';
        }

        function updateRecommendations(data) {
            const container = document.getElementById('methodRecommendations');
            
            if (!data.recommendations || data.recommendations.length === 0) {
                container.innerHTML = '<p>暫無推薦數據</p>';
                return;
            }
            
            let html = '';
            data.recommendations.slice(0, 3).forEach((rec, index) => {
                const rank = index + 1;
                const score = (rec.score * 100).toFixed(0);
                
                html += `
                    <div class="mb-3 p-3" style="background: rgba(255,255,255,0.1); border-radius: 10px;">
                        <div class="d-flex justify-content-between">
                            <h6>#${rank} ${rec.method}</h6>
                            <span class="badge bg-light text-dark">${score}分</span>
                        </div>
                        <small>${rec.recommendation_reason}</small>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        function updateCharts(data) {
            createMethodComparisonChart(data);
            createAccuracyTrendChart(data);
        }

        function createMethodComparisonChart(data) {
            const ctx = document.getElementById('methodComparisonChart').getContext('2d');
            
            if (methodComparisonChart) {
                methodComparisonChart.destroy();
            }
            
            const accuracyData = data.accuracy_metrics || {};
            const methods = Object.keys(accuracyData);
            const accuracies = methods.map(method => accuracyData[method].avg_confidence * 100);
            const predictions = methods.map(method => accuracyData[method].total_predictions);
            
            methodComparisonChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: methods,
                    datasets: [{
                        label: '平均信心度 (%)',
                        data: accuracies,
                        backgroundColor: 'rgba(102, 126, 234, 0.8)',
                        borderColor: 'rgba(102, 126, 234, 1)',
                        borderWidth: 1,
                        yAxisID: 'y'
                    }, {
                        label: '預測次數',
                        data: predictions,
                        type: 'line',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '信心度 (%)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '預測次數'
                            },
                            grid: {
                                drawOnChartArea: false,
                            }
                        }
                    }
                }
            });
        }

        function createAccuracyTrendChart(data) {
            const ctx = document.getElementById('accuracyTrendChart').getContext('2d');
            
            if (accuracyTrendChart) {
                accuracyTrendChart.destroy();
            }
            
            const trendData = data.time_trends || {};
            const dates = trendData.dates || [];
            const confidences = trendData.daily_avg_confidence || [];
            
            accuracyTrendChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [{
                        label: '每日平均信心度',
                        data: confidences.map(c => c * 100),
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false,
                            title: {
                                display: true,
                                text: '信心度 (%)'
                            }
                        }
                    }
                }
            });
        }

        function updateMethodTable(data) {
            const tbody = document.getElementById('methodDetailsTable');
            const accuracyData = data.accuracy_metrics || {};
            
            let html = '';
            Object.keys(accuracyData).forEach(method => {
                const methodData = accuracyData[method];
                const accuracy = (methodData.avg_confidence * 100).toFixed(1);
                const confidence = (methodData.avg_confidence * 100).toFixed(1);
                
                // 準確度等級
                let accuracyClass = 'accuracy-medium';
                if (methodData.avg_confidence > 0.75) accuracyClass = 'accuracy-high';
                else if (methodData.avg_confidence < 0.6) accuracyClass = 'accuracy-low';
                
                html += `
                    <tr class="${accuracyClass}">
                        <td><strong>${method}</strong></td>
                        <td>${methodData.total_predictions}</td>
                        <td>${accuracy}%</td>
                        <td>${confidence}%</td>
                        <td>-</td>
                        <td>今天</td>
                        <td>
                            <span class="performance-badge bg-primary">推薦</span>
                        </td>
                    </tr>
                `;
            });
            
            tbody.innerHTML = html;
        }

        // 添加刷新狀態指示器
        function showRefreshIndicator(show = true) {
            const indicators = document.querySelectorAll('.spinner-border');
            indicators.forEach(indicator => {
                indicator.style.display = show ? 'inline-block' : 'none';
            });
        }

        // 添加錯誤處理
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'alert alert-warning alert-dismissible fade show';
            errorDiv.innerHTML = `
                <strong>注意:</strong> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('.container-fluid').insertBefore(errorDiv, document.querySelector('.row'));
        }

        // 改進的刷新函數
        async function refreshDashboard() {
            const lotteryType = document.getElementById('lotteryType').value;
            const days = document.getElementById('analysisRange').value;
            const preference = document.getElementById('userPreference').value;
            
            showRefreshIndicator(true);
            
            try {
                // 並行載入數據，但增加超時處理
                const timeoutPromise = new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('請求超時')), 10000)
                );
                
                const [analysisData, recommendationData] = await Promise.race([
                    Promise.all([
                        loadAnalysisData(lotteryType, days),
                        loadRecommendationData(lotteryType, preference)
                    ]),
                    timeoutPromise
                ]);
                
                // 更新儀表板
                updateSummaryCards(analysisData);
                updateRecommendations(recommendationData);
                updateCharts(analysisData);
                updateMethodTable(analysisData);
                
                console.log('儀表板數據更新成功');
                
            } catch (error) {
                console.error('儀表板刷新失敗:', error);
                showError('數據更新失敗，顯示的可能是舊數據');
            } finally {
                showRefreshIndicator(false);
            }
        }

        // 定期自動刷新，增加智能間隔
        let refreshInterval = 5 * 60 * 1000; // 初始5分鐘
        let consecutiveErrors = 0;

        function scheduleNextRefresh() {
            // 如果連續錯誤，延長刷新間隔
            if (consecutiveErrors > 2) {
                refreshInterval = Math.min(refreshInterval * 1.5, 15 * 60 * 1000); // 最多15分鐘
            } else if (consecutiveErrors === 0) {
                refreshInterval = 5 * 60 * 1000; // 恢復正常間隔
            }
            
            setTimeout(() => {
                refreshDashboard().then(() => {
                    consecutiveErrors = 0;
                }).catch(() => {
                    consecutiveErrors++;
                }).finally(() => {
                    scheduleNextRefresh();
                });
            }, refreshInterval);
        }

        // 啟動智能刷新
        scheduleNextRefresh();
    </script>
</body>
</html>