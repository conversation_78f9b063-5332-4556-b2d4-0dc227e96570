<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>預測系統管理 - 彩票預測系統</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .management-card {
            transition: transform 0.2s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .management-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }
        
        .status-badge {
            position: absolute;
            top: 10px;
            right: 10px;
        }
        
        .action-btn {
            min-width: 120px;
        }
        
        .result-container {
            max-height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
        }
        
        .loading-spinner {
            display: none;
        }
        
        .prediction-result {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 15px;
        }
        
        .number-ball {
            display: inline-block;
            width: 35px;
            height: 35px;
            line-height: 35px;
            text-align: center;
            border-radius: 50%;
            margin-right: 8px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .main-number { background-color: #007bff; color: white; }
        .special-number { background-color: #dc3545; color: white; }
        
        .confidence-bar {
            width: 100%;
            height: 20px;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff4757, #ffa502, #2ed573);
            border-radius: 10px;
            transition: width 0.8s ease;
        }
    </style>
</head>
<body>
    {% include 'navbar.html' %}

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-cogs"></i> 預測系統管理</h1>
            <span class="badge bg-success" id="systemStatus">系統運行正常</span>
        </div>

        <!-- 系統狀態概覽 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card management-card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-chart-line"></i> 系統狀態概覽</h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="systemOverview">
                            <div class="col-md-4 text-center">
                                <h3 id="totalPredictions">-</h3>
                                <small class="text-muted">總預測數</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <h3 id="lastUpdateTime">-</h3>
                                <small class="text-muted">最後更新時間</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <h3 id="systemHealth">-</h3>
                                <small class="text-muted">系統健康度</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要管理功能 -->
        <div class="row">
            <!-- 自動更新開獎數據 -->
            <div class="col-md-6 mb-4">
                <div class="card management-card h-100">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-sync-alt"></i> 自動更新開獎數據</h5>
                        <span class="status-badge badge bg-light text-dark" id="updateStatus">就緒</span>
                    </div>
                    <div class="card-body">
                        <p>從台灣彩券官方網站自動獲取最新開獎結果，確保預測基於最新數據。</p>
                        
                        <button class="btn btn-success action-btn" onclick="autoUpdateResults()">
                            <i class="fas fa-download"></i> 立即更新
                        </button>
                        
                        <div class="loading-spinner" id="updateSpinner">
                            <div class="spinner-border spinner-border-sm text-success" role="status"></div>
                            <span class="ms-2">更新中...</span>
                        </div>
                        
                        <div class="result-container" id="updateResults" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- 清空預測記錄 -->
            <div class="col-md-6 mb-4">
                <div class="card management-card h-100">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-trash-alt"></i> 清空預測記錄</h5>
                        <span class="status-badge badge bg-light text-dark" id="clearStatus">就緒</span>
                    </div>
                    <div class="card-body">
                        <p>清空現有的預測記錄，為新的預測流程做準備。系統會自動備份現有數據。</p>
                        
                        <div class="mb-3">
                            <select class="form-select" id="clearType">
                                <option value="all">清空所有類型</option>
                                <option value="powercolor">僅威力彩</option>
                                <option value="lotto649">僅大樂透</option>
                                <option value="dailycash">僅今彩539</option>
                            </select>
                        </div>
                        
                        <button class="btn btn-warning action-btn" onclick="clearPredictions()">
                            <i class="fas fa-eraser"></i> 清空記錄
                        </button>
                        
                        <div class="loading-spinner" id="clearSpinner">
                            <div class="spinner-border spinner-border-sm text-warning" role="status"></div>
                            <span class="ms-2">清空中...</span>
                        </div>
                        
                        <div class="result-container" id="clearResults" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 手動數據輸入 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card management-card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-edit"></i> 手動輸入開獎結果</h5>
                        <span class="status-badge badge bg-light text-dark" id="manualStatus">就緒</span>
                    </div>
                    <div class="card-body">
                        <p>如果自動更新暫時無法獲取最新數據，您可以手動輸入開獎結果。</p>
                        
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="manualLotteryType" class="form-label">彩票類型</label>
                                <select class="form-select" id="manualLotteryType" onchange="updateManualForm()">
                                    <option value="powercolor">威力彩</option>
                                    <option value="lotto649">大樂透</option>
                                    <option value="dailycash">今彩539</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="manualPeriod" class="form-label">期號</label>
                                <input type="text" class="form-control" id="manualPeriod" placeholder="例: 114000068">
                            </div>
                            <div class="col-md-3">
                                <label for="manualDate" class="form-label">開獎日期</label>
                                <input type="date" class="form-control" id="manualDate">
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-12">
                                <label class="form-label">開獎號碼</label>
                                <div id="manualNumbersInput">
                                    <!-- 動態生成號碼輸入框 -->
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary action-btn" onclick="submitManualData()">
                                <i class="fas fa-save"></i> 保存數據
                            </button>
                            <button class="btn btn-secondary action-btn" onclick="clearManualForm()">
                                <i class="fas fa-undo"></i> 清空表單
                            </button>
                        </div>
                        
                        <div class="loading-spinner" id="manualSpinner">
                            <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                            <span class="ms-2">保存中...</span>
                        </div>
                        
                        <div class="result-container" id="manualResults" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 六次機會策略功能 -->
        <div class="row">
            <div class="col-12 mb-4">
                <div class="card management-card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-dice-six"></i> 六次機會智能策略</h5>
                        <span class="status-badge badge bg-light text-dark" id="sixChancesStatus">就緒</span>
                    </div>
                    <div class="card-body">
                        <p>基於數學優化和覆蓋理論的系統化多票投注策略，提供6種不同的投注策略組合。</p>
                        
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <select class="form-select" id="sixChancesLotteryType">
                                    <option value="powercolor">威力彩</option>
                                    <option value="lotto649">大樂透</option>
                                    <option value="dailycash">今彩539</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="sixChancesStrategyType">
                                    <option value="mixed" selected>混合策略（推薦）</option>
                                    <option value="wheel">輪盤覆蓋</option>
                                    <option value="zone">區間平衡</option>
                                    <option value="hotcold">冷熱混合</option>
                                    <option value="math">數學優化</option>
                                    <option value="pattern">模式識別</option>
                                    <option value="ai">AI集成</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-warning action-btn" onclick="generateSixChancesStrategy()">
                                    <i class="fas fa-magic"></i> 生成策略
                                </button>
                            </div>
                            <div class="col-md-3">
                                <div class="loading-spinner" id="sixChancesSpinner">
                                    <div class="spinner-border spinner-border-sm text-warning" role="status"></div>
                                    <span class="ms-2">生成中...</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <a href="/six_chances" class="btn btn-outline-warning">
                                <i class="fas fa-external-link-alt"></i> 進入專用頁面
                            </a>
                        </div>
                        
                        <!-- 六次機會策略結果顯示 -->
                        <div id="sixChancesResults" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 改進的預測功能 -->
        <div class="row">
            <div class="col-12 mb-4">
                <div class="card management-card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-magic"></i> 改進的預測功能</h5>
                        <span class="status-badge badge bg-light text-dark" id="predictStatus">就緒</span>
                    </div>
                    <div class="card-body">
                        <p>使用改進的預測算法，基於期號-1的數據分析，生成單組最高信心度的預測結果。</p>
                        
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <select class="form-select" id="lotteryType">
                                    <option value="powercolor">威力彩</option>
                                    <option value="lotto649">大樂透</option>
                                    <option value="dailycash">今彩539</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-primary action-btn" onclick="generatePrediction()">
                                    <i class="fas fa-crystal-ball"></i> 智能預測
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-success action-btn" onclick="compareModels()">
                                    <i class="fas fa-chart-bar"></i> 多模型比較
                                </button>
                            </div>
                            <div class="col-md-3">
                                <div class="loading-spinner" id="predictSpinner">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                                    <span class="ms-2">預測中...</span>
                                </div>
                                <div class="loading-spinner" id="compareSpinner" style="display: none;">
                                    <div class="spinner-border spinner-border-sm text-success" role="status"></div>
                                    <span class="ms-2">比較中...</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 預測結果顯示 -->
                        <div id="predictionResults" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系統重置 -->
        <div class="row">
            <div class="col-12 mb-4">
                <div class="card management-card">
                    <div class="card-header bg-danger text-white">
                        <h5><i class="fas fa-power-off"></i> 系統重置</h5>
                        <span class="status-badge badge bg-light text-dark" id="resetStatus">就緒</span>
                    </div>
                    <div class="card-body">
                        <p class="text-danger">
                            <strong>警告：</strong>此操作將完全重置預測系統，包括清空所有預測記錄並重新初始化。
                            操作前會自動備份現有數據。
                        </p>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="confirmReset">
                            <label class="form-check-label" for="confirmReset">
                                我確認要執行系統重置操作
                            </label>
                        </div>
                        
                        <button class="btn btn-danger action-btn" onclick="resetSystem()" disabled id="resetBtn">
                            <i class="fas fa-redo"></i> 重置系統
                        </button>
                        
                        <div class="loading-spinner" id="resetSpinner">
                            <div class="spinner-border spinner-border-sm text-danger" role="status"></div>
                            <span class="ms-2">重置中...</span>
                        </div>
                        
                        <div class="result-container" id="resetResults" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemStatistics();
            
            // 設置重置確認
            document.getElementById('confirmReset').addEventListener('change', function() {
                document.getElementById('resetBtn').disabled = !this.checked;
            });
        });

        // 載入系統統計信息
        async function loadSystemStatistics() {
            try {
                const response = await fetch('/api/prediction_statistics');
                const result = await response.json();
                
                if (result.success) {
                    const stats = result.data.statistics;
                    
                    // 計算總預測數
                    let totalPredictions = 0;
                    for (const [type, stat] of Object.entries(stats)) {
                        totalPredictions += stat.total_records || 0;
                    }
                    
                    document.getElementById('totalPredictions').textContent = totalPredictions;
                    document.getElementById('lastUpdateTime').textContent = 
                        new Date(result.data.timestamp).toLocaleString('zh-TW');
                    document.getElementById('systemHealth').textContent = '良好';
                }
            } catch (error) {
                console.error('載入系統統計失敗:', error);
                document.getElementById('systemHealth').textContent = '異常';
                document.getElementById('systemStatus').textContent = '系統異常';
                document.getElementById('systemStatus').className = 'badge bg-danger';
            }
        }

        // 自動更新開獎結果
        async function autoUpdateResults() {
            const spinner = document.getElementById('updateSpinner');
            const results = document.getElementById('updateResults');
            const status = document.getElementById('updateStatus');
            
            try {
                // 顯示載入狀態
                spinner.style.display = 'block';
                results.style.display = 'none';
                status.textContent = '更新中';
                status.className = 'status-badge badge bg-warning';
                
                const response = await fetch('/api/auto_update_results', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                // 顯示結果
                results.innerHTML = '';
                results.style.display = 'block';
                
                if (result.success) {
                    status.textContent = '更新成功';
                    status.className = 'status-badge badge bg-success';
                    
                    const summary = result.data.summary;
                    results.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle"></i> 更新完成</h6>
                            <p>成功新增 ${summary.total_new_records} 筆開獎記錄</p>
                            <small class="text-muted">執行時間: ${summary.execution_time} 秒</small>
                        </div>
                    `;
                    
                    // 顯示詳細結果
                    for (const [type, updateResult] of Object.entries(result.data.results)) {
                        const statusClass = updateResult.status === 'success' ? 'success' : 'warning';
                        results.innerHTML += `
                            <div class="alert alert-${statusClass} alert-sm">
                                <strong>${type}:</strong> ${updateResult.message || updateResult.status}
                                ${updateResult.period ? `<br><small>最新期號: ${updateResult.period}</small>` : ''}
                            </div>
                        `;
                    }
                } else {
                    // 改善錯誤訊息顯示
                    status.textContent = '需要關注';
                    status.className = 'status-badge badge bg-warning';
                    
                    results.innerHTML = `
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> 數據更新狀況</h6>
                            <p><strong>系統狀態：</strong>運行正常，但數據源需要更新</p>
                        </div>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> 原因說明</h6>
                            <p>由於台灣彩券官方網站結構調整，自動爬蟲暫時無法獲取最新數據。</p>
                        </div>
                        
                        <div class="alert alert-light border">
                            <h6><i class="fas fa-lightbulb"></i> 解決方案</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>📊 繼續使用現有功能：</strong></p>
                                    <ul class="mb-3">
                                        <li>智能預測功能正常運作</li>
                                        <li>歷史數據分析可用</li>
                                        <li>預測模型持續優化</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>🔄 獲取最新數據：</strong></p>
                                    <ul class="mb-3">
                                        <li>等待系統自動修復（預計1-2天）</li>
                                        <li>手動查看台灣彩券官網</li>
                                        <li>使用預測功能無需最新開獎</li>
                                    </ul>
                                </div>
                            </div>
                        </div>`;
                        
                    // 顯示數據狀態信息
                    if (result.data && result.data.results) {
                        results.innerHTML += `<div class="alert alert-secondary">
                            <h6><i class="fas fa-database"></i> 現有數據狀態</h6>`;
                        
                        result.data.results.forEach(info => {
                            const dayStatus = info.days_old <= 1 ? 'success' : info.days_old <= 7 ? 'warning' : 'secondary';
                            results.innerHTML += `
                                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                                    <span><strong>${info.lottery}：</strong></span>
                                    <span class="text-${dayStatus}">
                                        ${info.latest_period} (${info.latest_date})
                                        <small class="text-muted">- ${info.total_records} 筆記錄</small>
                                    </span>
                                </div>`;
                        });
                        results.innerHTML += '</div>';
                    }
                }
                
            } catch (error) {
                status.textContent = '更新失敗';
                status.className = 'status-badge badge bg-danger';
                
                results.innerHTML = `
                    <div class="alert alert-danger">
                        <h6>更新失敗</h6>
                        <p>網絡錯誤: ${error.message}</p>
                    </div>
                `;
                results.style.display = 'block';
            } finally {
                spinner.style.display = 'none';
            }
        }

        // 清空預測記錄
        async function clearPredictions() {
            const spinner = document.getElementById('clearSpinner');
            const results = document.getElementById('clearResults');
            const status = document.getElementById('clearStatus');
            const clearType = document.getElementById('clearType').value;
            
            if (!confirm(`確定要清空${clearType === 'all' ? '所有' : clearType}預測記錄嗎？操作不可撤銷。`)) {
                return;
            }
            
            try {
                // 顯示載入狀態
                spinner.style.display = 'block';
                results.style.display = 'none';
                status.textContent = '清空中';
                status.className = 'status-badge badge bg-warning';
                
                const response = await fetch('/api/clear_predictions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        lottery_type: clearType,
                        backup: true
                    })
                });
                
                const result = await response.json();
                
                // 顯示結果
                results.innerHTML = '';
                results.style.display = 'block';
                
                if (result.success) {
                    status.textContent = '清空成功';
                    status.className = 'status-badge badge bg-success';
                    
                    results.innerHTML = `
                        <div class="alert alert-success">
                            <h6>清空完成</h6>
                            <p>${result.message}</p>
                            ${result.data.backup_success ? '<small>數據備份成功</small>' : '<small class="text-warning">備份失敗，但清空成功</small>'}
                        </div>
                    `;
                } else {
                    status.textContent = '清空失敗';
                    status.className = 'status-badge badge bg-danger';
                    
                    results.innerHTML = `
                        <div class="alert alert-danger">
                            <h6>清空失敗</h6>
                            <p>${result.error || '未知錯誤'}</p>
                        </div>
                    `;
                }
                
                // 更新系統統計
                setTimeout(loadSystemStatistics, 1000);
                
            } catch (error) {
                status.textContent = '清空失敗';
                status.className = 'status-badge badge bg-danger';
                
                results.innerHTML = `
                    <div class="alert alert-danger">
                        <h6>清空失敗</h6>
                        <p>網絡錯誤: ${error.message}</p>
                    </div>
                `;
                results.style.display = 'block';
            } finally {
                spinner.style.display = 'none';
            }
        }

        // 生成預測
        async function generatePrediction() {
            const spinner = document.getElementById('predictSpinner');
            const results = document.getElementById('predictionResults');
            const status = document.getElementById('predictStatus');
            const lotteryType = document.getElementById('lotteryType').value;
            
            try {
                // 顯示載入狀態
                spinner.style.display = 'block';
                results.style.display = 'none';
                status.textContent = '預測中';
                status.className = 'status-badge badge bg-warning';
                
                const response = await fetch(`/api/improved_prediction/${lotteryType}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    status.textContent = '預測成功';
                    status.className = 'status-badge badge bg-success';
                    
                    const prediction = result.data.prediction;
                    displayPredictionResult(prediction, lotteryType);
                } else {
                    status.textContent = '預測失敗';
                    status.className = 'status-badge badge bg-danger';
                    
                    results.innerHTML = `
                        <div class="alert alert-danger">
                            <h6>預測失敗</h6>
                            <p>${result.error || '未知錯誤'}</p>
                        </div>
                    `;
                    results.style.display = 'block';
                }
                
            } catch (error) {
                status.textContent = '預測失敗';
                status.className = 'status-badge badge bg-danger';
                
                results.innerHTML = `
                    <div class="alert alert-danger">
                        <h6>預測失敗</h6>
                        <p>網絡錯誤: ${error.message}</p>
                    </div>
                `;
                results.style.display = 'block';
            } finally {
                spinner.style.display = 'none';
            }
        }

        // 顯示預測結果
        function displayPredictionResult(prediction, lotteryType) {
            const results = document.getElementById('predictionResults');
            
            // 生成號碼球
            let numbersHtml = '';
            prediction.numbers.forEach(num => {
                numbersHtml += `<span class="number-ball main-number">${num}</span>`;
            });
            
            // 特別號
            let specialHtml = '';
            if (prediction.special_number) {
                specialHtml = `<span class="number-ball special-number">${prediction.special_number}</span>`;
            }
            
            // 信心度百分比
            const confidencePercent = (prediction.confidence * 100).toFixed(1);
            
            // 模型相關信息
            let modelInfo = '';
            if (prediction.model_used) {
                modelInfo += `<p><strong>使用模型:</strong> ${prediction.model_used}</p>`;
            }
            
            // 威力彩專屬信息
            let powercolorInfo = '';
            if (lotteryType === 'powercolor' && prediction.model_used === 'PowerColor專屬優化模型') {
                powercolorInfo = `
                    <div class="mt-3 p-3" style="background-color: rgba(40, 167, 69, 0.1); border-radius: 8px;">
                        <h6 class="text-success"><i class="fas fa-trophy"></i> 威力彩專屬優化</h6>
                        <small class="text-muted">
                            回測準確度: ${prediction.backtest_accuracy || '14.7%'} | 
                            平均信心度: ${prediction.avg_confidence || '79.3%'} | 
                            優化功能: ${prediction.optimization_applied ? '已啟用' : '未啟用'}
                        </small>
                    </div>
                `;
            }
            
            // 獲取信心度顏色
            function getConfidenceColor(percent) {
                if (percent >= 80) return '#2ed573'; // 綠色
                if (percent >= 60) return '#ffa502'; // 橙色
                return '#ff4757'; // 紅色
            }
            
            const confidenceColor = getConfidenceColor(parseFloat(confidencePercent));
            
            results.innerHTML = `
                <div class="prediction-result">
                    <div class="row">
                        <div class="col-md-8">
                            <h5><i class="fas fa-star"></i> 預測結果</h5>
                            <p><strong>期號:</strong> ${prediction.period}</p>
                            <p><strong>預測號碼:</strong></p>
                            <div class="mb-2">
                                ${numbersHtml}
                                ${specialHtml}
                            </div>
                            ${modelInfo}
                            <p><strong>預測時間:</strong> ${prediction.prediction_date}</p>
                            ${powercolorInfo}
                        </div>
                        <div class="col-md-4">
                            <h6>信心度</h6>
                            <div class="confidence-bar">
                                <div class="confidence-fill" style="width: ${confidencePercent}%; background-color: ${confidenceColor};"></div>
                            </div>
                            <div class="text-center mt-2">
                                <strong style="color: ${confidenceColor};">${confidencePercent}%</strong>
                            </div>
                            ${lotteryType === 'powercolor' && prediction.model_used === 'PowerColor專屬優化模型' ? 
                                '<div class="text-center mt-1"><small class="badge bg-success">專屬優化</small></div>' : 
                                ''}
                        </div>
                    </div>
                </div>
            `;
            
            results.style.display = 'block';
            
            // 更新系統統計
            setTimeout(loadSystemStatistics, 1000);
        }

        // 多模型比較預測
        async function compareModels() {
            const spinner = document.getElementById('compareSpinner');
            const results = document.getElementById('predictionResults');
            const status = document.getElementById('predictStatus');
            const lotteryType = document.getElementById('lotteryType').value;
            
            try {
                // 顯示載入狀態
                spinner.style.display = 'block';
                results.style.display = 'none';
                status.textContent = '比較中';
                status.className = 'status-badge badge bg-warning';
                
                const response = await fetch(`/api/multi_model_comparison/${lotteryType}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    status.textContent = '比較成功';
                    status.className = 'status-badge badge bg-success';
                    
                    displayModelComparison(result.data, lotteryType);
                } else {
                    status.textContent = '比較失敗';
                    status.className = 'status-badge badge bg-danger';
                    
                    results.innerHTML = `
                        <div class="alert alert-danger">
                            <h6>多模型比較失敗</h6>
                            <p>${result.error || '未知錯誤'}</p>
                        </div>
                    `;
                    results.style.display = 'block';
                }
                
            } catch (error) {
                status.textContent = '比較失敗';
                status.className = 'status-badge badge bg-danger';
                
                results.innerHTML = `
                    <div class="alert alert-danger">
                        <h6>多模型比較失敗</h6>
                        <p>網絡錯誤: ${error.message}</p>
                    </div>
                `;
                results.style.display = 'block';
            } finally {
                spinner.style.display = 'none';
            }
        }

        // 顯示多模型比較結果
        function displayModelComparison(data, lotteryType) {
            const results = document.getElementById('predictionResults');
            
            if (!data.models || data.models.length === 0) {
                results.innerHTML = `
                    <div class="alert alert-warning">
                        <h6>沒有可用的模型預測結果</h6>
                    </div>
                `;
                results.style.display = 'block';
                return;
            }
            
            let modelsHtml = '';
            
            data.models.forEach((model, index) => {
                const isWinner = index === 0; // 第一個是最高信心度
                const confidenceColor = getConfidenceColorForComparison(model.confidence * 100);
                
                // 生成號碼球
                let numbersHtml = '';
                model.prediction.numbers.forEach(num => {
                    numbersHtml += `<span class="number-ball main-number">${num}</span>`;
                });
                
                // 特別號
                let specialHtml = '';
                if (model.prediction.special_number) {
                    specialHtml = `<span class="number-ball special-number">${model.prediction.special_number}</span>`;
                }
                
                // 模型額外信息
                let additionalInfo = '';
                if (model.additional_info.backtest_accuracy) {
                    additionalInfo = `
                        <div class="mt-2">
                            <small class="badge bg-info">
                                回測準確度: ${model.additional_info.backtest_accuracy}
                            </small>
                            ${model.additional_info.avg_confidence ? 
                                `<small class="badge bg-secondary">平均信心: ${model.additional_info.avg_confidence}</small>` : 
                                ''}
                        </div>
                    `;
                }
                
                modelsHtml += `
                    <div class="card mb-3 ${isWinner ? 'border-warning' : ''}">
                        <div class="card-header d-flex justify-content-between align-items-center ${isWinner ? 'bg-warning text-dark' : 'bg-light'}">
                            <h6 class="mb-0">
                                ${isWinner ? '<i class="fas fa-trophy"></i> ' : ''}
                                ${model.name}
                                ${isWinner ? ' <span class="badge bg-success">最佳推薦</span>' : ''}
                            </h6>
                            <span class="badge" style="background-color: ${confidenceColor};">
                                ${model.confidence_percent}
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <p><strong>預測號碼:</strong></p>
                                    <div class="mb-2">
                                        ${numbersHtml}
                                        ${specialHtml}
                                    </div>
                                    <p><strong>期號:</strong> ${model.prediction.period}</p>
                                    <p><strong>專業領域:</strong> ${model.specialization}</p>
                                    ${additionalInfo}
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <h5 style="color: ${confidenceColor};">${model.confidence_percent}</h5>
                                        <small class="text-muted">信心度</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            // 信心度排名
            let rankingHtml = '<h6><i class="fas fa-list-ol"></i> 信心度排名</h6><ol class="list-group list-group-numbered">';
            data.confidence_ranking.forEach(rank => {
                rankingHtml += `
                    <li class="list-group-item d-flex justify-content-between align-items-start">
                        <div class="ms-2 me-auto">
                            <div class="fw-bold">${rank.model}</div>
                        </div>
                        <span class="badge bg-primary rounded-pill">${rank.confidence}</span>
                    </li>
                `;
            });
            rankingHtml += '</ol>';
            
            results.innerHTML = `
                <div class="prediction-result">
                    <h5><i class="fas fa-chart-bar"></i> 多模型比較結果</h5>
                    <p class="text-muted">以下顯示所有可用模型的預測結果，按信心度排序</p>
                    
                    ${modelsHtml}
                    
                    <div class="mt-4">
                        ${rankingHtml}
                    </div>
                    
                    <div class="mt-3 text-center">
                        <small class="text-muted">
                            比較時間: ${new Date(data.timestamp).toLocaleString('zh-TW')} | 
                            參與模型: ${data.models.length} 個
                        </small>
                    </div>
                </div>
            `;
            
            results.style.display = 'block';
            
            // 更新系統統計
            setTimeout(loadSystemStatistics, 1000);
        }

        // 獲取信心度顏色（比較用）
        function getConfidenceColorForComparison(percent) {
            if (percent >= 90) return '#2ed573'; // 深綠
            if (percent >= 80) return '#5cb85c'; // 綠色
            if (percent >= 70) return '#f0ad4e'; // 黃色
            if (percent >= 60) return '#fd7e14'; // 橙色
            return '#dc3545'; // 紅色
        }

        // 生成六次機會策略
        async function generateSixChancesStrategy() {
            const spinner = document.getElementById('sixChancesSpinner');
            const results = document.getElementById('sixChancesResults');
            const status = document.getElementById('sixChancesStatus');
            const lotteryType = document.getElementById('sixChancesLotteryType').value;
            const strategyType = document.getElementById('sixChancesStrategyType').value;
            
            try {
                // 顯示載入狀態
                spinner.style.display = 'block';
                results.style.display = 'none';
                status.textContent = '生成中';
                status.className = 'status-badge badge bg-warning text-dark';
                
                const response = await fetch(`/api/six_chances_strategy/${lotteryType}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        strategy: strategyType
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    status.textContent = '生成成功';
                    status.className = 'status-badge badge bg-success text-white';
                    
                    displaySixChancesResult(result.data);
                } else {
                    status.textContent = '生成失敗';
                    status.className = 'status-badge badge bg-danger text-white';
                    
                    results.innerHTML = `
                        <div class="alert alert-danger mt-3">
                            <h6>六次機會策略生成失敗</h6>
                            <p>${result.error || '未知錯誤'}</p>
                        </div>
                    `;
                    results.style.display = 'block';
                }
                
            } catch (error) {
                status.textContent = '生成失敗';
                status.className = 'status-badge badge bg-danger text-white';
                
                results.innerHTML = `
                    <div class="alert alert-danger mt-3">
                        <h6>六次機會策略生成失敗</h6>
                        <p>網絡錯誤: ${error.message}</p>
                    </div>
                `;
                results.style.display = 'block';
            } finally {
                spinner.style.display = 'none';
            }
        }
        
        // 顯示六次機會策略結果
        function displaySixChancesResult(data) {
            const results = document.getElementById('sixChancesResults');
            
            let resultHtml = `
                <div class="mt-3 p-3" style="background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%); border-radius: 10px; color: white;">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h5>$${data.total_investment}</h5>
                            <small>總投資</small>
                        </div>
                        <div class="col-md-3">
                            <h5>${data.total_chances}</h5>
                            <small>投注組數</small>
                        </div>
                        <div class="col-md-3">
                            <h5>${data.coverage_analysis.coverage_rate}</h5>
                            <small>覆蓋率</small>
                        </div>
                        <div class="col-md-3">
                            <h5>${data.win_probability.coverage_improvement}</h5>
                            <small>概率提升</small>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6><i class="fas fa-list"></i> ${data.strategy_description}</h6>
                    <div class="row">
            `;
            
            // 顯示前3組作為預覽
            data.combinations.slice(0, 3).forEach((combo, index) => {
                let numbersHtml = '';
                combo.numbers.forEach(num => {
                    numbersHtml += `<span class="number-ball main-number" style="display: inline-block; width: 25px; height: 25px; line-height: 25px; text-align: center; border-radius: 50%; margin: 1px; font-size: 11px; background: #007bff; color: white;">${num}</span>`;
                });
                
                let specialHtml = '';
                if (combo.special && data.lottery_info.special_numbers > 0) {
                    specialHtml = `<span class="number-ball special-number" style="display: inline-block; width: 25px; height: 25px; line-height: 25px; text-align: center; border-radius: 50%; margin: 1px; font-size: 11px; background: #dc3545; color: white;">${combo.special}</span>`;
                }
                
                resultHtml += `
                    <div class="col-md-4 mb-2">
                        <div class="card border-warning">
                            <div class="card-body p-2">
                                <h6 class="card-title mb-1" style="font-size: 0.9rem;">第${combo.chance}組</h6>
                                <div class="text-center">
                                    ${numbersHtml}
                                    ${specialHtml}
                                </div>
                                <small class="text-muted">${combo.strategy_detail}</small>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            if (data.combinations.length > 3) {
                resultHtml += `
                    <div class="col-md-12 text-center">
                        <small class="text-muted">... 還有 ${data.combinations.length - 3} 組投注組合</small>
                    </div>
                `;
            }
            
            resultHtml += `
                    </div>
                    <div class="text-center mt-3">
                        <a href="/six_chances" class="btn btn-outline-primary">
                            <i class="fas fa-eye"></i> 查看完整策略詳情
                        </a>
                    </div>
                </div>
            `;
            
            results.innerHTML = resultHtml;
            results.style.display = 'block';
        }

        // 重置系統
        async function resetSystem() {
            const spinner = document.getElementById('resetSpinner');
            const results = document.getElementById('resetResults');
            const status = document.getElementById('resetStatus');
            
            if (!document.getElementById('confirmReset').checked) {
                alert('請先確認重置操作');
                return;
            }
            
            if (!confirm('最後確認：真的要重置整個預測系統嗎？此操作不可撤銷！')) {
                return;
            }
            
            try {
                // 顯示載入狀態
                spinner.style.display = 'block';
                results.style.display = 'none';
                status.textContent = '重置中';
                status.className = 'status-badge badge bg-warning';
                
                const response = await fetch('/api/reset_prediction_system', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                // 顯示結果
                results.innerHTML = '';
                results.style.display = 'block';
                
                if (result.success) {
                    status.textContent = '重置成功';
                    status.className = 'status-badge badge bg-success';
                    
                    results.innerHTML = `
                        <div class="alert alert-success">
                            <h6>重置完成</h6>
                            <p>${result.message}</p>
                            <small>備份狀態: ${result.data.backup_success ? '成功' : '失敗'}</small>
                        </div>
                    `;
                } else {
                    status.textContent = '重置失敗';
                    status.className = 'status-badge badge bg-danger';
                    
                    results.innerHTML = `
                        <div class="alert alert-danger">
                            <h6>重置失敗</h6>
                            <p>${result.error || '未知錯誤'}</p>
                        </div>
                    `;
                }
                
                // 重置確認框
                document.getElementById('confirmReset').checked = false;
                document.getElementById('resetBtn').disabled = true;
                
                // 更新系統統計
                setTimeout(loadSystemStatistics, 2000);
                
            } catch (error) {
                status.textContent = '重置失敗';
                status.className = 'status-badge badge bg-danger';
                
                results.innerHTML = `
                    <div class="alert alert-danger">
                        <h6>重置失敗</h6>
                        <p>網絡錯誤: ${error.message}</p>
                    </div>
                `;
                results.style.display = 'block';
            } finally {
                spinner.style.display = 'none';
            }
        }
        
        // 手動數據輸入相關函數
        function updateManualForm() {
            const lotteryType = document.getElementById('manualLotteryType').value;
            const numbersContainer = document.getElementById('manualNumbersInput');
            
            let html = '';
            
            if (lotteryType === 'powercolor') {
                html = `
                    <div class="row">
                        <div class="col-md-8">
                            <label class="form-label">第一區 (選6個號碼)</label>
                            <div class="d-flex gap-2 flex-wrap">
                                ${Array.from({length: 6}, (_, i) => 
                                    `<input type="number" class="form-control main-number-input" style="width: 70px;" 
                                     placeholder="${i+1}" min="1" max="38" id="main${i+1}">`
                                ).join('')}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">第二區 (選1個號碼)</label>
                            <input type="number" class="form-control" style="width: 70px;" 
                                   placeholder="特" min="1" max="8" id="special">
                        </div>
                    </div>
                `;
            } else if (lotteryType === 'lotto649') {
                html = `
                    <div class="row">
                        <div class="col-md-8">
                            <label class="form-label">一般號碼 (選6個號碼)</label>
                            <div class="d-flex gap-2 flex-wrap">
                                ${Array.from({length: 6}, (_, i) => 
                                    `<input type="number" class="form-control main-number-input" style="width: 70px;" 
                                     placeholder="${i+1}" min="1" max="49" id="main${i+1}">`
                                ).join('')}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">特別號 (1個號碼)</label>
                            <input type="number" class="form-control" style="width: 70px;" 
                                   placeholder="特" min="1" max="49" id="special">
                        </div>
                    </div>
                `;
            } else if (lotteryType === 'dailycash') {
                html = `
                    <div class="row">
                        <div class="col-12">
                            <label class="form-label">號碼 (選5個號碼)</label>
                            <div class="d-flex gap-2 flex-wrap">
                                ${Array.from({length: 5}, (_, i) => 
                                    `<input type="number" class="form-control main-number-input" style="width: 70px;" 
                                     placeholder="${i+1}" min="1" max="39" id="main${i+1}">`
                                ).join('')}
                            </div>
                        </div>
                    </div>
                `;
            }
            
            numbersContainer.innerHTML = html;
            
            // 設定今天的日期為預設值
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('manualDate').value = today;
        }
        
        async function submitManualData() {
            const spinner = document.getElementById('manualSpinner');
            const results = document.getElementById('manualResults');
            const status = document.getElementById('manualStatus');
            
            try {
                // 收集表單數據
                const lotteryType = document.getElementById('manualLotteryType').value;
                const period = document.getElementById('manualPeriod').value;
                const date = document.getElementById('manualDate').value;
                
                if (!period || !date) {
                    alert('請填寫期號和開獎日期');
                    return;
                }
                
                // 收集號碼
                const mainNumbers = [];
                const mainInputs = document.querySelectorAll('.main-number-input');
                
                for (let input of mainInputs) {
                    const value = parseInt(input.value);
                    if (!value) {
                        alert('請填寫所有號碼');
                        return;
                    }
                    mainNumbers.push(value);
                }
                
                let specialNumber = null;
                const specialInput = document.getElementById('special');
                if (specialInput && lotteryType !== 'dailycash') {
                    specialNumber = parseInt(specialInput.value);
                    if (!specialNumber) {
                        alert('請填寫特別號');
                        return;
                    }
                }
                
                // 顯示載入狀態
                spinner.style.display = 'block';
                results.style.display = 'none';
                status.textContent = '保存中';
                status.className = 'status-badge badge bg-warning';
                
                // 準備API請求數據
                const requestData = {
                    lottery_type: lotteryType,
                    period: period,
                    date: date,
                    main_numbers: mainNumbers,
                    special_number: specialNumber
                };
                
                const response = await fetch('/api/manual_add_result', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                const result = await response.json();
                
                // 顯示結果
                results.innerHTML = '';
                results.style.display = 'block';
                
                if (result.success) {
                    status.textContent = '保存成功';
                    status.className = 'status-badge badge bg-success';
                    
                    results.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle"></i> 數據保存成功</h6>
                            <p>期號 ${period} 的開獎結果已成功添加到數據庫</p>
                            <small class="text-muted">彩票類型: ${lotteryType} | 開獎日期: ${date}</small>
                        </div>
                    `;
                    
                    // 清空表單
                    clearManualForm();
                    
                    // 更新系統統計
                    setTimeout(loadSystemStatistics, 1000);
                } else {
                    status.textContent = '保存失敗';
                    status.className = 'status-badge badge bg-danger';
                    
                    results.innerHTML = `
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle"></i> 保存失敗</h6>
                            <p>${result.error || '未知錯誤'}</p>
                        </div>
                    `;
                }
                
            } catch (error) {
                status.textContent = '保存失敗';
                status.className = 'status-badge badge bg-danger';
                
                results.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle"></i> 保存失敗</h6>
                        <p>網路錯誤: ${error.message}</p>
                    </div>
                `;
                results.style.display = 'block';
            } finally {
                spinner.style.display = 'none';
            }
        }
        
        function clearManualForm() {
            document.getElementById('manualPeriod').value = '';
            document.getElementById('manualDate').value = '';
            
            const inputs = document.querySelectorAll('#manualNumbersInput input');
            inputs.forEach(input => input.value = '');
        }
        
        // 頁面載入時初始化手動表單
        document.addEventListener('DOMContentLoaded', function() {
            updateManualForm();
        });
    </script>
</body>
</html>