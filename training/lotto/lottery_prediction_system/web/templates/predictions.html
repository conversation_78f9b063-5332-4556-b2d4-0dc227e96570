<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ lottery_name }}預測記錄 - 彩票預測系統</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <style>
        .match {
            background-color: #d4edda;
        }
        .number-ball {
            display: inline-block;
            width: 30px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            border-radius: 50%;
            margin-right: 5px;
            font-weight: bold;
            color: white;
        }
        .main-number {
            background-color: #007bff;
        }
        .special-number {
            background-color: #dc3545;
        }
        .matched {
            background-color: #28a745;
        }
    </style>
</head>
<body>
    {% include 'navbar.html' %}

    <div class="container mt-4">
        <h1 class="mb-4">{{ lottery_name }}預測記錄</h1>
        
        <div class="btn-group mb-4">
            <a href="/predictions?type=powercolor" class="btn btn-outline-primary {{ 'active' if lottery_type == 'powercolor' else '' }}">威力彩</a>
            <a href="/predictions?type=lotto649" class="btn btn-outline-primary {{ 'active' if lottery_type == 'lotto649' else '' }}">大樂透</a>
            <a href="/predictions?type=dailycash" class="btn btn-outline-primary {{ 'active' if lottery_type == 'dailycash' else '' }}">今彩539</a>
        </div>
        
        {% if records %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>期數</th>
                            <th>預測號碼</th>
                            <th>實際號碼</th>
                            <th>匹配數</th>
                            <th>預測日期</th>
                            <th>詳情</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for record in records %}
                        <tr>
                            <td>{{ record.Period }}</td>
                            <td>
                                {% if lottery_type == 'powercolor' or lottery_type == 'lotto649' %}
                                    {% for i in range(1, 7) %}
                                        {% set pred_key = 'PredA' + i|string %}
                                        <span class="number-ball main-number">{{ record[pred_key] }}</span>
                                    {% endfor %}
                                    <span class="number-ball special-number">{{ record.PredSpecial }}</span>
                                {% elif lottery_type == 'dailycash' %}
                                    {% for i in range(1, 6) %}
                                        {% set pred_key = 'PredA' + i|string %}
                                        <span class="number-ball main-number">{{ record[pred_key] }}</span>
                                    {% endfor %}
                                {% endif %}
                            </td>
                            <td>
                                {% if record.ActualA1 %}
                                    {% if lottery_type == 'powercolor' or lottery_type == 'lotto649' %}
                                        {% for i in range(1, 7) %}
                                            {% set actual_key = 'ActualA' + i|string %}
                                            {% set pred_key = 'PredA' + i|string %}
                                            {% set is_match = record[actual_key] and record[pred_key] and record[actual_key]|int == record[pred_key]|int %}
                                            <span class="number-ball {{ 'matched' if is_match else 'main-number' }}">{{ record[actual_key] }}</span>
                                        {% endfor %}
                                        <span class="number-ball {{ 'matched' if record.ActualSpecial|int == record.PredSpecial|int else 'special-number' }}">{{ record.ActualSpecial }}</span>
                                    {% elif lottery_type == 'dailycash' %}
                                        {% for i in range(1, 6) %}
                                            {% set actual_key = 'ActualA' + i|string %}
                                            {% set pred_key = 'PredA' + i|string %}
                                            {% set is_match = record[actual_key] and record[pred_key] and record[actual_key]|int == record[pred_key]|int %}
                                            <span class="number-ball {{ 'matched' if is_match else 'main-number' }}">{{ record[actual_key] }}</span>
                                        {% endfor %}
                                    {% endif %}
                                {% else %}
                                    尚未開獎
                                {% endif %}
                            </td>
                            <td>{{ record.MatchCount if record.MatchCount is defined else '-' }}</td>
                            <td>{{ record.PredictionDate }}</td>
                            <td>
                                <button class="btn btn-sm btn-info" onclick="showDetails('{{ record.Period }}')">詳情</button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="alert alert-info">
                沒有找到{{ lottery_name }}的預測記錄
            </div>
        {% endif %}
    </div>

    <!-- 詳情模態框 -->
    <div class="modal fade" id="detailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">預測詳情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="modalContent">
                    載入中...
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 顯示預測詳情
        function showDetails(period) {
            const modal = new bootstrap.Modal(document.getElementById('detailsModal'));
            
            // 找到對應的記錄
            const records = {% if records %}{{ records|tojson|safe }}{% else %}[]{% endif %};
            const record = records.find(r => r.Period === period);
            
            if (!record) {
                document.getElementById('modalContent').innerHTML = '找不到該記錄';
                modal.show();
                return;
            }
            
            let html = '<h4>期數: ' + (record.Period || '') + '</h4>';
            html += '<p>預測日期: ' + (record.PredictionDate || '') + '</p>';
            
            // 預測方法
            if (record.PredictionMethods) {
                let methods;
                try {
                    methods = typeof record.PredictionMethods === 'string' 
                        ? JSON.parse(record.PredictionMethods) 
                        : record.PredictionMethods;
                    if (Array.isArray(methods)) {
                        html += '<p>預測方法: ' + methods.join(', ') + '</p>';
                    } else {
                        html += '<p>預測方法: ' + String(record.PredictionMethods) + '</p>';
                    }
                } catch (e) {
                    html += '<p>預測方法: ' + String(record.PredictionMethods) + '</p>';
                }
            }
            
            // 預測詳情
            if (record.PredictionDetails) {
                html += '<h5>預測詳情:</h5>';
                try {
                    const details = typeof record.PredictionDetails === 'string' 
                        ? JSON.parse(record.PredictionDetails) 
                        : record.PredictionDetails;
                    
                    html += '<pre class="bg-light p-3">' + JSON.stringify(details, null, 2) + '</pre>';
                } catch (e) {
                    html += '<p>' + String(record.PredictionDetails) + '</p>';
                }
            }
            
            // 候選結果
            if (record.CandidateIndex !== undefined && record.CandidateIndex !== null) {
                html += '<p>候選結果索引: ' + String(record.CandidateIndex) + '</p>';
            }
            
            // 信心分數
            if (record.Confidence) {
                html += '<p>信心分數: ' + String(record.Confidence) + '</p>';
            }
            
            document.getElementById('modalContent').innerHTML = html;
            modal.show();
        }
    </script>
</body>
</html>