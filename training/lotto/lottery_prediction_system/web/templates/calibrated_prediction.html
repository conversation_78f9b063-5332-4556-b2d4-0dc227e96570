{% extends "base.html" %}

{% block title %}校正預測模型{% endblock %}

{% block extra_css %}
<style>
.calibrated-header {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(67, 233, 123, 0.3);
}

.model-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.model-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.model-card .card-header {
    background: linear-gradient(45deg, #43e97b 0%, #38f9d7 100%);
    color: white;
    padding: 1rem 1.5rem;
    font-weight: 600;
}

.calibration-controls {
    background: #f0fdf4;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 2px solid #bbf7d0;
}

.prediction-display {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border-radius: 15px;
    padding: 2rem;
    margin: 1.5rem 0;
    text-align: center;
}

.number-ball-enhanced {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    font-size: 16px;
    margin: 5px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
}

.special-ball-enhanced {
    background: linear-gradient(135deg, #ef4444 0%, #f97316 100%);
}

.confidence-display {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.confidence-bar {
    height: 20px;
    background: #e5e7eb;
    border-radius: 10px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #059669);
    border-radius: 10px;
    transition: width 1s ease-in-out;
}

.analysis-details {
    background: #f8fafc;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1rem;
    border-left: 5px solid #43e97b;
}

.model-performance {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.performance-item {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.performance-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #059669;
}

.performance-label {
    font-size: 0.9rem;
    color: #6b7280;
    margin-top: 0.5rem;
}

.calibration-status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.calibration-active {
    background: #d1fae5;
    color: #059669;
}

.calibration-inactive {
    background: #fee2e2;
    color: #dc2626;
}

.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 9999;
    justify-content: center;
    align-items: center;
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.loading-spinner-lg {
    width: 60px;
    height: 60px;
    border: 6px solid #f3f4f6;
    border-top: 6px solid #43e97b;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.comparison-section {
    background: #f9fafb;
    border-radius: 15px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.comparison-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: white;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.btn-calibrated {
    background: linear-gradient(45deg, #43e97b, #38f9d7);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-calibrated:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(67, 233, 123, 0.4);
    color: white;
}

.btn-calibrated:disabled {
    opacity: 0.6;
    transform: none;
    box-shadow: none;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 校正模型標題 -->
    <div class="calibrated-header text-center">
        <h1><i class="fas fa-brain"></i> 校正預測模型 v2.0</h1>
        <p class="mb-0">基於歷史校正的智能預測系統 | 動態參數調整 | 多維度分析融合</p>
    </div>

    <div class="row">
        <!-- 左側：模型控制 -->
        <div class="col-md-4">
            <!-- 模型配置 -->
            <div class="model-card">
                <div class="card-header">
                    <i class="fas fa-sliders-h"></i> 模型配置
                </div>
                <div class="card-body">
                    <form id="calibratedPredictionForm">
                        <!-- 彩票類型選擇 -->
                        <div class="mb-3">
                            <label for="lotteryType" class="form-label">
                                <i class="fas fa-ticket-alt"></i> 彩票類型
                            </label>
                            <select class="form-select" id="lotteryType" name="lottery_type" required>
                                <option value="">請選擇彩票類型</option>
                                <option value="powercolor">威力彩</option>
                                <option value="lotto649">大樂透</option>
                                <option value="dailycash">今彩539</option>
                            </select>
                        </div>

                        <!-- 校正設定 -->
                        <div class="calibration-controls">
                            <label class="form-label mb-3">
                                <i class="fas fa-cogs"></i> 校正設定
                            </label>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="dynamicCalibration" checked>
                                <label class="form-check-label" for="dynamicCalibration">
                                    <strong>動態校正</strong><br>
                                    <small class="text-muted">根據最近期數自動調整參數</small>
                                </label>
                            </div>
                            
                            <div class="alert alert-info" style="font-size: 0.9rem;">
                                <i class="fas fa-info-circle"></i> 
                                <strong>校正特色:</strong><br>
                                • 頻率分析權重調整<br>
                                • 號碼分布均勻度檢測<br>
                                • 連續期數相關性分析<br>
                                • 信心度智能評估
                            </div>
                        </div>

                        <!-- 預測按鈕 -->
                        <button type="submit" class="btn btn-calibrated w-100" id="predictBtn">
                            <i class="fas fa-magic"></i> 開始校正預測
                        </button>
                    </form>
                </div>
            </div>

            <!-- 模型性能 -->
            <div class="model-card">
                <div class="card-header">
                    <i class="fas fa-chart-bar"></i> 模型性能
                </div>
                <div class="card-body">
                    <div id="modelPerformance">
                        <div class="text-center text-muted">
                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                            <p>選擇彩票類型查看性能統計</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 校正歷史 -->
            <div class="model-card">
                <div class="card-header">
                    <i class="fas fa-history"></i> 校正歷史
                </div>
                <div class="card-body">
                    <div id="calibrationHistory">
                        <div class="text-center text-muted">
                            <i class="fas fa-clock fa-2x mb-2"></i>
                            <p>執行預測後顯示校正歷史</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右側：預測結果 -->
        <div class="col-md-8">
            <!-- 預測結果展示 -->
            <div id="predictionResults" style="display: none;"></div>

            <!-- 空狀態 -->
            <div class="model-card text-center" id="emptyState">
                <div class="card-body py-5">
                    <i class="fas fa-brain fa-3x text-success mb-3"></i>
                    <h4 class="text-muted">校正預測模型就緒</h4>
                    <p class="text-muted">基於歷史校正的智能預測系統，提供更準確的號碼推薦</p>
                    
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="text-center p-3">
                                <i class="fas fa-chart-line fa-2x text-success mb-2"></i>
                                <h6>動態校正</h6>
                                <small class="text-muted">根據最近數據自動調整預測參數</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3">
                                <i class="fas fa-layers fa-2x text-info mb-2"></i>
                                <h6>多維分析</h6>
                                <small class="text-muted">頻率、模式、趨勢三重分析融合</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3">
                                <i class="fas fa-target fa-2x text-warning mb-2"></i>
                                <h6>智能選號</h6>
                                <small class="text-muted">加權隨機選擇最優候選號碼</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 載入遮罩 -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <div class="loading-spinner-lg"></div>
        <h5>校正模型運算中...</h5>
        <p class="text-muted">正在執行多維度分析與動態校正</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 監聽彩票類型變化
    $('#lotteryType').on('change', function() {
        const lotteryType = this.value;
        if (lotteryType) {
            loadModelPerformance(lotteryType);
        }
    });

    // 預測表單提交
    $('#calibratedPredictionForm').on('submit', function(e) {
        e.preventDefault();
        executeCalibratedPrediction();
    });
    
    function loadModelPerformance(lotteryType) {
        $.ajax({
            url: `/api/calibrated_prediction/performance/${lotteryType}`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    displayModelPerformance(response.data);
                } else {
                    $('#modelPerformance').html(`
                        <div class="text-center text-muted">
                            <i class="fas fa-exclamation-triangle"></i>
                            <p>暫無性能數據</p>
                        </div>
                    `);
                }
            },
            error: function(xhr, status, error) {
                $('#modelPerformance').html(`
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> 
                        載入性能數據失敗
                    </div>
                `);
            }
        });
    }
    
    function displayModelPerformance(performance) {
        if (performance.error || performance.total_predictions === 0) {
            $('#modelPerformance').html(`
                <div class="text-center text-muted">
                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                    <p>尚無歷史性能數據</p>
                    <small>執行預測後將顯示性能統計</small>
                </div>
            `);
            return;
        }
        
        const html = `
            <div class="model-performance">
                <div class="performance-item">
                    <div class="performance-value">${performance.total_predictions}</div>
                    <div class="performance-label">預測次數</div>
                </div>
                <div class="performance-item">
                    <div class="performance-value">${(performance.avg_confidence * 100).toFixed(1)}%</div>
                    <div class="performance-label">平均信心度</div>
                </div>
                <div class="performance-item">
                    <div class="performance-value">${(performance.max_confidence * 100).toFixed(1)}%</div>
                    <div class="performance-label">最高信心度</div>
                </div>
                <div class="performance-item">
                    <div class="performance-value">${performance.model_version}</div>
                    <div class="performance-label">模型版本</div>
                </div>
            </div>
            
            <div class="mt-3 text-center">
                <span class="calibration-status ${performance.calibration_active ? 'calibration-active' : 'calibration-inactive'}">
                    <i class="fas fa-${performance.calibration_active ? 'check' : 'times'}-circle"></i>
                    校正功能${performance.calibration_active ? '已啟用' : '未啟用'}
                </span>
            </div>
        `;
        
        $('#modelPerformance').html(html);
    }
    
    function executeCalibratedPrediction() {
        const formData = {
            lottery_type: $('#lotteryType').val(),
            use_dynamic_calibration: $('#dynamicCalibration').is(':checked')
        };
        
        if (!formData.lottery_type) {
            alert('請選擇彩票類型');
            return;
        }
        
        // 顯示載入動畫
        $('#loadingOverlay').css('display', 'flex');
        $('#predictionResults').hide();
        $('#emptyState').hide();
        $('#predictBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 預測中...');
        
        // 發送預測請求
        $.ajax({
            url: `/api/calibrated_prediction/${formData.lottery_type}`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            timeout: 30000,
            success: function(response) {
                if (response.success) {
                    displayPredictionResults(response.data.prediction);
                    
                    // 更新性能統計
                    if (response.data.prediction.model_performance) {
                        displayModelPerformance(response.data.prediction.model_performance);
                    }
                } else {
                    showError('校正預測失敗: ' + (response.error || '未知錯誤'));
                }
            },
            error: function(xhr, status, error) {
                let errorMsg = '校正預測執行失敗';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMsg = xhr.responseJSON.error;
                } else if (status === 'timeout') {
                    errorMsg = '預測執行超時，請稍後再試';
                }
                showError(errorMsg);
            },
            complete: function() {
                $('#loadingOverlay').hide();
                $('#predictBtn').prop('disabled', false).html('<i class="fas fa-magic"></i> 開始校正預測');
            }
        });
    }
    
    function displayPredictionResults(prediction) {
        const lotteryNames = {
            'powercolor': '威力彩',
            'lotto649': '大樂透',
            'dailycash': '今彩539'
        };
        
        const calibrationStatus = prediction.calibration_applied ? 
            '<span class="calibration-status calibration-active"><i class="fas fa-check-circle"></i> 動態校正已啟用</span>' :
            '<span class="calibration-status calibration-inactive"><i class="fas fa-times-circle"></i> 使用預設參數</span>';
        
        let html = `
            <div class="model-card">
                <div class="card-header">
                    <i class="fas fa-star"></i> ${lotteryNames[prediction.lottery_type] || prediction.lottery_type} 校正預測結果
                    <span class="float-end">
                        <span class="badge bg-light text-dark">期號: ${prediction.period || 'N/A'}</span>
                    </span>
                </div>
                <div class="card-body">
                    <div class="prediction-display">
                        <h5 class="mb-3">
                            <i class="fas fa-magic"></i> 推薦號碼
                            ${calibrationStatus}
                        </h5>
                        
                        <div class="mb-3">
        `;
        
        // 顯示主號碼
        prediction.numbers.forEach(num => {
            html += `<span class="number-ball-enhanced">${num}</span>`;
        });
        
        // 顯示特別號
        if (prediction.special_number) {
            html += `<span class="mx-2">+</span><span class="number-ball-enhanced special-ball-enhanced">${prediction.special_number}</span>`;
        }
        
        html += `
                        </div>
                        
                        <div class="confidence-display">
                            <div class="d-flex justify-content-between align-items-center">
                                <strong>模型信心度</strong>
                                <span class="badge bg-success">${Math.round(prediction.confidence * 100)}%</span>
                            </div>
                            <div class="confidence-bar">
                                <div class="confidence-fill" style="width: ${prediction.confidence * 100}%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="analysis-details">
                        <h6><i class="fas fa-microscope"></i> 分析詳情</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <small><strong>預測方法:</strong><br>
                                    <span class="badge bg-primary me-1">${prediction.method}</span>
                                </small>
                            </div>
                            <div class="col-md-6">
                                <small><strong>模型版本:</strong><br>
                                    <span class="badge bg-info me-1">${prediction.model_version}</span>
                                </small>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <small><strong>預測時間:</strong><br>${new Date(prediction.prediction_date).toLocaleString()}</small>
                            </div>
                            <div class="col-md-6">
                                <small><strong>分析期數:</strong><br>${prediction.analysis_details ? prediction.analysis_details.analysis_periods : 'N/A'}</small>
                            </div>
                        </div>
                        
                        ${prediction.analysis_details ? `
                            <div class="mt-3">
                                <small><strong>候選分析:</strong><br>
                                分析了 ${prediction.analysis_details.total_numbers_analyzed} 個號碼，
                                選出前 ${prediction.analysis_details.top_candidates ? prediction.analysis_details.top_candidates.length : 0} 個候選
                                </small>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
        
        $('#predictionResults').html(html).show();
        $('#emptyState').hide();
    }
    
    function showError(message) {
        const errorHtml = `
            <div class="model-card">
                <div class="card-body text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                    <h5 class="text-danger">預測失敗</h5>
                    <p class="text-muted">${message}</p>
                    <button class="btn btn-outline-primary" onclick="location.reload()">
                        <i class="fas fa-redo"></i> 重新嘗試
                    </button>
                </div>
            </div>
        `;
        $('#predictionResults').html(errorHtml).show();
        $('#emptyState').hide();
    }
});
</script>
{% endblock %}