{% extends "base.html" %}

{% block title %}Phase 3 儀表板{% endblock %}

{% block extra_head %}
<style>
.phase3-dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.dashboard-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-3px);
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-active { background-color: #28a745; }
.status-partial { background-color: #ffc107; }
.status-inactive { background-color: #dc3545; }

.metric-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 25%, #4facfe 50%, #00f2fe 100%);
    color: white;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 1rem;
}

.component-badge {
    display: inline-block;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    margin: 0.2rem;
    font-weight: 500;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.feature-item {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateX(5px);
}

.feature-universal { border-left-color: #667eea; }
.feature-tracking { border-left-color: #f093fb; }
.feature-realtime { border-left-color: #00f2fe; }
.feature-accuracy { border-left-color: #4facfe; }
.feature-scheduler { border-left-color: #f5576c; }
.feature-visualization { border-left-color: #96c93d; }

.quick-actions {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    margin-top: 2rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Phase 3 儀表板標題 -->
    <div class="phase3-dashboard-header text-center">
        <h1><i class="fas fa-tachometer-alt"></i> Phase 3 系統儀表板</h1>
        <p class="mb-0">高級功能監控 | 系統狀態 | 性能指標</p>
    </div>

    <div class="row">
        <!-- 左側：系統狀態 -->
        <div class="col-md-8">
            <!-- 系統狀態概覽 -->
            <div class="dashboard-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-server"></i> 系統狀態</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Phase 3 整合狀態</h6>
                            {% if phase3_available %}
                                <span class="status-indicator status-active"></span>
                                <span class="text-success">已啟用</span>
                            {% else %}
                                <span class="status-indicator status-inactive"></span>
                                <span class="text-danger">未啟用</span>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <h6>已載入組件</h6>
                            {% if dashboard_data.system_status %}
                                <strong>{{ dashboard_data.system_status.components_loaded }}</strong> / 6 個
                            {% else %}
                                <span class="text-muted">0 / 6 個</span>
                            {% endif %}
                        </div>
                    </div>

                    {% if dashboard_data.system_status and dashboard_data.system_status.components %}
                    <div class="mt-3">
                        <h6>載入的組件</h6>
                        {% for component in dashboard_data.system_status.components %}
                            <span class="component-badge">{{ component }}</span>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Phase 3 功能概覽 -->
            <div class="dashboard-card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-rocket"></i> Phase 3 功能</h5>
                </div>
                <div class="card-body">
                    <div class="feature-grid">
                        <div class="feature-item feature-universal">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-magic fa-lg text-primary me-3"></i>
                                <h6 class="mb-0">通用預測框架</h6>
                            </div>
                            <p class="text-muted small mb-2">跨彩票學習技術，提供更準確的預測</p>
                            <a href="/universal_prediction" class="btn btn-sm btn-outline-primary">開始預測</a>
                        </div>

                        <div class="feature-item feature-tracking">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-chart-line fa-lg text-success me-3"></i>
                                <h6 class="mb-0">預測追蹤系統</h6>
                            </div>
                            <p class="text-muted small mb-2">完整記錄預測結果和統計分析</p>
                            <a href="/tracking_analytics" class="btn btn-sm btn-outline-success">查看分析</a>
                        </div>

                        <div class="feature-item feature-realtime">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-sync-alt fa-lg text-info me-3"></i>
                                <h6 class="mb-0">實時數據管理</h6>
                            </div>
                            <p class="text-muted small mb-2">自動同步最新開獎數據</p>
                            <button class="btn btn-sm btn-outline-info" onclick="triggerRealTimeUpdate()">
                                立即同步
                            </button>
                        </div>

                        <div class="feature-item feature-accuracy">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-bullseye fa-lg text-warning me-3"></i>
                                <h6 class="mb-0">準確度評估</h6>
                            </div>
                            <p class="text-muted small mb-2">智能評估預測策略性能</p>
                            <button class="btn btn-sm btn-outline-warning" onclick="loadAccuracyAssessment()">
                                查看評估
                            </button>
                        </div>

                        <div class="feature-item feature-scheduler">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-clock fa-lg text-danger me-3"></i>
                                <h6 class="mb-0">自動化調度</h6>
                            </div>
                            <p class="text-muted small mb-2">定時執行預測和數據更新</p>
                            <button class="btn btn-sm btn-outline-danger" onclick="loadSchedulerStatus()">
                                調度狀態
                            </button>
                        </div>

                        <div class="feature-item feature-visualization">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-chart-pie fa-lg text-secondary me-3"></i>
                                <h6 class="mb-0">可視化報告</h6>
                            </div>
                            <p class="text-muted small mb-2">生成精美的分析報告圖表</p>
                            <button class="btn btn-sm btn-outline-secondary" onclick="generateReport()">
                                生成報告
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右側：快速操作和統計 -->
        <div class="col-md-4">
            <!-- 快速操作 -->
            <div class="dashboard-card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-bolt"></i> 快速操作</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="/universal_prediction" class="btn btn-primary">
                            <i class="fas fa-magic"></i> 開始預測
                        </a>
                        <button class="btn btn-outline-info" onclick="checkSystemHealth()">
                            <i class="fas fa-heartbeat"></i> 系統健康檢查
                        </button>
                        <button class="btn btn-outline-warning" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i> 刷新儀表板
                        </button>
                    </div>
                </div>
            </div>

            <!-- 系統指標 -->
            <div class="dashboard-card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-bar"></i> 系統指標</h5>
                </div>
                <div class="card-body">
                    <div class="metric-card">
                        <h4 id="totalPredictions">-</h4>
                        <small>今日預測次數</small>
                    </div>
                    <div class="metric-card">
                        <h4 id="averageAccuracy">-</h4>
                        <small>平均準確度</small>
                    </div>
                    <div class="metric-card">
                        <h4 id="systemUptime">-</h4>
                        <small>系統運行時間</small>
                    </div>
                </div>
            </div>

            <!-- 最近活動 -->
            <div class="dashboard-card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="fas fa-history"></i> 最近活動</h5>
                </div>
                <div class="card-body">
                    <div id="recentActivities">
                        <div class="text-center text-muted">
                            <i class="fas fa-spinner fa-spin"></i>
                            <p>載入中...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    loadDashboardMetrics();
    loadRecentActivities();
    
    // 每30秒刷新指標
    setInterval(loadDashboardMetrics, 30000);
});

function loadDashboardMetrics() {
    $.ajax({
        url: '/api/phase3/system_status',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                updateMetrics(response.data);
            }
        },
        error: function() {
            // 靜默處理錯誤
        }
    });
}

function updateMetrics(data) {
    // 更新指標卡片（使用模擬數據）
    $('#totalPredictions').text(Math.floor(Math.random() * 50 + 10));
    $('#averageAccuracy').text((Math.random() * 0.3 + 0.6).toFixed(1) + '%');
    $('#systemUptime').text(formatUptime(Date.now()));
}

function formatUptime(startTime) {
    const now = new Date();
    const uptime = now.getTime() - (startTime - Math.random() * 86400000);
    const hours = Math.floor(uptime / (1000 * 60 * 60));
    return hours + ' 小時';
}

function loadRecentActivities() {
    // 模擬最近活動數據
    const activities = [
        { time: '5分鐘前', action: '威力彩預測完成', status: 'success' },
        { time: '15分鐘前', action: '數據同步更新', status: 'info' },
        { time: '30分鐘前', action: '大樂透預測完成', status: 'success' },
        { time: '1小時前', action: '準確度評估運行', status: 'warning' },
        { time: '2小時前', action: '今彩539預測完成', status: 'success' }
    ];
    
    let html = '';
    activities.forEach(activity => {
        const badgeClass = {
            'success': 'bg-success',
            'info': 'bg-info',
            'warning': 'bg-warning',
            'danger': 'bg-danger'
        }[activity.status] || 'bg-secondary';
        
        html += `
            <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
                <div>
                    <small class="text-muted">${activity.time}</small>
                    <div>${activity.action}</div>
                </div>
                <span class="badge ${badgeClass}">●</span>
            </div>
        `;
    });
    
    $('#recentActivities').html(html);
}

function triggerRealTimeUpdate() {
    const btn = event.target;
    const originalHtml = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 同步中...';
    btn.disabled = true;
    
    $.ajax({
        url: '/api/phase3/realtime_update',
        method: 'POST',
        success: function(response) {
            if (response.success) {
                showToast('數據同步完成', 'success');
            } else {
                showToast('同步失敗: ' + response.error, 'error');
            }
        },
        error: function() {
            showToast('同步請求失敗', 'error');
        },
        complete: function() {
            btn.innerHTML = originalHtml;
            btn.disabled = false;
        }
    });
}

function loadAccuracyAssessment() {
    window.open('/api/phase3/accuracy_assessment', '_blank');
}

function loadSchedulerStatus() {
    window.open('/api/phase3/scheduler_status', '_blank');
}

function generateReport() {
    const reportData = {
        report_type: 'dashboard_summary',
        lottery_type: null
    };
    
    $.ajax({
        url: '/api/phase3/visualization_report',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(reportData),
        success: function(response) {
            if (response.success) {
                showToast('報告生成完成', 'success');
            } else {
                showToast('報告生成失敗: ' + response.error, 'error');
            }
        },
        error: function() {
            showToast('報告生成請求失敗', 'error');
        }
    });
}

function checkSystemHealth() {
    const btn = event.target;
    const originalHtml = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 檢查中...';
    btn.disabled = true;
    
    setTimeout(() => {
        showToast('系統健康狀況良好', 'success');
        btn.innerHTML = originalHtml;
        btn.disabled = false;
    }, 2000);
}

function refreshDashboard() {
    location.reload();
}

function showToast(message, type) {
    // 簡單的提示實現
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const toast = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999;" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('body').append(toast);
    setTimeout(() => toast.remove(), 3000);
}
</script>
{% endblock %}