<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能預測 - 增強彩票預測系統</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .prediction-card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .prediction-card:hover {
            transform: translateY(-2px);
        }
        .success-rate-display {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        .method-badge {
            background: #007bff;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
        }
        .number-ball {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 2px;
            font-weight: bold;
            color: white;
        }
        .main-number {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }
        .special-number {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        .method-comparison {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
        }
    </style>
</head>
<body>
    <!-- 載入遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="text-center text-white">
            <div class="spinner-border mb-3" role="status">
                <span class="visually-hidden">預測中...</span>
            </div>
            <h5>正在進行智能預測...</h5>
            <p>分析歷史數據，計算成功率，選擇最佳方法</p>
        </div>
    </div>

    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                增強彩票預測系統
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首頁</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/predict">智能預測</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analysis">成功率分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/performance">性能監控</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/history">歷史記錄</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <!-- 頁面標題 -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="fw-bold">
                    <i class="fas fa-magic me-2 text-primary"></i>
                    智能預測系統
                </h2>
                <p class="text-muted">基於歷史成功率的精準預測，告別盲目猜測</p>
            </div>
        </div>

        <!-- 預測設置 -->
        <div class="row mb-4">
            <div class="col-md-8 mx-auto">
                <div class="card prediction-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>
                            預測設置
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="predictionForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="lotteryType" class="form-label">彩票類型</label>
                                    <select class="form-select" id="lotteryType" required>
                                        <option value="powercolor">威力彩</option>
                                        <option value="lotto649">大樂透</option>
                                        <option value="dailycash">今彩539</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="period" class="form-label">期號（可選）</label>
                                    <input type="text" class="form-control" id="period" placeholder="例：2024001">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">預測方法（留空自動選擇最佳方法）</label>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="ml" id="methodML">
                                            <label class="form-check-label" for="methodML">
                                                機器學習
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="board_path" id="methodBoardPath">
                                            <label class="form-check-label" for="methodBoardPath">
                                                板路分析
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="integrated" id="methodIntegrated">
                                            <label class="form-check-label" for="methodIntegrated">
                                                集成預測
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="ensemble" id="methodEnsemble">
                                            <label class="form-check-label" for="methodEnsemble">
                                                組合預測
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <small class="text-muted">不選擇任何方法時，系統將自動選擇當前成功率最高的方法</small>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-brain me-2"></i>
                                    開始智能預測
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 方法成功率比較 -->
        <div class="row mb-4" id="methodComparison" style="display: none;">
            <div class="col-12">
                <div class="card prediction-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            各方法成功率比較
                        </h5>
                    </div>
                    <div class="card-body" id="methodComparisonContent">
                        <!-- 動態載入內容 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 預測結果 -->
        <div class="row" id="predictionResult" style="display: none;">
            <div class="col-12">
                <div class="card prediction-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-trophy me-2"></i>
                            預測結果
                        </h5>
                    </div>
                    <div class="card-body" id="predictionContent">
                        <!-- 動態載入預測結果 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 錯誤訊息 -->
        <div class="row" id="errorMessage" style="display: none;">
            <div class="col-12">
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span id="errorText"></span>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全域變數存儲當前預測結果
        let currentPredictionResult = null;
        
        // 表單提交處理
        document.getElementById('predictionForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const lotteryType = document.getElementById('lotteryType').value;
            const period = document.getElementById('period').value;
            
            // 獲取選中的方法
            const methods = [];
            document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
                methods.push(checkbox.value);
            });
            
            // 顯示載入遮罩
            showLoading();
            
            try {
                // 首先載入方法比較
                await loadMethodComparison(lotteryType);
                
                // 執行預測
                const response = await fetch('/api/predict', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        lottery_type: lotteryType,
                        methods: methods.length > 0 ? methods : null,
                        period: period || null
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    currentPredictionResult = data.result; // 儲存預測結果
                    displayPredictionResult(data);
                } else {
                    showError(data.error || '預測失敗');
                }
            } catch (error) {
                console.error('預測錯誤:', error);
                showError('預測過程中發生錯誤，請稍後再試');
            } finally {
                hideLoading();
            }
        });
        
        // 載入方法比較
        async function loadMethodComparison(lotteryType) {
            try {
                const response = await fetch(`/api/success_rates/${lotteryType}?days=30`);
                const data = await response.json();
                
                if (data.success) {
                    displayMethodComparison(data);
                }
            } catch (error) {
                console.error('載入方法比較錯誤:', error);
            }
        }
        
        // 顯示方法比較
        function displayMethodComparison(data) {
            const container = document.getElementById('methodComparisonContent');
            const successRates = data.success_rates || {};
            const bestMethod = data.best_method || 'N/A';
            
            let html = `
                <div class="row mb-3">
                    <div class="col-12">
                        <h6 class="text-primary">
                            <i class="fas fa-crown me-2"></i>
                            推薦方法：${bestMethod} 
                            <span class="badge bg-success ms-2">
                                成功率 ${((successRates[bestMethod] || 0) * 100).toFixed(1)}%
                            </span>
                        </h6>
                    </div>
                </div>
                <div class="row">
            `;
            
            Object.entries(successRates).forEach(([method, rate]) => {
                const percentage = (rate * 100).toFixed(1);
                const isBest = method === bestMethod;
                const badgeClass = isBest ? 'bg-success' : 'bg-secondary';
                const iconClass = isBest ? 'fas fa-star' : 'fas fa-chart-line';
                
                html += `
                    <div class="col-md-3 mb-2">
                        <div class="method-comparison p-3 ${isBest ? 'border border-success' : ''}">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>
                                    <i class="${iconClass} me-2"></i>
                                    ${method}
                                </span>
                                <span class="badge ${badgeClass}">${percentage}%</span>
                            </div>
                            <div class="progress mt-2" style="height: 5px;">
                                <div class="progress-bar ${isBest ? 'bg-success' : 'bg-secondary'}" 
                                     style="width: ${percentage}%"></div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            
            container.innerHTML = html;
            document.getElementById('methodComparison').style.display = 'block';
        }
        
        // 顯示預測結果
        function displayPredictionResult(data) {
            const container = document.getElementById('predictionContent');
            const result = data.result;
            const lotteryType = result.lottery_type;
            
            let html = `
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="success-rate-display">
                            <h3 class="mb-2">${(result.success_rate * 100).toFixed(1)}%</h3>
                            <p class="mb-0">歷史成功率</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="p-3">
                            <h6><i class="fas fa-robot me-2"></i>預測方法</h6>
                            <span class="method-badge">${result.prediction_method}</span>
                            
                            <h6 class="mt-3"><i class="fas fa-clock me-2"></i>預測時間</h6>
                            <p class="text-muted mb-0">${result.prediction_time}</p>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="mb-3">
                            <i class="fas fa-dice me-2"></i>
                            預測號碼
                        </h5>
            `;
            
            // 根據彩票類型顯示號碼
            if (lotteryType === 'powercolor') {
                const firstArea = result['第一區'] || [];
                const secondArea = result['第二區'] || 0;
                
                html += `
                    <div class="mb-3">
                        <h6>第一區號碼：</h6>
                        <div class="d-flex flex-wrap">
                `;
                
                firstArea.forEach(num => {
                    html += `<div class="number-ball main-number">${num}</div>`;
                });
                
                html += `
                        </div>
                    </div>
                    <div class="mb-3">
                        <h6>第二區號碼：</h6>
                        <div class="number-ball special-number">${secondArea}</div>
                    </div>
                `;
            } else if (lotteryType === 'lotto649') {
                const mainNumbers = result['一般號碼'] || [];
                const specialNumber = result['特別號'] || 0;
                
                html += `
                    <div class="mb-3">
                        <h6>一般號碼：</h6>
                        <div class="d-flex flex-wrap">
                `;
                
                mainNumbers.forEach(num => {
                    html += `<div class="number-ball main-number">${num}</div>`;
                });
                
                html += `
                        </div>
                    </div>
                    <div class="mb-3">
                        <h6>特別號：</h6>
                        <div class="number-ball special-number">${specialNumber}</div>
                    </div>
                `;
            } else if (lotteryType === 'dailycash') {
                const numbers = result['號碼'] || [];
                
                html += `
                    <div class="mb-3">
                        <h6>號碼：</h6>
                        <div class="d-flex flex-wrap">
                `;
                
                numbers.forEach(num => {
                    html += `<div class="number-ball main-number">${num}</div>`;
                });
                
                html += '</div></div>';
            }
            
            html += '</div></div>';
            
            // 添加保存預測按鈕
            html += `
                <div class="row mb-3">
                    <div class="col-12 text-center">
                        <button type="button" class="btn btn-success btn-lg" onclick="savePrediction()">
                            <i class="fas fa-save me-2"></i>
                            保存此預測
                        </button>
                        <small class="d-block text-muted mt-2">保存預測結果到預測記錄表</small>
                    </div>
                </div>
            `;
            
            // 顯示說明
            const explanation = result.explanation || [];
            if (explanation.length > 0) {
                html += `
                    <div class="row">
                        <div class="col-12">
                            <h6><i class="fas fa-lightbulb me-2"></i>預測說明</h6>
                            <ul class="list-unstyled">
                `;
                
                explanation.forEach(exp => {
                    html += `<li><i class="fas fa-check-circle text-success me-2"></i>${exp}</li>`;
                });
                
                html += '</ul></div></div>';
            }
            
            container.innerHTML = html;
            document.getElementById('predictionResult').style.display = 'block';
            
            // 隱藏錯誤訊息
            document.getElementById('errorMessage').style.display = 'none';
        }
        
        // 顯示錯誤
        function showError(message) {
            document.getElementById('errorText').textContent = message;
            document.getElementById('errorMessage').style.display = 'block';
            document.getElementById('predictionResult').style.display = 'none';
        }
        
        // 顯示載入遮罩
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }
        
        // 隱藏載入遮罩
        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }
        
        // 保存預測結果
        async function savePrediction() {
            if (!currentPredictionResult) {
                alert('❌ 沒有可保存的預測結果');
                return;
            }
            
            try {
                showLoading();
                
                // 準備保存數據 - 符合API期待格式
                let numbers = [];
                
                // 根據彩票類型提取和組合號碼
                if (currentPredictionResult.lottery_type === 'powercolor') {
                    const mainNumbers = currentPredictionResult['第一區'] || [];
                    const specialNumber = currentPredictionResult['第二區'] || 0;
                    // API 期待 7 個號碼：6個主號碼 + 1個特別號
                    numbers = [...mainNumbers, specialNumber];
                } else if (currentPredictionResult.lottery_type === 'lotto649') {
                    const mainNumbers = currentPredictionResult['一般號碼'] || [];
                    const specialNumber = currentPredictionResult['特別號'] || 0;
                    numbers = [...mainNumbers, specialNumber];
                } else if (currentPredictionResult.lottery_type === 'dailycash') {
                    numbers = currentPredictionResult['號碼'] || [];
                }
                
                const saveData = {
                    numbers: numbers,
                    strategy_type: currentPredictionResult.prediction_method || 'AI智能預測',
                    confidence: currentPredictionResult.success_rate || 0.5,
                    target_period: new Date().getFullYear() + '001' // 生成期號
                };
                
                const response = await fetch(`/api/save_prediction/${currentPredictionResult.lottery_type}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(saveData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('✅ 預測結果保存成功！\n已保存到預測記錄表中。');
                } else {
                    alert('❌ 保存失敗: ' + (result.error || '未知錯誤'));
                }
                
            } catch (error) {
                console.error('保存預測錯誤:', error);
                alert('❌ 保存過程中發生錯誤: ' + error.message);
            } finally {
                hideLoading();
            }
        }
        
        // 頁面載入時檢查URL參數
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const type = urlParams.get('type');
            
            if (type) {
                document.getElementById('lotteryType').value = type;
                // 自動載入該類型的方法比較
                loadMethodComparison(type);
            }
        });
    </script>
</body>
</html>