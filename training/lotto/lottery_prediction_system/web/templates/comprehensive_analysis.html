<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>綜合分析 - 彩票預測系統</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .analysis-card {
            border-left: 4px solid #007bff;
            margin-bottom: 20px;
        }
        .success-card {
            border-left: 4px solid #28a745;
        }
        .pattern-card {
            border-left: 4px solid #ffc107;
        }
        .enhanced-card {
            border-left: 4px solid #dc3545;
        }
        .loading-spinner {
            display: none;
        }
        .number-badge {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            margin: 2px;
            display: inline-block;
            font-weight: bold;
        }
        .math-formula {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            margin: 5px 0;
        }
        .confidence-bar {
            height: 20px;
            background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
            border-radius: 10px;
            position: relative;
        }
        .confidence-indicator {
            position: absolute;
            top: 0;
            height: 100%;
            width: 4px;
            background: #000;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    {% include 'navbar.html' %}

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1><i class="fas fa-microscope"></i> 綜合分析系統</h1>
                <p class="text-muted">深度分析預測成功原因、號碼出現模式、數學關係等</p>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-cogs"></i> 分析控制</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <label for="lotteryType" class="form-label">彩票類型</label>
                                <select class="form-select" id="lotteryType">
                                    <option value="powercolor">威力彩</option>
                                    <option value="lotto649">大樂透</option>
                                    <option value="dailycash">今彩539</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="analysisDepth" class="form-label">分析深度</label>
                                <select class="form-select" id="analysisDepth">
                                    <option value="basic">基礎分析</option>
                                    <option value="advanced" selected>進階分析</option>
                                    <option value="comprehensive">全面分析</option>
                                </select>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button class="btn btn-primary w-100" onclick="startComprehensiveAnalysis()">
                                    <i class="fas fa-play"></i> 開始分析
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 載入指示器 -->
        <div class="row loading-spinner" id="loadingSpinner">
            <div class="col-12 text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">分析中...</span>
                </div>
                <p class="mt-2">正在進行綜合分析，請稍候...</p>
            </div>
        </div>

        <!-- 分析結果 -->
        <div id="analysisResults" style="display: none;">
            <!-- 摘要卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary">總預測數</h5>
                            <h2 id="totalPredictions">-</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">成功預測</h5>
                            <h2 id="successfulPredictions">-</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning">成功率</h5>
                            <h2 id="successRate">-</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-info">最新期數</h5>
                            <h2 id="latestPeriod">-</h2>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 成功分析 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card analysis-card success-card">
                        <div class="card-header">
                            <h5><i class="fas fa-trophy"></i> 成功預測分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>機器學習成功因素</h6>
                                    <ul id="mlSuccessFactors" class="list-unstyled">
                                        <!-- 動態填充 -->
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>板路分析成功因素</h6>
                                    <ul id="bpSuccessFactors" class="list-unstyled">
                                        <!-- 動態填充 -->
                                    </ul>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>共同成功模式</h6>
                                    <div id="commonPatterns">
                                        <!-- 動態填充 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 號碼模式分析 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card analysis-card pattern-card">
                        <div class="card-header">
                            <h5><i class="fas fa-puzzle-piece"></i> 號碼模式分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>最新期號碼</h6>
                                    <div id="latestNumbers">
                                        <!-- 動態填充 -->
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>出現原因推論</h6>
                                    <div id="appearanceReasons">
                                        <!-- 動態填充 -->
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <h6>數學關係</h6>
                                    <div id="mathRelationships">
                                        <!-- 動態填充 -->
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>歷史關聯</h6>
                                    <div id="historicalContext">
                                        <!-- 動態填充 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 增強數學分析 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card analysis-card enhanced-card">
                        <div class="card-header">
                            <h5><i class="fas fa-calculator"></i> 增強數學分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>算術關係</h6>
                                    <div id="arithmeticRelations">
                                        <!-- 動態填充 -->
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>數列關係</h6>
                                    <div id="sequenceRelations">
                                        <!-- 動態填充 -->
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <h6>模運算關係</h6>
                                    <div id="modularRelations">
                                        <!-- 動態填充 -->
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>統計關係</h6>
                                    <div id="statisticalRelations">
                                        <!-- 動態填充 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 預測建議 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-lightbulb"></i> 智能預測建議</h5>
                        </div>
                        <div class="card-body">
                            <div id="predictionSuggestions">
                                <!-- 動態填充 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        async function startComprehensiveAnalysis() {
            const lotteryType = document.getElementById('lotteryType').value;
            const analysisDepth = document.getElementById('analysisDepth').value;
            
            // 顯示載入指示器
            document.getElementById('loadingSpinner').style.display = 'block';
            document.getElementById('analysisResults').style.display = 'none';
            
            try {
                // 執行綜合分析
                const response = await fetch(`/api/comprehensive_analysis/${lotteryType}`);
                const data = await response.json();
                
                if (data.success) {
                    displayAnalysisResults(data.analysis);
                } else {
                    alert('分析失敗: ' + data.error);
                }
            } catch (error) {
                console.error('分析錯誤:', error);
                alert('分析過程中發生錯誤');
            } finally {
                // 隱藏載入指示器
                document.getElementById('loadingSpinner').style.display = 'none';
            }
        }
        
        function displayAnalysisResults(analysis) {
            // 顯示摘要數據
            document.getElementById('totalPredictions').textContent = analysis.summary.total_predictions;
            document.getElementById('successfulPredictions').textContent = analysis.summary.successful_predictions;
            document.getElementById('successRate').textContent = (analysis.summary.success_rate * 100).toFixed(1) + '%';
            document.getElementById('latestPeriod').textContent = analysis.summary.latest_period;
            
            // 顯示成功分析
            displaySuccessAnalysis(analysis.success_analysis);
            
            // 顯示模式分析
            displayPatternAnalysis(analysis.pattern_analysis);
            
            // 顯示增強分析
            displayEnhancedAnalysis(analysis.enhanced_analysis);
            
            // 生成預測建議
            generatePredictionSuggestions(analysis);
            
            // 顯示結果區域
            document.getElementById('analysisResults').style.display = 'block';
        }
        
        function displaySuccessAnalysis(successAnalysis) {
            if (!successAnalysis) return;
            
            // ML成功因素
            const mlFactors = document.getElementById('mlSuccessFactors');
            mlFactors.innerHTML = '';
            (successAnalysis.ml_success_factors || []).forEach(factor => {
                const li = document.createElement('li');
                li.innerHTML = `<i class="fas fa-check-circle text-success"></i> ${factor}`;
                mlFactors.appendChild(li);
            });
            
            // 板路成功因素
            const bpFactors = document.getElementById('bpSuccessFactors');
            bpFactors.innerHTML = '';
            (successAnalysis.board_path_success_factors || []).forEach(factor => {
                const li = document.createElement('li');
                li.innerHTML = `<i class="fas fa-check-circle text-success"></i> ${factor}`;
                bpFactors.appendChild(li);
            });
            
            // 共同模式
            const commonPatterns = document.getElementById('commonPatterns');
            commonPatterns.innerHTML = '';
            (successAnalysis.common_success_patterns || []).forEach(pattern => {
                const badge = document.createElement('span');
                badge.className = 'badge bg-primary me-2 mb-2';
                badge.textContent = pattern;
                commonPatterns.appendChild(badge);
            });
        }
        
        function displayPatternAnalysis(patternAnalysis) {
            if (!patternAnalysis) return;
            
            // 最新號碼
            const latestNumbers = document.getElementById('latestNumbers');
            latestNumbers.innerHTML = '';
            if (patternAnalysis.numbers) {
                patternAnalysis.numbers.forEach(num => {
                    const badge = document.createElement('span');
                    badge.className = 'number-badge';
                    badge.textContent = num;
                    latestNumbers.appendChild(badge);
                });
            }
            
            // 出現原因
            const reasons = document.getElementById('appearanceReasons');
            reasons.innerHTML = '';
            (patternAnalysis.appearance_reasons || []).forEach(reason => {
                const p = document.createElement('p');
                p.innerHTML = `<small>${reason}</small>`;
                reasons.appendChild(p);
            });
        }
        
        function displayEnhancedAnalysis(enhancedAnalysis) {
            if (!enhancedAnalysis) return;
            
            // 算術關係
            const arithmetic = document.getElementById('arithmeticRelations');
            arithmetic.innerHTML = '';
            (enhancedAnalysis.arithmetic_relationships || []).slice(0, 5).forEach(rel => {
                const div = document.createElement('div');
                div.className = 'math-formula';
                div.textContent = rel.formula || `${rel.type}: ${rel.numbers?.join(', ')}`;
                arithmetic.appendChild(div);
            });
            
            // 數列關係
            const sequence = document.getElementById('sequenceRelations');
            sequence.innerHTML = '';
            (enhancedAnalysis.sequence_relationships || []).slice(0, 5).forEach(rel => {
                const div = document.createElement('div');
                div.className = 'math-formula';
                div.textContent = rel.formula || `${rel.type}: ${rel.numbers?.join(', ')}`;
                sequence.appendChild(div);
            });
        }
        
        function generatePredictionSuggestions(analysis) {
            const suggestions = document.getElementById('predictionSuggestions');
            suggestions.innerHTML = '';
            
            const suggestionList = [
                '基於成功分析，建議關注歷史表現良好的特徵組合',
                '數學關係分析顯示某些模式具有較高的重現性',
                '結合機器學習和板路分析的預測具有更高的準確性',
                '建議在預測時考慮號碼的統計分布特性'
            ];
            
            suggestionList.forEach(suggestion => {
                const p = document.createElement('p');
                p.innerHTML = `<i class="fas fa-arrow-right text-primary"></i> ${suggestion}`;
                suggestions.appendChild(p);
            });
        }
        
        // 頁面載入時執行初始分析
        document.addEventListener('DOMContentLoaded', function() {
            // 可以在這裡添加自動載入邏輯
        });
    </script>
</body>
</html>
