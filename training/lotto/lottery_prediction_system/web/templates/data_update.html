{% extends "base.html" %}

{% block title %}數據更新管理 - 彩票預測系統{% endblock %}

{% block extra_css %}
<style>
.update-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.status-success { background-color: #28a745; }
.status-error { background-color: #dc3545; }
.status-warning { background-color: #ffc107; }
.status-info { background-color: #17a2b8; }

.lottery-section {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.update-button {
    background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.update-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.2);
}

.manual-form {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-top: 15px;
}

.scheduler-controls {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
}

.log-viewer {
    background: #2d3748;
    color: #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    font-family: 'Monaco', 'Courier New', monospace;
    max-height: 400px;
    overflow-y: auto;
    font-size: 12px;
    line-height: 1.4;
}

.progress-bar {
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.3s ease;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2><i class="fas fa-sync-alt"></i> 數據更新管理</h2>
        </div>
    </div>

    <!-- 系統狀態卡片 -->
    <div class="row">
        <div class="col-12">
            <div class="update-card">
                <div class="row">
                    <div class="col-md-8">
                        <h4><i class="fas fa-chart-bar"></i> 系統更新狀態</h4>
                        <p id="last-update-time">最後更新時間: 檢查中...</p>
                        <div class="progress-bar">
                            <div class="progress-fill" id="update-progress" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="col-md-4 text-right">
                        <button class="btn update-button" onclick="checkSystemStatus()">
                            <i class="fas fa-check-circle"></i> 檢查狀態
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 調度器控制 -->
    <div class="row">
        <div class="col-12">
            <div class="scheduler-controls">
                <h4><i class="fas fa-clock"></i> 自動更新調度器</h4>
                <div class="row">
                    <div class="col-md-6">
                        <p>調度器狀態: <span id="scheduler-status">檢查中...</span></p>
                        <p>下次更新: <span id="next-update-time">--</span></p>
                    </div>
                    <div class="col-md-6 text-right">
                        <button class="btn btn-success mr-2" onclick="startScheduler()">
                            <i class="fas fa-play"></i> 啟動調度器
                        </button>
                        <button class="btn btn-warning" onclick="stopScheduler()">
                            <i class="fas fa-stop"></i> 停止調度器
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 彩票類型更新區域 -->
    <div class="row">
        <!-- 威力彩 -->
        <div class="col-md-4">
            <div class="lottery-section">
                <h5>
                    <span class="status-indicator status-info" id="powercolor-status"></span>
                    威力彩 (PowerColor)
                </h5>
                <p>最新期號: <span id="powercolor-latest">--</span></p>
                <button class="btn update-button btn-block" onclick="updateLottery('powercolor')">
                    <i class="fas fa-download"></i> 更新威力彩
                </button>
                
                <!-- 手動輸入表單 -->
                <div class="manual-form" style="display: none;" id="powercolor-manual">
                    <h6>手動輸入開獎結果</h6>
                    <form onsubmit="manualUpdatePowercolor(event)">
                        <div class="form-group">
                            <label>期號</label>
                            <input type="number" class="form-control" id="pc-period" placeholder="114000067">
                        </div>
                        <div class="form-group">
                            <label>開獎日期</label>
                            <input type="date" class="form-control" id="pc-date">
                        </div>
                        <div class="form-group">
                            <label>第一區號碼 (6個號碼，用逗號分隔)</label>
                            <input type="text" class="form-control" id="pc-numbers" placeholder="02,04,11,12,16,19">
                        </div>
                        <div class="form-group">
                            <label>第二區號碼 (特別號)</label>
                            <input type="number" class="form-control" id="pc-special" placeholder="2" min="1" max="8">
                        </div>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> 添加
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="toggleManualForm('powercolor')">
                            取消
                        </button>
                    </form>
                </div>
                
                <button class="btn btn-outline-secondary btn-sm mt-2" onclick="toggleManualForm('powercolor')">
                    <i class="fas fa-edit"></i> 手動輸入
                </button>
            </div>
        </div>

        <!-- 大樂透 -->
        <div class="col-md-4">
            <div class="lottery-section">
                <h5>
                    <span class="status-indicator status-info" id="lotto649-status"></span>
                    大樂透 (Lotto649)
                </h5>
                <p>最新期號: <span id="lotto649-latest">--</span></p>
                <button class="btn update-button btn-block" onclick="updateLottery('lotto649')">
                    <i class="fas fa-download"></i> 更新大樂透
                </button>
            </div>
        </div>

        <!-- 今彩539 -->
        <div class="col-md-4">
            <div class="lottery-section">
                <h5>
                    <span class="status-indicator status-info" id="dailycash-status"></span>
                    今彩539 (DailyCash)
                </h5>
                <p>最新期號: <span id="dailycash-latest">--</span></p>
                <button class="btn update-button btn-block" onclick="updateLottery('dailycash')">
                    <i class="fas fa-download"></i> 更新今彩539
                </button>
            </div>
        </div>
    </div>

    <!-- 全體更新 -->
    <div class="row">
        <div class="col-12">
            <div class="lottery-section text-center">
                <h5>批量操作</h5>
                <button class="btn btn-primary btn-lg mr-3" onclick="updateAllLotteries()">
                    <i class="fas fa-sync"></i> 更新所有彩票
                </button>
                <button class="btn btn-info btn-lg" onclick="checkAllStatus()">
                    <i class="fas fa-search"></i> 檢查所有狀態
                </button>
            </div>
        </div>
    </div>

    <!-- 日誌查看器 -->
    <div class="row">
        <div class="col-12">
            <div class="lottery-section">
                <h5>
                    <i class="fas fa-file-alt"></i> 更新日誌
                    <button class="btn btn-sm btn-outline-primary float-right" onclick="refreshLogs()">
                        <i class="fas fa-refresh"></i> 刷新
                    </button>
                </h5>
                <div class="log-viewer" id="log-viewer">
                    載入日誌中...
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 狀態提示模態框 -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">更新狀態</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="statusModalBody">
                處理中...
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">關閉</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 初始化頁面
$(document).ready(function() {
    checkSystemStatus();
    checkSchedulerStatus();
    refreshLogs();
    
    // 每5分鐘自動刷新狀態
    setInterval(function() {
        checkSystemStatus();
        checkSchedulerStatus();
    }, 300000);
});

// 檢查系統狀態
function checkSystemStatus() {
    $.get('/api/data-update/status')
        .done(function(response) {
            if (response.success) {
                updateSystemStatus(response.data);
            }
        })
        .fail(function() {
            $('#last-update-time').text('最後更新時間: 無法獲取');
        });
}

// 更新系統狀態顯示
function updateSystemStatus(data) {
    $('#last-update-time').text(`最後更新時間: ${data.last_update || '未知'}`);
    
    // 更新各彩票狀態
    ['powercolor', 'lotto649', 'dailycash'].forEach(lottery => {
        const status = data.lottery_status[lottery];
        const indicator = $(`#${lottery}-status`);
        const latest = $(`#${lottery}-latest`);
        
        if (status) {
            indicator.removeClass().addClass('status-indicator status-success');
            latest.text(status.latest_period || '--');
        } else {
            indicator.removeClass().addClass('status-indicator status-error');
            latest.text('錯誤');
        }
    });
    
    // 更新進度條
    const progress = Math.min(100, data.overall_progress || 0);
    $('#update-progress').css('width', `${progress}%`);
}

// 檢查調度器狀態
function checkSchedulerStatus() {
    $.get('/api/scheduler/status')
        .done(function(response) {
            if (response.success) {
                const status = response.data.is_running ? '運行中' : '已停止';
                const statusClass = response.data.is_running ? 'text-success' : 'text-warning';
                $('#scheduler-status').html(`<span class="${statusClass}">${status}</span>`);
                
                if (response.data.next_runs && response.data.next_runs.length > 0) {
                    $('#next-update-time').text(response.data.next_runs[0].time);
                } else {
                    $('#next-update-time').text('--');
                }
            }
        });
}

// 更新指定彩票
function updateLottery(lotteryType) {
    showProgress(`正在更新${getLotteryName(lotteryType)}...`);
    
    $.post('/api/data-update/update', {
        lottery_type: lotteryType
    })
    .done(function(response) {
        hideProgress();
        if (response.success) {
            showStatusModal(`✅ ${getLotteryName(lotteryType)} 更新成功`, 'success');
            checkSystemStatus();
        } else {
            showStatusModal(`❌ ${getLotteryName(lotteryType)} 更新失敗: ${response.error}`, 'error');
        }
    })
    .fail(function() {
        hideProgress();
        showStatusModal(`❌ ${getLotteryName(lotteryType)} 更新請求失敗`, 'error');
    });
}

// 更新所有彩票
function updateAllLotteries() {
    showProgress('正在更新所有彩票數據...');
    
    $.post('/api/data-update/update-all')
        .done(function(response) {
            hideProgress();
            if (response.success) {
                const results = response.data.results;
                let message = '更新結果:\n';
                Object.entries(results).forEach(([lottery, success]) => {
                    const status = success ? '✅' : '❌';
                    message += `${status} ${getLotteryName(lottery)}\n`;
                });
                showStatusModal(message, 'info');
                checkSystemStatus();
            } else {
                showStatusModal(`❌ 批量更新失敗: ${response.error}`, 'error');
            }
        })
        .fail(function() {
            hideProgress();
            showStatusModal('❌ 批量更新請求失敗', 'error');
        });
}

// 手動更新威力彩
function manualUpdatePowercolor(event) {
    event.preventDefault();
    
    const period = $('#pc-period').val();
    const date = $('#pc-date').val();
    const numbers = $('#pc-numbers').val().split(',').map(n => parseInt(n.trim()));
    const special = parseInt($('#pc-special').val());
    
    if (!period || !date || numbers.length !== 6 || !special) {
        alert('請填寫完整的開獎資料');
        return;
    }
    
    $.post('/api/data-update/manual-powercolor', {
        period: period,
        date: date,
        numbers: numbers,
        special: special
    })
    .done(function(response) {
        if (response.success) {
            showStatusModal('✅ 手動添加成功', 'success');
            toggleManualForm('powercolor');
            checkSystemStatus();
        } else {
            showStatusModal(`❌ 手動添加失敗: ${response.error}`, 'error');
        }
    })
    .fail(function() {
        showStatusModal('❌ 手動添加請求失敗', 'error');
    });
}

// 調度器控制
function startScheduler() {
    $.post('/api/scheduler/start')
        .done(function(response) {
            if (response.success) {
                showStatusModal('✅ 調度器已啟動', 'success');
                checkSchedulerStatus();
            } else {
                showStatusModal(`❌ 調度器啟動失敗: ${response.error}`, 'error');
            }
        });
}

function stopScheduler() {
    $.post('/api/scheduler/stop')
        .done(function(response) {
            if (response.success) {
                showStatusModal('✅ 調度器已停止', 'success');
                checkSchedulerStatus();
            } else {
                showStatusModal(`❌ 調度器停止失敗: ${response.error}`, 'error');
            }
        });
}

// 切換手動輸入表單
function toggleManualForm(lotteryType) {
    $(`#${lotteryType}-manual`).toggle();
}

// 刷新日誌
function refreshLogs() {
    $.get('/api/data-update/logs')
        .done(function(response) {
            if (response.success) {
                $('#log-viewer').html(response.data.logs.join('<br>'));
                $('#log-viewer').scrollTop($('#log-viewer')[0].scrollHeight);
            }
        })
        .fail(function() {
            $('#log-viewer').html('無法載入日誌');
        });
}

// 工具函數
function getLotteryName(type) {
    const names = {
        'powercolor': '威力彩',
        'lotto649': '大樂透', 
        'dailycash': '今彩539'
    };
    return names[type] || type;
}

function showProgress(message) {
    // 簡單的進度提示
    $('body').append(`<div id="progress-overlay" style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:9999;display:flex;justify-content:center;align-items:center;color:white;font-size:18px;"><div><i class="fas fa-spinner fa-spin mr-2"></i>${message}</div></div>`);
}

function hideProgress() {
    $('#progress-overlay').remove();
}

function showStatusModal(message, type) {
    $('#statusModalBody').html(`<div class="alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'}">${message.replace(/\n/g, '<br>')}</div>`);
    $('#statusModal').modal('show');
}
</script>
{% endblock %}