{% extends "base.html" %}

{% block title %}回測驗證系統{% endblock %}

{% block extra_css %}
<style>
.backtest-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.method-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.method-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.method-card .card-header {
    background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 1rem 1.5rem;
    font-weight: 600;
}

.accuracy-meter {
    height: 8px;
    background: #e0e6ed;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.accuracy-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.8s ease;
}

.accuracy-high { background: linear-gradient(90deg, #00b09b, #96c93d); }
.accuracy-medium { background: linear-gradient(90deg, #f2994a, #f2c94c); }
.accuracy-low { background: linear-gradient(90deg, #ee5a24, #ffc048); }

.loading-spinner {
    display: none;
    text-align: center;
    padding: 2rem;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.comparison-chart {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    margin-top: 1rem;
}

.method-result {
    display: none;
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1rem;
    border-left: 5px solid #667eea;
}

.result-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #667eea;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

.validation-form {
    background: #f8f9ff;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 2px solid #e3e8ff;
}

.btn-backtest {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-backtest:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-backtest:disabled {
    opacity: 0.6;
    transform: none;
    box-shadow: none;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 回測系統標題 -->
    <div class="backtest-header text-center">
        <h1><i class="fas fa-chart-line"></i> 回測驗證系統</h1>
        <p class="mb-0">預測算法準確度評估 | 兩種回測方法 | 系統性能分析</p>
    </div>

    <div class="row">
        <!-- 左側：控制面板 -->
        <div class="col-md-4">
            <!-- 系統狀態 -->
            <div class="method-card">
                <div class="card-header">
                    <i class="fas fa-heartbeat"></i> 系統狀態
                </div>
                <div class="card-body">
                    <div id="systemStatus">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status"></div>
                            <p class="mt-2">檢查系統狀態中...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 回測配置 -->
            <div class="method-card">
                <div class="card-header">
                    <i class="fas fa-cog"></i> 回測配置
                </div>
                <div class="card-body">
                    <form id="backtestForm">
                        <!-- 彩票類型選擇 -->
                        <div class="mb-3">
                            <label for="lotteryType" class="form-label">
                                <i class="fas fa-ticket-alt"></i> 彩票類型
                            </label>
                            <select class="form-select" id="lotteryType" name="lottery_type" required>
                                <option value="">請選擇彩票類型</option>
                                <option value="powercolor">威力彩</option>
                                <option value="lotto649">大樂透</option>
                                <option value="dailycash">今彩539</option>
                            </select>
                        </div>

                        <!-- 回測方法選擇 -->
                        <div class="validation-form">
                            <label class="form-label mb-3">
                                <i class="fas fa-flask"></i> 回測方法
                            </label>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="backtestMethod" value="method1" id="method1" checked>
                                <label class="form-check-label" for="method1">
                                    <strong>方法一：逐期回測</strong><br>
                                    <small class="text-muted">使用 n-1 期數據預測第 n 期</small>
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="backtestMethod" value="method2" id="method2">
                                <label class="form-check-label" for="method2">
                                    <strong>方法二：歷史校正</strong><br>
                                    <small class="text-muted">基於早期數據校正算法</small>
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="backtestMethod" value="comprehensive" id="comprehensive">
                                <label class="form-check-label" for="comprehensive">
                                    <strong>綜合回測</strong><br>
                                    <small class="text-muted">同時執行兩種方法並比較</small>
                                </label>
                            </div>
                        </div>

                        <!-- 方法一參數 -->
                        <div id="method1Params" class="method-params">
                            <div class="mb-3">
                                <label for="startPeriod" class="form-label">起始期號</label>
                                <input type="text" class="form-control" id="startPeriod" value="114000066">
                            </div>
                            <div class="mb-3">
                                <label for="numPeriods" class="form-label">回測期數</label>
                                <input type="number" class="form-control" id="numPeriods" value="10" min="5" max="50">
                            </div>
                        </div>

                        <!-- 方法二參數 -->
                        <div id="method2Params" class="method-params" style="display: none;">
                            <div class="mb-3">
                                <label for="calibrationStart" class="form-label">校正起始期號</label>
                                <input type="text" class="form-control" id="calibrationStart" value="114000035">
                            </div>
                            <div class="mb-3">
                                <label for="validationPeriods" class="form-label">驗證期數</label>
                                <input type="number" class="form-control" id="validationPeriods" value="20" min="10" max="100">
                            </div>
                        </div>

                        <!-- 執行按鈕 -->
                        <button type="submit" class="btn btn-backtest w-100" id="runBacktestBtn">
                            <i class="fas fa-play"></i> 執行回測
                        </button>
                    </form>
                </div>
            </div>

            <!-- 單期驗證 -->
            <div class="method-card">
                <div class="card-header">
                    <i class="fas fa-search"></i> 單期驗證
                </div>
                <div class="card-body">
                    <form id="singleValidationForm">
                        <div class="mb-3">
                            <label for="validationLotteryType" class="form-label">彩票類型</label>
                            <select class="form-select" id="validationLotteryType" name="lottery_type" required>
                                <option value="">請選擇彩票類型</option>
                                <option value="powercolor">威力彩</option>
                                <option value="lotto649">大樂透</option>
                                <option value="dailycash">今彩539</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="validationPeriod" class="form-label">期號</label>
                            <input type="text" class="form-control" id="validationPeriod" placeholder="例如：114000065">
                        </div>
                        <button type="submit" class="btn btn-outline-primary w-100" id="validateBtn">
                            <i class="fas fa-check"></i> 驗證期號
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- 右側：結果展示 -->
        <div class="col-md-8">
            <!-- 載入動畫 -->
            <div class="loading-spinner" id="loadingSpinner">
                <div class="spinner"></div>
                <p class="mt-2">回測執行中，請稍候...</p>
            </div>

            <!-- 結果容器 -->
            <div id="backtestResults" style="display: none;"></div>

            <!-- 單期驗證結果 -->
            <div id="validationResults" style="display: none;"></div>

            <!-- 空狀態 -->
            <div class="method-card text-center" id="emptyState">
                <div class="card-body py-5">
                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">準備開始回測</h4>
                    <p class="text-muted">選擇彩票類型和回測方法，點擊「執行回測」按鈕</p>
                    <div class="mt-4">
                        <div class="row text-center">
                            <div class="col-md-6">
                                <div class="border-end">
                                    <h6 class="text-primary">方法一：逐期回測</h6>
                                    <small class="text-muted">
                                        使用歷史數據逐期驗證<br>
                                        適合評估算法一致性
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">方法二：歷史校正</h6>
                                <small class="text-muted">
                                    基於早期數據校正算法<br>
                                    適合優化預測準確度
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 檢查系統狀態
    checkSystemStatus();
    
    // 回測方法選擇
    $('input[name="backtestMethod"]').on('change', function() {
        const method = this.value;
        $('.method-params').hide();
        if (method === 'method1') {
            $('#method1Params').show();
        } else if (method === 'method2') {
            $('#method2Params').show();
        }
    });

    // 回測表單提交
    $('#backtestForm').on('submit', function(e) {
        e.preventDefault();
        executeBacktest();
    });

    // 單期驗證表單提交
    $('#singleValidationForm').on('submit', function(e) {
        e.preventDefault();
        validateSinglePeriod();
    });
    
    function checkSystemStatus() {
        $.ajax({
            url: '/api/backtesting/status',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    displaySystemStatus(response.data);
                } else {
                    displaySystemError(response.error);
                }
            },
            error: function(xhr, status, error) {
                displaySystemError('系統狀態檢查失敗: ' + error);
            }
        });
    }
    
    function displaySystemStatus(status) {
        const statusHtml = `
            <div class="row text-center">
                <div class="col-6">
                    <div class="stat-item">
                        <div class="stat-value ${status.system_ready ? 'text-success' : 'text-danger'}">
                            <i class="fas fa-${status.system_ready ? 'check-circle' : 'times-circle'}"></i>
                        </div>
                        <div class="stat-label">系統狀態</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="stat-item">
                        <div class="stat-value ${status.database_available ? 'text-success' : 'text-danger'}">
                            <i class="fas fa-${status.database_available ? 'database' : 'exclamation-triangle'}"></i>
                        </div>
                        <div class="stat-label">數據庫</div>
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <small class="text-muted">
                    支持彩票: ${status.supported_lotteries ? status.supported_lotteries.join(', ') : 'N/A'}<br>
                    模組狀態: ${status.modules_loaded ? '已載入' : '載入失敗'}
                </small>
            </div>
        `;
        $('#systemStatus').html(statusHtml);
    }
    
    function displaySystemError(error) {
        $('#systemStatus').html(`
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> ${error}
            </div>
        `);
    }
    
    function executeBacktest() {
        const formData = {
            lottery_type: $('#lotteryType').val(),
            method: $('input[name="backtestMethod"]:checked').val()
        };
        
        if (!formData.lottery_type) {
            alert('請選擇彩票類型');
            return;
        }
        
        // 根據方法添加參數
        if (formData.method === 'method1') {
            formData.start_period = $('#startPeriod').val();
            formData.num_periods = parseInt($('#numPeriods').val());
        } else if (formData.method === 'method2') {
            formData.calibration_start = $('#calibrationStart').val();
            formData.validation_periods = parseInt($('#validationPeriods').val());
        }
        
        // 顯示載入動畫
        $('#loadingSpinner').show();
        $('#backtestResults').hide();
        $('#validationResults').hide();
        $('#emptyState').hide();
        $('#runBacktestBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 執行中...');
        
        // 構建API URL
        let apiUrl = `/api/backtesting/${formData.method}/${formData.lottery_type}`;
        
        // 發送請求
        $.ajax({
            url: apiUrl,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            timeout: 120000, // 2分鐘超時
            success: function(response) {
                if (response.success) {
                    displayBacktestResults(response.data);
                } else {
                    showError('回測失敗: ' + (response.error || '未知錯誤'));
                }
            },
            error: function(xhr, status, error) {
                let errorMsg = '回測執行失敗';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMsg = xhr.responseJSON.error;
                } else if (status === 'timeout') {
                    errorMsg = '回測執行超時，請稍後再試';
                }
                showError(errorMsg);
            },
            complete: function() {
                $('#loadingSpinner').hide();
                $('#runBacktestBtn').prop('disabled', false).html('<i class="fas fa-play"></i> 執行回測');
            }
        });
    }
    
    function displayBacktestResults(data) {
        let html = '';
        
        if (data.method === 'sequential_backtest' || data.method === 'historical_calibration') {
            // 單一方法結果
            html = displaySingleMethodResult(data);
        } else if (data.method_1_results && data.method_2_results) {
            // 綜合回測結果
            html = displayComprehensiveResults(data);
        }
        
        $('#backtestResults').html(html).show();
    }
    
    function displaySingleMethodResult(data) {
        const methodName = data.method === 'sequential_backtest' ? '逐期回測' : '歷史校正';
        const summary = data.summary;
        
        return `
            <div class="method-card">
                <div class="card-header">
                    <i class="fas fa-chart-area"></i> ${methodName}結果 - ${data.lottery_type.toUpperCase()}
                </div>
                <div class="card-body">
                    <div class="result-stats">
                        <div class="stat-item">
                            <div class="stat-value">${summary.total_periods_tested}</div>
                            <div class="stat-label">測試期數</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${summary.average_main_number_hits}</div>
                            <div class="stat-label">平均命中數</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${summary.special_number_hit_rate}%</div>
                            <div class="stat-label">特別號命中率</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${(summary.average_confidence * 100).toFixed(1)}%</div>
                            <div class="stat-label">平均信心度</div>
                        </div>
                    </div>
                    
                    <div class="accuracy-meter mt-3">
                        <div class="accuracy-fill ${getAccuracyClass(summary.average_main_number_hits)}" 
                             style="width: ${(summary.average_main_number_hits / 6 * 100)}%"></div>
                    </div>
                    <small class="text-muted">預測準確度: ${(summary.average_main_number_hits / 6 * 100).toFixed(1)}%</small>
                    
                    ${data.detailed_results ? displayDetailedResults(data.detailed_results) : ''}
                </div>
            </div>
        `;
    }
    
    function displayComprehensiveResults(data) {
        const method1 = data.method_1_results;
        const method2 = data.method_2_results;
        const comparison = data.comparison;
        
        return `
            <div class="method-card">
                <div class="card-header">
                    <i class="fas fa-balance-scale"></i> 綜合回測比較結果 - ${data.lottery_type.toUpperCase()}
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">方法一：逐期回測</h6>
                            <div class="result-stats">
                                <div class="stat-item">
                                    <div class="stat-value">${method1.success ? method1.summary.average_main_number_hits : 'N/A'}</div>
                                    <div class="stat-label">平均命中數</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">${method1.success ? method1.summary.special_number_hit_rate + '%' : 'N/A'}</div>
                                    <div class="stat-label">特別號命中率</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">方法二：歷史校正</h6>
                            <div class="result-stats">
                                <div class="stat-item">
                                    <div class="stat-value">${method2.success ? method2.summary.average_main_number_hits : 'N/A'}</div>
                                    <div class="stat-label">平均命中數</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">${method2.success ? method2.summary.special_number_hit_rate + '%' : 'N/A'}</div>
                                    <div class="stat-label">特別號命中率</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    ${comparison ? `
                        <div class="comparison-chart">
                            <h6><i class="fas fa-trophy"></i> 比較結果</h6>
                            <p><strong>較佳方法:</strong> ${comparison.better_method}</p>
                            <p><strong>改善幅度:</strong> ${comparison.improvement_percentage.toFixed(2)}%</p>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }
    
    function displayDetailedResults(detailedResults) {
        if (!detailedResults || detailedResults.length === 0) return '';
        
        let html = '<div class="mt-4"><h6>最近期數詳細結果:</h6>';
        
        detailedResults.forEach(result => {
            const accuracy = result.accuracy;
            html += `
                <div class="border-bottom py-2">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>期號 ${result.period}</strong>
                        </div>
                        <div class="col-md-3">
                            主號碼命中: ${accuracy.main_numbers_match_count}
                        </div>
                        <div class="col-md-3">
                            特別號: ${accuracy.special_number_match ? '✓' : '✗'}
                        </div>
                        <div class="col-md-3">
                            準確度: ${accuracy.match_percentage.toFixed(1)}%
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        return html;
    }
    
    function validateSinglePeriod() {
        const lotteryType = $('#validationLotteryType').val();
        const period = $('#validationPeriod').val();
        
        if (!lotteryType || !period) {
            alert('請填寫完整信息');
            return;
        }
        
        $('#validateBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 驗證中...');
        $('#validationResults').hide();
        
        $.ajax({
            url: `/api/backtesting/validate/${lotteryType}/${period}`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({}),
            success: function(response) {
                if (response.success) {
                    displayValidationResult(response.data);
                } else {
                    showError('驗證失敗: ' + (response.error || '未知錯誤'));
                }
            },
            error: function(xhr, status, error) {
                let errorMsg = '驗證失敗';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMsg = xhr.responseJSON.error;
                }
                showError(errorMsg);
            },
            complete: function() {
                $('#validateBtn').prop('disabled', false).html('<i class="fas fa-check"></i> 驗證期號');
            }
        });
    }
    
    function displayValidationResult(data) {
        const accuracy = data.accuracy_evaluation;
        const prediction = data.prediction;
        const actual = data.actual_result;
        
        const html = `
            <div class="method-card">
                <div class="card-header">
                    <i class="fas fa-search"></i> 期號 ${data.period} 驗證結果
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>預測結果</h6>
                            <p><strong>號碼:</strong> ${prediction.numbers.join(', ')}</p>
                            ${prediction.special_number ? `<p><strong>特別號:</strong> ${prediction.special_number}</p>` : ''}
                            <p><strong>信心度:</strong> ${(prediction.confidence * 100).toFixed(1)}%</p>
                        </div>
                        <div class="col-md-6">
                            <h6>實際開獎</h6>
                            <p><strong>號碼:</strong> ${actual.numbers.join(', ')}</p>
                            ${actual.special ? `<p><strong>特別號:</strong> ${actual.special}</p>` : ''}
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <h6>準確度分析</h6>
                        <div class="result-stats">
                            <div class="stat-item">
                                <div class="stat-value">${accuracy.main_numbers_match_count}</div>
                                <div class="stat-label">主號碼命中數</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${accuracy.special_number_match ? '是' : '否'}</div>
                                <div class="stat-label">特別號命中</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${accuracy.match_percentage.toFixed(1)}%</div>
                                <div class="stat-label">整體準確度</div>
                            </div>
                        </div>
                        
                        ${accuracy.main_numbers_matches.length > 0 ? 
                            `<p class="mt-2"><strong>命中號碼:</strong> ${accuracy.main_numbers_matches.join(', ')}</p>` : 
                            '<p class="mt-2 text-muted">沒有命中的主號碼</p>'
                        }
                    </div>
                </div>
            </div>
        `;
        
        $('#validationResults').html(html).show();
        $('#backtestResults').hide();
        $('#emptyState').hide();
    }
    
    function getAccuracyClass(hits) {
        if (hits >= 3) return 'accuracy-high';
        if (hits >= 2) return 'accuracy-medium';
        return 'accuracy-low';
    }
    
    function showError(message) {
        const errorHtml = `
            <div class="method-card">
                <div class="card-body text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                    <h5 class="text-danger">操作失敗</h5>
                    <p class="text-muted">${message}</p>
                    <button class="btn btn-outline-primary" onclick="location.reload()">
                        <i class="fas fa-redo"></i> 重新嘗試
                    </button>
                </div>
            </div>
        `;
        $('#backtestResults').html(errorHtml).show();
        $('#validationResults').hide();
        $('#emptyState').hide();
    }
});
</script>
{% endblock %}