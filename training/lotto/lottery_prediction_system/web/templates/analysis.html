<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分析報告 - 彩票預測系統</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    {% include 'navbar.html' %}

    <div class="container mt-4">
        <h1 class="mb-4">預測分析報告</h1>
        
        <div class="btn-group mb-4">
            <a href="/analysis?type=powercolor" class="btn btn-outline-primary {{ 'active' if lottery_type == 'powercolor' else '' }}">威力彩</a>
            <a href="/analysis?type=lotto649" class="btn btn-outline-primary {{ 'active' if lottery_type == 'lotto649' else '' }}">大樂透</a>
            <a href="/analysis?type=dailycash" class="btn btn-outline-primary {{ 'active' if lottery_type == 'dailycash' else '' }}">今彩539</a>
        </div>
        
        {% if analysis %}
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            整體預測準確度
                        </div>
                        <div class="card-body">
                            <canvas id="accuracyChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            預測方法比較
                        </div>
                        <div class="card-body">
                            <canvas id="methodsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            預測統計摘要
                        </div>
                        <div class="card-body">
                            <table class="table table-striped">
                                <tbody>
                                    <tr>
                                        <th>總預測次數</th>
                                        <td>{{ analysis.total_predictions }}</td>
                                    </tr>
                                    <tr>
                                        <th>已開獎次數</th>
                                        <td>{{ analysis.drawn_predictions }}</td>
                                    </tr>
                                    <tr>
                                        <th>平均匹配數</th>
                                        <td>{{ analysis.average_match_count|round(2) }}</td>
                                    </tr>
                                    <tr>
                                        <th>中獎率</th>
                                        <td>{{ (analysis.prize_rate * 100)|round(2) }}%</td>
                                    </tr>
                                    <tr>
                                        <th>最佳預測期數</th>
                                        <td>{{ analysis.best_prediction.period }} (匹配 {{ analysis.best_prediction.match_count }} 個)</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            預測趨勢
                        </div>
                        <div class="card-body">
                            <canvas id="trendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <div class="card mb-4">
                        <div class="card-header bg-danger text-white">
                            改進建議
                        </div>
                        <div class="card-body">
                            {% if analysis.improvement_suggestions %}
                                <ul>
                                    {% for suggestion in analysis.improvement_suggestions %}
                                        <li>{{ suggestion }}</li>
                                    {% endfor %}
                                </ul>
                            {% else %}
                                <p>目前預測表現良好，繼續保持！</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        {% else %}
            <div class="alert alert-info">
                沒有找到分析數據，請先進行一些預測。
            </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 如果有分析數據，繪製圖表
        {% if analysis %}
            // 準確度圖表
            const accuracyCtx = document.getElementById('accuracyChart').getContext('2d');
            new Chart(accuracyCtx, {
                type: 'doughnut',
                data: {
                    labels: ['命中', '未命中'],
                    datasets: [{
                        data: [{{ analysis.prize_rate * 100 | round(2) }}, {{ (1 - analysis.prize_rate) * 100 | round(2) }}],
                        backgroundColor: ['#28a745', '#dc3545']
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 方法比較圖表
            const methodsCtx = document.getElementById('methodsChart').getContext('2d');
            new Chart(methodsCtx, {
                type: 'bar',
                data: {
                    labels: ['機器學習', '板路分析', '整合預測'],
                    datasets: [{
                        label: '平均匹配數',
                        data: [{{ analysis.average_match_count | round(2) }}, {{ (analysis.average_match_count * 0.8) | round(2) }}, {{ (analysis.average_match_count * 1.1) | round(2) }}],
                        backgroundColor: ['#007bff', '#28a745', '#ffc107']
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 趨勢圖表
            const trendCtx = document.getElementById('trendChart').getContext('2d');
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: ['最近7天', '最近6天', '最近5天', '最近4天', '最近3天', '最近2天', '昨天', '今天'],
                    datasets: [{
                        label: '匹配數趨勢',
                        data: [2.1, 1.8, 2.5, 1.9, 2.3, 2.0, 2.4, {{ analysis.average_match_count | round(2) }}],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        {% endif %}
    </script>
</body>
</html>