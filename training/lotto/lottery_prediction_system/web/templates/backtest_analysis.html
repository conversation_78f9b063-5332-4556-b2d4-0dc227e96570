<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回測分析 - 彩票預測系統</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .match-0 { background-color: #dc3545; color: white; }
        .match-1 { background-color: #fd7e14; color: white; }
        .match-2 { background-color: #ffc107; color: black; }
        .match-3 { background-color: #20c997; color: white; }
        .match-4 { background-color: #198754; color: white; }
        .match-5 { background-color: #0d6efd; color: white; }
        .match-6 { background-color: #6610f2; color: white; }
        .match-special { background-color: #d63384; color: white; }
        
        .number-ball {
            display: inline-block;
            width: 28px;
            height: 28px;
            line-height: 28px;
            text-align: center;
            border-radius: 50%;
            margin-right: 3px;
            font-weight: bold;
            font-size: 12px;
        }
        
        .main-number { background-color: #007bff; color: white; }
        .special-number { background-color: #dc3545; color: white; }
        .matched-number { background-color: #28a745; color: white; box-shadow: 0 0 10px #28a745; }
        
        .accuracy-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        
        .prediction-row {
            transition: all 0.3s ease;
        }
        
        .prediction-row:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 2rem;
        }
        
        .filter-section {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    {% include 'navbar.html' %}

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>📊 回測分析系統</h1>
            <div>
                <span class="badge bg-info me-2" id="modeIndicator">預測記錄模式</span>
            </div>
        </div>

        <!-- 篩選和控制區域 -->
        <div class="filter-section">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">數據來源</label>
                    <select class="form-select" id="dataSource" onchange="handleDataSourceChange()">
                        <option value="predictions">預測記錄分析</option>
                        <option value="reports">回測報告查看</option>  
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">彩票類型</label>
                    <select class="form-select" id="lotteryType" onchange="loadBacktestData()">
                        <option value="powercolor">威力彩</option>
                        <option value="lotto649">大樂透</option>
                        <option value="dailycash">今彩539</option>
                    </select>
                </div>
                <div class="col-md-3" id="timeRangeSection">
                    <label class="form-label">分析期間</label>
                    <select class="form-select" id="timeRange" onchange="loadBacktestData()">
                        <option value="30">最近30期</option>
                        <option value="60">最近60期</option>
                        <option value="100">最近100期</option>
                        <option value="all">全部期數</option>
                    </select>
                </div>
                <div class="col-md-3" id="reportSelectionSection" style="display: none;">
                    <label class="form-label">選擇回測報告</label>
                    <select class="form-select" id="reportSelection" onchange="loadSelectedReport()">
                        <option value="">請選擇報告...</option>
                    </select>
                </div>
                <div class="col-md-3" id="methodSection">
                    <label class="form-label">預測方法</label>
                    <select class="form-select" id="predictionMethod" onchange="loadBacktestData()">
                        <option value="all">全部方法</option>
                        <option value="ml">機器學習</option>
                        <option value="board_path">板路分析</option>
                        <option value="integrated">整合預測</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3" id="filterRow">
                <div class="col-md-3">
                    <label class="form-label">匹配篩選</label>
                    <select class="form-select" id="matchFilter" onchange="loadBacktestData()">
                        <option value="all">全部結果</option>
                        <option value="3+">3個以上匹配</option>
                        <option value="4+">4個以上匹配</option>
                        <option value="5+">5個以上匹配</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-success mt-4" onclick="generateNewPredictions()">
                        <i class="fas fa-plus"></i> 生成新預測
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-primary mt-4" onclick="refreshReportsList()">
                        <i class="fas fa-sync-alt"></i> 重新載入報告
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-info mt-4" onclick="downloadCurrentReport()">
                        <i class="fas fa-download"></i> 下載報告
                    </button>
                </div>
            </div>
        </div>

        <!-- 統計概覽卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h5 class="card-title">總預測數</h5>
                        <h2 id="totalPredictions">-</h2>
                        <small>已驗證預測</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h5 class="card-title">整體準確度</h5>
                        <h2 id="overallAccuracy">-</h2>
                        <small>平均匹配率</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h5 class="card-title">最佳預測</h5>
                        <h2 id="bestMatch">-</h2>
                        <small>最高匹配數</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h5 class="card-title">中獎率</h5>
                        <h2 id="prizingRate">-</h2>
                        <small>有獎預測比例</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 圖表區域 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>準確度趨勢</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="accuracyTrendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>匹配數分佈</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="matchDistributionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>方法比較分析</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="methodComparisonChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 詳細回測結果表格 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5>📋 詳細回測結果</h5>
                <div>
                    <button class="btn btn-outline-primary btn-sm" onclick="exportToCSV()">
                        <i class="fas fa-download"></i> 匯出CSV
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="generateReport()">
                        <i class="fas fa-chart-line"></i> 生成報告
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>期數</th>
                                <th>預測號碼</th>
                                <th>實際號碼</th>
                                <th>匹配</th>
                                <th>準確度</th>
                                <th>預測方法</th>
                                <th>信心度</th>
                                <th>獎金等級</th>
                                <th>分析</th>
                            </tr>
                        </thead>
                        <tbody id="backtestResults">
                            <!-- 動態載入內容 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 分頁控制 -->
                <nav aria-label="分頁導航">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- 動態生成分頁 -->
                    </ul>
                </nav>
            </div>
        </div>

        <!-- 分析摘要 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5>🧠 智能分析摘要</h5>
            </div>
            <div class="card-body">
                <div id="analysisSummary">
                    <!-- 動態載入分析摘要 -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定義 JavaScript -->
    <script>
        let currentPage = 1;
        let totalPages = 1;
        let currentData = [];
        let charts = {};
        let reportsList = [];
        let currentReport = null;
        let dataSourceMode = 'predictions'; // 'predictions' 或 'reports'

        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadBacktestData();
            loadReportsList();
        });

        // 處理數據來源切換
        function handleDataSourceChange() {
            const dataSource = document.getElementById('dataSource').value;
            dataSourceMode = dataSource;
            
            if (dataSource === 'reports') {
                // 切換到回測報告模式
                document.getElementById('timeRangeSection').style.display = 'none';
                document.getElementById('methodSection').style.display = 'none';
                document.getElementById('reportSelectionSection').style.display = 'block';
                document.getElementById('modeIndicator').textContent = '回測報告模式';
                document.getElementById('modeIndicator').className = 'badge bg-success me-2';
                loadReportsList();
            } else {
                // 切換到預測記錄分析模式
                document.getElementById('timeRangeSection').style.display = 'block';
                document.getElementById('methodSection').style.display = 'block';
                document.getElementById('reportSelectionSection').style.display = 'none';
                document.getElementById('modeIndicator').textContent = '預測記錄模式';
                document.getElementById('modeIndicator').className = 'badge bg-info me-2';
                loadBacktestData();
            }
        }

        // 載入回測報告列表
        async function loadReportsList() {
            try {
                const response = await fetch('/api/backtest/reports');
                const data = await response.json();
                
                if (data.success) {
                    reportsList = data.data.reports;
                    updateReportsDropdown();
                } else {
                    showError(data.error);
                }
            } catch (error) {
                showError('載入報告列表時發生錯誤: ' + error.message);
            }
        }

        // 更新報告下拉列表
        function updateReportsDropdown() {
            const reportSelection = document.getElementById('reportSelection');
            const lotteryType = document.getElementById('lotteryType').value;
            
            // 清空選項
            reportSelection.innerHTML = '<option value="">請選擇報告...</option>';
            
            // 篩選對應彩票類型的報告
            const filteredReports = reportsList.filter(report => 
                report.lottery_type === lotteryType
            );
            
            filteredReports.forEach(report => {
                const timestamp = report.timestamp.replace('_', ' ');
                const option = document.createElement('option');
                option.value = report.filename;
                option.textContent = `${report.lottery_type} (${timestamp})`;
                reportSelection.appendChild(option);
            });
        }

        // 載入選定的回測報告
        async function loadSelectedReport() {
            const filename = document.getElementById('reportSelection').value;
            if (!filename) return;
            
            try {
                showLoading(true);
                
                const response = await fetch(`/api/backtest/report/${filename}`);
                const data = await response.json();
                
                if (data.success) {
                    currentReport = data.data;
                    displayReportData(data.data);
                } else {
                    showError(data.error);
                }
            } catch (error) {
                showError('載入報告時發生錯誤: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 顯示報告數據
        function displayReportData(reportData) {
            // 更新統計卡片
            const stats = reportData.statistics || {};
            updateReportStatistics(stats);
            
            // 更新圖表
            updateReportCharts(reportData);
            
            // 更新詳細結果表格
            updateReportTable(reportData.detailed_results || []);
            
            // 更新分析摘要
            updateReportAnalysisSummary(reportData);
        }

        // 載入回測數據
        async function loadBacktestData() {
            if (dataSourceMode === 'reports') {
                // 如果是報告模式，不執行預測記錄載入
                return;
            }
            
            const lotteryType = document.getElementById('lotteryType').value;
            const timeRange = document.getElementById('timeRange').value;
            const method = document.getElementById('predictionMethod').value;
            const matchFilter = document.getElementById('matchFilter').value;

            try {
                showLoading(true);
                
                // 載入預測記錄
                const response = await fetch(`/api/backtest_analysis/${lotteryType}?time_range=${timeRange}&method=${method}&match_filter=${matchFilter}&page=${currentPage}`);
                const data = await response.json();
                
                if (data.success) {
                    currentData = data.data;
                    updateStatistics(data.data.statistics);
                    updateCharts(data.data);
                    updateTable(data.data.predictions);
                    updateAnalysisSummary(data.data.analysis);
                    updatePagination(data.data.pagination);
                } else {
                    showError(data.error);
                }
            } catch (error) {
                showError('載入數據時發生錯誤: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 更新統計卡片
        function updateStatistics(stats) {
            document.getElementById('totalPredictions').textContent = stats.total_predictions || '0';
            document.getElementById('overallAccuracy').textContent = (stats.overall_accuracy * 100).toFixed(1) + '%';
            document.getElementById('bestMatch').textContent = stats.best_match + ' 個';
            document.getElementById('prizingRate').textContent = (stats.prizing_rate * 100).toFixed(1) + '%';
        }

        // 更新報告統計卡片
        function updateReportStatistics(stats) {
            document.getElementById('totalPredictions').textContent = stats.total_periods || '0';
            document.getElementById('overallAccuracy').textContent = (stats.accuracy || 0).toFixed(1) + '%';
            document.getElementById('bestMatch').textContent = (stats.total_matches || 0) + ' 個';
            document.getElementById('prizingRate').textContent = (stats.average_matches_per_period || 0).toFixed(1);
        }

        // 更新報告圖表
        function updateReportCharts(reportData) {
            // 準確度趨勢圖 - 根據詳細結果生成
            if (reportData.detailed_results && reportData.detailed_results.length > 0) {
                const trendData = reportData.detailed_results.slice(0, 20).map(result => ({
                    period: result.period,
                    accuracy: result.accuracy || 0
                }));
                updateAccuracyTrendChart(trendData);
            }
            
            // 匹配數分佈圖
            if (reportData.match_distribution) {
                const distributionData = Object.entries(reportData.match_distribution).map(([key, value]) => ({
                    matches: key.replace('matches_', ''),
                    count: value
                }));
                updateMatchDistributionChart(distributionData);
            }
            
            // 方法比較圖 - 模擬數據，因為報告中可能沒有多方法比較
            const mockMethodData = [{
                method: reportData.config?.method || '未知方法',
                accuracy: (reportData.statistics?.accuracy || 0) / 100,
                count: reportData.statistics?.total_periods || 0
            }];
            updateMethodComparisonChart(mockMethodData);
        }

        // 更新報告表格
        function updateReportTable(detailedResults) {
            const tbody = document.getElementById('backtestResults');
            tbody.innerHTML = '';

            if (!detailedResults || detailedResults.length === 0) {
                tbody.innerHTML = '<tr><td colspan="9" class="text-center">沒有詳細結果數據</td></tr>';
                return;
            }

            detailedResults.slice(0, 20).forEach(result => {
                const row = createReportRow(result);
                tbody.appendChild(row);
            });
        }

        // 創建報告行
        function createReportRow(result) {
            const tr = document.createElement('tr');
            tr.className = 'prediction-row';

            const lotteryType = document.getElementById('lotteryType').value;
            const matchCount = result.matches || 0;
            
            // 匹配數樣式
            let matchClass = `match-${Math.min(matchCount, 6)}`;
            let accuracyBadge = '';
            if (matchCount >= 4) accuracyBadge = 'bg-success';
            else if (matchCount >= 2) accuracyBadge = 'bg-warning';
            else accuracyBadge = 'bg-secondary';

            const predictedNumbers = result.predicted_numbers || [];
            const actualNumbers = result.actual_numbers || [];
            const accuracy = result.accuracy || 0;
            const confidence = result.confidence || 0;

            tr.innerHTML = `
                <td><strong>${result.period}</strong></td>
                <td>${renderNumberBalls(predictedNumbers, null, lotteryType, 'predicted')}</td>
                <td>${renderNumberBalls(actualNumbers, null, lotteryType, 'actual')}</td>
                <td>
                    <span class="badge ${matchClass} accuracy-badge">
                        ${matchCount} / ${lotteryType === 'dailycash' ? 5 : 6}
                    </span>
                </td>
                <td>
                    <span class="badge ${accuracyBadge} accuracy-badge">
                        ${(accuracy * 100).toFixed(1)}%
                    </span>
                </td>
                <td>
                    <small class="text-muted">${result.method || '未知'}</small>
                </td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" role="progressbar" 
                             style="width: ${confidence * 100}%" 
                             aria-valuenow="${confidence * 100}" 
                             aria-valuemin="0" aria-valuemax="100">
                            ${(confidence * 100).toFixed(0)}%
                        </div>
                    </div>
                </td>
                <td><span class="badge bg-secondary">未分析</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" 
                            onclick="showReportDetailAnalysis('${result.period}')">
                        詳細
                    </button>
                </td>
            `;

            return tr;
        }

        // 更新報告分析摘要
        function updateReportAnalysisSummary(reportData) {
            const container = document.getElementById('analysisSummary');
            const config = reportData.config || {};
            const stats = reportData.statistics || {};
            
            container.innerHTML = `
                <div class="row">
                    <div class="col-md-12">
                        <h6>📊 回測配置</h6>
                        <p><strong>彩票類型:</strong> ${config.game_type || '未知'}</p>
                        <p><strong>預測方法:</strong> ${config.method || '未知'}</p>
                        <p><strong>訓練窗口:</strong> ${config.training_window || 0} 期</p>
                        
                        <h6>📈 統計結果</h6>
                        <p><strong>總測試期數:</strong> ${stats.total_periods || 0}</p>
                        <p><strong>平均準確度:</strong> ${(stats.accuracy || 0).toFixed(2)}%</p>
                        <p><strong>總匹配數:</strong> ${stats.total_matches || 0}</p>
                        <p><strong>平均匹配/期:</strong> ${(stats.average_matches_per_period || 0).toFixed(2)}</p>
                        
                        <h6>🏆 獎項統計</h6>
                        ${generatePrizeDistributionHTML(reportData.prize_distribution)}
                        
                        <h6>💡 分析建議</h6>
                        <ul>
                            <li>此報告基於歷史數據回測生成</li>
                            <li>準確度反映歷史表現，不保證未來結果</li>
                            <li>建議結合多種方法進行預測</li>
                        </ul>
                    </div>
                </div>
            `;
        }

        // 生成獎項分佈HTML
        function generatePrizeDistributionHTML(prizeDistribution) {
            if (!prizeDistribution) {
                return '<p>無獎項分佈數據</p>';
            }
            
            let html = '<div class="row">';
            for (const [prize, count] of Object.entries(prizeDistribution)) {
                if (count > 0) {
                    const prizeName = prize.replace('prize_', '第') + '獎';
                    html += `<div class="col-md-2"><span class="badge bg-info">${prizeName}: ${count}</span></div>`;
                }
            }
            html += '</div>';
            
            return html;
        }

        // 更新圖表
        function updateCharts(data) {
            // 準確度趨勢圖
            updateAccuracyTrendChart(data.accuracy_trend);
            
            // 匹配數分佈圖
            updateMatchDistributionChart(data.match_distribution);
            
            // 方法比較圖
            updateMethodComparisonChart(data.method_comparison);
        }

        // 準確度趨勢圖
        function updateAccuracyTrendChart(trendData) {
            const ctx = document.getElementById('accuracyTrendChart').getContext('2d');
            
            if (charts.accuracyTrend) {
                charts.accuracyTrend.destroy();
            }

            charts.accuracyTrend = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: trendData.map(d => d.period),
                    datasets: [{
                        label: '準確度',
                        data: trendData.map(d => d.accuracy * 100),
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                }
            });
        }

        // 匹配數分佈圖
        function updateMatchDistributionChart(distributionData) {
            const ctx = document.getElementById('matchDistributionChart').getContext('2d');
            
            if (charts.matchDistribution) {
                charts.matchDistribution.destroy();
            }

            const colors = ['#dc3545', '#fd7e14', '#ffc107', '#20c997', '#198754', '#0d6efd', '#6610f2'];

            charts.matchDistribution = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: distributionData.map(d => d.matches + ' 個匹配'),
                    datasets: [{
                        data: distributionData.map(d => d.count),
                        backgroundColor: colors.slice(0, distributionData.length),
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // 方法比較圖
        function updateMethodComparisonChart(comparisonData) {
            const ctx = document.getElementById('methodComparisonChart').getContext('2d');
            
            if (charts.methodComparison) {
                charts.methodComparison.destroy();
            }

            charts.methodComparison = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: comparisonData.map(d => d.method),
                    datasets: [
                        {
                            label: '準確度',
                            data: comparisonData.map(d => d.accuracy * 100),
                            backgroundColor: 'rgba(0, 123, 255, 0.8)',
                            borderColor: '#007bff',
                            borderWidth: 1,
                            yAxisID: 'y'
                        },
                        {
                            label: '預測數量',
                            data: comparisonData.map(d => d.count),
                            backgroundColor: 'rgba(40, 167, 69, 0.8)',
                            borderColor: '#28a745',
                            borderWidth: 1,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            beginAtZero: true,
                            grid: {
                                drawOnChartArea: false,
                            }
                        }
                    }
                }
            });
        }

        // 更新表格
        function updateTable(predictions) {
            const tbody = document.getElementById('backtestResults');
            tbody.innerHTML = '';

            predictions.forEach(prediction => {
                const row = createPredictionRow(prediction);
                tbody.appendChild(row);
            });
        }

        // 創建預測行
        function createPredictionRow(prediction) {
            const tr = document.createElement('tr');
            tr.className = 'prediction-row';

            const lotteryType = document.getElementById('lotteryType').value;
            const matchCount = prediction.matches;
            
            // 匹配數樣式
            let matchClass = `match-${Math.min(matchCount, 6)}`;
            let accuracyBadge = '';
            if (matchCount >= 4) accuracyBadge = 'bg-success';
            else if (matchCount >= 2) accuracyBadge = 'bg-warning';
            else accuracyBadge = 'bg-secondary';

            tr.innerHTML = `
                <td><strong>${prediction.period}</strong></td>
                <td>${renderNumberBalls(prediction.predicted_numbers, prediction.predicted_special, lotteryType, 'predicted')}</td>
                <td>${renderNumberBalls(prediction.actual_numbers, prediction.actual_special, lotteryType, 'actual')}</td>
                <td>
                    <span class="badge ${matchClass} accuracy-badge">
                        ${matchCount} / ${lotteryType === 'dailycash' ? 5 : 6}
                    </span>
                </td>
                <td>
                    <span class="badge ${accuracyBadge} accuracy-badge">
                        ${(prediction.accuracy * 100).toFixed(1)}%
                    </span>
                </td>
                <td>
                    <small class="text-muted">${prediction.method}</small>
                </td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" role="progressbar" 
                             style="width: ${prediction.confidence * 100}%" 
                             aria-valuenow="${prediction.confidence * 100}" 
                             aria-valuemin="0" aria-valuemax="100">
                            ${(prediction.confidence * 100).toFixed(0)}%
                        </div>
                    </div>
                </td>
                <td>${renderPrizeLevel(prediction.prize_level)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" 
                            onclick="showDetailAnalysis('${prediction.period}')">
                        詳細
                    </button>
                </td>
            `;

            return tr;
        }

        // 渲染號碼球
        function renderNumberBalls(numbers, special, lotteryType, type) {
            let html = '';
            
            // 主要號碼
            numbers.forEach(num => {
                html += `<span class="number-ball main-number">${num}</span>`;
            });
            
            // 特別號 (威力彩和大樂透)
            if (lotteryType !== 'dailycash' && special) {
                html += `<span class="number-ball special-number">${special}</span>`;
            }
            
            return html;
        }

        // 渲染獎金等級
        function renderPrizeLevel(level) {
            if (!level || level === 'no_prize') {
                return '<span class="badge bg-secondary">未中獎</span>';
            }
            
            const prizes = {
                'level_1': '<span class="badge bg-danger">頭獎</span>',
                'level_2': '<span class="badge bg-warning">二獎</span>',
                'level_3': '<span class="badge bg-info">三獎</span>',
                'level_4': '<span class="badge bg-success">四獎</span>',
                'level_5': '<span class="badge bg-primary">五獎</span>',
                'level_6': '<span class="badge bg-dark">六獎</span>'
            };
            
            return prizes[level] || '<span class="badge bg-light text-dark">普獎</span>';
        }

        // 更新分析摘要
        function updateAnalysisSummary(analysis) {
            const container = document.getElementById('analysisSummary');
            
            container.innerHTML = `
                <div class="row">
                    <div class="col-md-12">
                        <h6>📈 趨勢分析</h6>
                        <p>${analysis.trend_analysis}</p>
                        
                        <h6>🎯 最佳策略</h6>
                        <p>${analysis.best_strategy}</p>
                        
                        <h6>💡 改進建議</h6>
                        <ul>
                            ${analysis.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                        
                        <h6>📊 統計洞察</h6>
                        <p>${analysis.statistical_insights}</p>
                    </div>
                </div>
            `;
        }

        // 更新分頁
        function updatePagination(pagination) {
            totalPages = pagination.total_pages;
            currentPage = pagination.current_page;
            
            const paginationContainer = document.getElementById('pagination');
            paginationContainer.innerHTML = '';
            
            // 上一頁
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一頁</a>`;
            paginationContainer.appendChild(prevLi);
            
            // 頁碼
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
                paginationContainer.appendChild(li);
            }
            
            // 下一頁
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一頁</a>`;
            paginationContainer.appendChild(nextLi);
        }

        // 切換頁面
        function changePage(page) {
            if (page >= 1 && page <= totalPages && page !== currentPage) {
                currentPage = page;
                loadBacktestData();
            }
        }

        // 顯示詳細分析
        function showDetailAnalysis(period) {
            // TODO: 實現詳細分析模態框
            alert(`顯示期數 ${period} 的詳細分析`);
        }

        // 執行批次回測
        async function runBacktest() {
            const lotteryType = document.getElementById('lotteryType').value;
            const testPeriods = parseInt(document.getElementById('testPeriods').value);
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            const submitBtn = event.target;
            const originalText = submitBtn.innerHTML;
            
            try {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>回測中...';
                
                const requestData = {
                    test_periods: testPeriods
                };
                
                if (startDate) requestData.start_date = startDate;
                if (endDate) requestData.end_date = endDate;
                
                const response = await fetch(`/api/backtest/run/${lotteryType}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    displayBacktestResults(result.data);
                    loadReportsList();
                    alert('回測完成！');
                } else {
                    throw new Error(result.error);
                }
                
            } catch (error) {
                console.error('回測失敗:', error);
                alert(`回測失敗: ${error.message}`);
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        }
        
        // 執行快速回測
        async function runQuickBacktest() {
            const lotteryType = document.getElementById('lotteryType').value;
            const submitBtn = event.target;
            const originalText = submitBtn.innerHTML;
            
            try {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>快速回測中...';
                
                const response = await fetch(`/api/backtest/quick/${lotteryType}`);
                const result = await response.json();
                
                if (result.success) {
                    displayQuickResults(result.data);
                    alert('快速回測完成！');
                } else {
                    throw new Error(result.error);
                }
                
            } catch (error) {
                console.error('快速回測失敗:', error);
                alert(`快速回測失敗: ${error.message}`);
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        }
        
        // 顯示回測結果
        function displayBacktestResults(data) {
            const resultsContainer = document.getElementById('backtestResults');
            
            let html = `
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle me-2"></i>回測完成</h5>
                    <p>彩票類型: ${data.lottery_type} | 測試期數: ${data.backtest_period.total_periods}</p>
                    <p>測試期間: ${data.backtest_period.start_date} 至 ${data.backtest_period.end_date}</p>
                </div>
            `;
            
            if (data.overall_analysis) {
                const analysis = data.overall_analysis;
                html += `
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6><i class="fas fa-trophy me-2"></i>最佳表現方法</h6>
                                </div>
                                <div class="card-body">
                                    <h5>${analysis.best_performing_method.method}</h5>
                                    <p class="text-success">準確度: ${analysis.best_performing_method.accuracy.toFixed(2)}%</p>
                                    <p class="text-muted">性能分數: ${analysis.best_performing_method.score.toFixed(2)}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6><i class="fas fa-chart-bar me-2"></i>整體統計</h6>
                                </div>
                                <div class="card-body">
                                    <p>平均準確度: ${analysis.average_accuracy.toFixed(2)}%</p>
                                    <p>準確度變異: ${analysis.accuracy_variance.toFixed(4)}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            // 方法性能表格
            if (data.method_performance) {
                html += `
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-table me-2"></i>方法性能詳情</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>預測方法</th>
                                            <th>平均準確度</th>
                                            <th>總預測次數</th>
                                            <th>3中以上率</th>
                                            <th>性能分數</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                `;
                
                for (const [method, performance] of Object.entries(data.method_performance)) {
                    html += `
                        <tr>
                            <td><strong>${method}</strong></td>
                            <td>${performance.avg_accuracy.toFixed(2)}%</td>
                            <td>${performance.total_predictions}</td>
                            <td>${(performance.hit_rate_3plus * 100).toFixed(1)}%</td>
                            <td>${performance.performance_score.toFixed(2)}</td>
                        </tr>
                    `;
                }
                
                html += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            // 建議
            if (data.recommendations) {
                html += `
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6><i class="fas fa-lightbulb me-2"></i>優化建議</h6>
                        </div>
                        <div class="card-body">
                            <ul>
                                ${data.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                `;
            }
            
            resultsContainer.innerHTML = html;
        }
        
        // 顯示快速結果
        function displayQuickResults(data) {
            const resultsContainer = document.getElementById('backtestResults');
            
            let html = `
                <div class="alert alert-info">
                    <h5><i class="fas fa-bolt me-2"></i>快速回測結果</h5>
                    <p>彩票類型: ${data.lottery_type} | 測試期數: ${data.test_periods}</p>
                </div>
            `;
            
            if (data.best_method) {
                html += `
                    <div class="card border-primary mb-3">
                        <div class="card-header bg-primary text-white">
                            <h6><i class="fas fa-star me-2"></i>推薦方法</h6>
                        </div>
                        <div class="card-body">
                            <h5>${data.best_method.method}</h5>
                            <p>準確度: ${data.best_method.accuracy.toFixed(2)}%</p>
                        </div>
                    </div>
                `;
            }
            
            if (data.top_methods) {
                html += `
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-list me-2"></i>前三名方法</h6>
                        </div>
                        <div class="card-body">
                `;
                
                data.top_methods.forEach((method, index) => {
                    const [name, stats] = method;
                    const rank = index + 1;
                    html += `
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <span class="badge bg-secondary me-2">${rank}</span>
                                <strong>${name}</strong>
                            </div>
                            <div class="text-end">
                                <div>準確度: ${stats.avg_accuracy.toFixed(2)}%</div>
                                <small class="text-muted">分數: ${stats.performance_score.toFixed(2)}</small>
                            </div>
                        </div>
                        ${index < data.top_methods.length - 1 ? '<hr>' : ''}
                    `;
                });
                
                html += `
                        </div>
                    </div>
                `;
            }
            
            resultsContainer.innerHTML = html;
        }

        // 生成新預測
        function generateNewPredictions() {
            // TODO: 實現生成新預測功能
            alert('生成新預測功能開發中');
        }

        // 匯出CSV
        function exportToCSV() {
            // TODO: 實現CSV匯出功能
            alert('CSV匯出功能開發中');
        }

        // 生成報告
        function generateReport() {
            // TODO: 實現報告生成功能
            alert('報告生成功能開發中');
        }

        // 顯示載入狀態
        function showLoading(show) {
            // TODO: 實現載入狀態顯示
        }

        // 顯示錯誤
        function showError(message) {
            alert('錯誤: ' + message);
        }

        // 重新載入報告列表
        function refreshReportsList() {
            loadReportsList().then(() => {
                updateReportsDropdown();
            });
        }

        // 下載當前報告
        function downloadCurrentReport() {
            if (dataSourceMode === 'reports' && currentReport) {
                const filename = document.getElementById('reportSelection').value;
                if (filename) {
                    const dataStr = JSON.stringify(currentReport, null, 2);
                    const dataBlob = new Blob([dataStr], {type: 'application/json'});
                    const url = URL.createObjectURL(dataBlob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = filename;
                    link.click();
                    URL.revokeObjectURL(url);
                }
            } else {
                alert('請先選擇一個回測報告');
            }
        }

        // 顯示報告詳細分析
        function showReportDetailAnalysis(period) {
            if (currentReport && currentReport.detailed_results) {
                const result = currentReport.detailed_results.find(r => r.period === period);
                if (result) {
                    let analysisHTML = `
                        <div class="modal fade" id="detailModal" tabindex="-1">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">期數 ${period} 詳細分析</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <h6>預測結果</h6>
                                        <p><strong>預測號碼:</strong> ${result.predicted_numbers.join(', ')}</p>
                                        <p><strong>實際號碼:</strong> ${result.actual_numbers.join(', ')}</p>
                                        <p><strong>匹配數:</strong> ${result.matches}</p>
                                        <p><strong>準確度:</strong> ${(result.accuracy * 100).toFixed(2)}%</p>
                                        <p><strong>信心度:</strong> ${(result.confidence * 100).toFixed(2)}%</p>
                                        
                                        <h6>預測方法</h6>
                                        <p>${result.method || '未知方法'}</p>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // 移除現有的模態框
                    const existingModal = document.getElementById('detailModal');
                    if (existingModal) {
                        existingModal.remove();
                    }
                    
                    // 添加新的模態框
                    document.body.insertAdjacentHTML('beforeend', analysisHTML);
                    
                    // 顯示模態框
                    const modal = new bootstrap.Modal(document.getElementById('detailModal'));
                    modal.show();
                }
            } else {
                alert(`顯示期數 ${period} 的詳細分析`);
            }
        }

        // 處理彩票類型變更
        document.getElementById('lotteryType').addEventListener('change', function() {
            if (dataSourceMode === 'reports') {
                updateReportsDropdown();
            } else {
                loadBacktestData();
            }
        });
    </script>
</body>
</html>