<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系統儀表板 - 彩票預測系統</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .stat-card {
            border-radius: 10px;
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .stat-icon {
            font-size: 2rem;
            opacity: 0.8;
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-online {
            background-color: #28a745;
            animation: pulse 2s infinite;
        }
        .status-offline {
            background-color: #dc3545;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    {% include 'navbar.html' %}

    <div class="container-fluid mt-4">
        <!-- 系統狀態 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h5><i class="fas fa-server"></i> 系統狀態</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="d-flex align-items-center">
                                    <span class="status-indicator status-online" id="systemStatus"></span>
                                    <span>系統運行正常</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">最後更新:</small><br>
                                <span id="lastUpdate">載入中...</span>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">總預測數:</small><br>
                                <span id="totalPredictions">載入中...</span>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-primary btn-sm" onclick="refreshData()">
                                    <i class="fas fa-sync-alt"></i> 刷新數據
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 統計卡片 -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card stat-card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">威力彩</h6>
                                <h3 id="powercolorCount">-</h3>
                                <small>最新預測</small>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stat-card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">大樂透</h6>
                                <h3 id="lotto649Count">-</h3>
                                <small>最新預測</small>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-gem"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stat-card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">今彩539</h6>
                                <h3 id="dailycashCount">-</h3>
                                <small>最新預測</small>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-coins"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最新預測結果 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-clock"></i> 最新預測結果</h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="latestPredictions">
                            <div class="col-12 text-center text-muted">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">載入中...</span>
                                </div>
                                <p class="mt-2">載入最新預測結果...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 圖表區域 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-chart-pie"></i> 預測方法分佈</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="methodChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-chart-line"></i> 預測趨勢</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="trendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-tools"></i> 快速操作</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <button class="btn btn-outline-primary w-100 mb-2" onclick="window.location.href='/separated_prediction'">
                                    <i class="fas fa-magic"></i><br>分離式預測
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-success w-100 mb-2" onclick="runQuickTask('update')">
                                    <i class="fas fa-download"></i><br>更新開獎結果
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-info w-100 mb-2" onclick="runQuickTask('analysis')">
                                    <i class="fas fa-chart-bar"></i><br>分析準確度
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-warning w-100 mb-2" onclick="runQuickTask('full')">
                                    <i class="fas fa-cogs"></i><br>完整自動化任務
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 刷新按鈕 -->
    <button class="btn btn-primary btn-lg refresh-btn" onclick="refreshData()" title="刷新數據">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let methodChart, trendChart;

        // 初始化圖表
        function initCharts() {
            // 預測方法分佈圖
            const methodCtx = document.getElementById('methodChart').getContext('2d');
            methodChart = new Chart(methodCtx, {
                type: 'doughnut',
                data: {
                    labels: ['機器學習', '板路分析', '整合預測'],
                    datasets: [{
                        data: [30, 25, 45],
                        backgroundColor: ['#007bff', '#28a745', '#ffc107'],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 預測趨勢圖
            const trendCtx = document.getElementById('trendChart').getContext('2d');
            trendChart = new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: ['1週前', '6天前', '5天前', '4天前', '3天前', '2天前', '昨天', '今天'],
                    datasets: [{
                        label: '預測數量',
                        data: [12, 15, 8, 20, 18, 25, 22, 30],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 載入儀表板數據
        async function loadDashboardData() {
            try {
                const response = await fetch('/api/dashboard_data');
                const data = await response.json();

                if (data.error) {
                    console.error('載入儀表板數據失敗:', data.error);
                    return;
                }

                // 更新系統狀態
                if (data.system_status) {
                    document.getElementById('lastUpdate').textContent = data.system_status.last_update;
                    document.getElementById('totalPredictions').textContent = data.system_status.total_predictions;
                }

                // 更新統計卡片
                updateStatCard('powercolor', data.powercolor_latest);
                updateStatCard('lotto649', data.lotto649_latest);
                updateStatCard('dailycash', data.dailycash_latest);

                // 更新最新預測結果
                updateLatestPredictions(data);

            } catch (error) {
                console.error('載入儀表板數據時出錯:', error);
                document.getElementById('systemStatus').className = 'status-indicator status-offline';
            }
        }

        // 更新統計卡片
        function updateStatCard(lotteryType, latestData) {
            const countElement = document.getElementById(lotteryType + 'Count');
            
            if (latestData) {
                const period = latestData.Period || '-';
                countElement.textContent = `期數 ${period}`;
            } else {
                countElement.textContent = '無數據';
            }
        }

        // 更新最新預測結果
        function updateLatestPredictions(data) {
            const container = document.getElementById('latestPredictions');
            let html = '';

            const lotteryTypes = [
                { key: 'powercolor', name: '威力彩', color: 'primary' },
                { key: 'lotto649', name: '大樂透', color: 'success' },
                { key: 'dailycash', name: '今彩539', color: 'warning' }
            ];

            lotteryTypes.forEach(lottery => {
                const latest = data[lottery.key + '_latest'];
                
                html += `
                    <div class="col-md-4 mb-3">
                        <div class="card border-${lottery.color}">
                            <div class="card-header bg-${lottery.color} text-white">
                                <h6 class="mb-0">${lottery.name}</h6>
                            </div>
                            <div class="card-body">
                `;

                if (latest) {
                    html += `
                        <p class="mb-1"><strong>期數:</strong> ${latest.Period || '-'}</p>
                        <p class="mb-1"><strong>預測時間:</strong> ${latest.PredictionDate || '-'}</p>
                        <p class="mb-1"><strong>方法:</strong> ${latest.PredictionMethod || '-'}</p>
                        <p class="mb-0"><strong>信心分數:</strong> ${latest.Confidence ? latest.Confidence.toFixed(3) : '-'}</p>
                    `;
                } else {
                    html += '<p class="text-muted mb-0">暫無預測記錄</p>';
                }

                html += `
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 刷新數據
        async function refreshData() {
            const refreshBtn = document.querySelector('.refresh-btn i');
            refreshBtn.classList.add('fa-spin');
            
            await loadDashboardData();
            
            setTimeout(() => {
                refreshBtn.classList.remove('fa-spin');
            }, 1000);
        }

        // 執行快速任務
        async function runQuickTask(taskType) {
            const taskNames = {
                'update': '更新開獎結果',
                'analysis': '分析準確度',
                'full': '完整自動化任務'
            };

            if (!confirm(`確定要執行「${taskNames[taskType]}」嗎？`)) {
                return;
            }

            try {
                const response = await fetch('/api/daily_automation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({task_type: taskType})
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert(`✅ ${result.message}`);
                    // 刷新數據
                    await refreshData();
                } else {
                    alert(`❌ 任務失敗: ${result.error}`);
                }
            } catch (error) {
                alert(`❌ 網路錯誤: ${error.message}`);
            }
        }

        // 頁面載入完成後初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            loadDashboardData();
            
            // 每5分鐘自動刷新一次
            setInterval(loadDashboardData, 5 * 60 * 1000);
        });
    </script>
</body>
</html>
