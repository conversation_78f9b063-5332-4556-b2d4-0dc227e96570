<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能監控 - 增強彩票預測系統</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .performance-card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .performance-card:hover {
            transform: translateY(-2px);
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            margin-bottom: 20px;
        }
        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .metric-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-online {
            background: #28a745;
            animation: pulse 2s infinite;
        }
        .status-warning {
            background: #ffc107;
        }
        .status-offline {
            background: #dc3545;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .real-time-data {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .performance-gauge {
            text-align: center;
            padding: 20px;
        }
        .gauge-container {
            position: relative;
            width: 200px;
            height: 100px;
            margin: 0 auto;
        }
        .alert-item {
            border-left: 4px solid;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 0 5px 5px 0;
        }
        .alert-success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .alert-warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .alert-danger {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                增強彩票預測系統
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首頁</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/predict">智能預測</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analysis">成功率分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/performance">性能監控</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/history">歷史記錄</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <!-- 頁面標題 -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="fw-bold">
                    <i class="fas fa-tachometer-alt me-2 text-primary"></i>
                    系統性能監控
                </h2>
                <p class="text-muted">實時監控預測系統的運行狀態和性能指標</p>
            </div>
        </div>

        <!-- 系統狀態總覽 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card performance-card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-server me-2"></i>
                            系統狀態總覽
                        </h5>
                        <div>
                            <span class="status-indicator status-online"></span>
                            <span>系統運行中</span>
                            <button class="btn btn-sm btn-outline-light ms-3" onclick="refreshSystemStatus()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row" id="systemStatusContent">
                            <!-- 動態載入系統狀態 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 關鍵性能指標 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="totalPredictions">0</div>
                    <div class="metric-label">總預測次數</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card" style="background: linear-gradient(135deg, #28a745, #20c997);">
                    <div class="metric-value" id="avgSuccessRate">0%</div>
                    <div class="metric-label">平均成功率</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card" style="background: linear-gradient(135deg, #ffc107, #fd7e14);">
                    <div class="metric-value" id="activeModels">0</div>
                    <div class="metric-label">活躍模型數</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card" style="background: linear-gradient(135deg, #dc3545, #e83e8c);">
                    <div class="metric-value" id="systemUptime">0h</div>
                    <div class="metric-label">系統運行時間</div>
                </div>
            </div>
        </div>

        <!-- 實時性能圖表 -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card performance-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-area me-2"></i>
                            實時性能趨勢
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="performanceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card performance-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-gauge me-2"></i>
                            系統健康度
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="performance-gauge">
                            <div class="gauge-container">
                                <canvas id="healthGauge" width="200" height="100"></canvas>
                            </div>
                            <h4 class="mt-3" id="healthScore">95%</h4>
                            <p class="text-muted">系統健康度</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 方法性能比較 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card performance-card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-balance-scale me-2"></i>
                            方法性能比較
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="chart-container">
                                    <canvas id="methodComparisonChart"></canvas>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div id="methodPerformanceDetails">
                                    <!-- 動態載入方法性能詳情 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系統警報和通知 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card performance-card">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-bell me-2"></i>
                            系統警報
                        </h5>
                    </div>
                    <div class="card-body" id="systemAlerts">
                        <!-- 動態載入系統警報 -->
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card performance-card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            最近活動
                        </h5>
                    </div>
                    <div class="card-body" id="recentActivity">
                        <!-- 動態載入最近活動 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 資源使用情況 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card performance-card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-microchip me-2"></i>
                            資源使用情況
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="real-time-data">
                                    <h6><i class="fas fa-memory me-2"></i>記憶體使用</h6>
                                    <div class="progress mb-2">
                                        <div class="progress-bar bg-info" id="memoryUsage" style="width: 0%"></div>
                                    </div>
                                    <small class="text-muted" id="memoryText">0 MB / 0 MB</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="real-time-data">
                                    <h6><i class="fas fa-hdd me-2"></i>磁碟使用</h6>
                                    <div class="progress mb-2">
                                        <div class="progress-bar bg-warning" id="diskUsage" style="width: 0%"></div>
                                    </div>
                                    <small class="text-muted" id="diskText">0 GB / 0 GB</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="real-time-data">
                                    <h6><i class="fas fa-network-wired me-2"></i>網路流量</h6>
                                    <div class="progress mb-2">
                                        <div class="progress-bar bg-success" id="networkUsage" style="width: 0%"></div>
                                    </div>
                                    <small class="text-muted" id="networkText">0 KB/s</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let performanceChart = null;
        let methodComparisonChart = null;
        let healthGauge = null;
        let refreshInterval = null;
        
        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            loadPerformanceData();
            startAutoRefresh();
        });
        
        // 初始化圖表
        function initializeCharts() {
            // 性能趨勢圖表
            const performanceCtx = document.getElementById('performanceChart').getContext('2d');
            performanceChart = new Chart(performanceCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '成功率',
                        data: [],
                        borderColor: '#007bff',
                        backgroundColor: '#007bff20',
                        tension: 0.4
                    }, {
                        label: '響應時間',
                        data: [],
                        borderColor: '#28a745',
                        backgroundColor: '#28a74520',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            max: 1,
                            ticks: {
                                callback: function(value) {
                                    return (value * 100).toFixed(0) + '%';
                                }
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            ticks: {
                                callback: function(value) {
                                    return value.toFixed(0) + 'ms';
                                }
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
            
            // 方法比較圖表
            const methodCtx = document.getElementById('methodComparisonChart').getContext('2d');
            methodComparisonChart = new Chart(methodCtx, {
                type: 'radar',
                data: {
                    labels: ['成功率', '穩定性', '響應速度', '資源使用', '可靠性'],
                    datasets: []
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 1
                        }
                    }
                }
            });
            
            // 健康度儀表
            initializeHealthGauge();
        }
        
        // 初始化健康度儀表
        function initializeHealthGauge() {
            const canvas = document.getElementById('healthGauge');
            const ctx = canvas.getContext('2d');
            
            function drawGauge(value) {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                const centerX = canvas.width / 2;
                const centerY = canvas.height - 10;
                const radius = 80;
                
                // 背景弧
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, Math.PI, 0);
                ctx.lineWidth = 15;
                ctx.strokeStyle = '#e9ecef';
                ctx.stroke();
                
                // 進度弧
                const angle = Math.PI * (value / 100);
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, Math.PI, Math.PI + angle);
                ctx.lineWidth = 15;
                
                // 根據值設置顏色
                if (value >= 80) {
                    ctx.strokeStyle = '#28a745';
                } else if (value >= 60) {
                    ctx.strokeStyle = '#ffc107';
                } else {
                    ctx.strokeStyle = '#dc3545';
                }
                ctx.stroke();
                
                // 指針
                const pointerAngle = Math.PI + angle;
                const pointerLength = radius - 10;
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.lineTo(
                    centerX + Math.cos(pointerAngle) * pointerLength,
                    centerY + Math.sin(pointerAngle) * pointerLength
                );
                ctx.lineWidth = 3;
                ctx.strokeStyle = '#343a40';
                ctx.stroke();
                
                // 中心點
                ctx.beginPath();
                ctx.arc(centerX, centerY, 5, 0, 2 * Math.PI);
                ctx.fillStyle = '#343a40';
                ctx.fill();
            }
            
            // 初始繪製
            drawGauge(95);
            
            // 保存繪製函數供後續使用
            window.drawHealthGauge = drawGauge;
        }
        
        // 載入性能數據
        async function loadPerformanceData() {
            try {
                // 載入系統狀態
                await loadSystemStatus();
                
                // 載入關鍵指標
                await loadKeyMetrics();
                
                // 載入圖表數據
                await loadChartData();
                
                // 載入警報和活動
                await loadAlertsAndActivity();
                
                // 載入資源使用情況
                await loadResourceUsage();
                
            } catch (error) {
                console.error('載入性能數據錯誤:', error);
            }
        }
        
        // 載入系統狀態
        async function loadSystemStatus() {
            // 模擬系統狀態數據
            const statusData = {
                database: { status: 'online', response_time: 15 },
                ml_models: { status: 'online', loaded_models: 8 },
                api_server: { status: 'online', requests_per_minute: 45 },
                prediction_engine: { status: 'online', queue_size: 3 }
            };
            
            let html = '';
            
            Object.entries(statusData).forEach(([service, data]) => {
                const statusClass = data.status === 'online' ? 'status-online' : 'status-offline';
                const statusText = data.status === 'online' ? '正常' : '離線';
                
                html += `
                    <div class="col-md-3 mb-3">
                        <div class="real-time-data text-center">
                            <h6>
                                <span class="status-indicator ${statusClass}"></span>
                                ${service.replace('_', ' ').toUpperCase()}
                            </h6>
                            <p class="mb-1">${statusText}</p>
                            ${data.response_time ? `<small class="text-muted">響應時間: ${data.response_time}ms</small>` : ''}
                            ${data.loaded_models ? `<small class="text-muted">已載入模型: ${data.loaded_models}</small>` : ''}
                            ${data.requests_per_minute ? `<small class="text-muted">請求/分鐘: ${data.requests_per_minute}</small>` : ''}
                            ${data.queue_size ? `<small class="text-muted">佇列大小: ${data.queue_size}</small>` : ''}
                        </div>
                    </div>
                `;
            });
            
            document.getElementById('systemStatusContent').innerHTML = html;
        }
        
        // 載入關鍵指標
        async function loadKeyMetrics() {
            // 模擬關鍵指標數據
            const metrics = {
                totalPredictions: 1247,
                avgSuccessRate: 78.5,
                activeModels: 8,
                systemUptime: 72
            };
            
            document.getElementById('totalPredictions').textContent = metrics.totalPredictions.toLocaleString();
            document.getElementById('avgSuccessRate').textContent = metrics.avgSuccessRate.toFixed(1) + '%';
            document.getElementById('activeModels').textContent = metrics.activeModels;
            document.getElementById('systemUptime').textContent = metrics.systemUptime + 'h';
            
            // 更新健康度
            const healthScore = Math.min(95, metrics.avgSuccessRate + 10);
            document.getElementById('healthScore').textContent = healthScore.toFixed(0) + '%';
            if (window.drawHealthGauge) {
                window.drawHealthGauge(healthScore);
            }
        }
        
        // 載入圖表數據
        async function loadChartData() {
            // 更新性能趨勢圖表
            const now = new Date();
            const labels = [];
            const successRateData = [];
            const responseTimeData = [];
            
            for (let i = 23; i >= 0; i--) {
                const time = new Date(now.getTime() - i * 60 * 60 * 1000);
                labels.push(time.getHours() + ':00');
                successRateData.push(Math.random() * 0.3 + 0.6);
                responseTimeData.push(Math.random() * 100 + 50);
            }
            
            performanceChart.data.labels = labels;
            performanceChart.data.datasets[0].data = successRateData;
            performanceChart.data.datasets[1].data = responseTimeData;
            performanceChart.update();
            
            // 更新方法比較圖表
            const methods = ['ML', 'Board Path', 'Integrated', 'Ensemble'];
            const colors = ['#007bff', '#28a745', '#ffc107', '#dc3545'];
            
            methodComparisonChart.data.datasets = methods.map((method, index) => ({
                label: method,
                data: [
                    Math.random() * 0.4 + 0.6, // 成功率
                    Math.random() * 0.3 + 0.7, // 穩定性
                    Math.random() * 0.4 + 0.6, // 響應速度
                    Math.random() * 0.3 + 0.7, // 資源使用
                    Math.random() * 0.2 + 0.8  // 可靠性
                ],
                borderColor: colors[index],
                backgroundColor: colors[index] + '20'
            }));
            
            methodComparisonChart.update();
            
            // 更新方法性能詳情
            updateMethodPerformanceDetails(methods);
        }
        
        // 更新方法性能詳情
        function updateMethodPerformanceDetails(methods) {
            let html = '<h6 class="mb-3">方法性能詳情</h6>';
            
            methods.forEach((method, index) => {
                const successRate = (Math.random() * 30 + 60).toFixed(1);
                const predictions = Math.floor(Math.random() * 100 + 50);
                const avgTime = (Math.random() * 50 + 100).toFixed(0);
                
                html += `
                    <div class="real-time-data mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">${method}</h6>
                            <span class="badge bg-primary">${successRate}%</span>
                        </div>
                        <div class="row text-center">
                            <div class="col-4">
                                <small class="text-muted">預測次數</small>
                                <div class="fw-bold">${predictions}</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">平均時間</small>
                                <div class="fw-bold">${avgTime}ms</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">狀態</small>
                                <div class="fw-bold text-success">正常</div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            document.getElementById('methodPerformanceDetails').innerHTML = html;
        }
        
        // 載入警報和活動
        async function loadAlertsAndActivity() {
            // 系統警報
            const alerts = [
                { type: 'success', message: '所有系統組件運行正常', time: '2分鐘前' },
                { type: 'warning', message: 'ML模型準確率略有下降', time: '15分鐘前' },
                { type: 'success', message: '成功完成模型更新', time: '1小時前' }
            ];
            
            let alertsHtml = '';
            alerts.forEach(alert => {
                alertsHtml += `
                    <div class="alert-item alert-${alert.type}">
                        <div class="d-flex justify-content-between">
                            <span>${alert.message}</span>
                            <small class="text-muted">${alert.time}</small>
                        </div>
                    </div>
                `;
            });
            
            document.getElementById('systemAlerts').innerHTML = alertsHtml;
            
            // 最近活動
            const activities = [
                { action: '執行威力彩預測', user: '系統', time: '剛剛' },
                { action: '更新ML模型', user: '管理員', time: '5分鐘前' },
                { action: '分析歷史數據', user: '系統', time: '10分鐘前' },
                { action: '生成性能報告', user: '系統', time: '30分鐘前' }
            ];
            
            let activityHtml = '';
            activities.forEach(activity => {
                activityHtml += `
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <div class="fw-bold">${activity.action}</div>
                            <small class="text-muted">由 ${activity.user} 執行</small>
                        </div>
                        <small class="text-muted">${activity.time}</small>
                    </div>
                `;
            });
            
            document.getElementById('recentActivity').innerHTML = activityHtml;
        }
        
        // 載入資源使用情況
        async function loadResourceUsage() {
            // 模擬資源使用數據
            const resources = {
                memory: { used: 2.4, total: 8.0, percentage: 30 },
                disk: { used: 45.2, total: 100.0, percentage: 45 },
                network: { speed: 125.6, percentage: 25 }
            };
            
            // 更新記憶體使用
            document.getElementById('memoryUsage').style.width = resources.memory.percentage + '%';
            document.getElementById('memoryText').textContent = 
                `${resources.memory.used.toFixed(1)} GB / ${resources.memory.total.toFixed(1)} GB`;
            
            // 更新磁碟使用
            document.getElementById('diskUsage').style.width = resources.disk.percentage + '%';
            document.getElementById('diskText').textContent = 
                `${resources.disk.used.toFixed(1)} GB / ${resources.disk.total.toFixed(1)} GB`;
            
            // 更新網路流量
            document.getElementById('networkUsage').style.width = resources.network.percentage + '%';
            document.getElementById('networkText').textContent = 
                `${resources.network.speed.toFixed(1)} KB/s`;
        }
        
        // 刷新系統狀態
        async function refreshSystemStatus() {
            const button = event.target.closest('button');
            const icon = button.querySelector('i');
            
            // 添加旋轉動畫
            icon.classList.add('fa-spin');
            button.disabled = true;
            
            try {
                await loadPerformanceData();
            } finally {
                // 移除旋轉動畫
                setTimeout(() => {
                    icon.classList.remove('fa-spin');
                    button.disabled = false;
                }, 1000);
            }
        }
        
        // 開始自動刷新
        function startAutoRefresh() {
            refreshInterval = setInterval(() => {
                loadPerformanceData();
            }, 30000); // 每30秒刷新一次
        }
        
        // 停止自動刷新
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
        }
        
        // 頁面卸載時清理
        window.addEventListener('beforeunload', function() {
            stopAutoRefresh();
        });
    </script>
</body>
</html>