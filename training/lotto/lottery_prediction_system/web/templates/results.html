<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>開獎結果查詢 - 彩票預測系統</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/auto-refresh.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/data-export.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/charts.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/mobile-responsive.css') }}" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft JhengHei', sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1200px;
            padding: 30px;
        }
        
        .header-section {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 15px;
            color: white;
        }
        
        .lottery-tabs {
            margin-bottom: 30px;
        }
        
        .lottery-tabs .nav-link {
            border-radius: 25px;
            margin: 0 5px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .lottery-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
        }
        
        .search-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .number-ball {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 3px;
            font-weight: bold;
            color: white;
            font-size: 16px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .number-ball.normal {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
        }
        
        .number-ball.special {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }
        
        .result-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .result-card:hover {
            transform: translateY(-5px);
        }
        
        .period-badge {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 15px;
        }
        
        .date-info {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 40px;
        }
        
        .no-results {
            text-align: center;
            padding: 60px;
            color: #6c757d;
        }
        
        .pagination-wrapper {
            display: flex;
            justify-content: center;
            margin-top: 30px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 600;
        }
        
        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 600;
        }
        
        .btn-outline-primary:hover {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-color: #667eea;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            display: block;
        }
        
        .stats-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .number-ball.matched {
            background: linear-gradient(135deg, #28a745, #20c997);
            animation: pulse 1.5s infinite;
        }
        
        .comparison-header {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .prediction-section, .actual-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
        }
        
        .comparison-result {
            background: white;
            border-radius: 10px;
            padding: 15px;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- 標題區域 -->
            <div class="header-section">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="fas fa-trophy"></i> 開獎結果查詢</h1>
                        <p class="mb-0">查看歷史開獎號碼和統計資訊</p>
                    </div>
                    <div>
                        <a href="/" class="btn btn-outline-light">
                            <i class="fas fa-home"></i> 回首頁
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 導航選項卡 -->
            <ul class="nav nav-pills lottery-tabs justify-content-center" id="lotteryTabs">
                <li class="nav-item">
                    <a class="nav-link active" data-lottery="powercolor" href="#">
                        <i class="fas fa-star"></i> 威力彩
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-lottery="lotto649" href="#">
                        <i class="fas fa-gem"></i> 大樂透
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-lottery="dailycash" href="#">
                        <i class="fas fa-coins"></i> 今彩539
                    </a>
                </li>
            </ul>
            
            <!-- 搜尋區域 -->
            <div class="search-section">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">期號搜尋</label>
                        <input type="number" class="form-control" id="periodSearch" placeholder="輸入期號">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">開始日期</label>
                        <input type="date" class="form-control" id="startDate">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">結束日期</label>
                        <input type="date" class="form-control" id="endDate">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">顯示筆數</label>
                        <select class="form-control" id="limitSelect">
                            <option value="10">10筆</option>
                            <option value="20" selected>20筆</option>
                            <option value="50">50筆</option>
                            <option value="100">100筆</option>
                        </select>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12 text-center">
                        <button class="btn btn-primary me-2" onclick="searchResults()">
                            <i class="fas fa-search"></i> 搜尋
                        </button>
                        <button class="btn btn-outline-primary" onclick="resetSearch()">
                            <i class="fas fa-refresh"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 統計資訊 -->
            <div class="row" id="statsSection" style="display: none;">
                <div class="col-md-3">
                    <div class="stats-card">
                        <span class="stats-number" id="totalDraws">0</span>
                        <span class="stats-label">總開獎次數</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <span class="stats-number" id="dateRange">-</span>
                        <span class="stats-label">日期範圍</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <span class="stats-number" id="mostFrequentNumber">-</span>
                        <span class="stats-label">最常出現號碼</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <span class="stats-number" id="leastFrequentNumber">-</span>
                        <span class="stats-label">最少出現號碼</span>
                    </div>
                </div>
            </div>
            
            <!-- 載入中 -->
            <div class="loading-spinner" id="loadingSpinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">載入中...</span>
                </div>
                <p class="mt-3">正在載入開獎結果...</p>
            </div>
            
            <!-- 結果區域 -->
            <div id="resultsContainer">
                <!-- 結果將在這裡動態載入 -->
            </div>
            
            <!-- 無結果 -->
            <div class="no-results" id="noResults" style="display: none;">
                <i class="fas fa-search fa-3x mb-3"></i>
                <h4>沒有找到符合條件的開獎結果</h4>
                <p>請調整搜尋條件後重試</p>
            </div>
            
            <!-- 分頁 -->
            <div class="pagination-wrapper" id="paginationWrapper" style="display: none;">
                <nav>
                    <ul class="pagination" id="pagination">
                        <!-- 分頁將在這裡動態生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 預測比對結果模態框 -->
    <div class="modal fade" id="comparisonModal" tabindex="-1" aria-labelledby="comparisonModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="comparisonModalLabel">
                        <i class="fas fa-balance-scale"></i> 預測比對結果
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="comparisonModalBody">
                    <!-- 比對結果內容將在這裡動態載入 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> 關閉
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentLotteryType = 'powercolor';
        let currentPage = 1;
        let totalPages = 1;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            loadResults();
        });
        
        function initializePage() {
            // 設置日期範圍（最近30天）
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 30);
            
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            
            // 綁定選項卡事件
            document.querySelectorAll('.lottery-tabs .nav-link').forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 更新選項卡狀態
                    document.querySelectorAll('.lottery-tabs .nav-link').forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 更新當前彩票類型
                    currentLotteryType = this.getAttribute('data-lottery');
                    currentPage = 1;
                    
                    // 重新載入結果
                    loadResults();
                });
            });
        }
        
        function loadResults() {
            console.log('loadResults 被調用，彩票類型:', currentLotteryType, '頁碼:', currentPage);
            showLoading();
            
            const params = new URLSearchParams({
                lottery_type: currentLotteryType,
                page: currentPage,
                limit: document.getElementById('limitSelect').value
            });
            
            // 添加搜尋條件
            const periodSearch = document.getElementById('periodSearch').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            if (periodSearch) params.append('period', periodSearch);
            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);
            
            const url = `/api/results?${params.toString()}`;
            console.log('發送請求到:', url);
            
            // 返回 Promise 以便圖表模組可以鏈式調用
            return fetch(url)
                .then(response => {
                    console.log('收到響應，狀態:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('收到數據:', data);
                    hideLoading();
                    
                    if (data.success && data.data) {
                        displayResults(data.data.results);
                        updateStatistics(data.data.statistics);
                        updatePagination(data.data.pagination);
                        return data; // 返回數據供圖表使用
                    } else {
                        showError(data.error || '載入開獎結果失敗');
                        throw new Error(data.error || '載入開獎結果失敗');
                    }
                })
                .catch(error => {
                    console.error('請求錯誤:', error);
                    hideLoading();
                    showError('網路錯誤，請稍後重試');
                    throw error; // 重新拋出錯誤以便圖表模組處理
                });
        }
        
        function displayResults(results) {
            const container = document.getElementById('resultsContainer');
            
            if (!results || results.length === 0) {
                container.innerHTML = '';
                document.getElementById('noResults').style.display = 'block';
                document.getElementById('statsSection').style.display = 'none';
                return;
            }
            
            document.getElementById('noResults').style.display = 'none';
            document.getElementById('statsSection').style.display = 'flex';
            
            let html = '';
            
            results.forEach(result => {
                html += `
                    <div class="result-card">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="period-badge">第 ${result.period} 期</div>
                                <div class="date-info">
                                    <i class="fas fa-calendar"></i> 開獎日期：${formatDate(result.date)}
                                </div>
                                <div class="numbers-display">
                                    ${renderNumbers(result.numbers, currentLotteryType)}
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <button class="btn btn-outline-primary btn-sm me-2" onclick="viewDetails('${result.period}')">
                                    <i class="fas fa-eye"></i> 詳細資訊
                                </button>
                                <button class="btn btn-success btn-sm" onclick="comparePrediction('${result.period}')">
                                    <i class="fas fa-balance-scale"></i> 比對預測
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        function renderNumbers(numbers, lotteryType) {
            let html = '';
            
            if (lotteryType === 'powercolor') {
                // 威力彩：第一區6個號碼 + 第二區1個號碼
                const firstArea = numbers.first_area || [];
                const secondArea = numbers.second_area || 0;
                
                html += '<div class="mb-2"><strong>第一區：</strong></div>';
                firstArea.forEach(num => {
                    html += `<div class="number-ball normal">${num}</div>`;
                });
                
                html += '<div class="mt-3 mb-2"><strong>第二區：</strong></div>';
                html += `<div class="number-ball special">${secondArea}</div>`;
                
            } else if (lotteryType === 'lotto649') {
                // 大樂透：一般號碼6個 + 特別號1個
                const mainNumbers = numbers.main_numbers || [];
                const specialNumber = numbers.special_number || 0;
                
                html += '<div class="mb-2"><strong>一般號碼：</strong></div>';
                mainNumbers.forEach(num => {
                    html += `<div class="number-ball normal">${num}</div>`;
                });
                
                html += '<div class="mt-3 mb-2"><strong>特別號：</strong></div>';
                html += `<div class="number-ball special">${specialNumber}</div>`;
                
            } else if (lotteryType === 'dailycash') {
                // 今彩539：5個號碼
                const nums = numbers.numbers || [];
                
                html += '<div class="mb-2"><strong>開獎號碼：</strong></div>';
                nums.forEach(num => {
                    html += `<div class="number-ball normal">${num}</div>`;
                });
            }
            
            return html;
        }
        
        function updateStatistics(stats) {
            if (!stats) return;
            
            document.getElementById('totalDraws').textContent = stats.total_draws || 0;
            document.getElementById('dateRange').textContent = stats.date_range || '-';
            document.getElementById('mostFrequentNumber').textContent = stats.most_frequent_number || '-';
            document.getElementById('leastFrequentNumber').textContent = stats.least_frequent_number || '-';
        }
        
        function updatePagination(pagination) {
            if (!pagination) return;
            
            totalPages = pagination.total_pages || 1;
            currentPage = pagination.current_page || 1;
            
            if (totalPages <= 1) {
                document.getElementById('paginationWrapper').style.display = 'none';
                return;
            }
            
            const paginationContainer = document.getElementById('pagination');
            let html = '';
            
            // 上一頁
            html += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            `;
            
            // 頁碼
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                html += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                    </li>
                `;
            }
            
            // 下一頁
            html += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            `;
            
            paginationContainer.innerHTML = html;
            document.getElementById('paginationWrapper').style.display = 'block';
        }
        
        function searchResults() {
            console.log('搜尋按鈕被點擊');
            currentPage = 1;
            loadResults();
        }
        
        function resetSearch() {
            document.getElementById('periodSearch').value = '';
            
            // 重置日期為最近30天
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 30);
            
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('limitSelect').value = '20';
            
            currentPage = 1;
            loadResults();
        }
        
        function changePage(page) {
            if (page >= 1 && page <= totalPages && page !== currentPage) {
                currentPage = page;
                loadResults();
            }
        }
        
        function viewDetails(period) {
            // 跳轉到詳細頁面或顯示詳細資訊
            window.open(`/period_detail?lottery_type=${currentLotteryType}&period=${period}`, '_blank');
        }
        
        async function comparePrediction(period) {
            try {
                showLoading();
                
                const response = await fetch(`/api/compare_prediction/${currentLotteryType}/${period}`);
                const data = await response.json();
                
                hideLoading();
                
                if (data.success) {
                    displayComparisonResult(data.data);
                } else {
                    alert(`比對失敗：${data.error}`);
                }
                
            } catch (error) {
                hideLoading();
                alert(`比對時發生錯誤：${error.message}`);
            }
        }
        
        function displayComparisonResult(data) {
            const modal = document.getElementById('comparisonModal');
            const modalBody = document.getElementById('comparisonModalBody');
            
            let html = `
                <div class="comparison-header">
                    <h5><i class="fas fa-trophy"></i> ${data.lottery_name} 第 ${data.period} 期 預測比對結果</h5>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="prediction-section">
                            <h6><i class="fas fa-brain"></i> 預測號碼</h6>
                            <div class="numbers-display">
                                ${renderComparisonNumbers(data.comparison.prediction_numbers, 'prediction')}
                            </div>
            `;
            
            if (data.comparison.prediction_special !== undefined) {
                html += `
                            <div class="mt-2">
                                <strong>特別號：</strong>
                                <div class="number-ball special">${data.comparison.prediction_special}</div>
                            </div>
                `;
            }
            
            html += `
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="actual-section">
                            <h6><i class="fas fa-star"></i> 實際開獎</h6>
                            <div class="numbers-display">
                                ${renderComparisonNumbers(data.comparison.actual_numbers, 'actual')}
                            </div>
            `;
            
            if (data.comparison.actual_special !== undefined) {
                html += `
                            <div class="mt-2">
                                <strong>特別號：</strong>
                                <div class="number-ball ${data.comparison.special_matched ? 'matched' : 'special'}">${data.comparison.actual_special}</div>
                            </div>
                `;
            }
            
            html += `
                        </div>
                    </div>
                </div>
                
                <div class="comparison-result mt-4">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-chart-bar"></i> 比對結果</h6>
                        <p><strong>命中號碼：</strong> ${data.comparison.matched_numbers} / ${data.comparison.total_numbers}</p>
            `;
            
            if (data.comparison.special_matched !== undefined) {
                html += `<p><strong>特別號：</strong> ${data.comparison.special_matched ? '✅ 命中' : '❌ 未中'}</p>`;
            }
            
            // 計算命中率
            const hitRate = (data.comparison.matched_numbers / data.comparison.total_numbers * 100).toFixed(1);
            html += `
                        <p><strong>命中率：</strong> ${hitRate}%</p>
                    </div>
                </div>
            `;
            
            modalBody.innerHTML = html;
            
            // 顯示模態框
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        }
        
        function renderComparisonNumbers(numbers, type) {
            let html = '';
            const predictionNumbers = type === 'prediction' ? numbers : [];
            const actualNumbers = type === 'actual' ? numbers : [];
            
            numbers.forEach(num => {
                let className = 'number-ball normal';
                
                if (type === 'actual') {
                    // 檢查是否在預測號碼中
                    const comparisonData = document.getElementById('comparisonModalBody').dataset;
                    if (comparisonData.predictionNumbers && comparisonData.predictionNumbers.includes(num)) {
                        className = 'number-ball matched';
                    }
                }
                
                html += `<div class="${className}">${num}</div>`;
            });
            
            return html;
        }
        
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-TW', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                weekday: 'short'
            });
        }
        
        function showLoading() {
            document.getElementById('loadingSpinner').style.display = 'block';
            document.getElementById('resultsContainer').style.display = 'none';
            document.getElementById('noResults').style.display = 'none';
        }
        
        function hideLoading() {
            document.getElementById('loadingSpinner').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'block';
        }
        
        function showError(message) {
            hideLoading();
            document.getElementById('resultsContainer').innerHTML = `
                <div class="alert alert-danger text-center">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>錯誤：</strong> ${message}
                </div>
            `;
        }
    </script>
    
    <!-- 自動刷新功能 -->
    <script src="{{ url_for('static', filename='js/auto-refresh.js') }}"></script>
    <!-- 數據匯出功能 -->
    <script src="{{ url_for('static', filename='js/data-export.js') }}"></script>
    <!-- 圖表視覺化功能 -->
    <script src="{{ url_for('static', filename='js/charts.js') }}"></script>
</body>
</html>