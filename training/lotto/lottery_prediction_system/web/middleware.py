#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中間件模組
提供錯誤處理、請求驗證、性能監控等中間件功能
"""

import time
import logging
import traceback
from functools import wraps
from typing import Dict, Any, Optional, Callable, Union
from datetime import datetime, timedelta
from collections import defaultdict, deque

from flask import request, jsonify, g

# 嘗試導入 redis，如果失敗則使用 None
try:
    import redis
    HAS_REDIS = True
except ImportError:
    redis = None
    HAS_REDIS = False
    print("警告: redis 模組未安裝，將使用內存緩存替代")


class APIResponse:
    """標準化API響應格式"""
    
    def __init__(self, success: bool, data: Optional[Dict[str, Any]] = None, 
                 error: Optional[str] = None, message: Optional[str] = None,
                 code: Optional[str] = None, timestamp: Optional[str] = None):
        self.success = success
        self.data = data
        self.error = error
        self.message = message
        self.code = code
        self.timestamp = timestamp or datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        result = {
            'success': self.success,
            'timestamp': self.timestamp
        }
        
        if self.data is not None:
            result['data'] = self.data
        if self.error is not None:
            result['error'] = self.error
        if self.message is not None:
            result['message'] = self.message
        if self.code is not None:
            result['code'] = self.code
            
        return result
    
    @classmethod
    def success_response(cls, data: Optional[Dict[str, Any]] = None, 
                        message: Optional[str] = None) -> 'APIResponse':
        """創建成功響應"""
        return cls(success=True, data=data, message=message)
    
    @classmethod
    def error_response(cls, error: str, code: Optional[str] = None, 
                      data: Optional[Dict[str, Any]] = None) -> 'APIResponse':
        """創建錯誤響應"""
        return cls(success=False, error=error, code=code, data=data)


class RateLimiter:
    """速率限制器"""
    
    def __init__(self, redis_client: Optional[Any] = None):
        # 如果有 redis 且提供了 client，則使用 redis，否則使用內存存儲
        self.redis_client = redis_client if HAS_REDIS and redis_client else None
        self.memory_store = defaultdict(deque)  # 內存存儲作為備選
        self.logger = logging.getLogger(__name__)
    
    def is_allowed(self, key: str, limit: int, window_seconds: int) -> tuple[bool, Dict[str, Any]]:
        """檢查是否允許請求"""
        now = time.time()
        
        if self.redis_client:
            return self._redis_rate_limit(key, limit, window_seconds, now)
        else:
            return self._memory_rate_limit(key, limit, window_seconds, now)
    
    def _redis_rate_limit(self, key: str, limit: int, window_seconds: int, now: float) -> tuple[bool, Dict[str, Any]]:
        """使用Redis進行速率限制"""
        try:
            pipe = self.redis_client.pipeline()
            pipe.zremrangebyscore(key, 0, now - window_seconds)
            pipe.zcard(key)
            pipe.zadd(key, {str(now): now})
            pipe.expire(key, window_seconds)
            results = pipe.execute()
            
            current_requests = results[1]
            
            info = {
                'limit': limit,
                'remaining': max(0, limit - current_requests - 1),
                'reset_time': int(now + window_seconds),
                'window_seconds': window_seconds
            }
            
            return current_requests < limit, info
            
        except Exception as e:
            self.logger.error(f"Redis速率限制錯誤: {e}")
            # 降級到內存存儲
            return self._memory_rate_limit(key, limit, window_seconds, now)
    
    def _memory_rate_limit(self, key: str, limit: int, window_seconds: int, now: float) -> tuple[bool, Dict[str, Any]]:
        """使用內存進行速率限制"""
        requests = self.memory_store[key]
        
        # 清理過期請求
        while requests and requests[0] <= now - window_seconds:
            requests.popleft()
        
        info = {
            'limit': limit,
            'remaining': max(0, limit - len(requests) - 1),
            'reset_time': int(now + window_seconds),
            'window_seconds': window_seconds
        }
        
        if len(requests) < limit:
            requests.append(now)
            return True, info
        else:
            return False, info


class PerformanceMonitor:
    """性能監控器"""
    
    def __init__(self):
        self.metrics = defaultdict(list)
        self.logger = logging.getLogger(__name__)
    
    def record_request(self, endpoint: str, method: str, duration: float, 
                      status_code: int, error: Optional[str] = None):
        """記錄請求指標"""
        metric = {
            'timestamp': datetime.now().isoformat(),
            'endpoint': endpoint,
            'method': method,
            'duration': duration,
            'status_code': status_code,
            'error': error
        }
        
        self.metrics[endpoint].append(metric)
        
        # 保持最近1000條記錄
        if len(self.metrics[endpoint]) > 1000:
            self.metrics[endpoint] = self.metrics[endpoint][-1000:]
        
        # 記錄慢請求
        if duration > 5.0:  # 5秒以上的請求
            self.logger.warning(f"慢請求: {method} {endpoint} - {duration:.2f}s")
    
    def get_stats(self, endpoint: Optional[str] = None) -> Dict[str, Any]:
        """獲取統計信息"""
        if endpoint:
            metrics = self.metrics.get(endpoint, [])
        else:
            metrics = []
            for endpoint_metrics in self.metrics.values():
                metrics.extend(endpoint_metrics)
        
        if not metrics:
            return {'total_requests': 0}
        
        durations = [m['duration'] for m in metrics]
        status_codes = [m['status_code'] for m in metrics]
        errors = [m for m in metrics if m['error']]
        
        return {
            'total_requests': len(metrics),
            'avg_duration': sum(durations) / len(durations),
            'max_duration': max(durations),
            'min_duration': min(durations),
            'error_rate': len(errors) / len(metrics),
            'status_code_distribution': {str(code): status_codes.count(code) for code in set(status_codes)}
        }


class ErrorHandler:
    """統一錯誤處理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def handle_validation_error(self, error: Exception) -> APIResponse:
        """處理驗證錯誤"""
        self.logger.warning(f"驗證錯誤: {str(error)}")
        return APIResponse.error_response(
            error=f"參數驗證失敗: {str(error)}",
            code="VALIDATION_ERROR"
        )
    
    def handle_business_error(self, error: Exception) -> APIResponse:
        """處理業務邏輯錯誤"""
        self.logger.error(f"業務邏輯錯誤: {str(error)}")
        return APIResponse.error_response(
            error=str(error),
            code="BUSINESS_ERROR"
        )
    
    def handle_system_error(self, error: Exception) -> APIResponse:
        """處理系統錯誤"""
        self.logger.error(f"系統錯誤: {str(error)}", exc_info=True)
        return APIResponse.error_response(
            error="內部服務器錯誤",
            code="SYSTEM_ERROR"
        )
    
    def handle_rate_limit_error(self, info: Dict[str, Any]) -> APIResponse:
        """處理速率限制錯誤"""
        return APIResponse.error_response(
            error="請求過於頻繁，請稍後再試",
            code="RATE_LIMIT_EXCEEDED",
            data=info
        )


# 全局實例
rate_limiter = RateLimiter()
performance_monitor = PerformanceMonitor()
error_handler = ErrorHandler()


# 裝飾器
def handle_api_errors(func: Callable) -> Callable:
    """API錯誤處理裝飾器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            
            # 如果返回的是APIResponse對象，轉換為JSON響應
            if isinstance(result, APIResponse):
                return jsonify(result.to_dict())
            
            return result
            
        except ValueError as e:
            response = error_handler.handle_validation_error(e)
            return jsonify(response.to_dict()), 400
        
        except Exception as e:
            response = error_handler.handle_system_error(e)
            return jsonify(response.to_dict()), 500
    
    return wrapper


def rate_limit(per_minute: int = 60, per_hour: int = 1000):
    """速率限制裝飾器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 獲取客戶端IP
            client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            
            # 檢查分鐘級限制
            minute_key = f"rate_limit:minute:{client_ip}:{func.__name__}"
            allowed, info = rate_limiter.is_allowed(minute_key, per_minute, 60)
            
            if not allowed:
                response = error_handler.handle_rate_limit_error(info)
                return jsonify(response.to_dict()), 429
            
            # 檢查小時級限制
            hour_key = f"rate_limit:hour:{client_ip}:{func.__name__}"
            allowed, info = rate_limiter.is_allowed(hour_key, per_hour, 3600)
            
            if not allowed:
                response = error_handler.handle_rate_limit_error(info)
                return jsonify(response.to_dict()), 429
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


def monitor_performance(func: Callable) -> Callable:
    """性能監控裝飾器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        error = None
        status_code = 200
        
        try:
            result = func(*args, **kwargs)
            
            # 嘗試從響應中獲取狀態碼
            if hasattr(result, 'status_code'):
                status_code = result.status_code
            elif isinstance(result, tuple) and len(result) > 1:
                status_code = result[1]
            
            return result
            
        except Exception as e:
            error = str(e)
            status_code = 500
            raise
        
        finally:
            duration = time.time() - start_time
            endpoint = request.endpoint or 'unknown'
            method = request.method
            
            performance_monitor.record_request(
                endpoint=endpoint,
                method=method,
                duration=duration,
                status_code=status_code,
                error=error
            )
    
    return wrapper


def validate_lottery_type(supported_types: list = None):
    """彩票類型驗證裝飾器"""
    if supported_types is None:
        supported_types = ['powercolor', 'lotto649', 'dailycash']
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 從不同來源獲取lottery_type
            lottery_type = (
                kwargs.get('lottery_type') or 
                request.args.get('type') or 
                request.args.get('lottery_type') or
                (request.json.get('lottery_type') if request.json else None)
            )
            
            if lottery_type and lottery_type not in supported_types:
                response = APIResponse.error_response(
                    error=f"不支援的彩票類型: {lottery_type}",
                    code="INVALID_LOTTERY_TYPE"
                )
                return jsonify(response.to_dict()), 400
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


def validate_request_data(required_fields: list = None, optional_fields: list = None):
    """請求數據驗證裝飾器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            if request.method in ['POST', 'PUT', 'PATCH']:
                if not request.json:
                    response = APIResponse.error_response(
                        error="請求體必須是JSON格式",
                        code="INVALID_JSON"
                    )
                    return jsonify(response.to_dict()), 400
                
                # 檢查必需字段
                if required_fields:
                    missing_fields = []
                    for field in required_fields:
                        if field not in request.json:
                            missing_fields.append(field)
                    
                    if missing_fields:
                        response = APIResponse.error_response(
                            error=f"缺少必需字段: {', '.join(missing_fields)}",
                            code="MISSING_REQUIRED_FIELDS"
                        )
                        return jsonify(response.to_dict()), 400
                
                # 檢查未知字段
                if optional_fields is not None:
                    allowed_fields = set((required_fields or []) + optional_fields)
                    unknown_fields = set(request.json.keys()) - allowed_fields
                    
                    if unknown_fields:
                        response = APIResponse.error_response(
                            error=f"未知字段: {', '.join(unknown_fields)}",
                            code="UNKNOWN_FIELDS"
                        )
                        return jsonify(response.to_dict()), 400
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


def log_request_response(func: Callable) -> Callable:
    """請求響應日誌裝飾器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(__name__)
        
        # 記錄請求
        logger.info(f"請求: {request.method} {request.path} - IP: {request.remote_addr}")
        
        if request.json:
            # 過濾敏感信息
            safe_data = {k: v for k, v in request.json.items() if 'password' not in k.lower()}
            logger.debug(f"請求數據: {safe_data}")
        
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            logger.info(f"響應: {request.path} - 耗時: {duration:.3f}s")
            return result
        
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"錯誤: {request.path} - 耗時: {duration:.3f}s - 錯誤: {str(e)}")
            raise
    
    return wrapper