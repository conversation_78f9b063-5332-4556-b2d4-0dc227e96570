#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
威力彩歷史資料查詢功能
提供威力彩歷史開獎資料的查詢、統計分析和可視化功能
"""

import os
import sys
from flask import Flask, render_template, request, jsonify
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import json

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.db_manager import DBManager
from config.config_manager import ConfigManager

class PowercolorHistoryManager:
    """威力彩歷史資料管理器"""
    
    def __init__(self, config_manager=None):
        self.config_manager = config_manager or ConfigManager()
        self.db_manager = DBManager(config_manager=self.config_manager)
    
    def get_history_data(self, start_date=None, end_date=None, limit=None, period_range=None):
        """
        獲取威力彩歷史資料
        
        Args:
            start_date: 開始日期 (YYYY-MM-DD)
            end_date: 結束日期 (YYYY-MM-DD)
            limit: 限制筆數
            period_range: 期數範圍 [start_period, end_period]
            
        Returns:
            DataFrame: 威力彩歷史資料
        """
        try:
            # 載入所有威力彩資料
            df = self.db_manager.load_lottery_data('powercolor')
            
            if df.empty:
                return df
            
            # 按日期篩選
            if start_date:
                df = df[df['Sdate'] >= pd.to_datetime(start_date)]
            if end_date:
                df = df[df['Sdate'] <= pd.to_datetime(end_date)]
            
            # 按期數範圍篩選
            if period_range and len(period_range) == 2:
                df = df[(df['Period'] >= period_range[0]) & (df['Period'] <= period_range[1])]
            
            # 限制筆數
            if limit:
                df = df.tail(limit)
            
            # 按期數降序排列（最新的在前面）
            df = df.sort_values('Period', ascending=False)
            
            return df
            
        except Exception as e:
            print(f"獲取歷史資料錯誤: {str(e)}")
            return pd.DataFrame()
    
    def get_number_statistics(self, df=None, analysis_period=100):
        """
        獲取號碼統計分析
        
        Args:
            df: 資料框，如果為None則載入最近的資料
            analysis_period: 分析期數
            
        Returns:
            dict: 統計分析結果
        """
        try:
            if df is None or df.empty:
                df = self.get_history_data(limit=analysis_period)
            
            if df.empty:
                return {}
            
            # 第一區號碼統計
            first_area_stats = {}
            for i in range(1, 39):  # 威力彩第一區 1-38
                count = 0
                for col in ['Anumber1', 'Anumber2', 'Anumber3', 'Anumber4', 'Anumber5', 'Anumber6']:
                    count += (df[col] == i).sum()
                first_area_stats[i] = count
            
            # 第二區號碼統計
            second_area_stats = {}
            for i in range(1, 9):  # 威力彩第二區 1-8
                count = (df['Second_district'] == i).sum()
                second_area_stats[i] = count
            
            # 計算出現頻率
            total_draws = len(df)
            first_area_freq = {k: v/total_draws for k, v in first_area_stats.items()}
            second_area_freq = {k: v/total_draws for k, v in second_area_stats.items()}
            
            # 找出熱門和冷門號碼
            sorted_first = sorted(first_area_stats.items(), key=lambda x: x[1], reverse=True)
            sorted_second = sorted(second_area_stats.items(), key=lambda x: x[1], reverse=True)
            
            hot_numbers_first = [num for num, count in sorted_first[:10]]  # 前10個熱門
            cold_numbers_first = [num for num, count in sorted_first[-10:]]  # 後10個冷門
            
            hot_numbers_second = [num for num, count in sorted_second[:3]]  # 前3個熱門
            cold_numbers_second = [num for num, count in sorted_second[-3:]]  # 後3個冷門
            
            return {
                'analysis_period': total_draws,
                'first_area': {
                    'statistics': first_area_stats,
                    'frequency': first_area_freq,
                    'hot_numbers': hot_numbers_first,
                    'cold_numbers': cold_numbers_first
                },
                'second_area': {
                    'statistics': second_area_stats,
                    'frequency': second_area_freq,
                    'hot_numbers': hot_numbers_second,
                    'cold_numbers': cold_numbers_second
                }
            }
            
        except Exception as e:
            print(f"統計分析錯誤: {str(e)}")
            return {}
    
    def get_pattern_analysis(self, df=None, analysis_period=50):
        """
        獲取開獎模式分析
        
        Args:
            df: 資料框
            analysis_period: 分析期數
            
        Returns:
            dict: 模式分析結果
        """
        try:
            if df is None or df.empty:
                df = self.get_history_data(limit=analysis_period)
            
            if df.empty:
                return {}
            
            patterns = {
                'odd_even_ratio': [],  # 奇偶比例
                'sum_range': [],       # 號碼總和範圍
                'consecutive_count': [], # 連號數量
                'span_range': []       # 號碼跨度
            }
            
            for _, row in df.iterrows():
                numbers = [row[f'Anumber{i}'] for i in range(1, 7)]
                numbers = [int(n) for n in numbers if pd.notna(n)]
                
                if len(numbers) == 6:
                    # 奇偶比例
                    odd_count = sum(1 for n in numbers if n % 2 == 1)
                    patterns['odd_even_ratio'].append(f"{odd_count}:{6-odd_count}")
                    
                    # 號碼總和
                    total_sum = sum(numbers)
                    patterns['sum_range'].append(total_sum)
                    
                    # 連號數量
                    sorted_numbers = sorted(numbers)
                    consecutive = 0
                    for i in range(len(sorted_numbers)-1):
                        if sorted_numbers[i+1] - sorted_numbers[i] == 1:
                            consecutive += 1
                    patterns['consecutive_count'].append(consecutive)
                    
                    # 號碼跨度
                    span = max(numbers) - min(numbers)
                    patterns['span_range'].append(span)
            
            # 統計分析
            analysis = {}
            
            # 奇偶比例統計
            odd_even_stats = {}
            for ratio in patterns['odd_even_ratio']:
                odd_even_stats[ratio] = odd_even_stats.get(ratio, 0) + 1
            analysis['odd_even_distribution'] = odd_even_stats
            
            # 總和範圍統計
            if patterns['sum_range']:
                analysis['sum_statistics'] = {
                    'min': min(patterns['sum_range']),
                    'max': max(patterns['sum_range']),
                    'avg': round(np.mean(patterns['sum_range']), 2),
                    'median': np.median(patterns['sum_range'])
                }
            
            # 連號統計
            consecutive_stats = {}
            for count in patterns['consecutive_count']:
                consecutive_stats[count] = consecutive_stats.get(count, 0) + 1
            analysis['consecutive_distribution'] = consecutive_stats
            
            # 跨度統計
            if patterns['span_range']:
                analysis['span_statistics'] = {
                    'min': min(patterns['span_range']),
                    'max': max(patterns['span_range']),
                    'avg': round(np.mean(patterns['span_range']), 2),
                    'median': np.median(patterns['span_range'])
                }
            
            return analysis
            
        except Exception as e:
            print(f"模式分析錯誤: {str(e)}")
            return {}
    
    def get_recent_trends(self, periods=20):
        """
        獲取最近開獎趨勢
        
        Args:
            periods: 分析期數
            
        Returns:
            dict: 趨勢分析結果
        """
        try:
            df = self.get_history_data(limit=periods)
            
            if df.empty:
                return {}
            
            # 按時間順序排列
            df = df.sort_values('Period', ascending=True)
            
            trends = {
                'recent_numbers': [],
                'missing_numbers_first': [],
                'missing_numbers_second': [],
                'hot_streak': {},
                'cold_streak': {}
            }
            
            # 最近開出的號碼
            for _, row in df.tail(5).iterrows():
                period_numbers = {
                    'period': int(row['Period']),
                    'date': row['Sdate'].strftime('%Y-%m-%d'),
                    'first_area': [int(row[f'Anumber{i}']) for i in range(1, 7)],
                    'second_area': int(row['Second_district'])
                }
                trends['recent_numbers'].append(period_numbers)
            
            # 找出未開出的號碼
            recent_first_area = set()
            recent_second_area = set()
            
            for _, row in df.iterrows():
                for i in range(1, 7):
                    recent_first_area.add(int(row[f'Anumber{i}']))
                recent_second_area.add(int(row['Second_district']))
            
            all_first_area = set(range(1, 39))
            all_second_area = set(range(1, 9))
            
            trends['missing_numbers_first'] = list(all_first_area - recent_first_area)
            trends['missing_numbers_second'] = list(all_second_area - recent_second_area)
            
            return trends
            
        except Exception as e:
            print(f"趨勢分析錯誤: {str(e)}")
            return {}
    
    def search_by_numbers(self, numbers, search_type='exact'):
        """
        根據號碼搜尋歷史記錄
        
        Args:
            numbers: 要搜尋的號碼列表
            search_type: 搜尋類型 ('exact', 'contains', 'any')
            
        Returns:
            DataFrame: 符合條件的歷史記錄
        """
        try:
            df = self.get_history_data()
            
            if df.empty or not numbers:
                return df
            
            if search_type == 'exact':
                # 精確匹配（號碼完全相同）
                if len(numbers) == 6:
                    mask = True
                    for i, num in enumerate(numbers, 1):
                        mask &= (df[f'Anumber{i}'] == num)
                    return df[mask]
            
            elif search_type == 'contains':
                # 包含匹配（包含所有指定號碼）
                mask = pd.Series([False] * len(df))
                for _, row in df.iterrows():
                    row_numbers = [int(row[f'Anumber{i}']) for i in range(1, 7)]
                    if all(num in row_numbers for num in numbers):
                        mask[row.name] = True
                return df[mask]
            
            elif search_type == 'any':
                # 任意匹配（包含任一指定號碼）
                mask = pd.Series([False] * len(df))
                for _, row in df.iterrows():
                    row_numbers = [int(row[f'Anumber{i}']) for i in range(1, 7)]
                    if any(num in row_numbers for num in numbers):
                        mask[row.name] = True
                return df[mask]
            
            return pd.DataFrame()
            
        except Exception as e:
            print(f"號碼搜尋錯誤: {str(e)}")
            return pd.DataFrame()

# Flask 應用
app = Flask(__name__)
config_manager = ConfigManager()
history_manager = PowercolorHistoryManager(config_manager=config_manager)

@app.route('/api/powercolor/history')
def api_powercolor_history():
    """
    API: 獲取威力彩歷史資料
    """
    try:
        # 獲取查詢參數
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        limit = request.args.get('limit', type=int)
        period_start = request.args.get('period_start', type=int)
        period_end = request.args.get('period_end', type=int)
        
        period_range = None
        if period_start and period_end:
            period_range = [period_start, period_end]
        
        # 獲取歷史資料
        df = history_manager.get_history_data(
            start_date=start_date,
            end_date=end_date,
            limit=limit,
            period_range=period_range
        )
        
        # 轉換為JSON格式
        records = []
        for _, row in df.iterrows():
            record = {
                'period': int(row['Period']),
                'date': row['Sdate'].strftime('%Y-%m-%d'),
                'numbers': {
                    'first_area': [int(row[f'Anumber{i}']) for i in range(1, 7)],
                    'second_area': int(row['Second_district'])
                }
            }
            records.append(record)
        
        return jsonify({
            'success': True,
            'total_records': len(records),
            'records': records
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'獲取歷史資料錯誤: {str(e)}'
        })

@app.route('/api/powercolor/statistics')
def api_powercolor_statistics():
    """
    API: 獲取威力彩統計分析
    """
    try:
        analysis_period = request.args.get('period', 100, type=int)
        
        # 獲取統計分析
        stats = history_manager.get_number_statistics(analysis_period=analysis_period)
        
        return jsonify({
            'success': True,
            'statistics': stats
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'統計分析錯誤: {str(e)}'
        })

@app.route('/api/powercolor/patterns')
def api_powercolor_patterns():
    """
    API: 獲取威力彩模式分析
    """
    try:
        analysis_period = request.args.get('period', 50, type=int)
        
        # 獲取模式分析
        patterns = history_manager.get_pattern_analysis(analysis_period=analysis_period)
        
        return jsonify({
            'success': True,
            'patterns': patterns
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'模式分析錯誤: {str(e)}'
        })

@app.route('/api/powercolor/trends')
def api_powercolor_trends():
    """
    API: 獲取威力彩趨勢分析
    """
    try:
        periods = request.args.get('periods', 20, type=int)
        
        # 獲取趨勢分析
        trends = history_manager.get_recent_trends(periods=periods)
        
        return jsonify({
            'success': True,
            'trends': trends
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'趨勢分析錯誤: {str(e)}'
        })

@app.route('/api/powercolor/search')
def api_powercolor_search():
    """
    API: 根據號碼搜尋威力彩歷史記錄
    """
    try:
        numbers_str = request.args.get('numbers', '')
        search_type = request.args.get('type', 'contains')
        
        if not numbers_str:
            return jsonify({
                'success': False,
                'error': '請提供要搜尋的號碼'
            })
        
        # 解析號碼
        try:
            numbers = [int(x.strip()) for x in numbers_str.split(',') if x.strip()]
        except ValueError:
            return jsonify({
                'success': False,
                'error': '號碼格式錯誤，請使用逗號分隔的數字'
            })
        
        # 搜尋歷史記錄
        df = history_manager.search_by_numbers(numbers, search_type)
        
        # 轉換為JSON格式
        records = []
        for _, row in df.iterrows():
            record = {
                'period': int(row['Period']),
                'date': row['Sdate'].strftime('%Y-%m-%d'),
                'numbers': {
                    'first_area': [int(row[f'Anumber{i}']) for i in range(1, 7)],
                    'second_area': int(row['Second_district'])
                }
            }
            records.append(record)
        
        return jsonify({
            'success': True,
            'search_numbers': numbers,
            'search_type': search_type,
            'total_matches': len(records),
            'records': records
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'搜尋錯誤: {str(e)}'
        })

if __name__ == '__main__':
    print("威力彩歷史資料查詢系統")
    print("可用的API端點:")
    print("- /api/powercolor/history - 獲取歷史資料")
    print("- /api/powercolor/statistics - 獲取統計分析")
    print("- /api/powercolor/patterns - 獲取模式分析")
    print("- /api/powercolor/trends - 獲取趨勢分析")
    print("- /api/powercolor/search - 根據號碼搜尋")
    
    app.run(host='0.0.0.0', port=5004, debug=True)