#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部署腳本
用於部署和管理彩票預測系統
"""

import os
import sys
import subprocess
import argparse
import json
import time
from pathlib import Path
from typing import Dict, List, Optional


class DeploymentManager:
    """部署管理器"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root or os.getcwd())
        self.web_dir = self.project_root / 'web'
        self.config_file = self.web_dir / 'config.json'
        self.requirements_file = self.project_root / 'requirements.txt'
        self.pid_file = self.web_dir / 'app.pid'
        
    def check_dependencies(self) -> bool:
        """檢查依賴項"""
        print("檢查依賴項...")
        
        # 檢查Python版本
        if sys.version_info < (3, 8):
            print("錯誤: 需要Python 3.8或更高版本")
            return False
        
        # 檢查必要文件
        required_files = [
            self.web_dir / 'app_refactored.py',
            self.web_dir / 'config_manager.py',
            self.web_dir / 'services.py',
            self.web_dir / 'middleware.py',
            self.web_dir / 'cache_manager.py',
            self.web_dir / 'exceptions.py'
        ]
        
        missing_files = []
        for file_path in required_files:
            if not file_path.exists():
                missing_files.append(str(file_path))
        
        if missing_files:
            print(f"錯誤: 缺少必要文件: {', '.join(missing_files)}")
            return False
        
        print("✓ 依賴項檢查通過")
        return True
    
    def install_requirements(self) -> bool:
        """安裝依賴包"""
        print("安裝依賴包...")
        
        if not self.requirements_file.exists():
            print("警告: requirements.txt 不存在，跳過依賴安裝")
            return True
        
        try:
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', str(self.requirements_file)
            ], check=True, capture_output=True, text=True)
            print("✓ 依賴包安裝完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"錯誤: 依賴包安裝失敗: {e.stderr}")
            return False
    
    def create_default_config(self) -> Dict:
        """創建默認配置"""
        default_config = {
            "app": {
                "host": "0.0.0.0",
                "port": 5003,
                "debug": False,
                "secret_key": "your-secret-key-here"
            },
            "database": {
                "path": str(self.project_root / "lottery_data.db"),
                "pool_size": 10,
                "timeout": 30
            },
            "cache": {
                "enabled": True,
                "backend": "memory",
                "default_ttl": 300,
                "max_size": 1000
            },
            "redis": {
                "host": "localhost",
                "port": 6379,
                "db": 0,
                "password": None
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file": str(self.web_dir / "app.log"),
                "max_size": 10485760,
                "backup_count": 5
            },
            "api": {
                "rate_limit": {
                    "enabled": True,
                    "default_limit": 100,
                    "default_window": 3600
                },
                "cors": {
                    "enabled": True,
                    "origins": ["*"]
                }
            },
            "prediction": {
                "max_predictions": 10,
                "cache_ttl": 1800,
                "analysis_depth": 100
            },
            "system": {
                "max_workers": 4,
                "request_timeout": 30,
                "max_request_size": 16777216
            }
        }
        return default_config
    
    def setup_config(self, force: bool = False) -> bool:
        """設置配置文件"""
        print("設置配置文件...")
        
        if self.config_file.exists() and not force:
            print("✓ 配置文件已存在")
            return True
        
        try:
            config = self.create_default_config()
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            print(f"✓ 配置文件已創建: {self.config_file}")
            return True
        except Exception as e:
            print(f"錯誤: 創建配置文件失敗: {e}")
            return False
    
    def setup_directories(self) -> bool:
        """設置目錄結構"""
        print("設置目錄結構...")
        
        directories = [
            self.web_dir / 'logs',
            self.web_dir / 'static',
            self.web_dir / 'templates',
            self.project_root / 'data',
            self.project_root / 'backups'
        ]
        
        try:
            for directory in directories:
                directory.mkdir(parents=True, exist_ok=True)
            print("✓ 目錄結構設置完成")
            return True
        except Exception as e:
            print(f"錯誤: 目錄設置失敗: {e}")
            return False
    
    def run_tests(self) -> bool:
        """運行測試"""
        print("運行測試...")
        
        test_file = self.web_dir / 'tests.py'
        if not test_file.exists():
            print("警告: 測試文件不存在，跳過測試")
            return True
        
        try:
            result = subprocess.run([
                sys.executable, str(test_file)
            ], cwd=str(self.web_dir), capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✓ 所有測試通過")
                return True
            else:
                print(f"錯誤: 測試失敗\n{result.stderr}")
                return False
        except Exception as e:
            print(f"錯誤: 運行測試失敗: {e}")
            return False
    
    def start_application(self, background: bool = True) -> bool:
        """啟動應用程序"""
        print("啟動應用程序...")
        
        app_file = self.web_dir / 'app_refactored.py'
        if not app_file.exists():
            print(f"錯誤: 應用程序文件不存在: {app_file}")
            return False
        
        try:
            if background:
                # 後台運行
                process = subprocess.Popen([
                    sys.executable, str(app_file)
                ], cwd=str(self.web_dir), stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                
                # 保存PID
                with open(self.pid_file, 'w') as f:
                    f.write(str(process.pid))
                
                # 等待一下確保啟動成功
                time.sleep(2)
                if process.poll() is None:
                    print(f"✓ 應用程序已在後台啟動 (PID: {process.pid})")
                    return True
                else:
                    print("錯誤: 應用程序啟動失敗")
                    return False
            else:
                # 前台運行
                subprocess.run([sys.executable, str(app_file)], cwd=str(self.web_dir))
                return True
        except Exception as e:
            print(f"錯誤: 啟動應用程序失敗: {e}")
            return False
    
    def stop_application(self) -> bool:
        """停止應用程序"""
        print("停止應用程序...")
        
        if not self.pid_file.exists():
            print("警告: PID文件不存在，應用程序可能未運行")
            return True
        
        try:
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            # 嘗試終止進程
            import signal
            os.kill(pid, signal.SIGTERM)
            
            # 等待進程結束
            time.sleep(2)
            
            # 檢查進程是否還在運行
            try:
                os.kill(pid, 0)
                # 如果還在運行，強制終止
                os.kill(pid, signal.SIGKILL)
                print("✓ 應用程序已強制停止")
            except OSError:
                print("✓ 應用程序已停止")
            
            # 刪除PID文件
            self.pid_file.unlink()
            return True
        except Exception as e:
            print(f"錯誤: 停止應用程序失敗: {e}")
            return False
    
    def get_status(self) -> Dict:
        """獲取應用程序狀態"""
        status = {
            'running': False,
            'pid': None,
            'config_exists': self.config_file.exists(),
            'dependencies_ok': True
        }
        
        if self.pid_file.exists():
            try:
                with open(self.pid_file, 'r') as f:
                    pid = int(f.read().strip())
                
                # 檢查進程是否存在
                try:
                    os.kill(pid, 0)
                    status['running'] = True
                    status['pid'] = pid
                except OSError:
                    # 進程不存在，刪除過期的PID文件
                    self.pid_file.unlink()
            except Exception:
                pass
        
        return status
    
    def backup_database(self) -> bool:
        """備份數據庫"""
        print("備份數據庫...")
        
        db_path = self.project_root / "lottery_data.db"
        if not db_path.exists():
            print("警告: 數據庫文件不存在")
            return True
        
        backup_dir = self.project_root / "backups"
        backup_dir.mkdir(exist_ok=True)
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        backup_path = backup_dir / f"lottery_data_{timestamp}.db"
        
        try:
            import shutil
            shutil.copy2(db_path, backup_path)
            print(f"✓ 數據庫已備份到: {backup_path}")
            return True
        except Exception as e:
            print(f"錯誤: 數據庫備份失敗: {e}")
            return False
    
    def deploy(self, skip_tests: bool = False, background: bool = True) -> bool:
        """完整部署流程"""
        print("開始部署彩票預測系統...")
        
        steps = [
            ("檢查依賴項", self.check_dependencies),
            ("設置目錄結構", self.setup_directories),
            ("安裝依賴包", self.install_requirements),
            ("設置配置文件", self.setup_config),
        ]
        
        if not skip_tests:
            steps.append(("運行測試", self.run_tests))
        
        steps.extend([
            ("備份數據庫", self.backup_database),
            ("啟動應用程序", lambda: self.start_application(background))
        ])
        
        for step_name, step_func in steps:
            print(f"\n=== {step_name} ===")
            if not step_func():
                print(f"\n❌ 部署失敗於步驟: {step_name}")
                return False
        
        print("\n🎉 部署成功完成!")
        print("\n應用程序信息:")
        print(f"  - 訪問地址: http://localhost:5003")
        print(f"  - 配置文件: {self.config_file}")
        print(f"  - 日誌文件: {self.web_dir / 'app.log'}")
        print(f"  - PID文件: {self.pid_file}")
        
        return True


def main():
    """主函數"""
    parser = argparse.ArgumentParser(description='彩票預測系統部署工具')
    parser.add_argument('action', choices=['deploy', 'start', 'stop', 'restart', 'status', 'test', 'backup'],
                       help='要執行的操作')
    parser.add_argument('--project-root', help='項目根目錄路徑')
    parser.add_argument('--skip-tests', action='store_true', help='跳過測試')
    parser.add_argument('--foreground', action='store_true', help='前台運行應用程序')
    parser.add_argument('--force-config', action='store_true', help='強制重新創建配置文件')
    
    args = parser.parse_args()
    
    # 創建部署管理器
    manager = DeploymentManager(args.project_root)
    
    # 執行操作
    if args.action == 'deploy':
        success = manager.deploy(skip_tests=args.skip_tests, background=not args.foreground)
        sys.exit(0 if success else 1)
    
    elif args.action == 'start':
        if manager.get_status()['running']:
            print("應用程序已在運行")
            sys.exit(0)
        success = manager.start_application(background=not args.foreground)
        sys.exit(0 if success else 1)
    
    elif args.action == 'stop':
        success = manager.stop_application()
        sys.exit(0 if success else 1)
    
    elif args.action == 'restart':
        print("重啟應用程序...")
        manager.stop_application()
        time.sleep(1)
        success = manager.start_application(background=not args.foreground)
        sys.exit(0 if success else 1)
    
    elif args.action == 'status':
        status = manager.get_status()
        print("應用程序狀態:")
        print(f"  運行中: {'是' if status['running'] else '否'}")
        if status['pid']:
            print(f"  進程ID: {status['pid']}")
        print(f"  配置文件存在: {'是' if status['config_exists'] else '否'}")
        print(f"  依賴項正常: {'是' if status['dependencies_ok'] else '否'}")
    
    elif args.action == 'test':
        success = manager.run_tests()
        sys.exit(0 if success else 1)
    
    elif args.action == 'backup':
        success = manager.backup_database()
        sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()