#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正版：更新最新彩券開獎數據
使用正確的台灣彩券官方網站網址
"""

import sqlite3
import requests
from bs4 import BeautifulSoup
import json
from datetime import datetime, timedelta
import logging
import re
import time

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('lottery_updater')

class TaiwanLotteryUpdater:
    """台灣彩券數據更新器 - 使用正確網址"""
    
    def __init__(self, db_path='data/lottery_data.db'):
        self.db_path = db_path
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Referer': 'https://www.taiwanlottery.com/'
        })
        
        # 正確的網址 - 是 .com 不是 .com.tw！
        self.base_url = 'https://www.taiwanlottery.com'
        
    def fetch_dailycash_results(self):
        """獲取今彩539最新開獎結果"""
        try:
            # 正確的今彩539結果頁面
            url = f'{self.base_url}/lotto/result/daily_cash'
            logger.info(f"正在訪問: {url}")
            
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 尋找開獎結果
                results = []
                
                # 根據頁面結構尋找結果
                result_sections = soup.find_all('div', class_='result-section')
                
                # 如果找不到用這個class，嘗試其他方式
                if not result_sections:
                    # 尋找包含期數的元素
                    period_elements = soup.find_all(text=re.compile(r'第\d+期'))
                    
                    for period_elem in period_elements[:5]:  # 只取最新5期
                        try:
                            parent = period_elem.parent
                            # 提取期號
                            period_match = re.search(r'第(\d+)期', period_elem)
                            if period_match:
                                period = period_match.group(1)
                                
                                # 尋找日期
                                date_match = re.search(r'(\d+/\d+/\d+)', str(parent.parent))
                                date = date_match.group(1) if date_match else None
                                
                                # 尋找開獎號碼
                                number_elements = parent.parent.find_all('span', class_='ball-number')
                                if not number_elements:
                                    number_elements = parent.parent.find_all('div', class_='number')
                                
                                numbers = []
                                for elem in number_elements:
                                    num_text = elem.text.strip()
                                    if num_text.isdigit():
                                        numbers.append(int(num_text))
                                
                                if len(numbers) == 5:  # 今彩539有5個號碼
                                    results.append({
                                        'period': period,
                                        'date': self.convert_date_format(date),
                                        'numbers': sorted(numbers)
                                    })
                                    
                        except Exception as e:
                            logger.debug(f"解析單筆資料失敗: {e}")
                            continue
                
                logger.info(f"成功解析 {len(results)} 筆開獎結果")
                return results
                
            else:
                logger.error(f"HTTP請求失敗，狀態碼: {response.status_code}")
                
        except Exception as e:
            logger.error(f"獲取今彩539數據失敗: {e}")
            
        return []
    
    def convert_date_format(self, date_str):
        """轉換日期格式 114/08/13 -> 2025-08-13"""
        if not date_str:
            return None
            
        try:
            parts = date_str.split('/')
            if len(parts) == 3:
                year = int(parts[0]) + 1911  # 民國轉西元
                month = parts[1].zfill(2)
                day = parts[2].zfill(2)
                return f"{year}-{month}-{day}"
        except:
            pass
        return date_str
    
    def update_database(self, results):
        """更新資料庫"""
        if not results:
            logger.warning("沒有資料可更新")
            return 0
            
        updated_count = 0
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for data in results:
                try:
                    period = int(data['period'])
                    
                    # 檢查期號是否已存在
                    cursor.execute("SELECT Period FROM DailyCash WHERE Period = ?", (period,))
                    
                    if cursor.fetchone():
                        logger.info(f"期號 {period} 已存在，跳過")
                        continue
                    
                    # 插入新數據
                    numbers = data['numbers']
                    cursor.execute("""
                        INSERT INTO DailyCash (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, ('今彩539', period, data['date'], numbers[0], numbers[1], numbers[2], numbers[3], numbers[4]))
                    
                    updated_count += 1
                    logger.info(f"成功更新今彩539期號 {period} 的開獎結果")
                    
                except Exception as e:
                    logger.error(f"插入資料失敗: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            logger.info(f"總共更新 {updated_count} 筆資料")
            return updated_count
            
        except Exception as e:
            logger.error(f"更新資料庫失敗: {e}")
            return 0
    
    def manual_insert_results(self):
        """手動插入從截圖看到的最新結果"""
        manual_data = [
            {'period': '114000196', 'date': '2025-08-13', 'numbers': [11, 23, 26, 32, 34]},
            {'period': '114000195', 'date': '2025-08-12', 'numbers': [1, 9, 17, 25, 30]},
            {'period': '114000194', 'date': '2025-08-11', 'numbers': [1, 14, 22, 26, 28]},
            {'period': '114000193', 'date': '2025-08-09', 'numbers': [1, 9, 27, 29, 30]},
        ]
        
        return self.update_database(manual_data)

def main():
    """主程式"""
    print("=" * 50)
    print("台灣彩券開獎資料更新程式（修正版）")
    print("=" * 50)
    
    updater = TaiwanLotteryUpdater()
    
    print("\n正在更新今彩539...")
    
    # 嘗試自動獲取
    results = updater.fetch_dailycash_results()
    
    if results:
        count = updater.update_database(results)
        if count > 0:
            print(f"✅ 成功更新 {count} 筆今彩539資料！")
        else:
            print("⚠️ 所有資料都是最新的")
    else:
        # 如果自動獲取失敗，使用手動資料
        print("自動獲取失敗，使用手動資料更新...")
        count = updater.manual_insert_results()
        if count > 0:
            print(f"✅ 手動更新 {count} 筆今彩539資料！")
        else:
            print("⚠️ 手動資料已是最新的")
    
    print("\n更新完成！")

if __name__ == "__main__":
    main()