#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改進的Web應用啟動器 - 解決路徑問題
"""

import os
import sys
from pathlib import Path

def setup_paths():
    """設置正確的路徑配置"""
    # 獲取腳本所在目錄（專案根目錄）
    script_dir = Path(__file__).parent.absolute()
    
    # 確保專案根目錄在Python路徑中
    if str(script_dir) not in sys.path:
        sys.path.insert(0, str(script_dir))
    
    # 設置工作目錄
    os.chdir(script_dir)
    
    print(f"📁 工作目錄設定為: {script_dir}")
    print(f"🐍 Python路徑: {sys.path[:3]}...")
    
    return script_dir

def import_with_fallback():
    """帶備援機制的模組導入"""
    imports_status = {
        'config_manager': False,
        'services': False,
        'middleware': False,
        'db_manager': False,
        'lottery_updater': False
    }
    
    # 嘗試導入核心模組
    try:
        from config_manager import ConfigManager
        imports_status['config_manager'] = True
        print("✅ ConfigManager 導入成功")
    except ImportError as e:
        print(f"⚠️  ConfigManager 導入失敗: {e}")
    
    try:
        from services import get_service_container
        imports_status['services'] = True
        print("✅ Services 導入成功")
    except ImportError as e:
        print(f"⚠️  Services 導入失敗: {e}")
    
    try:
        from middleware import APIResponse
        imports_status['middleware'] = True
        print("✅ Middleware 導入成功")
    except ImportError as e:
        print(f"⚠️  Middleware 導入失敗: {e}")
    
    try:
        from data.db_manager import DBManager
        imports_status['db_manager'] = True
        print("✅ DBManager 導入成功")
    except ImportError as e:
        print(f"⚠️  DBManager 導入失敗: {e}")
    
    # 嘗試導入更新模組
    update_modules = [
        'automated_lottery_updater',
        'data.lottery_daily_updater',
        'data.real_lottery_updater'
    ]
    
    for module in update_modules:
        try:
            __import__(module)
            imports_status['lottery_updater'] = True
            print(f"✅ {module} 導入成功")
            break
        except ImportError as e:
            print(f"⚠️  {module} 導入失敗: {e}")
    
    return imports_status

def start_web_app():
    """啟動Web應用"""
    try:
        # 設置路徑
        project_root = setup_paths()
        
        # 檢查導入狀況
        print("\n🔍 檢查模組導入狀況...")
        imports_status = import_with_fallback()
        
        # 檢查關鍵檔案
        web_app_path = project_root / 'web' / 'app.py'
        if not web_app_path.exists():
            print(f"❌ Web應用檔案不存在: {web_app_path}")
            return False
        
        print(f"\n🚀 準備啟動Web應用: {web_app_path}")
        
        # 設置環境變數
        os.environ['FLASK_APP'] = str(web_app_path)
        os.environ['FLASK_ENV'] = 'development'
        os.environ['PYTHONPATH'] = str(project_root)
        
        # 導入並啟動Flask應用
        sys.path.insert(0, str(project_root / 'web'))
        
        try:
            from web.app import app
            print("✅ Flask應用導入成功")
            
            print("\n🌐 啟動Web服務器...")
            print("🔗 訪問地址: http://localhost:7891")
            print("⏹️  按 Ctrl+C 停止服務器")
            
            app.run(
                host='0.0.0.0',
                port=7891,
                debug=True,
                use_reloader=False  # 避免重載器導致路徑問題
            )
            
        except ImportError as e:
            print(f"❌ Flask應用導入失敗: {e}")
            print("\n🛠️  嘗試基礎模式啟動...")
            
            # 基礎模式啟動
            from flask import Flask
            basic_app = Flask(__name__)
            
            @basic_app.route('/')
            def home():
                return """
                <h1>彩票預測系統 - 基礎模式</h1>
                <p>系統在基礎模式下運行，某些功能可能不可用</p>
                <p>模組導入狀況:</p>
                <ul>
                """ + "\n".join([
                    f"<li>{'✅' if v else '❌'} {k}</li>" 
                    for k, v in imports_status.items()
                ]) + """
                </ul>
                """
            
            basic_app.run(host='0.0.0.0', port=7891, debug=True)
            
    except Exception as e:
        print(f"❌ Web應用啟動失敗: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🎯 彩票預測系統 - 改進啟動器")
    print("=" * 50)
    success = start_web_app()
    
    if not success:
        print("\n❌ 啟動失敗，請檢查錯誤訊息")
        sys.exit(1)