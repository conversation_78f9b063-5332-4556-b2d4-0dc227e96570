#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新最新彩券開獎數據
使用台灣彩券官方網站數據
"""

import sqlite3
import requests
from bs4 import BeautifulSoup
import json
from datetime import datetime, timedelta
import logging
import re
import time

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('lottery_updater')

class TaiwanLotteryUpdater:
    """台灣彩券數據更新器"""
    
    def __init__(self, db_path='data/lottery_data.db'):
        self.db_path = db_path
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
        })
        
    def fetch_latest_539(self):
        """獲取今彩539最新開獎結果"""
        try:
            # 使用台灣彩券官方API端點
            url = 'https://www.taiwanlottery.com/api/index.php'
            
            # 今彩539的參數
            params = {
                'action': 'getlotteryresult', 
                'gameno': '539',
                'period': '1'  # 獲取最近1期
            }
            
            response = self.session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"成功獲取今彩539數據: {data}")
                return data
            else:
                # 備用方案：直接從網頁解析
                return self.scrape_539_from_web()
                
        except Exception as e:
            logger.error(f"獲取今彩539數據失敗: {e}")
            return self.scrape_539_from_web()
    
    def scrape_539_from_web(self):
        """從網頁爬取今彩539開獎結果"""
        try:
            url = 'https://www.taiwanlottery.com/lotto/dailycash/history'
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 尋找開獎結果表格
                table = soup.find('table', {'class': 'history-table'})
                if table:
                    rows = table.find_all('tr')[1:2]  # 只取第一行（最新一期）
                    for row in rows:
                        cols = row.find_all('td')
                        if len(cols) >= 7:
                            period = cols[0].text.strip()
                            date = cols[1].text.strip()
                            # 獲取開獎號碼
                            numbers = []
                            for i in range(2, 7):
                                num = cols[i].text.strip()
                                if num.isdigit():
                                    numbers.append(int(num))
                            
                            if len(numbers) == 5:
                                return {
                                    'period': period,
                                    'date': date,
                                    'numbers': numbers
                                }
                                
        except Exception as e:
            logger.error(f"從網頁爬取失敗: {e}")
            
        return None
        
    def update_database(self, lottery_type, data):
        """更新資料庫"""
        if not data:
            logger.warning(f"沒有資料可更新: {lottery_type}")
            return False
            
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 檢查表是否存在
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS DailyCash (
                    period INTEGER PRIMARY KEY,
                    draw_date TEXT,
                    num1 INTEGER,
                    num2 INTEGER, 
                    num3 INTEGER,
                    num4 INTEGER,
                    num5 INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 檢查期號是否已存在
            period = int(data['period']) if isinstance(data['period'], str) else data['period']
            cursor.execute("SELECT period FROM DailyCash WHERE period = ?", (period,))
            
            if cursor.fetchone():
                logger.info(f"期號 {period} 已存在，跳過")
                conn.close()
                return False
                
            # 插入新數據
            numbers = data['numbers']
            cursor.execute("""
                INSERT INTO DailyCash (period, draw_date, num1, num2, num3, num4, num5)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (period, data['date'], numbers[0], numbers[1], numbers[2], numbers[3], numbers[4]))
            
            conn.commit()
            conn.close()
            
            logger.info(f"成功更新今彩539期號 {period} 的開獎結果")
            return True
            
        except Exception as e:
            logger.error(f"更新資料庫失敗: {e}")
            return False

def main():
    """主程式"""
    print("=" * 50)
    print("台灣彩券開獎資料更新程式")
    print("=" * 50)
    
    updater = TaiwanLotteryUpdater()
    
    # 更新今彩539
    print("\n正在更新今彩539...")
    data = updater.fetch_latest_539()
    
    if data:
        print(f"取得資料: {data}")
        if updater.update_database('dailycash', data):
            print("✅ 今彩539更新成功！")
        else:
            print("⚠️ 今彩539已是最新資料")
    else:
        print("❌ 無法取得今彩539資料")
    
    print("\n更新完成！")

if __name__ == "__main__":
    main()