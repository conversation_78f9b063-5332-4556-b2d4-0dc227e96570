#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能數據檢查系統 (Intelligent Data Checker)
自動檢查台灣彩券官方數據並判斷是否需要同步

核心功能：
1. 智能數據檢查 (Intelligent Data Checking)
2. 期號比較分析 (Period Comparison Analysis)
3. 數據新鮮度檢測 (Data Freshness Detection)
4. 自動同步觸發 (Auto Sync Triggering)
5. 數據驗證機制 (Data Validation Mechanism)
"""

import os
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import json
import requests
import time

# 導入我們的數據完整性框架
try:
    from data_integrity_framework import get_integrity_manager
    from real_data_integration import get_real_data_manager
    DATA_INTEGRITY_AVAILABLE = True
except ImportError:
    DATA_INTEGRITY_AVAILABLE = False

# 設置日誌系統
checker_logger = logging.getLogger('intelligent_data_checker')
handler = logging.FileHandler('data/intelligent_checker.log', encoding='utf-8')
handler.setFormatter(logging.Formatter(
    '%(asctime)s - INTELLIGENT_CHECKER - %(levelname)s - %(message)s'
))
checker_logger.addHandler(handler)
checker_logger.setLevel(logging.INFO)

@dataclass
class DataCheckResult:
    """數據檢查結果"""
    lottery_type: str
    lottery_name: str
    current_period: int
    latest_available_period: Optional[int]
    has_new_data: bool
    data_age_hours: float
    sync_needed: bool
    check_timestamp: str
    error_message: Optional[str] = None

@dataclass 
class SyncRecommendation:
    """同步建議"""
    immediate_sync_needed: bool
    lottery_types_to_sync: List[str]
    priority_level: str  # 'high', 'medium', 'low'
    estimated_new_records: int
    recommendation_reason: str
    next_check_time: datetime

class IntelligentDataChecker:
    """智能數據檢查器"""
    
    def __init__(self):
        self.db_path = 'data/lottery_data.db'
        
        # 彩票類型配置
        self.lottery_config = {
            'powercolor': {
                'name': '威力彩',
                'table': 'Powercolor',
                'draw_frequency_hours': 72,  # 每3天開獎
                'check_frequency_hours': 2,  # 每2小時檢查
                'priority': 'high'
            },
            'lotto649': {
                'name': '大樂透', 
                'table': 'Lotto649',
                'draw_frequency_hours': 72,  # 每3天開獎
                'check_frequency_hours': 2,  # 每2小時檢查
                'priority': 'high'
            },
            'dailycash': {
                'name': '今彩539',
                'table': 'DailyCash',
                'draw_frequency_hours': 24,  # 每天開獎
                'check_frequency_hours': 1,  # 每小時檢查
                'priority': 'medium'
            }
        }
        
        # 數據管理器
        if DATA_INTEGRITY_AVAILABLE:
            self.integrity_manager = get_integrity_manager()
            self.data_manager = get_real_data_manager()
        
        checker_logger.info("智能數據檢查器初始化完成")
    
    def get_latest_period_from_db(self, lottery_type: str) -> Tuple[Optional[int], Optional[datetime]]:
        """從數據庫獲取最新期號和時間"""
        config = self.lottery_config.get(lottery_type)
        if not config:
            return None, None
            
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 獲取最新期號和時間
            query = f"""
                SELECT Period, Date, datetime(Date) as parsed_date 
                FROM {config['table']} 
                ORDER BY Period DESC, Date DESC 
                LIMIT 1
            """
            
            cursor.execute(query)
            result = cursor.fetchone()
            conn.close()
            
            if result:
                period, date_str, parsed_date = result
                try:
                    # 嘗試解析日期
                    if parsed_date:
                        date_obj = datetime.strptime(parsed_date, '%Y-%m-%d %H:%M:%S')
                    else:
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                    return period, date_obj
                except:
                    return period, None
            
            return None, None
            
        except Exception as e:
            checker_logger.error(f"獲取{lottery_type}最新期號失敗: {str(e)}")
            return None, None
    
    def estimate_expected_period(self, lottery_type: str, current_period: Optional[int], 
                                last_update: Optional[datetime]) -> Optional[int]:
        """根據開獎頻率估算期望的最新期號"""
        if not current_period or not last_update:
            return None
            
        config = self.lottery_config.get(lottery_type)
        if not config:
            return None
        
        # 計算從最後更新到現在的時間
        time_diff = datetime.now() - last_update
        hours_passed = time_diff.total_seconds() / 3600
        
        # 估算可能的新開獎次數
        draw_frequency = config['draw_frequency_hours']
        estimated_new_draws = int(hours_passed / draw_frequency)
        
        if estimated_new_draws > 0:
            # 估算期望期號（假設期號是連續遞增的）
            expected_period = current_period + estimated_new_draws
            return expected_period
        
        return current_period
    
    def check_data_freshness(self, lottery_type: str) -> DataCheckResult:
        """檢查數據新鮮度"""
        config = self.lottery_config.get(lottery_type)
        if not config:
            return DataCheckResult(
                lottery_type=lottery_type,
                lottery_name=lottery_type,
                current_period=0,
                latest_available_period=None,
                has_new_data=False,
                data_age_hours=0,
                sync_needed=False,
                check_timestamp=datetime.now().isoformat(),
                error_message="未知的彩票類型"
            )
        
        # 從數據庫獲取最新數據
        current_period, last_update = self.get_latest_period_from_db(lottery_type)
        
        # 計算數據年齡
        data_age_hours = 0
        if last_update:
            data_age_hours = (datetime.now() - last_update).total_seconds() / 3600
        
        # 估算期望的最新期號
        expected_period = self.estimate_expected_period(lottery_type, current_period, last_update)
        
        # 判斷是否需要同步
        sync_needed = False
        has_new_data = False
        
        # 基於時間的同步判斷
        check_frequency = config['check_frequency_hours']
        if data_age_hours > check_frequency:
            sync_needed = True
            has_new_data = True
        
        # 基於期號的同步判斷
        if expected_period and current_period and expected_period > current_period:
            sync_needed = True
            has_new_data = True
        
        result = DataCheckResult(
            lottery_type=lottery_type,
            lottery_name=config['name'],
            current_period=current_period or 0,
            latest_available_period=expected_period,
            has_new_data=has_new_data,
            data_age_hours=data_age_hours,
            sync_needed=sync_needed,
            check_timestamp=datetime.now().isoformat()
        )
        
        checker_logger.info(f"{config['name']} 檢查結果: 當前期號{current_period}, "
                          f"期望期號{expected_period}, 數據年齡{data_age_hours:.1f}小時, "
                          f"需要同步: {sync_needed}")
        
        return result
    
    def check_all_lotteries(self) -> Dict[str, DataCheckResult]:
        """檢查所有彩票類型的數據新鮮度"""
        checker_logger.info("開始檢查所有彩票數據新鮮度")
        
        results = {}
        for lottery_type in self.lottery_config.keys():
            results[lottery_type] = self.check_data_freshness(lottery_type)
        
        return results
    
    def generate_sync_recommendation(self, check_results: Dict[str, DataCheckResult]) -> SyncRecommendation:
        """生成同步建議"""
        
        # 統計需要同步的彩票類型
        sync_needed_types = []
        high_priority_count = 0
        total_estimated_records = 0
        
        for lottery_type, result in check_results.items():
            if result.sync_needed:
                sync_needed_types.append(lottery_type)
                config = self.lottery_config.get(lottery_type)
                if config and config['priority'] == 'high':
                    high_priority_count += 1
                
                # 估算新記錄數量
                if result.latest_available_period and result.current_period:
                    estimated_new = max(0, result.latest_available_period - result.current_period)
                    total_estimated_records += estimated_new
        
        # 確定優先級
        if high_priority_count > 0:
            priority_level = 'high'
        elif len(sync_needed_types) > 0:
            priority_level = 'medium' 
        else:
            priority_level = 'low'
        
        # 確定是否需要立即同步
        immediate_sync = len(sync_needed_types) > 0
        
        # 生成建議原因
        if not sync_needed_types:
            reason = "所有彩票數據都是最新的，無需同步"
        else:
            lottery_names = [self.lottery_config[lt]['name'] for lt in sync_needed_types]
            reason = f"檢測到 {len(sync_needed_types)} 種彩票需要同步: {', '.join(lottery_names)}"
        
        # 計算下次檢查時間（選擇最小的檢查頻率）
        min_check_hours = min([config['check_frequency_hours'] 
                              for config in self.lottery_config.values()])
        next_check_time = datetime.now() + timedelta(hours=min_check_hours)
        
        recommendation = SyncRecommendation(
            immediate_sync_needed=immediate_sync,
            lottery_types_to_sync=sync_needed_types,
            priority_level=priority_level,
            estimated_new_records=total_estimated_records,
            recommendation_reason=reason,
            next_check_time=next_check_time
        )
        
        checker_logger.info(f"同步建議: {recommendation.recommendation_reason}")
        
        return recommendation
    
    def perform_intelligent_check(self) -> Tuple[Dict[str, DataCheckResult], SyncRecommendation]:
        """執行智能檢查並生成建議"""
        checker_logger.info("開始執行智能數據檢查")
        
        # 檢查所有彩票數據
        check_results = self.check_all_lotteries()
        
        # 生成同步建議
        recommendation = self.generate_sync_recommendation(check_results)
        
        # 保存檢查結果
        self.save_check_results(check_results, recommendation)
        
        checker_logger.info("智能數據檢查完成")
        
        return check_results, recommendation
    
    def save_check_results(self, check_results: Dict[str, DataCheckResult], 
                          recommendation: SyncRecommendation):
        """保存檢查結果到文件"""
        try:
            os.makedirs('data/check_results', exist_ok=True)
            
            report = {
                'check_timestamp': datetime.now().isoformat(),
                'check_results': {k: {
                    'lottery_type': v.lottery_type,
                    'lottery_name': v.lottery_name,
                    'current_period': v.current_period,
                    'latest_available_period': v.latest_available_period,
                    'has_new_data': v.has_new_data,
                    'data_age_hours': v.data_age_hours,
                    'sync_needed': v.sync_needed,
                    'check_timestamp': v.check_timestamp,
                    'error_message': v.error_message
                } for k, v in check_results.items()},
                'recommendation': {
                    'immediate_sync_needed': recommendation.immediate_sync_needed,
                    'lottery_types_to_sync': recommendation.lottery_types_to_sync,
                    'priority_level': recommendation.priority_level,
                    'estimated_new_records': recommendation.estimated_new_records,
                    'recommendation_reason': recommendation.recommendation_reason,
                    'next_check_time': recommendation.next_check_time.isoformat()
                }
            }
            
            filename = f"data/check_results/check_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
                
            checker_logger.info(f"檢查結果已保存到: {filename}")
            
        except Exception as e:
            checker_logger.error(f"保存檢查結果失敗: {str(e)}")
    
    def get_latest_check_results(self) -> Optional[Dict[str, Any]]:
        """獲取最新的檢查結果"""
        try:
            check_dir = 'data/check_results'
            if not os.path.exists(check_dir):
                return None
            
            # 獲取最新的檢查結果文件
            check_files = [f for f in os.listdir(check_dir) if f.startswith('check_') and f.endswith('.json')]
            if not check_files:
                return None
            
            latest_file = max(check_files)
            file_path = os.path.join(check_dir, latest_file)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
                
        except Exception as e:
            checker_logger.error(f"獲取最新檢查結果失敗: {str(e)}")
            return None

# 全局檢查器實例
_data_checker = None

def get_data_checker() -> IntelligentDataChecker:
    """獲取全局智能數據檢查器實例"""
    global _data_checker
    if _data_checker is None:
        _data_checker = IntelligentDataChecker()
    return _data_checker

# 便利函數
def check_lottery_data_intelligently() -> Tuple[Dict[str, DataCheckResult], SyncRecommendation]:
    """智能檢查彩票數據"""
    checker = get_data_checker()
    return checker.perform_intelligent_check()

def get_sync_recommendation() -> Optional[SyncRecommendation]:
    """獲取最新的同步建議"""
    checker = get_data_checker()
    results = checker.get_latest_check_results()
    if results and 'recommendation' in results:
        rec_data = results['recommendation']
        return SyncRecommendation(
            immediate_sync_needed=rec_data['immediate_sync_needed'],
            lottery_types_to_sync=rec_data['lottery_types_to_sync'],
            priority_level=rec_data['priority_level'],
            estimated_new_records=rec_data['estimated_new_records'],
            recommendation_reason=rec_data['recommendation_reason'],
            next_check_time=datetime.fromisoformat(rec_data['next_check_time'])
        )
    return None

if __name__ == "__main__":
    # 測試智能數據檢查器
    print("="*80)
    print("智能數據檢查系統 測試")
    print("="*80)
    
    checker = get_data_checker()
    
    # 執行智能檢查
    print("\n執行智能數據檢查...")
    check_results, recommendation = checker.perform_intelligent_check()
    
    # 顯示檢查結果
    print("\n檢查結果:")
    for lottery_type, result in check_results.items():
        print(f"  {result.lottery_name}:")
        print(f"    當前期號: {result.current_period}")
        print(f"    期望期號: {result.latest_available_period}")
        print(f"    數據年齡: {result.data_age_hours:.1f} 小時")
        print(f"    需要同步: {'是' if result.sync_needed else '否'}")
        print()
    
    # 顯示同步建議
    print("同步建議:")
    print(f"  立即同步: {'是' if recommendation.immediate_sync_needed else '否'}")
    print(f"  優先級: {recommendation.priority_level}")
    print(f"  需要同步的彩票: {recommendation.lottery_types_to_sync}")
    print(f"  估算新記錄: {recommendation.estimated_new_records}")
    print(f"  建議原因: {recommendation.recommendation_reason}")
    print(f"  下次檢查時間: {recommendation.next_check_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\n智能數據檢查測試完成")