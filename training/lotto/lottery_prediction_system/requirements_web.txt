# Phase 3.4 Web API 依賴項
# Web API 和用戶界面所需的 Python 包

# FastAPI 和 Web 服務器
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6

# WebSocket 支持
websockets>=12.0

# HTTP 客戶端和工具
requests>=2.31.0
httpx>=0.25.0

# 異步支持
asyncio
aiofiles>=23.0.0

# 數據驗證和序列化
pydantic>=2.4.0

# 日期時間處理
python-dateutil>=2.8.2

# JSON 處理
orjson>=3.9.0

# CORS 支持 (已包含在 FastAPI 中)

# 可選：API 文檔增強
# swagger-ui-bundle>=0.19.0

# 可選：性能監控
# prometheus-client>=0.18.0

# 可選：安全增強
# python-jose[cryptography]>=3.3.0
# passlib[bcrypt]>=1.7.4

# 測試工具
pytest>=7.4.0
pytest-asyncio>=0.21.0
httpx>=0.25.0  # 用於 FastAPI 測試客戶端