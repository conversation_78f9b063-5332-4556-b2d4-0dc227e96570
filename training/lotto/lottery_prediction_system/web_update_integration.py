#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web更新集成模組
為Flask應用提供更新API支持
"""

from automated_lottery_updater import AutomatedLotteryUpdater
import json
from datetime import datetime

class WebUpdateIntegration:
    """Web更新集成類"""
    
    def __init__(self):
        self.updater = AutomatedLotteryUpdater()
    
    def update_all_lotteries_api(self):
        """API格式的全彩票更新"""
        try:
            results = self.updater.update_all_lotteries()
            
            response = {
                'success': True,
                'timestamp': datetime.now().isoformat(),
                'results': results,
                'message': f'更新完成：{sum(1 for r in results.values() if r)}/{len(results)} 種彩票成功'
            }
            
            return response
            
        except Exception as e:
            return {
                'success': False,
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'message': '更新失敗'
            }
    
    def update_single_lottery_api(self, lottery_type):
        """API格式的單一彩票更新"""
        try:
            if lottery_type == 'powercolor':
                success = self.updater.update_powercolor()
            elif lottery_type == 'lotto649':
                success = self.updater.update_lotto649()
            elif lottery_type == 'dailycash':
                success = self.updater.update_dailycash()
            else:
                raise ValueError(f"未知的彩票類型: {lottery_type}")
            
            return {
                'success': success,
                'timestamp': datetime.now().isoformat(),
                'lottery_type': lottery_type,
                'message': '更新成功' if success else '更新失敗'
            }
            
        except Exception as e:
            return {
                'success': False,
                'timestamp': datetime.now().isoformat(),
                'lottery_type': lottery_type,
                'error': str(e),
                'message': '更新失敗'
            }
    
    def get_status_api(self):
        """獲取系統狀態API"""
        try:
            import sqlite3
            
            conn = sqlite3.connect('data/lottery_data.db')
            cursor = conn.cursor()
            
            status_data = {
                'success': True,
                'timestamp': datetime.now().isoformat(),
                'lottery_status': {}
            }
            
            # 檢查威力彩
            try:
                cursor.execute('SELECT MAX(Period), MAX(Sdate) FROM Powercolor')
                pc_result = cursor.fetchone()
                status_data['lottery_status']['powercolor'] = {
                    'latest_period': pc_result[0],
                    'latest_date': pc_result[1],
                    'status': 'success'
                }
            except Exception:
                status_data['lottery_status']['powercolor'] = {'status': 'error'}
            
            # 檢查大樂透
            try:
                cursor.execute('SELECT MAX(Period), MAX(Sdate) FROM Lotto649')
                lotto_result = cursor.fetchone()
                status_data['lottery_status']['lotto649'] = {
                    'latest_period': lotto_result[0],
                    'latest_date': lotto_result[1],
                    'status': 'success'
                }
            except Exception:
                status_data['lottery_status']['lotto649'] = {'status': 'error'}
            
            # 檢查今彩539
            try:
                cursor.execute('SELECT MAX(Period), MAX(Sdate) FROM DailyCash')
                dc_result = cursor.fetchone()
                status_data['lottery_status']['dailycash'] = {
                    'latest_period': dc_result[0],
                    'latest_date': dc_result[1],
                    'status': 'success'
                }
            except Exception:
                status_data['lottery_status']['dailycash'] = {'status': 'error'}
            
            conn.close()
            
            # 計算整體進度
            success_count = sum(1 for lottery in status_data['lottery_status'].values() if lottery['status'] == 'success')
            status_data['overall_progress'] = (success_count / 3) * 100
            status_data['last_update'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            return status_data
            
        except Exception as e:
            return {
                'success': False,
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'message': '狀態檢查失敗'
            }

# 全域實例
web_updater = WebUpdateIntegration()

# 便捷函數
def update_all_lotteries_for_web():
    """供Web應用調用的更新所有彩票函數"""
    return web_updater.update_all_lotteries_api()

def update_single_lottery_for_web(lottery_type):
    """供Web應用調用的更新單一彩票函數"""
    return web_updater.update_single_lottery_api(lottery_type)

def get_lottery_status_for_web():
    """供Web應用調用的狀態檢查函數"""
    return web_updater.get_status_api()

if __name__ == "__main__":
    # 測試功能
    print("🧪 測試Web更新集成...")
    
    # 測試狀態檢查
    status = get_lottery_status_for_web()
    print("📊 系統狀態:")
    print(json.dumps(status, ensure_ascii=False, indent=2))
    
    # 測試更新功能
    print("\n🔄 測試更新功能...")
    update_result = update_all_lotteries_for_web()
    print("更新結果:")
    print(json.dumps(update_result, ensure_ascii=False, indent=2))