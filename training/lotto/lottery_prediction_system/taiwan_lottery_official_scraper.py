#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
台灣彩券官方網站完整資料爬蟲
支援威力彩、大樂透、今彩539的歷史資料抓取
"""

import requests
import sqlite3
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import time
from bs4 import BeautifulSoup
import urllib3

# 抑制SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class TaiwanLotteryOfficialScraper:
    """台灣彩券官方網站爬蟲"""
    
    def __init__(self, db_path: str = "data/lottery_data.db"):
        self.db_path = db_path
        self.base_url = "https://www.taiwanlottery.com"
        
        # 設定會話
        self.session = requests.Session()
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
        
        # 彩票類型配置
        self.lottery_configs = {
            'powercolor': {
                'name': '威力彩',
                'url': 'https://www.taiwanlottery.com/lotto/result/super_lotto638',
                'table_name': 'Powercolor',
                'main_numbers': 6,
                'special_numbers': 1,
                'draw_days': [1, 4]  # 週一、週四
            },
            'lotto649': {
                'name': '大樂透',
                'url': 'https://www.taiwanlottery.com/lotto/result/lotto649',
                'table_name': 'Lotto649',
                'main_numbers': 6,
                'special_numbers': 1,
                'draw_days': [2, 5]  # 週二、週五
            },
            'dailycash': {
                'name': '今彩539',
                'url': 'https://www.taiwanlottery.com/lotto/result/daily_cash',
                'table_name': 'DailyCash',
                'main_numbers': 5,
                'special_numbers': 0,
                'draw_days': [1, 2, 3, 4, 5, 6, 7]  # 每日
            }
        }
    
    def create_lottery_tables(self):
        """創建彩票歷史資料表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 威力彩表 (已存在，檢查結構)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS Powercolor (
                    gameKind TEXT,
                    Period INTEGER PRIMARY KEY,
                    Sdate TEXT,
                    Anumber1 INTEGER,
                    Anumber2 INTEGER,
                    Anumber3 INTEGER,
                    Anumber4 INTEGER,
                    Anumber5 INTEGER,
                    Anumber6 INTEGER,
                    Second_district INTEGER
                )
            ''')
            
            # 大樂透表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS Lotto649 (
                    gameKind TEXT,
                    Period INTEGER PRIMARY KEY,
                    Sdate TEXT,
                    Anumber1 INTEGER,
                    Anumber2 INTEGER,
                    Anumber3 INTEGER,
                    Anumber4 INTEGER,
                    Anumber5 INTEGER,
                    Anumber6 INTEGER,
                    Special_number INTEGER
                )
            ''')
            
            # 今彩539表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS DailyCash (
                    gameKind TEXT,
                    Period INTEGER PRIMARY KEY,
                    Sdate TEXT,
                    Anumber1 INTEGER,
                    Anumber2 INTEGER,
                    Anumber3 INTEGER,
                    Anumber4 INTEGER,
                    Anumber5 INTEGER
                )
            ''')
            
            conn.commit()
            print("✅ 資料表創建/檢查完成")
            
        except Exception as e:
            print(f"❌ 資料表創建失敗: {e}")
        finally:
            conn.close()
    
    def get_recent_data(self, lottery_type: str) -> List[Dict]:
        """獲取最新開獎資料 - 使用API方式"""
        if lottery_type not in self.lottery_configs:
            print(f"❌ 不支援的彩票類型: {lottery_type}")
            return []
        
        config = self.lottery_configs[lottery_type]
        results = []
        
        try:
            # 嘗試API方式獲取資料
            api_results = self._get_data_via_api(lottery_type, config)
            if api_results:
                results.extend(api_results)
            
            # 如果API方式失敗，嘗試網頁解析方式
            if not results:
                web_results = self._get_data_via_web(lottery_type, config)
                results.extend(web_results)
                
        except Exception as e:
            print(f"❌ 爬取 {config['name']} 失敗: {e}")
        
        return results
    
    def _get_data_via_api(self, lottery_type: str, config: Dict) -> List[Dict]:
        """通過API獲取資料"""
        print(f"🌐 嘗試通過API獲取 {config['name']} 資料...")
        
        # 台灣彩券API端點
        api_base = "https://api.taiwanlottery.com/TLCAPIWeB"
        
        # 不同彩票類型的API路徑
        api_paths = {
            'powercolor': '/lotto/SuperLotto638/result/recent',
            'lotto649': '/lotto/Lotto649/result/recent',
            'dailycash': '/lotto/DailyCash/result/recent'
        }
        
        if lottery_type not in api_paths:
            print(f"   ⚠️ 沒有對應的API路徑")
            return []
        
        api_url = f"{api_base}{api_paths[lottery_type]}"
        print(f"    API URL: {api_url}")
        
        try:
            response = self.session.get(api_url, timeout=15)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ 成功獲取API資料: {type(data)}")
                    
                    # 解析API回應
                    parsed_results = self._parse_api_response(data, lottery_type, config)
                    
                    if parsed_results:
                        print(f"📊 API解析到 {len(parsed_results)} 筆 {config['name']} 資料")
                        return parsed_results
                    else:
                        print(f"⚠️ API資料解析失敗")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ API回應不是有效的JSON: {e}")
                    
            else:
                print(f"❌ API請求失敗: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ API請求錯誤: {e}")
        
        return []
    
    def _get_data_via_web(self, lottery_type: str, config: Dict) -> List[Dict]:
        """通過網頁解析獲取資料"""
        print(f"🌐 回退到網頁解析方式獲取 {config['name']} 資料...")
        
        try:
            url = config['url']
            response = self.session.get(url, timeout=15)
            
            if response.status_code == 200:
                print(f"✅ 成功獲取網頁 ({len(response.content):,} bytes)")
                
                soup = BeautifulSoup(response.content, 'html.parser')
                parsed_results = self._parse_lottery_page(soup, lottery_type, config)
                
                if parsed_results:
                    print(f"📊 網頁解析到 {len(parsed_results)} 筆資料")
                    return parsed_results
                else:
                    print(f"⚠️ 網頁解析失敗")
                    
            else:
                print(f"❌ 網頁請求失敗: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ 網頁解析錯誤: {e}")
        
        return []
    
    def _parse_api_response(self, data: Dict, lottery_type: str, config: Dict) -> List[Dict]:
        """解析API回應資料"""
        results = []
        
        try:
            # 這裡需要根據實際API回應格式進行解析
            # 先打印數據結構以便分析
            print(f"🔍 API回應結構分析:")
            print(f"   數據類型: {type(data)}")
            
            if isinstance(data, dict):
                print(f"   頂層鍵值: {list(data.keys())}")
                
                # 常見的數據路徑
                possible_data_keys = ['data', 'results', 'items', 'records', 'draws']
                
                for key in possible_data_keys:
                    if key in data:
                        items = data[key]
                        print(f"   找到數據陣列 '{key}': {len(items) if isinstance(items, list) else 1} 項")
                        
                        if isinstance(items, list):
                            for i, item in enumerate(items[:3]):  # 檢查前3項
                                print(f"     項目 {i+1}: {list(item.keys()) if isinstance(item, dict) else type(item)}")
                                
                                # 嘗試提取開獎資料
                                result = self._extract_api_item(item, lottery_type, config)
                                if result:
                                    results.append(result)
                        else:
                            # 單項數據
                            result = self._extract_api_item(items, lottery_type, config)
                            if result:
                                results.append(result)
                        break
            
        except Exception as e:
            print(f"❌ API資料解析錯誤: {e}")
        
        return results
    
    def _extract_api_item(self, item: Dict, lottery_type: str, config: Dict) -> Dict:
        """從API項目中提取開獎資料"""
        try:
            # 常見的欄位名稱
            period_keys = ['period', 'drawNo', 'drawNumber', 'gameNo', 'issueNo']
            date_keys = ['drawDate', 'date', 'drawTime', 'issueDate']
            
            # 尋找期號
            period = None
            for key in period_keys:
                if key in item:
                    period = int(item[key])
                    break
            
            # 尋找日期
            date_str = None
            for key in date_keys:
                if key in item:
                    date_str = str(item[key])[:10]  # 取前10個字符 (YYYY-MM-DD)
                    break
            
            # 尋找數字
            numbers = []
            special = None
            
            # 根據彩票類型查找數字
            if lottery_type == 'powercolor':
                for i in range(1, 7):
                    key = f'num{i:02d}' if f'num{i:02d}' in item else f'number{i}'
                    if key in item:
                        numbers.append(int(item[key]))
                special_key = 'specialNum' if 'specialNum' in item else 'powerNum'
                if special_key in item:
                    special = int(item[special_key])
                    
            elif lottery_type == 'lotto649':
                for i in range(1, 7):
                    key = f'num{i:02d}' if f'num{i:02d}' in item else f'number{i}'
                    if key in item:
                        numbers.append(int(item[key]))
                if 'specialNum' in item:
                    special = int(item['specialNum'])
                    
            else:  # dailycash
                for i in range(1, 6):
                    key = f'num{i:02d}' if f'num{i:02d}' in item else f'number{i}'
                    if key in item:
                        numbers.append(int(item[key]))
            
            if period and date_str and len(numbers) == config['main_numbers']:
                return {
                    'period': period,
                    'date': date_str,
                    'numbers': numbers,
                    'special': special,
                    'lottery_type': lottery_type,
                    'source': 'official_api'
                }
                
        except Exception as e:
            print(f"   ⚠️ 解析項目失敗: {e}")
        
        return None

    def get_monthly_data(self, lottery_type: str, year: int, month: int) -> List[Dict]:
        """獲取指定月份的開獎資料"""
        # 目前先使用最新資料方法，之後可以擴展支援歷史資料查詢
        return self.get_recent_data(lottery_type)
    
    def _parse_lottery_page(self, soup: BeautifulSoup, lottery_type: str, config: Dict) -> List[Dict]:
        """解析彩票頁面內容"""
        results = []
        
        try:
            print(f"🔍 開始解析 {config['name']} 網頁內容...")
            
            # 方法1: 通用方法 - 尋找包含數字的區塊
            # 尋找所有包含期號格式的文本
            page_text = soup.get_text()
            
            # 搜尋期號模式 (例如: 114000XXX)
            period_matches = re.findall(r'11[4-9]\d{6}', page_text)
            if period_matches:
                print(f"   找到期號: {period_matches[:3]}...")  # 顯示前3個
            
            # 方法2: 尋找表格結構
            tables = soup.find_all('table')
            print(f"   找到 {len(tables)} 個表格")
            
            for table_idx, table in enumerate(tables):
                rows = table.find_all('tr')
                print(f"   表格 {table_idx+1}: {len(rows)} 行")
                
                for row_idx, row in enumerate(rows):
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 3:  # 至少要有期號、日期、號碼
                        row_text = ' '.join([cell.get_text().strip() for cell in cells])
                        
                        # 檢查是否包含期號
                        period_match = re.search(r'11[4-9]\d{6}', row_text)
                        if period_match:
                            print(f"     第 {row_idx+1} 行可能包含開獎資料: {row_text[:100]}...")
                            
                            # 嘗試提取資料
                            try:
                                result = self._extract_draw_data(row_text, lottery_type, config)
                                if result:
                                    results.append(result)
                                    print(f"     ✅ 成功解析: 期號 {result['period']}")
                                    
                            except Exception as e:
                                print(f"     ⚠️ 解析失敗: {e}")
                                continue
            
            # 方法3: 尋找特定元素
            if not results:
                # 尋找可能的開獎結果容器
                result_containers = soup.find_all(['div', 'section', 'article'], 
                    attrs={'class': re.compile(r'result|draw|lottery|number', re.I)})
                
                print(f"   找到 {len(result_containers)} 個結果容器")
                
                for container in result_containers[:5]:  # 限制檢查前5個
                    container_text = container.get_text()
                    period_match = re.search(r'11[4-9]\d{6}', container_text)
                    if period_match:
                        print(f"   容器包含期號: {period_match.group()}")
                        # 可以在這裡添加更多解析邏輯
            
            print(f"🎯 解析完成，找到 {len(results)} 筆有效資料")
            
        except Exception as e:
            print(f"❌ 解析頁面錯誤: {e}")
        
        return results
    
    def _extract_draw_data(self, text: str, lottery_type: str, config: Dict) -> Dict:
        """從文本中提取開獎資料"""
        # 提取期號
        period_match = re.search(r'11[4-9]\d{6}', text)
        if not period_match:
            return None
            
        period = int(period_match.group())
        
        # 提取日期
        date_match = re.search(r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})', text)
        if not date_match:
            # 嘗試其他日期格式
            date_match = re.search(r'(\d{1,2})[/-](\d{1,2})[/-](\d{4})', text)
            if date_match:
                day, month, year = date_match.groups()
                date_str = f"{year}-{month:0>2}-{day:0>2}"
            else:
                return None
        else:
            year, month, day = date_match.groups()
            date_str = f"{year}-{month:0>2}-{day:0>2}"
        
        # 提取數字 (尋找所有1-2位數字)
        all_numbers = re.findall(r'\b([1-9]\d?)\b', text)
        all_numbers = [int(n) for n in all_numbers if 1 <= int(n) <= 49]
        
        # 根據彩票類型篩選合理範圍
        if lottery_type == 'powercolor':
            valid_numbers = [n for n in all_numbers if 1 <= n <= 38]
            special_range = (1, 8)
        elif lottery_type == 'lotto649':
            valid_numbers = [n for n in all_numbers if 1 <= n <= 49]
            special_range = (1, 49)
        else:  # dailycash
            valid_numbers = [n for n in all_numbers if 1 <= n <= 39]
            special_range = None
        
        if len(valid_numbers) >= config['main_numbers']:
            main_numbers = valid_numbers[:config['main_numbers']]
            
            # 提取特別號
            special = None
            if config['special_numbers'] > 0 and len(valid_numbers) > config['main_numbers']:
                potential_special = valid_numbers[config['main_numbers']]
                if special_range and special_range[0] <= potential_special <= special_range[1]:
                    special = potential_special
            
            return {
                'period': period,
                'date': date_str,
                'numbers': main_numbers,
                'special': special,
                'lottery_type': lottery_type,
                'source': 'official_website'
            }
        
        return None
    
    def save_lottery_data(self, results: List[Dict], lottery_type: str) -> int:
        """儲存彩票資料到資料庫"""
        if not results:
            return 0
        
        config = self.lottery_configs[lottery_type]
        table_name = config['table_name']
        saved_count = 0
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            for result in results:
                period = result['period']
                date = result['date']
                numbers = result['numbers']
                special = result.get('special')
                
                # 檢查是否已存在
                cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE Period = ?", (period,))
                if cursor.fetchone()[0] > 0:
                    continue  # 跳過已存在的記錄
                
                # 插入新記錄
                if lottery_type == 'powercolor':
                    cursor.execute('''
                        INSERT INTO Powercolor 
                        (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3,
                         Anumber4, Anumber5, Anumber6, Second_district)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (config['name'], period, date, *numbers, special))
                
                elif lottery_type == 'lotto649':
                    cursor.execute('''
                        INSERT INTO Lotto649 
                        (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3,
                         Anumber4, Anumber5, Anumber6, Special_number)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (config['name'], period, date, *numbers, special))
                
                elif lottery_type == 'dailycash':
                    cursor.execute('''
                        INSERT INTO DailyCash 
                        (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3,
                         Anumber4, Anumber5)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (config['name'], period, date, *numbers))
                
                saved_count += 1
            
            conn.commit()
            print(f"✅ {config['name']}: 儲存了 {saved_count} 筆新資料")
            
        except Exception as e:
            print(f"❌ 儲存 {config['name']} 資料失敗: {e}")
        finally:
            conn.close()
        
        return saved_count
    
    def get_recent_months_data(self, lottery_type: str, months: int = 6) -> Dict:
        """獲取最近幾個月的資料"""
        results = []
        current_date = datetime.now()
        
        for i in range(months):
            target_date = current_date - timedelta(days=i*30)
            year = target_date.year
            month = target_date.month
            
            monthly_results = self.get_monthly_data(lottery_type, year, month)
            results.extend(monthly_results)
            
            # 避免過於頻繁的請求
            time.sleep(2)
        
        # 儲存資料
        saved_count = self.save_lottery_data(results, lottery_type)
        
        return {
            'lottery_type': lottery_type,
            'total_fetched': len(results),
            'total_saved': saved_count,
            'months_processed': months
        }
    
    def scrape_all_lotteries(self, months: int = 6) -> Dict:
        """爬取所有彩票類型的資料"""
        print("🚀 開始爬取台灣彩券官方資料")
        print("=" * 50)
        
        # 創建資料表
        self.create_lottery_tables()
        
        results = {}
        
        for lottery_type in ['powercolor', 'lotto649', 'dailycash']:
            print(f"\n📊 處理 {self.lottery_configs[lottery_type]['name']}...")
            
            try:
                result = self.get_recent_months_data(lottery_type, months)
                results[lottery_type] = result
            except Exception as e:
                print(f"❌ 處理 {lottery_type} 時發生錯誤: {e}")
                results[lottery_type] = {'error': str(e)}
        
        return results

def test_scraper():
    """測試爬蟲功能"""
    scraper = TaiwanLotteryOfficialScraper()
    
    print("🧪 台灣彩券爬蟲測試")
    print("=" * 40)
    
    # 創建資料表
    scraper.create_lottery_tables()
    
    # 測試單一彩票類型
    test_lottery = 'powercolor'  # 先測試威力彩
    
    print(f"\n🎯 測試 {scraper.lottery_configs[test_lottery]['name']}...")
    
    # 獲取最新資料
    results = scraper.get_recent_data(test_lottery)
    
    if results:
        print(f"✅ 解析成功！找到 {len(results)} 筆資料:")
        for i, result in enumerate(results[:3], 1):  # 顯示前3筆
            print(f"   {i}. 期號: {result['period']}")
            print(f"      日期: {result['date']}")
            print(f"      號碼: {result['numbers']}")
            if result['special']:
                print(f"      特別號: {result['special']}")
        
        # 嘗試儲存資料
        saved_count = scraper.save_lottery_data(results, test_lottery)
        print(f"\n💾 儲存結果: {saved_count} 筆新資料")
        
    else:
        print("❌ 沒有解析到任何資料")
        print("💡 這可能是因為網站結構改變，需要調整解析邏輯")

def main():
    """主函數"""
    scraper = TaiwanLotteryOfficialScraper()
    
    print("🎯 台灣彩券官方資料爬蟲")
    print("=" * 40)
    
    # 首先進行測試
    test_scraper()
    
    # 如果測試成功，可以繼續爬取所有資料
    print("\n" + "=" * 50)
    print("如果測試成功，可以取消註解下面的代碼來爬取所有彩票資料")
    
    # # 爬取所有彩票的最新資料
    # results = scraper.scrape_all_lotteries(months=1)
    # 
    # print("\n📊 爬取結果總結:")
    # print("=" * 40)
    # 
    # total_saved = 0
    # for lottery_type, result in results.items():
    #     if 'error' not in result:
    #         config = scraper.lottery_configs[lottery_type]
    #         print(f"✅ {config['name']}:")
    #         print(f"   獲取: {result['total_fetched']} 筆")
    #         print(f"   儲存: {result['total_saved']} 筆新資料")
    #         total_saved += result['total_saved']
    #     else:
    #         config = scraper.lottery_configs[lottery_type]
    #         print(f"❌ {config['name']}: {result['error']}")
    # 
    # print(f"\n🎉 總計儲存 {total_saved} 筆新資料")

if __name__ == "__main__":
    main()