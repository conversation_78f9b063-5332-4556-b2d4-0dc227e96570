#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
台灣彩券網站結構分析工具
"""

import requests
from bs4 import BeautifulSoup
import urllib3
import re

# 抑制SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def analyze_website_structure(url, lottery_name):
    """分析網站結構"""
    print(f"🔍 分析 {lottery_name} 網站結構")
    print("=" * 50)
    
    session = requests.Session()
    session.verify = False
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    try:
        response = session.get(url, timeout=15)
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            print(f"📊 頁面基本資訊:")
            print(f"   內容大小: {len(response.content):,} bytes")
            print(f"   標題: {soup.title.string if soup.title else '無標題'}")
            
            # 分析頁面結構
            print(f"\n🏗️ HTML結構分析:")
            
            # 檢查所有div元素
            divs = soup.find_all('div')
            print(f"   找到 {len(divs)} 個 div 元素")
            
            # 尋找包含數字的div
            number_divs = []
            for div in divs:
                div_text = div.get_text().strip()
                if re.search(r'\d+', div_text) and len(div_text) < 100:
                    number_divs.append((div, div_text))
            
            print(f"   包含數字的div: {len(number_divs)} 個")
            
            # 顯示前10個包含數字的div
            for i, (div, text) in enumerate(number_divs[:10]):
                div_class = div.get('class', [])
                div_id = div.get('id', '')
                print(f"     {i+1}. Class: {div_class}, ID: {div_id}")
                print(f"        內容: {text[:80]}...")
            
            # 尋找特定關鍵字
            print(f"\n🔍 關鍵字搜尋:")
            keywords = ['開獎', '期號', '號碼', 'result', 'number', 'draw', 'lottery']
            
            for keyword in keywords:
                elements = soup.find_all(text=re.compile(keyword, re.I))
                if elements:
                    print(f"   '{keyword}': 找到 {len(elements)} 處")
                    # 顯示前3個匹配的上下文
                    for i, element in enumerate(elements[:3]):
                        parent = element.parent
                        if parent:
                            parent_class = parent.get('class', [])
                            parent_tag = parent.name
                            print(f"     {i+1}. 標籤: {parent_tag}, Class: {parent_class}")
                            print(f"        上下文: {str(element).strip()[:50]}...")
            
            # 檢查JavaScript
            print(f"\n💻 JavaScript分析:")
            scripts = soup.find_all('script')
            print(f"   找到 {len(scripts)} 個 script 標籤")
            
            js_keywords = ['lottery', 'number', 'draw', 'result', 'period']
            for script in scripts:
                if script.string:
                    for keyword in js_keywords:
                        if keyword.lower() in script.string.lower():
                            print(f"   Script包含關鍵字 '{keyword}'")
                            break
            
            # 檢查CSS類別
            print(f"\n🎨 CSS類別分析:")
            all_classes = []
            for element in soup.find_all(class_=True):
                all_classes.extend(element.get('class', []))
            
            # 統計最常見的類別
            from collections import Counter
            class_counts = Counter(all_classes)
            
            print("   最常見的CSS類別:")
            for class_name, count in class_counts.most_common(10):
                print(f"     {class_name}: {count} 次")
            
            # 尋找可能相關的CSS類別
            relevant_classes = []
            for class_name in class_counts:
                if any(keyword in class_name.lower() 
                      for keyword in ['number', 'result', 'draw', 'lottery', 'ball']):
                    relevant_classes.append(class_name)
            
            if relevant_classes:
                print(f"\n   可能相關的CSS類別:")
                for class_name in relevant_classes:
                    elements = soup.find_all(class_=class_name)
                    print(f"     {class_name}: {len(elements)} 個元素")
                    if elements:
                        sample_text = elements[0].get_text().strip()[:50]
                        print(f"       樣本內容: {sample_text}...")
            
            # 保存HTML到文件供進一步分析
            output_file = f"{lottery_name}_page_source.html"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(str(soup))
            print(f"\n💾 頁面原始碼已保存到: {output_file}")
            
        else:
            print(f"❌ 無法獲取網頁: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ 分析失敗: {e}")

def main():
    """主函數"""
    urls = [
        ('https://www.taiwanlottery.com/lotto/result/super_lotto638', '威力彩'),
        # ('https://www.taiwanlottery.com/lotto/result/lotto649', '大樂透'),
        # ('https://www.taiwanlottery.com/lotto/result/daily_cash', '今彩539')
    ]
    
    for url, name in urls:
        analyze_website_structure(url, name)
        print("\n" + "="*80 + "\n")

if __name__ == "__main__":
    main()