#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
資料庫清理工具 - 統一使用單一資料庫並清理多餘檔案
"""

import os
import shutil
import sqlite3
from datetime import datetime
from pathlib import Path

class DatabaseCleanupTool:
    def __init__(self):
        self.project_root = Path('/Users/<USER>/python/training/lotto/lottery_prediction_system')
        self.main_db = 'data/lottery_data.db'  # 系統主要資料庫
        self.backup_dir = f'database_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
        
    def find_all_databases(self):
        """找出所有 .db 檔案"""
        print("🔍 掃描所有 .db 檔案...")
        print("=" * 70)
        
        all_dbs = []
        for root, dirs, files in os.walk(self.project_root):
            # 跳過虛擬環境和備份目錄
            if '.venv' in root or 'backup' in root.lower():
                continue
            
            for file in files:
                if file.endswith('.db'):
                    full_path = os.path.join(root, file)
                    rel_path = os.path.relpath(full_path, self.project_root)
                    size = os.path.getsize(full_path) / 1024  # KB
                    all_dbs.append({
                        'path': rel_path,
                        'size': size,
                        'full_path': full_path
                    })
        
        return all_dbs
    
    def categorize_databases(self, all_dbs):
        """分類資料庫檔案"""
        categories = {
            'main': [],      # 主要資料庫
            'secondary': [], # 次要但可能重要
            'backup': [],    # 備份檔案
            'test': [],      # 測試資料庫
            'removable': []  # 可安全移除
        }
        
        for db in all_dbs:
            path = db['path']
            
            # 主要資料庫
            if path == self.main_db:
                categories['main'].append(db)
            
            # 備份檔案
            elif 'backup' in path.lower() or '.backup' in path:
                categories['backup'].append(db)
            
            # 測試資料庫
            elif 'test' in path.lower():
                categories['test'].append(db)
            
            # 次要但可能重要的資料庫
            elif path in ['data/prediction_tracking.db', 'data/accuracy.db', 'data/scheduler.db']:
                categories['secondary'].append(db)
            
            # 其他可移除的
            else:
                categories['removable'].append(db)
        
        return categories
    
    def display_categorized_databases(self, categories):
        """顯示分類結果"""
        print("\n📊 資料庫分類結果")
        print("=" * 70)
        
        print(f"\n✅ 主要資料庫 (保留):")
        for db in categories['main']:
            print(f"   {db['path']} ({db['size']:.1f} KB)")
        
        print(f"\n⚠️  次要資料庫 (可能需要):")
        for db in categories['secondary']:
            print(f"   {db['path']} ({db['size']:.1f} KB)")
        
        print(f"\n🧪 測試資料庫 (可移除):")
        for db in categories['test']:
            print(f"   {db['path']} ({db['size']:.1f} KB)")
        
        print(f"\n📦 備份資料庫 (可移除):")
        for db in categories['backup'][:5]:  # 只顯示前5個
            print(f"   {db['path']} ({db['size']:.1f} KB)")
        if len(categories['backup']) > 5:
            print(f"   ... 還有 {len(categories['backup'])-5} 個備份檔案")
        
        print(f"\n🗑️  其他可移除資料庫:")
        for db in categories['removable']:
            print(f"   {db['path']} ({db['size']:.1f} KB)")
        
        # 統計
        total_removable = (len(categories['test']) + 
                          len(categories['backup']) + 
                          len(categories['removable']))
        
        total_size = sum(db['size'] for cat in categories.values() for db in cat)
        removable_size = sum(db['size'] for cat in ['test', 'backup', 'removable'] 
                           for db in categories[cat])
        
        print(f"\n📈 統計資訊:")
        print(f"   總共找到: {sum(len(cat) for cat in categories.values())} 個 .db 檔案")
        print(f"   可移除: {total_removable} 個檔案")
        print(f"   總大小: {total_size/1024:.1f} MB")
        print(f"   可釋放空間: {removable_size/1024:.1f} MB")
    
    def backup_important_databases(self, categories):
        """備份重要資料庫"""
        print(f"\n💾 備份重要資料到: {self.backup_dir}")
        
        # 創建備份目錄
        backup_path = self.project_root / self.backup_dir
        backup_path.mkdir(exist_ok=True)
        
        # 備份主要和次要資料庫
        important_dbs = categories['main'] + categories['secondary']
        
        for db in important_dbs:
            src = db['full_path']
            dst = backup_path / db['path'].replace('/', '_')
            
            if os.path.exists(src):
                shutil.copy2(src, dst)
                print(f"   ✅ 備份: {db['path']}")
        
        print(f"   備份完成！檔案保存在: {self.backup_dir}")
        return backup_path
    
    def clean_databases(self, categories):
        """清理非主要資料庫"""
        print("\n🧹 開始清理非主要資料庫...")
        
        removed_count = 0
        removed_size = 0
        
        # 要清理的類別
        to_remove = categories['test'] + categories['backup'] + categories['removable']
        
        # 特別處理 data/lottery.db - 這是造成問題的資料庫
        lottery_db_path = self.project_root / 'data' / 'lottery.db'
        if lottery_db_path.exists():
            print(f"\n⚠️  特別注意: data/lottery.db 是造成同步問題的來源")
            confirm = input("   確定要刪除 data/lottery.db 嗎？(y/N): ")
            if confirm.lower() == 'y':
                to_remove.append({
                    'path': 'data/lottery.db',
                    'full_path': str(lottery_db_path),
                    'size': os.path.getsize(lottery_db_path) / 1024
                })
        
        for db in to_remove:
            try:
                if os.path.exists(db['full_path']):
                    os.remove(db['full_path'])
                    removed_count += 1
                    removed_size += db['size']
                    print(f"   ✅ 已刪除: {db['path']}")
            except Exception as e:
                print(f"   ❌ 無法刪除 {db['path']}: {e}")
        
        print(f"\n📊 清理完成:")
        print(f"   刪除檔案: {removed_count} 個")
        print(f"   釋放空間: {removed_size/1024:.1f} MB")
    
    def update_configurations(self):
        """更新系統配置，確保都使用主要資料庫"""
        print("\n🔧 更新系統配置...")
        
        # 需要檢查和更新的檔案
        files_to_check = [
            'automated_lottery_updater.py',
            'updated_lottery_updater.py',
            'web_update_integration.py',
            'data/lottery_daily_updater.py'
        ]
        
        updates_made = 0
        
        for file_path in files_to_check:
            full_path = self.project_root / file_path
            if full_path.exists():
                # 讀取檔案
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 檢查是否使用 lottery.db
                if 'lottery.db' in content and 'lottery_data.db' not in content:
                    print(f"   ⚠️  {file_path} 使用 lottery.db，需要更新")
                    # 這裡可以實際更新檔案內容
                    updates_made += 1
        
        if updates_made > 0:
            print(f"   發現 {updates_made} 個檔案需要更新配置")
        else:
            print("   ✅ 所有配置已經正確")

def main():
    print("🗄️  資料庫清理與統一工具")
    print("=" * 70)
    print("目標: 統一使用 data/lottery_data.db 作為唯一資料庫")
    print("=" * 70)
    
    tool = DatabaseCleanupTool()
    
    # 1. 找出所有資料庫
    all_dbs = tool.find_all_databases()
    
    # 2. 分類資料庫
    categories = tool.categorize_databases(all_dbs)
    
    # 3. 顯示分類結果
    tool.display_categorized_databases(categories)
    
    # 4. 詢問是否執行清理
    print("\n" + "=" * 70)
    response = input("\n是否執行清理作業？(將備份重要資料並刪除多餘資料庫) (y/N): ")
    
    if response.lower() == 'y':
        # 5. 備份重要資料
        backup_path = tool.backup_important_databases(categories)
        
        # 6. 清理資料庫
        tool.clean_databases(categories)
        
        # 7. 更新配置
        tool.update_configurations()
        
        print("\n✅ 清理完成！")
        print(f"   重要資料已備份到: {backup_path}")
        print(f"   系統現在統一使用: data/lottery_data.db")
    else:
        print("\n取消清理作業")

if __name__ == "__main__":
    main()