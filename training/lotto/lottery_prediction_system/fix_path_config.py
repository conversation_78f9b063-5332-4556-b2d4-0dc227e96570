#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路徑配置修復工具 - 解決開獎記錄更新功能路徑問題
"""

import os
import sys
from pathlib import Path

def fix_python_path():
    """標準化Python路徑配置"""
    # 獲取專案根目錄
    project_root = Path(__file__).parent.absolute()
    
    # 添加到Python路徑（如果不存在）
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    # 添加核心模組目錄
    core_paths = [
        project_root / 'data',
        project_root / 'prediction', 
        project_root / 'analysis',
        project_root / 'automation',
        project_root / 'display'
    ]
    
    for path in core_paths:
        if path.exists() and str(path) not in sys.path:
            sys.path.insert(0, str(path))
    
    return project_root

def get_project_paths():
    """獲取所有專案路徑"""
    project_root = fix_python_path()
    
    paths = {
        'project_root': project_root,
        'data_dir': project_root / 'data',
        'web_dir': project_root / 'web',
        'logs_dir': project_root / 'logs',
        'models_dir': project_root / 'models'
    }
    
    # 創建不存在的目錄
    for name, path in paths.items():
        if not path.exists() and name.endswith('_dir'):
            path.mkdir(exist_ok=True)
    
    return paths

def validate_imports():
    """驗證關鍵模組導入"""
    try:
        # 修復路徑
        fix_python_path()
        
        # 測試核心模組導入
        from data.db_manager import DBManager
        from data.lottery_daily_updater import LotteryDailyUpdater
        print("✅ 核心模組導入成功")
        
        # 測試更新模組導入
        try:
            from automated_lottery_updater import AutomatedLotteryUpdater
            print("✅ 自動更新模組導入成功")
        except ImportError as e:
            print(f"⚠️  自動更新模組導入失敗: {e}")
            
        return True
        
    except Exception as e:
        print(f"❌ 模組導入驗證失敗: {e}")
        return False

if __name__ == "__main__":
    print("🔧 執行路徑配置修復...")
    paths = get_project_paths()
    print(f"📁 專案根目錄: {paths['project_root']}")
    
    success = validate_imports()
    if success:
        print("✅ 路徑配置修復完成")
    else:
        print("❌ 路徑配置修復失敗")