#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手動彩票資料更新工具
用於更新最新的開獎號碼
"""

import sqlite3
import re
from datetime import datetime, timedelta
from typing import Dict, List, Tuple

class ManualLotteryUpdater:
    """手動彩票資料更新器"""
    
    def __init__(self, db_path: str = "data/lottery_data.db"):
        self.db_path = db_path
        
        # 彩票配置
        self.lottery_configs = {
            'powercolor': {
                'name': '威力彩',
                'table': 'Powercolor',
                'main_numbers': 6,
                'number_range': (1, 38),
                'special_range': (1, 8),
                'draw_days': [1, 4]  # 週一、週四
            },
            'lotto649': {
                'name': '大樂透',
                'table': 'Lotto649',
                'main_numbers': 6,
                'number_range': (1, 49),
                'special_range': (1, 49),
                'draw_days': [2, 5]  # 週二、週五
            },
            'dailycash': {
                'name': '今彩539',
                'table': 'DailyCash',
                'main_numbers': 5,
                'number_range': (1, 39),
                'special_range': None,
                'draw_days': [1, 2, 3, 4, 5, 6, 7]  # 每日
            }
        }
    
    def get_latest_period_info(self, lottery_type: str) -> Dict:
        """取得最新期號資訊"""
        if lottery_type not in self.lottery_configs:
            return None
        
        config = self.lottery_configs[lottery_type]
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 取得最新期號和日期
            cursor.execute(f"SELECT MAX(Period), MAX(Sdate) FROM {config['table']}")
            result = cursor.fetchone()
            
            latest_period = result[0] if result[0] else 0
            latest_date = result[1] if result[1] else "2020-01-01"
            
            # 預估下一期期號和日期
            next_period, next_date = self._estimate_next_draw(latest_period, latest_date, config)
            
            return {
                'lottery_name': config['name'],
                'latest_period': latest_period,
                'latest_date': latest_date,
                'next_period': next_period,
                'next_date': next_date,
                'config': config
            }
            
        finally:
            conn.close()
    
    def _estimate_next_draw(self, latest_period: int, latest_date: str, config: Dict) -> Tuple[int, str]:
        """預估下一期開獎日期和期號"""
        try:
            # 解析最新日期
            latest_dt = datetime.strptime(latest_date, '%Y-%m-%d')
            
            # 計算下一個開獎日
            current_dt = latest_dt + timedelta(days=1)
            
            # 尋找下一個開獎日
            for _ in range(10):  # 最多查找10天
                if current_dt.weekday() + 1 in config['draw_days']:
                    break
                current_dt += timedelta(days=1)
            
            # 預估下一期號
            if latest_period > 0:
                year = current_dt.year - 1911  # 民國年
                next_period = int(f"{year:03d}000{latest_period % 1000 + 1:03d}")
                
                # 處理跨年情況
                if current_dt.year != latest_dt.year:
                    next_period = int(f"{year:03d}000001")
                    
                # 處理期號格式 (確保是9位數)
                if next_period < 100000000:
                    next_period = int(f"114{next_period % 100000:06d}")
            else:
                # 如果沒有歷史資料，使用當前年份
                year = current_dt.year - 1911
                next_period = int(f"{year:03d}000001")
            
            return next_period, current_dt.strftime('%Y-%m-%d')
            
        except Exception as e:
            print(f"⚠️ 日期計算錯誤: {e}")
            return latest_period + 1, latest_date
    
    def validate_input(self, numbers: List[int], special: int, lottery_type: str) -> Tuple[bool, str]:
        """驗證輸入的號碼"""
        config = self.lottery_configs[lottery_type]
        
        # 檢查主要號碼數量
        if len(numbers) != config['main_numbers']:
            return False, f"需要 {config['main_numbers']} 個主要號碼"
        
        # 檢查主要號碼範圍
        min_num, max_num = config['number_range']
        for num in numbers:
            if not (min_num <= num <= max_num):
                return False, f"號碼必須在 {min_num}-{max_num} 之間"
        
        # 檢查重複號碼
        if len(set(numbers)) != len(numbers):
            return False, "主要號碼不能重複"
        
        # 檢查特別號
        if config['special_range']:
            if special is None:
                return False, "需要特別號"
            
            special_min, special_max = config['special_range']
            if not (special_min <= special <= special_max):
                return False, f"特別號必須在 {special_min}-{special_max} 之間"
        
        return True, "驗證通過"
    
    def add_draw_result(self, lottery_type: str, period: int, date: str, 
                       numbers: List[int], special: int = None) -> bool:
        """新增開獎結果"""
        
        if lottery_type not in self.lottery_configs:
            print(f"❌ 不支援的彩票類型: {lottery_type}")
            return False
        
        config = self.lottery_configs[lottery_type]
        
        # 驗證輸入
        is_valid, message = self.validate_input(numbers, special, lottery_type)
        if not is_valid:
            print(f"❌ 輸入驗證失敗: {message}")
            return False
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 檢查期號是否已存在
            cursor.execute(f"SELECT COUNT(*) FROM {config['table']} WHERE Period = ?", (period,))
            if cursor.fetchone()[0] > 0:
                print(f"⚠️ 期號 {period} 已存在，是否要更新？(y/n)")
                if input().lower() != 'y':
                    return False
                
                # 刪除現有記錄
                cursor.execute(f"DELETE FROM {config['table']} WHERE Period = ?", (period,))
            
            # 插入新記錄
            if lottery_type == 'powercolor':
                cursor.execute('''
                    INSERT INTO Powercolor 
                    (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3,
                     Anumber4, Anumber5, Anumber6, Second_district)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (config['name'], period, date, *numbers, special))
                
            elif lottery_type == 'lotto649':
                cursor.execute('''
                    INSERT INTO Lotto649 
                    (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3,
                     Anumber4, Anumber5, Anumber6, Special_number)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (config['name'], period, date, *numbers, special))
                
            else:  # dailycash
                cursor.execute('''
                    INSERT INTO DailyCash 
                    (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3,
                     Anumber4, Anumber5)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (config['name'], period, date, *numbers))
            
            conn.commit()
            print(f"✅ 成功新增 {config['name']} 期號 {period} 開獎結果")
            return True
            
        except Exception as e:
            print(f"❌ 資料庫操作失敗: {e}")
            return False
        finally:
            conn.close()
    
    def interactive_update(self, lottery_type: str = None):
        """互動式更新介面"""
        print("🎯 手動彩票資料更新工具")
        print("=" * 40)
        
        # 選擇彩票類型
        if not lottery_type:
            print("請選擇彩票類型:")
            for i, (key, config) in enumerate(self.lottery_configs.items(), 1):
                print(f"  {i}. {config['name']}")
            
            try:
                choice = int(input("請輸入選項 (1-3): "))
                lottery_type = list(self.lottery_configs.keys())[choice - 1]
            except (ValueError, IndexError):
                print("❌ 無效的選項")
                return
        
        if lottery_type not in self.lottery_configs:
            print("❌ 無效的彩票類型")
            return
        
        # 取得最新資訊
        info = self.get_latest_period_info(lottery_type)
        if not info:
            print("❌ 無法取得彩票資訊")
            return
        
        print(f"\n📊 {info['lottery_name']} 最新資訊:")
        print(f"   最新期號: {info['latest_period']}")
        print(f"   最新日期: {info['latest_date']}")
        print(f"   建議下期: {info['next_period']}")
        print(f"   建議日期: {info['next_date']}")
        
        print(f"\n📝 請輸入新的開獎資料:")
        
        try:
            # 輸入期號
            period_input = input(f"期號 (建議: {info['next_period']}): ").strip()
            period = int(period_input) if period_input else info['next_period']
            
            # 輸入日期
            date_input = input(f"日期 (YYYY-MM-DD, 建議: {info['next_date']}): ").strip()
            date = date_input if date_input else info['next_date']
            
            # 驗證日期格式
            datetime.strptime(date, '%Y-%m-%d')
            
            # 輸入號碼
            config = info['config']
            print(f"主要號碼 ({config['main_numbers']}個, 範圍: {config['number_range'][0]}-{config['number_range'][1]}):")
            numbers_input = input("請輸入號碼 (用空格或逗號分隔): ").strip()
            
            # 解析號碼
            numbers_str = re.findall(r'\d+', numbers_input)
            numbers = [int(n) for n in numbers_str[:config['main_numbers']]]
            
            if len(numbers) != config['main_numbers']:
                print(f"❌ 需要 {config['main_numbers']} 個號碼")
                return
            
            # 輸入特別號
            special = None
            if config['special_range']:
                special_input = input(f"特別號 (範圍: {config['special_range'][0]}-{config['special_range'][1]}): ").strip()
                special = int(special_input) if special_input else None
            
            # 確認輸入
            print(f"\n📋 確認資料:")
            print(f"   彩票: {info['lottery_name']}")
            print(f"   期號: {period}")
            print(f"   日期: {date}")
            print(f"   號碼: {numbers}")
            if special is not None:
                print(f"   特別號: {special}")
            
            confirm = input("\n確認新增? (y/n): ").lower()
            if confirm == 'y':
                success = self.add_draw_result(lottery_type, period, date, numbers, special)
                if success:
                    print("🎉 資料更新成功！")
                else:
                    print("❌ 資料更新失敗")
            else:
                print("取消操作")
                
        except ValueError as e:
            print(f"❌ 輸入格式錯誤: {e}")
        except Exception as e:
            print(f"❌ 操作失敗: {e}")

def main():
    """主函數"""
    updater = ManualLotteryUpdater()
    
    # 顯示各彩票最新狀態
    print("🔍 各彩票最新狀態:")
    print("=" * 50)
    
    for lottery_type in ['powercolor', 'lotto649', 'dailycash']:
        info = updater.get_latest_period_info(lottery_type)
        if info:
            print(f"📊 {info['lottery_name']}:")
            print(f"   最新期號: {info['latest_period']}")
            print(f"   最新日期: {info['latest_date']}")
            print()
    
    # 開始互動式更新
    updater.interactive_update()

if __name__ == "__main__":
    main()