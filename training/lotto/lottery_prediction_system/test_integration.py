#!/usr/bin/env python3
"""
彩票系统集成测试脚本
验证修复后的更新功能是否正常工作
"""

import json
import time
from datetime import datetime

def test_integration():
    """完整的集成测试"""
    
    print("🎯 彩票预测系统集成测试")
    print("=" * 60)
    
    # 1. 测试核心模块导入
    print("\n📦 测试核心模块导入...")
    try:
        from core import Config, VersionManager, CORE_MODULE_AVAILABLE
        print(f"✅ 核心模块导入成功! 状态: {CORE_MODULE_AVAILABLE}")
        
        config = Config()
        version_manager = VersionManager()
        print(f"📋 配置管理器: 已初始化")
        print(f"🔄 版本管理器: 已初始化")
    except Exception as e:
        print(f"❌ 核心模块导入失败: {e}")
        return False
    
    # 2. 测试增强型爬虫
    print("\n🔍 测试增强型爬虫...")
    try:
        from enhanced_scraper import EnhancedTaiwanLotteryScraper
        
        scraper = EnhancedTaiwanLotteryScraper()
        results = scraper.scrape_latest_results()
        
        print(f"✅ 爬虫测试成功! 获取到 {len(results)} 个结果")
        for result in results[:2]:  # 只显示前2个结果
            lottery_name = {
                'powercolor': '威力彩',
                'lotto649': '大乐透', 
                'dailycash': '今彩539'
            }.get(result.lottery_type, result.lottery_type)
            
            print(f"   📊 {lottery_name}: 期号 {result.period}, 号码 {result.main_numbers}")
            
    except Exception as e:
        print(f"❌ 爬虫测试失败: {e}")
        return False
    
    # 3. 测试更新器集成
    print("\n🔧 测试更新器集成...")
    try:
        from updated_lottery_updater import UpdatedLotteryUpdater
        
        updater = UpdatedLotteryUpdater()
        
        # 获取数据状态
        status = updater.get_data_status()
        print("📊 当前数据状态:")
        for lottery, info in status.items():
            status_emoji = "✅" if info['status'] == "良好" else "⚠️"
            print(f"   {status_emoji} {lottery}: 期号 {info['latest_period']}, {info['days_old']}天前")
        
        # 执行更新
        print("\n🚀 执行数据更新测试...")
        start_time = time.time()
        result = updater.update_lottery_data()
        execution_time = time.time() - start_time
        
        if result['success']:
            print(f"✅ 更新成功! 耗时 {execution_time:.2f} 秒")
            print(f"📈 新增记录: {result['total_new_records']} 笔")
            if result['lottery_results']:
                for lottery, stats in result['lottery_results'].items():
                    print(f"   • {lottery}: 新增 {stats['new_records']}, 跳过 {stats['skipped_records']}")
        else:
            print(f"❌ 更新失败: {result.get('error_message', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 更新器测试失败: {e}")
        return False
    
    # 4. 测试Web API集成
    print("\n🌐 测试Web API集成...")
    try:
        from web_api_integration import api_bp, updater as api_updater
        
        # 测试API更新器实例
        api_status = api_updater.get_data_status()
        print(f"✅ API更新器初始化成功")
        print(f"📋 API数据状态: {len(api_status)} 种彩票类型")
        
        # 验证蓝图注册
        print(f"🔌 API蓝图: {api_bp.name} 已准备就绪")
        
    except Exception as e:
        print(f"❌ Web API测试失败: {e}")
        return False
    
    # 5. 系统健康检查
    print("\n🔍 系统健康检查...")
    
    health_status = {
        'core_module': True,
        'enhanced_scraper': True,
        'updated_lottery_updater': True,
        'web_api_integration': True,
        'database_connection': True,
        'scraping_strategies': len(results) > 0
    }
    
    print("📋 系统组件状态:")
    for component, status in health_status.items():
        emoji = "✅" if status else "❌"
        print(f"   {emoji} {component.replace('_', ' ').title()}")
    
    all_healthy = all(health_status.values())
    
    print("\n" + "=" * 60)
    if all_healthy:
        print("🎉 集成测试完全成功!")
        print("💡 修复摘要:")
        print("   • 已解决核心模块导入错误")
        print("   • 已实现JavaScript网站爬取解决方案")
        print("   • 已修复数据库连接作用域问题")
        print("   • 已集成Web API自动更新功能")
        print("\n🚀 系统已准备就绪，请重启Web应用测试'立即更新'按钮!")
        
        # 提供启动命令
        print("\n📋 启动命令:")
        print("   cd /Users/<USER>/python/training/lotto/lottery_prediction_system/web")
        print("   python app.py")
        
    else:
        print("❌ 部分组件仍有问题，请检查上述错误信息")
    
    return all_healthy

if __name__ == "__main__":
    # 运行集成测试
    success = test_integration()
    
    if success:
        print("\n🎯 测试结论: 系统修复成功，更新功能已恢复正常!")
    else:
        print("\n⚠️  测试结论: 仍有问题需要解决")