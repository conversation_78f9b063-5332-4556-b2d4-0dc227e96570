#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復彩券開獎資料更新問題
針對台灣彩券網站更新的解決方案
"""

import sqlite3
import logging
from datetime import datetime, timedelta
import sys
import os

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('lottery_fix')

class LotteryDataFixer:
    """修復彩券資料更新問題"""
    
    def __init__(self, db_path='data/lottery_data.db'):
        self.db_path = db_path
        
    def check_database_status(self):
        """檢查資料庫狀態"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 檢查各彩券類型的最新資料
            lottery_types = [
                ('Powercolor', '威力彩'),
                ('Lotto649', '大樂透'),
                ('DailyCash', '今彩539')
            ]
            
            print("\n📊 資料庫狀態檢查：")
            print("-" * 50)
            
            for table, name in lottery_types:
                try:
                    cursor.execute(f"""
                        SELECT MAX(period), MAX(draw_date) 
                        FROM {table}
                    """)
                    result = cursor.fetchone()
                    
                    if result and result[0]:
                        period, date = result
                        print(f"{name:10} - 最新期號: {period}, 日期: {date}")
                        
                        # 計算距今天數
                        if date:
                            try:
                                last_date = datetime.strptime(date, '%Y-%m-%d')
                                days_ago = (datetime.now() - last_date).days
                                if days_ago > 0:
                                    print(f"           ⚠️  已經 {days_ago} 天沒有更新！")
                            except:
                                pass
                    else:
                        print(f"{name:10} - 無資料")
                        
                except Exception as e:
                    print(f"{name:10} - 表格不存在或錯誤: {e}")
                    
            conn.close()
            
        except Exception as e:
            logger.error(f"檢查資料庫失敗: {e}")
            
    def setup_auto_update(self):
        """設置自動更新排程"""
        print("\n⚙️  設置自動更新：")
        print("-" * 50)
        
        # 建立更新腳本
        update_script = """#!/bin/bash
# 彩券資料自動更新腳本

cd /Users/<USER>/python/training/lotto/lottery_prediction_system

# 執行Python更新程式
/usr/bin/python3 automated_lottery_updater.py >> logs/daily_update.log 2>&1

# 記錄執行時間
echo "Update completed at $(date)" >> logs/update_history.log
"""
        
        script_path = 'auto_update.sh'
        with open(script_path, 'w') as f:
            f.write(update_script)
        
        os.chmod(script_path, 0o755)
        print(f"✅ 已建立更新腳本: {script_path}")
        
        # 顯示crontab設置指令
        print("\n📅 請執行以下指令設置每日自動更新：")
        print("-" * 50)
        print("crontab -e")
        print("# 加入以下行（每天晚上10點執行）：")
        print(f"0 22 * * * {os.path.abspath(script_path)}")
        
    def manual_update_instruction(self):
        """手動更新指引"""
        print("\n📝 手動更新方法：")
        print("-" * 50)
        print("由於台灣彩券網站有防爬蟲機制，建議使用以下方法：")
        print()
        print("1. 使用 Playwright 爬蟲（推薦）：")
        print("   python playwright_scraper.py")
        print()
        print("2. 手動輸入最新開獎結果：")
        print("   - 訪問 https://www.taiwanlottery.com.tw")
        print("   - 查看最新開獎結果")
        print("   - 使用資料管理界面輸入")
        print()
        print("3. 使用替代資料源：")
        print("   - 考慮使用其他彩券資訊網站")
        print("   - API整合第三方服務")

def main():
    """主程式"""
    print("=" * 60)
    print("🎲 彩券資料更新問題修復工具")
    print("=" * 60)
    
    fixer = LotteryDataFixer()
    
    # 檢查資料庫狀態
    fixer.check_database_status()
    
    # 設置自動更新
    fixer.setup_auto_update()
    
    # 手動更新指引
    fixer.manual_update_instruction()
    
    print("\n" + "=" * 60)
    print("修復建議已生成完成！")
    print("請根據上述指引進行操作。")
    print("=" * 60)

if __name__ == "__main__":
    main()