#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正虛假資料的專用工具
"""

from data_integrity_cleaner import DataIntegrityCleaner
import sqlite3
from datetime import datetime

def main():
    """清理虛假資料並顯示結果"""
    print("🚨 清理台灣彩券資料庫中的虛假資料")
    print("=" * 50)
    
    cleaner = DataIntegrityCleaner()
    
    # 分析問題
    analysis = cleaner.analyze_suspicious_data()
    
    print(f"\n💡 發現的問題總結:")
    print(f"=" * 30)
    
    total_issues = 0
    for table, data in analysis.items():
        if data['suspicious']:
            print(f"\n📊 {data['name']}:")
            print(f"   🚨 可疑記錄: {len(data['suspicious'])} 筆")
            print(f"   📅 開獎規則: {data['rules']}")
            
            print(f"   詳細問題:")
            for period, date, issues in data['suspicious']:
                print(f"      • 期號{period} ({date}): {', '.join(issues)}")
            
            total_issues += len(data['suspicious'])
    
    print(f"\n📈 總問題數: {total_issues} 筆虛假/錯誤資料")
    
    if total_issues > 0:
        print(f"\n🔧 自動清理這些虛假資料...")
        
        # 備份資料庫
        backup_path = cleaner.backup_database("fake_data_cleanup")
        
        # 執行清理（不需要確認）
        deleted = cleaner.clean_suspicious_data(analysis, confirm=False)
        
        print(f"\n✅ 清理完成！")
        print(f"   📁 備份位置: {backup_path}")
        print(f"   🗑️  已刪除: {deleted} 筆虛假資料")
        
        # 檢查清理後的狀態
        print(f"\n🔍 清理後資料庫狀態:")
        check_current_status()
    else:
        print(f"\n✅ 資料庫中沒有發現虛假資料！")

def check_current_status():
    """檢查當前資料庫狀態"""
    conn = sqlite3.connect("data/lottery_data.db")
    cursor = conn.cursor()
    
    lotteries = [
        ('威力彩', 'Powercolor'),
        ('大樂透', 'Lotto649'), 
        ('今彩539', 'DailyCash')
    ]
    
    for name, table in lotteries:
        cursor.execute(f"SELECT MAX(Period), MAX(Sdate), COUNT(*) FROM {table}")
        result = cursor.fetchone()
        latest_period, latest_date, total_count = result
        
        print(f"   📊 {name}: {total_count} 筆記錄")
        print(f"      最新期號: {latest_period}")
        print(f"      最新日期: {latest_date}")
    
    conn.close()

if __name__ == "__main__":
    main()