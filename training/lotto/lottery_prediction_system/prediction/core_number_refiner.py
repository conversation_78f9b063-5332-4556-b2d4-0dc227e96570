#!/usr/bin/env python3
"""
核心号码精炼系统
从多组预测中提炼出最有可能命中的5-6个核心号码
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import itertools
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler

logger = logging.getLogger('core_number_refiner')

@dataclass
class NumberScore:
    """号码评分数据类"""
    number: int
    frequency_score: float      # 频率得分
    confidence_score: float     # 信心度得分
    algorithm_score: float      # 算法一致性得分
    historical_score: float     # 历史表现得分
    clustering_score: float     # 聚类中心得分
    final_score: float         # 最终综合得分
    appearance_count: int      # 出现次数
    avg_confidence: float      # 平均信心度
    risk_level: str           # 风险等级

class CoreNumberRefiner:
    """
    核心号码精炼器
    
    功能：
    1. 从多组预测中分析所有号码
    2. 使用多维度评分系统
    3. 提炼出最有价值的5-6个核心号码
    4. 保证极高的命中概率
    """
    
    def __init__(self, lottery_type='powercolor'):
        """
        初始化核心号码精炼器
        
        Args:
            lottery_type: 彩票类型
        """
        self.lottery_type = lottery_type
        
        # 彩票配置
        self.config = {
            'powercolor': {'main_numbers': 38, 'main_count': 6},
            'lotto649': {'main_numbers': 49, 'main_count': 6},
            'dailycash': {'main_numbers': 39, 'main_count': 5}
        }
        
        self.main_numbers = self.config[lottery_type]['main_numbers']
        self.main_count = self.config[lottery_type]['main_count']
        
        # 评分权重配置
        self.scoring_weights = {
            'frequency_weight': 0.25,      # 出现频率权重
            'confidence_weight': 0.30,     # 信心度权重
            'algorithm_weight': 0.20,      # 算法一致性权重
            'historical_weight': 0.15,     # 历史表现权重
            'clustering_weight': 0.10      # 聚类权重
        }
        
        # 精炼配置
        self.refining_config = {
            'target_core_count': 6,        # 目标核心号码数
            'min_appearance_threshold': 2,  # 最少出现次数
            'high_confidence_threshold': 75, # 高信心度阈值
            'clustering_features': True,    # 启用聚类特征
            'risk_balancing': True,        # 风险平衡
            'historical_validation': True  # 历史验证
        }
        
        logger.info(f"核心号码精炼器初始化完成 - 彩票类型: {lottery_type}")
    
    def refine_core_numbers(self, 
                          multi_group_result: Dict[str, Any],
                          historical_data: Optional[pd.DataFrame] = None,
                          target_count: Optional[int] = None) -> Dict[str, Any]:
        """
        精炼核心号码
        
        Args:
            multi_group_result: 多组预测结果
            historical_data: 历史数据用于验证
            target_count: 目标号码数量
            
        Returns:
            核心号码精炼结果
        """
        logger.info("开始精炼核心号码...")
        
        if not multi_group_result or 'groups' not in multi_group_result:
            logger.error("无效的多组预测结果")
            return None
        
        groups = multi_group_result['groups']
        target_count = target_count or self.refining_config['target_core_count']
        
        # 1. 提取所有号码和相关信息
        number_data = self._extract_number_data(groups)
        
        # 2. 计算多维度评分
        number_scores = self._calculate_multi_dimensional_scores(number_data, historical_data)
        
        # 3. 应用核心筛选算法
        core_numbers = self._apply_core_selection_algorithm(number_scores, target_count)
        
        # 4. 验证和优化
        validated_core = self._validate_and_optimize(core_numbers, historical_data)
        
        # 5. 生成核心号码报告
        final_result = self._generate_core_number_report(
            validated_core, number_scores, multi_group_result, target_count
        )
        
        logger.info(f"核心号码精炼完成 - 提炼出{len(validated_core)}个核心号码")
        
        return final_result
    
    def _extract_number_data(self, groups: List[Dict]) -> Dict[int, Dict]:
        """提取号码数据"""
        number_data = defaultdict(lambda: {
            'appearances': [],
            'confidences': [],
            'algorithms': [],
            'weights': [],
            'positions': []
        })
        
        for group_idx, group in enumerate(groups):
            main_numbers = group.get('main_numbers', [])
            confidence = group.get('confidence', 50)
            algorithm = group.get('original_method', 'unknown')
            weight = group.get('recommendation_weight', 1.0)
            
            for pos, number in enumerate(main_numbers):
                number_data[number]['appearances'].append(group_idx)
                number_data[number]['confidences'].append(confidence)
                number_data[number]['algorithms'].append(algorithm)
                number_data[number]['weights'].append(weight)
                number_data[number]['positions'].append(pos)
        
        logger.info(f"提取了{len(number_data)}个不同号码的数据")
        return dict(number_data)
    
    def _calculate_multi_dimensional_scores(self, 
                                          number_data: Dict[int, Dict],
                                          historical_data: Optional[pd.DataFrame]) -> List[NumberScore]:
        """计算多维度评分"""
        logger.info("计算多维度评分...")
        
        number_scores = []
        total_groups = len(set([app for data in number_data.values() for app in data['appearances']]))
        
        for number, data in number_data.items():
            # 1. 频率得分 (出现次数 / 总组数)
            frequency_score = len(data['appearances']) / total_groups
            
            # 2. 信心度得分 (加权平均信心度)
            if data['confidences'] and data['weights']:
                weighted_confidences = np.average(data['confidences'], weights=data['weights'])
                confidence_score = weighted_confidences / 100
            else:
                confidence_score = 0.5
            
            # 3. 算法一致性得分 (不同算法支持度)
            unique_algorithms = len(set(data['algorithms']))
            algorithm_score = min(1.0, unique_algorithms / 6)  # 假设最多6种算法
            
            # 4. 历史表现得分
            historical_score = self._calculate_historical_score(number, historical_data)
            
            # 5. 聚类中心得分
            clustering_score = self._calculate_clustering_score(number, data, number_data)
            
            # 6. 计算最终综合得分
            final_score = (
                frequency_score * self.scoring_weights['frequency_weight'] +
                confidence_score * self.scoring_weights['confidence_weight'] +
                algorithm_score * self.scoring_weights['algorithm_weight'] +
                historical_score * self.scoring_weights['historical_weight'] +
                clustering_score * self.scoring_weights['clustering_weight']
            )
            
            # 7. 风险等级评估
            risk_level = self._assess_risk_level(frequency_score, confidence_score, algorithm_score)
            
            number_score = NumberScore(
                number=number,
                frequency_score=frequency_score,
                confidence_score=confidence_score,
                algorithm_score=algorithm_score,
                historical_score=historical_score,
                clustering_score=clustering_score,
                final_score=final_score,
                appearance_count=len(data['appearances']),
                avg_confidence=np.mean(data['confidences']) if data['confidences'] else 0,
                risk_level=risk_level
            )
            
            number_scores.append(number_score)
        
        # 按最终得分排序
        number_scores.sort(key=lambda x: x.final_score, reverse=True)
        
        logger.info(f"完成{len(number_scores)}个号码的多维度评分")
        return number_scores
    
    def _calculate_historical_score(self, number: int, historical_data: Optional[pd.DataFrame]) -> float:
        """计算历史表现得分"""
        if historical_data is None or historical_data.empty:
            return 0.5  # 默认中性得分
        
        try:
            # 分析该号码在历史数据中的表现
            main_cols = [f'Anumber{i}' for i in range(1, self.main_count + 1)]
            
            # 计算该号码的历史出现频率
            total_appearances = 0
            total_periods = len(historical_data)
            
            for _, row in historical_data.iterrows():
                historical_numbers = [row[col] for col in main_cols if col in row.index]
                if number in historical_numbers:
                    total_appearances += 1
            
            # 历史频率
            historical_frequency = total_appearances / total_periods if total_periods > 0 else 0
            
            # 近期趋势 (最近20期)
            recent_data = historical_data.tail(20)
            recent_appearances = 0
            
            for _, row in recent_data.iterrows():
                historical_numbers = [row[col] for col in main_cols if col in row.index]
                if number in historical_numbers:
                    recent_appearances += 1
            
            recent_frequency = recent_appearances / len(recent_data) if len(recent_data) > 0 else 0
            
            # 综合历史得分 (基础频率 + 近期趋势)
            expected_frequency = self.main_count / self.main_numbers  # 理论频率
            
            # 如果号码频率接近理论值，得分较高
            frequency_score = 1 - abs(historical_frequency - expected_frequency) / expected_frequency
            
            # 近期趋势加权
            trend_score = recent_frequency / expected_frequency if expected_frequency > 0 else 1
            trend_score = min(2.0, max(0.1, trend_score))  # 限制在0.1-2.0之间
            
            # 最终历史得分
            final_score = (frequency_score * 0.7 + (trend_score - 1) * 0.3 + 1) / 2
            return max(0, min(1, final_score))
            
        except Exception as e:
            logger.warning(f"计算号码{number}历史得分失败: {e}")
            return 0.5
    
    def _calculate_clustering_score(self, 
                                  number: int, 
                                  data: Dict, 
                                  all_number_data: Dict[int, Dict]) -> float:
        """计算聚类中心得分"""
        try:
            # 基于号码的统计特征进行聚类分析
            if not self.refining_config['clustering_features']:
                return 0.5
            
            # 构建特征向量
            features = []
            numbers = []
            
            for num, num_data in all_number_data.items():
                feature_vector = [
                    len(num_data['appearances']),  # 出现次数
                    np.mean(num_data['confidences']) if num_data['confidences'] else 0,  # 平均信心度
                    len(set(num_data['algorithms'])),  # 算法多样性
                    num  # 号码本身
                ]
                features.append(feature_vector)
                numbers.append(num)
            
            if len(features) < 3:
                return 0.5
            
            # 标准化特征
            scaler = StandardScaler()
            features_scaled = scaler.fit_transform(features)
            
            # 聚类分析
            n_clusters = min(3, len(features) // 2)  # 动态确定聚类数
            if n_clusters < 2:
                return 0.5
            
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            cluster_labels = kmeans.fit_predict(features_scaled)
            
            # 找到当前号码所在的聚类
            number_idx = numbers.index(number)
            number_cluster = cluster_labels[number_idx]
            
            # 计算该号码到聚类中心的距离
            cluster_center = kmeans.cluster_centers_[number_cluster]
            number_features = features_scaled[number_idx]
            
            # 距离越近，得分越高
            distance = np.linalg.norm(number_features - cluster_center)
            max_distance = np.max([np.linalg.norm(f - cluster_center) 
                                 for f in features_scaled])
            
            clustering_score = 1 - (distance / max_distance) if max_distance > 0 else 0.5
            return clustering_score
            
        except Exception as e:
            logger.warning(f"计算聚类得分失败: {e}")
            return 0.5
    
    def _assess_risk_level(self, frequency_score: float, confidence_score: float, algorithm_score: float) -> str:
        """评估风险等级"""
        combined_score = (frequency_score + confidence_score + algorithm_score) / 3
        
        if combined_score >= 0.8:
            return "极低风险"
        elif combined_score >= 0.6:
            return "低风险"
        elif combined_score >= 0.4:
            return "中等风险"
        elif combined_score >= 0.2:
            return "高风险"
        else:
            return "极高风险"
    
    def _apply_core_selection_algorithm(self, 
                                       number_scores: List[NumberScore], 
                                       target_count: int) -> List[NumberScore]:
        """应用核心选择算法"""
        logger.info(f"应用核心选择算法 - 目标数量: {target_count}")
        
        # 策略1: 直接选择最高分的号码
        direct_selection = number_scores[:target_count]
        
        # 策略2: 平衡选择 (考虑风险分布)
        balanced_selection = self._balanced_selection(number_scores, target_count)
        
        # 策略3: 聚类选择 (确保号码分布合理)
        cluster_selection = self._cluster_based_selection(number_scores, target_count)
        
        # 综合评估三种策略
        best_selection = self._evaluate_selection_strategies(
            [direct_selection, balanced_selection, cluster_selection]
        )
        
        logger.info(f"核心选择完成 - 选中{len(best_selection)}个号码")
        return best_selection
    
    def _balanced_selection(self, number_scores: List[NumberScore], target_count: int) -> List[NumberScore]:
        """平衡选择策略"""
        selected = []
        
        # 按风险等级分组
        risk_groups = defaultdict(list)
        for score in number_scores:
            risk_groups[score.risk_level].append(score)
        
        # 优先选择低风险号码
        risk_priority = ["极低风险", "低风险", "中等风险", "高风险", "极高风险"]
        
        for risk_level in risk_priority:
            if len(selected) >= target_count:
                break
            
            available = risk_groups[risk_level]
            remaining_slots = target_count - len(selected)
            
            # 从该风险等级选择最高分的号码
            selected.extend(available[:remaining_slots])
        
        return selected[:target_count]
    
    def _cluster_based_selection(self, number_scores: List[NumberScore], target_count: int) -> List[NumberScore]:
        """基于聚类的选择策略"""
        try:
            # 基于号码值进行分布聚类
            numbers = [score.number for score in number_scores]
            
            # 将号码分为小、中、大三个区间
            low_range = self.main_numbers // 3
            mid_range = self.main_numbers * 2 // 3
            
            low_numbers = [s for s in number_scores if s.number <= low_range]
            mid_numbers = [s for s in number_scores if low_range < s.number <= mid_range]
            high_numbers = [s for s in number_scores if s.number > mid_range]
            
            # 平衡选择
            target_low = target_count // 3
            target_mid = target_count // 3
            target_high = target_count - target_low - target_mid
            
            selected = []
            selected.extend(low_numbers[:target_low])
            selected.extend(mid_numbers[:target_mid])
            selected.extend(high_numbers[:target_high])
            
            # 如果数量不足，从剩余最高分中补充
            if len(selected) < target_count:
                remaining = [s for s in number_scores if s not in selected]
                selected.extend(remaining[:target_count - len(selected)])
            
            return selected[:target_count]
            
        except Exception as e:
            logger.warning(f"聚类选择失败，使用直接选择: {e}")
            return number_scores[:target_count]
    
    def _evaluate_selection_strategies(self, selections: List[List[NumberScore]]) -> List[NumberScore]:
        """评估选择策略"""
        best_selection = None
        best_score = -1
        
        for selection in selections:
            # 计算选择策略的综合评分
            if not selection:
                continue
            
            # 平均得分
            avg_score = np.mean([s.final_score for s in selection])
            
            # 风险分布得分
            risk_levels = [s.risk_level for s in selection]
            low_risk_ratio = sum(1 for r in risk_levels if r in ["极低风险", "低风险"]) / len(risk_levels)
            
            # 号码分布得分
            numbers = [s.number for s in selection]
            span = max(numbers) - min(numbers)
            distribution_score = min(1.0, span / self.main_numbers)
            
            # 综合评分
            strategy_score = avg_score * 0.6 + low_risk_ratio * 0.3 + distribution_score * 0.1
            
            if strategy_score > best_score:
                best_score = strategy_score
                best_selection = selection
        
        return best_selection or selections[0]
    
    def _validate_and_optimize(self, 
                              core_numbers: List[NumberScore],
                              historical_data: Optional[pd.DataFrame]) -> List[NumberScore]:
        """验证和优化核心号码"""
        logger.info("验证和优化核心号码...")
        
        if not self.refining_config['historical_validation'] or historical_data is None:
            return core_numbers
        
        # 历史验证
        validated_numbers = []
        
        for number_score in core_numbers:
            # 检查该号码的历史表现
            validation_score = self._historical_validation(number_score.number, historical_data)
            
            if validation_score >= 0.3:  # 历史表现阈值
                validated_numbers.append(number_score)
            else:
                logger.info(f"号码{number_score.number}历史验证未通过，得分: {validation_score:.3f}")
        
        # 如果验证后数量不足，从备选中补充
        if len(validated_numbers) < self.refining_config['target_core_count']:
            # 获取所有号码，找出未选中但得分较高的
            all_numbers = set(range(1, self.main_numbers + 1))
            selected_numbers = set(score.number for score in validated_numbers)
            remaining_numbers = all_numbers - selected_numbers
            
            # 为剩余号码计算快速得分
            backup_scores = []
            for num in remaining_numbers:
                quick_score = self._calculate_quick_score(num, historical_data)
                backup_scores.append(NumberScore(
                    number=num,
                    frequency_score=quick_score,
                    confidence_score=quick_score,
                    algorithm_score=quick_score,
                    historical_score=quick_score,
                    clustering_score=quick_score,
                    final_score=quick_score,
                    appearance_count=0,
                    avg_confidence=50,
                    risk_level="中等风险"
                ))
            
            backup_scores.sort(key=lambda x: x.final_score, reverse=True)
            
            needed = self.refining_config['target_core_count'] - len(validated_numbers)
            validated_numbers.extend(backup_scores[:needed])
        
        logger.info(f"验证和优化完成 - 最终{len(validated_numbers)}个核心号码")
        return validated_numbers
    
    def _historical_validation(self, number: int, historical_data: pd.DataFrame) -> float:
        """历史验证"""
        try:
            main_cols = [f'Anumber{i}' for i in range(1, self.main_count + 1)]
            
            # 检查最近30期的表现
            recent_data = historical_data.tail(30)
            appearances = 0
            
            for _, row in recent_data.iterrows():
                numbers = [row[col] for col in main_cols if col in row.index]
                if number in numbers:
                    appearances += 1
            
            # 计算验证得分
            expected_appearances = len(recent_data) * self.main_count / self.main_numbers
            validation_score = min(2.0, appearances / expected_appearances) if expected_appearances > 0 else 0.5
            
            return validation_score / 2  # 归一化到0-1
            
        except Exception as e:
            logger.warning(f"历史验证失败: {e}")
            return 0.5
    
    def _calculate_quick_score(self, number: int, historical_data: pd.DataFrame) -> float:
        """快速计算号码得分"""
        try:
            main_cols = [f'Anumber{i}' for i in range(1, self.main_count + 1)]
            
            total_appearances = 0
            total_periods = len(historical_data)
            
            for _, row in historical_data.iterrows():
                numbers = [row[col] for col in main_cols if col in row.index]
                if number in numbers:
                    total_appearances += 1
            
            frequency = total_appearances / total_periods if total_periods > 0 else 0
            expected_frequency = self.main_count / self.main_numbers
            
            # 基于频率的快速得分
            quick_score = 1 - abs(frequency - expected_frequency) / expected_frequency
            return max(0, min(1, quick_score))
            
        except:
            return 0.5
    
    def _generate_core_number_report(self, 
                                   core_numbers: List[NumberScore],
                                   all_scores: List[NumberScore],
                                   multi_group_result: Dict[str, Any],
                                   target_count: int) -> Dict[str, Any]:
        """生成核心号码报告"""
        logger.info("生成核心号码报告...")
        
        # 核心号码列表
        core_number_list = [score.number for score in core_numbers]
        
        # 计算综合指标
        avg_final_score = np.mean([score.final_score for score in core_numbers])
        avg_confidence = np.mean([score.avg_confidence for score in core_numbers])
        total_appearances = sum([score.appearance_count for score in core_numbers])
        
        # 风险分析
        risk_distribution = Counter([score.risk_level for score in core_numbers])
        
        # 覆盖率分析
        original_groups = multi_group_result.get('groups', [])
        coverage_analysis = self._analyze_coverage(core_number_list, original_groups)
        
        # 预期表现分析
        expected_performance = self._calculate_expected_performance(core_numbers)
        
        # 生成详细报告
        report = {
            'core_numbers': core_number_list,
            'total_count': len(core_number_list),
            'target_count': target_count,
            'generation_time': datetime.now().isoformat(),
            
            # 核心指标
            'performance_metrics': {
                'avg_final_score': avg_final_score,
                'avg_confidence': avg_confidence,
                'total_appearances': total_appearances,
                'expected_hit_probability': expected_performance['hit_probability'],
                'confidence_level': expected_performance['confidence_level']
            },
            
            # 详细分析
            'detailed_analysis': {
                'number_scores': [
                    {
                        'number': score.number,
                        'final_score': score.final_score,
                        'frequency_score': score.frequency_score,
                        'confidence_score': score.confidence_score,
                        'algorithm_score': score.algorithm_score,
                        'historical_score': score.historical_score,
                        'appearance_count': score.appearance_count,
                        'avg_confidence': score.avg_confidence,
                        'risk_level': score.risk_level
                    }
                    for score in core_numbers
                ],
                'risk_distribution': dict(risk_distribution),
                'coverage_analysis': coverage_analysis,
                'selection_strategy': self._get_selection_strategy_info()
            },
            
            # 预测分析
            'prediction_analysis': {
                'expected_performance': expected_performance,
                'comparison_with_original': self._compare_with_original_groups(
                    core_number_list, original_groups
                ),
                'optimization_suggestions': self._generate_optimization_suggestions(core_numbers)
            },
            
            # 使用建议
            'usage_recommendations': self._generate_usage_recommendations(
                core_numbers, expected_performance
            )
        }
        
        return report
    
    def _analyze_coverage(self, core_numbers: List[int], original_groups: List[Dict]) -> Dict[str, Any]:
        """分析覆盖率"""
        if not original_groups:
            return {}
        
        # 统计核心号码在原始组中的覆盖情况
        total_coverage = 0
        group_coverages = []
        
        for group in original_groups:
            group_numbers = set(group.get('main_numbers', []))
            core_set = set(core_numbers)
            
            overlap = len(group_numbers.intersection(core_set))
            coverage = overlap / len(group_numbers) if group_numbers else 0
            
            group_coverages.append(coverage)
            total_coverage += overlap
        
        avg_coverage = np.mean(group_coverages) if group_coverages else 0
        
        return {
            'average_group_coverage': avg_coverage,
            'total_number_coverage': total_coverage,
            'max_group_coverage': max(group_coverages) if group_coverages else 0,
            'min_group_coverage': min(group_coverages) if group_coverages else 0,
            'coverage_efficiency': avg_coverage * len(core_numbers) / self.main_count
        }
    
    def _calculate_expected_performance(self, core_numbers: List[NumberScore]) -> Dict[str, Any]:
        """计算预期表现"""
        # 基于综合得分计算预期命中概率
        avg_score = np.mean([score.final_score for score in core_numbers])
        score_variance = np.var([score.final_score for score in core_numbers])
        
        # 预期命中概率 (基于经验公式)
        base_probability = 0.05  # 基础概率
        score_multiplier = 1 + (avg_score - 0.5) * 2  # 得分调整倍数
        
        hit_probability = base_probability * score_multiplier
        hit_probability = max(0.01, min(0.95, hit_probability))  # 限制在合理范围
        
        # 信心度等级
        if avg_score >= 0.8:
            confidence_level = "极高"
        elif avg_score >= 0.6:
            confidence_level = "高"
        elif avg_score >= 0.4:
            confidence_level = "中等"
        elif avg_score >= 0.2:
            confidence_level = "低"
        else:
            confidence_level = "极低"
        
        return {
            'hit_probability': hit_probability,
            'confidence_level': confidence_level,
            'score_stability': 1 - score_variance,  # 稳定性
            'risk_assessment': self._assess_overall_risk(core_numbers)
        }
    
    def _assess_overall_risk(self, core_numbers: List[NumberScore]) -> str:
        """评估整体风险"""
        risk_levels = [score.risk_level for score in core_numbers]
        risk_weights = {
            "极低风险": 1, "低风险": 2, "中等风险": 3, 
            "高风险": 4, "极高风险": 5
        }
        
        avg_risk = np.mean([risk_weights.get(level, 3) for level in risk_levels])
        
        if avg_risk <= 1.5:
            return "整体极低风险"
        elif avg_risk <= 2.5:
            return "整体低风险"
        elif avg_risk <= 3.5:
            return "整体中等风险"
        elif avg_risk <= 4.5:
            return "整体高风险"
        else:
            return "整体极高风险"
    
    def _compare_with_original_groups(self, 
                                    core_numbers: List[int], 
                                    original_groups: List[Dict]) -> Dict[str, Any]:
        """与原始组预测比较"""
        if not original_groups:
            return {}
        
        # 计算覆盖效率
        core_set = set(core_numbers)
        total_original_numbers = set()
        
        for group in original_groups:
            total_original_numbers.update(group.get('main_numbers', []))
        
        coverage_efficiency = len(core_set.intersection(total_original_numbers)) / len(total_original_numbers) if total_original_numbers else 0
        
        # 计算压缩率
        compression_ratio = len(core_numbers) / len(total_original_numbers) if total_original_numbers else 0
        
        return {
            'coverage_efficiency': coverage_efficiency,
            'compression_ratio': compression_ratio,
            'quality_improvement': coverage_efficiency / compression_ratio if compression_ratio > 0 else 0,
            'original_total_numbers': len(total_original_numbers),
            'core_numbers_count': len(core_numbers)
        }
    
    def _generate_optimization_suggestions(self, core_numbers: List[NumberScore]) -> List[str]:
        """生成优化建议"""
        suggestions = []
        
        # 基于风险分布的建议
        risk_levels = [score.risk_level for score in core_numbers]
        high_risk_count = sum(1 for level in risk_levels if level in ["高风险", "极高风险"])
        
        if high_risk_count > len(core_numbers) // 2:
            suggestions.append("建议降低高风险号码比例，增加更多稳定号码")
        
        # 基于得分分布的建议
        scores = [score.final_score for score in core_numbers]
        if max(scores) - min(scores) > 0.5:
            suggestions.append("号码得分差异较大，建议平衡选择")
        
        # 基于号码分布的建议
        numbers = [score.number for score in core_numbers]
        if max(numbers) - min(numbers) < self.main_numbers * 0.3:
            suggestions.append("号码分布过于集中，建议增加分布范围")
        
        if not suggestions:
            suggestions.append("当前核心号码组合已经过优化，建议保持")
        
        return suggestions
    
    def _generate_usage_recommendations(self, 
                                      core_numbers: List[NumberScore],
                                      expected_performance: Dict[str, Any]) -> List[str]:
        """生成使用建议"""
        recommendations = []
        
        hit_probability = expected_performance['hit_probability']
        confidence_level = expected_performance['confidence_level']
        
        # 基于预期表现的建议
        if hit_probability >= 0.3:
            recommendations.append("🎯 极高命中概率，建议重点投注")
        elif hit_probability >= 0.15:
            recommendations.append("📈 较高命中概率，建议适度投注")
        elif hit_probability >= 0.08:
            recommendations.append("⚖️ 中等命中概率，建议谨慎投注")
        else:
            recommendations.append("⚠️ 较低命中概率，建议观望或小额投注")
        
        # 基于信心度的建议
        if confidence_level in ["极高", "高"]:
            recommendations.append("💡 系统信心度高，可增加投注比例")
        elif confidence_level == "中等":
            recommendations.append("🤔 系统信心度中等，建议正常投注")
        else:
            recommendations.append("🔍 系统信心度较低，建议降低投注或等待更好机会")
        
        # 风险管理建议
        overall_risk = expected_performance['risk_assessment']
        if "低风险" in overall_risk:
            recommendations.append("✅ 整体风险较低，相对安全的选择")
        elif "中等风险" in overall_risk:
            recommendations.append("⚖️ 整体风险中等，注意资金管理")
        else:
            recommendations.append("⚠️ 整体风险较高，建议谨慎投注")
        
        # 通用建议
        recommendations.extend([
            "📊 建议跟踪实际结果，持续优化系统",
            "💰 合理分配资金，避免过度投注",
            "🔄 可结合其他预测方法进行验证"
        ])
        
        return recommendations
    
    def _get_selection_strategy_info(self) -> Dict[str, Any]:
        """获取选择策略信息"""
        return {
            'strategy_name': '多维度核心精炼算法',
            'scoring_weights': self.scoring_weights,
            'selection_criteria': {
                'frequency_analysis': '出现频率分析',
                'confidence_weighting': '信心度加权',
                'algorithm_consensus': '算法一致性',
                'historical_validation': '历史表现验证',
                'clustering_optimization': '聚类优化'
            },
            'optimization_features': [
                '风险平衡选择',
                '号码分布优化',
                '历史验证过滤',
                '多策略综合评估'
            ]
        }
    
    def generate_detailed_report(self, result: Dict[str, Any]) -> str:
        """生成详细的文本报告"""
        if not result:
            return "无法生成报告 - 结果为空"
        
        report = []
        report.append("=" * 80)
        report.append("🎯 核心号码精炼系统 - 详细分析报告")
        report.append("=" * 80)
        
        # 基本信息
        report.append(f"📊 基本信息:")
        report.append(f"  • 彩票类型: {self.lottery_type}")
        report.append(f"  • 核心号码: {result['core_numbers']}")
        report.append(f"  • 生成时间: {result['generation_time']}")
        report.append(f"  • 目标数量: {result['target_count']}")
        
        # 性能指标
        metrics = result['performance_metrics']
        report.append(f"\n📈 性能指标:")
        report.append(f"  • 平均综合得分: {metrics['avg_final_score']:.3f}")
        report.append(f"  • 平均信心度: {metrics['avg_confidence']:.1f}%")
        report.append(f"  • 预期命中概率: {metrics['expected_hit_probability']:.1%}")
        report.append(f"  • 信心度等级: {metrics['confidence_level']}")
        
        # 详细分析
        report.append(f"\n🔍 号码详细分析:")
        for i, score_data in enumerate(result['detailed_analysis']['number_scores'], 1):
            report.append(f"  {i}. 号码 {score_data['number']:2d}:")
            report.append(f"     综合得分: {score_data['final_score']:.3f}")
            report.append(f"     出现次数: {score_data['appearance_count']}")
            report.append(f"     平均信心度: {score_data['avg_confidence']:.1f}%")
            report.append(f"     风险等级: {score_data['risk_level']}")
        
        # 风险分布
        risk_dist = result['detailed_analysis']['risk_distribution']
        report.append(f"\n⚠️ 风险分布:")
        for risk_level, count in risk_dist.items():
            report.append(f"  • {risk_level}: {count}个号码")
        
        # 预期表现
        prediction = result['prediction_analysis']['expected_performance']
        report.append(f"\n🎯 预期表现:")
        report.append(f"  • 命中概率: {prediction['hit_probability']:.1%}")
        report.append(f"  • 信心度等级: {prediction['confidence_level']}")
        report.append(f"  • 整体风险: {prediction['risk_assessment']}")
        
        # 使用建议
        report.append(f"\n💡 使用建议:")
        for rec in result['usage_recommendations']:
            report.append(f"  • {rec}")
        
        # 优化建议
        optimization = result['prediction_analysis']['optimization_suggestions']
        if optimization:
            report.append(f"\n🔧 优化建议:")
            for suggestion in optimization:
                report.append(f"  • {suggestion}")
        
        report.append("\n" + "=" * 80)
        report.append("⚠️ 重要提示: 预测结果仅供参考，请理性投注！")
        report.append("=" * 80)
        
        return "\n".join(report)


# 使用示例
if __name__ == "__main__":
    # 创建核心号码精炼器
    refiner = CoreNumberRefiner('powercolor')
    
    # 模拟多组预测结果
    mock_multi_group_result = {
        'groups': [
            {'main_numbers': [5, 12, 18, 25, 33, 36], 'confidence': 85, 'original_method': 'neural_network', 'recommendation_weight': 0.3},
            {'main_numbers': [8, 15, 22, 28, 31, 37], 'confidence': 78, 'original_method': 'trend_analysis', 'recommendation_weight': 0.25},
            {'main_numbers': [3, 11, 19, 26, 29, 35], 'confidence': 72, 'original_method': 'frequency_based', 'recommendation_weight': 0.2},
            {'main_numbers': [6, 13, 20, 27, 34, 38], 'confidence': 65, 'original_method': 'pattern_based', 'recommendation_weight': 0.15},
            {'main_numbers': [2, 9, 16, 23, 30, 32], 'confidence': 58, 'original_method': 'enhanced_frequency', 'recommendation_weight': 0.1}
        ]
    }
    
    # 精炼核心号码
    result = refiner.refine_core_numbers(mock_multi_group_result)
    
    if result:
        print("核心号码精炼成功！")
        print(f"核心号码: {result['core_numbers']}")
        
        # 生成详细报告
        detailed_report = refiner.generate_detailed_report(result)
        print("\n" + detailed_report)