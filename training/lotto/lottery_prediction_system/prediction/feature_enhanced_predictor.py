#!/usr/bin/env python3
"""
特徵增強預測器 - 整合增強特徵工程的預測系統
結合優化板路分析和增強特徵分析提升預測準確性
"""

import pandas as pd
import numpy as np
from collections import defaultdict
import logging
from datetime import datetime
from .optimized_board_path_analyzer import OptimizedBoardPathAnalyzer
from .enhanced_feature_analyzer import EnhancedFeatureAnalyzer
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
import math

logger = logging.getLogger('feature_enhanced_predictor')

class FeatureEnhancedPredictor:
    """
    特徵增強預測器
    
    主要功能：
    1. 整合優化板路分析
    2. 加入增強特徵工程
    3. 機器學習輔助預測
    4. 多維度信心度評估
    """
    
    def __init__(self, lottery_type='powercolor', db_manager=None, optimization_level=2):
        """
        初始化特徵增強預測器
        
        Args:
            lottery_type: 彩票類型
            db_manager: 數據庫管理器
            optimization_level: 優化級別
        """
        self.lottery_type = lottery_type
        self.db_manager = db_manager
        self.optimization_level = optimization_level
        
        # 初始化組件
        self.board_analyzer = OptimizedBoardPathAnalyzer(lottery_type, db_manager, optimization_level)
        self.feature_analyzer = EnhancedFeatureAnalyzer(lottery_type)
        
        # 機器學習模型
        self.ml_model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.scaler = StandardScaler()
        
        # 特徵權重配置
        self.feature_weights = {
            'frequency': 0.25,      # 頻率特徵權重
            'trend': 0.20,          # 趨勢特徵權重
            'cycle': 0.15,          # 週期特徵權重
            'pattern': 0.15,        # 模式特徵權重
            'statistical': 0.10,    # 統計特徵權重
            'board_path': 0.15      # 板路分析權重
        }
        
        # 預測配置
        self.prediction_config = {
            'candidate_count': 7,           # 候選數量
            'feature_threshold': 0.3,       # 特徵閾值
            'confidence_threshold': 0.6,    # 信心閾值
            'ensemble_weight': 0.7,         # 集成權重
            'diversity_factor': 0.8         # 多樣性因子
        }
        
        logger.info(f"特徵增強預測器初始化完成 - 彩票類型: {lottery_type}, 優化級別: {optimization_level}")
    
    def prepare_features(self, df):
        """
        準備特徵數據
        
        Args:
            df: 歷史數據DataFrame
            
        Returns:
            tuple: (特徵矩陣, 特徵名稱, 特徵重要性)
        """
        logger.info("準備特徵數據...")
        
        # 提取增強特徵
        enhanced_features = self.feature_analyzer.extract_all_features(df)
        feature_importance = self.feature_analyzer.get_feature_importance(df)
        
        # 創建特徵矩陣
        feature_matrix, feature_names = self.feature_analyzer.create_feature_matrix(df)
        
        logger.info(f"特徵準備完成 - 特徵數量: {len(feature_names)}")
        
        return feature_matrix, feature_names, feature_importance
    
    def calculate_feature_based_scores(self, df, feature_importance):
        """
        基於特徵計算號碼分數
        
        Args:
            df: 歷史數據DataFrame
            feature_importance: 特徵重要性
            
        Returns:
            dict: 號碼分數字典
        """
        logger.info("計算基於特徵的號碼分數...")
        
        scores = {}
        
        # 獲取配置
        main_numbers = self.board_analyzer.main_numbers
        
        # 為每個號碼計算基於特徵的分數
        for num in range(1, main_numbers + 1):
            score = 0
            
            # 1. 頻率特徵分數
            freq_key = f'number_frequency'
            if freq_key in feature_importance:
                # 計算該號碼的相對頻率
                number_freq = self.get_number_frequency(df, num)
                avg_freq = feature_importance.get('avg_frequency', 1)
                freq_score = (number_freq / avg_freq) * self.feature_weights['frequency'] * 100
                score += freq_score
            
            # 2. 趨勢特徵分數
            trend_key = f'trend_slope_{num}'
            if trend_key in feature_importance:
                trend_score = feature_importance[trend_key] * self.feature_weights['trend'] * 100
                score += trend_score
            
            # 3. 週期特徵分數
            cycle_key = f'dominant_frequency_{num}'
            if cycle_key in feature_importance:
                cycle_score = feature_importance[cycle_key] * self.feature_weights['cycle'] * 100
                score += cycle_score
            
            # 4. 間隔特徵分數
            gap_key = f'gap_avg_{num}'
            if gap_key in feature_importance:
                gap_score = self.calculate_gap_score(df, num) * self.feature_weights['statistical'] * 100
                score += gap_score
            
            # 5. 基礎分數
            base_score = 20  # 基礎分數
            score += base_score
            
            scores[num] = max(0, score)  # 確保分數非負
        
        logger.info(f"特徵分數計算完成 - 平均分數: {np.mean(list(scores.values())):.2f}")
        return scores
    
    def get_number_frequency(self, df, number):
        """獲取號碼出現頻率"""
        main_cols = [f'Anumber{i}' for i in range(1, self.board_analyzer.main_count + 1)]
        count = 0
        
        for _, row in df.iterrows():
            if number in [row[col] for col in main_cols]:
                count += 1
        
        return count
    
    def calculate_gap_score(self, df, number):
        """計算間隔分數"""
        main_cols = [f'Anumber{i}' for i in range(1, self.board_analyzer.main_count + 1)]
        appearances = []
        
        for idx, row in df.iterrows():
            if number in [row[col] for col in main_cols]:
                appearances.append(idx)
        
        if len(appearances) >= 2:
            gaps = np.diff(appearances)
            avg_gap = np.mean(gaps)
            current_gap = len(df) - appearances[-1]
            
            if avg_gap > 0:
                gap_ratio = current_gap / avg_gap
                # 最佳間隔比例為 0.8-1.2
                if 0.8 <= gap_ratio <= 1.2:
                    return 1.0
                elif gap_ratio < 0.8:
                    return gap_ratio / 0.8
                else:
                    return 1.0 / (gap_ratio - 0.8)
        
        return 0.5  # 默認分數
    
    def ensemble_prediction(self, df, candidate_count=7):
        """
        集成預測方法
        
        Args:
            df: 歷史數據DataFrame
            candidate_count: 候選數量
            
        Returns:
            dict: 集成預測結果
        """
        logger.info(f"開始集成預測 - 候選數量: {candidate_count}")
        
        # 1. 獲取板路分析結果
        board_result = self.board_analyzer.predict_next_numbers_optimized(df, candidate_count)
        
        # 2. 準備特徵數據
        feature_matrix, feature_names, feature_importance = self.prepare_features(df)
        
        # 3. 計算基於特徵的分數
        feature_scores = self.calculate_feature_based_scores(df, feature_importance)
        
        # 4. 整合預測結果
        ensemble_candidates = []
        
        for i in range(candidate_count):
            # 設定隨機種子以確保可重現性
            np.random.seed(42 + i)
            
            # 結合板路分析和特徵分析
            combined_scores = self.combine_scores(
                board_result.get('candidates', [{}])[i % len(board_result.get('candidates', [{}]))],
                feature_scores,
                feature_importance
            )
            
            # 選擇號碼
            selected_numbers = self.select_numbers_ensemble(combined_scores)
            
            # 計算多維信心度
            confidence = self.calculate_ensemble_confidence(
                selected_numbers, 
                combined_scores, 
                feature_importance,
                board_result.get('confidence', 50)
            )
            
            candidate = {
                'main_numbers': selected_numbers,
                'confidence': confidence,
                'method': f'feature_enhanced_v{i+1}',
                'explanation': f'特徵增強預測候選{i+1}，結合板路分析和特徵工程',
                'feature_contribution': self.calculate_feature_contribution(selected_numbers, feature_importance),
                'board_contribution': board_result.get('confidence', 50) * 0.3
            }
            
            # 如果有特別號
            if self.board_analyzer.has_special:
                candidate['special_number'] = self.predict_special_number_enhanced(df, selected_numbers, feature_importance)
            
            ensemble_candidates.append(candidate)
        
        # 選擇最佳候選
        best_candidate = max(ensemble_candidates, key=lambda x: x['confidence'])
        
        result = {
            'method': 'feature_enhanced_ensemble',
            'version': 'v1.0_enhanced',
            'main_numbers': best_candidate['main_numbers'],
            'confidence': best_candidate['confidence'],
            'candidates': ensemble_candidates,
            'feature_count': len(feature_names),
            'board_confidence': board_result.get('confidence', 50),
            'feature_contribution': best_candidate['feature_contribution'],
            'explanation': f'特徵增強集成預測，結合{len(feature_names)}個特徵和板路分析'
        }
        
        if self.board_analyzer.has_special:
            result['special_number'] = best_candidate.get('special_number', 1)
        
        logger.info(f"集成預測完成 - 最佳候選信心度: {best_candidate['confidence']:.1f}%")
        
        return result
    
    def combine_scores(self, board_candidate, feature_scores, feature_importance):
        """
        結合板路分析和特徵分析的分數
        
        Args:
            board_candidate: 板路分析候選
            feature_scores: 特徵分數
            feature_importance: 特徵重要性
            
        Returns:
            dict: 結合後的分數
        """
        combined_scores = {}
        
        # 獲取板路分析的號碼和信心度
        board_numbers = board_candidate.get('main_numbers', [])
        board_confidence = board_candidate.get('confidence', 50)
        
        # 為每個號碼計算結合分數
        for num in range(1, self.board_analyzer.main_numbers + 1):
            # 板路分析分數
            board_score = 0
            if num in board_numbers:
                board_score = board_confidence * self.feature_weights['board_path']
            
            # 特徵分析分數
            feature_score = feature_scores.get(num, 0) * (1 - self.feature_weights['board_path'])
            
            # 結合分數
            combined_score = board_score + feature_score
            
            # 添加少量隨機性以增加多樣性
            diversity_adjustment = np.random.normal(0, combined_score * 0.05)
            combined_scores[num] = max(0, combined_score + diversity_adjustment)
        
        return combined_scores
    
    def select_numbers_ensemble(self, combined_scores):
        """
        基於結合分數選擇號碼
        
        Args:
            combined_scores: 結合分數字典
            
        Returns:
            list: 選擇的號碼列表
        """
        if not combined_scores:
            return list(range(1, self.board_analyzer.main_count + 1))
        
        # 排序候選
        sorted_candidates = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 選擇策略：70%高分，30%中分
        high_score_count = int(self.board_analyzer.main_count * 0.7)
        mid_score_count = self.board_analyzer.main_count - high_score_count
        
        selected_numbers = []
        
        # 選擇高分號碼
        for i in range(min(high_score_count, len(sorted_candidates))):
            selected_numbers.append(sorted_candidates[i][0])
        
        # 選擇中分號碼（使用加權隨機）
        if mid_score_count > 0 and len(sorted_candidates) > high_score_count:
            mid_candidates = sorted_candidates[high_score_count:high_score_count*3]
            if mid_candidates:
                weights = [score for _, score in mid_candidates]
                if sum(weights) > 0:
                    selected_indices = np.random.choice(
                        len(mid_candidates),
                        size=min(mid_score_count, len(mid_candidates)),
                        replace=False,
                        p=np.array(weights) / sum(weights)
                    )
                    for idx in selected_indices:
                        selected_numbers.append(mid_candidates[idx][0])
        
        # 確保數量正確
        while len(selected_numbers) < self.board_analyzer.main_count:
            for num, score in sorted_candidates:
                if num not in selected_numbers:
                    selected_numbers.append(num)
                    break
        
        return sorted(selected_numbers[:self.board_analyzer.main_count])
    
    def calculate_ensemble_confidence(self, selected_numbers, combined_scores, feature_importance, board_confidence):
        """
        計算集成信心度
        
        Args:
            selected_numbers: 選擇的號碼
            combined_scores: 結合分數
            feature_importance: 特徵重要性
            board_confidence: 板路信心度
            
        Returns:
            float: 集成信心度
        """
        if not selected_numbers or not combined_scores:
            return 50.0
        
        # 1. 基於分數的信心度
        selected_scores = [combined_scores.get(num, 0) for num in selected_numbers]
        avg_score = np.mean(selected_scores)
        all_scores = list(combined_scores.values())
        overall_avg = np.mean(all_scores)
        
        score_confidence = (avg_score / overall_avg) * 40 if overall_avg > 0 else 40
        
        # 2. 特徵一致性信心度
        feature_consistency = len(feature_importance) / 100 * 20  # 特徵數量貢獻
        
        # 3. 板路分析信心度貢獻
        board_contribution = board_confidence * 0.3
        
        # 4. 多樣性調整
        diversity_bonus = self.calculate_diversity_bonus(selected_numbers)
        
        # 綜合信心度
        total_confidence = score_confidence + feature_consistency + board_contribution + diversity_bonus
        
        # 限制在合理範圍
        confidence = max(35, min(90, total_confidence))
        
        return confidence
    
    def calculate_diversity_bonus(self, selected_numbers):
        """計算多樣性獎勵"""
        if len(selected_numbers) < 2:
            return 0
        
        # 計算號碼分佈的多樣性
        numbers_array = np.array(selected_numbers)
        
        # 奇偶分佈
        odd_count = np.sum(numbers_array % 2 == 1)
        even_count = len(numbers_array) - odd_count
        odd_even_balance = 1 - abs(odd_count - even_count) / len(numbers_array)
        
        # 大小分佈
        mid_point = self.board_analyzer.main_numbers // 2
        small_count = np.sum(numbers_array <= mid_point)
        large_count = len(numbers_array) - small_count
        size_balance = 1 - abs(small_count - large_count) / len(numbers_array)
        
        # 間隔分佈
        gaps = np.diff(sorted(numbers_array))
        gap_uniformity = 1 - (np.std(gaps) / np.mean(gaps)) if np.mean(gaps) > 0 else 0
        
        diversity_score = (odd_even_balance + size_balance + gap_uniformity) / 3 * 10
        
        return diversity_score
    
    def calculate_feature_contribution(self, selected_numbers, feature_importance):
        """計算特徵貢獻度"""
        if not feature_importance:
            return 0
        
        # 計算選中號碼相關特徵的總重要性
        total_importance = 0
        relevant_features = 0
        
        for num in selected_numbers:
            for feature_name, importance in feature_importance.items():
                if f'_{num}' in feature_name or f'{num}_' in feature_name:
                    total_importance += importance
                    relevant_features += 1
        
        if relevant_features > 0:
            return total_importance / relevant_features * 100
        
        return 0
    
    def predict_special_number_enhanced(self, df, main_numbers, feature_importance):
        """
        增強的特別號預測
        
        Args:
            df: 歷史數據DataFrame
            main_numbers: 主要號碼
            feature_importance: 特徵重要性
            
        Returns:
            int: 預測的特別號
        """
        # 使用板路分析的特別號預測
        special_result = self.board_analyzer.analyze_special_number(df, main_numbers)
        
        if isinstance(special_result, tuple):
            special_scores, _ = special_result
        else:
            special_scores = special_result
        
        # 加入特徵增強
        if special_scores:
            # 基於特徵重要性調整分數
            enhanced_scores = {}
            for num, score in special_scores.items():
                # 添加特徵增強因子
                enhancement_factor = 1.0
                
                # 檢查是否有相關特徵
                for feature_name, importance in feature_importance.items():
                    if f'special_{num}' in feature_name or f'second_{num}' in feature_name:
                        enhancement_factor += importance * 0.1
                
                enhanced_scores[num] = score * enhancement_factor
            
            return self.board_analyzer.select_special_number_optimized(enhanced_scores)
        
        return np.random.randint(1, self.board_analyzer.special_numbers + 1)
    
    def generate_prediction_report(self, df, result):
        """
        生成預測報告
        
        Args:
            df: 歷史數據DataFrame
            result: 預測結果
            
        Returns:
            str: 預測報告
        """
        logger.info("生成預測報告...")
        
        # 生成特徵報告
        feature_report = self.feature_analyzer.generate_feature_report(df)
        
        report = []
        report.append("=" * 80)
        report.append("🚀 特徵增強預測系統 - 預測報告")
        report.append("=" * 80)
        
        report.append(f"🎯 預測結果:")
        report.append(f"  主要號碼: {result.get('main_numbers', 'N/A')}")
        if result.get('special_number'):
            report.append(f"  特別號碼: {result.get('special_number', 'N/A')}")
        report.append(f"  信心度: {result.get('confidence', 0):.1f}%")
        report.append(f"  預測方法: {result.get('method', 'N/A')}")
        
        report.append(f"\n📊 預測分析:")
        report.append(f"  特徵數量: {result.get('feature_count', 0)}")
        report.append(f"  板路信心度: {result.get('board_confidence', 0):.1f}%")
        report.append(f"  特徵貢獻度: {result.get('feature_contribution', 0):.1f}%")
        
        report.append(f"\n🎲 候選組合:")
        candidates = result.get('candidates', [])
        for i, candidate in enumerate(candidates[:5], 1):
            main_nums = candidate.get('main_numbers', [])
            confidence = candidate.get('confidence', 0)
            feature_contrib = candidate.get('feature_contribution', 0)
            report.append(f"  {i}. {main_nums} (信心度: {confidence:.1f}%, 特徵貢獻: {feature_contrib:.1f}%)")
        
        report.append(f"\n{feature_report}")
        
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def predict_with_enhanced_features(self, df, candidate_count=7, generate_report=False):
        """
        使用增強特徵進行預測
        
        Args:
            df: 歷史數據DataFrame
            candidate_count: 候選數量
            generate_report: 是否生成報告
            
        Returns:
            dict: 預測結果
        """
        logger.info(f"開始特徵增強預測 - 候選數量: {candidate_count}")
        
        # 執行集成預測
        result = self.ensemble_prediction(df, candidate_count)
        
        # 生成報告
        if generate_report:
            report = self.generate_prediction_report(df, result)
            result['report'] = report
        
        logger.info(f"特徵增強預測完成 - 信心度: {result.get('confidence', 0):.1f}%")
        
        return result