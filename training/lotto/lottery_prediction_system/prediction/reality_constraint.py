#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
現實性約束模組
用於防止預測系統產生不合理的號碼組合，如123456這樣的完全連續號碼
"""

import logging
from typing import List, Dict, Tuple
import numpy as np
from collections import Counter

logger = logging.getLogger(__name__)

class RealityConstraint:
    """現實性約束檢查器"""
    
    def __init__(self, lottery_type: str = 'powercolor'):
        self.lottery_type = lottery_type
        self.constraints = self._load_constraints()
        
    def _load_constraints(self) -> Dict:
        """載入約束條件"""
        constraints = {
            # 連續號碼約束
            'max_consecutive': 3,  # 最多允許3個連續號碼
            'consecutive_penalty': 0.8,  # 連續號碼懲罰係數
            
            # 完全連續約束
            'allow_full_consecutive': False,  # 不允許完全連續
            'full_consecutive_penalty': 0.1,  # 完全連續的嚴重懲罰
            
            # 數字分佈約束
            'min_range_span': 10,  # 號碼範圍最小跨度
            'max_same_decade': 4,  # 同一個十位數最多號碼數
            
            # 奇偶約束
            'min_odd_count': 1,   # 最少奇數個數
            'max_odd_count': 5,   # 最多奇數個數
            'min_even_count': 1,  # 最少偶數個數
            'max_even_count': 5,  # 最多偶數個數
            
            # 大小約束
            'min_small_count': 1,  # 最少小號個數（1-19）
            'max_small_count': 5,  # 最多小號個數
            'min_large_count': 1,  # 最少大號個數（20-38/49）
            'max_large_count': 5,  # 最多大號個數
            
            # 特殊模式約束
            'arithmetic_sequence_penalty': 0.7,  # 等差數列懲罰
            'same_ending_penalty': 0.8,  # 相同尾數懲罰
            
            # 歷史重複約束
            'recent_repeat_penalty': 0.6,  # 最近期重複懲罰
            'exact_match_penalty': 0.1,   # 完全匹配歷史的懲罰
        }
        
        # 根據彩票類型調整約束
        if self.lottery_type == 'dailycash':
            constraints['max_consecutive'] = 2
            constraints['min_range_span'] = 8
        elif self.lottery_type == 'lotto649':
            constraints['min_range_span'] = 15
            
        return constraints
    
    def check_and_adjust_prediction(self, predicted_numbers: List[int], 
                                  historical_data: List[List[int]] = None) -> Tuple[List[int], float, List[str]]:
        """檢查並調整預測結果
        
        Args:
            predicted_numbers: 預測的號碼列表
            historical_data: 歷史開獎數據
            
        Returns:
            Tuple[調整後的號碼, 現實性分數, 警告信息]
        """
        warnings = []
        reality_score = 1.0
        adjusted_numbers = predicted_numbers.copy()
        
        # 1. 檢查連續號碼
        consecutive_info = self._check_consecutive_numbers(adjusted_numbers)
        if consecutive_info['violation']:
            warnings.extend(consecutive_info['warnings'])
            reality_score *= consecutive_info['penalty']
            
            # 如果是完全連續，強制調整
            if consecutive_info['is_full_consecutive']:
                adjusted_numbers = self._fix_full_consecutive(adjusted_numbers)
                warnings.append("🔧 已調整完全連續號碼組合")
        
        # 2. 檢查數字分佈
        distribution_info = self._check_number_distribution(adjusted_numbers)
        if distribution_info['violation']:
            warnings.extend(distribution_info['warnings'])
            reality_score *= distribution_info['penalty']
        
        # 3. 檢查奇偶分佈
        odd_even_info = self._check_odd_even_distribution(adjusted_numbers)
        if odd_even_info['violation']:
            warnings.extend(odd_even_info['warnings'])
            reality_score *= odd_even_info['penalty']
        
        # 4. 檢查大小分佈
        size_info = self._check_size_distribution(adjusted_numbers)
        if size_info['violation']:
            warnings.extend(size_info['warnings'])
            reality_score *= size_info['penalty']
        
        # 5. 檢查特殊模式
        pattern_info = self._check_special_patterns(adjusted_numbers)
        if pattern_info['violation']:
            warnings.extend(pattern_info['warnings'])
            reality_score *= pattern_info['penalty']
        
        # 6. 檢查歷史重複
        if historical_data:
            history_info = self._check_historical_repetition(adjusted_numbers, historical_data)
            if history_info['violation']:
                warnings.extend(history_info['warnings'])
                reality_score *= history_info['penalty']
        
        return sorted(adjusted_numbers), reality_score, warnings
    
    def _check_consecutive_numbers(self, numbers: List[int]) -> Dict:
        """檢查連續號碼"""
        sorted_nums = sorted(numbers)
        consecutive_groups = []
        current_group = [sorted_nums[0]]
        
        for i in range(1, len(sorted_nums)):
            if sorted_nums[i] == sorted_nums[i-1] + 1:
                current_group.append(sorted_nums[i])
            else:
                if len(current_group) >= 2:
                    consecutive_groups.append(current_group.copy())
                current_group = [sorted_nums[i]]
        
        if len(current_group) >= 2:
            consecutive_groups.append(current_group)
        
        max_consecutive = max([len(group) for group in consecutive_groups]) if consecutive_groups else 0
        is_full_consecutive = (len(numbers) >= 6 and 
                              sorted_nums == list(range(sorted_nums[0], sorted_nums[0] + len(sorted_nums))))
        
        violation = False
        warnings = []
        penalty = 1.0
        
        if is_full_consecutive:
            violation = True
            warnings.append("⚠️ 檢測到完全連續號碼組合（如123456），這在現實中極其罕見")
            penalty = self.constraints['full_consecutive_penalty']
        elif max_consecutive > self.constraints['max_consecutive']:
            violation = True
            warnings.append(f"⚠️ 連續號碼過多（{max_consecutive}個），建議減少至{self.constraints['max_consecutive']}個以內")
            penalty = self.constraints['consecutive_penalty']
        
        return {
            'violation': violation,
            'warnings': warnings,
            'penalty': penalty,
            'is_full_consecutive': is_full_consecutive,
            'max_consecutive': max_consecutive,
            'consecutive_groups': consecutive_groups
        }
    
    def _fix_full_consecutive(self, numbers: List[int]) -> List[int]:
        """修復完全連續的號碼組合"""
        sorted_nums = sorted(numbers)
        
        # 保留前3個連續號碼，替換其他的
        fixed_numbers = sorted_nums[:3].copy()
        
        # 生成替換號碼
        max_num = 38 if self.lottery_type == 'powercolor' else (49 if self.lottery_type == 'lotto649' else 39)
        available_numbers = [i for i in range(1, max_num + 1) if i not in fixed_numbers]
        
        # 隨機選擇剩餘號碼，確保不連續
        import random
        while len(fixed_numbers) < len(numbers):
            candidate = random.choice(available_numbers)
            # 確保不與已選號碼連續
            if not any(abs(candidate - num) == 1 for num in fixed_numbers):
                fixed_numbers.append(candidate)
                available_numbers.remove(candidate)
            else:
                available_numbers.remove(candidate)
                if not available_numbers:
                    break
        
        # 如果還不夠，隨機補充
        while len(fixed_numbers) < len(numbers) and available_numbers:
            fixed_numbers.append(random.choice(available_numbers))
            available_numbers.remove(fixed_numbers[-1])
        
        return fixed_numbers
    
    def _check_number_distribution(self, numbers: List[int]) -> Dict:
        """檢查號碼分佈"""
        sorted_nums = sorted(numbers)
        range_span = sorted_nums[-1] - sorted_nums[0]
        
        # 檢查同一十位數的號碼
        decades = Counter([num // 10 for num in numbers])
        max_same_decade = max(decades.values()) if decades else 0
        
        violation = False
        warnings = []
        penalty = 1.0
        
        if range_span < self.constraints['min_range_span']:
            violation = True
            warnings.append(f"⚠️ 號碼範圍過小（跨度{range_span}），建議增加分散性")
            penalty *= 0.8
        
        if max_same_decade > self.constraints['max_same_decade']:
            violation = True
            warnings.append(f"⚠️ 同一十位數號碼過多（{max_same_decade}個），建議分散選擇")
            penalty *= 0.7
        
        return {
            'violation': violation,
            'warnings': warnings,
            'penalty': penalty
        }
    
    def _check_odd_even_distribution(self, numbers: List[int]) -> Dict:
        """檢查奇偶分佈"""
        odd_count = sum(1 for num in numbers if num % 2 == 1)
        even_count = len(numbers) - odd_count
        
        violation = False
        warnings = []
        penalty = 1.0
        
        if odd_count < self.constraints['min_odd_count'] or odd_count > self.constraints['max_odd_count']:
            violation = True
            warnings.append(f"⚠️ 奇數分佈不均（{odd_count}個奇數），建議調整")
            penalty *= 0.9
        
        if even_count < self.constraints['min_even_count'] or even_count > self.constraints['max_even_count']:
            violation = True
            warnings.append(f"⚠️ 偶數分佈不均（{even_count}個偶數），建議調整")
            penalty *= 0.9
        
        return {
            'violation': violation,
            'warnings': warnings,
            'penalty': penalty
        }
    
    def _check_size_distribution(self, numbers: List[int]) -> Dict:
        """檢查大小號分佈"""
        max_num = 38 if self.lottery_type == 'powercolor' else (49 if self.lottery_type == 'lotto649' else 39)
        mid_point = max_num // 2
        
        small_count = sum(1 for num in numbers if num <= mid_point)
        large_count = len(numbers) - small_count
        
        violation = False
        warnings = []
        penalty = 1.0
        
        if small_count < self.constraints['min_small_count'] or small_count > self.constraints['max_small_count']:
            violation = True
            warnings.append(f"⚠️ 小號分佈不均（{small_count}個小號），建議調整")
            penalty *= 0.9
        
        if large_count < self.constraints['min_large_count'] or large_count > self.constraints['max_large_count']:
            violation = True
            warnings.append(f"⚠️ 大號分佈不均（{large_count}個大號），建議調整")
            penalty *= 0.9
        
        return {
            'violation': violation,
            'warnings': warnings,
            'penalty': penalty
        }
    
    def _check_special_patterns(self, numbers: List[int]) -> Dict:
        """檢查特殊模式"""
        sorted_nums = sorted(numbers)
        
        violation = False
        warnings = []
        penalty = 1.0
        
        # 檢查等差數列
        if len(sorted_nums) >= 3:
            differences = [sorted_nums[i+1] - sorted_nums[i] for i in range(len(sorted_nums)-1)]
            if len(set(differences)) == 1 and differences[0] > 1:
                violation = True
                warnings.append(f"⚠️ 檢測到等差數列（公差{differences[0]}），這種模式較為罕見")
                penalty *= self.constraints['arithmetic_sequence_penalty']
        
        # 檢查相同尾數
        endings = [num % 10 for num in numbers]
        ending_counts = Counter(endings)
        max_same_ending = max(ending_counts.values())
        
        if max_same_ending >= 3:
            violation = True
            warnings.append(f"⚠️ 相同尾數過多（{max_same_ending}個），建議增加變化")
            penalty *= self.constraints['same_ending_penalty']
        
        return {
            'violation': violation,
            'warnings': warnings,
            'penalty': penalty
        }
    
    def _check_historical_repetition(self, numbers: List[int], historical_data: List[List[int]]) -> Dict:
        """檢查歷史重複"""
        violation = False
        warnings = []
        penalty = 1.0
        
        # 檢查完全匹配
        sorted_prediction = sorted(numbers)
        for i, historical_draw in enumerate(historical_data[-10:]):  # 檢查最近10期
            if sorted(historical_draw) == sorted_prediction:
                violation = True
                warnings.append(f"⚠️ 與第{len(historical_data)-i}期完全相同，這種重複極其罕見")
                penalty *= self.constraints['exact_match_penalty']
                break
        
        # 檢查最近期重複號碼過多
        if historical_data:
            recent_numbers = set()
            for draw in historical_data[-3:]:  # 最近3期
                recent_numbers.update(draw)
            
            overlap_count = len(set(numbers) & recent_numbers)
            if overlap_count >= len(numbers) - 1:  # 幾乎全部重複
                violation = True
                warnings.append(f"⚠️ 與最近期重複號碼過多（{overlap_count}個），建議增加新號碼")
                penalty *= self.constraints['recent_repeat_penalty']
        
        return {
            'violation': violation,
            'warnings': warnings,
            'penalty': penalty
        }
    
    def generate_reality_report(self, numbers: List[int], reality_score: float, warnings: List[str]) -> str:
        """生成現實性報告"""
        report = []
        report.append("\n" + "="*50)
        report.append("📊 預測結果現實性分析報告")
        report.append("="*50)
        
        report.append(f"\n🎯 預測號碼: {numbers}")
        report.append(f"📈 現實性分數: {reality_score:.3f} ({'高' if reality_score >= 0.8 else '中' if reality_score >= 0.6 else '低'})")
        
        if warnings:
            report.append("\n⚠️ 現實性警告:")
            for warning in warnings:
                report.append(f"  {warning}")
        else:
            report.append("\n✅ 未發現明顯的現實性問題")
        
        # 建議
        if reality_score < 0.6:
            report.append("\n💡 建議:")
            report.append("  - 考慮重新生成預測")
            report.append("  - 增加號碼的隨機性和分散性")
            report.append("  - 避免過於規律的模式")
        elif reality_score < 0.8:
            report.append("\n💡 建議:")
            report.append("  - 預測基本合理，可考慮微調")
            report.append("  - 注意號碼分佈的均衡性")
        
        report.append("\n" + "="*50)
        
        return "\n".join(report)

def test_reality_constraint():
    """測試現實性約束功能"""
    constraint = RealityConstraint('powercolor')
    
    # 測試完全連續號碼
    print("\n🧪 測試1: 完全連續號碼 [1,2,3,4,5,6]")
    adjusted, score, warnings = constraint.check_and_adjust_prediction([1,2,3,4,5,6])
    print(constraint.generate_reality_report(adjusted, score, warnings))
    
    # 測試部分連續號碼
    print("\n🧪 測試2: 部分連續號碼 [1,2,3,15,20,25]")
    adjusted, score, warnings = constraint.check_and_adjust_prediction([1,2,3,15,20,25])
    print(constraint.generate_reality_report(adjusted, score, warnings))
    
    # 測試正常號碼
    print("\n🧪 測試3: 正常分佈號碼 [5,12,18,23,31,36]")
    adjusted, score, warnings = constraint.check_and_adjust_prediction([5,12,18,23,31,36])
    print(constraint.generate_reality_report(adjusted, score, warnings))

if __name__ == "__main__":
    test_reality_constraint()