#!/usr/bin/env python3
"""
预测准确性跟踪系统
实时跟踪和分析预测效果，提供数据驱动的算法优化建议
"""

import sqlite3
import pandas as pd
import numpy as np
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, Counter
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import dataclass, asdict

logger = logging.getLogger('accuracy_tracker')

@dataclass
class PredictionRecord:
    """预测记录数据类"""
    prediction_id: str
    lottery_type: str
    prediction_date: str
    draw_date: str
    predicted_numbers: List[int]
    predicted_special: Optional[int]
    actual_numbers: Optional[List[int]]
    actual_special: Optional[int]
    algorithm: str
    confidence: float
    group_id: Optional[int]
    strategy: str
    matches: Optional[int]
    special_match: Optional[bool]
    accuracy_score: Optional[float]
    status: str  # 'pending', 'verified', 'missed'

@dataclass
class AccuracyMetrics:
    """准确性指标数据类"""
    algorithm: str
    total_predictions: int
    total_matches: int
    accuracy_rate: float
    avg_matches: float
    confidence_accuracy: float
    stability_score: float
    recent_performance: float
    trend: str  # 'improving', 'stable', 'declining'

class AccuracyTracker:
    """
    预测准确性跟踪器
    
    功能：
    1. 记录所有预测结果
    2. 跟踪预测准确性
    3. 分析算法表现
    4. 生成优化建议
    5. 实时监控预测质量
    """
    
    def __init__(self, db_path: str = "prediction_accuracy.db"):
        """
        初始化准确性跟踪器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.init_database()
        
        # 配置参数
        self.config = {
            'accuracy_window': 30,  # 用于计算准确性的天数窗口
            'min_predictions_for_analysis': 10,  # 最少预测数量要求
            'confidence_tolerance': 0.1,  # 信心度容忍度
            'trend_detection_periods': 20,  # 趋势检测周期
        }
        
        logger.info(f"预测准确性跟踪器初始化完成 - 数据库: {db_path}")
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 预测记录表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS prediction_records (
            prediction_id TEXT PRIMARY KEY,
            lottery_type TEXT NOT NULL,
            prediction_date TEXT NOT NULL,
            draw_date TEXT NOT NULL,
            predicted_numbers TEXT NOT NULL,
            predicted_special INTEGER,
            actual_numbers TEXT,
            actual_special INTEGER,
            algorithm TEXT NOT NULL,
            confidence REAL NOT NULL,
            group_id INTEGER,
            strategy TEXT,
            matches INTEGER,
            special_match BOOLEAN,
            accuracy_score REAL,
            status TEXT DEFAULT 'pending',
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 算法性能表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS algorithm_performance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            algorithm TEXT NOT NULL,
            lottery_type TEXT NOT NULL,
            date TEXT NOT NULL,
            total_predictions INTEGER,
            total_matches INTEGER,
            accuracy_rate REAL,
            avg_matches REAL,
            confidence_accuracy REAL,
            stability_score REAL,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 准确性历史表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS accuracy_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            lottery_type TEXT NOT NULL,
            overall_accuracy REAL,
            best_algorithm TEXT,
            worst_algorithm TEXT,
            total_predictions INTEGER,
            successful_predictions INTEGER,
            analysis_notes TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 预测建议表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS prediction_recommendations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            lottery_type TEXT NOT NULL,
            recommendation_type TEXT,
            target_algorithm TEXT,
            suggestion TEXT,
            priority INTEGER,
            implementation_status TEXT DEFAULT 'pending',
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        conn.commit()
        conn.close()
        
        logger.info("数据库初始化完成")
    
    def record_prediction(self, 
                         prediction_result: Dict[str, Any], 
                         lottery_type: str,
                         draw_date: str,
                         strategy: str = 'single') -> str:
        """
        记录预测结果
        
        Args:
            prediction_result: 预测结果字典
            lottery_type: 彩票类型
            draw_date: 开奖日期
            strategy: 预测策略
            
        Returns:
            预测记录ID
        """
        prediction_id = f"{lottery_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 处理多组预测
        if 'groups' in prediction_result:
            # 多组预测
            for group in prediction_result['groups']:
                group_id = group.get('group_id', 1)
                sub_prediction_id = f"{prediction_id}_group_{group_id}"
                
                record = PredictionRecord(
                    prediction_id=sub_prediction_id,
                    lottery_type=lottery_type,
                    prediction_date=datetime.now().isoformat(),
                    draw_date=draw_date,
                    predicted_numbers=group['main_numbers'],
                    predicted_special=group.get('special_number'),
                    actual_numbers=None,
                    actual_special=None,
                    algorithm=group.get('original_method', 'unknown'),
                    confidence=group['confidence'],
                    group_id=group_id,
                    strategy=strategy,
                    matches=None,
                    special_match=None,
                    accuracy_score=None,
                    status='pending'
                )
                
                self._save_prediction_record(record)
        else:
            # 单组预测
            record = PredictionRecord(
                prediction_id=prediction_id,
                lottery_type=lottery_type,
                prediction_date=datetime.now().isoformat(),
                draw_date=draw_date,
                predicted_numbers=prediction_result.get('main_numbers', []),
                predicted_special=prediction_result.get('special_number'),
                actual_numbers=None,
                actual_special=None,
                algorithm=prediction_result.get('method', 'unknown'),
                confidence=prediction_result.get('confidence', 50),
                group_id=None,
                strategy=strategy,
                matches=None,
                special_match=None,
                accuracy_score=None,
                status='pending'
            )
            
            self._save_prediction_record(record)
        
        logger.info(f"预测记录已保存 - ID: {prediction_id}")
        return prediction_id
    
    def _save_prediction_record(self, record: PredictionRecord):
        """保存预测记录到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        INSERT OR REPLACE INTO prediction_records 
        (prediction_id, lottery_type, prediction_date, draw_date, 
         predicted_numbers, predicted_special, actual_numbers, actual_special,
         algorithm, confidence, group_id, strategy, matches, special_match,
         accuracy_score, status, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ''', (
            record.prediction_id, record.lottery_type, record.prediction_date,
            record.draw_date, json.dumps(record.predicted_numbers),
            record.predicted_special,
            json.dumps(record.actual_numbers) if record.actual_numbers else None,
            record.actual_special, record.algorithm, record.confidence,
            record.group_id, record.strategy, record.matches,
            record.special_match, record.accuracy_score, record.status
        ))
        
        conn.commit()
        conn.close()
    
    def verify_predictions(self, 
                         draw_date: str, 
                         actual_numbers: List[int], 
                         actual_special: Optional[int] = None,
                         lottery_type: str = None) -> Dict[str, Any]:
        """
        验证预测结果
        
        Args:
            draw_date: 开奖日期
            actual_numbers: 实际开奖号码
            actual_special: 实际特别号
            lottery_type: 彩票类型
            
        Returns:
            验证结果统计
        """
        logger.info(f"开始验证预测 - 开奖日期: {draw_date}")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 查询待验证的预测
        query = "SELECT * FROM prediction_records WHERE draw_date = ? AND status = 'pending'"
        params = [draw_date]
        
        if lottery_type:
            query += " AND lottery_type = ?"
            params.append(lottery_type)
        
        cursor.execute(query, params)
        pending_predictions = cursor.fetchall()
        
        verification_results = {
            'verified_count': 0,
            'total_matches': 0,
            'algorithm_performance': defaultdict(list),
            'best_prediction': None,
            'worst_prediction': None,
            'group_analysis': defaultdict(list)
        }
        
        for row in pending_predictions:
            prediction_id = row[0]
            predicted_numbers = json.loads(row[4])
            predicted_special = row[5]
            algorithm = row[8]
            confidence = row[9]
            group_id = row[10]
            
            # 计算匹配数
            matches = len(set(predicted_numbers).intersection(set(actual_numbers)))
            
            # 计算特别号匹配
            special_match = predicted_special == actual_special if predicted_special and actual_special else None
            
            # 计算准确性分数
            accuracy_score = self._calculate_accuracy_score(
                matches, special_match, len(actual_numbers), confidence
            )
            
            # 更新数据库记录
            cursor.execute('''
            UPDATE prediction_records 
            SET actual_numbers = ?, actual_special = ?, matches = ?, 
                special_match = ?, accuracy_score = ?, status = 'verified',
                updated_at = CURRENT_TIMESTAMP
            WHERE prediction_id = ?
            ''', (
                json.dumps(actual_numbers), actual_special, matches,
                special_match, accuracy_score, prediction_id
            ))
            
            # 收集统计信息
            verification_results['verified_count'] += 1
            verification_results['total_matches'] += matches
            verification_results['algorithm_performance'][algorithm].append({
                'matches': matches,
                'accuracy_score': accuracy_score,
                'confidence': confidence
            })
            
            if group_id:
                verification_results['group_analysis'][group_id].append({
                    'matches': matches,
                    'accuracy_score': accuracy_score,
                    'algorithm': algorithm
                })
            
            # 记录最佳和最差预测
            if not verification_results['best_prediction'] or accuracy_score > verification_results['best_prediction']['score']:
                verification_results['best_prediction'] = {
                    'id': prediction_id,
                    'algorithm': algorithm,
                    'matches': matches,
                    'score': accuracy_score,
                    'predicted': predicted_numbers
                }
            
            if not verification_results['worst_prediction'] or accuracy_score < verification_results['worst_prediction']['score']:
                verification_results['worst_prediction'] = {
                    'id': prediction_id,
                    'algorithm': algorithm,
                    'matches': matches,
                    'score': accuracy_score,
                    'predicted': predicted_numbers
                }
        
        conn.commit()
        conn.close()
        
        # 更新算法性能统计
        self._update_algorithm_performance(draw_date, lottery_type)
        
        logger.info(f"预测验证完成 - 验证{verification_results['verified_count']}个预测")
        
        return verification_results
    
    def _calculate_accuracy_score(self, 
                                matches: int, 
                                special_match: Optional[bool], 
                                total_numbers: int,
                                confidence: float) -> float:
        """
        计算准确性分数
        
        Args:
            matches: 匹配的号码数
            special_match: 特别号是否匹配
            total_numbers: 总号码数
            confidence: 预测信心度
            
        Returns:
            准确性分数 (0-100)
        """
        # 基础分数：基于匹配数
        base_score = (matches / total_numbers) * 70
        
        # 特别号奖励
        special_bonus = 15 if special_match else 0
        
        # 信心度调整：高信心度预测如果匹配则奖励，不匹配则惩罚
        confidence_factor = confidence / 100
        if matches >= total_numbers * 0.5:  # 匹配数超过一半
            confidence_adjustment = confidence_factor * 10
        else:
            confidence_adjustment = -confidence_factor * 5
        
        # 综合分数
        total_score = base_score + special_bonus + confidence_adjustment + 5  # 基础分
        
        return max(0, min(100, total_score))
    
    def _update_algorithm_performance(self, date: str, lottery_type: str):
        """更新算法性能统计"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 获取各算法在指定日期的表现
        cursor.execute('''
        SELECT algorithm, 
               COUNT(*) as total_predictions,
               SUM(matches) as total_matches,
               AVG(CAST(matches AS FLOAT) / 6) as accuracy_rate,
               AVG(matches) as avg_matches,
               AVG(accuracy_score) as avg_accuracy_score
        FROM prediction_records 
        WHERE draw_date = ? AND lottery_type = ? AND status = 'verified'
        GROUP BY algorithm
        ''', (date, lottery_type))
        
        performance_data = cursor.fetchall()
        
        for row in performance_data:
            algorithm, total_pred, total_matches, accuracy_rate, avg_matches, avg_score = row
            
            # 计算信心度准确性
            cursor.execute('''
            SELECT confidence, accuracy_score 
            FROM prediction_records 
            WHERE algorithm = ? AND draw_date = ? AND lottery_type = ? AND status = 'verified'
            ''', (algorithm, date, lottery_type))
            
            confidence_data = cursor.fetchall()
            confidence_accuracy = self._calculate_confidence_accuracy(confidence_data)
            
            # 计算稳定性分数
            stability_score = self._calculate_stability_score(algorithm, lottery_type)
            
            # 保存性能数据
            cursor.execute('''
            INSERT INTO algorithm_performance 
            (algorithm, lottery_type, date, total_predictions, total_matches,
             accuracy_rate, avg_matches, confidence_accuracy, stability_score)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                algorithm, lottery_type, date, total_pred, total_matches,
                accuracy_rate, avg_matches, confidence_accuracy, stability_score
            ))
        
        conn.commit()
        conn.close()
    
    def _calculate_confidence_accuracy(self, confidence_data: List[Tuple[float, float]]) -> float:
        """计算信心度准确性"""
        if not confidence_data:
            return 0.0
        
        # 计算信心度与实际准确性的相关性
        confidences = [cd[0] for cd in confidence_data]
        accuracies = [cd[1] for cd in confidence_data]
        
        if len(set(confidences)) <= 1 or len(set(accuracies)) <= 1:
            return 50.0  # 默认值
        
        correlation = np.corrcoef(confidences, accuracies)[0, 1]
        return max(0, min(100, (correlation + 1) * 50))  # 转换为0-100分数
    
    def _calculate_stability_score(self, algorithm: str, lottery_type: str, days: int = 30) -> float:
        """计算算法稳定性分数"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 获取最近N天的表现
        cursor.execute('''
        SELECT accuracy_score 
        FROM prediction_records 
        WHERE algorithm = ? AND lottery_type = ? AND status = 'verified'
              AND draw_date >= date('now', '-{} days')
        ORDER BY draw_date DESC
        '''.format(days), (algorithm, lottery_type))
        
        scores = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        if len(scores) < 3:
            return 50.0  # 默认值
        
        # 计算方差的倒数作为稳定性指标
        variance = np.var(scores)
        stability = 100 / (1 + variance / 100)  # 归一化到0-100
        
        return stability
    
    def get_algorithm_metrics(self, 
                            algorithm: str, 
                            lottery_type: str, 
                            days: int = 30) -> Optional[AccuracyMetrics]:
        """
        获取算法准确性指标
        
        Args:
            algorithm: 算法名称
            lottery_type: 彩票类型
            days: 统计天数
            
        Returns:
            准确性指标对象
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 获取基础统计
        cursor.execute('''
        SELECT COUNT(*) as total_predictions,
               SUM(matches) as total_matches,
               AVG(CAST(matches AS FLOAT) / 6) as accuracy_rate,
               AVG(matches) as avg_matches,
               AVG(accuracy_score) as avg_accuracy_score
        FROM prediction_records 
        WHERE algorithm = ? AND lottery_type = ? AND status = 'verified'
              AND draw_date >= date('now', '-{} days')
        '''.format(days), (algorithm, lottery_type))
        
        result = cursor.fetchone()
        if not result or result[0] == 0:
            conn.close()
            return None
        
        total_pred, total_matches, accuracy_rate, avg_matches, avg_score = result
        
        # 获取信心度准确性
        cursor.execute('''
        SELECT confidence, accuracy_score 
        FROM prediction_records 
        WHERE algorithm = ? AND lottery_type = ? AND status = 'verified'
              AND draw_date >= date('now', '-{} days')
        '''.format(days), (algorithm, lottery_type))
        
        confidence_data = cursor.fetchall()
        confidence_accuracy = self._calculate_confidence_accuracy(confidence_data)
        
        # 计算稳定性分数
        stability_score = self._calculate_stability_score(algorithm, lottery_type, days)
        
        # 计算近期表现 (最近7天)
        cursor.execute('''
        SELECT AVG(accuracy_score)
        FROM prediction_records 
        WHERE algorithm = ? AND lottery_type = ? AND status = 'verified'
              AND draw_date >= date('now', '-7 days')
        ''', (algorithm, lottery_type))
        
        recent_result = cursor.fetchone()
        recent_performance = recent_result[0] if recent_result[0] else avg_score
        
        # 判断趋势
        trend = self._determine_trend(algorithm, lottery_type)
        
        conn.close()
        
        return AccuracyMetrics(
            algorithm=algorithm,
            total_predictions=total_pred,
            total_matches=total_matches or 0,
            accuracy_rate=accuracy_rate or 0,
            avg_matches=avg_matches or 0,
            confidence_accuracy=confidence_accuracy,
            stability_score=stability_score,
            recent_performance=recent_performance,
            trend=trend
        )
    
    def _determine_trend(self, algorithm: str, lottery_type: str) -> str:
        """判断算法表现趋势"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 获取最近20次预测的准确性分数
        cursor.execute('''
        SELECT accuracy_score, draw_date
        FROM prediction_records 
        WHERE algorithm = ? AND lottery_type = ? AND status = 'verified'
        ORDER BY draw_date DESC
        LIMIT 20
        ''', (algorithm, lottery_type))
        
        results = cursor.fetchall()
        conn.close()
        
        if len(results) < 10:
            return 'stable'
        
        scores = [r[0] for r in reversed(results)]  # 时间正序
        
        # 简单线性趋势分析
        x = np.arange(len(scores))
        slope = np.polyfit(x, scores, 1)[0]
        
        if slope > 2:
            return 'improving'
        elif slope < -2:
            return 'declining'
        else:
            return 'stable'
    
    def get_overall_performance_report(self, 
                                     lottery_type: str, 
                                     days: int = 30) -> Dict[str, Any]:
        """
        获取整体性能报告
        
        Args:
            lottery_type: 彩票类型
            days: 统计天数
            
        Returns:
            整体性能报告
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 整体统计
        cursor.execute('''
        SELECT COUNT(*) as total_predictions,
               COUNT(CASE WHEN matches >= 3 THEN 1 END) as successful_predictions,
               AVG(matches) as avg_matches,
               AVG(accuracy_score) as avg_accuracy,
               MAX(matches) as best_matches
        FROM prediction_records 
        WHERE lottery_type = ? AND status = 'verified'
              AND draw_date >= date('now', '-{} days')
        '''.format(days), (lottery_type,))
        
        overall_stats = cursor.fetchone()
        
        # 算法排名
        cursor.execute('''
        SELECT algorithm,
               COUNT(*) as predictions,
               AVG(matches) as avg_matches,
               AVG(accuracy_score) as avg_score,
               COUNT(CASE WHEN matches >= 3 THEN 1 END) as successful_count
        FROM prediction_records 
        WHERE lottery_type = ? AND status = 'verified'
              AND draw_date >= date('now', '-{} days')
        GROUP BY algorithm
        HAVING predictions >= 5
        ORDER BY avg_score DESC
        '''.format(days), (lottery_type,))
        
        algorithm_rankings = cursor.fetchall()
        
        # 多组预测分析
        cursor.execute('''
        SELECT group_id,
               COUNT(*) as predictions,
               AVG(matches) as avg_matches,
               AVG(accuracy_score) as avg_score
        FROM prediction_records 
        WHERE lottery_type = ? AND status = 'verified' AND group_id IS NOT NULL
              AND draw_date >= date('now', '-{} days')
        GROUP BY group_id
        ORDER BY avg_score DESC
        '''.format(days), (lottery_type,))
        
        group_analysis = cursor.fetchall()
        
        conn.close()
        
        # 生成报告
        report = {
            'lottery_type': lottery_type,
            'analysis_period': f'{days} days',
            'overall_statistics': {
                'total_predictions': overall_stats[0] or 0,
                'successful_predictions': overall_stats[1] or 0,
                'success_rate': (overall_stats[1] / overall_stats[0] * 100) if overall_stats[0] > 0 else 0,
                'average_matches': overall_stats[2] or 0,
                'average_accuracy': overall_stats[3] or 0,
                'best_matches': overall_stats[4] or 0
            },
            'algorithm_rankings': [
                {
                    'algorithm': row[0],
                    'predictions': row[1],
                    'avg_matches': row[2],
                    'avg_score': row[3],
                    'successful_count': row[4],
                    'success_rate': (row[4] / row[1] * 100) if row[1] > 0 else 0
                }
                for row in algorithm_rankings
            ],
            'group_analysis': [
                {
                    'group_id': row[0],
                    'predictions': row[1],
                    'avg_matches': row[2],
                    'avg_score': row[3]
                }
                for row in group_analysis
            ] if group_analysis else [],
            'recommendations': self._generate_performance_recommendations(algorithm_rankings, group_analysis)
        }
        
        return report
    
    def _generate_performance_recommendations(self, 
                                           algorithm_rankings: List[Tuple], 
                                           group_analysis: List[Tuple]) -> List[str]:
        """生成性能改进建议"""
        recommendations = []
        
        if algorithm_rankings:
            best_algorithm = algorithm_rankings[0]
            worst_algorithm = algorithm_rankings[-1]
            
            recommendations.append(
                f"最佳算法: {best_algorithm[0]} (平均分数: {best_algorithm[3]:.1f})"
            )
            
            if len(algorithm_rankings) > 1:
                recommendations.append(
                    f"表现最差算法: {worst_algorithm[0]} (平均分数: {worst_algorithm[3]:.1f})，建议优化"
                )
            
            # 成功率分析
            high_success_algorithms = [alg for alg in algorithm_rankings if (alg[4] / alg[1] * 100) > 30]
            if high_success_algorithms:
                recommendations.append(
                    f"高成功率算法: {', '.join([alg[0] for alg in high_success_algorithms[:3]])}"
                )
        
        if group_analysis:
            best_group = group_analysis[0]
            recommendations.append(
                f"最佳预测组: 第{best_group[0]}组 (平均分数: {best_group[3]:.1f})"
            )
            
            if len(group_analysis) > 1:
                group_scores = [g[3] for g in group_analysis]
                if max(group_scores) - min(group_scores) > 10:
                    recommendations.append("建议重新调整多组预测的权重分配")
        
        if not algorithm_rankings:
            recommendations.append("数据不足，建议积累更多预测数据后再进行分析")
        
        return recommendations
    
    def export_analysis_report(self, 
                             lottery_type: str, 
                             output_path: str,
                             days: int = 30) -> str:
        """
        导出分析报告
        
        Args:
            lottery_type: 彩票类型
            output_path: 输出文件路径
            days: 分析天数
            
        Returns:
            报告文件路径
        """
        report = self.get_overall_performance_report(lottery_type, days)
        
        # 生成详细报告
        report_content = []
        report_content.append("=" * 80)
        report_content.append("🎯 预测准确性分析报告")
        report_content.append("=" * 80)
        
        report_content.append(f"彩票类型: {lottery_type}")
        report_content.append(f"分析周期: {days} 天")
        report_content.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 整体统计
        stats = report['overall_statistics']
        report_content.append(f"\n📊 整体统计:")
        report_content.append(f"  总预测数: {stats['total_predictions']}")
        report_content.append(f"  成功预测数: {stats['successful_predictions']}")
        report_content.append(f"  成功率: {stats['success_rate']:.1f}%")
        report_content.append(f"  平均匹配数: {stats['average_matches']:.2f}")
        report_content.append(f"  平均准确度: {stats['average_accuracy']:.1f}")
        report_content.append(f"  最佳匹配数: {stats['best_matches']}")
        
        # 算法排名
        report_content.append(f"\n🏆 算法性能排名:")
        for i, alg in enumerate(report['algorithm_rankings'], 1):
            report_content.append(
                f"  {i}. {alg['algorithm']}: "
                f"预测{alg['predictions']}次, "
                f"平均{alg['avg_matches']:.2f}个匹配, "
                f"成功率{alg['success_rate']:.1f}%, "
                f"平均分数{alg['avg_score']:.1f}"
            )
        
        # 多组分析
        if report['group_analysis']:
            report_content.append(f"\n📊 多组预测分析:")
            for group in report['group_analysis']:
                report_content.append(
                    f"  第{group['group_id']}组: "
                    f"预测{group['predictions']}次, "
                    f"平均{group['avg_matches']:.2f}个匹配, "
                    f"平均分数{group['avg_score']:.1f}"
                )
        
        # 建议
        report_content.append(f"\n💡 改进建议:")
        for rec in report['recommendations']:
            report_content.append(f"  • {rec}")
        
        report_content.append("\n" + "=" * 80)
        
        # 保存到文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{output_path}/accuracy_report_{lottery_type}_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_content))
        
        logger.info(f"分析报告已导出: {filename}")
        return filename
    
    def get_real_time_metrics(self, lottery_type: str) -> Dict[str, Any]:
        """获取实时指标"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 最近7天的表现
        cursor.execute('''
        SELECT algorithm,
               COUNT(*) as predictions,
               AVG(matches) as avg_matches,
               AVG(accuracy_score) as avg_score
        FROM prediction_records 
        WHERE lottery_type = ? AND status = 'verified'
              AND draw_date >= date('now', '-7 days')
        GROUP BY algorithm
        ORDER BY avg_score DESC
        ''', (lottery_type,))
        
        recent_performance = cursor.fetchall()
        
        # 待验证的预测数
        cursor.execute('''
        SELECT COUNT(*) 
        FROM prediction_records 
        WHERE lottery_type = ? AND status = 'pending'
        ''', (lottery_type,))
        
        pending_count = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            'pending_predictions': pending_count,
            'recent_performance': [
                {
                    'algorithm': row[0],
                    'predictions': row[1],
                    'avg_matches': row[2],
                    'avg_score': row[3]
                }
                for row in recent_performance
            ],
            'last_updated': datetime.now().isoformat()
        }


# 使用示例
if __name__ == "__main__":
    # 创建跟踪器实例
    tracker = AccuracyTracker()
    
    # 模拟预测记录
    prediction_result = {
        'main_numbers': [5, 12, 18, 25, 33, 36],
        'special_number': 3,
        'confidence': 75.5,
        'method': 'enhanced_multi_algorithm'
    }
    
    # 记录预测
    prediction_id = tracker.record_prediction(
        prediction_result, 
        'powercolor', 
        '2025-08-19',
        'single'
    )
    
    print(f"预测已记录: {prediction_id}")
    
    # 模拟验证结果
    verification_result = tracker.verify_predictions(
        '2025-08-19',
        [3, 12, 18, 22, 33, 38],
        5,
        'powercolor'
    )
    
    print("验证结果:", verification_result)
    
    # 获取性能报告
    report = tracker.get_overall_performance_report('powercolor', 30)
    print("性能报告:", json.dumps(report, indent=2, ensure_ascii=False))