#!/usr/bin/env python3
"""
多组预测器 - 提升预测命中率的核心模块
支持生成多组高质量预测，显著提升中奖概率
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import itertools
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler

logger = logging.getLogger('multi_group_predictor')

class MultiGroupPredictor:
    """
    多组预测器
    
    核心功能：
    1. 生成多组高质量预测（3-10组）
    2. 智能分散风险，避免预测重复
    3. 动态调整组数和质量阈值
    4. 提供详细的预测分析和建议
    """
    
    def __init__(self, lottery_type='powercolor', db_manager=None):
        """
        初始化多组预测器
        
        Args:
            lottery_type: 彩票类型
            db_manager: 数据库管理器
        """
        self.lottery_type = lottery_type
        self.db_manager = db_manager
        
        # 彩票配置
        self.config = {
            'powercolor': {'main_numbers': 38, 'main_count': 6, 'special_numbers': 8, 'has_special': True},
            'lotto649': {'main_numbers': 49, 'main_count': 6, 'special_numbers': 10, 'has_special': True},
            'dailycash': {'main_numbers': 39, 'main_count': 5, 'special_numbers': 0, 'has_special': False}
        }
        
        self.main_numbers = self.config[lottery_type]['main_numbers']
        self.main_count = self.config[lottery_type]['main_count']
        self.special_numbers = self.config[lottery_type]['special_numbers']
        self.has_special = self.config[lottery_type]['has_special']
        
        # 多组预测配置
        self.multi_group_config = {
            'default_group_count': 5,          # 默认生成5组预测
            'max_group_count': 10,             # 最大10组预测
            'min_confidence_gap': 5,           # 组间信心度最小差距
            'diversity_threshold': 0.3,        # 多样性阈值
            'quality_threshold': 60,           # 最低质量阈值
            'risk_distribution': 'balanced'    # 风险分布策略
        }
        
        # 预测策略
        self.prediction_strategies = {
            'conservative': {
                'group_count': 3,
                'confidence_weights': [0.5, 0.3, 0.2],
                'risk_level': 'low',
                'description': '保守策略：3组高信心度预测'
            },
            'balanced': {
                'group_count': 5,
                'confidence_weights': [0.3, 0.25, 0.2, 0.15, 0.1],
                'risk_level': 'medium',
                'description': '平衡策略：5组中等信心度预测'
            },
            'aggressive': {
                'group_count': 8,
                'confidence_weights': [0.2, 0.15, 0.15, 0.15, 0.1, 0.1, 0.1, 0.05],
                'risk_level': 'high',
                'description': '激进策略：8组覆盖更多可能性'
            }
        }
        
        logger.info(f"多组预测器初始化完成 - 彩票类型: {lottery_type}")
    
    def generate_multi_group_predictions(self, 
                                       enhanced_predictor, 
                                       df: pd.DataFrame, 
                                       strategy: str = 'balanced',
                                       custom_group_count: Optional[int] = None) -> Dict[str, Any]:
        """
        生成多组预测
        
        Args:
            enhanced_predictor: 增强版预测器实例
            df: 历史数据
            strategy: 预测策略 ('conservative', 'balanced', 'aggressive')
            custom_group_count: 自定义组数
            
        Returns:
            包含多组预测的详细结果
        """
        logger.info(f"开始生成多组预测 - 策略: {strategy}")
        
        # 获取策略配置
        strategy_config = self.prediction_strategies.get(strategy, self.prediction_strategies['balanced'])
        group_count = custom_group_count or strategy_config['group_count']
        
        # 1. 收集所有算法的预测结果
        all_algorithm_predictions = self._collect_algorithm_predictions(enhanced_predictor, df)
        
        if not all_algorithm_predictions:
            logger.error("无法获取算法预测结果")
            return None
        
        # 2. 生成多组预测
        multi_groups = self._generate_diverse_groups(all_algorithm_predictions, group_count)
        
        # 3. 优化和排序
        optimized_groups = self._optimize_prediction_groups(multi_groups, df)
        
        # 4. 计算每组的详细指标
        final_predictions = self._calculate_group_metrics(optimized_groups, strategy_config)
        
        # 5. 生成综合报告
        result = {
            'strategy': strategy,
            'strategy_description': strategy_config['description'],
            'total_groups': len(final_predictions),
            'groups': final_predictions,
            'summary': self._generate_summary_statistics(final_predictions),
            'recommendations': self._generate_recommendations(final_predictions, strategy),
            'timestamp': datetime.now().isoformat(),
            'coverage_analysis': self._analyze_number_coverage(final_predictions),
            'risk_assessment': self._assess_risk_distribution(final_predictions)
        }
        
        logger.info(f"多组预测生成完成 - 共{len(final_predictions)}组")
        
        return result
    
    def _collect_algorithm_predictions(self, enhanced_predictor, df) -> List[Dict[str, Any]]:
        """收集所有算法的预测结果"""
        logger.info("收集算法预测结果...")
        
        # 初始化预测器
        enhanced_predictor.initialize_all_predictors(df)
        
        predictions = []
        
        # 调用各种预测方法
        prediction_methods = [
            ('optimized_board_path', enhanced_predictor.predict_with_optimized_board_path),
            ('feature_enhanced', enhanced_predictor.predict_with_feature_enhanced),
            ('enhanced_frequency', enhanced_predictor.predict_with_enhanced_frequency),
            ('enhanced_pattern', enhanced_predictor.predict_with_enhanced_pattern),
            ('trend_analysis', enhanced_predictor.predict_with_trend_analysis),
            ('neural_network', enhanced_predictor.predict_with_neural_network)
        ]
        
        for method_name, method_func in prediction_methods:
            try:
                prediction = method_func(df)
                if prediction and 'main_numbers' in prediction:
                    prediction['algorithm'] = method_name
                    prediction['unique_id'] = f"{method_name}_{datetime.now().strftime('%H%M%S')}"
                    predictions.append(prediction)
                    logger.info(f"{method_name}: {prediction['main_numbers']} (信心度: {prediction['confidence']:.1f}%)")
            except Exception as e:
                logger.warning(f"{method_name} 预测失败: {e}")
        
        # 生成变体预测（通过参数调整）
        variant_predictions = self._generate_algorithm_variants(enhanced_predictor, df, predictions)
        predictions.extend(variant_predictions)
        
        logger.info(f"收集到 {len(predictions)} 个算法预测结果")
        return predictions
    
    def _generate_algorithm_variants(self, enhanced_predictor, df, base_predictions) -> List[Dict[str, Any]]:
        """生成算法变体预测"""
        variants = []
        
        # 方法1：调整权重生成变体
        for i in range(3):
            try:
                # 随机调整算法权重
                original_weights = enhanced_predictor.algorithm_weights.copy()
                
                # 生成随机权重调整
                weight_multipliers = np.random.uniform(0.7, 1.3, len(original_weights))
                for j, (key, weight) in enumerate(original_weights.items()):
                    enhanced_predictor.algorithm_weights[key] = weight * weight_multipliers[j]
                
                # 重新归一化权重
                total_weight = sum(enhanced_predictor.algorithm_weights.values())
                for key in enhanced_predictor.algorithm_weights:
                    enhanced_predictor.algorithm_weights[key] /= total_weight
                
                # 生成变体预测
                variant = enhanced_predictor.enhanced_ensemble_predict(df, ensemble_size=10)
                if variant and 'main_numbers' in variant:
                    variant['algorithm'] = f'ensemble_variant_{i+1}'
                    variant['unique_id'] = f"variant_{i+1}_{datetime.now().strftime('%H%M%S')}"
                    variants.append(variant)
                
                # 恢复原始权重
                enhanced_predictor.algorithm_weights = original_weights
                
            except Exception as e:
                logger.warning(f"生成变体预测 {i+1} 失败: {e}")
        
        # 方法2：基于现有预测生成混合变体
        if len(base_predictions) >= 2:
            hybrid_variants = self._generate_hybrid_predictions(base_predictions)
            variants.extend(hybrid_variants)
        
        logger.info(f"生成 {len(variants)} 个算法变体")
        return variants
    
    def _generate_hybrid_predictions(self, base_predictions) -> List[Dict[str, Any]]:
        """生成混合预测"""
        hybrids = []
        
        # 两两组合生成混合预测
        for i, pred1 in enumerate(base_predictions[:4]):  # 限制组合数量
            for j, pred2 in enumerate(base_predictions[i+1:5], i+1):
                try:
                    # 合并两个预测的号码
                    combined_numbers = list(set(pred1['main_numbers'] + pred2['main_numbers']))
                    
                    if len(combined_numbers) >= self.main_count:
                        # 选择得分最高的号码
                        selected_numbers = combined_numbers[:self.main_count]
                        
                        # 如果数量不足，随机补充
                        while len(selected_numbers) < self.main_count:
                            available = [n for n in range(1, self.main_numbers + 1) 
                                       if n not in selected_numbers]
                            if available:
                                selected_numbers.append(np.random.choice(available))
                        
                        # 计算混合信心度
                        hybrid_confidence = (pred1['confidence'] + pred2['confidence']) / 2 * 0.9  # 稍微降低
                        
                        hybrid = {
                            'main_numbers': sorted(selected_numbers[:self.main_count]),
                            'confidence': hybrid_confidence,
                            'method': f"hybrid_{pred1['method']}_{pred2['method']}",
                            'algorithm': f"hybrid_{i}_{j}",
                            'unique_id': f"hybrid_{i}_{j}_{datetime.now().strftime('%H%M%S')}",
                            'parent_methods': [pred1['method'], pred2['method']],
                            'special_number': np.random.randint(1, self.special_numbers + 1) if self.has_special else None
                        }
                        
                        hybrids.append(hybrid)
                        
                except Exception as e:
                    logger.warning(f"生成混合预测 {i}-{j} 失败: {e}")
        
        logger.info(f"生成 {len(hybrids)} 个混合预测")
        return hybrids[:5]  # 限制混合预测数量
    
    def _generate_diverse_groups(self, all_predictions, target_group_count) -> List[Dict[str, Any]]:
        """生成多样化的预测组"""
        logger.info(f"生成多样化预测组 - 目标组数: {target_group_count}")
        
        if len(all_predictions) < target_group_count:
            logger.warning(f"可用预测数({len(all_predictions)}) < 目标组数({target_group_count})")
            return all_predictions
        
        # 方法1：基于聚类的分组
        clustered_groups = self._cluster_based_grouping(all_predictions, target_group_count)
        
        # 方法2：基于多样性的选择
        diverse_groups = self._diversity_based_selection(all_predictions, target_group_count)
        
        # 方法3：基于信心度分层
        confidence_groups = self._confidence_based_selection(all_predictions, target_group_count)
        
        # 合并和去重
        combined_groups = clustered_groups + diverse_groups + confidence_groups
        unique_groups = self._deduplicate_groups(combined_groups, target_group_count)
        
        logger.info(f"生成了 {len(unique_groups)} 个多样化预测组")
        return unique_groups
    
    def _cluster_based_grouping(self, predictions, target_count) -> List[Dict[str, Any]]:
        """基于聚类的分组方法"""
        if len(predictions) < 2:
            return predictions
        
        try:
            # 准备特征矩阵
            features = []
            for pred in predictions:
                # 使用号码的独热编码作为特征
                feature_vector = [0] * self.main_numbers
                for num in pred['main_numbers']:
                    if 1 <= num <= self.main_numbers:
                        feature_vector[num - 1] = 1
                
                # 添加其他特征
                feature_vector.extend([
                    pred['confidence'] / 100,
                    sum(pred['main_numbers']) / (self.main_numbers * self.main_count),  # 归一化和值
                    len([n for n in pred['main_numbers'] if n % 2 == 1]) / self.main_count,  # 奇数比例
                ])
                
                features.append(feature_vector)
            
            # 应用K-means聚类
            n_clusters = min(target_count, len(predictions))
            scaler = StandardScaler()
            features_scaled = scaler.fit_transform(features)
            
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            cluster_labels = kmeans.fit_predict(features_scaled)
            
            # 从每个聚类中选择最佳预测
            clustered_groups = []
            for cluster_id in range(n_clusters):
                cluster_predictions = [predictions[i] for i in range(len(predictions)) 
                                    if cluster_labels[i] == cluster_id]
                
                if cluster_predictions:
                    # 选择信心度最高的
                    best_in_cluster = max(cluster_predictions, key=lambda x: x['confidence'])
                    best_in_cluster['selection_method'] = 'cluster_based'
                    best_in_cluster['cluster_id'] = cluster_id
                    clustered_groups.append(best_in_cluster)
            
            return clustered_groups
            
        except Exception as e:
            logger.warning(f"聚类分组失败: {e}")
            return []
    
    def _diversity_based_selection(self, predictions, target_count) -> List[Dict[str, Any]]:
        """基于多样性的选择方法"""
        if not predictions:
            return []
        
        selected = []
        remaining = predictions.copy()
        
        # 首先选择信心度最高的
        best_prediction = max(remaining, key=lambda x: x['confidence'])
        selected.append(best_prediction)
        remaining.remove(best_prediction)
        
        # 逐步选择与已选择预测差异最大的
        while len(selected) < target_count and remaining:
            best_candidate = None
            max_diversity_score = -1
            
            for candidate in remaining:
                diversity_score = self._calculate_diversity_score(candidate, selected)
                
                if diversity_score > max_diversity_score:
                    max_diversity_score = diversity_score
                    best_candidate = candidate
            
            if best_candidate:
                best_candidate['selection_method'] = 'diversity_based'
                best_candidate['diversity_score'] = max_diversity_score
                selected.append(best_candidate)
                remaining.remove(best_candidate)
        
        return selected
    
    def _confidence_based_selection(self, predictions, target_count) -> List[Dict[str, Any]]:
        """基于信心度分层的选择方法"""
        # 按信心度排序
        sorted_predictions = sorted(predictions, key=lambda x: x['confidence'], reverse=True)
        
        # 分层选择
        confidence_groups = []
        step = max(1, len(sorted_predictions) // target_count)
        
        for i in range(0, min(len(sorted_predictions), target_count * step), step):
            if i < len(sorted_predictions):
                pred = sorted_predictions[i].copy()
                pred['selection_method'] = 'confidence_based'
                pred['confidence_rank'] = i // step + 1
                confidence_groups.append(pred)
        
        return confidence_groups[:target_count]
    
    def _calculate_diversity_score(self, candidate, selected_predictions) -> float:
        """计算多样性分数"""
        if not selected_predictions:
            return 1.0
        
        diversity_scores = []
        candidate_numbers = set(candidate['main_numbers'])
        
        for selected in selected_predictions:
            selected_numbers = set(selected['main_numbers'])
            
            # 计算号码重叠度（越小越好）
            overlap = len(candidate_numbers.intersection(selected_numbers))
            max_overlap = min(len(candidate_numbers), len(selected_numbers))
            overlap_score = 1.0 - (overlap / max_overlap) if max_overlap > 0 else 1.0
            
            # 计算信心度差异（适度差异更好）
            confidence_diff = abs(candidate['confidence'] - selected['confidence'])
            confidence_score = min(1.0, confidence_diff / 50)  # 归一化到[0,1]
            
            # 综合多样性分数
            combined_score = overlap_score * 0.7 + confidence_score * 0.3
            diversity_scores.append(combined_score)
        
        # 返回平均多样性分数
        return np.mean(diversity_scores)
    
    def _deduplicate_groups(self, groups, target_count) -> List[Dict[str, Any]]:
        """去重并选择最佳组合"""
        unique_groups = []
        seen_combinations = set()
        
        # 按信心度排序
        sorted_groups = sorted(groups, key=lambda x: x['confidence'], reverse=True)
        
        for group in sorted_groups:
            # 创建号码组合的唯一标识
            combination_key = tuple(sorted(group['main_numbers']))
            
            if combination_key not in seen_combinations:
                seen_combinations.add(combination_key)
                unique_groups.append(group)
                
                if len(unique_groups) >= target_count:
                    break
        
        return unique_groups
    
    def _optimize_prediction_groups(self, groups, df) -> List[Dict[str, Any]]:
        """优化预测组"""
        logger.info("优化预测组...")
        
        optimized_groups = []
        
        for i, group in enumerate(groups):
            # 优化号码组合
            optimized_numbers = self._optimize_number_combination(group['main_numbers'], df)
            
            # 重新计算信心度
            adjusted_confidence = self._recalculate_confidence(optimized_numbers, group, df)
            
            # 生成特别号
            special_number = self._predict_optimal_special_number(df) if self.has_special else None
            
            optimized_group = {
                'group_id': i + 1,
                'main_numbers': optimized_numbers,
                'confidence': adjusted_confidence,
                'special_number': special_number,
                'original_method': group.get('algorithm', 'unknown'),
                'selection_method': group.get('selection_method', 'unknown'),
                'optimization_applied': True
            }
            
            optimized_groups.append(optimized_group)
        
        # 按信心度排序
        optimized_groups.sort(key=lambda x: x['confidence'], reverse=True)
        
        logger.info(f"预测组优化完成 - {len(optimized_groups)}组")
        return optimized_groups
    
    def _optimize_number_combination(self, numbers, df) -> List[int]:
        """优化号码组合"""
        # 检查组合的合理性并进行微调
        optimized = numbers.copy()
        
        # 检查奇偶比例
        odd_count = sum(1 for n in optimized if n % 2 == 1)
        if odd_count < 2:  # 奇数太少
            even_numbers = [n for n in optimized if n % 2 == 0]
            if even_numbers:
                # 替换一个偶数为奇数
                replace_even = even_numbers[0]
                optimized.remove(replace_even)
                # 找一个合适的奇数替换
                available_odds = [n for n in range(1, self.main_numbers + 1, 2) 
                                if n not in optimized]
                if available_odds:
                    optimized.append(available_odds[0])
        
        elif odd_count > 4:  # 奇数太多
            odd_numbers = [n for n in optimized if n % 2 == 1]
            if len(odd_numbers) > 1:
                # 替换一个奇数为偶数
                replace_odd = odd_numbers[0]
                optimized.remove(replace_odd)
                # 找一个合适的偶数替换
                available_evens = [n for n in range(2, self.main_numbers + 1, 2) 
                                 if n not in optimized]
                if available_evens:
                    optimized.append(available_evens[0])
        
        return sorted(optimized)
    
    def _recalculate_confidence(self, numbers, original_group, df) -> float:
        """重新计算信心度"""
        base_confidence = original_group['confidence']
        
        # 基于历史数据调整信心度
        historical_performance = self._analyze_historical_performance(numbers, df)
        
        # 组合合理性评分
        combination_score = self._evaluate_combination_quality(numbers)
        
        # 综合调整
        adjusted_confidence = base_confidence * 0.7 + historical_performance * 20 + combination_score * 10
        
        return min(95, max(50, adjusted_confidence))
    
    def _analyze_historical_performance(self, numbers, df) -> float:
        """分析历史表现"""
        if df.empty:
            return 0.5
        
        # 分析这些号码在历史中的表现
        main_cols = [f'Anumber{i}' for i in range(1, self.main_count + 1)]
        
        match_scores = []
        for _, row in df.tail(20).iterrows():  # 检查最近20期
            historical_numbers = [row[col] for col in main_cols]
            matches = len(set(numbers).intersection(set(historical_numbers)))
            match_scores.append(matches / self.main_count)
        
        return np.mean(match_scores) if match_scores else 0.5
    
    def _evaluate_combination_quality(self, numbers) -> float:
        """评估组合质量"""
        quality_score = 0.5
        
        # 奇偶比例评分
        odd_count = sum(1 for n in numbers if n % 2 == 1)
        if 2 <= odd_count <= 4:
            quality_score += 0.2
        
        # 大小比例评分
        mid_point = self.main_numbers // 2
        small_count = sum(1 for n in numbers if n <= mid_point)
        if 2 <= small_count <= 4:
            quality_score += 0.2
        
        # 和值评分
        total_sum = sum(numbers)
        expected_sum = (self.main_numbers + 1) * self.main_count / 2
        sum_ratio = total_sum / expected_sum
        if 0.8 <= sum_ratio <= 1.2:
            quality_score += 0.1
        
        return min(1.0, quality_score)
    
    def _predict_optimal_special_number(self, df) -> Optional[int]:
        """预测最优特别号"""
        if not self.has_special:
            return None
        
        # 简单的特别号预测逻辑
        if 'Snumber' in df.columns:
            recent_specials = df['Snumber'].tail(10).tolist()
            special_freq = Counter(recent_specials)
            
            # 选择频率适中的特别号
            avg_freq = len(recent_specials) / self.special_numbers
            candidates = [num for num in range(1, self.special_numbers + 1)
                         if special_freq.get(num, 0) <= avg_freq * 1.5]
            
            if candidates:
                return np.random.choice(candidates)
        
        return np.random.randint(1, self.special_numbers + 1)
    
    def _calculate_group_metrics(self, groups, strategy_config) -> List[Dict[str, Any]]:
        """计算每组的详细指标"""
        final_predictions = []
        confidence_weights = strategy_config['confidence_weights']
        
        for i, group in enumerate(groups):
            # 应用策略权重
            if i < len(confidence_weights):
                weighted_confidence = group['confidence'] * confidence_weights[i]
                recommendation_weight = confidence_weights[i]
            else:
                weighted_confidence = group['confidence'] * 0.05
                recommendation_weight = 0.05
            
            # 计算详细指标
            metrics = self._calculate_detailed_metrics(group['main_numbers'])
            
            enhanced_group = {
                **group,
                'weighted_confidence': weighted_confidence,
                'recommendation_weight': recommendation_weight,
                'priority': i + 1,
                'metrics': metrics,
                'analysis': self._generate_group_analysis(group['main_numbers']),
                'risk_level': self._assess_group_risk(group['confidence'], i),
                'expected_return': self._calculate_expected_return(weighted_confidence, recommendation_weight)
            }
            
            final_predictions.append(enhanced_group)
        
        return final_predictions
    
    def _calculate_detailed_metrics(self, numbers) -> Dict[str, Any]:
        """计算详细指标"""
        odd_count = sum(1 for n in numbers if n % 2 == 1)
        mid_point = self.main_numbers // 2
        small_count = sum(1 for n in numbers if n <= mid_point)
        total_sum = sum(numbers)
        span = max(numbers) - min(numbers)
        
        # 连续数检查
        consecutive_count = 0
        sorted_nums = sorted(numbers)
        for i in range(1, len(sorted_nums)):
            if sorted_nums[i] == sorted_nums[i-1] + 1:
                consecutive_count += 1
        
        return {
            'odd_count': odd_count,
            'even_count': self.main_count - odd_count,
            'small_count': small_count,
            'large_count': self.main_count - small_count,
            'total_sum': total_sum,
            'average': total_sum / self.main_count,
            'span': span,
            'consecutive_pairs': consecutive_count,
            'balance_score': self._calculate_balance_score(odd_count, small_count)
        }
    
    def _calculate_balance_score(self, odd_count, small_count) -> float:
        """计算平衡分数"""
        # 理想的奇偶比例是3:3或2:4
        odd_score = 1.0 - abs(odd_count - 3) / 3
        
        # 理想的大小比例是3:3或2:4
        size_score = 1.0 - abs(small_count - 3) / 3
        
        return (odd_score + size_score) / 2
    
    def _generate_group_analysis(self, numbers) -> str:
        """生成组分析"""
        metrics = self._calculate_detailed_metrics(numbers)
        
        analysis_parts = []
        
        # 奇偶分析
        if metrics['odd_count'] == 3:
            analysis_parts.append("奇偶平衡良好")
        elif metrics['odd_count'] > 3:
            analysis_parts.append("奇数偏多")
        else:
            analysis_parts.append("偶数偏多")
        
        # 大小分析
        if metrics['small_count'] == 3:
            analysis_parts.append("大小数平衡")
        elif metrics['small_count'] > 3:
            analysis_parts.append("小数偏多")
        else:
            analysis_parts.append("大数偏多")
        
        # 连续数分析
        if metrics['consecutive_pairs'] > 0:
            analysis_parts.append(f"包含{metrics['consecutive_pairs']}对连续数")
        
        # 和值分析
        expected_sum = (self.main_numbers + 1) * self.main_count / 2
        if metrics['total_sum'] > expected_sum * 1.1:
            analysis_parts.append("和值偏高")
        elif metrics['total_sum'] < expected_sum * 0.9:
            analysis_parts.append("和值偏低")
        else:
            analysis_parts.append("和值适中")
        
        return "，".join(analysis_parts)
    
    def _assess_group_risk(self, confidence, priority) -> str:
        """评估组风险"""
        if confidence >= 80 and priority <= 2:
            return "低风险"
        elif confidence >= 65 and priority <= 4:
            return "中等风险"
        else:
            return "高风险"
    
    def _calculate_expected_return(self, weighted_confidence, recommendation_weight) -> float:
        """计算期望回报"""
        # 简化的期望回报计算
        return (weighted_confidence / 100) * recommendation_weight * 10
    
    def _generate_summary_statistics(self, predictions) -> Dict[str, Any]:
        """生成汇总统计"""
        if not predictions:
            return {}
        
        confidences = [p['confidence'] for p in predictions]
        weights = [p['recommendation_weight'] for p in predictions]
        
        # 号码覆盖分析
        all_numbers = set()
        for pred in predictions:
            all_numbers.update(pred['main_numbers'])
        
        return {
            'total_groups': len(predictions),
            'confidence_range': {
                'min': min(confidences),
                'max': max(confidences),
                'average': np.mean(confidences),
                'median': np.median(confidences)
            },
            'weight_distribution': {
                'total_weight': sum(weights),
                'top_3_weight': sum(weights[:3]) if len(weights) >= 3 else sum(weights)
            },
            'number_coverage': {
                'unique_numbers': len(all_numbers),
                'coverage_percentage': len(all_numbers) / self.main_numbers * 100,
                'most_common': self._find_most_common_numbers(predictions)
            },
            'risk_distribution': {
                'low_risk': len([p for p in predictions if p['risk_level'] == '低风险']),
                'medium_risk': len([p for p in predictions if p['risk_level'] == '中等风险']),
                'high_risk': len([p for p in predictions if p['risk_level'] == '高风险'])
            }
        }
    
    def _find_most_common_numbers(self, predictions) -> List[Tuple[int, int]]:
        """找出最常见的号码"""
        number_counts = Counter()
        for pred in predictions:
            number_counts.update(pred['main_numbers'])
        
        return number_counts.most_common(10)
    
    def _generate_recommendations(self, predictions, strategy) -> List[str]:
        """生成推荐建议"""
        recommendations = []
        
        if not predictions:
            return ["无可用预测，请检查数据"]
        
        # 策略建议
        if strategy == 'conservative':
            recommendations.append("保守策略：建议重点关注前3组预测，风险较低")
        elif strategy == 'balanced':
            recommendations.append("平衡策略：建议分散投注前5组，平衡风险与收益")
        elif strategy == 'aggressive':
            recommendations.append("激进策略：可考虑覆盖更多组合，但注意风险控制")
        
        # 信心度建议
        high_confidence_count = len([p for p in predictions if p['confidence'] >= 75])
        if high_confidence_count >= 3:
            recommendations.append(f"有{high_confidence_count}组高信心度预测，建议优先考虑")
        
        # 覆盖率建议
        all_numbers = set()
        for pred in predictions:
            all_numbers.update(pred['main_numbers'])
        
        coverage_rate = len(all_numbers) / self.main_numbers * 100
        if coverage_rate >= 60:
            recommendations.append(f"号码覆盖率{coverage_rate:.1f}%，覆盖面较广")
        else:
            recommendations.append(f"号码覆盖率{coverage_rate:.1f}%，相对集中")
        
        # 风险提示
        recommendations.append("多组预测能提升命中概率，但请理性投注，控制风险")
        
        return recommendations
    
    def _analyze_number_coverage(self, predictions) -> Dict[str, Any]:
        """分析号码覆盖情况"""
        if not predictions:
            return {}
        
        number_frequency = Counter()
        for pred in predictions:
            number_frequency.update(pred['main_numbers'])
        
        # 分析覆盖模式
        uncovered_numbers = [n for n in range(1, self.main_numbers + 1) 
                           if n not in number_frequency]
        
        coverage_analysis = {
            'total_covered_numbers': len(number_frequency),
            'uncovered_numbers': uncovered_numbers,
            'coverage_percentage': len(number_frequency) / self.main_numbers * 100,
            'frequency_distribution': dict(number_frequency.most_common()),
            'hot_numbers': [num for num, freq in number_frequency.items() if freq >= 3],
            'cold_numbers': uncovered_numbers
        }
        
        return coverage_analysis
    
    def _assess_risk_distribution(self, predictions) -> Dict[str, Any]:
        """评估风险分布"""
        if not predictions:
            return {}
        
        risk_levels = [p['risk_level'] for p in predictions]
        confidences = [p['confidence'] for p in predictions]
        
        risk_assessment = {
            'overall_risk_level': self._calculate_overall_risk(risk_levels, confidences),
            'risk_balance': {
                '低风险': risk_levels.count('低风险'),
                '中等风险': risk_levels.count('中等风险'),
                '高风险': risk_levels.count('高风险')
            },
            'confidence_variance': np.var(confidences),
            'recommended_allocation': self._recommend_allocation(predictions)
        }
        
        return risk_assessment
    
    def _calculate_overall_risk(self, risk_levels, confidences) -> str:
        """计算整体风险水平"""
        avg_confidence = np.mean(confidences)
        low_risk_ratio = risk_levels.count('低风险') / len(risk_levels)
        
        if avg_confidence >= 75 and low_risk_ratio >= 0.4:
            return '整体低风险'
        elif avg_confidence >= 60 and low_risk_ratio >= 0.2:
            return '整体中等风险'
        else:
            return '整体高风险'
    
    def _recommend_allocation(self, predictions) -> Dict[str, str]:
        """推荐分配策略"""
        total_groups = len(predictions)
        high_conf_groups = len([p for p in predictions if p['confidence'] >= 75])
        
        if high_conf_groups >= 3:
            return {
                '建议': '重点投注前3组高信心度预测',
                '分配': '70%资金投注前3组，30%分散其他组'
            }
        elif total_groups >= 5:
            return {
                '建议': '平均分配前5组预测',
                '分配': '每组15-25%资金，保持风险分散'
            }
        else:
            return {
                '建议': '平均分配所有预测组',
                '分配': f'每组{100//total_groups:.0f}%左右资金'
            }
    
    def generate_prediction_report(self, result) -> str:
        """生成预测报告"""
        if not result:
            return "无法生成预测报告"
        
        report = []
        report.append("=" * 100)
        report.append("🎯 多组预测系统 - 综合预测报告")
        report.append("=" * 100)
        
        # 基本信息
        report.append(f"📊 预测策略: {result['strategy']}")
        report.append(f"📝 策略说明: {result['strategy_description']}")
        report.append(f"🔢 预测组数: {result['total_groups']}")
        report.append(f"⏰ 生成时间: {result['timestamp']}")
        
        # 详细预测结果
        report.append(f"\n🎲 详细预测结果:")
        report.append("-" * 80)
        
        for i, group in enumerate(result['groups'], 1):
            report.append(f"\n第{i}组预测 (优先级: {group['priority']})")
            report.append(f"  主号码: {group['main_numbers']}")
            if group.get('special_number'):
                report.append(f"  特别号: {group['special_number']}")
            report.append(f"  信心度: {group['confidence']:.1f}%")
            report.append(f"  权重: {group['recommendation_weight']:.1%}")
            report.append(f"  风险级别: {group['risk_level']}")
            report.append(f"  预测来源: {group['original_method']}")
            report.append(f"  分析: {group['analysis']}")
            
            # 详细指标
            metrics = group['metrics']
            report.append(f"  指标: 奇数{metrics['odd_count']}个, 小数{metrics['small_count']}个, "
                         f"和值{metrics['total_sum']}, 跨度{metrics['span']}")
        
        # 汇总统计
        summary = result['summary']
        report.append(f"\n📈 汇总统计:")
        report.append("-" * 40)
        report.append(f"信心度范围: {summary['confidence_range']['min']:.1f}% - {summary['confidence_range']['max']:.1f}%")
        report.append(f"平均信心度: {summary['confidence_range']['average']:.1f}%")
        report.append(f"号码覆盖率: {summary['number_coverage']['coverage_percentage']:.1f}%")
        report.append(f"覆盖号码数: {summary['number_coverage']['unique_numbers']}/{self.main_numbers}")
        
        # 风险分析
        risk_dist = summary['risk_distribution']
        report.append(f"风险分布: 低风险{risk_dist['low_risk']}组, "
                     f"中等风险{risk_dist['medium_risk']}组, 高风险{risk_dist['high_risk']}组")
        
        # 推荐建议
        report.append(f"\n💡 推荐建议:")
        report.append("-" * 40)
        for rec in result['recommendations']:
            report.append(f"• {rec}")
        
        # 覆盖分析
        coverage = result['coverage_analysis']
        if coverage.get('hot_numbers'):
            report.append(f"\n🔥 热门号码: {coverage['hot_numbers']}")
        if coverage.get('cold_numbers'):
            cold_display = coverage['cold_numbers'][:10]  # 显示前10个
            report.append(f"❄️ 冷门号码: {cold_display}")
        
        # 风险评估
        risk_assessment = result['risk_assessment']
        report.append(f"\n⚖️ 风险评估:")
        report.append(f"整体风险水平: {risk_assessment['overall_risk_level']}")
        allocation = risk_assessment['recommended_allocation']
        report.append(f"推荐分配: {allocation['建议']}")
        report.append(f"资金分配: {allocation['分配']}")
        
        report.append("\n" + "=" * 100)
        report.append("⚠️ 风险提示: 多组预测仅供参考，请理性投注，控制风险！")
        report.append("=" * 100)
        
        return "\n".join(report)


# 使用示例和测试代码
if __name__ == "__main__":
    # 测试多组预测器
    multi_predictor = MultiGroupPredictor('powercolor')
    
    # 模拟测试数据
    test_data = pd.DataFrame({
        'Anumber1': np.random.randint(1, 39, 100),
        'Anumber2': np.random.randint(1, 39, 100),
        'Anumber3': np.random.randint(1, 39, 100),
        'Anumber4': np.random.randint(1, 39, 100),
        'Anumber5': np.random.randint(1, 39, 100),
        'Anumber6': np.random.randint(1, 39, 100),
        'Snumber': np.random.randint(1, 9, 100)
    })
    
    print("多组预测器测试完成！")