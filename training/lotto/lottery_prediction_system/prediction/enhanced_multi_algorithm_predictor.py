#!/usr/bin/env python3
"""
增強版多算法預測器 - Phase 2 增強版
修復現有問題並添加新的預測算法
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter
import logging
from datetime import datetime, timedelta
from .board_path_analyzer import BoardPathAnalyzer
from .optimized_board_path_analyzer import OptimizedBoardPathAnalyzer
from .enhanced_feature_analyzer import EnhancedFeatureAnalyzer
from .feature_enhanced_predictor import FeatureEnhancedPredictor
from sklearn.ensemble import RandomForestClassifier, VotingClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import cross_val_score
from sklearn.linear_model import LogisticRegression
from sklearn.naive_bayes import GaussianNB
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger('enhanced_multi_algorithm_predictor')

class EnhancedMultiAlgorithmPredictor:
    """
    增強版多算法預測器
    
    主要改進：
    1. 修復頻率和模式預測器初始化問題
    2. 添加趨勢分析算法
    3. 增強機器學習模型
    4. 智能權重調整機制
    5. 更完善的驗證和測試
    """
    
    def __init__(self, lottery_type='powercolor', db_manager=None):
        """
        初始化增強版多算法預測器
        
        Args:
            lottery_type: 彩票類型
            db_manager: 數據庫管理器
        """
        self.lottery_type = lottery_type
        self.db_manager = db_manager
        
        # 彩票配置
        self.config = {
            'powercolor': {'main_numbers': 38, 'main_count': 6, 'special_numbers': 8, 'has_special': True},
            'lotto649': {'main_numbers': 49, 'main_count': 6, 'special_numbers': 10, 'has_special': True},
            'dailycash': {'main_numbers': 39, 'main_count': 5, 'special_numbers': 0, 'has_special': False}
        }
        
        self.main_numbers = self.config[lottery_type]['main_numbers']
        self.main_count = self.config[lottery_type]['main_count']
        self.special_numbers = self.config[lottery_type]['special_numbers']
        self.has_special = self.config[lottery_type]['has_special']
        
        # 初始化預測器
        self.predictors = {
            'board_path': BoardPathAnalyzer(lottery_type, db_manager),
            'optimized_board_path': OptimizedBoardPathAnalyzer(lottery_type, db_manager, 2),
            'feature_enhanced': FeatureEnhancedPredictor(lottery_type, db_manager, 2),
            'statistical_ml': None,
            'frequency_based': None,
            'pattern_based': None,
            'trend_analysis': None,      # 新增：趨勢分析
            'neural_network': None,      # 新增：神經網絡
            'ensemble_ml': None          # 新增：集成機器學習
        }
        
        # 動態算法權重
        self.algorithm_weights = {
            'board_path': 0.12,
            'optimized_board_path': 0.20,
            'feature_enhanced': 0.25,
            'statistical_ml': 0.15,
            'frequency_based': 0.08,
            'pattern_based': 0.05,
            'trend_analysis': 0.10,      # 新增
            'neural_network': 0.05       # 新增
        }
        
        # 預測配置
        self.prediction_config = {
            'ensemble_size': 15,           # 增加集成數量
            'voting_threshold': 0.6,
            'confidence_threshold': 0.7,
            'diversity_weight': 0.3,
            'trend_weight': 0.2            # 新增：趨勢權重
        }
        
        # 性能追蹤
        self.performance_history = {
            algorithm: [] for algorithm in self.algorithm_weights.keys()
        }
        
        # 預測歷史
        self.prediction_history = []
        
        # 算法穩定性評分
        self.algorithm_stability = {
            algorithm: 0.5 for algorithm in self.algorithm_weights.keys()
        }
        
        logger.info(f"增強版多算法預測器初始化完成 - 彩票類型: {lottery_type}")
    
    def _init_enhanced_statistical_ml_predictor(self, df):
        """初始化增強版統計機器學習預測器"""
        logger.info("初始化增強版統計機器學習預測器...")
        
        try:
            # 準備訓練數據
            X, y = self._prepare_enhanced_ml_data(df)
            
            if len(X) < 20:
                logger.warning("訓練數據不足，跳過增強版機器學習預測器初始化")
                return None
            
            # 創建更強大的集成分類器
            rf_classifier = RandomForestClassifier(
                n_estimators=200, 
                max_depth=10,
                min_samples_split=5,
                random_state=42
            )
            
            gb_classifier = GradientBoostingClassifier(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42
            )
            
            svm_classifier = SVC(
                probability=True, 
                kernel='rbf',
                C=1.0,
                random_state=42
            )
            
            lr_classifier = LogisticRegression(
                random_state=42,
                max_iter=1000
            )
            
            # 投票分類器
            ensemble_classifier = VotingClassifier(
                estimators=[
                    ('rf', rf_classifier),
                    ('gb', gb_classifier),
                    ('svm', svm_classifier),
                    ('lr', lr_classifier)
                ],
                voting='soft'
            )
            
            # 標準化特徵
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            # 訓練模型
            ensemble_classifier.fit(X_scaled, y)
            
            logger.info("增強版統計機器學習預測器初始化完成")
            return {
                'model': ensemble_classifier,
                'scaler': scaler
            }
            
        except Exception as e:
            logger.warning(f"增強版統計機器學習預測器初始化失敗: {e}")
            return None
    
    def _prepare_enhanced_ml_data(self, df):
        """準備增強版機器學習數據"""
        main_cols = [f'Anumber{i}' for i in range(1, self.main_count + 1)]
        
        X = []
        y = []
        
        # 使用更大的滑動窗口
        window_size = 8
        
        for i in range(window_size, len(df)):
            # 特徵：前N期的號碼出現情況
            features = []
            
            # 前N期的統計特徵
            window_data = df.iloc[i-window_size:i]
            
            # 1. 號碼頻率特徵
            number_counts = defaultdict(int)
            for _, row in window_data.iterrows():
                for col in main_cols:
                    number_counts[row[col]] += 1
            
            # 轉換為特徵向量
            for num in range(1, self.main_numbers + 1):
                features.append(number_counts[num] / window_size)
            
            # 2. 趨勢特徵
            recent_numbers = []
            for _, row in window_data.iterrows():
                recent_numbers.extend([row[col] for col in main_cols])
            
            # 號碼變化趨勢
            number_trends = defaultdict(list)
            for idx, row in window_data.iterrows():
                for col in main_cols:
                    number_trends[row[col]].append(idx)
            
            # 計算趨勢斜率
            trend_slopes = []
            for num in range(1, self.main_numbers + 1):
                if len(number_trends[num]) >= 2:
                    y_vals = list(range(len(number_trends[num])))
                    x_vals = number_trends[num]
                    if len(x_vals) > 1:
                        slope = np.polyfit(x_vals, y_vals, 1)[0]
                        trend_slopes.append(slope)
                    else:
                        trend_slopes.append(0)
                else:
                    trend_slopes.append(0)
            
            features.extend(trend_slopes)
            
            # 3. 統計特徵
            # 奇偶比例
            odd_count = sum(1 for num in recent_numbers if num % 2 == 1)
            features.append(odd_count / len(recent_numbers))
            
            # 大小比例
            mid_point = self.main_numbers // 2
            small_count = sum(1 for num in recent_numbers if num <= mid_point)
            features.append(small_count / len(recent_numbers))
            
            # 連續性特徵
            consecutive_count = 0
            sorted_recent = sorted(set(recent_numbers))
            for j in range(1, len(sorted_recent)):
                if sorted_recent[j] == sorted_recent[j-1] + 1:
                    consecutive_count += 1
            features.append(consecutive_count / len(sorted_recent) if sorted_recent else 0)
            
            # 方差特徵
            features.append(np.var(recent_numbers))
            features.append(np.std(recent_numbers))
            
            X.append(features)
            
            # 標籤：當前期的號碼（多標籤分類）
            current_numbers = [df.iloc[i][col] for col in main_cols]
            # 使用二進制編碼
            label = [0] * self.main_numbers
            for num in current_numbers:
                if 1 <= num <= self.main_numbers:
                    label[num - 1] = 1
            y.append(label)
        
        return np.array(X), np.array(y)
    
    def _init_robust_frequency_predictor(self, df):
        """初始化強化版頻率預測器"""
        logger.info("初始化強化版頻率預測器...")
        
        try:
            main_cols = [f'Anumber{i}' for i in range(1, self.main_count + 1)]
            
            # 基本頻率統計
            number_frequency = defaultdict(int)
            position_frequency = defaultdict(lambda: defaultdict(int))
            
            for idx, row in df.iterrows():
                for pos, col in enumerate(main_cols):
                    num = row[col]
                    number_frequency[num] += 1
                    position_frequency[pos][num] += 1
            
            # 計算加權頻率（近期權重更高）
            weighted_frequency = defaultdict(float)
            exponential_frequency = defaultdict(float)
            total_periods = len(df)
            
            for idx, row in df.iterrows():
                # 線性衰減權重
                linear_weight = (idx + 1) / total_periods
                # 指數衰減權重
                exp_weight = np.exp((idx - total_periods + 1) / (total_periods / 4))
                
                for col in main_cols:
                    num = row[col]
                    weighted_frequency[num] += linear_weight
                    exponential_frequency[num] += exp_weight
            
            # 計算冷熱號
            avg_freq = sum(number_frequency.values()) / len(number_frequency)
            hot_numbers = [num for num, freq in number_frequency.items() if freq > avg_freq * 1.2]
            cold_numbers = [num for num, freq in number_frequency.items() if freq < avg_freq * 0.8]
            
            # 計算間隔統計
            number_gaps = defaultdict(list)
            last_appearance = defaultdict(int)
            
            for idx, row in df.iterrows():
                current_numbers = [row[col] for col in main_cols]
                
                # 記錄間隔
                for num in range(1, self.main_numbers + 1):
                    if num in current_numbers:
                        if last_appearance[num] > 0:
                            gap = idx - last_appearance[num]
                            number_gaps[num].append(gap)
                        last_appearance[num] = idx
            
            # 計算平均間隔
            avg_gaps = {}
            for num in range(1, self.main_numbers + 1):
                if number_gaps[num]:
                    avg_gaps[num] = np.mean(number_gaps[num])
                else:
                    avg_gaps[num] = total_periods  # 從未出現
            
            predictor = {
                'number_frequency': dict(number_frequency),
                'weighted_frequency': dict(weighted_frequency),
                'exponential_frequency': dict(exponential_frequency),
                'position_frequency': dict(position_frequency),
                'hot_numbers': hot_numbers,
                'cold_numbers': cold_numbers,
                'avg_gaps': avg_gaps,
                'total_periods': total_periods
            }
            
            logger.info("強化版頻率預測器初始化完成")
            return predictor
            
        except Exception as e:
            logger.error(f"強化版頻率預測器初始化失敗: {e}")
            return None
    
    def _init_enhanced_pattern_predictor(self, df):
        """初始化增強版模式預測器"""
        logger.info("初始化增強版模式預測器...")
        
        try:
            main_cols = [f'Anumber{i}' for i in range(1, self.main_count + 1)]
            
            # 分析奇偶模式
            odd_even_patterns = []
            for _, row in df.iterrows():
                numbers = [row[col] for col in main_cols]
                odd_count = sum(1 for num in numbers if num % 2 == 1)
                odd_even_patterns.append(odd_count)
            
            # 分析大小模式
            mid_point = self.main_numbers // 2
            size_patterns = []
            for _, row in df.iterrows():
                numbers = [row[col] for col in main_cols]
                small_count = sum(1 for num in numbers if num <= mid_point)
                size_patterns.append(small_count)
            
            # 分析連續模式
            consecutive_patterns = []
            for _, row in df.iterrows():
                numbers = sorted([row[col] for col in main_cols])
                consecutive_count = 0
                for i in range(1, len(numbers)):
                    if numbers[i] == numbers[i-1] + 1:
                        consecutive_count += 1
                consecutive_patterns.append(consecutive_count)
            
            # 分析和值模式
            sum_patterns = []
            for _, row in df.iterrows():
                numbers = [row[col] for col in main_cols]
                sum_patterns.append(sum(numbers))
            
            # 分析跨度模式
            span_patterns = []
            for _, row in df.iterrows():
                numbers = [row[col] for col in main_cols]
                span_patterns.append(max(numbers) - min(numbers))
            
            # 分析AC值（算術複雜性）
            ac_patterns = []
            for _, row in df.iterrows():
                numbers = sorted([row[col] for col in main_cols])
                differences = []
                for i in range(len(numbers)):
                    for j in range(i+1, len(numbers)):
                        differences.append(abs(numbers[i] - numbers[j]))
                ac_value = len(set(differences)) - len(numbers) + 1
                ac_patterns.append(ac_value)
            
            predictor = {
                'odd_even_patterns': odd_even_patterns,
                'size_patterns': size_patterns,
                'consecutive_patterns': consecutive_patterns,
                'sum_patterns': sum_patterns,
                'span_patterns': span_patterns,
                'ac_patterns': ac_patterns,
                'avg_odd_count': np.mean(odd_even_patterns),
                'avg_small_count': np.mean(size_patterns),
                'avg_consecutive': np.mean(consecutive_patterns),
                'avg_sum': np.mean(sum_patterns),
                'avg_span': np.mean(span_patterns),
                'avg_ac': np.mean(ac_patterns),
                'std_odd': np.std(odd_even_patterns),
                'std_small': np.std(size_patterns),
                'std_consecutive': np.std(consecutive_patterns),
                'std_sum': np.std(sum_patterns),
                'std_span': np.std(span_patterns),
                'std_ac': np.std(ac_patterns)
            }
            
            logger.info("增強版模式預測器初始化完成")
            return predictor
            
        except Exception as e:
            logger.error(f"增強版模式預測器初始化失敗: {e}")
            return None
    
    def _init_trend_analysis_predictor(self, df):
        """初始化趨勢分析預測器"""
        logger.info("初始化趨勢分析預測器...")
        
        try:
            main_cols = [f'Anumber{i}' for i in range(1, self.main_count + 1)]
            
            # 短期趨勢分析（最近10期）
            short_term_trends = defaultdict(list)
            medium_term_trends = defaultdict(list)
            long_term_trends = defaultdict(list)
            
            # 分析每個號碼的出現趨勢
            for num in range(1, self.main_numbers + 1):
                appearances = []
                for idx, row in df.iterrows():
                    current_numbers = [row[col] for col in main_cols]
                    appearances.append(1 if num in current_numbers else 0)
                
                # 短期趨勢（最近10期）
                if len(appearances) >= 10:
                    short_term = appearances[-10:]
                    short_trend = np.polyfit(range(len(short_term)), short_term, 1)[0]
                    short_term_trends[num] = short_trend
                
                # 中期趨勢（最近20期）
                if len(appearances) >= 20:
                    medium_term = appearances[-20:]
                    medium_trend = np.polyfit(range(len(medium_term)), medium_term, 1)[0]
                    medium_term_trends[num] = medium_trend
                
                # 長期趨勢（最近50期）
                if len(appearances) >= 50:
                    long_term = appearances[-50:]
                    long_trend = np.polyfit(range(len(long_term)), long_term, 1)[0]
                    long_term_trends[num] = long_trend
            
            # 週期性分析
            cyclic_patterns = defaultdict(dict)
            for num in range(1, self.main_numbers + 1):
                appearances = []
                for idx, row in df.iterrows():
                    current_numbers = [row[col] for col in main_cols]
                    appearances.append(1 if num in current_numbers else 0)
                
                # 檢測不同週期的模式
                for period in [3, 5, 7, 10]:
                    if len(appearances) >= period * 3:
                        cyclic_strength = 0
                        for i in range(period, len(appearances)):
                            if appearances[i] == appearances[i - period]:
                                cyclic_strength += 1
                        cyclic_patterns[num][period] = cyclic_strength / (len(appearances) - period)
            
            predictor = {
                'short_term_trends': dict(short_term_trends),
                'medium_term_trends': dict(medium_term_trends),
                'long_term_trends': dict(long_term_trends),
                'cyclic_patterns': dict(cyclic_patterns)
            }
            
            logger.info("趨勢分析預測器初始化完成")
            return predictor
            
        except Exception as e:
            logger.error(f"趨勢分析預測器初始化失敗: {e}")
            return None
    
    def _init_neural_network_predictor(self, df):
        """初始化神經網絡預測器"""
        logger.info("初始化神經網絡預測器...")
        
        try:
            # 準備神經網絡訓練數據
            X, y = self._prepare_enhanced_ml_data(df)
            
            if len(X) < 30:
                logger.warning("數據不足，跳過神經網絡預測器初始化")
                return None
            
            # 創建多層感知機
            mlp = MLPClassifier(
                hidden_layer_sizes=(128, 64, 32),
                activation='relu',
                solver='adam',
                alpha=0.001,
                batch_size='auto',
                learning_rate='adaptive',
                learning_rate_init=0.01,
                max_iter=500,
                random_state=42,
                early_stopping=True,
                validation_fraction=0.1
            )
            
            # 標準化特徵
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            # 由於是多標籤問題，我們需要為每個位置訓練一個模型
            models = []
            for label_idx in range(y.shape[1]):
                model = MLPClassifier(
                    hidden_layer_sizes=(64, 32),
                    activation='relu',
                    solver='adam',
                    alpha=0.001,
                    max_iter=300,
                    random_state=42
                )
                model.fit(X_scaled, y[:, label_idx])
                models.append(model)
            
            predictor = {
                'models': models,
                'scaler': scaler,
                'feature_count': X.shape[1]
            }
            
            logger.info("神經網絡預測器初始化完成")
            return predictor
            
        except Exception as e:
            logger.error(f"神經網絡預測器初始化失敗: {e}")
            return None
    
    def initialize_all_predictors(self, df):
        """初始化所有預測器"""
        logger.info("開始初始化所有增強版預測器...")
        
        try:
            # 初始化增強版統計機器學習預測器
            self.predictors['statistical_ml'] = self._init_enhanced_statistical_ml_predictor(df)
            
            # 初始化強化版頻率預測器
            self.predictors['frequency_based'] = self._init_robust_frequency_predictor(df)
            
            # 初始化增強版模式預測器
            self.predictors['pattern_based'] = self._init_enhanced_pattern_predictor(df)
            
            # 初始化趨勢分析預測器
            self.predictors['trend_analysis'] = self._init_trend_analysis_predictor(df)
            
            # 初始化神經網絡預測器
            self.predictors['neural_network'] = self._init_neural_network_predictor(df)
            
            logger.info("所有增強版預測器初始化完成")
            
        except Exception as e:
            logger.error(f"預測器初始化過程中發生錯誤: {e}")
    
    def predict_with_enhanced_frequency(self, df):
        """使用增強版頻率預測器進行預測"""
        if self.predictors['frequency_based'] is None:
            return None
        
        try:
            predictor = self.predictors['frequency_based']
            
            # 綜合多種頻率指標
            combined_scores = {}
            
            for num in range(1, self.main_numbers + 1):
                # 基礎分數
                base_score = predictor['number_frequency'].get(num, 0)
                weighted_score = predictor['weighted_frequency'].get(num, 0)
                exp_score = predictor['exponential_frequency'].get(num, 0)
                
                # 冷熱號調整
                hot_bonus = 1.2 if num in predictor['hot_numbers'] else 1.0
                cold_penalty = 0.8 if num in predictor['cold_numbers'] else 1.0
                
                # 間隔調整
                avg_gap = predictor['avg_gaps'].get(num, predictor['total_periods'])
                gap_factor = 1.0 / (1.0 + avg_gap / predictor['total_periods'])
                
                # 綜合評分
                combined_scores[num] = (
                    base_score * 0.3 + 
                    weighted_score * 0.4 + 
                    exp_score * 0.3
                ) * hot_bonus * cold_penalty * gap_factor
            
            # 選擇最高分的號碼
            sorted_numbers = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
            predicted_numbers = [num for num, score in sorted_numbers[:self.main_count]]
            
            # 計算信心度
            top_scores = [score for num, score in sorted_numbers[:self.main_count]]
            max_possible_score = max(combined_scores.values()) if combined_scores else 1
            confidence = (np.mean(top_scores) / max_possible_score) * 100
            
            return {
                'main_numbers': sorted(predicted_numbers),
                'confidence': min(95, max(50, confidence)),
                'method': 'enhanced_frequency',
                'special_number': np.random.randint(1, self.special_numbers + 1) if self.has_special else None
            }
            
        except Exception as e:
            logger.warning(f"增強版頻率預測失敗: {e}")
            return None
    
    def predict_with_enhanced_pattern(self, df):
        """使用增強版模式預測器進行預測"""
        if self.predictors['pattern_based'] is None:
            return None
        
        try:
            predictor = self.predictors['pattern_based']
            
            # 基於歷史模式生成預測
            target_odd_count = max(1, min(self.main_count-1, 
                round(predictor['avg_odd_count'] + np.random.normal(0, predictor['std_odd']))))
            target_small_count = max(1, min(self.main_count-1, 
                round(predictor['avg_small_count'] + np.random.normal(0, predictor['std_small']))))
            target_sum = max(self.main_count * 2, min(self.main_numbers * self.main_count,
                round(predictor['avg_sum'] + np.random.normal(0, predictor['std_sum']))))
            
            # 生成符合模式的號碼組合
            predicted_numbers = []
            
            # 奇偶數分配
            odd_numbers = [num for num in range(1, self.main_numbers + 1) if num % 2 == 1]
            even_numbers = [num for num in range(1, self.main_numbers + 1) if num % 2 == 0]
            
            # 根據目標選擇奇數
            selected_odds = np.random.choice(
                odd_numbers, 
                size=min(target_odd_count, len(odd_numbers)), 
                replace=False
            )
            predicted_numbers.extend(selected_odds)
            
            # 補充偶數
            remaining_count = self.main_count - len(predicted_numbers)
            if remaining_count > 0:
                available_evens = [num for num in even_numbers if num not in predicted_numbers]
                selected_evens = np.random.choice(
                    available_evens, 
                    size=min(remaining_count, len(available_evens)), 
                    replace=False
                )
                predicted_numbers.extend(selected_evens)
            
            # 調整以符合和值目標
            current_sum = sum(predicted_numbers)
            while len(predicted_numbers) < self.main_count:
                available_nums = [num for num in range(1, self.main_numbers + 1) 
                                if num not in predicted_numbers]
                if not available_nums:
                    break
                
                # 選擇能讓和值接近目標的號碼
                target_remaining = target_sum - current_sum
                avg_needed = target_remaining / (self.main_count - len(predicted_numbers))
                
                best_num = min(available_nums, key=lambda x: abs(x - avg_needed))
                predicted_numbers.append(best_num)
                current_sum += best_num
            
            # 確保數量正確
            while len(predicted_numbers) < self.main_count:
                for num in range(1, self.main_numbers + 1):
                    if num not in predicted_numbers:
                        predicted_numbers.append(num)
                        break
            
            # 計算信心度（基於模式匹配度）
            actual_odd_count = sum(1 for num in predicted_numbers if num % 2 == 1)
            actual_sum = sum(predicted_numbers)
            
            odd_match = 1 - abs(actual_odd_count - predictor['avg_odd_count']) / self.main_count
            sum_match = 1 - abs(actual_sum - predictor['avg_sum']) / predictor['avg_sum']
            
            confidence = (odd_match + sum_match) / 2 * 100
            
            return {
                'main_numbers': sorted(predicted_numbers[:self.main_count]),
                'confidence': min(85, max(55, confidence)),
                'method': 'enhanced_pattern',
                'special_number': np.random.randint(1, self.special_numbers + 1) if self.has_special else None
            }
            
        except Exception as e:
            logger.warning(f"增強版模式預測失敗: {e}")
            return None
    
    def predict_with_trend_analysis(self, df):
        """使用趨勢分析進行預測"""
        if self.predictors['trend_analysis'] is None:
            return None
        
        try:
            predictor = self.predictors['trend_analysis']
            
            # 綜合趨勢分數
            trend_scores = {}
            
            for num in range(1, self.main_numbers + 1):
                # 短期趨勢權重最高
                short_trend = predictor['short_term_trends'].get(num, 0)
                medium_trend = predictor['medium_term_trends'].get(num, 0)
                long_trend = predictor['long_term_trends'].get(num, 0)
                
                # 綜合趨勢分數
                trend_score = short_trend * 0.5 + medium_trend * 0.3 + long_trend * 0.2
                
                # 週期性調整
                cyclic_bonus = 0
                if num in predictor['cyclic_patterns']:
                    cyclic_strengths = predictor['cyclic_patterns'][num]
                    cyclic_bonus = max(cyclic_strengths.values()) if cyclic_strengths else 0
                
                trend_scores[num] = trend_score + cyclic_bonus * 0.1
            
            # 選擇趨勢分數最高的號碼
            sorted_trends = sorted(trend_scores.items(), key=lambda x: x[1], reverse=True)
            predicted_numbers = [num for num, score in sorted_trends[:self.main_count]]
            
            # 計算信心度
            top_scores = [score for num, score in sorted_trends[:self.main_count]]
            max_score = max(trend_scores.values()) if trend_scores else 1
            min_score = min(trend_scores.values()) if trend_scores else 0
            
            if max_score > min_score:
                confidence = ((np.mean(top_scores) - min_score) / (max_score - min_score)) * 100
            else:
                confidence = 60
            
            return {
                'main_numbers': sorted(predicted_numbers),
                'confidence': min(90, max(55, confidence)),
                'method': 'trend_analysis',
                'special_number': np.random.randint(1, self.special_numbers + 1) if self.has_special else None
            }
            
        except Exception as e:
            logger.warning(f"趨勢分析預測失敗: {e}")
            return None
    
    def predict_with_neural_network(self, df):
        """使用神經網絡進行預測"""
        if self.predictors['neural_network'] is None:
            return None
        
        try:
            predictor = self.predictors['neural_network']
            
            # 準備預測特徵
            X, _ = self._prepare_enhanced_ml_data(df)
            if len(X) == 0:
                return None
            
            # 使用最後一個樣本的特徵進行預測
            last_features = X[-1].reshape(1, -1)
            features_scaled = predictor['scaler'].transform(last_features)
            
            # 獲取每個號碼的預測概率
            number_probabilities = []
            for i, model in enumerate(predictor['models']):
                prob = model.predict_proba(features_scaled)[0]
                if len(prob) > 1:  # 有正類概率
                    number_probabilities.append(prob[1])
                else:
                    number_probabilities.append(0.5)
            
            # 選擇概率最高的號碼
            number_scores = [(i+1, prob) for i, prob in enumerate(number_probabilities)]
            sorted_scores = sorted(number_scores, key=lambda x: x[1], reverse=True)
            
            predicted_numbers = [num for num, prob in sorted_scores[:self.main_count]]
            top_probs = [prob for num, prob in sorted_scores[:self.main_count]]
            
            # 計算信心度
            confidence = np.mean(top_probs) * 100
            
            return {
                'main_numbers': sorted(predicted_numbers),
                'confidence': min(88, max(50, confidence)),
                'method': 'neural_network',
                'special_number': np.random.randint(1, self.special_numbers + 1) if self.has_special else None
            }
            
        except Exception as e:
            logger.warning(f"神經網絡預測失敗: {e}")
            return None
    
    def enhanced_ensemble_predict(self, df, ensemble_size=15):
        """增強版集成預測方法"""
        logger.info(f"開始增強版集成預測 - 集成大小: {ensemble_size}")
        
        # 初始化所有預測器
        self.initialize_all_predictors(df)
        
        # 收集所有預測結果
        all_predictions = []
        
        # 調用各種預測方法
        prediction_methods = [
            ('optimized_board_path', self.predict_with_optimized_board_path),
            ('feature_enhanced', self.predict_with_feature_enhanced),
            ('enhanced_frequency', self.predict_with_enhanced_frequency),
            ('enhanced_pattern', self.predict_with_enhanced_pattern),
            ('trend_analysis', self.predict_with_trend_analysis),
            ('neural_network', self.predict_with_neural_network)
        ]
        
        for method_name, method_func in prediction_methods:
            try:
                prediction = method_func(df)
                if prediction:
                    # 使用動態權重
                    base_weight = self.algorithm_weights.get(method_name, 0.1)
                    stability_factor = self.algorithm_stability.get(method_name, 0.5)
                    prediction['weight'] = base_weight * (0.5 + stability_factor)
                    all_predictions.append(prediction)
                    logger.info(f"{method_name} 預測: {prediction['main_numbers']} (信心度: {prediction['confidence']:.1f}%)")
            except Exception as e:
                logger.warning(f"{method_name} 預測失敗: {e}")
        
        if not all_predictions:
            logger.error("所有預測方法都失敗")
            return None
        
        # 智能投票機制
        final_result = self._intelligent_voting(all_predictions, df)
        
        # 更新預測歷史
        self.prediction_history.append({
            'timestamp': datetime.now(),
            'prediction': final_result,
            'individual_predictions': all_predictions
        })
        
        return final_result
    
    def _intelligent_voting(self, predictions, df):
        """智能投票機制"""
        number_votes = defaultdict(float)
        confidence_sum = 0
        total_weight = 0
        
        # 加權投票
        for prediction in predictions:
            weight = prediction['weight']
            confidence = prediction['confidence'] / 100
            method_stability = self.algorithm_stability.get(prediction['method'], 0.5)
            
            # 綜合權重計算
            final_weight = weight * confidence * (0.5 + method_stability)
            total_weight += final_weight
            confidence_sum += prediction['confidence'] * final_weight
            
            for number in prediction['main_numbers']:
                number_votes[number] += final_weight
        
        # 多樣性獎勵
        diversity_bonus = self._calculate_enhanced_diversity_bonus(predictions)
        
        # 選擇得票最高的號碼
        sorted_votes = sorted(number_votes.items(), key=lambda x: x[1], reverse=True)
        
        # 智能號碼選擇（考慮組合合理性）
        final_numbers = self._select_balanced_combination(sorted_votes, df)
        
        # 計算綜合信心度
        weighted_confidence = confidence_sum / total_weight if total_weight > 0 else 50
        final_confidence = min(95, weighted_confidence + diversity_bonus)
        
        # 特別號預測（使用投票）
        special_number = self._predict_special_number(predictions)
        
        result = {
            'method': 'enhanced_multi_algorithm_ensemble',
            'version': 'v2.0',
            'main_numbers': final_numbers,
            'confidence': final_confidence,
            'special_number': special_number,
            'individual_predictions': predictions,
            'algorithm_weights': self.algorithm_weights,
            'voting_summary': dict(sorted_votes),
            'diversity_bonus': diversity_bonus,
            'explanation': f'增強版集成{len(predictions)}種算法的預測結果'
        }
        
        logger.info(f"增強版集成預測完成 - 最終預測: {result['main_numbers']} (信心度: {result['confidence']:.1f}%)")
        
        return result
    
    def _select_balanced_combination(self, sorted_votes, df):
        """選擇平衡的號碼組合"""
        # 基本選擇：得票最高的號碼
        basic_selection = [num for num, votes in sorted_votes[:self.main_count]]
        
        # 檢查組合的合理性
        odd_count = sum(1 for num in basic_selection if num % 2 == 1)
        mid_point = self.main_numbers // 2
        small_count = sum(1 for num in basic_selection if num <= mid_point)
        total_sum = sum(basic_selection)
        span = max(basic_selection) - min(basic_selection)
        
        # 如果組合不合理，進行調整
        candidate_pool = [num for num, votes in sorted_votes[:self.main_count * 2]]
        
        # 調整奇偶比例（目標：2-4個奇數）
        if odd_count < 2 or odd_count > 4:
            basic_selection = self._adjust_odd_even_balance(basic_selection, candidate_pool)
        
        # 調整大小比例（目標：2-4個小號）
        odd_count = sum(1 for num in basic_selection if num % 2 == 1)
        small_count = sum(1 for num in basic_selection if num <= mid_point)
        if small_count < 2 or small_count > 4:
            basic_selection = self._adjust_size_balance(basic_selection, candidate_pool, mid_point)
        
        return sorted(basic_selection[:self.main_count])
    
    def _adjust_odd_even_balance(self, selection, candidate_pool):
        """調整奇偶平衡"""
        odd_count = sum(1 for num in selection if num % 2 == 1)
        target_odd = 3  # 目標奇數個數
        
        if odd_count < target_odd:
            # 需要更多奇數
            even_nums_in_selection = [num for num in selection if num % 2 == 0]
            odd_candidates = [num for num in candidate_pool if num % 2 == 1 and num not in selection]
            
            # 替換偶數為奇數
            for i in range(min(len(even_nums_in_selection), target_odd - odd_count)):
                if odd_candidates:
                    selection.remove(even_nums_in_selection[i])
                    selection.append(odd_candidates[i])
        
        elif odd_count > target_odd:
            # 需要更多偶數
            odd_nums_in_selection = [num for num in selection if num % 2 == 1]
            even_candidates = [num for num in candidate_pool if num % 2 == 0 and num not in selection]
            
            # 替換奇數為偶數
            for i in range(min(len(odd_nums_in_selection), odd_count - target_odd)):
                if even_candidates:
                    selection.remove(odd_nums_in_selection[i])
                    selection.append(even_candidates[i])
        
        return selection
    
    def _adjust_size_balance(self, selection, candidate_pool, mid_point):
        """調整大小平衡"""
        small_count = sum(1 for num in selection if num <= mid_point)
        target_small = 3  # 目標小號個數
        
        if small_count < target_small:
            # 需要更多小號
            large_nums_in_selection = [num for num in selection if num > mid_point]
            small_candidates = [num for num in candidate_pool if num <= mid_point and num not in selection]
            
            for i in range(min(len(large_nums_in_selection), target_small - small_count)):
                if small_candidates:
                    selection.remove(large_nums_in_selection[i])
                    selection.append(small_candidates[i])
        
        elif small_count > target_small:
            # 需要更多大號
            small_nums_in_selection = [num for num in selection if num <= mid_point]
            large_candidates = [num for num in candidate_pool if num > mid_point and num not in selection]
            
            for i in range(min(len(small_nums_in_selection), small_count - target_small)):
                if large_candidates:
                    selection.remove(small_nums_in_selection[i])
                    selection.append(large_candidates[i])
        
        return selection
    
    def _calculate_enhanced_diversity_bonus(self, predictions):
        """計算增強版多樣性獎勵"""
        if len(predictions) < 2:
            return 0
        
        # 方法多樣性
        methods = set(pred['method'] for pred in predictions)
        method_diversity = len(methods) / len(predictions)
        
        # 號碼多樣性
        all_numbers = set()
        for prediction in predictions:
            all_numbers.update(prediction['main_numbers'])
        number_diversity = len(all_numbers) / (len(predictions) * self.main_count)
        
        # 信心度分布多樣性
        confidences = [pred['confidence'] for pred in predictions]
        confidence_std = np.std(confidences) / 100
        
        # 綜合多樣性獎勵
        diversity_bonus = (method_diversity * 0.4 + number_diversity * 0.4 + confidence_std * 0.2) * 8
        
        return diversity_bonus
    
    def _predict_special_number(self, predictions):
        """預測特別號"""
        if not self.has_special:
            return None
        
        special_votes = defaultdict(float)
        total_weight = 0
        
        for prediction in predictions:
            if prediction.get('special_number'):
                weight = prediction['weight']
                special_votes[prediction['special_number']] += weight
                total_weight += weight
        
        if special_votes:
            # 選擇得票最高的特別號
            best_special = max(special_votes.items(), key=lambda x: x[1])[0]
            return best_special
        else:
            # 隨機選擇
            return np.random.randint(1, self.special_numbers + 1)
    
    def predict_with_optimized_board_path(self, df):
        """使用優化板路分析進行預測"""
        try:
            result = self.predictors['optimized_board_path'].predict_next_numbers_optimized(df)
            if result and 'main_numbers' in result:
                return {
                    'main_numbers': result['main_numbers'],
                    'confidence': result.get('confidence', 50),
                    'method': 'optimized_board_path',
                    'special_number': result.get('special_number', 1) if self.has_special else None
                }
        except Exception as e:
            logger.warning(f"優化板路分析預測失敗: {e}")
        
        return None
    
    def predict_with_feature_enhanced(self, df):
        """使用特徵增強預測器進行預測"""
        try:
            result = self.predictors['feature_enhanced'].predict_with_enhanced_features(df)
            if result and 'main_numbers' in result:
                return {
                    'main_numbers': result['main_numbers'],
                    'confidence': result.get('confidence', 50),
                    'method': 'feature_enhanced',
                    'special_number': result.get('special_number', 1) if self.has_special else None
                }
        except Exception as e:
            logger.warning(f"特徵增強預測失敗: {e}")
        
        return None
    
    def update_algorithm_performance(self, actual_result, prediction_result):
        """更新算法性能"""
        if not actual_result or not prediction_result:
            return
        
        logger.info("更新算法性能指標...")
        
        individual_predictions = prediction_result.get('individual_predictions', [])
        
        for prediction in individual_predictions:
            method = prediction['method']
            predicted_numbers = set(prediction['main_numbers'])
            actual_numbers = set(actual_result)
            
            # 計算匹配數和準確率
            matches = len(predicted_numbers.intersection(actual_numbers))
            accuracy = matches / len(actual_numbers)
            
            # 更新性能歷史
            self.performance_history[method].append(accuracy)
            
            # 保持歷史記錄在合理範圍內
            if len(self.performance_history[method]) > 100:
                self.performance_history[method] = self.performance_history[method][-100:]
            
            # 更新算法穩定性
            if len(self.performance_history[method]) >= 5:
                recent_performance = self.performance_history[method][-5:]
                stability = 1.0 - np.std(recent_performance)
                self.algorithm_stability[method] = max(0.1, min(1.0, stability))
        
        # 動態調整權重
        self._update_algorithm_weights()
        
        logger.info(f"算法性能更新完成")
    
    def _update_algorithm_weights(self):
        """動態更新算法權重"""
        total_performance = 0
        performance_scores = {}
        
        for method in self.algorithm_weights.keys():
            if self.performance_history[method]:
                # 使用近期表現和穩定性計算權重
                recent_performance = np.mean(self.performance_history[method][-10:])
                stability = self.algorithm_stability.get(method, 0.5)
                
                # 綜合評分
                score = recent_performance * 0.7 + stability * 0.3
                performance_scores[method] = score
                total_performance += score
        
        # 更新權重
        if total_performance > 0:
            for method in self.algorithm_weights.keys():
                if method in performance_scores:
                    new_weight = performance_scores[method] / total_performance
                    # 平滑權重更新，避免劇烈變化
                    old_weight = self.algorithm_weights[method]
                    self.algorithm_weights[method] = old_weight * 0.8 + new_weight * 0.2
        
        logger.info(f"權重更新完成: {self.algorithm_weights}")
    
    def generate_enhanced_report(self, df, result):
        """生成增強版預測報告"""
        logger.info("生成增強版預測報告...")
        
        report = []
        report.append("=" * 90)
        report.append("🚀 增強版多算法集成預測系統 - 綜合報告 v2.0")
        report.append("=" * 90)
        
        report.append(f"📊 數據概況:")
        report.append(f"  • 歷史期數: {len(df)}")
        report.append(f"  • 彩票類型: {self.lottery_type}")
        report.append(f"  • 預測時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"  • 系統版本: Enhanced v2.0")
        
        report.append(f"\n🎯 最終預測結果:")
        report.append(f"  • 主要號碼: {result.get('main_numbers', 'N/A')}")
        if result.get('special_number'):
            report.append(f"  • 特別號碼: {result.get('special_number', 'N/A')}")
        report.append(f"  • 綜合信心度: {result.get('confidence', 0):.1f}%")
        report.append(f"  • 多樣性獎勵: +{result.get('diversity_bonus', 0):.1f}%")
        report.append(f"  • 預測方法: {result.get('method', 'N/A')}")
        
        report.append(f"\n🔄 各算法預測結果:")
        individual_predictions = result.get('individual_predictions', [])
        for i, pred in enumerate(individual_predictions, 1):
            method = pred.get('method', 'Unknown')
            numbers = pred.get('main_numbers', [])
            confidence = pred.get('confidence', 0)
            weight = pred.get('weight', 0)
            stability = self.algorithm_stability.get(method, 0.5)
            report.append(f"  {i}. {method}: {numbers}")
            report.append(f"     信心度: {confidence:.1f}% | 權重: {weight:.3f} | 穩定性: {stability:.3f}")
        
        report.append(f"\n⚖️ 算法權重分配:")
        for method, weight in self.algorithm_weights.items():
            stability = self.algorithm_stability.get(method, 0.5)
            performance = "N/A"
            if self.performance_history[method]:
                performance = f"{np.mean(self.performance_history[method][-10:]):.3f}"
            report.append(f"  • {method}: {weight:.3f} (性能: {performance}, 穩定性: {stability:.3f})")
        
        report.append(f"\n📈 投票統計:")
        voting_summary = result.get('voting_summary', {})
        sorted_votes = sorted(voting_summary.items(), key=lambda x: x[1], reverse=True)
        for i, (number, votes) in enumerate(sorted_votes[:12], 1):
            report.append(f"  {i:2d}. 號碼 {number:2d}: {votes:.3f} 票")
        
        report.append(f"\n🎲 增強版預測策略:")
        report.append(f"  • 參與算法數: {len(individual_predictions)}")
        report.append(f"  • 投票機制: 智能加權投票")
        report.append(f"  • 信心度計算: 綜合加權平均 + 多樣性獎勵")
        report.append(f"  • 權重調整: 動態性能反饋調整")
        report.append(f"  • 組合優化: 平衡奇偶、大小、和值分布")
        
        if self.performance_history:
            report.append(f"\n📊 算法歷史表現:")
            for method, history in self.performance_history.items():
                if history:
                    recent_avg = np.mean(history[-10:])
                    overall_avg = np.mean(history)
                    report.append(f"  • {method}: 近期 {recent_avg:.3f} | 總體 {overall_avg:.3f}")
        
        report.append(f"\n💡 預測建議:")
        confidence = result.get('confidence', 0)
        if confidence >= 85:
            report.append(f"  • 高信心度預測，建議重點關注")
        elif confidence >= 70:
            report.append(f"  • 中等信心度預測，可適度關注")
        else:
            report.append(f"  • 較低信心度預測，建議謹慎參考")
        
        report.append("\n" + "=" * 90)
        
        return "\n".join(report)