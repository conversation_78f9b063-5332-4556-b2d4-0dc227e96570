#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最佳預測選擇器

用於從多個預測候選中選擇最可靠的單一預測，解決預測選項過多的問題。
基於歷史成功率和預測一致性進行智能選擇。
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import logging
from collections import Counter

from .success_rate_calculator import SuccessRateCalculator
from .prediction_result import PredictionResult

@dataclass
class OptimalPrediction:
    """最佳預測結果"""
    numbers: List[int]
    special_number: Optional[int]
    method: str
    success_rate_info: Dict
    selection_score: float
    selection_reason: str
    confidence_level: str
    historical_validation: Dict

class OptimalPredictionSelector:
    """最佳預測選擇器
    
    負責從多個預測候選中選擇最可靠的單一預測，
    基於歷史成功率、預測一致性和方法可靠性進行評估。
    """
    
    def __init__(self, success_rate_calculator: SuccessRateCalculator):
        self.success_calculator = success_rate_calculator
        self.logger = logging.getLogger(__name__)
        
        # 方法權重配置
        self.method_weights = {
            'ml': 1.0,
            'board_path': 0.9,
            'ensemble': 1.1,
            'integrated': 1.2
        }
        
        # 成功率權重配置
        self.success_rate_weights = {
            '3_or_more': 0.4,
            '4_or_more': 0.3,
            '5_or_more': 0.2,
            '6_or_more': 0.1
        }
    
    def select_optimal_prediction(self, prediction_results: List[PredictionResult], 
                                lottery_type: str, 
                                max_candidates: int = 1) -> Dict:
        """選擇最佳預測
        
        Args:
            prediction_results: 預測結果列表
            lottery_type: 彩票類型
            max_candidates: 最大候選數量（1=單一預測，2=主推+備選）
            
        Returns:
            Dict: 最佳預測結果
        """
        if not prediction_results:
            raise ValueError("沒有可用的預測結果")
        
        self.logger.info(f"開始選擇最佳預測，候選數量: {len(prediction_results)}")
        
        # 評估所有候選預測
        evaluated_predictions = []
        
        for result in prediction_results:
            try:
                evaluation = self._evaluate_prediction_result(result, lottery_type)
                if evaluation:
                    evaluated_predictions.append(evaluation)
            except Exception as e:
                self.logger.warning(f"評估預測結果失敗: {e}")
                continue
        
        if not evaluated_predictions:
            # 如果沒有歷史數據，使用備用選擇策略
            return self._fallback_selection(prediction_results, lottery_type)
        
        # 按評分排序
        evaluated_predictions.sort(key=lambda x: x['total_score'], reverse=True)
        
        if max_candidates == 1:
            return self._create_single_prediction_result(evaluated_predictions[0], lottery_type)
        else:
            return self._create_multiple_prediction_result(evaluated_predictions[:max_candidates], lottery_type)
    
    def _evaluate_prediction_result(self, result: PredictionResult, lottery_type: str) -> Optional[Dict]:
        """評估單個預測結果
        
        Args:
            result: 預測結果
            lottery_type: 彩票類型
            
        Returns:
            Dict: 評估結果，包含評分和詳細信息
        """
        try:
            # 獲取方法的歷史成功率
            success_stats = self.success_calculator.calculate_method_success_rate(
                result.method, lottery_type
            )
            
            if success_stats.get('status') != 'success':
                return None
            
            # 計算基礎評分（基於歷史成功率）
            base_score = self._calculate_base_score(success_stats)
            
            # 計算一致性評分（基於候選預測的相似性）
            consistency_score = self._calculate_consistency_score(result)
            
            # 計算方法可靠性評分
            method_score = self._calculate_method_score(result.method, success_stats)
            
            # 計算數字分佈評分
            distribution_score = self._calculate_distribution_score(result)
            
            # 計算總評分
            total_score = (
                base_score * 0.4 +
                consistency_score * 0.2 +
                method_score * 0.2 +
                distribution_score * 0.2
            )
            
            return {
                'result': result,
                'success_stats': success_stats,
                'base_score': base_score,
                'consistency_score': consistency_score,
                'method_score': method_score,
                'distribution_score': distribution_score,
                'total_score': total_score,
                'top_candidate': result.get_top_candidate()
            }
            
        except Exception as e:
            self.logger.error(f"評估預測結果時發生錯誤: {e}")
            return None
    
    def _calculate_base_score(self, success_stats: Dict) -> float:
        """計算基於歷史成功率的基礎評分"""
        if 'success_rates' not in success_stats:
            return 0.0
        
        score = 0.0
        for level, weight in self.success_rate_weights.items():
            if level in success_stats['success_rates']:
                rate = success_stats['success_rates'][level]['rate']
                score += rate * weight
        
        return score
    
    def _calculate_consistency_score(self, result: PredictionResult) -> float:
        """計算預測一致性評分
        
        基於多個候選預測之間的相似性
        """
        candidates = result.candidates
        if len(candidates) <= 1:
            return 0.5  # 單一候選，給予中等評分
        
        top_candidate = result.get_top_candidate()
        top_numbers = set(top_candidate['main_numbers'])
        
        similarity_scores = []
        
        # 計算與其他候選的相似性
        for candidate in candidates[1:min(4, len(candidates))]:  # 檢查前3個其他候選
            candidate_numbers = set(candidate['main_numbers'])
            similarity = len(top_numbers & candidate_numbers) / 6
            similarity_scores.append(similarity)
        
        if not similarity_scores:
            return 0.5
        
        # 高一致性表示預測更可靠
        avg_similarity = sum(similarity_scores) / len(similarity_scores)
        
        # 將相似性轉換為評分（0.3-0.8的相似性給予較高評分）
        if 0.3 <= avg_similarity <= 0.8:
            return 0.7 + (avg_similarity - 0.3) * 0.6  # 0.7-1.0
        elif avg_similarity > 0.8:
            return 0.6  # 過高相似性可能表示缺乏多樣性
        else:
            return 0.3 + avg_similarity  # 0.3-0.6
    
    def _calculate_method_score(self, method: str, success_stats: Dict) -> float:
        """計算方法可靠性評分"""
        # 基礎方法權重
        base_weight = self.method_weights.get(method, 0.8)
        
        # 根據預測數量調整（更多數據 = 更可靠）
        total_predictions = success_stats.get('total_predictions', 0)
        data_reliability = min(1.0, total_predictions / 50)  # 50個預測為滿分
        
        # 根據平均匹配數調整
        avg_matches = success_stats.get('average_matches', 0)
        performance_bonus = min(0.2, avg_matches / 6 * 0.2)  # 最多20%獎勵
        
        return (base_weight + performance_bonus) * data_reliability
    
    def _calculate_distribution_score(self, result: PredictionResult) -> float:
        """計算數字分佈評分
        
        評估預測數字的分佈合理性
        """
        top_candidate = result.get_top_candidate()
        numbers = top_candidate['main_numbers']
        
        score = 0.0
        
        # 1. 數字範圍分佈 (0.3權重)
        range_score = self._evaluate_number_range_distribution(numbers)
        score += range_score * 0.3
        
        # 2. 奇偶分佈 (0.2權重)
        odd_even_score = self._evaluate_odd_even_distribution(numbers)
        score += odd_even_score * 0.2
        
        # 3. 大小分佈 (0.2權重)
        size_score = self._evaluate_size_distribution(numbers)
        score += size_score * 0.2
        
        # 4. 連續數字檢查 (0.3權重)
        consecutive_score = self._evaluate_consecutive_numbers(numbers)
        score += consecutive_score * 0.3
        
        return score
    
    def _evaluate_number_range_distribution(self, numbers: List[int]) -> float:
        """評估數字範圍分佈"""
        # 將1-49分為5個區間
        ranges = [(1, 10), (11, 20), (21, 30), (31, 40), (41, 49)]
        range_counts = [0] * 5
        
        for num in numbers:
            for i, (start, end) in enumerate(ranges):
                if start <= num <= end:
                    range_counts[i] += 1
                    break
        
        # 理想分佈是每個區間1-2個數字
        ideal_distribution = [1, 1, 1, 1, 2]  # 總共6個數字
        
        # 計算與理想分佈的差異
        diff = sum(abs(actual - ideal) for actual, ideal in zip(range_counts, ideal_distribution))
        
        # 轉換為評分（差異越小評分越高）
        return max(0.0, 1.0 - diff / 6)
    
    def _evaluate_odd_even_distribution(self, numbers: List[int]) -> float:
        """評估奇偶分佈"""
        odd_count = sum(1 for num in numbers if num % 2 == 1)
        even_count = 6 - odd_count
        
        # 理想比例是3:3或4:2或2:4
        if abs(odd_count - even_count) <= 2:
            return 1.0
        elif abs(odd_count - even_count) <= 4:
            return 0.6
        else:
            return 0.2
    
    def _evaluate_size_distribution(self, numbers: List[int]) -> float:
        """評估大小數分佈"""
        small_count = sum(1 for num in numbers if num <= 25)
        large_count = 6 - small_count
        
        # 理想比例是2:4到4:2之間
        if 2 <= small_count <= 4:
            return 1.0
        elif small_count == 1 or small_count == 5:
            return 0.6
        else:
            return 0.2
    
    def _evaluate_consecutive_numbers(self, numbers: List[int]) -> float:
        """評估連續數字情況"""
        sorted_numbers = sorted(numbers)
        consecutive_count = 0
        max_consecutive = 0
        current_consecutive = 1
        
        for i in range(1, len(sorted_numbers)):
            if sorted_numbers[i] == sorted_numbers[i-1] + 1:
                current_consecutive += 1
            else:
                max_consecutive = max(max_consecutive, current_consecutive)
                current_consecutive = 1
        
        max_consecutive = max(max_consecutive, current_consecutive)
        
        # 評分標準：2-3個連續數字較好，過多或過少都不理想
        if max_consecutive == 2 or max_consecutive == 3:
            return 1.0
        elif max_consecutive == 1 or max_consecutive == 4:
            return 0.7
        elif max_consecutive == 5:
            return 0.3
        else:  # 6個連續數字
            return 0.1
    
    def _create_single_prediction_result(self, best_evaluation: Dict, lottery_type: str) -> Dict:
        """創建單一預測結果"""
        result = best_evaluation['result']
        success_stats = best_evaluation['success_stats']
        top_candidate = best_evaluation['top_candidate']
        
        # 生成選擇原因
        selection_reason = self._generate_selection_reason(best_evaluation)
        
        # 確定信心級別
        confidence_level = self._determine_confidence_level(best_evaluation['total_score'])
        
        return {
            'type': 'single_prediction',
            'prediction': {
                'main_numbers': top_candidate['main_numbers'],
                'special_number': top_candidate.get('special_number'),
                'method': result.method,
                'selection_score': best_evaluation['total_score']
            },
            'success_rate_info': {
                'method': result.method,
                'lottery_type': lottery_type,
                'historical_performance': success_stats['success_rates'],
                'total_predictions': success_stats['total_predictions'],
                'average_matches': success_stats['average_matches'],
                'validation_period': f"過去{success_stats.get('period_days', 90)}天"
            },
            'selection_reason': selection_reason,
            'confidence_level': confidence_level,
            'recommendation': "這是基於歷史成功率分析的最佳預測選擇"
        }
    
    def _create_multiple_prediction_result(self, evaluations: List[Dict], lottery_type: str) -> Dict:
        """創建多重預測結果（主推+備選）"""
        primary = evaluations[0]
        backup = evaluations[1] if len(evaluations) > 1 else None
        
        result = {
            'type': 'multiple_prediction',
            'primary': self._create_single_prediction_result(primary, lottery_type)['prediction'],
            'primary_info': {
                'success_rate_info': primary['success_stats']['success_rates'],
                'selection_reason': self._generate_selection_reason(primary),
                'confidence_level': self._determine_confidence_level(primary['total_score'])
            }
        }
        
        if backup:
            result['backup'] = {
                'main_numbers': backup['top_candidate']['main_numbers'],
                'special_number': backup['top_candidate'].get('special_number'),
                'method': backup['result'].method,
                'selection_score': backup['total_score']
            }
            result['backup_info'] = {
                'success_rate_info': backup['success_stats']['success_rates'],
                'selection_reason': self._generate_selection_reason(backup),
                'confidence_level': self._determine_confidence_level(backup['total_score'])
            }
        
        result['recommendation'] = "建議優先考慮主推預測，備選預測僅供參考"
        
        return result
    
    def _fallback_selection(self, prediction_results: List[PredictionResult], lottery_type: str) -> Dict:
        """備用選擇策略（當沒有足夠歷史數據時）"""
        self.logger.warning("使用備用選擇策略：歷史數據不足")
        
        # 優先選擇ensemble或integrated方法
        preferred_methods = ['ensemble', 'integrated', 'ml', 'board_path']
        
        selected_result = None
        for method in preferred_methods:
            for result in prediction_results:
                if result.method == method:
                    selected_result = result
                    break
            if selected_result:
                break
        
        if not selected_result:
            selected_result = prediction_results[0]
        
        top_candidate = selected_result.get_top_candidate()
        
        return {
            'type': 'fallback_prediction',
            'prediction': {
                'main_numbers': top_candidate['main_numbers'],
                'special_number': top_candidate.get('special_number'),
                'method': selected_result.method,
                'selection_score': 0.5
            },
            'success_rate_info': {
                'status': 'insufficient_data',
                'message': '歷史數據不足，無法提供準確的成功率統計',
                'recommendation': '建議累積更多預測數據後重新評估'
            },
            'selection_reason': f"基於方法優先級選擇 {selected_result.method} 預測",
            'confidence_level': 'low',
            'recommendation': "由於歷史數據不足，請謹慎參考此預測"
        }
    
    def _generate_selection_reason(self, evaluation: Dict) -> str:
        """生成選擇原因說明"""
        result = evaluation['result']
        success_stats = evaluation['success_stats']
        
        method_names = {
            'ml': '機器學習',
            'board_path': '板路分析',
            'ensemble': '集成預測',
            'integrated': '整合預測'
        }
        
        method_name = method_names.get(result.method, result.method)
        success_rate = success_stats['success_rates']['3_or_more']['percentage']
        total_predictions = success_stats['total_predictions']
        
        reasons = [
            f"選擇 {method_name} 方法",
            f"歷史成功率: {success_rate}",
            f"基於 {total_predictions} 個歷史預測"
        ]
        
        # 添加特殊原因
        if evaluation['consistency_score'] > 0.7:
            reasons.append("預測一致性良好")
        
        if evaluation['distribution_score'] > 0.7:
            reasons.append("數字分佈合理")
        
        if evaluation['total_score'] > 0.8:
            reasons.append("綜合評分優秀")
        
        return "；".join(reasons)
    
    def _determine_confidence_level(self, total_score: float) -> str:
        """確定信心級別"""
        if total_score >= 0.8:
            return 'high'
        elif total_score >= 0.6:
            return 'medium'
        else:
            return 'low'


def test_optimal_prediction_selector():
    """測試最佳預測選擇器"""
    print("=== 最佳預測選擇器測試 ===")
    
    # 創建測試用的成功率計算器
    from .success_rate_calculator import SuccessRateCalculator
    calculator = SuccessRateCalculator(":memory:")
    
    # 添加一些測試數據
    for i in range(20):
        record_id = calculator.record_prediction(
            method="ml",
            lottery_type="lotto649",
            predicted_numbers=[1+i, 15+i, 22, 28, 35, 38],
            confidence=0.7 + i * 0.01
        )
        
        # 模擬驗證結果
        match_count = 3 if i % 3 == 0 else (4 if i % 5 == 0 else 2)
        actual_numbers = [1+i, 15+i, 22, 28, 35, 38][:match_count] + [40, 41, 42, 43, 44, 45][match_count:]
        calculator.verify_prediction(record_id, actual_numbers[:6])
    
    # 創建選擇器
    selector = OptimalPredictionSelector(calculator)
    
    # 創建模擬預測結果
    class MockPredictionResult:
        def __init__(self, method, candidates):
            self.method = method
            self.candidates = candidates
        
        def get_top_candidate(self):
            return self.candidates[0]
    
    mock_results = [
        MockPredictionResult("ml", [
            {'main_numbers': [1, 15, 22, 28, 35, 38], 'special_number': 7},
            {'main_numbers': [2, 16, 23, 29, 36, 39], 'special_number': 8}
        ]),
        MockPredictionResult("board_path", [
            {'main_numbers': [3, 17, 24, 30, 37, 40], 'special_number': 9}
        ])
    ]
    
    # 測試單一預測選擇
    print("\n1. 測試單一預測選擇")
    try:
        result = selector.select_optimal_prediction(mock_results, "lotto649", max_candidates=1)
        print(f"選擇結果: {result['prediction']['main_numbers']}")
        print(f"選擇方法: {result['prediction']['method']}")
        print(f"選擇原因: {result['selection_reason']}")
        print(f"信心級別: {result['confidence_level']}")
    except Exception as e:
        print(f"測試失敗: {e}")
    
    print("\n=== 測試完成 ===")

if __name__ == "__main__":
    test_optimal_prediction_selector()