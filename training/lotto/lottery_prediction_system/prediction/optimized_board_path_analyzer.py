"""
優化版板路分析器 - Phase 2 算法參數調優
針對現有板路分析算法的關鍵問題進行優化
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter
import logging
import random
import math
from datetime import datetime
from itertools import combinations
from .board_path_analyzer import BoardPathAnalyzer

logger = logging.getLogger('optimized_board_path_analyzer')

class OptimizedBoardPathAnalyzer(BoardPathAnalyzer):
    """
    優化版板路分析器
    
    主要改進：
    1. 改進選號邏輯，減少隨機性
    2. 優化分數計算和權重分配
    3. 增加候選池大小和多樣性
    4. 加入更多統計特徵
    """
    
    def __init__(self, lottery_type=None, db_manager=None, optimization_level=2):
        """
        初始化優化版板路分析器
        
        Args:
            lottery_type: 彩票類型
            db_manager: 數據庫管理器
            optimization_level: 優化級別 (1-3)
        """
        super().__init__(lottery_type, db_manager)
        self.optimization_level = optimization_level
        
        # 優化參數設置
        self.params = {
            'base_score': 20,        # 提升基礎分數
            'max_bonus': 20,         # 提升最大獎勵分數
            'candidate_multiplier': 4,  # 擴大候選池
            'stability_factor': 0.7,    # 穩定性因子
            'diversity_threshold': 0.8,  # 多樣性閾值
            'min_gap_ratio': 0.6,      # 最小間隙比例
            'max_gap_ratio': 1.5,      # 最大間隙比例
        }
        
        # 根據優化級別調整參數
        if optimization_level >= 2:
            self.params['candidate_multiplier'] = 5
            self.params['stability_factor'] = 0.8
        if optimization_level >= 3:
            self.params['base_score'] = 25
            self.params['max_bonus'] = 25
            self.params['diversity_threshold'] = 0.9
            
        logger.info(f"優化版板路分析器初始化完成 - 優化級別: {optimization_level}")
    
    def calculate_enhanced_cycle_scores(self, df):
        """
        計算增強的週期分數
        
        Args:
            df: 歷史數據DataFrame
            
        Returns:
            dict: 增強的週期分數
        """
        enhanced_scores = {}
        
        for num, data in self.number_cycles.items():
            if isinstance(num, str) and num.startswith('S'):
                continue
                
            if data['avg_gap'] > 0 and len(data['appearances']) >= 3:
                # 計算當前間隙
                current_gap = len(df) - data['appearances'][-1]
                avg_gap = data['avg_gap']
                
                # 計算間隙比例
                gap_ratio = current_gap / avg_gap
                
                # 基於間隙比例計算分數
                if self.params['min_gap_ratio'] <= gap_ratio <= self.params['max_gap_ratio']:
                    # 最佳區間，給予高分
                    score = self.params['base_score'] + self.params['max_bonus'] * (1 - abs(gap_ratio - 1))
                elif gap_ratio > self.params['max_gap_ratio']:
                    # 超過平均間隙，逐漸降低分數
                    score = self.params['base_score'] * (1 / (gap_ratio - self.params['max_gap_ratio'] + 1))
                else:
                    # 未達到最小間隙，給予低分
                    score = self.params['base_score'] * gap_ratio / self.params['min_gap_ratio']
                
                # 加入穩定性調整
                gap_std = np.std(data['gaps']) if len(data['gaps']) > 1 else 0
                stability_bonus = self.params['base_score'] * 0.3 / (1 + gap_std / avg_gap)
                
                enhanced_scores[num] = score + stability_bonus
            else:
                enhanced_scores[num] = self.params['base_score'] * 0.5
                
        return enhanced_scores
    
    def select_numbers_optimized(self, scores, count):
        """
        優化的選號方法
        
        Args:
            scores: 號碼分數字典
            count: 要選擇的號碼數量
            
        Returns:
            list: 選擇的號碼列表
        """
        if not scores:
            return random.sample(range(1, self.main_numbers + 1), count)
        
        # 擴大候選池
        candidate_count = min(len(scores), count * self.params['candidate_multiplier'])
        candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:candidate_count]
        
        # 分組選號策略
        high_score_group = candidates[:count]  # 高分組
        mid_score_group = candidates[count:count*2] if len(candidates) > count else []  # 中分組
        
        selected_numbers = []
        
        # 第一階段：選擇高分組中的號碼
        high_score_ratio = self.params['stability_factor']
        high_score_count = int(count * high_score_ratio)
        
        for i in range(high_score_count):
            if i < len(high_score_group):
                selected_numbers.append(high_score_group[i][0])
        
        # 第二階段：從中分組中選擇剩餘號碼
        remaining_count = count - len(selected_numbers)
        if remaining_count > 0 and mid_score_group:
            # 使用加權隨機選擇
            weights = [score for _, score in mid_score_group]
            if weights:
                selected_indices = np.random.choice(
                    len(mid_score_group), 
                    size=min(remaining_count, len(mid_score_group)), 
                    replace=False,
                    p=np.array(weights) / sum(weights)
                )
                for idx in selected_indices:
                    selected_numbers.append(mid_score_group[idx][0])
        
        # 第三階段：如果還需要號碼，從全部候選中選擇
        while len(selected_numbers) < count:
            for num, score in candidates:
                if num not in selected_numbers:
                    selected_numbers.append(num)
                    break
            if len(selected_numbers) >= count:
                break
        
        # 確保沒有重複並排序
        selected_numbers = sorted(list(set(selected_numbers)))
        
        # 如果數量不足，補充高分候選
        while len(selected_numbers) < count:
            for num, score in candidates:
                if num not in selected_numbers:
                    selected_numbers.append(num)
                    if len(selected_numbers) >= count:
                        break
        
        return sorted(selected_numbers[:count])
    
    def generate_multiple_candidates(self, df, candidate_count=5):
        """
        生成多個候選預測組合
        
        Args:
            df: 歷史數據DataFrame
            candidate_count: 候選組合數量
            
        Returns:
            list: 多個候選預測結果
        """
        if not self.number_cycles:
            self.run_full_analysis(df)
        
        candidates = []
        
        for i in range(candidate_count):
            # 為每個候選使用不同的隨機種子
            np.random.seed(42 + i)
            random.seed(42 + i)
            
            # 獲取增強的週期分數
            cycle_scores = self.calculate_enhanced_cycle_scores(df)
            
            # 添加少量隨機性以產生多樣性
            diversity_factor = 1 + (i * 0.1)  # 每個候選增加10%的多樣性
            
            adjusted_scores = {}
            for num, score in cycle_scores.items():
                # 添加受控的隨機調整
                random_adjustment = np.random.normal(0, score * 0.1)  # 10%的隨機調整
                adjusted_scores[num] = score * diversity_factor + random_adjustment
            
            # 選擇號碼
            selected_main = self.select_numbers_optimized(adjusted_scores, self.main_count)
            
            # 計算信心分數
            confidence = self.calculate_confidence_score(selected_main, adjusted_scores)
            
            candidate = {
                'main_numbers': selected_main,
                'confidence': confidence,
                'method': f'optimized_board_path_v{i+1}',
                'explanation': f'優化板路分析候選{i+1}，信心度：{confidence:.1f}%'
            }
            
            # 如果有特別號
            if self.has_special:
                special_result = self.analyze_special_number(df, selected_main)
                # analyze_special_number 返回 (scores, explanations) 的 tuple
                if isinstance(special_result, tuple):
                    special_scores, special_explanations = special_result
                else:
                    special_scores = special_result
                candidate['special_number'] = self.select_special_number_optimized(special_scores)
            
            candidates.append(candidate)
        
        return candidates
    
    def calculate_confidence_score(self, selected_numbers, scores):
        """
        計算預測信心分數
        
        Args:
            selected_numbers: 選擇的號碼
            scores: 號碼分數字典
            
        Returns:
            float: 信心分數 (0-100)
        """
        if not selected_numbers or not scores:
            return 50.0
        
        # 計算選中號碼的平均分數
        selected_scores = [scores.get(num, 0) for num in selected_numbers]
        avg_score = np.mean(selected_scores)
        
        # 計算所有號碼的平均分數
        all_scores = list(scores.values())
        overall_avg = np.mean(all_scores)
        
        # 計算相對信心度
        relative_confidence = (avg_score / overall_avg) * 50 if overall_avg > 0 else 50
        
        # 限制在合理範圍內
        confidence = max(30, min(85, relative_confidence))
        
        return confidence
    
    def select_special_number_optimized(self, scores):
        """
        優化的特別號選擇
        
        Args:
            scores: 特別號分數字典
            
        Returns:
            int: 選擇的特別號
        """
        if not scores:
            return random.randint(1, self.special_numbers)
        
        # 選擇前3個候選
        candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:3]
        
        # 使用加權隨機選擇
        weights = [score for _, score in candidates]
        if weights:
            selected_idx = np.random.choice(
                len(candidates), 
                p=np.array(weights) / sum(weights)
            )
            return candidates[selected_idx][0]
        
        return candidates[0][0] if candidates else random.randint(1, self.special_numbers)
    
    def predict_next_numbers_optimized(self, df, candidate_count=5):
        """
        優化的預測方法
        
        Args:
            df: 歷史數據DataFrame
            candidate_count: 候選數量
            
        Returns:
            dict: 包含多個候選的預測結果
        """
        logger.info(f"開始優化版板路分析預測 - 候選數量: {candidate_count}")
        
        # 生成多個候選
        candidates = self.generate_multiple_candidates(df, candidate_count)
        
        # 選擇最佳候選作為主要推薦
        best_candidate = max(candidates, key=lambda x: x['confidence'])
        
        result = {
            'method': 'optimized_board_path',
            'version': 'v2.0_optimized',
            'main_numbers': best_candidate['main_numbers'],
            'confidence': best_candidate['confidence'],
            'candidates': candidates,
            'optimization_level': self.optimization_level,
            'explanation': f'優化板路分析，共{len(candidates)}個候選組合，選擇最高信心度組合'
        }
        
        if self.has_special:
            result['special_number'] = best_candidate.get('special_number', 1)
        
        logger.info(f"優化版預測完成 - 主要候選信心度: {best_candidate['confidence']:.1f}%")
        
        return result