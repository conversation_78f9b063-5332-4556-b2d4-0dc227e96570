#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增強彩票預測系統 - 主預測器
整合成功率計算器和最佳預測選擇器，提供基於歷史成功率的預測結果
"""

import os
import sys
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import pandas as pd
import numpy as np

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from .lottery_predictor import LotteryPredictor
from .integrated_predictor import IntegratedPredictor
from .board_path_engine import BoardPathEngine
from .success_rate_calculator import SuccessRateCalculator
from .optimal_prediction_selector import OptimalPredictionSelector
from .adaptive_prediction_strategy import AdaptivePredictionStrategy
from .prediction_result import PredictionResult

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedLotteryPredictor:
    """
    增強彩票預測系統
    
    主要功能：
    1. 整合多種預測方法（ML、板路分析、集成預測）
    2. 基於歷史成功率評估預測方法
    3. 自動選擇最佳預測結果
    4. 提供成功率而非信心指數
    5. 減少預測選項數量，提高準確性
    """
    
    def __init__(self, db_manager, model_dir: str = 'models'):
        """
        初始化增強預測器
        
        Args:
            db_manager: 數據庫管理器
            model_dir: 模型目錄
        """
        self.db_manager = db_manager
        self.model_dir = model_dir
        
        # 初始化各種預測器
        self.ml_predictor = LotteryPredictor(model_dir=model_dir)
        self.integrated_predictor = IntegratedPredictor(db_manager)
        self.board_path_engine = BoardPathEngine()
        
        # 初始化成功率計算器和選擇器
        db_path = getattr(db_manager, 'db_path', 'data/lottery_data.db')
        self.success_rate_calculator = SuccessRateCalculator(db_path)
        self.optimal_selector = OptimalPredictionSelector(db_path)
        self.adaptive_strategy = AdaptivePredictionStrategy(db_path)
        
        # 設置預測器
        self.adaptive_strategy.set_predictors(
            ml_predictor=self.ml_predictor,
            board_path_engine=self.board_path_engine,
            integrated_predictor=self.integrated_predictor
        )
        
        logger.info("增強彩票預測系統初始化完成")
    
    def predict_with_success_rate(self, df: pd.DataFrame, features: np.ndarray, 
                                 lottery_type: str = 'powercolor',
                                 methods: List[str] = None,
                                 period: str = None) -> Dict[str, Any]:
        """
        使用成功率評估進行預測
        
        Args:
            df: 歷史數據
            features: 預測特徵
            lottery_type: 彩票類型
            methods: 預測方法列表，None表示自動選擇
            period: 期號
            
        Returns:
            包含預測結果和成功率的字典
        """
        logger.info(f"開始進行{lottery_type}預測（基於成功率評估）")
        
        try:
            # 使用自適應策略進行預測
            result = self.adaptive_strategy.predict_with_adaptive_strategy(
                df=df,
                lottery_type=lottery_type,
                features=features,
                methods=methods,
                period=period
            )
            
            if not result:
                logger.error("自適應預測策略失敗")
                return self._create_fallback_result(lottery_type)
            
            # 格式化結果
            formatted_result = self._format_prediction_result(result, lottery_type)
            
            logger.info(f"預測完成，最佳方法：{result.get('best_method', 'unknown')}")
            return formatted_result
            
        except Exception as e:
            logger.error(f"預測過程中發生錯誤：{str(e)}")
            return self._create_fallback_result(lottery_type)
    
    def get_method_success_rates(self, lottery_type: str, days: int = 30) -> Dict[str, float]:
        """
        獲取各預測方法的成功率
        
        Args:
            lottery_type: 彩票類型
            days: 統計天數
            
        Returns:
            各方法的成功率字典
        """
        try:
            methods = ['ml', 'board_path', 'integrated', 'ensemble']
            success_rates = {}
            
            for method in methods:
                stats = self.success_rate_calculator.calculate_method_success_rate(
                    method=method,
                    lottery_type=lottery_type,
                    period_days=days
                )
                # 提取成功率（3個號碼以上的成功率）
                if stats.get('status') == 'success':
                    rate = stats['success_rates']['3_or_more']['rate']
                else:
                    rate = 0.0
                success_rates[method] = rate
            
            return success_rates
            
        except Exception as e:
            logger.error(f"獲取成功率時發生錯誤：{str(e)}")
            return {}
    
    def analyze_prediction_performance(self, lottery_type: str, days: int = 30) -> Dict[str, Any]:
        """
        分析預測性能
        
        Args:
            lottery_type: 彩票類型
            days: 分析天數
            
        Returns:
            性能分析結果
        """
        try:
            # 獲取成功率
            success_rates = self.get_method_success_rates(lottery_type, days)
            
            # 獲取方法比較
            comparison = self.success_rate_calculator.get_all_methods_comparison(lottery_type, days)
            
            # 獲取趨勢分析
            trends = {}
            for method in success_rates.keys():
                trend = self.success_rate_calculator.get_recent_trend(
                    method=method,
                    lottery_type=lottery_type,
                    periods=4  # 分析最近4個期間的趨勢
                )
                trends[method] = trend
            
            return {
                'success_rates': success_rates,
                'method_comparison': comparison,
                'trends': trends,
                'analysis_period': f'{days}天',
                'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            logger.error(f"分析預測性能時發生錯誤：{str(e)}")
            return {}
    
    def record_prediction_result(self, prediction_result: Dict[str, Any], 
                               actual_numbers: List[int], special_number: int = None) -> bool:
        """
        記錄預測結果用於後續驗證
        
        Args:
            prediction_result: 預測結果
            actual_numbers: 實際開獎號碼
            special_number: 特別號（如適用）
            
        Returns:
            是否成功記錄
        """
        try:
            # 從預測結果中提取信息
            method = prediction_result.get('prediction_method', 'unknown')
            lottery_type = prediction_result.get('lottery_type', 'powercolor')
            predicted_numbers = self._extract_predicted_numbers(prediction_result)
            
            if not predicted_numbers:
                logger.error("無法從預測結果中提取預測號碼")
                return False
            
            # 記錄預測
            record_id = self.success_rate_calculator.record_prediction(
                method=method,
                lottery_type=lottery_type,
                predicted_numbers=predicted_numbers,
                special_predicted=special_number,
                confidence=prediction_result.get('confidence_score', 0.0)
            )
            
            if record_id:
                # 驗證結果
                success = self.success_rate_calculator.verify_prediction(
                    record_id=record_id,
                    actual_numbers=actual_numbers,
                    special_actual=special_number
                )
                
                logger.info(f"預測結果已記錄並驗證，記錄ID：{record_id}，驗證結果：{success}")
                return True
            else:
                logger.error("記錄預測失敗")
                return False
                
        except Exception as e:
            logger.error(f"記錄預測結果時發生錯誤：{str(e)}")
            return False
    
    def get_best_prediction_method(self, lottery_type: str, days: int = 30) -> str:
        """
        獲取當前最佳預測方法
        
        Args:
            lottery_type: 彩票類型
            days: 評估天數
            
        Returns:
            最佳預測方法名稱
        """
        try:
            success_rates = self.get_method_success_rates(lottery_type, days)
            
            if not success_rates:
                return 'ml'  # 默認方法
            
            # 找到成功率最高的方法
            best_method = max(success_rates.items(), key=lambda x: x[1])[0]
            
            logger.info(f"當前最佳預測方法：{best_method}（成功率：{success_rates[best_method]:.2%}）")
            return best_method
            
        except Exception as e:
            logger.error(f"獲取最佳預測方法時發生錯誤：{str(e)}")
            return 'ml'
    
    def _format_prediction_result(self, result: Dict[str, Any], lottery_type: str) -> Dict[str, Any]:
        """
        格式化預測結果
        
        Args:
            result: 原始預測結果
            lottery_type: 彩票類型
            
        Returns:
            格式化後的結果
        """
        try:
            formatted = {
                'lottery_type': lottery_type,
                'prediction_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'prediction_method': result.get('best_method', 'unknown'),
                'success_rate': result.get('success_rate', 0.0),
                'predicted_numbers': result.get('predicted_numbers', {}),
                'explanation': result.get('explanation', []),
                'performance_report': result.get('performance_report', {})
            }
            
            # 添加彩票類型特定的格式化
            if lottery_type == 'powercolor':
                numbers = result.get('predicted_numbers', {})
                formatted['第一區'] = numbers.get('main_numbers', [])
                formatted['第二區'] = numbers.get('special_number', 0)
            elif lottery_type == 'lotto649':
                numbers = result.get('predicted_numbers', {})
                formatted['一般號碼'] = numbers.get('main_numbers', [])
                formatted['特別號'] = numbers.get('special_number', 0)
            elif lottery_type == 'dailycash':
                numbers = result.get('predicted_numbers', {})
                formatted['號碼'] = numbers.get('numbers', [])
            
            # 移除信心指數相關信息，強調成功率
            if 'confidence_score' in formatted:
                del formatted['confidence_score']
            
            return formatted
            
        except Exception as e:
            logger.error(f"格式化預測結果時發生錯誤：{str(e)}")
            return result
    
    def _extract_predicted_numbers(self, prediction_result: Dict[str, Any]) -> List[int]:
        """
        從預測結果中提取預測號碼
        
        Args:
            prediction_result: 預測結果
            
        Returns:
            預測號碼列表
        """
        try:
            numbers = prediction_result.get('predicted_numbers', {})
            
            if isinstance(numbers, dict):
                main_numbers = numbers.get('main_numbers', [])
                if main_numbers:
                    return main_numbers
            
            # 嘗試其他可能的鍵
            for key in ['第一區', '一般號碼', '號碼', 'numbers']:
                if key in prediction_result and prediction_result[key]:
                    return prediction_result[key]
            
            return []
            
        except Exception as e:
            logger.error(f"提取預測號碼時發生錯誤：{str(e)}")
            return []
    
    def _create_fallback_result(self, lottery_type: str) -> Dict[str, Any]:
        """
        創建備用預測結果
        
        Args:
            lottery_type: 彩票類型
            
        Returns:
            備用預測結果
        """
        logger.warning("使用備用預測結果")
        
        if lottery_type == 'powercolor':
            return {
                'lottery_type': lottery_type,
                'prediction_method': 'fallback',
                'success_rate': 0.0,
                '第一區': [1, 2, 3, 4, 5, 6],
                '第二區': 1,
                'explanation': ['系統備用預測，建議謹慎使用'],
                'prediction_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        elif lottery_type == 'lotto649':
            return {
                'lottery_type': lottery_type,
                'prediction_method': 'fallback',
                'success_rate': 0.0,
                '一般號碼': [1, 2, 3, 4, 5, 6],
                '特別號': 1,
                'explanation': ['系統備用預測，建議謹慎使用'],
                'prediction_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        elif lottery_type == 'dailycash':
            return {
                'lottery_type': lottery_type,
                'prediction_method': 'fallback',
                'success_rate': 0.0,
                '號碼': [1, 2, 3, 4, 5],
                'explanation': ['系統備用預測，建議謹慎使用'],
                'prediction_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        
        return {}

def test_enhanced_lottery_predictor():
    """
    測試增強彩票預測系統
    """
    print("=== 增強彩票預測系統測試 ===")
    
    try:
        # 初始化預測器
        predictor = EnhancedLotteryPredictor()
        
        # 創建測試數據
        test_df = pd.DataFrame({
            'period': ['2024001', '2024002', '2024003'],
            'n1': [1, 5, 10],
            'n2': [8, 12, 15],
            'n3': [15, 20, 25],
            'n4': [22, 28, 30],
            'n5': [30, 35, 38],
            'n6': [38, 2, 8],
            'special': [3, 6, 2]
        })
        
        test_features = np.array([[0.1, 0.2, 0.3, 0.4, 0.5]])
        
        # 測試預測
        print("\n1. 測試基於成功率的預測...")
        result = predictor.predict_with_success_rate(
            df=test_df,
            features=test_features,
            lottery_type='powercolor'
        )
        print(f"預測結果：{result}")
        
        # 測試成功率獲取
        print("\n2. 測試成功率獲取...")
        success_rates = predictor.get_method_success_rates('powercolor')
        print(f"各方法成功率：{success_rates}")
        
        # 測試性能分析
        print("\n3. 測試性能分析...")
        performance = predictor.analyze_prediction_performance('powercolor')
        print(f"性能分析：{performance}")
        
        # 測試最佳方法獲取
        print("\n4. 測試最佳方法獲取...")
        best_method = predictor.get_best_prediction_method('powercolor')
        print(f"最佳預測方法：{best_method}")
        
        print("\n=== 測試完成 ===")
        
    except Exception as e:
        print(f"測試過程中發生錯誤：{str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_lottery_predictor()