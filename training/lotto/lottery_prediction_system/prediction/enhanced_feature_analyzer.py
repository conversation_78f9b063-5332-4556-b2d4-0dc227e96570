#!/usr/bin/env python3
"""
增強特徵分析器 - Phase 2 特徵工程改進
加入更多歷史特徵和統計指標提升預測準確性
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from scipy import stats
from scipy.stats import skew, kurtosis
import math
import logging
from datetime import datetime, timedelta
from itertools import combinations

logger = logging.getLogger('enhanced_feature_analyzer')

class EnhancedFeatureAnalyzer:
    """
    增強特徵分析器
    
    主要功能：
    1. 多維度統計特徵提取
    2. 時序特徵分析
    3. 相關性和模式識別
    4. 機器學習特徵工程
    """
    
    def __init__(self, lottery_type='powercolor'):
        """
        初始化增強特徵分析器
        
        Args:
            lottery_type: 彩票類型
        """
        self.lottery_type = lottery_type
        
        # 彩票配置
        self.config = {
            'powercolor': {'main_numbers': 38, 'main_count': 6, 'special_numbers': 8, 'has_special': True},
            'lotto649': {'main_numbers': 49, 'main_count': 6, 'special_numbers': 10, 'has_special': True},
            'dailycash': {'main_numbers': 39, 'main_count': 5, 'special_numbers': 0, 'has_special': False}
        }
        
        self.main_numbers = self.config[lottery_type]['main_numbers']
        self.main_count = self.config[lottery_type]['main_count']
        self.special_numbers = self.config[lottery_type]['special_numbers']
        self.has_special = self.config[lottery_type]['has_special']
        
        # 特徵存儲
        self.features = {}
        self.feature_matrix = None
        self.scaler = StandardScaler()
        
        logger.info(f"增強特徵分析器初始化完成 - 彩票類型: {lottery_type}")
    
    def extract_basic_statistical_features(self, df):
        """
        提取基本統計特徵
        
        Args:
            df: 歷史數據DataFrame
            
        Returns:
            dict: 基本統計特徵
        """
        logger.info("提取基本統計特徵...")
        
        features = {}
        
        # 取得主要號碼列
        main_cols = [f'Anumber{i}' for i in range(1, self.main_count + 1)]
        
        # 1. 號碼出現頻率特徵
        number_frequency = defaultdict(int)
        for _, row in df.iterrows():
            for col in main_cols:
                number_frequency[row[col]] += 1
        
        features['number_frequency'] = dict(number_frequency)
        features['avg_frequency'] = np.mean(list(number_frequency.values()))
        features['frequency_std'] = np.std(list(number_frequency.values()))
        
        # 2. 號碼間隔特徵
        gap_features = self._calculate_gap_features(df, main_cols)
        features.update(gap_features)
        
        # 3. 奇偶性特徵
        odd_even_features = self._calculate_odd_even_features(df, main_cols)
        features.update(odd_even_features)
        
        # 4. 大小號特徵
        size_features = self._calculate_size_features(df, main_cols)
        features.update(size_features)
        
        # 5. 連續號碼特徵
        consecutive_features = self._calculate_consecutive_features(df, main_cols)
        features.update(consecutive_features)
        
        logger.info(f"基本統計特徵提取完成，共 {len(features)} 個特徵")
        return features
    
    def extract_time_series_features(self, df):
        """
        提取時間序列特徵
        
        Args:
            df: 歷史數據DataFrame
            
        Returns:
            dict: 時間序列特徵
        """
        logger.info("提取時間序列特徵...")
        
        features = {}
        main_cols = [f'Anumber{i}' for i in range(1, self.main_count + 1)]
        
        # 1. 趨勢特徵
        trend_features = self._calculate_trend_features(df, main_cols)
        features.update(trend_features)
        
        # 2. 季節性特徵
        seasonal_features = self._calculate_seasonal_features(df, main_cols)
        features.update(seasonal_features)
        
        # 3. 波動性特徵
        volatility_features = self._calculate_volatility_features(df, main_cols)
        features.update(volatility_features)
        
        # 4. 自相關特徵
        autocorr_features = self._calculate_autocorrelation_features(df, main_cols)
        features.update(autocorr_features)
        
        # 5. 週期性特徵
        cycle_features = self._calculate_cycle_features(df, main_cols)
        features.update(cycle_features)
        
        logger.info(f"時間序列特徵提取完成，共 {len(features)} 個特徵")
        return features
    
    def extract_pattern_features(self, df):
        """
        提取模式特徵
        
        Args:
            df: 歷史數據DataFrame
            
        Returns:
            dict: 模式特徵
        """
        logger.info("提取模式特徵...")
        
        features = {}
        main_cols = [f'Anumber{i}' for i in range(1, self.main_count + 1)]
        
        # 1. 重複模式特徵
        repeat_features = self._calculate_repeat_patterns(df, main_cols)
        features.update(repeat_features)
        
        # 2. 對稱性特徵
        symmetry_features = self._calculate_symmetry_features(df, main_cols)
        features.update(symmetry_features)
        
        # 3. 聚類特徵
        cluster_features = self._calculate_cluster_features(df, main_cols)
        features.update(cluster_features)
        
        # 4. 距離特徵
        distance_features = self._calculate_distance_features(df, main_cols)
        features.update(distance_features)
        
        logger.info(f"模式特徵提取完成，共 {len(features)} 個特徵")
        return features
    
    def extract_advanced_statistical_features(self, df):
        """
        提取高級統計特徵
        
        Args:
            df: 歷史數據DataFrame
            
        Returns:
            dict: 高級統計特徵
        """
        logger.info("提取高級統計特徵...")
        
        features = {}
        main_cols = [f'Anumber{i}' for i in range(1, self.main_count + 1)]
        
        # 1. 分佈特徵
        distribution_features = self._calculate_distribution_features(df, main_cols)
        features.update(distribution_features)
        
        # 2. 相關性特徵
        correlation_features = self._calculate_correlation_features(df, main_cols)
        features.update(correlation_features)
        
        # 3. 熵特徵
        entropy_features = self._calculate_entropy_features(df, main_cols)
        features.update(entropy_features)
        
        # 4. 統計檢驗特徵
        statistical_test_features = self._calculate_statistical_test_features(df, main_cols)
        features.update(statistical_test_features)
        
        logger.info(f"高級統計特徵提取完成，共 {len(features)} 個特徵")
        return features
    
    def _calculate_gap_features(self, df, main_cols):
        """計算間隔特徵"""
        features = {}
        
        # 計算每個號碼的間隔
        for num in range(1, self.main_numbers + 1):
            appearances = []
            for idx, row in df.iterrows():
                if num in [row[col] for col in main_cols]:
                    appearances.append(idx)
            
            if len(appearances) >= 2:
                gaps = np.diff(appearances)
                features[f'gap_avg_{num}'] = np.mean(gaps)
                features[f'gap_std_{num}'] = np.std(gaps)
                features[f'gap_min_{num}'] = np.min(gaps)
                features[f'gap_max_{num}'] = np.max(gaps)
        
        return features
    
    def _calculate_odd_even_features(self, df, main_cols):
        """計算奇偶性特徵"""
        features = {}
        
        odd_counts = []
        even_counts = []
        
        for _, row in df.iterrows():
            numbers = [row[col] for col in main_cols]
            odd_count = sum(1 for num in numbers if num % 2 == 1)
            even_count = len(numbers) - odd_count
            
            odd_counts.append(odd_count)
            even_counts.append(even_count)
        
        features['odd_avg'] = np.mean(odd_counts)
        features['odd_std'] = np.std(odd_counts)
        features['even_avg'] = np.mean(even_counts)
        features['even_std'] = np.std(even_counts)
        features['odd_even_ratio'] = np.mean(odd_counts) / np.mean(even_counts) if np.mean(even_counts) > 0 else 0
        
        return features
    
    def _calculate_size_features(self, df, main_cols):
        """計算大小號特徵"""
        features = {}
        
        mid_point = self.main_numbers // 2
        small_counts = []
        large_counts = []
        
        for _, row in df.iterrows():
            numbers = [row[col] for col in main_cols]
            small_count = sum(1 for num in numbers if num <= mid_point)
            large_count = len(numbers) - small_count
            
            small_counts.append(small_count)
            large_counts.append(large_count)
        
        features['small_avg'] = np.mean(small_counts)
        features['small_std'] = np.std(small_counts)
        features['large_avg'] = np.mean(large_counts)
        features['large_std'] = np.std(large_counts)
        features['small_large_ratio'] = np.mean(small_counts) / np.mean(large_counts) if np.mean(large_counts) > 0 else 0
        
        return features
    
    def _calculate_consecutive_features(self, df, main_cols):
        """計算連續號碼特徵"""
        features = {}
        
        consecutive_counts = []
        max_consecutive_counts = []
        
        for _, row in df.iterrows():
            numbers = sorted([row[col] for col in main_cols])
            consecutive_count = 0
            max_consecutive = 0
            current_consecutive = 1
            
            for i in range(1, len(numbers)):
                if numbers[i] == numbers[i-1] + 1:
                    current_consecutive += 1
                    consecutive_count += 1
                else:
                    max_consecutive = max(max_consecutive, current_consecutive)
                    current_consecutive = 1
            
            max_consecutive = max(max_consecutive, current_consecutive)
            consecutive_counts.append(consecutive_count)
            max_consecutive_counts.append(max_consecutive)
        
        features['consecutive_avg'] = np.mean(consecutive_counts)
        features['consecutive_std'] = np.std(consecutive_counts)
        features['max_consecutive_avg'] = np.mean(max_consecutive_counts)
        features['max_consecutive_std'] = np.std(max_consecutive_counts)
        
        return features
    
    def _calculate_trend_features(self, df, main_cols):
        """計算趨勢特徵"""
        features = {}
        
        # 計算每個號碼的趨勢
        for num in range(1, self.main_numbers + 1):
            appearances = []
            for idx, row in df.iterrows():
                if num in [row[col] for col in main_cols]:
                    appearances.append(1)
                else:
                    appearances.append(0)
            
            if len(appearances) >= 10:
                # 計算趨勢斜率
                x = np.arange(len(appearances))
                slope, intercept, r_value, p_value, std_err = stats.linregress(x, appearances)
                
                features[f'trend_slope_{num}'] = slope
                features[f'trend_r2_{num}'] = r_value ** 2
                features[f'trend_p_value_{num}'] = p_value
        
        return features
    
    def _calculate_seasonal_features(self, df, main_cols):
        """計算季節性特徵"""
        features = {}
        
        # 假設每10期為一個季節
        season_length = 10
        
        for num in range(1, self.main_numbers + 1):
            seasonal_counts = []
            
            for season_start in range(0, len(df), season_length):
                season_end = min(season_start + season_length, len(df))
                season_data = df.iloc[season_start:season_end]
                
                count = 0
                for _, row in season_data.iterrows():
                    if num in [row[col] for col in main_cols]:
                        count += 1
                
                seasonal_counts.append(count)
            
            if len(seasonal_counts) >= 3:
                features[f'seasonal_avg_{num}'] = np.mean(seasonal_counts)
                features[f'seasonal_std_{num}'] = np.std(seasonal_counts)
                features[f'seasonal_cv_{num}'] = np.std(seasonal_counts) / np.mean(seasonal_counts) if np.mean(seasonal_counts) > 0 else 0
        
        return features
    
    def _calculate_volatility_features(self, df, main_cols):
        """計算波動性特徵"""
        features = {}
        
        # 計算整體波動性
        all_numbers = []
        for _, row in df.iterrows():
            numbers = [row[col] for col in main_cols]
            all_numbers.extend(numbers)
        
        if len(all_numbers) >= 10:
            # 移動平均和波動率
            window_size = 10
            moving_avg = []
            moving_std = []
            
            for i in range(window_size, len(all_numbers)):
                window_data = all_numbers[i-window_size:i]
                moving_avg.append(np.mean(window_data))
                moving_std.append(np.std(window_data))
            
            features['volatility_avg'] = np.mean(moving_std)
            features['volatility_std'] = np.std(moving_std)
            features['volatility_max'] = np.max(moving_std)
            features['volatility_min'] = np.min(moving_std)
        
        return features
    
    def _calculate_autocorrelation_features(self, df, main_cols):
        """計算自相關特徵"""
        features = {}
        
        # 計算號碼序列的自相關
        for num in range(1, self.main_numbers + 1):
            appearances = []
            for _, row in df.iterrows():
                if num in [row[col] for col in main_cols]:
                    appearances.append(1)
                else:
                    appearances.append(0)
            
            if len(appearances) >= 20:
                # 計算不同滯後期的自相關
                for lag in [1, 2, 3, 5, 10]:
                    if len(appearances) > lag:
                        autocorr = np.corrcoef(appearances[:-lag], appearances[lag:])[0, 1]
                        if not np.isnan(autocorr):
                            features[f'autocorr_{num}_lag{lag}'] = autocorr
        
        return features
    
    def _calculate_cycle_features(self, df, main_cols):
        """計算週期性特徵"""
        features = {}
        
        # 使用FFT檢測週期性
        for num in range(1, self.main_numbers + 1):
            appearances = []
            for _, row in df.iterrows():
                if num in [row[col] for col in main_cols]:
                    appearances.append(1)
                else:
                    appearances.append(0)
            
            if len(appearances) >= 32:  # 需要足夠的數據進行FFT
                # 進行FFT分析
                fft_result = np.fft.fft(appearances)
                frequencies = np.fft.fftfreq(len(appearances))
                
                # 找到主要頻率
                magnitude = np.abs(fft_result)
                main_freq_idx = np.argmax(magnitude[1:len(magnitude)//2]) + 1
                main_frequency = frequencies[main_freq_idx]
                
                features[f'dominant_frequency_{num}'] = main_frequency
                features[f'dominant_magnitude_{num}'] = magnitude[main_freq_idx]
        
        return features
    
    def _calculate_repeat_patterns(self, df, main_cols):
        """計算重複模式"""
        features = {}
        
        # 計算號碼組合的重複
        combinations_2 = []
        combinations_3 = []
        
        for _, row in df.iterrows():
            numbers = sorted([row[col] for col in main_cols])
            
            # 2號碼組合
            for combo in combinations(numbers, 2):
                combinations_2.append(combo)
            
            # 3號碼組合
            for combo in combinations(numbers, 3):
                combinations_3.append(combo)
        
        # 計算重複頻率
        combo_2_counts = Counter(combinations_2)
        combo_3_counts = Counter(combinations_3)
        
        features['repeat_2_max'] = max(combo_2_counts.values()) if combo_2_counts else 0
        features['repeat_2_avg'] = np.mean(list(combo_2_counts.values())) if combo_2_counts else 0
        features['repeat_3_max'] = max(combo_3_counts.values()) if combo_3_counts else 0
        features['repeat_3_avg'] = np.mean(list(combo_3_counts.values())) if combo_3_counts else 0
        
        return features
    
    def _calculate_symmetry_features(self, df, main_cols):
        """計算對稱性特徵"""
        features = {}
        
        symmetry_scores = []
        
        for _, row in df.iterrows():
            numbers = sorted([row[col] for col in main_cols])
            
            # 計算相對於中位數的對稱性
            mid_point = self.main_numbers / 2
            left_distances = [abs(num - mid_point) for num in numbers if num < mid_point]
            right_distances = [abs(num - mid_point) for num in numbers if num > mid_point]
            
            if len(left_distances) > 0 and len(right_distances) > 0:
                symmetry_score = 1 - abs(np.mean(left_distances) - np.mean(right_distances)) / mid_point
                symmetry_scores.append(symmetry_score)
        
        if symmetry_scores:
            features['symmetry_avg'] = np.mean(symmetry_scores)
            features['symmetry_std'] = np.std(symmetry_scores)
        
        return features
    
    def _calculate_cluster_features(self, df, main_cols):
        """計算聚類特徵"""
        features = {}
        
        # 將號碼組合轉換為特徵向量
        number_vectors = []
        for _, row in df.iterrows():
            vector = [0] * self.main_numbers
            for col in main_cols:
                if 1 <= row[col] <= self.main_numbers:
                    vector[row[col] - 1] = 1
            number_vectors.append(vector)
        
        if len(number_vectors) >= 10:
            # 使用KMeans聚類
            try:
                kmeans = KMeans(n_clusters=min(5, len(number_vectors)), random_state=42)
                cluster_labels = kmeans.fit_predict(number_vectors)
                
                features['cluster_count'] = len(set(cluster_labels))
                features['cluster_entropy'] = self._calculate_entropy(cluster_labels)
                
                # 計算聚類中心特徵
                cluster_centers = kmeans.cluster_centers_
                features['cluster_center_avg'] = np.mean(cluster_centers)
                features['cluster_center_std'] = np.std(cluster_centers)
                
            except Exception as e:
                logger.warning(f"聚類分析失敗: {e}")
        
        return features
    
    def _calculate_distance_features(self, df, main_cols):
        """計算距離特徵"""
        features = {}
        
        # 計算連續期數間的距離
        distances = []
        prev_numbers = None
        
        for _, row in df.iterrows():
            current_numbers = set([row[col] for col in main_cols])
            
            if prev_numbers is not None:
                # 計算歐幾里得距離
                intersection = len(current_numbers.intersection(prev_numbers))
                union = len(current_numbers.union(prev_numbers))
                
                jaccard_distance = 1 - intersection / union if union > 0 else 1
                distances.append(jaccard_distance)
            
            prev_numbers = current_numbers
        
        if distances:
            features['distance_avg'] = np.mean(distances)
            features['distance_std'] = np.std(distances)
            features['distance_min'] = np.min(distances)
            features['distance_max'] = np.max(distances)
        
        return features
    
    def _calculate_distribution_features(self, df, main_cols):
        """計算分佈特徵"""
        features = {}
        
        # 收集所有號碼
        all_numbers = []
        for _, row in df.iterrows():
            all_numbers.extend([row[col] for col in main_cols])
        
        if len(all_numbers) >= 10:
            # 基本分佈統計
            features['mean'] = np.mean(all_numbers)
            features['std'] = np.std(all_numbers)
            features['skewness'] = skew(all_numbers)
            features['kurtosis'] = kurtosis(all_numbers)
            
            # 百分位數
            percentiles = [10, 25, 50, 75, 90]
            for p in percentiles:
                features[f'percentile_{p}'] = np.percentile(all_numbers, p)
        
        return features
    
    def _calculate_correlation_features(self, df, main_cols):
        """計算相關性特徵"""
        features = {}
        
        # 計算號碼間的相關性
        if len(df) >= 10:
            correlation_matrix = df[main_cols].corr()
            
            # 提取上三角矩陣的相關係數
            correlations = []
            for i in range(len(main_cols)):
                for j in range(i+1, len(main_cols)):
                    corr = correlation_matrix.iloc[i, j]
                    if not np.isnan(corr):
                        correlations.append(corr)
            
            if correlations:
                features['correlation_avg'] = np.mean(correlations)
                features['correlation_std'] = np.std(correlations)
                features['correlation_max'] = np.max(correlations)
                features['correlation_min'] = np.min(correlations)
        
        return features
    
    def _calculate_entropy_features(self, df, main_cols):
        """計算熵特徵"""
        features = {}
        
        # 計算號碼出現的熵
        number_counts = Counter()
        for _, row in df.iterrows():
            for col in main_cols:
                number_counts[row[col]] += 1
        
        if number_counts:
            total = sum(number_counts.values())
            probabilities = [count / total for count in number_counts.values()]
            entropy = -sum(p * np.log2(p) for p in probabilities if p > 0)
            
            features['entropy'] = entropy
            features['max_entropy'] = np.log2(len(number_counts))
            features['entropy_ratio'] = entropy / np.log2(len(number_counts))
        
        return features
    
    def _calculate_statistical_test_features(self, df, main_cols):
        """計算統計檢驗特徵"""
        features = {}
        
        # 收集所有號碼
        all_numbers = []
        for _, row in df.iterrows():
            all_numbers.extend([row[col] for col in main_cols])
        
        if len(all_numbers) >= 20:
            # 正態性檢驗
            try:
                from scipy.stats import shapiro, kstest
                
                # 限制數據大小以避免shapiro測試限制
                sample_size = min(len(all_numbers), 5000)
                sample_data = np.random.choice(all_numbers, sample_size, replace=False)
                
                # Shapiro-Wilk檢驗
                shapiro_stat, shapiro_p = shapiro(sample_data)
                features['shapiro_statistic'] = float(shapiro_stat)
                features['shapiro_p_value'] = float(shapiro_p)
                
                # 均勻性檢驗
                def uniform_cdf(x):
                    return (x - 1) / (self.main_numbers - 1) if 1 <= x <= self.main_numbers else 0
                
                ks_stat, ks_p = kstest(sample_data, uniform_cdf)
                features['ks_statistic'] = float(ks_stat)
                features['ks_p_value'] = float(ks_p)
                
            except Exception as e:
                logger.warning(f"統計檢驗失敗: {e}")
        
        return features
    
    def _calculate_entropy(self, labels):
        """計算熵"""
        if len(labels) == 0:
            return 0
        
        label_counts = Counter(labels)
        total = len(labels)
        
        entropy = 0
        for count in label_counts.values():
            p = count / total
            if p > 0:
                entropy -= p * np.log2(p)
        
        return entropy
    
    def extract_all_features(self, df):
        """
        提取所有特徵
        
        Args:
            df: 歷史數據DataFrame
            
        Returns:
            dict: 所有特徵的字典
        """
        logger.info("開始提取所有特徵...")
        
        all_features = {}
        
        # 提取各類特徵
        basic_features = self.extract_basic_statistical_features(df)
        all_features.update(basic_features)
        
        time_series_features = self.extract_time_series_features(df)
        all_features.update(time_series_features)
        
        pattern_features = self.extract_pattern_features(df)
        all_features.update(pattern_features)
        
        advanced_features = self.extract_advanced_statistical_features(df)
        all_features.update(advanced_features)
        
        # 過濾無效特徵
        valid_features = {}
        for key, value in all_features.items():
            try:
                # 檢查是否為數字類型且有效
                if isinstance(value, (int, float, np.integer, np.floating)):
                    if not np.isnan(float(value)) and not np.isinf(float(value)):
                        valid_features[key] = float(value)
                elif isinstance(value, (list, tuple)):
                    # 如果是列表或元組，跳過
                    continue
                else:
                    # 嘗試轉換為浮點數
                    float_value = float(value)
                    if not np.isnan(float_value) and not np.isinf(float_value):
                        valid_features[key] = float_value
            except (ValueError, TypeError):
                # 無法轉換的值，跳過
                continue
        
        self.features = valid_features
        logger.info(f"所有特徵提取完成，共 {len(valid_features)} 個有效特徵")
        
        return valid_features
    
    def create_feature_matrix(self, df):
        """
        創建特徵矩陣
        
        Args:
            df: 歷史數據DataFrame
            
        Returns:
            np.ndarray: 特徵矩陣
        """
        logger.info("創建特徵矩陣...")
        
        # 提取所有特徵
        features = self.extract_all_features(df)
        
        # 轉換為矩陣格式
        feature_names = list(features.keys())
        feature_values = list(features.values())
        
        # 創建單行矩陣（代表當前狀態）
        feature_matrix = np.array(feature_values).reshape(1, -1)
        
        # 標準化
        self.feature_matrix = self.scaler.fit_transform(feature_matrix)
        
        logger.info(f"特徵矩陣創建完成，形狀: {self.feature_matrix.shape}")
        
        return self.feature_matrix, feature_names
    
    def get_feature_importance(self, df):
        """
        計算特徵重要性
        
        Args:
            df: 歷史數據DataFrame
            
        Returns:
            dict: 特徵重要性排序
        """
        logger.info("計算特徵重要性...")
        
        features = self.extract_all_features(df)
        
        # 簡單的特徵重要性評估（基於方差）
        feature_importance = {}
        
        for feature_name, value in features.items():
            # 基於特徵類型給予不同權重
            if 'frequency' in feature_name:
                importance = 0.8
            elif 'trend' in feature_name:
                importance = 0.7
            elif 'cycle' in feature_name:
                importance = 0.6
            elif 'entropy' in feature_name:
                importance = 0.5
            else:
                importance = 0.3
            
            feature_importance[feature_name] = importance * abs(value)
        
        # 排序
        sorted_importance = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
        
        logger.info(f"特徵重要性計算完成，top 10 特徵：")
        for i, (feature, importance) in enumerate(sorted_importance[:10]):
            logger.info(f"  {i+1}. {feature}: {importance:.4f}")
        
        return dict(sorted_importance)
    
    def generate_feature_report(self, df):
        """
        生成特徵分析報告
        
        Args:
            df: 歷史數據DataFrame
            
        Returns:
            str: 特徵分析報告
        """
        logger.info("生成特徵分析報告...")
        
        # 提取特徵
        features = self.extract_all_features(df)
        feature_importance = self.get_feature_importance(df)
        
        report = []
        report.append("=" * 60)
        report.append("🔍 增強特徵分析報告")
        report.append("=" * 60)
        
        report.append(f"📊 數據概況:")
        report.append(f"  • 歷史期數: {len(df)}")
        report.append(f"  • 彩票類型: {self.lottery_type}")
        report.append(f"  • 主要號碼範圍: 1-{self.main_numbers}")
        report.append(f"  • 選取號碼數: {self.main_count}")
        
        report.append(f"\n🎯 特徵統計:")
        report.append(f"  • 總特徵數: {len(features)}")
        report.append(f"  • 基本統計特徵: {len([k for k in features.keys() if any(x in k for x in ['frequency', 'gap', 'odd', 'even'])])}")
        report.append(f"  • 時間序列特徵: {len([k for k in features.keys() if any(x in k for x in ['trend', 'seasonal', 'autocorr'])])}")
        report.append(f"  • 模式特徵: {len([k for k in features.keys() if any(x in k for x in ['repeat', 'symmetry', 'cluster'])])}")
        report.append(f"  • 高級統計特徵: {len([k for k in features.keys() if any(x in k for x in ['entropy', 'correlation', 'distribution'])])}")
        
        report.append(f"\n🏆 重要特徵 Top 10:")
        for i, (feature, importance) in enumerate(list(feature_importance.items())[:10]):
            report.append(f"  {i+1:2d}. {feature[:40]:<40} {importance:>8.4f}")
        
        report.append(f"\n📈 關鍵統計指標:")
        if 'entropy' in features:
            report.append(f"  • 系統熵值: {features['entropy']:.4f}")
        if 'correlation_avg' in features:
            report.append(f"  • 平均相關性: {features['correlation_avg']:.4f}")
        if 'volatility_avg' in features:
            report.append(f"  • 平均波動率: {features['volatility_avg']:.4f}")
        if 'symmetry_avg' in features:
            report.append(f"  • 平均對稱性: {features['symmetry_avg']:.4f}")
        
        report.append("\n" + "=" * 60)
        
        return "\n".join(report)