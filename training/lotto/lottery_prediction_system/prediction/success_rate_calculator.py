#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
成功率計算器

用於計算和管理預測方法的歷史成功率，替代原有的信心指數系統。
提供基於實際驗證數據的可靠性評估。
"""

import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass
from collections import defaultdict

@dataclass
class PredictionRecord:
    """預測記錄數據類"""
    id: int
    prediction_date: str
    draw_date: str
    method: str
    lottery_type: str
    predicted_numbers: List[int]
    actual_numbers: List[int]
    special_predicted: Optional[int]
    special_actual: Optional[int]
    match_count: int
    special_match: bool
    confidence: float
    is_verified: bool

class SuccessRateCalculator:
    """成功率計算器
    
    負責計算各種預測方法的歷史成功率，提供可靠的預測性能評估。
    """
    
    def __init__(self, db_path: str = "lottery_predictions.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self._init_database()
    
    def _init_database(self):
        """初始化數據庫表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 創建預測記錄表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS prediction_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        prediction_date TEXT NOT NULL,
                        draw_date TEXT NOT NULL,
                        method TEXT NOT NULL,
                        lottery_type TEXT NOT NULL,
                        predicted_numbers TEXT NOT NULL,
                        actual_numbers TEXT,
                        special_predicted INTEGER,
                        special_actual INTEGER,
                        match_count INTEGER DEFAULT 0,
                        special_match BOOLEAN DEFAULT FALSE,
                        confidence REAL DEFAULT 0.0,
                        is_verified BOOLEAN DEFAULT FALSE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 創建方法性能統計表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS method_performance (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        method TEXT NOT NULL,
                        lottery_type TEXT NOT NULL,
                        calculation_date TEXT NOT NULL,
                        period_days INTEGER NOT NULL,
                        total_predictions INTEGER NOT NULL,
                        success_rate_3plus REAL NOT NULL,
                        success_rate_4plus REAL NOT NULL,
                        success_rate_5plus REAL NOT NULL,
                        success_rate_6 REAL NOT NULL,
                        avg_matches REAL NOT NULL,
                        trend TEXT,
                        UNIQUE(method, lottery_type, calculation_date, period_days)
                    )
                """)
                
                conn.commit()
                self.logger.info("數據庫初始化完成")
                
        except Exception as e:
            self.logger.error(f"數據庫初始化失敗: {e}")
            raise
    
    def record_prediction(self, method: str, lottery_type: str, 
                         predicted_numbers: List[int], 
                         special_predicted: Optional[int] = None,
                         confidence: float = 0.0,
                         draw_date: Optional[str] = None) -> int:
        """記錄新的預測
        
        Args:
            method: 預測方法 ('ml', 'board_path', 'ensemble')
            lottery_type: 彩票類型
            predicted_numbers: 預測的主要號碼
            special_predicted: 預測的特別號碼
            confidence: 原始信心指數（用於對比分析）
            draw_date: 開獎日期，如果為None則自動計算下次開獎日期
            
        Returns:
            int: 預測記錄ID
        """
        try:
            prediction_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            if draw_date is None:
                # 自動計算下次開獎日期（假設每週二、四、日開獎）
                draw_date = self._calculate_next_draw_date()
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO prediction_records 
                    (prediction_date, draw_date, method, lottery_type, 
                     predicted_numbers, special_predicted, confidence)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    prediction_date, draw_date, method, lottery_type,
                    ','.join(map(str, predicted_numbers)), special_predicted, confidence
                ))
                
                record_id = cursor.lastrowid
                conn.commit()
                
                self.logger.info(f"預測記錄已保存，ID: {record_id}")
                return record_id
                
        except Exception as e:
            self.logger.error(f"記錄預測失敗: {e}")
            raise
    
    def verify_prediction(self, record_id: int, actual_numbers: List[int], 
                         special_actual: Optional[int] = None) -> Dict:
        """驗證預測結果
        
        Args:
            record_id: 預測記錄ID
            actual_numbers: 實際開獎號碼
            special_actual: 實際特別號碼
            
        Returns:
            Dict: 驗證結果
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 獲取預測記錄
                cursor.execute(
                    "SELECT predicted_numbers, special_predicted FROM prediction_records WHERE id = ?",
                    (record_id,)
                )
                result = cursor.fetchone()
                
                if not result:
                    raise ValueError(f"找不到預測記錄 ID: {record_id}")
                
                predicted_numbers = list(map(int, result[0].split(',')))
                special_predicted = result[1]
                
                # 計算匹配數量
                match_count = len(set(predicted_numbers) & set(actual_numbers))
                special_match = (special_predicted == special_actual) if special_predicted and special_actual else False
                
                # 更新記錄
                cursor.execute("""
                    UPDATE prediction_records 
                    SET actual_numbers = ?, special_actual = ?, 
                        match_count = ?, special_match = ?, is_verified = TRUE
                    WHERE id = ?
                """, (
                    ','.join(map(str, actual_numbers)), special_actual,
                    match_count, special_match, record_id
                ))
                
                conn.commit()
                
                verification_result = {
                    'record_id': record_id,
                    'predicted_numbers': predicted_numbers,
                    'actual_numbers': actual_numbers,
                    'match_count': match_count,
                    'special_match': special_match,
                    'success_level': self._determine_success_level(match_count, special_match)
                }
                
                self.logger.info(f"預測驗證完成，ID: {record_id}, 匹配: {match_count}個")
                return verification_result
                
        except Exception as e:
            self.logger.error(f"驗證預測失敗: {e}")
            raise
    
    def calculate_method_success_rate(self, method: str, lottery_type: str, 
                                    period_days: int = 90, 
                                    min_predictions: int = 10) -> Dict:
        """計算特定方法的成功率
        
        Args:
            method: 預測方法
            lottery_type: 彩票類型
            period_days: 統計期間（天）
            min_predictions: 最少預測數量要求
            
        Returns:
            Dict: 成功率統計
        """
        try:
            start_date = (datetime.now() - timedelta(days=period_days)).strftime('%Y-%m-%d')
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 獲取指定期間的已驗證預測
                cursor.execute("""
                    SELECT match_count, special_match, confidence
                    FROM prediction_records 
                    WHERE method = ? AND lottery_type = ? 
                          AND draw_date >= ? AND is_verified = TRUE
                    ORDER BY draw_date DESC
                """, (method, lottery_type, start_date))
                
                records = cursor.fetchall()
                
                if len(records) < min_predictions:
                    return {
                        'status': 'insufficient_data',
                        'message': f'數據不足，需要至少 {min_predictions} 個已驗證預測，當前只有 {len(records)} 個',
                        'total_predictions': len(records),
                        'period_days': period_days
                    }
                
                # 計算各級別成功率
                total_predictions = len(records)
                success_stats = {
                    'status': 'success',
                    'method': method,
                    'lottery_type': lottery_type,
                    'period_days': period_days,
                    'total_predictions': total_predictions,
                    'success_rates': {},
                    'average_matches': sum(r[0] for r in records) / total_predictions,
                    'confidence_correlation': self._analyze_confidence_correlation(records)
                }
                
                # 按中獎號碼數分類統計
                for min_match in [3, 4, 5, 6]:
                    successful = [r for r in records if r[0] >= min_match]
                    success_rate = len(successful) / total_predictions
                    
                    success_stats['success_rates'][f'{min_match}_or_more'] = {
                        'rate': success_rate,
                        'count': len(successful),
                        'percentage': f"{success_rate * 100:.1f}%",
                        'description': self._get_success_description(min_match)
                    }
                
                # 特別號碼成功率
                special_matches = [r for r in records if r[1]]
                if special_matches:
                    success_stats['special_number_rate'] = {
                        'rate': len(special_matches) / total_predictions,
                        'count': len(special_matches),
                        'percentage': f"{len(special_matches) / total_predictions * 100:.1f}%"
                    }
                
                # 保存統計結果
                self._save_performance_stats(method, lottery_type, period_days, success_stats)
                
                return success_stats
                
        except Exception as e:
            self.logger.error(f"計算成功率失敗: {e}")
            raise
    
    def get_all_methods_comparison(self, lottery_type: str, period_days: int = 90) -> Dict:
        """獲取所有方法的成功率對比
        
        Args:
            lottery_type: 彩票類型
            period_days: 統計期間
            
        Returns:
            Dict: 方法對比結果
        """
        methods = ['ml', 'board_path', 'ensemble']
        comparison = {
            'lottery_type': lottery_type,
            'period_days': period_days,
            'methods': {},
            'best_method': None,
            'recommendation': None
        }
        
        best_score = 0
        best_method = None
        
        for method in methods:
            stats = self.calculate_method_success_rate(method, lottery_type, period_days)
            comparison['methods'][method] = stats
            
            if stats.get('status') == 'success':
                # 計算綜合評分（3個號碼以上成功率 * 0.6 + 4個號碼以上成功率 * 0.4）
                score = (stats['success_rates']['3_or_more']['rate'] * 0.6 + 
                        stats['success_rates']['4_or_more']['rate'] * 0.4)
                
                if score > best_score:
                    best_score = score
                    best_method = method
        
        comparison['best_method'] = best_method
        comparison['recommendation'] = self._generate_method_recommendation(comparison)
        
        return comparison
    
    def get_recent_trend(self, method: str, lottery_type: str, periods: int = 4) -> Dict:
        """獲取最近的成功率趨勢
        
        Args:
            method: 預測方法
            lottery_type: 彩票類型
            periods: 分析期數
            
        Returns:
            Dict: 趨勢分析結果
        """
        trend_data = []
        period_length = 30  # 每期30天
        
        for i in range(periods):
            start_days = (i + 1) * period_length
            end_days = i * period_length
            
            start_date = (datetime.now() - timedelta(days=start_days)).strftime('%Y-%m-%d')
            end_date = (datetime.now() - timedelta(days=end_days)).strftime('%Y-%m-%d')
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT match_count FROM prediction_records 
                    WHERE method = ? AND lottery_type = ? 
                          AND draw_date >= ? AND draw_date < ? AND is_verified = TRUE
                """, (method, lottery_type, start_date, end_date))
                
                records = cursor.fetchall()
                
                if records:
                    success_rate = len([r for r in records if r[0] >= 3]) / len(records)
                    avg_matches = sum(r[0] for r in records) / len(records)
                    
                    trend_data.append({
                        'period': f"{start_date} 至 {end_date}",
                        'predictions': len(records),
                        'success_rate': success_rate,
                        'avg_matches': avg_matches
                    })
        
        # 分析趨勢
        trend_analysis = self._analyze_trend(trend_data)
        
        return {
            'method': method,
            'lottery_type': lottery_type,
            'trend_data': trend_data,
            'trend_direction': trend_analysis['direction'],
            'trend_strength': trend_analysis['strength'],
            'recommendation': trend_analysis['recommendation']
        }
    
    def _calculate_next_draw_date(self) -> str:
        """計算下次開獎日期"""
        today = datetime.now()
        # 假設每週二(1)、四(3)、日(6)開獎
        draw_days = [1, 3, 6]
        
        for i in range(7):
            check_date = today + timedelta(days=i)
            if check_date.weekday() in draw_days:
                return check_date.strftime('%Y-%m-%d')
        
        # 如果找不到，返回下週二
        next_tuesday = today + timedelta(days=(1 - today.weekday()) % 7)
        return next_tuesday.strftime('%Y-%m-%d')
    
    def _determine_success_level(self, match_count: int, special_match: bool) -> str:
        """確定成功級別"""
        if match_count == 6:
            return "頭獎" if special_match else "二獎"
        elif match_count == 5:
            return "三獎" if special_match else "四獎"
        elif match_count == 4:
            return "五獎" if special_match else "六獎"
        elif match_count == 3:
            return "七獎" if special_match else "八獎"
        else:
            return "未中獎"
    
    def _analyze_confidence_correlation(self, records: List[Tuple]) -> Dict:
        """分析信心指數與實際成功率的相關性"""
        if not records:
            return {'correlation': 'no_data'}
        
        # 按信心指數分組
        high_conf = [r for r in records if r[2] >= 0.8]
        med_conf = [r for r in records if 0.6 <= r[2] < 0.8]
        low_conf = [r for r in records if r[2] < 0.6]
        
        correlation_data = {}
        
        for group_name, group_data in [('high', high_conf), ('medium', med_conf), ('low', low_conf)]:
            if group_data:
                success_rate = len([r for r in group_data if r[0] >= 3]) / len(group_data)
                avg_matches = sum(r[0] for r in group_data) / len(group_data)
                
                correlation_data[group_name] = {
                    'count': len(group_data),
                    'success_rate': success_rate,
                    'avg_matches': avg_matches
                }
        
        return correlation_data
    
    def _get_success_description(self, min_match: int) -> str:
        """獲取成功級別描述"""
        descriptions = {
            3: "中獎（至少3個號碼）",
            4: "較好中獎（至少4個號碼）",
            5: "優秀中獎（至少5個號碼）",
            6: "完美中獎（6個號碼）"
        }
        return descriptions.get(min_match, "未知級別")
    
    def _save_performance_stats(self, method: str, lottery_type: str, 
                               period_days: int, stats: Dict):
        """保存性能統計數據"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                calculation_date = datetime.now().strftime('%Y-%m-%d')
                
                cursor.execute("""
                    INSERT OR REPLACE INTO method_performance 
                    (method, lottery_type, calculation_date, period_days, total_predictions,
                     success_rate_3plus, success_rate_4plus, success_rate_5plus, success_rate_6, avg_matches)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    method, lottery_type, calculation_date, period_days,
                    stats['total_predictions'],
                    stats['success_rates']['3_or_more']['rate'],
                    stats['success_rates']['4_or_more']['rate'],
                    stats['success_rates']['5_or_more']['rate'],
                    stats['success_rates']['6_or_more']['rate'],
                    stats['average_matches']
                ))
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"保存性能統計失敗: {e}")
    
    def _generate_method_recommendation(self, comparison: Dict) -> str:
        """生成方法推薦"""
        best_method = comparison['best_method']
        if not best_method:
            return "數據不足，無法提供推薦"
        
        method_names = {
            'ml': '機器學習',
            'board_path': '板路分析',
            'ensemble': '集成預測'
        }
        
        best_stats = comparison['methods'][best_method]
        success_rate = best_stats['success_rates']['3_or_more']['percentage']
        
        return f"推薦使用 {method_names.get(best_method, best_method)} 方法，" \
               f"該方法在過去 {comparison['period_days']} 天內的成功率為 {success_rate}"
    
    def _analyze_trend(self, trend_data: List[Dict]) -> Dict:
        """分析趨勢方向"""
        if len(trend_data) < 2:
            return {
                'direction': 'unknown',
                'strength': 'weak',
                'recommendation': '數據不足以分析趨勢'
            }
        
        # 計算趨勢斜率
        success_rates = [d['success_rate'] for d in trend_data]
        
        # 簡單線性趨勢分析
        if len(success_rates) >= 3:
            recent_avg = sum(success_rates[:2]) / 2
            older_avg = sum(success_rates[-2:]) / 2
            
            if recent_avg > older_avg * 1.1:
                direction = 'improving'
                strength = 'strong' if recent_avg > older_avg * 1.2 else 'moderate'
            elif recent_avg < older_avg * 0.9:
                direction = 'declining'
                strength = 'strong' if recent_avg < older_avg * 0.8 else 'moderate'
            else:
                direction = 'stable'
                strength = 'moderate'
        else:
            direction = 'stable'
            strength = 'weak'
        
        recommendations = {
            'improving': '該方法表現正在改善，建議優先使用',
            'declining': '該方法表現正在下降，建議謹慎使用或考慮其他方法',
            'stable': '該方法表現穩定，可以繼續使用'
        }
        
        return {
            'direction': direction,
            'strength': strength,
            'recommendation': recommendations.get(direction, '無法提供建議')
        }


def test_success_rate_calculator():
    """測試成功率計算器"""
    print("=== 成功率計算器測試 ===")
    
    calculator = SuccessRateCalculator(":memory:")  # 使用內存數據庫測試
    
    # 測試記錄預測
    print("\n1. 測試記錄預測")
    record_id = calculator.record_prediction(
        method="ml",
        lottery_type="lotto649",
        predicted_numbers=[1, 15, 22, 28, 35, 38],
        special_predicted=7,
        confidence=0.85
    )
    print(f"預測記錄ID: {record_id}")
    
    # 測試驗證預測
    print("\n2. 測試驗證預測")
    verification = calculator.verify_prediction(
        record_id=record_id,
        actual_numbers=[1, 15, 22, 30, 35, 42],
        special_actual=7
    )
    print(f"驗證結果: {verification}")
    
    # 添加更多測試數據
    print("\n3. 添加更多測試數據")
    test_data = [
        ([2, 16, 23, 29, 36, 39], [2, 16, 23, 29, 36, 39], 6),  # 完美匹配
        ([3, 17, 24, 30, 37, 40], [3, 17, 24, 31, 38, 41], 3),  # 3個匹配
        ([4, 18, 25, 31, 38, 41], [5, 19, 26, 32, 39, 42], 0),  # 無匹配
        ([5, 19, 26, 32, 39, 42], [5, 19, 26, 32, 39, 43], 5),  # 5個匹配
    ]
    
    for i, (predicted, actual, expected_matches) in enumerate(test_data):
        record_id = calculator.record_prediction(
            method="board_path",
            lottery_type="lotto649",
            predicted_numbers=predicted,
            confidence=0.7 + i * 0.05
        )
        
        verification = calculator.verify_prediction(record_id, actual)
        print(f"測試 {i+1}: 預期匹配 {expected_matches}, 實際匹配 {verification['match_count']}")
    
    # 測試成功率計算
    print("\n4. 測試成功率計算")
    success_stats = calculator.calculate_method_success_rate("board_path", "lotto649", period_days=30)
    print(f"Board Path 成功率統計:")
    print(f"  總預測數: {success_stats.get('total_predictions', 0)}")
    if success_stats.get('success_rates'):
        for level, stats in success_stats['success_rates'].items():
            print(f"  {level}: {stats['percentage']} ({stats['count']}/{success_stats['total_predictions']})")
    
    print("\n=== 測試完成 ===")

if __name__ == "__main__":
    test_success_rate_calculator()