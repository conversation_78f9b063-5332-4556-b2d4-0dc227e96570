"""增強號碼分析器
實現大小號分析、特定號碼模式識別和穩定號碼確定功能
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging
from datetime import datetime
import json
from collections import Counter, defaultdict

logger = logging.getLogger(__name__)

class EnhancedNumberAnalyzer:
    """增強號碼分析器"""
    
    def __init__(self):
        self.lottery_configs = {
            'powercolor': {
                'first_area_range': (1, 38),
                'first_area_count': 6,
                'second_area_range': (1, 8),
                'second_area_count': 1,
                'big_small_threshold': 19  # 大於19為大號
            },
            'lotto649': {
                'first_area_range': (1, 49),
                'first_area_count': 6,
                'special_range': (1, 49),
                'special_count': 1,
                'big_small_threshold': 25  # 大於25為大號
            },
            'dailycash': {
                'number_range': (1, 39),
                'number_count': 5,
                'big_small_threshold': 20  # 大於20為大號
            }
        }
    
    def analyze_big_small_pattern(self, df: pd.DataFrame, lottery_type: str) -> Dict:
        """分析大小號模式
        
        Args:
            df: 歷史數據
            lottery_type: 彩票類型
            
        Returns:
            Dict: 大小號分析結果
        """
        try:
            config = self.lottery_configs[lottery_type]
            threshold = config['big_small_threshold']
            
            analysis = {
                'recent_patterns': [],
                'big_small_distribution': {},
                'sum_analysis': {},
                'prediction': {}
            }
            
            # 分析最近20期的大小號模式
            recent_data = df.tail(20)
            
            for _, row in recent_data.iterrows():
                if lottery_type == 'powercolor':
                    numbers = [row[f'Anumber{i}'] for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])]
                    second_area = row.get('Snumber', 0)
                elif lottery_type == 'lotto649':
                    numbers = [row[f'Anumber{i}'] for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])]
                    special = row.get('SpecialNumber', 0)
                elif lottery_type == 'dailycash':
                    numbers = [row[f'Anumber{i}'] for i in range(1, 6) if pd.notna(row[f'Anumber{i}'])]
                
                # 計算大小號分佈
                big_count = sum(1 for num in numbers if num > threshold)
                small_count = len(numbers) - big_count
                total_sum = sum(numbers)
                
                pattern = {
                    'period': row.get('Period', ''),
                    'numbers': numbers,
                    'big_count': big_count,
                    'small_count': small_count,
                    'total_sum': total_sum,
                    'is_big_sum': total_sum > (threshold * len(numbers))
                }
                
                if lottery_type in ['powercolor', 'lotto649']:
                    if lottery_type == 'powercolor':
                        pattern['second_area_big'] = second_area > (config['second_area_range'][1] // 2)
                    else:
                        pattern['special_big'] = special > threshold
                
                analysis['recent_patterns'].append(pattern)
            
            # 統計大小號分佈
            big_counts = [p['big_count'] for p in analysis['recent_patterns']]
            analysis['big_small_distribution'] = {
                'avg_big_count': np.mean(big_counts),
                'most_common_big_count': Counter(big_counts).most_common(1)[0],
                'big_count_trend': self._calculate_trend(big_counts[-10:])  # 最近10期趨勢
            }
            
            # 總和分析
            sums = [p['total_sum'] for p in analysis['recent_patterns']]
            analysis['sum_analysis'] = {
                'avg_sum': np.mean(sums),
                'sum_trend': self._calculate_trend(sums[-10:]),
                'big_sum_ratio': sum(1 for p in analysis['recent_patterns'] if p['is_big_sum']) / len(analysis['recent_patterns'])
            }
            
            # 預測下期大小號模式
            analysis['prediction'] = self._predict_big_small_pattern(analysis, lottery_type)
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析大小號模式時出錯: {str(e)}")
            return {}
    
    def identify_stable_numbers(self, df: pd.DataFrame, lottery_type: str, confidence_threshold: float = 0.7) -> Dict:
        """識別穩定的必中號碼
        
        Args:
            df: 歷史數據
            lottery_type: 彩票類型
            confidence_threshold: 信心閾值
            
        Returns:
            Dict: 穩定號碼分析結果
        """
        try:
            config = self.lottery_configs[lottery_type]
            
            analysis = {
                'hot_numbers': [],
                'cold_numbers': [],
                'stable_candidates': [],
                'pattern_analysis': {},
                'recommended_numbers': []
            }
            
            # 分析最近50期數據
            recent_data = df.tail(50)
            
            # 統計每個號碼的出現頻率
            number_frequency = defaultdict(int)
            number_positions = defaultdict(list)
            
            for idx, row in recent_data.iterrows():
                if lottery_type == 'powercolor':
                    numbers = [row[f'Anumber{i}'] for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])]
                elif lottery_type == 'lotto649':
                    numbers = [row[f'Anumber{i}'] for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])]
                elif lottery_type == 'dailycash':
                    numbers = [row[f'Anumber{i}'] for i in range(1, 6) if pd.notna(row[f'Anumber{i}'])]
                
                for pos, num in enumerate(numbers):
                    number_frequency[num] += 1
                    number_positions[num].append(pos)
            
            # 計算期望頻率
            total_periods = len(recent_data)
            if lottery_type in ['powercolor', 'lotto649']:
                expected_frequency = (6 / config['first_area_range'][1]) * total_periods
            else:
                expected_frequency = (5 / config['number_range'][1]) * total_periods
            
            # 分類號碼
            for num, freq in number_frequency.items():
                frequency_ratio = freq / total_periods
                
                if frequency_ratio > 0.3:  # 高頻號碼
                    analysis['hot_numbers'].append({
                        'number': num,
                        'frequency': freq,
                        'ratio': frequency_ratio,
                        'positions': number_positions[num]
                    })
                elif frequency_ratio < 0.1:  # 低頻號碼
                    analysis['cold_numbers'].append({
                        'number': num,
                        'frequency': freq,
                        'ratio': frequency_ratio
                    })
            
            # 識別穩定候選號碼
            analysis['stable_candidates'] = self._identify_stable_candidates(
                number_frequency, number_positions, recent_data, lottery_type
            )
            
            # 模式分析
            analysis['pattern_analysis'] = self._analyze_number_patterns(
                recent_data, lottery_type
            )
            
            # 推薦號碼（2-4個穩定號碼）
            analysis['recommended_numbers'] = self._select_recommended_numbers(
                analysis, confidence_threshold
            )
            
            return analysis
            
        except Exception as e:
            logger.error(f"識別穩定號碼時出錯: {str(e)}")
            return {}
    
    def analyze_specific_number_patterns(self, df: pd.DataFrame, lottery_type: str) -> Dict:
        """分析特定號碼產生模式
        
        Args:
            df: 歷史數據
            lottery_type: 彩票類型
            
        Returns:
            Dict: 特定號碼模式分析結果
        """
        try:
            analysis = {
                'consecutive_patterns': {},
                'gap_patterns': {},
                'position_patterns': {},
                'sum_patterns': {},
                'special_combinations': []
            }
            
            recent_data = df.tail(30)
            
            # 分析連號模式
            analysis['consecutive_patterns'] = self._analyze_consecutive_patterns(recent_data, lottery_type)
            
            # 分析間隔模式
            analysis['gap_patterns'] = self._analyze_gap_patterns(recent_data, lottery_type)
            
            # 分析位置模式
            analysis['position_patterns'] = self._analyze_position_patterns(recent_data, lottery_type)
            
            # 分析總和模式
            analysis['sum_patterns'] = self._analyze_sum_patterns(recent_data, lottery_type)
            
            # 識別特殊組合
            analysis['special_combinations'] = self._identify_special_combinations(recent_data, lottery_type)
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析特定號碼模式時出錯: {str(e)}")
            return {}
    
    def generate_enhanced_prediction(self, df: pd.DataFrame, lottery_type: str) -> Dict:
        """生成增強預測
        
        Args:
            df: 歷史數據
            lottery_type: 彩票類型
            
        Returns:
            Dict: 增強預測結果
        """
        try:
            # 執行各種分析
            big_small_analysis = self.analyze_big_small_pattern(df, lottery_type)
            stable_analysis = self.identify_stable_numbers(df, lottery_type)
            pattern_analysis = self.analyze_specific_number_patterns(df, lottery_type)
            
            # 整合分析結果生成預測
            prediction = {
                'stable_numbers': stable_analysis.get('recommended_numbers', []),
                'big_small_prediction': big_small_analysis.get('prediction', {}),
                'pattern_based_numbers': self._extract_pattern_numbers(pattern_analysis),
                'confidence_scores': {},
                'analysis_summary': {
                    'big_small': big_small_analysis,
                    'stable': stable_analysis,
                    'patterns': pattern_analysis
                }
            }
            
            # 生成最終號碼組合
            prediction['final_numbers'] = self._generate_final_numbers(
                prediction, lottery_type
            )
            
            return prediction
            
        except Exception as e:
            logger.error(f"生成增強預測時出錯: {str(e)}")
            return {}
    
    def _calculate_trend(self, values: List[float]) -> str:
        """計算趨勢"""
        if len(values) < 2:
            return 'stable'
        
        recent_avg = np.mean(values[-3:])
        earlier_avg = np.mean(values[:-3]) if len(values) > 3 else np.mean(values[:-1])
        
        if recent_avg > earlier_avg * 1.1:
            return 'increasing'
        elif recent_avg < earlier_avg * 0.9:
            return 'decreasing'
        else:
            return 'stable'
    
    def _predict_big_small_pattern(self, analysis: Dict, lottery_type: str) -> Dict:
        """預測大小號模式"""
        config = self.lottery_configs[lottery_type]
        
        # 基於趨勢和歷史模式預測
        big_count_trend = analysis['big_small_distribution']['big_count_trend']
        most_common_big = analysis['big_small_distribution']['most_common_big_count'][0]
        
        if big_count_trend == 'increasing':
            predicted_big_count = min(most_common_big + 1, config['first_area_count'] if lottery_type != 'dailycash' else config['number_count'])
        elif big_count_trend == 'decreasing':
            predicted_big_count = max(most_common_big - 1, 0)
        else:
            predicted_big_count = most_common_big
        
        return {
            'predicted_big_count': predicted_big_count,
            'predicted_small_count': (config['first_area_count'] if lottery_type != 'dailycash' else config['number_count']) - predicted_big_count,
            'confidence': 0.7 if big_count_trend != 'stable' else 0.8
        }
    
    def _identify_stable_candidates(self, frequency: Dict, positions: Dict, data: pd.DataFrame, lottery_type: str) -> List[Dict]:
        """識別穩定候選號碼"""
        candidates = []
        
        for num, freq in frequency.items():
            if freq >= 3:  # 至少出現3次
                # 計算位置穩定性
                pos_variance = np.var(positions[num]) if len(positions[num]) > 1 else 0
                
                # 計算間隔穩定性
                intervals = self._calculate_intervals(num, data, lottery_type)
                interval_stability = 1 / (np.var(intervals) + 1) if len(intervals) > 1 else 0.5
                
                stability_score = (freq / len(data)) * 0.6 + interval_stability * 0.4
                
                if stability_score > 0.3:
                    candidates.append({
                        'number': num,
                        'frequency': freq,
                        'stability_score': stability_score,
                        'position_variance': pos_variance
                    })
        
        return sorted(candidates, key=lambda x: x['stability_score'], reverse=True)
    
    def _calculate_intervals(self, number: int, data: pd.DataFrame, lottery_type: str) -> List[int]:
        """計算號碼出現間隔"""
        intervals = []
        last_appearance = -1
        
        for idx, row in data.iterrows():
            if lottery_type == 'powercolor':
                numbers = [row[f'Anumber{i}'] for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])]
            elif lottery_type == 'lotto649':
                numbers = [row[f'Anumber{i}'] for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])]
            elif lottery_type == 'dailycash':
                numbers = [row[f'Anumber{i}'] for i in range(1, 6) if pd.notna(row[f'Anumber{i}'])]
            
            if number in numbers:
                if last_appearance >= 0:
                    intervals.append(idx - last_appearance)
                last_appearance = idx
        
        return intervals
    
    def _analyze_consecutive_patterns(self, data: pd.DataFrame, lottery_type: str) -> Dict:
        """分析連號模式"""
        consecutive_counts = []
        
        for _, row in data.iterrows():
            if lottery_type == 'powercolor':
                numbers = sorted([row[f'Anumber{i}'] for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])])
            elif lottery_type == 'lotto649':
                numbers = sorted([row[f'Anumber{i}'] for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])])
            elif lottery_type == 'dailycash':
                numbers = sorted([row[f'Anumber{i}'] for i in range(1, 6) if pd.notna(row[f'Anumber{i}'])])
            
            consecutive_count = 0
            max_consecutive = 0
            
            for i in range(1, len(numbers)):
                if numbers[i] == numbers[i-1] + 1:
                    consecutive_count += 1
                else:
                    max_consecutive = max(max_consecutive, consecutive_count + 1)
                    consecutive_count = 0
            
            max_consecutive = max(max_consecutive, consecutive_count + 1)
            consecutive_counts.append(max_consecutive)
        
        return {
            'avg_consecutive': np.mean(consecutive_counts),
            'most_common_consecutive': Counter(consecutive_counts).most_common(1)[0],
            'consecutive_trend': self._calculate_trend(consecutive_counts[-10:])
        }
    
    def _analyze_gap_patterns(self, data: pd.DataFrame, lottery_type: str) -> Dict:
        """分析間隔模式"""
        gaps = []
        
        for _, row in data.iterrows():
            if lottery_type == 'powercolor':
                numbers = sorted([row[f'Anumber{i}'] for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])])
            elif lottery_type == 'lotto649':
                numbers = sorted([row[f'Anumber{i}'] for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])])
            elif lottery_type == 'dailycash':
                numbers = sorted([row[f'Anumber{i}'] for i in range(1, 6) if pd.notna(row[f'Anumber{i}'])])
            
            period_gaps = []
            for i in range(1, len(numbers)):
                gap = numbers[i] - numbers[i-1] - 1
                period_gaps.append(gap)
            
            gaps.extend(period_gaps)
        
        return {
            'avg_gap': np.mean(gaps) if gaps else 0,
            'most_common_gap': Counter(gaps).most_common(1)[0] if gaps else (0, 0),
            'gap_distribution': dict(Counter(gaps))
        }
    
    def _analyze_position_patterns(self, data: pd.DataFrame, lottery_type: str) -> Dict:
        """分析位置模式"""
        position_analysis = {}
        
        num_positions = 6 if lottery_type in ['powercolor', 'lotto649'] else 5
        
        for pos in range(num_positions):
            position_numbers = []
            for _, row in data.iterrows():
                num = row[f'Anumber{pos+1}']
                if pd.notna(num):
                    position_numbers.append(int(num))
            
            position_analysis[f'position_{pos+1}'] = {
                'avg': np.mean(position_numbers) if position_numbers else 0,
                'most_common': Counter(position_numbers).most_common(3) if position_numbers else [],
                'trend': self._calculate_trend(position_numbers[-10:]) if len(position_numbers) >= 10 else 'stable'
            }
        
        return position_analysis
    
    def _analyze_sum_patterns(self, data: pd.DataFrame, lottery_type: str) -> Dict:
        """分析總和模式"""
        sums = []
        
        for _, row in data.iterrows():
            if lottery_type == 'powercolor':
                numbers = [row[f'Anumber{i}'] for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])]
            elif lottery_type == 'lotto649':
                numbers = [row[f'Anumber{i}'] for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])]
            elif lottery_type == 'dailycash':
                numbers = [row[f'Anumber{i}'] for i in range(1, 6) if pd.notna(row[f'Anumber{i}'])]
            
            sums.append(sum(numbers))
        
        return {
            'avg_sum': np.mean(sums),
            'sum_range': (min(sums), max(sums)),
            'sum_trend': self._calculate_trend(sums[-10:]),
            'sum_distribution': dict(Counter(sums))
        }
    
    def _identify_special_combinations(self, data: pd.DataFrame, lottery_type: str) -> List[Dict]:
        """識別特殊組合"""
        combinations = []
        
        # 這裡可以實現更複雜的組合識別邏輯
        # 例如：奇偶組合、質數組合、等差數列等
        
        return combinations
    
    def _extract_pattern_numbers(self, pattern_analysis: Dict) -> List[int]:
        """從模式分析中提取推薦號碼"""
        numbers = []
        
        # 基於位置模式提取號碼
        position_patterns = pattern_analysis.get('position_patterns', {})
        for pos_key, pos_data in position_patterns.items():
            most_common = pos_data.get('most_common', [])
            if most_common:
                numbers.append(most_common[0][0])
        
        return list(set(numbers))  # 去重
    
    def _generate_final_numbers(self, prediction: Dict, lottery_type: str) -> Dict:
        """生成最終號碼組合"""
        config = self.lottery_configs[lottery_type]
        stable_numbers = prediction.get('stable_numbers', [])
        pattern_numbers = prediction.get('pattern_based_numbers', [])
        big_small_pred = prediction.get('big_small_prediction', {})
        
        # 合併穩定號碼和模式號碼
        candidate_numbers = list(set(stable_numbers + pattern_numbers))
        
        # 根據大小號預測調整
        predicted_big_count = big_small_pred.get('predicted_big_count', 3)
        threshold = config['big_small_threshold']
        
        big_candidates = [n for n in candidate_numbers if n > threshold]
        small_candidates = [n for n in candidate_numbers if n <= threshold]
        
        # 生成最終組合
        final_numbers = []
        
        # 先選擇穩定的號碼
        for num in stable_numbers[:4]:  # 最多4個穩定號碼
            if num not in final_numbers:
                final_numbers.append(num)
        
        # 根據大小號預測補充
        current_big = sum(1 for n in final_numbers if n > threshold)
        current_small = len(final_numbers) - current_big
        
        need_big = max(0, predicted_big_count - current_big)
        need_small = max(0, (config['first_area_count'] if lottery_type != 'dailycash' else config['number_count']) - len(final_numbers) - need_big)
        
        # 補充大號
        for num in big_candidates:
            if len(final_numbers) < (config['first_area_count'] if lottery_type != 'dailycash' else config['number_count']) and need_big > 0 and num not in final_numbers:
                final_numbers.append(num)
                need_big -= 1
        
        # 補充小號
        for num in small_candidates:
            if len(final_numbers) < (config['first_area_count'] if lottery_type != 'dailycash' else config['number_count']) and need_small > 0 and num not in final_numbers:
                final_numbers.append(num)
                need_small -= 1
        
        # 如果還不夠，隨機補充
        import random
        all_numbers = list(range(config['first_area_range'][0] if lottery_type != 'dailycash' else config['number_range'][0], 
                                config['first_area_range'][1] + 1 if lottery_type != 'dailycash' else config['number_range'][1] + 1))
        
        while len(final_numbers) < (config['first_area_count'] if lottery_type != 'dailycash' else config['number_count']):
            available = [n for n in all_numbers if n not in final_numbers]
            if available:
                final_numbers.append(random.choice(available))
            else:
                break
        
        return {
            'numbers': sorted(final_numbers),
            'big_count': sum(1 for n in final_numbers if n > threshold),
            'small_count': sum(1 for n in final_numbers if n <= threshold),
            'confidence': 0.75
        }
    
    def _select_recommended_numbers(self, analysis: Dict, confidence_threshold: float) -> List[int]:
        """選擇推薦號碼"""
        stable_candidates = analysis.get('stable_candidates', [])
        
        # 選擇穩定性分數高的號碼
        recommended = []
        for candidate in stable_candidates:
            if candidate['stability_score'] >= confidence_threshold and len(recommended) < 4:
                recommended.append(candidate['number'])
        
        return recommended