"""
彩票預測系統 - 預測模組
負責整合多種預測方法，產生預測結果，支援威力彩、大樂透和今彩539
"""

import os
import numpy as np
import pandas as pd
import pickle
import logging
import json
from datetime import datetime
import traceback
from .enhanced_number_analyzer import EnhancedNumberAnalyzer
# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/predictor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('predictor')

class LotteryPredictor:
    def __init__(self, model_dir='models', version='v1.0'):
        """初始化預測器
        
        Args:
            model_dir: 模型目錄
            version: 模型版本
        """
        self.model_dir = model_dir
        self.version = version
        self.models = {
            'powercolor': {
                'first_area_models': [],
                'second_area_model': None
            },
            'lotto649': {
                'first_area_models': [],
                'special_number_model': None
            },
            'dailycash': {
                'number_models': []
            }
        }
        self.scalers = {}
        self.board_path_analyzer = None
        self.enhanced_analyzer = EnhancedNumberAnalyzer()  # 增強分析器
        self.actual_numbers_cache = {}  # 實際號碼緩存
    
    def set_board_path_analyzer(self, analyzer):
        """設置板路分析器
        
        Args:
            analyzer: BoardPathAnalyzer實例
        """
        self.board_path_analyzer = analyzer
    
    def load_models(self, lottery_type, version=None):
        """加載模型
        
        Args:
            lottery_type: 彩票類型 ('powercolor'=威力彩, 'lotto649'=大樂透, 'dailycash'=今彩539)
            version: 模型版本，如果為None則使用初始化時設置的版本
            
        Returns:
            bool: 是否成功加載模型
        """
        if version is None:
            version = self.version
            
        try:
            # 根據彩票類型加載模型
            if lottery_type.lower() == 'powercolor':
                # 清空當前模型
                self.models['powercolor']['first_area_models'] = []
                
                # 載入6個第一區模型（至少需要6個）
                loaded_models = 0
                for i in range(1, 7):
                    model_path = os.path.join(self.model_dir, f'powercolor_first_area_model_{i}_{version}.pkl')
                    if os.path.exists(model_path):
                        with open(model_path, 'rb') as f:
                            model = pickle.load(f)
                            self.models['powercolor']['first_area_models'].append(model)
                            loaded_models += 1
                    else:
                        logger.warning(f"威力彩第一區模型 {i} 文件不存在: {model_path}")
                
                # 檢查是否載入了足夠的模型（至少需要3個才能進行預測）
                if loaded_models < 3:
                    logger.error(f"威力彩第一區模型載入不足，需要至少3個，實際載入{loaded_models}個")
                    return False
                elif loaded_models < 6:
                    logger.warning(f"威力彩第一區模型載入不完整，建議6個，實際載入{loaded_models}個，將使用現有模型進行預測")
                
                # 加載第二區模型
                second_area_path = os.path.join(self.model_dir, f'powercolor_second_area_model_{version}.pkl')
                if not os.path.exists(second_area_path):
                    logger.error(f"威力彩第二區模型文件不存在: {second_area_path}")
                    return False
                with open(second_area_path, 'rb') as f:
                    self.models['powercolor']['second_area_model'] = pickle.load(f)
                
                # 加載標準化器
                scaler_path = os.path.join(self.model_dir, f'powercolor_scaler_{version}.pkl')
                if not os.path.exists(scaler_path):
                    logger.error(f"威力彩標準化器文件不存在: {scaler_path}")
                    return False
                with open(scaler_path, 'rb') as f:
                    self.scalers['powercolor'] = pickle.load(f)
                
            elif lottery_type.lower() == 'lotto649':
                # 清空當前模型
                self.models['lotto649']['first_area_models'] = []
                
                # 載入6個第一區模型（至少需要6個）
                loaded_models = 0
                for i in range(1, 7):
                    model_path = os.path.join(self.model_dir, f'lotto649_first_area_model_{i}_{version}.pkl')
                    if os.path.exists(model_path):
                        with open(model_path, 'rb') as f:
                            model = pickle.load(f)
                            self.models['lotto649']['first_area_models'].append(model)
                            loaded_models += 1
                    else:
                        logger.warning(f"大樂透第一區模型 {i} 文件不存在: {model_path}")
                
                # 檢查是否載入了足夠的模型（至少需要3個才能進行預測）
                if loaded_models < 3:
                    logger.error(f"大樂透第一區模型載入不足，需要至少3個，實際載入{loaded_models}個")
                    return False
                elif loaded_models < 6:
                    logger.warning(f"大樂透第一區模型載入不完整，建議6個，實際載入{loaded_models}個，將使用現有模型進行預測")
                
                # 加載特別號模型
                special_path = os.path.join(self.model_dir, f'lotto649_special_number_model_{version}.pkl')
                if not os.path.exists(special_path):
                    logger.error(f"大樂透特別號模型文件不存在: {special_path}")
                    return False
                with open(special_path, 'rb') as f:
                    self.models['lotto649']['special_number_model'] = pickle.load(f)
                
                # 加載標準化器
                scaler_path = os.path.join(self.model_dir, f'lotto649_scaler_{version}.pkl')
                if not os.path.exists(scaler_path):
                    logger.error(f"大樂透標準化器文件不存在: {scaler_path}")
                    return False
                with open(scaler_path, 'rb') as f:
                    self.scalers['lotto649'] = pickle.load(f)
                
            elif lottery_type.lower() == 'dailycash':
                # 清空當前模型
                self.models['dailycash']['number_models'] = []
                
                # 載入5個號碼模型（至少需要5個）
                loaded_models = 0
                for i in range(1, 6):
                    model_path = os.path.join(self.model_dir, f'dailycash_number_model_{i}_{version}.pkl')
                    if os.path.exists(model_path):
                        with open(model_path, 'rb') as f:
                            model = pickle.load(f)
                            self.models['dailycash']['number_models'].append(model)
                            loaded_models += 1
                    else:
                        logger.warning(f"今彩539號碼模型 {i} 文件不存在: {model_path}")
                
                # 檢查是否載入了足夠的模型（至少需要3個才能進行預測）
                if loaded_models < 3:
                    logger.error(f"今彩539號碼模型載入不足，需要至少3個，實際載入{loaded_models}個")
                    return False
                elif loaded_models < 5:
                    logger.warning(f"今彩539號碼模型載入不完整，建議5個，實際載入{loaded_models}個，將使用現有模型進行預測")
                
                # 加載標準化器
                scaler_path = os.path.join(self.model_dir, f'dailycash_scaler_{version}.pkl')
                if not os.path.exists(scaler_path):
                    logger.error(f"今彩539標準化器文件不存在: {scaler_path}")
                    return False
                with open(scaler_path, 'rb') as f:
                    self.scalers['dailycash'] = pickle.load(f)
            
            logger.info(f"已加載{self._get_lottery_name(lottery_type)}模型 (版本: {version})")
            return True
        
        except FileNotFoundError:
            logger.warning(f"找不到{self._get_lottery_name(lottery_type)}模型文件 (版本: {version})")
            return False
        
        except Exception as e:
            logger.error(f"加載{self._get_lottery_name(lottery_type)}模型時出錯: {str(e)}")
            return False
    
    def predict(self, df, features, lottery_type='powercolor', prediction_methods=['ml'], ensemble=False, model_version=None, use_enhanced_analysis=True):
        """使用多種方法預測下一期號碼
        
        Args:
            df: 歷史數據DataFrame
            features: 預測特徵
            lottery_type: 彩票類型 ('powercolor'=威力彩, 'lotto649'=大樂透, 'dailycash'=今彩539)
            prediction_methods: 預測方法列表 ['ml', 'board_path', 'enhanced']
            ensemble: 是否進行集成預測
            model_version: 模型版本，如果為None則使用初始化時設置的版本
            use_enhanced_analysis: 是否使用增強分析
            
        Returns:
            dict: 預測結果
        """
        logger.info(f"開始預測{self._get_lottery_name(lottery_type)}下一期號碼...")
        
        results = {}
        predictions = {}
        enhanced_analysis = None
        
        # 執行增強分析（如果啟用）
        if use_enhanced_analysis or 'enhanced' in prediction_methods:
            try:
                enhanced_analysis = self.enhanced_analyzer.generate_enhanced_prediction(df, lottery_type)
                if enhanced_analysis:
                    predictions['enhanced'] = enhanced_analysis.get('final_numbers', {})
                    logger.info(f"增強分析預測結果: {predictions['enhanced']}")
            except Exception as e:
                logger.warning(f"增強分析失敗: {str(e)}")
        
        # 如果包含機器學習方法
        if 'ml' in prediction_methods:
            # 加載模型
            if not self.load_models(lottery_type, model_version):
                logger.warning(f"加載{self._get_lottery_name(lottery_type)}模型失敗，無法使用機器學習方法預測")
            else:
                # 使用機器學習模型預測（結合增強分析）
                ml_prediction = self._predict_with_ml(features, lottery_type, enhanced_analysis)
                
                if ml_prediction:
                    predictions['ml'] = ml_prediction
                    logger.info(f"機器學習預測結果: {ml_prediction}")
        
        # 如果包含板路分析方法（僅威力彩支援）
        if 'board_path' in prediction_methods and lottery_type == 'powercolor':
            if self.board_path_analyzer is None:
                logger.warning("板路分析器未設置，無法使用板路分析方法預測")
            else:
                # 使用板路分析預測
                board_path_prediction = self.board_path_analyzer.predict_next_numbers(df)
                
                if board_path_prediction:
                    predictions['board_path'] = board_path_prediction
                    logger.info(f"板路分析預測結果: {board_path_prediction}")
        
        # 如果沒有任何預測結果
        if not predictions:
            logger.error("所有預測方法均失敗，無法生成預測結果")
            return None
        
        # 如果只有一種預測方法或不進行集成
        if len(predictions) == 1 or not ensemble:
            method = list(predictions.keys())[0]
            results = predictions[method]
            logger.info(f"使用 {method} 方法作為最終預測結果")
        else:
            # 進行集成預測
            if lottery_type == 'powercolor':
                results = self._ensemble_powercolor_predictions(predictions)
            elif lottery_type == 'lotto649':
                results = self._ensemble_lotto649_predictions(predictions)
            elif lottery_type == 'dailycash':
                results = self._ensemble_dailycash_predictions(predictions)
                
            logger.info(f"集成預測結果: {results}")
        
        # 添加預測方法和模型版本信息
        if isinstance(results, dict):
            results['預測方法'] = '+'.join(predictions.keys())
            results['模型版本'] = model_version or self.version
            
            # 添加增強分析信息
            if enhanced_analysis:
                results['增強分析'] = {
                    '大小號預測': enhanced_analysis.get('big_small_prediction', {}),
                    '穩定號碼': enhanced_analysis.get('stable_numbers', []),
                    '信心分數': enhanced_analysis.get('confidence_scores', {})
                }
        
        logger.info(f"{self._get_lottery_name(lottery_type)}下一期號碼預測完成")
        return results
    
    def _predict_with_ml(self, features, lottery_type, enhanced_analysis=None):
        """使用機器學習模型預測
        
        Args:
            features: 預測特徵
            lottery_type: 彩票類型
            enhanced_analysis: 增強分析結果
            
        Returns:
            dict: 預測結果
        """
        try:
            # 檢查模型是否已加載
            if lottery_type not in self.models or not self.models[lottery_type]:
                logger.error(f"{lottery_type} 模型未加載")
                return None
                
            # 檢查標準化器是否已加載
            if lottery_type not in self.scalers:
                logger.error(f"{lottery_type} 標準化器未加載")
                return None
                
            # 標準化特徵
            X = self.scalers[lottery_type].transform(features)
            
            if lottery_type == 'powercolor':
                # 檢查第一區模型是否存在
                if not self.models['powercolor']['first_area_models'] or len(self.models['powercolor']['first_area_models']) < 3:
                    logger.error(f"威力彩第一區模型不足，需要至少3個，當前有{len(self.models['powercolor']['first_area_models']) if self.models['powercolor']['first_area_models'] else 0}個")
                    return None
                    
                # 檢查第二區模型是否存在
                if not self.models['powercolor']['second_area_model']:
                    logger.error("威力彩第二區模型未加載")
                    return None
                
                # 預測第一區
                first_area_preds = []
                
                # 為每個模型獲取預測值
                for i, model in enumerate(self.models['powercolor']['first_area_models']):
                    pred = model.predict(X)[0]
                    first_area_preds.append((i + 1, pred))  # (號碼, 預測值)
                
                # 對預測值進行排序
                sorted_preds = sorted(first_area_preds, key=lambda x: x[1], reverse=True)
                
                # 取前6個不同的號碼
                first_area_numbers = []
                for num, _ in sorted_preds:
                    if num not in first_area_numbers and num <= 38:  # 確保號碼在有效範圍內
                        first_area_numbers.append(num)
                        if len(first_area_numbers) == 6:
                            break
                
                # 如果不足6個，補充
                while len(first_area_numbers) < 6:
                    for num in range(1, 39):
                        if num not in first_area_numbers:
                            first_area_numbers.append(num)
                            break
                
                # 預測第二區
                second_area_pred = self.models['powercolor']['second_area_model'].predict(X)[0]
                second_area_number = round(second_area_pred)
                
                # 確保號碼在有效範圍內
                second_area_number = max(1, min(8, second_area_number))
                
                # 按照順序排序第一區號碼
                first_area_numbers.sort()
                
                return {
                    '第一區': first_area_numbers,
                    '第二區': second_area_number
                }
            
            elif lottery_type == 'lotto649':
                # 檢查第一區模型是否存在
                if not self.models['lotto649']['first_area_models'] or len(self.models['lotto649']['first_area_models']) < 3:
                    logger.error(f"大樂透第一區模型不足，需要至少3個，當前有{len(self.models['lotto649']['first_area_models']) if self.models['lotto649']['first_area_models'] else 0}個")
                    return None
                
                # 檢查特別號模型是否存在（如果沒有特別號模型，使用隨機生成）
                if not self.models['lotto649']['special_model']:
                    logger.warning("大樂透特別號模型未載入，將使用隨機生成")
                
                # 預測第一區
                first_area_preds = []
                
                # 為每個模型獲取預測值
                for i, model in enumerate(self.models['lotto649']['first_area_models']):
                    pred = model.predict(X)[0]
                    first_area_preds.append((i + 1, pred))  # (號碼, 預測值)
                
                # 對預測值進行排序
                sorted_preds = sorted(first_area_preds, key=lambda x: x[1], reverse=True)
                
                # 取前6個不同的號碼
                first_area_numbers = []
                for num, _ in sorted_preds:
                    if num not in first_area_numbers and num <= 49:  # 確保號碼在有效範圍內
                        first_area_numbers.append(num)
                        if len(first_area_numbers) == 6:
                            break
                
                # 如果不足6個，補充
                while len(first_area_numbers) < 6:
                    for num in range(1, 50):
                        if num not in first_area_numbers:
                            first_area_numbers.append(num)
                            break
                
                # 預測特別號
                if self.models['lotto649']['special_model']:
                    special_pred = self.models['lotto649']['special_model'].predict(X)[0]
                    special_number = round(special_pred)
                else:
                    # 如果特別號模型不存在，使用隨機生成
                    import random
                    available_numbers = [i for i in range(1, 50) if i not in first_area_numbers]
                    special_number = random.choice(available_numbers) if available_numbers else random.randint(1, 49)
                
                # 確保號碼在有效範圍內且不在第一區中
                while special_number in first_area_numbers or special_number < 1 or special_number > 49:
                    if special_number < 1:
                        special_number = 1
                    elif special_number > 49:
                        special_number = 49
                    else:
                        special_number = (special_number % 49) + 1
                
                # 按照順序排序第一區號碼
                first_area_numbers.sort()
                
                return {
                    '第一區': first_area_numbers,
                    '特別號': special_number
                }
            
            elif lottery_type == 'dailycash':
                # 檢查號碼模型是否存在
                if not self.models['dailycash']['number_models'] or len(self.models['dailycash']['number_models']) < 3:
                    logger.error(f"今彩539號碼模型不足，需要至少3個，當前有{len(self.models['dailycash']['number_models']) if self.models['dailycash']['number_models'] else 0}個")
                    return None
                
                # 預測號碼
                number_preds = []
                
                # 為每個模型獲取預測值
                for i, model in enumerate(self.models['dailycash']['number_models']):
                    pred = model.predict(X)[0]
                    number_preds.append((i + 1, pred))  # (號碼, 預測值)
                
                # 對預測值進行排序
                sorted_preds = sorted(number_preds, key=lambda x: x[1], reverse=True)
                
                # 取前5個不同的號碼
                numbers = []
                for num, _ in sorted_preds:
                    if num not in numbers and num <= 39:  # 確保號碼在有效範圍內
                        numbers.append(num)
                        if len(numbers) == 5:
                            break
                
                # 如果不足5個，補充
                while len(numbers) < 5:
                    for num in range(1, 40):
                        if num not in numbers:
                            numbers.append(num)
                            break
                
                # 按照順序排序號碼
                numbers.sort()
                
                return {
                    '號碼': numbers
                }
            
            else:
                logger.error(f"不支援的彩票類型: {lottery_type}")
                return None
        
        except Exception as e:
            logger.error(f"使用機器學習模型預測時出錯: {str(e)}")
            return None
    
    def _ensemble_powercolor_predictions(self, predictions):
        """集成威力彩預測結果
        
        Args:
            predictions: 不同方法的預測結果
            
        Returns:
            dict: 集成的預測結果
        """
        # 收集所有方法的第一區預測
        first_area_numbers = {}
        for method, pred in predictions.items():
            for num in pred['第一區']:
                if num not in first_area_numbers:
                    first_area_numbers[num] = 0
                first_area_numbers[num] += 1
        
        # 選擇出現頻率最高的6個號碼
        selected_first_area = []
        sorted_nums = sorted(first_area_numbers.items(), key=lambda x: (-x[1], x[0]))
        
        for num, count in sorted_nums:
            if len(selected_first_area) < 6:
                selected_first_area.append(num)
        
        # 如果不足6個，補充
        while len(selected_first_area) < 6:
            for num in range(1, 39):
                if num not in selected_first_area:
                    selected_first_area.append(num)
                    break
        
        # 收集所有方法的第二區預測
        second_area_numbers = {}
        for method, pred in predictions.items():
            num = pred['第二區']
            if num not in second_area_numbers:
                second_area_numbers[num] = 0
            second_area_numbers[num] += 1
        
        # 選擇出現頻率最高的第二區號碼
        selected_second_area = max(second_area_numbers.items(), key=lambda x: x[1])[0]
        
        # 按照順序排序第一區號碼
        selected_first_area.sort()
        
        # 集成解釋
        explanations = {}
        for method, pred in predictions.items():
            if '解釋' in pred:
                for exp_type, exps in pred['解釋'].items():
                    if exp_type not in explanations:
                        explanations[exp_type] = []
                    explanations[exp_type].extend(exps)
        
        return {
            '第一區': selected_first_area,
            '第二區': selected_second_area,
            '解釋': explanations
        }
    
    def _ensemble_lotto649_predictions(self, predictions):
        """集成大樂透預測結果
        
        Args:
            predictions: 不同方法的預測結果
            
        Returns:
            dict: 集成的預測結果
        """
        # 收集所有方法的第一區預測
        first_area_numbers = {}
        for method, pred in predictions.items():
            for num in pred['第一區']:
                if num not in first_area_numbers:
                    first_area_numbers[num] = 0
                first_area_numbers[num] += 1
        
        # 選擇出現頻率最高的6個號碼
        selected_first_area = []
        sorted_nums = sorted(first_area_numbers.items(), key=lambda x: (-x[1], x[0]))
        
        for num, count in sorted_nums:
            if len(selected_first_area) < 6:
                selected_first_area.append(num)
        
        # 如果不足6個，補充
        while len(selected_first_area) < 6:
            for num in range(1, 50):
                if num not in selected_first_area:
                    selected_first_area.append(num)
                    break
        
        # 收集所有方法的特別號預測
        special_numbers = {}
        for method, pred in predictions.items():
            num = pred['特別號']
            if num not in special_numbers:
                special_numbers[num] = 0
            special_numbers[num] += 1
        
        # 選擇出現頻率最高的特別號
        selected_special = max(special_numbers.items(), key=lambda x: x[1])[0]
        
        # 確保特別號不在第一區中
        while selected_special in selected_first_area:
            selected_special = (selected_special % 49) + 1
        
        # 按照順序排序第一區號碼
        selected_first_area.sort()
        
        return {
            '第一區': selected_first_area,
            '特別號': selected_special
        }
    
    def _ensemble_dailycash_predictions(self, predictions):
        """集成今彩539預測結果
        
        Args:
            predictions: 不同方法的預測結果
            
        Returns:
            dict: 集成的預測結果
        """
        # 收集所有方法的號碼預測
        all_numbers = {}
        for method, pred in predictions.items():
            for num in pred['號碼']:
                if num not in all_numbers:
                    all_numbers[num] = 0
                all_numbers[num] += 1
        
        # 選擇出現頻率最高的5個號碼
        selected_numbers = []
        sorted_nums = sorted(all_numbers.items(), key=lambda x: (-x[1], x[0]))
        
        for num, count in sorted_nums:
            if len(selected_numbers) < 5:
                selected_numbers.append(num)
        
        # 如果不足5個，補充
        while len(selected_numbers) < 5:
            for num in range(1, 40):
                if num not in selected_numbers:
                    selected_numbers.append(num)
                    break
        
        # 按照順序排序號碼
        selected_numbers.sort()
        
        return {
            '號碼': selected_numbers
        }
    
    def _validate_numbers(self, numbers, lottery_type):
        """驗證號碼有效性

        Args:
            numbers: 號碼列表
            lottery_type: 彩票類型

        Returns:
            bool: 是否有效
        """
        if not numbers or len(numbers) == 0:
            return False

        # 檢查是否有重複號碼
        if len(set(numbers)) != len(numbers):
            return False

        # 檢查是否有無效號碼（0或負數）
        if any(num <= 0 for num in numbers):
            return False

        # 根據彩票類型檢查號碼範圍和數量
        if lottery_type == 'powercolor':
            # 威力彩：第一區1-38，需要6個號碼
            return (len(numbers) == 6 and
                   all(1 <= num <= 38 for num in numbers))
        elif lottery_type == 'lotto649':
            # 大樂透：第一區1-49，需要6個號碼
            return (len(numbers) == 6 and
                   all(1 <= num <= 49 for num in numbers))
        elif lottery_type == 'dailycash':
            # 今彩539：1-39，需要5個號碼
            return (len(numbers) == 5 and
                   all(1 <= num <= 39 for num in numbers))

        return False

    def predict_with_candidates(self, df, features, lottery_type='powercolor', prediction_methods=['ml'], ensemble=False, model_version=None, generation_options=None):
        """使用多種方法預測下一期號碼，支援多候選結果生成
        
        Args:
            df: 歷史數據DataFrame
            features: 預測特徵
            lottery_type: 彩票類型 ('powercolor'=威力彩, 'lotto649'=大樂透, 'dailycash'=今彩539)
            prediction_methods: 預測方法列表 ['ml', 'board_path']
            ensemble: 是否進行集成預測
            model_version: 模型版本
            generation_options: 多候選生成選項字典
            
        Returns:
            PredictionResult: 多候選預測結果物件
        """
        logger.info(f"開始多候選預測{self._get_lottery_name(lottery_type)}下一期號碼...")
        
        # 默認生成選項
        if generation_options is None:
            generation_options = {
                'candidates_count': 5,
                'stabilize_results': True,
                'use_enhanced_analysis': True,
                'min_confidence': 0.5
            }
        
        # 建立預測結果物件
        from prediction.prediction_result import PredictionResult
        from prediction.result_narrower import ResultNarrower
        
        # 進行各種方法的預測
        prediction_results = []
        
        # 載入模型（如果未指定版本則使用初始化時設置的版本）
        if model_version is None:
            model_version = self.version
            
        # 如果包含機器學習方法
        if 'ml' in prediction_methods:
            # 加載模型
            if not self.load_models(lottery_type, model_version):
                logger.warning(f"加載{self._get_lottery_name(lottery_type)}模型失敗，無法使用機器學習方法預測")
            else:
                # 使用機器學習模型進行多候選預測
                ml_result = self._predict_with_ml_candidates(
                    features, 
                    lottery_type, 
                    candidates_count=generation_options['candidates_count'],
                    min_confidence=generation_options['min_confidence']
                )
                
                if ml_result:
                    prediction_results.append(ml_result)
                    logger.info(f"機器學習預測完成，生成 {len(ml_result.candidates)} 個候選")
        
        # 如果包含板路分析方法
        if 'board_path' in prediction_methods:
            if self.board_path_analyzer is None:
                logger.warning("板路分析器未設置，無法使用板路分析方法預測")
            else:
                # 使用板路分析進行多候選預測
                # 設置板路分析器彩種類型
                analyzer_type_map = {
                    'powercolor': 'powerball',
                    'lotto649': 'lotto', 
                    'dailycash': '539'
                }
                analyzer_type = analyzer_type_map.get(lottery_type, 'powerball')
                
                # 更新板路分析器設置
                if hasattr(self.board_path_analyzer, 'set_lottery_type'):
                    self.board_path_analyzer.set_lottery_type(analyzer_type)
                
                # 執行預測
                board_path_result = self.board_path_analyzer.predict(df, generation_options)
                
                if board_path_result:
                    prediction_results.append(board_path_result)
                    logger.info(f"板路分析預測完成，生成 {len(board_path_result.candidates)} 個候選")
        
        # 如果沒有任何預測結果
        if not prediction_results:
            logger.error("所有預測方法均失敗，無法生成預測結果")
            return None
        
        # 如果只有一種預測方法或不進行集成
        if len(prediction_results) == 1 or not ensemble:
            final_result = prediction_results[0]
            logger.info(f"使用單一方法作為最終預測結果，候選數量: {len(final_result.candidates)}")
        else:
            # 進行集成預測
            narrower = ResultNarrower()
            final_result = narrower.narrow_by_ensemble(prediction_results)
            logger.info(f"完成集成預測，候選數量: {len(final_result.candidates)}")
        
        logger.info(f"{self._get_lottery_name(lottery_type)}下一期號碼多候選預測完成")
        return final_result
        
    def _predict_with_ml_candidates(self, features, lottery_type, candidates_count=5, min_confidence=0.5):
        """使用機器學習模型進行多候選預測
        
        Args:
            features: 預測特徵
            lottery_type: 彩票類型
            candidates_count: 候選數量
            min_confidence: 最低信心分數
            
        Returns:
            PredictionResult: 預測結果物件
        """
        from prediction.prediction_result import PredictionResult
        import numpy as np
        
        try:
            # 標準化特徵
            X = self.scalers[lottery_type].transform(features)
            
            # 建立預測結果物件
            result = PredictionResult(
                lottery_type=lottery_type,
                method="ml",
                version=self.version
            )
            
            if lottery_type == 'powercolor':
                # 獲取每個模型對各個號碼的預測分數
                number_scores = {}
                
                # 計算1-38號碼的預測分數
                for num in range(1, 39):
                    # 初始化該號碼的分數
                    number_scores[num] = 0
                    
                    # 累計來自各個模型的分數
                    for i, model in enumerate(self.models['powercolor']['first_area_models']):
                        pred = model.predict(X)[0]
                        
                        # 如果是當前模型負責的號碼，增加分數
                        if i+1 == num:
                            number_scores[num] += pred * 2  # 加權
                        else:
                            # 計算與預測值的接近程度
                            similarity = 1.0 / (1.0 + abs(i+1 - num))
                            number_scores[num] += pred * similarity
                
                # 設置固定種子確保結果穩定
                np.random.seed(42)

                # 生成多個候選組合
                for i in range(candidates_count):
                    # 使用確定性方法生成不同候選，而非隨機擾動
                    # 根據候選索引調整分數權重
                    weight_factor = 1.0 - (i * 0.1)  # 第一個候選權重最高
                    adjusted_scores = {num: score * weight_factor + (i * 0.01 * (num % 10)) for num, score in number_scores.items()}

                    # 從分數最高的號碼中選擇6個
                    selected_numbers = sorted(adjusted_scores.items(), key=lambda x: x[1], reverse=True)[:6]
                    selected_main = [num for num, _ in selected_numbers]

                    # 驗證號碼有效性
                    if not self._validate_numbers(selected_main, lottery_type):
                        logger.warning(f"候選 {i+1} 號碼無效，跳過: {selected_main}")
                        continue

                    # 計算第二區號碼
                    second_area_pred = self.models['powercolor']['second_area_model'].predict(X)[0]
                    second_area_number = round(second_area_pred)

                    # 確保號碼在有效範圍內
                    second_area_number = max(1, min(8, second_area_number))

                    # 排序號碼
                    selected_main.sort()

                    # 計算信心分數 (基於相對分數高低)
                    avg_score = sum(score for _, score in selected_numbers) / 6
                    max_possible_score = max(number_scores.values()) if number_scores.values() else 1.0
                    confidence = min(1.0, max(min_confidence, avg_score / max_possible_score)) if max_possible_score > 0 else min_confidence

                    # 添加候選結果
                    result.add_candidate(
                        main_numbers=selected_main,
                        special_number=second_area_number,
                        confidence=confidence,
                        explanation=[f"機器學習模型預測，信心分數: {confidence:.2f}", f"候選排序: {i+1}"]
                    )
                    
            elif lottery_type == 'lotto649':
                # 大樂透預測邏輯
                number_scores = {}

                # 計算1-49號碼的預測分數
                for num in range(1, 50):
                    number_scores[num] = 0

                    # 累計來自各個模型的分數
                    for i, model in enumerate(self.models['lotto649']['first_area_models']):
                        pred = model.predict(X)[0]

                        if i+1 == num:
                            number_scores[num] += pred * 2
                        else:
                            similarity = 1.0 / (1.0 + abs(i+1 - num))
                            number_scores[num] += pred * similarity

                # 設置固定種子確保結果穩定
                np.random.seed(42)

                # 生成多個候選組合
                for i in range(candidates_count):
                    weight_factor = 1.0 - (i * 0.1)
                    adjusted_scores = {num: score * weight_factor + (i * 0.01 * (num % 10)) for num, score in number_scores.items()}

                    # 選擇6個主區號碼
                    selected_numbers = sorted(adjusted_scores.items(), key=lambda x: x[1], reverse=True)[:6]
                    selected_main = [num for num, _ in selected_numbers]

                    # 驗證號碼有效性
                    if not self._validate_numbers(selected_main, lottery_type):
                        logger.warning(f"大樂透候選 {i+1} 號碼無效，跳過: {selected_main}")
                        continue

                    # 計算特別號
                    special_pred = self.models['lotto649']['special_model'].predict(X)[0]
                    special_number = round(special_pred)
                    special_number = max(1, min(49, special_number))

                    # 確保特別號不在主區中
                    while special_number in selected_main:
                        special_number = (special_number % 49) + 1

                    selected_main.sort()

                    # 計算信心分數
                    avg_score = sum(score for _, score in selected_numbers) / 6
                    max_possible_score = max(number_scores.values()) if number_scores.values() else 1.0
                    confidence = min(1.0, max(min_confidence, avg_score / max_possible_score)) if max_possible_score > 0 else min_confidence

                    result.add_candidate(
                        main_numbers=selected_main,
                        special_number=special_number,
                        confidence=confidence,
                        explanation=[f"大樂透機器學習預測，信心分數: {confidence:.2f}", f"候選排序: {i+1}"]
                    )

            elif lottery_type == 'dailycash':
                # 今彩539預測邏輯
                number_scores = {}

                # 計算1-39號碼的預測分數
                for num in range(1, 40):
                    number_scores[num] = 0

                    # 累計來自各個模型的分數
                    for i, model in enumerate(self.models['dailycash']['number_models']):
                        pred = model.predict(X)[0]

                        if i+1 == num:
                            number_scores[num] += pred * 2
                        else:
                            similarity = 1.0 / (1.0 + abs(i+1 - num))
                            number_scores[num] += pred * similarity

                # 設置固定種子確保結果穩定
                np.random.seed(42)

                # 生成多個候選組合
                for i in range(candidates_count):
                    weight_factor = 1.0 - (i * 0.1)
                    adjusted_scores = {num: score * weight_factor + (i * 0.01 * (num % 10)) for num, score in number_scores.items()}

                    # 選擇5個號碼
                    selected_numbers = sorted(adjusted_scores.items(), key=lambda x: x[1], reverse=True)[:5]
                    selected_main = [num for num, _ in selected_numbers]

                    # 驗證號碼有效性
                    if not self._validate_numbers(selected_main, lottery_type):
                        logger.warning(f"今彩539候選 {i+1} 號碼無效，跳過: {selected_main}")
                        continue

                    selected_main.sort()

                    # 計算信心分數
                    avg_score = sum(score for _, score in selected_numbers) / 5
                    max_possible_score = max(number_scores.values()) if number_scores.values() else 1.0
                    confidence = min(1.0, max(min_confidence, avg_score / max_possible_score)) if max_possible_score > 0 else min_confidence

                    result.add_candidate(
                        main_numbers=selected_main,
                        confidence=confidence,
                        explanation=[f"今彩539機器學習預測，信心分數: {confidence:.2f}", f"候選排序: {i+1}"]
                    )
            
            return result
        
        except Exception as e:
            logger.error(f"使用機器學習模型進行多候選預測時出錯: {str(e)}")
            logger.error(traceback.format_exc())
            return None
    
    def _get_lottery_name(self, lottery_type):
        """根據彩票類型獲取中文名稱"""
        name_map = {
            'powercolor': '威力彩',
            'lotto649': '大樂透',
            'dailycash': '今彩539'
        }
        return name_map.get(lottery_type.lower(), lottery_type)
    
    def update_actual_numbers(self, lottery_type, period, actual_numbers):
        """更新實際開獎號碼
        
        Args:
            lottery_type: 彩票類型
            period: 期數
            actual_numbers: 實際號碼字典
        """
        try:
            # 將實際號碼存入緩存
            cache_key = f"{lottery_type}_{period}"
            self.actual_numbers_cache[cache_key] = {
                'period': period,
                'numbers': actual_numbers,
                'update_time': datetime.now().isoformat()
            }
            
            logger.info(f"已更新{self._get_lottery_name(lottery_type)}第{period}期實際號碼: {actual_numbers}")
            
            # 可以在這裡添加數據庫更新邏輯
            # self._save_actual_numbers_to_db(lottery_type, period, actual_numbers)
            
        except Exception as e:
            logger.error(f"更新實際號碼時出錯: {str(e)}")
    
    def get_prediction_accuracy(self, lottery_type, period):
        """計算預測準確度
        
        Args:
            lottery_type: 彩票類型
            period: 期數
            
        Returns:
            dict: 準確度分析結果
        """
        try:
            cache_key = f"{lottery_type}_{period}"
            
            if cache_key not in self.actual_numbers_cache:
                logger.warning(f"找不到{self._get_lottery_name(lottery_type)}第{period}期的實際號碼")
                return None
            
            actual_data = self.actual_numbers_cache[cache_key]
            actual_numbers = actual_data['numbers']
            
            # 這裡可以添加與預測結果的比較邏輯
            # 目前返回基本信息
            return {
                'period': period,
                'lottery_type': lottery_type,
                'actual_numbers': actual_numbers,
                'update_time': actual_data['update_time']
            }
            
        except Exception as e:
            logger.error(f"計算預測準確度時出錯: {str(e)}")
            return None
    
    def predict_with_enhanced_analysis(self, df, features, lottery_type='powercolor', model_version=None):
        """使用增強分析進行預測
        
        Args:
            df: 歷史數據DataFrame
            features: 預測特徵
            lottery_type: 彩票類型
            model_version: 模型版本
            
        Returns:
            dict: 增強預測結果
        """
        try:
            logger.info(f"開始{self._get_lottery_name(lottery_type)}增強分析預測...")
            
            # 執行增強分析
            enhanced_result = self.enhanced_analyzer.generate_enhanced_prediction(df, lottery_type)
            
            if not enhanced_result:
                logger.warning("增強分析失敗，使用標準預測方法")
                return self.predict(df, features, lottery_type, ['ml'], False, model_version, False)
            
            # 結合機器學習預測
            ml_result = None
            if self.load_models(lottery_type, model_version):
                ml_result = self._predict_with_ml(features, lottery_type, enhanced_result)
            
            # 整合結果
            final_result = {
                '增強分析結果': enhanced_result,
                '推薦號碼': enhanced_result.get('final_numbers', {}),
                '分析摘要': {
                    '大小號分析': enhanced_result.get('big_small_prediction', {}),
                    '穩定號碼': enhanced_result.get('stable_numbers', []),
                    '模式分析': enhanced_result.get('pattern_based_numbers', [])
                }
            }
            
            # 如果有機器學習結果，添加到最終結果中
            if ml_result:
                final_result['機器學習結果'] = ml_result
                
                # 融合兩種方法的結果
                final_result['融合預測'] = self._merge_predictions(
                    enhanced_result.get('final_numbers', {}),
                    ml_result,
                    lottery_type
                )
            
            logger.info(f"{self._get_lottery_name(lottery_type)}增強分析預測完成")
            return final_result
            
        except Exception as e:
            logger.error(f"增強分析預測時出錯: {str(e)}")
            return None
    
    def _merge_predictions(self, enhanced_numbers, ml_numbers, lottery_type):
        """融合增強分析和機器學習的預測結果
        
        Args:
            enhanced_numbers: 增強分析號碼
            ml_numbers: 機器學習號碼
            lottery_type: 彩票類型
            
        Returns:
            dict: 融合後的預測結果
        """
        try:
            if lottery_type == 'powercolor':
                # 威力彩融合邏輯
                enhanced_first = enhanced_numbers.get('numbers', [])
                ml_first = ml_numbers.get('第一區', [])
                
                # 合併第一區號碼，優先選擇出現在兩個預測中的號碼
                common_numbers = list(set(enhanced_first) & set(ml_first))
                all_numbers = list(set(enhanced_first + ml_first))
                
                # 選擇最終的6個號碼
                final_first = common_numbers[:6]
                remaining_needed = 6 - len(final_first)
                
                if remaining_needed > 0:
                    remaining_candidates = [n for n in all_numbers if n not in final_first]
                    final_first.extend(remaining_candidates[:remaining_needed])
                
                # 第二區使用機器學習結果
                final_second = ml_numbers.get('第二區', 1)
                
                return {
                    '第一區': sorted(final_first),
                    '第二區': final_second,
                    '融合信心度': 0.8 if len(common_numbers) >= 3 else 0.6
                }
                
            elif lottery_type == 'lotto649':
                # 大樂透融合邏輯（與威力彩類似）
                enhanced_first = enhanced_numbers.get('numbers', [])
                ml_first = ml_numbers.get('第一區', [])
                
                common_numbers = list(set(enhanced_first) & set(ml_first))
                all_numbers = list(set(enhanced_first + ml_first))
                
                final_first = common_numbers[:6]
                remaining_needed = 6 - len(final_first)
                
                if remaining_needed > 0:
                    remaining_candidates = [n for n in all_numbers if n not in final_first]
                    final_first.extend(remaining_candidates[:remaining_needed])
                
                # 特別號使用機器學習結果
                final_special = ml_numbers.get('特別號', 1)
                
                return {
                    '第一區': sorted(final_first),
                    '特別號': final_special,
                    '融合信心度': 0.8 if len(common_numbers) >= 3 else 0.6
                }
                
            elif lottery_type == 'dailycash':
                # 今彩539融合邏輯
                enhanced_numbers_list = enhanced_numbers.get('numbers', [])
                ml_numbers_list = ml_numbers.get('號碼', [])
                
                common_numbers = list(set(enhanced_numbers_list) & set(ml_numbers_list))
                all_numbers = list(set(enhanced_numbers_list + ml_numbers_list))
                
                final_numbers = common_numbers[:5]
                remaining_needed = 5 - len(final_numbers)
                
                if remaining_needed > 0:
                    remaining_candidates = [n for n in all_numbers if n not in final_numbers]
                    final_numbers.extend(remaining_candidates[:remaining_needed])
                
                return {
                    '號碼': sorted(final_numbers),
                    '融合信心度': 0.8 if len(common_numbers) >= 2 else 0.6
                }
            
            return {}
            
        except Exception as e:
            logger.error(f"融合預測結果時出錯: {str(e)}")
            return {}


# 測試代碼
if __name__ == "__main__":
    from data.db_manager import DBManager
    from data.feature_engineering import FeatureEngineer
    from prediction.board_path_analyzer import BoardPathAnalyzer
    from config.config_manager import ConfigManager
    
    # 加載數據
    config_manager = ConfigManager()
    db = DBManager(config_manager=config_manager)
    
    # 威力彩
    print("\n=== 測試威力彩預測 ===")
    powercolor_df = db.load_lottery_data('powercolor')
    
    # 創建特徵
    fe = FeatureEngineer()
    next_features = fe.prepare_next_period_features(powercolor_df, 'powercolor')
    
    # 創建預測器
    predictor = LotteryPredictor()
    
    # 加載板路分析器
    board_analyzer = BoardPathAnalyzer()
    board_analyzer.run_full_analysis(powercolor_df)
    predictor.set_board_path_analyzer(board_analyzer)
    
    # 預測威力彩
    prediction = predictor.predict(powercolor_df, next_features, 'powercolor', ['ml', 'board_path'], True)
    
    if prediction:
        print(f"威力彩預測結果:")
        print(f"第一區: {prediction['第一區']}")
        print(f"第二區: {prediction['第二區']}")
    
    # 大樂透
    print("\n=== 測試大樂透預測 ===")
    lotto649_df = db.load_lottery_data('lotto649')
    
    # 創建特徵
    next_features = fe.prepare_next_period_features(lotto649_df, 'lotto649')
    
    # 預測大樂透
    prediction = predictor.predict(lotto649_df, next_features, 'lotto649', ['ml'], False)
    
    if prediction:
        print(f"大樂透預測結果:")
        print(f"第一區: {prediction['第一區']}")
        print(f"特別號: {prediction['特別號']}")
    
    # 今彩539
    print("\n=== 測試今彩539預測 ===")
    dailycash_df = db.load_lottery_data('dailycash')
    
    # 創建特徵
    next_features = fe.prepare_next_period_features(dailycash_df, 'dailycash')
    
    # 預測今彩539
    prediction = predictor.predict(dailycash_df, next_features, 'dailycash', ['ml'], False)
    
    if prediction:
        print(f"今彩539預測結果:")
        print(f"號碼: {prediction['號碼']}")



class IntegratedPredictor:
    """整合預測器，結合多種方法進行彩票預測，支援多候選結果生成"""
    
    def __init__(self, model_dir='models', version='v2.0'):
        """初始化整合預測器
        
        Args:
            model_dir: 模型目錄
            version: 模型版本
        """
        self.model_dir = model_dir
        self.version = version
        self.ml_predictor = None
        self.board_path_analyzer = None
        
    def predict_with_candidates(self, df, features, lottery_type='powercolor', methods=['ml', 'board_path'], 
                               ensemble=True, period=None, generation_options=None):
        """使用多種方法預測下一期號碼，支援多候選結果生成
        
        Args:
            df: 歷史數據DataFrame
            features: 預測特徵
            lottery_type: 彩票類型 ('powercolor'=威力彩, 'lotto649'=大樂透, 'dailycash'=今彩539)
            methods: 預測方法列表 ['ml', 'board_path']
            ensemble: 是否進行集成預測
            period: 預測期數
            generation_options: 多候選生成選項字典
            
        Returns:
            PredictionResult: 多候選預測結果物件
        """
        from prediction.lottery_predictor import LotteryPredictor
        from prediction.prediction_result import PredictionResult
        from prediction.result_narrower import ResultNarrower
        import logging
        
        logger = logging.getLogger('integrated_predictor')
        logger.info(f"開始整合預測{self._get_lottery_name(lottery_type)}下一期號碼...")
        
        # 默認生成選項
        if generation_options is None:
            generation_options = {
                'candidates_count': 5,
                'stabilize_results': True,
                'use_enhanced_analysis': True,
                'min_confidence': 0.5
            }
        
        # 初始化預測結果列表
        prediction_results = []
        
        # 使用機器學習方法
        if 'ml' in methods:
            logger.info("使用機器學習方法進行預測...")
            
            # 初始化ML預測器
            if self.ml_predictor is None:
                self.ml_predictor = LotteryPredictor(model_dir=self.model_dir, version=self.version)
            
            # 加載模型（若未指定版本則使用初始化時設置的版本）
            model_version = self.version
            
            # 加載模型
            if not self.ml_predictor.load_models(lottery_type, model_version):
                logger.warning(f"加載{self._get_lottery_name(lottery_type)}模型失敗，無法使用機器學習方法預測")
            else:
                # 創建 predict_with_candidates 函數的參數
                ml_options = generation_options.copy()
                
                # 執行多候選預測
                ml_result = self.ml_predictor.predict_with_candidates(
                    df, features, 
                    lottery_type=lottery_type,
                    prediction_methods=['ml'],
                    ensemble=False,
                    model_version=model_version,
                    generation_options=ml_options
                )
                
                if ml_result:
                    prediction_results.append(ml_result)
                    logger.info(f"機器學習預測完成，生成 {len(ml_result.candidates)} 個候選")
        
        # 使用板路分析方法
        if 'board_path' in methods:
            logger.info("使用板路分析方法進行預測...")
            
            # 初始化板路分析器（如果未設置）
            if self.board_path_analyzer is None:
                # 設置板路分析器彩種類型
                analyzer_type_map = {
                    'powercolor': 'powerball',
                    'lotto649': 'lotto', 
                    'dailycash': '539'
                }
                analyzer_type = analyzer_type_map.get(lottery_type, 'powerball')
                
                # 創建板路分析器
                from prediction.board_path_engine import BoardPathEngine
                
                # 這裡我們不直接傳入db_manager，讓BoardPathEngine在內部處理資料庫連接
                self.board_path_analyzer = BoardPathEngine(None, analyzer_type, self.version)
            
            # 執行板路分析預測
            board_path_options = generation_options.copy()
            board_path_result = self.board_path_analyzer.predict(df, board_path_options)
            
            if board_path_result:
                prediction_results.append(board_path_result)
                logger.info(f"板路分析預測完成，生成 {len(board_path_result.candidates)} 個候選")
        
        # 如果沒有任何預測結果
        if not prediction_results:
            logger.error("所有預測方法均失敗，無法生成預測結果")
            return None
        
        # 如果只有一種預測方法或不進行集成
        if len(prediction_results) == 1 or not ensemble:
            final_result = prediction_results[0]
            logger.info(f"使用單一方法作為最終預測結果，候選數量: {len(final_result.candidates)}")
        else:
            # 進行集成預測
            narrower = ResultNarrower()
            final_result = narrower.narrow_by_ensemble(prediction_results)
            logger.info(f"完成集成預測，候選數量: {len(final_result.candidates)}")
        
        # 添加期數和其他元數據
        final_result.add_metadata('period', period)
        final_result.add_metadata('methods', methods)
        final_result.add_metadata('ensemble', ensemble)
        final_result.add_metadata('generation_options', generation_options)
        
        logger.info(f"{self._get_lottery_name(lottery_type)}下一期號碼多候選預測完成")
        return final_result
    
    def _get_lottery_name(self, lottery_type):
        """根據彩票類型獲取中文名稱"""
        name_map = {
            'powercolor': '威力彩',
            'lotto649': '大樂透',
            'dailycash': '今彩539'
        }
        return name_map.get(lottery_type.lower(), lottery_type)
    
    def update_actual_numbers(self, lottery_type, period, actual_numbers):
        """更新實際開獎號碼
        
        Args:
            lottery_type: 彩票類型
            period: 期數
            actual_numbers: 實際號碼字典
        """
        try:
            # 將實際號碼存入緩存
            cache_key = f"{lottery_type}_{period}"
            self.actual_numbers_cache[cache_key] = {
                'period': period,
                'numbers': actual_numbers,
                'update_time': datetime.now().isoformat()
            }
            
            logger.info(f"已更新{self._get_lottery_name(lottery_type)}第{period}期實際號碼: {actual_numbers}")
            
            # 可以在這裡添加數據庫更新邏輯
            # self._save_actual_numbers_to_db(lottery_type, period, actual_numbers)
            
        except Exception as e:
            logger.error(f"更新實際號碼時出錯: {str(e)}")
    
    def get_prediction_accuracy(self, lottery_type, period):
        """計算預測準確度
        
        Args:
            lottery_type: 彩票類型
            period: 期數
            
        Returns:
            dict: 準確度分析結果
        """
        try:
            cache_key = f"{lottery_type}_{period}"
            
            if cache_key not in self.actual_numbers_cache:
                logger.warning(f"找不到{self._get_lottery_name(lottery_type)}第{period}期的實際號碼")
                return None
            
            actual_data = self.actual_numbers_cache[cache_key]
            actual_numbers = actual_data['numbers']
            
            # 這裡可以添加與預測結果的比較邏輯
            # 目前返回基本信息
            return {
                'period': period,
                'lottery_type': lottery_type,
                'actual_numbers': actual_numbers,
                'update_time': actual_data['update_time']
            }
            
        except Exception as e:
            logger.error(f"計算預測準確度時出錯: {str(e)}")
            return None
    
    def predict_with_enhanced_analysis(self, df, features, lottery_type='powercolor', model_version=None):
        """使用增強分析進行預測
        
        Args:
            df: 歷史數據DataFrame
            features: 預測特徵
            lottery_type: 彩票類型
            model_version: 模型版本
            
        Returns:
            dict: 增強預測結果
        """
        try:
            logger.info(f"開始{self._get_lottery_name(lottery_type)}增強分析預測...")
            
            # 執行增強分析
            enhanced_result = self.enhanced_analyzer.generate_enhanced_prediction(df, lottery_type)
            
            if not enhanced_result:
                logger.warning("增強分析失敗，使用標準預測方法")
                return self.predict(df, features, lottery_type, ['ml'], False, model_version, False)
            
            # 結合機器學習預測
            ml_result = None
            if self.load_models(lottery_type, model_version):
                ml_result = self._predict_with_ml(features, lottery_type, enhanced_result)
            
            # 整合結果
            final_result = {
                '增強分析結果': enhanced_result,
                '推薦號碼': enhanced_result.get('final_numbers', {}),
                '分析摘要': {
                    '大小號分析': enhanced_result.get('big_small_prediction', {}),
                    '穩定號碼': enhanced_result.get('stable_numbers', []),
                    '模式分析': enhanced_result.get('pattern_based_numbers', [])
                }
            }
            
            # 如果有機器學習結果，添加到最終結果中
            if ml_result:
                final_result['機器學習結果'] = ml_result
                
                # 融合兩種方法的結果
                final_result['融合預測'] = self._merge_predictions(
                    enhanced_result.get('final_numbers', {}),
                    ml_result,
                    lottery_type
                )
            
            logger.info(f"{self._get_lottery_name(lottery_type)}增強分析預測完成")
            return final_result
            
        except Exception as e:
            logger.error(f"增強分析預測時出錯: {str(e)}")
            return None
    
    def _merge_predictions(self, enhanced_numbers, ml_numbers, lottery_type):
        """融合增強分析和機器學習的預測結果
        
        Args:
            enhanced_numbers: 增強分析號碼
            ml_numbers: 機器學習號碼
            lottery_type: 彩票類型
            
        Returns:
            dict: 融合後的預測結果
        """
        try:
            if lottery_type == 'powercolor':
                # 威力彩融合邏輯
                enhanced_first = enhanced_numbers.get('numbers', [])
                ml_first = ml_numbers.get('第一區', [])
                
                # 合併第一區號碼，優先選擇出現在兩個預測中的號碼
                common_numbers = list(set(enhanced_first) & set(ml_first))
                all_numbers = list(set(enhanced_first + ml_first))
                
                # 選擇最終的6個號碼
                final_first = common_numbers[:6]
                remaining_needed = 6 - len(final_first)
                
                if remaining_needed > 0:
                    remaining_candidates = [n for n in all_numbers if n not in final_first]
                    final_first.extend(remaining_candidates[:remaining_needed])
                
                # 第二區使用機器學習結果
                final_second = ml_numbers.get('第二區', 1)
                
                return {
                    '第一區': sorted(final_first),
                    '第二區': final_second,
                    '融合信心度': 0.8 if len(common_numbers) >= 3 else 0.6
                }
                
            elif lottery_type == 'lotto649':
                # 大樂透融合邏輯（與威力彩類似）
                enhanced_first = enhanced_numbers.get('numbers', [])
                ml_first = ml_numbers.get('第一區', [])
                
                common_numbers = list(set(enhanced_first) & set(ml_first))
                all_numbers = list(set(enhanced_first + ml_first))
                
                final_first = common_numbers[:6]
                remaining_needed = 6 - len(final_first)
                
                if remaining_needed > 0:
                    remaining_candidates = [n for n in all_numbers if n not in final_first]
                    final_first.extend(remaining_candidates[:remaining_needed])
                
                # 特別號使用機器學習結果
                final_special = ml_numbers.get('特別號', 1)
                
                return {
                    '第一區': sorted(final_first),
                    '特別號': final_special,
                    '融合信心度': 0.8 if len(common_numbers) >= 3 else 0.6
                }
                
            elif lottery_type == 'dailycash':
                # 今彩539融合邏輯
                enhanced_numbers_list = enhanced_numbers.get('numbers', [])
                ml_numbers_list = ml_numbers.get('號碼', [])
                
                common_numbers = list(set(enhanced_numbers_list) & set(ml_numbers_list))
                all_numbers = list(set(enhanced_numbers_list + ml_numbers_list))
                
                final_numbers = common_numbers[:5]
                remaining_needed = 5 - len(final_numbers)
                
                if remaining_needed > 0:
                    remaining_candidates = [n for n in all_numbers if n not in final_numbers]
                    final_numbers.extend(remaining_candidates[:remaining_needed])
                
                return {
                    '號碼': sorted(final_numbers),
                    '融合信心度': 0.8 if len(common_numbers) >= 2 else 0.6
                }
            
            return {}
            
        except Exception as e:
            logger.error(f"融合預測結果時出錯: {str(e)}")
            return {}