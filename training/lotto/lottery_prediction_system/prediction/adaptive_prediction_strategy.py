#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自適應預測策略

實現動態方法選擇和智能預測，根據各方法的歷史表現自動選擇最佳預測策略。
整合成功率計算器和最佳預測選擇器，提供單一、可靠的預測結果。
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging
from dataclasses import dataclass

from .success_rate_calculator import SuccessRateCalculator
from .optimal_prediction_selector import OptimalPredictionSelector
from .prediction_result import PredictionResult

@dataclass
class MethodPerformance:
    """方法性能數據"""
    method: str
    success_rate: float
    avg_matches: float
    trend: str  # 'improving', 'stable', 'declining'
    total_predictions: int
    recommendation_score: float
    last_updated: str

class MethodPerformanceMonitor:
    """方法性能監控器
    
    負責監控各預測方法的性能表現，分析趨勢，提供方法推薦。
    """
    
    def __init__(self, success_rate_calculator: SuccessRateCalculator):
        self.success_calculator = success_rate_calculator
        self.logger = logging.getLogger(__name__)
    
    def get_recent_performance(self, lottery_type: str, days: int = 30) -> Dict[str, MethodPerformance]:
        """獲取最近性能表現
        
        Args:
            lottery_type: 彩票類型
            days: 分析天數
            
        Returns:
            Dict: 各方法的性能數據
        """
        methods = ['ml', 'board_path', 'ensemble', 'integrated']
        performance = {}
        
        for method in methods:
            try:
                # 獲取基本統計
                stats = self.success_calculator.calculate_method_success_rate(
                    method, lottery_type, period_days=days
                )
                
                if stats.get('status') == 'success':
                    # 獲取趨勢分析
                    trend_data = self.success_calculator.get_recent_trend(
                        method, lottery_type, periods=3
                    )
                    
                    # 計算推薦評分
                    recommendation_score = self._calculate_recommendation_score(
                        stats, trend_data
                    )
                    
                    performance[method] = MethodPerformance(
                        method=method,
                        success_rate=stats['success_rates']['3_or_more']['rate'],
                        avg_matches=stats['average_matches'],
                        trend=trend_data['trend_direction'],
                        total_predictions=stats['total_predictions'],
                        recommendation_score=recommendation_score,
                        last_updated=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    )
                else:
                    # 數據不足的情況
                    performance[method] = MethodPerformance(
                        method=method,
                        success_rate=0.0,
                        avg_matches=0.0,
                        trend='unknown',
                        total_predictions=0,
                        recommendation_score=0.0,
                        last_updated=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    )
                    
            except Exception as e:
                self.logger.warning(f"獲取方法 {method} 性能數據失敗: {e}")
                performance[method] = MethodPerformance(
                    method=method,
                    success_rate=0.0,
                    avg_matches=0.0,
                    trend='error',
                    total_predictions=0,
                    recommendation_score=0.0,
                    last_updated=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                )
        
        return performance
    
    def recommend_best_method(self, lottery_type: str) -> str:
        """推薦當前最佳方法
        
        Args:
            lottery_type: 彩票類型
            
        Returns:
            str: 推薦的方法名稱
        """
        performance = self.get_recent_performance(lottery_type)
        
        # 過濾出有效的方法（有足夠數據）
        valid_methods = {
            method: perf for method, perf in performance.items()
            if perf.total_predictions >= 5  # 至少5個預測
        }
        
        if not valid_methods:
            # 如果沒有足夠數據，使用默認優先級
            default_priority = ['ensemble', 'integrated', 'ml', 'board_path']
            for method in default_priority:
                if method in performance:
                    self.logger.info(f"使用默認優先級選擇方法: {method}")
                    return method
            return 'ml'  # 最後備選
        
        # 選擇推薦評分最高的方法
        best_method = max(valid_methods.items(), 
                         key=lambda x: x[1].recommendation_score)[0]
        
        self.logger.info(f"基於性能分析推薦方法: {best_method}")
        return best_method
    
    def _calculate_recommendation_score(self, stats: Dict, trend_data: Dict) -> float:
        """計算推薦評分
        
        綜合考慮成功率、趨勢、數據量等因素
        """
        # 基礎成功率評分 (0-0.4)
        base_score = stats['success_rates']['3_or_more']['rate'] * 0.4
        
        # 趨勢獎勵/懲罰 (±0.2)
        trend_bonus = {
            'improving': 0.2,
            'stable': 0.0,
            'declining': -0.2,
            'unknown': -0.1
        }.get(trend_data['trend_direction'], 0.0)
        
        # 數據可靠性評分 (0-0.2)
        total_predictions = stats['total_predictions']
        reliability_score = min(0.2, total_predictions / 50 * 0.2)
        
        # 平均匹配數獎勵 (0-0.2)
        avg_matches = stats['average_matches']
        match_bonus = min(0.2, avg_matches / 6 * 0.2)
        
        total_score = base_score + trend_bonus + reliability_score + match_bonus
        return max(0.0, min(1.0, total_score))  # 限制在0-1範圍內

class AdaptivePredictionStrategy:
    """自適應預測策略
    
    主要的預測協調器，負責整合各個組件，提供智能的預測服務。
    """
    
    def __init__(self, db_path: str = "lottery_predictions.db"):
        self.success_calculator = SuccessRateCalculator(db_path)
        self.performance_monitor = MethodPerformanceMonitor(self.success_calculator)
        self.prediction_selector = OptimalPredictionSelector(self.success_calculator)
        self.logger = logging.getLogger(__name__)
        
        # 預測器實例（需要在使用時注入）
        self.ml_predictor = None
        self.board_path_engine = None
        self.integrated_predictor = None
        self.ensemble_predictor = None
    
    def set_predictors(self, ml_predictor=None, board_path_engine=None, 
                      integrated_predictor=None, ensemble_predictor=None):
        """設置預測器實例"""
        self.ml_predictor = ml_predictor
        self.board_path_engine = board_path_engine
        self.integrated_predictor = integrated_predictor
        self.ensemble_predictor = ensemble_predictor
    
    def predict_with_adaptive_strategy(self, df: pd.DataFrame, lottery_type: str, 
                                     features: np.ndarray = None, methods: List[str] = None,
                                     period: str = None, strategy: str = 'auto') -> Dict:
        """使用自適應策略進行預測
        
        Args:
            df: 歷史數據
            lottery_type: 彩票類型
            features: 預測特徵
            methods: 預測方法列表
            period: 期號
            strategy: 預測策略 ('auto', 'single_best', 'primary_backup')
            
        Returns:
            Dict: 預測結果
        """
        try:
            self.logger.info(f"開始自適應預測，策略: {strategy}")
            
            # 1. 分析當前最佳方法
            if strategy == 'auto':
                best_method = self.performance_monitor.recommend_best_method(lottery_type)
                self.logger.info(f"自動選擇最佳方法: {best_method}")
            else:
                best_method = 'ensemble'  # 默認使用集成方法
            
            # 2. 生成預測結果
            prediction_results = self._generate_prediction_results(df, lottery_type, best_method, features, period)
            
            if not prediction_results:
                return self._create_fallback_result(df, lottery_type)
            
            # 3. 選擇最佳預測
            max_candidates = 1 if strategy == 'single_best' else (2 if strategy == 'primary_backup' else 1)
            optimal_result = self.prediction_selector.select_optimal_prediction(
                prediction_results, lottery_type, max_candidates
            )
            
            # 4. 記錄預測（用於後續驗證）
            self._record_prediction(optimal_result, lottery_type)
            
            # 5. 生成最終結果
            final_result = self._create_final_result(optimal_result, lottery_type, best_method)
            
            self.logger.info("自適應預測完成")
            return final_result
            
        except Exception as e:
            self.logger.error(f"自適應預測失敗: {e}")
            return self._create_error_result(str(e))
    
    def _generate_prediction_results(self, df: pd.DataFrame, lottery_type: str, 
                                   preferred_method: str, features: np.ndarray = None, 
                                   period: str = None) -> List[PredictionResult]:
        """生成預測結果"""
        results = []
        
        # 根據偏好方法和可用預測器生成結果
        if preferred_method == 'ml' and self.ml_predictor:
            try:
                result = self.ml_predictor.predict(df, lottery_type, period=period)
                if result:
                    results.append(result)
            except Exception as e:
                self.logger.warning(f"ML預測失敗: {e}")
        
        elif preferred_method == 'board_path' and self.board_path_engine:
            try:
                result = self.board_path_engine.predict(df, period=period)
                if result:
                    results.append(result)
            except Exception as e:
                self.logger.warning(f"板路分析預測失敗: {e}")
        
        elif preferred_method == 'integrated' and self.integrated_predictor:
            try:
                result = self.integrated_predictor.predict(df, lottery_type, period=period)
                if result:
                    results.append(result)
            except Exception as e:
                self.logger.warning(f"整合預測失敗: {e}")
        
        elif preferred_method == 'ensemble' and self.ensemble_predictor:
            try:
                result = self.ensemble_predictor.predict(df, lottery_type, period=period)
                if result:
                    results.append(result)
            except Exception as e:
                self.logger.warning(f"集成預測失敗: {e}")
        
        # 如果偏好方法失敗，嘗試其他可用方法
        if not results:
            self.logger.warning(f"偏好方法 {preferred_method} 失敗，嘗試其他方法")
            
            backup_methods = [('ensemble', self.ensemble_predictor),
                            ('integrated', self.integrated_predictor),
                            ('ml', self.ml_predictor),
                            ('board_path', self.board_path_engine)]
            
            for method_name, predictor in backup_methods:
                if method_name != preferred_method and predictor:
                    try:
                        if method_name == 'board_path':
                            result = predictor.predict(df, period=period)
                        else:
                            result = predictor.predict(df, lottery_type, period=period)
                        
                        if result:
                            results.append(result)
                            break
                    except Exception as e:
                        self.logger.warning(f"備用方法 {method_name} 也失敗: {e}")
                        continue
        
        return results
    
    def _record_prediction(self, optimal_result: Dict, lottery_type: str):
        """記錄預測結果（用於後續驗證）"""
        try:
            if optimal_result['type'] in ['single_prediction', 'fallback_prediction']:
                prediction = optimal_result['prediction']
                
                record_id = self.success_calculator.record_prediction(
                    method=prediction['method'],
                    lottery_type=lottery_type,
                    predicted_numbers=prediction['main_numbers'],
                    special_predicted=prediction.get('special_number'),
                    confidence=prediction.get('selection_score', 0.0)
                )
                
                self.logger.info(f"預測已記錄，ID: {record_id}")
                
            elif optimal_result['type'] == 'multiple_prediction':
                # 記錄主推預測
                primary = optimal_result['primary']
                record_id = self.success_calculator.record_prediction(
                    method=primary['method'],
                    lottery_type=lottery_type,
                    predicted_numbers=primary['main_numbers'],
                    special_predicted=primary.get('special_number'),
                    confidence=primary.get('selection_score', 0.0)
                )
                
                self.logger.info(f"主推預測已記錄，ID: {record_id}")
                
        except Exception as e:
            self.logger.error(f"記錄預測失敗: {e}")
    
    def _create_final_result(self, optimal_result: Dict, lottery_type: str, best_method: str) -> Dict:
        """創建最終預測結果"""
        # 獲取性能對比數據
        performance_comparison = self.performance_monitor.get_recent_performance(lottery_type)
        
        final_result = {
            'prediction_type': 'adaptive_strategy',
            'lottery_type': lottery_type,
            'prediction_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'recommended_method': best_method,
            'optimal_prediction': optimal_result,
            'method_performance': {
                method: {
                    'success_rate': f"{perf.success_rate * 100:.1f}%",
                    'avg_matches': f"{perf.avg_matches:.1f}",
                    'trend': perf.trend,
                    'total_predictions': perf.total_predictions,
                    'recommendation_score': f"{perf.recommendation_score:.3f}"
                }
                for method, perf in performance_comparison.items()
            },
            'system_status': {
                'data_quality': self._assess_data_quality(performance_comparison),
                'recommendation_confidence': self._assess_recommendation_confidence(performance_comparison),
                'next_update_time': (datetime.now() + timedelta(hours=24)).strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        
        return final_result
    
    def _create_fallback_result(self, df: pd.DataFrame, lottery_type: str) -> Dict:
        """創建備用結果（當所有預測器都失敗時）"""
        self.logger.warning("所有預測器都失敗，使用備用策略")
        
        # 簡單的隨機預測作為最後備選
        import random
        random.seed(int(datetime.now().timestamp()))
        
        main_numbers = sorted(random.sample(range(1, 50), 6))
        special_number = random.randint(1, 49)
        
        return {
            'prediction_type': 'fallback_strategy',
            'lottery_type': lottery_type,
            'prediction_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'optimal_prediction': {
                'type': 'fallback_prediction',
                'prediction': {
                    'main_numbers': main_numbers,
                    'special_number': special_number,
                    'method': 'random_fallback',
                    'selection_score': 0.1
                },
                'success_rate_info': {
                    'status': 'fallback_mode',
                    'message': '所有預測器都失敗，使用隨機備用策略'
                },
                'selection_reason': '系統故障，使用隨機數字作為備用',
                'confidence_level': 'very_low',
                'recommendation': '建議檢查系統狀態，此預測僅供參考'
            },
            'system_status': {
                'data_quality': 'error',
                'recommendation_confidence': 'very_low',
                'error_message': '所有預測器都無法正常工作'
            }
        }
    
    def _create_error_result(self, error_message: str) -> Dict:
        """創建錯誤結果"""
        return {
            'prediction_type': 'error',
            'error': True,
            'error_message': error_message,
            'prediction_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'recommendation': '請檢查系統配置和數據完整性'
        }
    
    def _assess_data_quality(self, performance: Dict[str, MethodPerformance]) -> str:
        """評估數據質量"""
        total_predictions = sum(perf.total_predictions for perf in performance.values())
        
        if total_predictions >= 100:
            return 'excellent'
        elif total_predictions >= 50:
            return 'good'
        elif total_predictions >= 20:
            return 'fair'
        else:
            return 'poor'
    
    def _assess_recommendation_confidence(self, performance: Dict[str, MethodPerformance]) -> str:
        """評估推薦信心度"""
        valid_methods = [perf for perf in performance.values() if perf.total_predictions >= 10]
        
        if not valid_methods:
            return 'very_low'
        
        max_score = max(perf.recommendation_score for perf in valid_methods)
        
        if max_score >= 0.8:
            return 'high'
        elif max_score >= 0.6:
            return 'medium'
        elif max_score >= 0.4:
            return 'low'
        else:
            return 'very_low'
    
    def verify_prediction_result(self, record_id: int, actual_numbers: List[int], 
                               special_actual: Optional[int] = None) -> Dict:
        """驗證預測結果
        
        Args:
            record_id: 預測記錄ID
            actual_numbers: 實際開獎號碼
            special_actual: 實際特別號碼
            
        Returns:
            Dict: 驗證結果
        """
        return self.success_calculator.verify_prediction(record_id, actual_numbers, special_actual)
    
    def get_system_performance_report(self, lottery_type: str, days: int = 90) -> Dict:
        """獲取系統性能報告
        
        Args:
            lottery_type: 彩票類型
            days: 報告期間（天）
            
        Returns:
            Dict: 性能報告
        """
        # 獲取方法對比
        comparison = self.success_calculator.get_all_methods_comparison(lottery_type, days)
        
        # 獲取最近性能
        recent_performance = self.performance_monitor.get_recent_performance(lottery_type, 30)
        
        # 生成報告
        report = {
            'report_type': 'system_performance',
            'lottery_type': lottery_type,
            'report_period': f'{days} 天',
            'generated_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'method_comparison': comparison,
            'recent_performance': {
                method: {
                    'success_rate': f"{perf.success_rate * 100:.1f}%",
                    'trend': perf.trend,
                    'recommendation_score': f"{perf.recommendation_score:.3f}"
                }
                for method, perf in recent_performance.items()
            },
            'recommendations': {
                'best_method': comparison.get('best_method', 'unknown'),
                'system_recommendation': comparison.get('recommendation', '無推薦'),
                'data_quality': self._assess_data_quality(recent_performance),
                'improvement_suggestions': self._generate_improvement_suggestions(recent_performance)
            }
        }
        
        return report
    
    def _generate_improvement_suggestions(self, performance: Dict[str, MethodPerformance]) -> List[str]:
        """生成改進建議"""
        suggestions = []
        
        # 檢查數據量
        total_predictions = sum(perf.total_predictions for perf in performance.values())
        if total_predictions < 50:
            suggestions.append("建議累積更多預測數據以提高分析準確性")
        
        # 檢查方法表現
        declining_methods = [perf.method for perf in performance.values() if perf.trend == 'declining']
        if declining_methods:
            suggestions.append(f"以下方法表現下降，建議檢查: {', '.join(declining_methods)}")
        
        # 檢查成功率
        low_performance_methods = [perf.method for perf in performance.values() 
                                 if perf.success_rate < 0.15 and perf.total_predictions >= 10]
        if low_performance_methods:
            suggestions.append(f"以下方法成功率較低，建議優化: {', '.join(low_performance_methods)}")
        
        if not suggestions:
            suggestions.append("系統運行良好，繼續保持當前策略")
        
        return suggestions


def test_adaptive_prediction_strategy():
    """測試自適應預測策略"""
    print("=== 自適應預測策略測試 ===")
    
    # 創建策略實例
    strategy = AdaptivePredictionStrategy(":memory:")
    
    # 創建模擬數據
    import pandas as pd
    df = pd.DataFrame({
        'draw_date': ['2024-01-01', '2024-01-04', '2024-01-07'],
        'number_1': [1, 5, 9],
        'number_2': [15, 19, 23],
        'number_3': [22, 26, 30],
        'number_4': [28, 32, 36],
        'number_5': [35, 39, 43],
        'number_6': [38, 42, 46],
        'special_number': [7, 11, 15]
    })
    
    # 測試性能監控
    print("\n1. 測試性能監控")
    performance = strategy.performance_monitor.get_recent_performance("lotto649")
    for method, perf in performance.items():
        print(f"{method}: 成功率={perf.success_rate:.3f}, 趨勢={perf.trend}, 評分={perf.recommendation_score:.3f}")
    
    # 測試方法推薦
    print("\n2. 測試方法推薦")
    best_method = strategy.performance_monitor.recommend_best_method("lotto649")
    print(f"推薦方法: {best_method}")
    
    # 測試系統報告
    print("\n3. 測試系統報告")
    report = strategy.get_system_performance_report("lotto649")
    print(f"報告類型: {report['report_type']}")
    print(f"最佳方法: {report['recommendations']['best_method']}")
    print(f"數據質量: {report['recommendations']['data_quality']}")
    print(f"改進建議: {report['recommendations']['improvement_suggestions']}")
    
    print("\n=== 測試完成 ===")

if __name__ == "__main__":
    test_adaptive_prediction_strategy()