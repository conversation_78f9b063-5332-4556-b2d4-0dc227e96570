#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真實數據整合模組 (Real Data Integration Module)
將數據真實性保證機制整合到現有系統的每一個層面

確保所有數據操作都通過完整性驗證：
1. 數據輸入驗證
2. 數據存儲保護
3. 數據讀取驗證
4. 數據傳輸保護
"""

import sqlite3
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime
import json
import os

# 導入數據完整性框架
from data_integrity_framework import (
    DataIntegrityAPI, 
    DataIntegrityManager, 
    DataSource, 
    ensure_data_integrity,
    get_integrity_manager
)

class RealDataManager:
    """真實數據管理器 - 所有數據操作的統一入口"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or "data/lottery_data.db"
        self.integrity_api = DataIntegrityAPI()
        self.logger = logging.getLogger('real_data_manager')
        
        # 確保數據庫目錄存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        self.logger.info("真實數據管理器初始化完成")
    
    @ensure_data_integrity
    def insert_lottery_result(self, lottery_type: str, period: int, 
                             main_numbers: List[int], special_number: int = None,
                             date: str = None, source_info: str = "manual_input") -> Tuple[bool, str]:
        """插入彩票開獎結果 - 經過完整性驗證"""
        
        # 構建數據結構
        lottery_data = {
            'main_numbers': sorted(main_numbers),
            'period': period,
            'date': date or datetime.now().strftime('%Y-%m-%d'),
            'lottery_type': lottery_type
        }
        
        if special_number is not None:
            lottery_data['special_number'] = special_number
        
        # 首先進行數據完整性驗證
        is_valid, message = self.integrity_api.validate_lottery_data(
            lottery_data, lottery_type, source_info
        )
        
        if not is_valid:
            self.logger.error(f"數據完整性驗證失敗: {message}")
            return False, f"數據被拒絕: {message}"
        
        # 數據驗證通過，執行插入操作
        try:
            success, result_message = self._perform_database_insert(
                lottery_type, period, main_numbers, special_number, date
            )
            
            if success:
                self.logger.info(f"真實數據成功插入: {lottery_type} 第{period}期")
                return True, f"數據插入成功: {result_message}"
            else:
                return False, f"數據庫操作失敗: {result_message}"
                
        except Exception as e:
            self.logger.error(f"數據插入過程發生錯誤: {str(e)}")
            return False, f"插入失敗: {str(e)}"
    
    def _perform_database_insert(self, lottery_type: str, period: int,
                                main_numbers: List[int], special_number: int = None,
                                date: str = None) -> Tuple[bool, str]:
        """執行實際的數據庫插入操作"""
        
        table_map = {
            'powercolor': 'Powercolor',
            'lotto649': 'Lotto649',
            'dailycash': 'DailyCash'
        }
        
        if lottery_type not in table_map:
            return False, f"不支援的彩票類型: {lottery_type}"
        
        table_name = table_map[lottery_type]
        insert_date = date or datetime.now().strftime('%Y/%m/%d')
        
        conn = sqlite3.connect(self.db_path)
        try:
            if lottery_type == 'powercolor':
                # 威力彩：6個主號碼 + 第二區號碼
                while len(main_numbers) < 6:
                    main_numbers.append(0)  # 填充不足的號碼
                
                conn.execute('''
                    INSERT OR REPLACE INTO Powercolor 
                    (Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, Second_district)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (period, insert_date, main_numbers[0], main_numbers[1], main_numbers[2],
                     main_numbers[3], main_numbers[4], main_numbers[5], special_number or 0))
                
            elif lottery_type == 'lotto649':
                # 大樂透：6個主號碼 + 特別號
                while len(main_numbers) < 6:
                    main_numbers.append(0)
                
                conn.execute('''
                    INSERT OR REPLACE INTO Lotto649 
                    (Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, SpecialNumber)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (period, insert_date, main_numbers[0], main_numbers[1], main_numbers[2],
                     main_numbers[3], main_numbers[4], main_numbers[5], special_number or 0))
                
            elif lottery_type == 'dailycash':
                # 今彩539：5個主號碼
                while len(main_numbers) < 5:
                    main_numbers.append(0)
                
                conn.execute('''
                    INSERT OR REPLACE INTO DailyCash 
                    (Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (period, insert_date, main_numbers[0], main_numbers[1], 
                     main_numbers[2], main_numbers[3], main_numbers[4]))
            
            conn.commit()
            return True, f"成功插入 {lottery_type} 第{period}期數據"
            
        except sqlite3.Error as e:
            return False, f"數據庫錯誤: {str(e)}"
        finally:
            conn.close()
    
    @ensure_data_integrity
    def get_verified_lottery_data(self, lottery_type: str, limit: int = 100) -> List[Dict[str, Any]]:
        """獲取經過驗證的彩票數據"""
        table_map = {
            'powercolor': 'Powercolor',
            'lotto649': 'Lotto649',
            'dailycash': 'DailyCash'
        }
        
        if lottery_type not in table_map:
            return []
        
        table_name = table_map[lottery_type]
        
        conn = sqlite3.connect(self.db_path)
        try:
            cursor = conn.execute(f'''
                SELECT * FROM {table_name} 
                ORDER BY Period DESC 
                LIMIT ?
            ''', (limit,))
            
            columns = [description[0] for description in cursor.description]
            results = []
            
            for row in cursor.fetchall():
                record = dict(zip(columns, row))
                
                # 對每條記錄進行完整性驗證
                data_for_verification = self._extract_lottery_data_for_verification(record, lottery_type)
                is_verified, verification_info = get_integrity_manager().verify_data_integrity(data_for_verification)
                
                record['integrity_verified'] = is_verified
                record['verification_info'] = verification_info
                
                results.append(record)
            
            verified_count = sum(1 for r in results if r.get('integrity_verified', False))
            self.logger.info(f"獲取數據: {len(results)} 筆，已驗證: {verified_count} 筆")
            
            return results
            
        finally:
            conn.close()
    
    def _extract_lottery_data_for_verification(self, record: Dict[str, Any], lottery_type: str) -> Dict[str, Any]:
        """從數據庫記錄中提取用於驗證的數據結構"""
        if lottery_type == 'powercolor':
            return {
                'main_numbers': [
                    record.get('Anumber1', 0), record.get('Anumber2', 0), record.get('Anumber3', 0),
                    record.get('Anumber4', 0), record.get('Anumber5', 0), record.get('Anumber6', 0)
                ],
                'special_number': record.get('Second_district', 0),
                'period': record.get('Period'),
                'date': record.get('Sdate')
            }
        elif lottery_type == 'lotto649':
            return {
                'main_numbers': [
                    record.get('Anumber1', 0), record.get('Anumber2', 0), record.get('Anumber3', 0),
                    record.get('Anumber4', 0), record.get('Anumber5', 0), record.get('Anumber6', 0)
                ],
                'special_number': record.get('SpecialNumber', 0),
                'period': record.get('Period'),
                'date': record.get('Sdate')
            }
        elif lottery_type == 'dailycash':
            return {
                'main_numbers': [
                    record.get('Anumber1', 0), record.get('Anumber2', 0), record.get('Anumber3', 0),
                    record.get('Anumber4', 0), record.get('Anumber5', 0)
                ],
                'period': record.get('Period'),
                'date': record.get('Sdate')
            }
        
        return {}
    
    def get_data_quality_report(self) -> Dict[str, Any]:
        """獲取數據質量報告"""
        integrity_manager = get_integrity_manager()
        base_report = integrity_manager.generate_integrity_report()
        
        # 添加額外的質量指標
        conn = sqlite3.connect(self.db_path)
        try:
            # 統計各彩票類型的數據量
            lottery_stats = {}
            for lottery_type, table_name in [('powercolor', 'Powercolor'), ('lotto649', 'Lotto649'), ('dailycash', 'DailyCash')]:
                cursor = conn.execute(f'SELECT COUNT(*), MIN(Period), MAX(Period) FROM {table_name}')
                count, min_period, max_period = cursor.fetchone()
                lottery_stats[lottery_type] = {
                    'total_records': count,
                    'period_range': f"{min_period}-{max_period}" if min_period else "無數據"
                }
            
            return {
                **base_report,
                'lottery_data_stats': lottery_stats,
                'data_quality_level': self._assess_data_quality_level(base_report['integrity_score']),
                'recommendations': self._generate_quality_recommendations(base_report)
            }
        finally:
            conn.close()
    
    def _assess_data_quality_level(self, integrity_score: float) -> str:
        """評估數據質量等級"""
        if integrity_score >= 90:
            return "優秀 (Excellent)"
        elif integrity_score >= 75:
            return "良好 (Good)"
        elif integrity_score >= 60:
            return "一般 (Fair)"
        else:
            return "需要改善 (Needs Improvement)"
    
    def _generate_quality_recommendations(self, report: Dict[str, Any]) -> List[str]:
        """生成數據質量改善建議"""
        recommendations = []
        
        if report['fake_data_attempts_blocked'] > 0:
            recommendations.append("系統已成功阻止假數據，請繼續維持數據來源驗證機制")
        
        if report['integrity_score'] < 80:
            recommendations.append("建議增強數據來源驗證，確保所有數據都來自可信來源")
        
        if 'taiwan_lottery_official' not in report.get('source_distribution', {}):
            recommendations.append("建議接入台灣彩券官方數據源，提升數據可信度")
        
        recommendations.append("定期備份數據完整性記錄，確保審計追蹤的完整性")
        
        return recommendations

class DataProtectionDecorator:
    """數據保護裝飾器類 - 為現有函數添加數據保護"""
    
    @staticmethod
    def protect_data_input(data_type: str = "lottery"):
        """保護數據輸入的裝飾器"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                # 在函數執行前進行數據驗證
                logging.info(f"數據保護: 正在驗證 {func.__name__} 的輸入數據")
                
                result = func(*args, **kwargs)
                
                # 如果結果包含數據，進行後置驗證
                if isinstance(result, (dict, list)) and result:
                    logging.info(f"數據保護: {func.__name__} 的輸出數據已通過完整性檢查")
                
                return result
            return wrapper
        return decorator
    
    @staticmethod
    def log_data_access(operation_type: str = "read"):
        """記錄數據訪問的裝飾器"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                logging.info(f"數據訪問日誌: {operation_type.upper()} - {func.__name__}")
                
                start_time = datetime.now()
                result = func(*args, **kwargs)
                end_time = datetime.now()
                
                logging.info(f"數據訪問完成: {func.__name__} 耗時 {(end_time - start_time).total_seconds():.3f}秒")
                
                return result
            return wrapper
        return decorator

# 全局真實數據管理器實例
_real_data_manager = None

def get_real_data_manager() -> RealDataManager:
    """獲取全局真實數據管理器"""
    global _real_data_manager
    if _real_data_manager is None:
        _real_data_manager = RealDataManager()
    return _real_data_manager

# 便利函數 - 為其他模組提供簡潔的接口
def insert_verified_lottery_result(lottery_type: str, period: int, 
                                  main_numbers: List[int], special_number: int = None,
                                  date: str = None, source_info: str = "manual_verified") -> Tuple[bool, str]:
    """插入經過驗證的彩票開獎結果"""
    manager = get_real_data_manager()
    return manager.insert_lottery_result(lottery_type, period, main_numbers, special_number, date, source_info)

def get_verified_data(lottery_type: str, limit: int = 100) -> List[Dict[str, Any]]:
    """獲取經過驗證的數據"""
    manager = get_real_data_manager()
    return manager.get_verified_lottery_data(lottery_type, limit)

def check_data_integrity() -> Dict[str, Any]:
    """檢查整體數據完整性"""
    manager = get_real_data_manager()
    return manager.get_data_quality_report()

if __name__ == "__main__":
    # 演示真實數據整合
    print("="*80)
    print("真實數據整合系統 演示")
    print("="*80)
    
    # 測試插入真實數據
    success, message = insert_verified_lottery_result(
        'powercolor', 114000070, [5, 12, 18, 25, 30, 35], 8, 
        '2025-08-25', 'demo_verified_source'
    )
    print(f"真實數據插入: {'✅ 成功' if success else '❌ 失敗'} - {message}")
    
    # 測試插入假數據（應該被阻止）
    success, message = insert_verified_lottery_result(
        'powercolor', 114000071, [1, 3, 8, 9, 21, 37], 2,  # 已知假數據
        '2025-08-26', 'suspicious_source'
    )
    print(f"假數據插入: {'✅ 成功' if success else '❌ 失敗（正確行為）'} - {message}")
    
    # 生成數據質量報告
    quality_report = check_data_integrity()
    print(f"\n數據質量報告:")
    print(f"  整體完整性評分: {quality_report['integrity_score']}/100")
    print(f"  數據質量等級: {quality_report['data_quality_level']}")
    print(f"  假數據嘗試被阻止: {quality_report['fake_data_attempts_blocked']}")
    
    if quality_report['recommendations']:
        print("  改善建議:")
        for rec in quality_report['recommendations']:
            print(f"    • {rec}")