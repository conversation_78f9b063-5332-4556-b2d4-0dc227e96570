#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試台灣彩券網站內容解析
"""

import requests
import re
from bs4 import BeautifulSoup
import urllib3

# 抑制SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_website_content():
    """測試網站內容獲取和解析"""
    
    url = "https://www.taiwanlottery.com/lotto/lotto_lastest_result"
    
    # 設置請求頭
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    try:
        print(f"正在請求: {url}")
        response = requests.get(url, headers=headers, timeout=30, verify=False)
        response.raise_for_status()
        
        print(f"狀態碼: {response.status_code}")
        print(f"內容類型: {response.headers.get('content-type')}")
        print(f"內容長度: {len(response.text)}")
        print(f"響應編碼: {response.encoding}")
        
        # 嘗試不同的編碼方式
        encodings_to_try = ['utf-8', 'big5', 'gb2312', 'gbk']
        
        best_text = None
        best_encoding = None
        
        for encoding in encodings_to_try:
            try:
                decoded_text = response.content.decode(encoding)
                if '威力彩' in decoded_text or '台灣彩券' in decoded_text:
                    best_text = decoded_text
                    best_encoding = encoding
                    print(f"成功解碼，使用編碼: {encoding}")
                    break
            except UnicodeDecodeError:
                continue
        
        if best_text is None:
            # 如果都無法解碼，使用原始response.text
            best_text = response.text
            best_encoding = response.encoding
            print(f"使用原始編碼: {best_encoding}")
        
        # 解析HTML
        soup = BeautifulSoup(best_text, 'html.parser')
        page_text = soup.get_text()
        
        print(f"頁面文本長度: {len(page_text)}")
        print(f"使用的編碼: {best_encoding}")
        
        # 尋找期號模式
        period_pattern = r'第(\d{9})期'
        period_matches = re.findall(period_pattern, page_text)
        print(f"找到的期號: {period_matches}")
        
        # 尋找日期模式  
        date_pattern = r'(\d{3})/(\d{1,2})/(\d{1,2})'
        date_matches = re.findall(date_pattern, page_text)
        print(f"找到的日期: {date_matches}")
        
        # 尋找威力彩關鍵詞
        if '威力彩' in page_text:
            powercolor_pos = page_text.find('威力彩')
            context = page_text[max(0, powercolor_pos-200):powercolor_pos+500]
            print(f"\n威力彩上下文:\n{context}")
        else:
            print("未找到威力彩關鍵詞")
            
        # 顯示頁面前1000個字符
        print(f"\n頁面前1000個字符:\n{page_text[:1000]}")
        
        # 尋找所有數字
        all_numbers = re.findall(r'\b(\d{1,2})\b', page_text[:2000])
        print(f"\n頁面前2000字符中的數字: {all_numbers[:30]}")
        
        return True
        
    except Exception as e:
        print(f"請求失敗: {str(e)}")
        return False

if __name__ == "__main__":
    test_website_content()