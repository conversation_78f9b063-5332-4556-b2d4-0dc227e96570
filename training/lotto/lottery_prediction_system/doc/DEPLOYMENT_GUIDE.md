# Phase 3.6 生產環境部署指南

## 📋 系統概覽

彩票預測系統 Phase 3.6 提供完整的生產環境部署解決方案，包含：

- **🐳 容器化部署**: Docker + Docker Compose 完整容器編排
- **📊 監控系統**: Prometheus + Grafana 實時監控和可視化
- **🔍 日誌管理**: ELK Stack (Elasticsearch + Logstash + Kibana)
- **🛡️ 反向代理**: Nginx 負載均衡和安全防護
- **💾 自動備份**: 定時數據備份和恢復系統
- **🏥 健康檢查**: 全方位服務健康監控

## 🏗️ 系統架構

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx Proxy   │    │  Grafana UI     │    │   Kibana UI     │
│   Port: 80/443  │    │   Port: 3000    │    │   Port: 5601    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Lottery API    │    │   Prometheus    │    │ Elasticsearch   │
│  Port: 8000     │    │   Port: 9090    │    │   Port: 9200    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web UI        │    │  Redis Cache    │    │   Filebeat      │
│   Port: 8080    │    │   Port: 6379    │    │  Log Collector  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速部署

### 先決條件

1. **Docker** (版本 ≥ 20.0)
2. **Docker Compose** (版本 ≥ 2.0)
3. **系統資源**: 
   - CPU: 2+ 核心
   - 內存: 4GB+ RAM
   - 磁盤: 10GB+ 可用空間

### 一鍵部署

```bash
# 1. 下載並解壓系統
git clone <repository>
cd lottery_prediction_system

# 2. 執行一鍵部署
python deploy.py deploy --build --wait --health-check

# 3. 等待部署完成（約 3-5 分鐘）
```

### 部署參數

```bash
# 基本部署
python deploy.py deploy

# 重新構建鏡像
python deploy.py deploy --build

# 僅啟動特定服務
python deploy.py deploy --services "lottery-api,redis,prometheus"

# 跳過健康檢查
python deploy.py deploy --no-wait --no-health-check
```

## 🔧 配置管理

### 主配置文件: `deploy_config.yaml`

```yaml
deployment:
  environment: production
  domain: your-domain.com
  ssl_enabled: true
  backup_enabled: true
  monitoring_enabled: true

services:
  api:
    host: 0.0.0.0
    port: 8000
    workers: 4
    timeout: 30
  
  web:
    port: 8080
  
  redis:
    enabled: true
    max_memory: 512mb
  
  prometheus:
    enabled: true
    retention: 30d
  
  grafana:
    enabled: true
    admin_password: your_secure_password
  
  elasticsearch:
    enabled: true
    memory: 1g

database:
  backup_schedule: "0 2 * * *"  # 每天凌晨2點
  backup_retention: 30

security:
  rate_limit_api: "100r/s"
  rate_limit_web: "50r/s"
  allowed_hosts:
    - localhost
    - your-domain.com
```

### 環境變量配置

部署腳本自動生成 `.env` 文件：

```bash
# 應用配置
LOTTERY_SYSTEM_MODE=production
LOTTERY_SYSTEM_LOG_LEVEL=INFO
DATABASE_URL=sqlite:///app/data/lottery_prediction.db

# API 配置
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# 監控配置
GF_SECURITY_ADMIN_PASSWORD=your_password

# 安全配置
RATE_LIMIT_API=100r/s
RATE_LIMIT_WEB=50r/s
```

## 📊 監控與運維

### 服務端點

| 服務 | 端點 | 說明 |
|------|------|------|
| **Web 界面** | http://localhost:8080 | 用戶操作界面 |
| **API 接口** | http://localhost:8000 | REST API 服務 |
| **API 文檔** | http://localhost:8000/docs | 交互式 API 文檔 |
| **健康檢查** | http://localhost:8000/health | 系統健康狀態 |
| **監控指標** | http://localhost:8000/metrics | Prometheus 指標 |
| **Prometheus** | http://localhost:9090 | 監控數據查詢 |
| **Grafana** | http://localhost:3000 | 監控面板 (admin/admin123) |
| **Kibana** | http://localhost:5601 | 日誌分析 |

### 監控指標

#### 系統級指標
- **CPU 使用率**: 實時 CPU 負載監控
- **內存使用率**: 內存消耗和可用內存
- **磁盤使用率**: 存儲空間監控
- **網絡 I/O**: 網絡流量統計

#### 應用級指標
- **API 響應時間**: 接口性能監控
- **請求成功率**: API 可用性指標
- **預測準確率**: 預測模型性能
- **數據庫連接**: 數據庫健康狀態

#### 業務指標
- **預測請求數**: 每日預測請求統計
- **用戶活躍度**: 用戶使用情況
- **彩票類型分佈**: 不同彩票類型使用率
- **系統錯誤率**: 業務異常監控

### Grafana 儀表板

預配置儀表板包含：

1. **系統概覽**: 整體系統狀態
2. **API 性能**: 接口響應和錯誤率
3. **數據庫監控**: 數據庫性能指標
4. **業務指標**: 預測和用戶統計
5. **錯誤追蹤**: 系統異常和警報

## 📂 數據管理

### 目錄結構

```
lottery_prediction_system/
├── data/                    # 數據庫文件
│   ├── lottery_prediction.db
│   └── cache.db
├── logs/                    # 系統日誌
│   ├── api.log
│   ├── web.log
│   └── system.log
├── backups/                 # 備份文件
│   ├── 2024-01-15_lottery_prediction.db.gz
│   └── system_backup_2024-01-15.tar.gz
├── reports/                 # 分析報告
│   └── prediction_analysis.html
└── exports/                 # 導出數據
    └── predictions_export.csv
```

### 備份策略

#### 自動備份
- **頻率**: 每日凌晨 2 點自動備份
- **保留**: 30 天歷史備份
- **內容**: 數據庫、配置文件、日誌
- **格式**: 壓縮 tar.gz 格式

#### 手動備份
```bash
# 立即創建備份
docker-compose exec lottery-api python -c "
from phase3.production_deployment_system import BackupManager, DeploymentConfig
config = DeploymentConfig.load_from_file('deploy_config.yaml')
backup_manager = BackupManager(config)
backup_info = backup_manager.create_system_backup()
print(f'備份完成: {backup_info[\"backup_file\"]}')
"
```

#### 備份恢復
```bash
# 停止服務
docker-compose down

# 恢復數據
tar -xzf backups/system_backup_2024-01-15.tar.gz -C ./

# 重啟服務
docker-compose up -d
```

## 🔒 安全配置

### SSL/HTTPS 配置

1. **獲取 SSL 證書** (Let's Encrypt 或商業證書)
2. **放置證書文件**:
   ```bash
   docker/ssl/cert.pem
   docker/ssl/key.pem
   ```
3. **更新配置**:
   ```yaml
   deployment:
     ssl_enabled: true
     domain: your-domain.com
   ```
4. **重新部署**:
   ```bash
   python deploy.py deploy --build
   ```

### 防火牆配置

```bash
# 僅開放必要端口
sudo ufw allow 80      # HTTP
sudo ufw allow 443     # HTTPS
sudo ufw allow 22      # SSH (管理)

# 關閉直接訪問
sudo ufw deny 8000     # API (通過 Nginx 代理)
sudo ufw deny 3000     # Grafana (VPN 訪問)
sudo ufw deny 9090     # Prometheus (VPN 訪問)
```

### 訪問控制

Nginx 配置中已包含：
- **速率限制**: API 10req/s, Web 5req/s
- **IP 白名單**: 監控端點僅內網訪問
- **安全頭部**: XSS、CSRF、點擊劫持防護

## 🛠️ 故障排除

### 常見問題

#### 1. 服務無法啟動
```bash
# 檢查 Docker 狀態
docker-compose ps

# 查看服務日誌
docker-compose logs lottery-api

# 檢查資源使用
docker stats
```

#### 2. 端口衝突
```bash
# 檢查端口占用
python deploy.py deploy --check-ports

# 修改端口配置
vim deploy_config.yaml
```

#### 3. 內存不足
```bash
# 檢查內存使用
free -h

# 調整服務配置
vim deploy_config.yaml
# 減少 workers 數量或禁用 ELK Stack
```

#### 4. 數據庫問題
```bash
# 檢查數據庫完整性
sqlite3 data/lottery_prediction.db "PRAGMA integrity_check;"

# 重建數據庫索引
sqlite3 data/lottery_prediction.db "REINDEX;"
```

### 日誌分析

#### 系統日誌位置
- **API 日誌**: `logs/api.log`
- **Web 日誌**: `logs/web.log`
- **系統日誌**: `logs/system.log`
- **Nginx 日誌**: `logs/nginx/access.log`

#### 常用日誌命令
```bash
# 實時查看 API 日誌
docker-compose logs -f lottery-api

# 查看錯誤日誌
docker-compose logs lottery-api | grep ERROR

# 分析訪問日誌
tail -f logs/nginx/access.log | grep -v "200"
```

### 性能優化

#### 資源調優
```yaml
# docker-compose.yml 中調整資源限制
services:
  lottery-api:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
```

#### 數據庫優化
```bash
# 定期清理舊數據
sqlite3 data/lottery_prediction.db "
DELETE FROM prediction_results 
WHERE created_at < datetime('now', '-90 days');
"

# 優化數據庫大小
sqlite3 data/lottery_prediction.db "VACUUM;"
```

## 🎯 管理命令

### 服務管理
```bash
# 查看服務狀態
python deploy.py status

# 停止所有服務
python deploy.py stop

# 重啟服務
docker-compose restart

# 更新服務
docker-compose pull && docker-compose up -d
```

### 維護操作
```bash
# 清理未使用的 Docker 資源
docker system prune -f

# 查看磁盤使用
du -sh data/ logs/ backups/

# 輪轉日誌
python -c "
from phase3.production_deployment_system import LogManager, DeploymentConfig
config = DeploymentConfig.load_from_file('deploy_config.yaml')
log_manager = LogManager(config)
log_manager.rotate_logs()
"
```

### 健康檢查
```bash
# 運行完整健康檢查
python deploy.py status

# 檢查特定服務
curl http://localhost:8000/health

# 檢查監控指標
curl http://localhost:8000/metrics
```

## 📈 擴展部署

### 高可用部署

對於生產環境高可用部署，建議：

1. **負載均衡**: 多個 API 實例
2. **數據庫集群**: PostgreSQL 或 MySQL 集群
3. **緩存集群**: Redis Cluster
4. **文件存儲**: 共享文件系統或對象存儲
5. **監控集群**: Prometheus 集群 + Grafana HA

### Docker Swarm 部署

```bash
# 初始化 Swarm
docker swarm init

# 部署 Stack
docker stack deploy -c docker-compose.prod.yml lottery-system
```

### Kubernetes 部署

提供 Kubernetes 清單文件 (`k8s/`) 支持容器編排平台部署。

## 📞 技術支持

### 系統監控聯繫方式
- **Grafana 警報**: 配置郵件或 Slack 通知
- **日誌監控**: ELK Stack 異常檢測
- **健康檢查**: API 端點監控

### 維護窗口
建議維護窗口：
- **每日**: 凌晨 2-4 點 (自動備份時間)
- **每週**: 週日凌晨 (系統更新)
- **每月**: 第一個週日 (深度維護)

---

**🎉 部署完成後，您的彩票預測系統將在生產環境中穩定運行！**

如需更多技術支持，請查看 API 文檔 (http://localhost:8000/docs) 或系統日誌。