# 彩票預測系統 - 功能說明與位置指南

## 🎯 系統概述

本系統是一個完整的彩票預測平台，支援威力彩、大樂透、今彩539三種台灣彩票類型，具備多重預測算法、數據分析、Web界面等功能。

---

## 🚀 啟動方式

### 主要啟動點
```bash
python start_app.py
```
- **位置**: `/start_app.py`
- **功能**: 統一啟動腳本，自動檢查依賴、創建目錄、配置端口
- **訪問地址**: `http://localhost:7890` (或自動分配的可用端口)

### Web應用入口
```bash
python web/app.py
```
- **位置**: `/web/app.py`
- **功能**: Flask Web應用主程序
- **端口**: 7890 (可在代碼中修改)

---

## 📊 核心功能模組

### 1. 預測引擎

#### 🤖 改進預測器 (主要預測邏輯)
- **位置**: `/improved_predictor.py`
- **功能**: 
  - 整合多種預測算法
  - 自動期號計算
  - 信心度評估
  - 預測結果保存
- **支援方法**:
  - 頻率分析預測
  - 趨勢分析預測
  - 機器學習預測
  - 綜合分析預測

#### 🧠 集成預測器
- **位置**: `/prediction/integrated_predictor.py`
- **功能**: 機器學習模型預測

#### 📈 板路分析器
- **位置**: `/enhanced_board_analysis.py`
- **功能**: 傳統彩票板路分析

### 2. 數據管理

#### 💾 數據庫管理器
- **位置**: `/data/db_manager.py`
- **功能**: 
  - SQLite資料庫連接管理
  - 表格創建與維護
  - 事務處理

#### 🌐 即時數據更新器
- **位置**: `/data/real_lottery_updater.py`
- **功能**:
  - 從台灣彩券官網抓取最新開獎結果
  - 多重重試機制
  - 備用數據源
  - 自動保存到資料庫

#### 🔄 備用數據生成器
- **位置**: `/data/fallback_data_generator.py`
- **功能**:
  - 當網路失敗時生成模擬開獎數據
  - 確保系統100%可用性
  - 遵循彩票規則生成合理數據

### 3. 分析工具

#### 📊 預測分析器
- **位置**: `/analysis/prediction_analyzer.py`
- **功能**: 
  - 預測準確度分析
  - 歷史預測統計
  - 性能評估報告

#### 🔍 數據統計分析
- **位置**: `/analysis/statistics_analyzer.py`
- **功能**: 號碼頻率、趨勢等統計分析

---

## 🌐 Web界面功能

### 主要頁面

#### 🏠 首頁 / 儀表板
- **路由**: `/`
- **模板**: `/web/templates/index.html`
- **功能**: 系統狀態概覽、最新預測展示

#### 🤖 預測頁面
- **路由**: `/predict`
- **模板**: `/web/templates/predict.html`
- **功能**: 
  - 選擇彩票類型進行預測
  - 即時生成預測結果
  - 顯示預測信心度和分析詳情

#### 📈 預測記錄
- **路由**: `/predictions`
- **模板**: `/web/templates/predictions.html`
- **功能**: 查看歷史預測記錄

#### 📊 分析報告
- **路由**: `/analysis`
- **模板**: `/web/templates/analysis.html`
- **功能**: 預測準確度統計和分析

#### 🔢 號碼分析
- **路由**: `/numbers`
- **模板**: `/web/templates/numbers.html`
- **功能**: 號碼頻率、熱號冷號分析

### API端點

#### 預測相關API
```
GET  /api/prediction/{lottery_type}     - 生成新預測
GET  /api/latest_predictions/{type}     - 獲取最新預測
GET  /api/prediction_history/{type}     - 獲取預測歷史
```

#### 數據相關API
```
GET  /api/latest_results/{lottery_type} - 獲取最新開獎結果
POST /api/update_data                   - 手動更新開獎數據
GET  /api/statistics/{lottery_type}     - 獲取統計數據
```

#### 系統狀態API
```
GET  /api/system/status                 - 系統狀態檢查
GET  /api/system/health                 - 健康狀態檢查
```

---

## 🗃️ 資料庫結構

### 開獎結果表
- **Powercolor** - 威力彩開獎記錄
- **Lotto649** - 大樂透開獎記錄  
- **DailyCash** - 今彩539開獎記錄

### 預測結果表
- **PowercolorPredictions** - 威力彩預測記錄
- **Lotto649Predictions** - 大樂透預測記錄
- **DailyCashPredictions** - 今彩539預測記錄

### 資料庫位置
- **檔案**: `/data/lottery.db` (SQLite)
- **初始化**: 系統首次啟動時自動創建

---

## ⚙️ 配置文件

### 主配置文件
- **位置**: `/config.json`
- **內容**: 
  - 資料庫連接設定
  - API超時設定
  - 預測參數配置
  - 日誌設定

### 配置管理器
- **位置**: `/config_manager.py`
- **功能**: 統一配置文件讀取和管理

---

## 📝 日誌系統

### 日誌位置
- **應用日誌**: `/logs/app_startup_YYYYMMDD.log`
- **Web日誌**: `/web/logs/`
- **預測日誌**: 控制台輸出 + 檔案記錄

### 日誌級別
- **INFO**: 正常操作記錄
- **WARNING**: 警告信息（如網路重試）
- **ERROR**: 錯誤信息
- **DEBUG**: 調試詳情

---

## 🔧 維護與故障排除

### 常見問題解決

#### 1. 預測失敗
- **檢查**: `/improved_predictor.py` 日誌輸出
- **可能原因**: 數據格式問題、網路連接失敗
- **解決方案**: 系統會自動使用備用數據生成器

#### 2. 網路連接問題
- **位置**: `/data/real_lottery_updater.py`
- **機制**: 自動重試 + 多重數據源 + 備用生成器
- **狀態**: 系統保證100%可用性

#### 3. 端口佔用
- **處理**: `start_app.py` 自動檢測並分配可用端口
- **範圍**: 7890-7989

#### 4. 資料庫問題
- **檢查**: `/data/db_manager.py`
- **備份**: 系統自動維護資料庫完整性

### 系統狀態檢查
```bash
# 檢查系統狀態
curl http://localhost:7890/api/system/status

# 檢查健康狀態  
curl http://localhost:7890/api/system/health
```

---

## 🚦 系統狀態指示

### 運行狀態
- ✅ **正常**: 所有功能運作正常
- ⚠️ **警告**: 網路連接問題，使用備用數據
- ❌ **錯誤**: 系統故障，需要檢查日誌

### 數據狀態
- 🌐 **即時數據**: 從官網獲取的最新數據
- 🔄 **備用數據**: 模擬生成的數據（標記為 `is_simulated: true`）

---

## 📋 開發與擴展

### 添加新預測算法
1. 在 `/improved_predictor.py` 中添加新方法
2. 在 `generate_multiple_predictions()` 中註冊
3. 設定適當的信心度權重

### 添加新彩票類型
1. 在 `lottery_config` 中添加配置
2. 在 `/data/real_lottery_updater.py` 中添加API配置
3. 創建對應的資料庫表格

### 自定義Web頁面
1. 在 `/web/templates/` 中創建新模板
2. 在 `/web/app.py` 中添加路由
3. 使用統一的 `navbar.html` 導航模板

---

## 📞 技術支援

### 開發團隊聯繫
- **系統架構**: 改進預測器設計
- **數據處理**: 即時更新與備用機制
- **Web界面**: Bootstrap 5.2.3 響應式設計
- **數據分析**: 多重算法整合

### 版本信息
- **當前版本**: 整合版 v2.0
- **最後更新**: 2025-07-23
- **支援彩票**: 威力彩、大樂透、今彩539
- **可用性**: 100% (含備用機制)

---

*本系統具備完整的錯誤處理和備用機制，確保在任何情況下都能正常提供預測服務。*