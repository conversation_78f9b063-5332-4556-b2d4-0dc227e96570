# 彩票預測系統部署指南

版本: v1.0 | 最後更新: 2025-07-23

## 系統概述

彩票預測系統是一個基於機器學習的預測分析平台，支持威力彩、大樂透、今彩539的智能預測。

### 核心功能
- 多種預測方法分類和分析
- 預測準確度自動追蹤和評估  
- 智能推薦引擎
- 實時效果監控儀表板
- 自動化準確度更新

### 技術架構
- **後端**: Python Flask + SQLite
- **前端**: Bootstrap + Chart.js
- **數據庫**: SQLite
- **機器學習**: scikit-learn, pandas, numpy

## 快速部署

### 系統要求
- Python 3.8+
- 記憶體: 2GB+ (推薦4GB)
- 磁盤: 5GB+ (推薦20GB)
- 網路: 穩定連接

### 一鍵安裝
```bash
# 1. 克隆代碼
git clone <repository> lottery_prediction_system
cd lottery_prediction_system

# 2. 安裝依賴
pip install -r requirements.txt

# 3. 初始化數據庫
python -c "from data.db_manager import DBManager; DBManager()"

# 4. 運行測試
python system_test.py

# 5. 啟動服務
python web/app.py
```

訪問: http://localhost:7890

## 生產環境部署

### Docker 部署 (推薦)
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY . .
RUN pip install -r requirements.txt
EXPOSE 7890
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:7890", "web.app:app"]
```

```bash
docker build -t lottery-prediction .
docker run -d -p 7890:7890 -v $(pwd)/data:/app/data lottery-prediction
```

### 手動部署
```bash
# 安裝生產環境依賴
pip install gunicorn supervisor

# 啟動服務
gunicorn -w 4 -b 0.0.0.0:7890 web.app:app
```

## 配置管理

### 環境變量
```bash
export FLASK_ENV=production
export DB_PATH=/app/data/lottery_data.db
export LOG_LEVEL=INFO
```

### 配置文件
```json
{
  "web": {"host": "0.0.0.0", "port": 7890, "debug": false},
  "database": {"path": "data/lottery_data.db"},
  "automation": {"enabled": true, "interval": 3600}
}
```

## 監控和維護

### 健康檢查
```bash
curl http://localhost:7890/api/health
curl http://localhost:7890/api/automation/status
```

### 日誌監控
```bash
# 查看系統日誌
tail -f logs/system.log

# 查看錯誤日誌
grep ERROR logs/system.log
```

### 備份
```bash
# 數據備份
cp data/lottery_data.db backup/lottery_data_$(date +%Y%m%d).db

# 自動備份 (crontab)
0 2 * * * /path/to/backup.sh
```

## 故障排除

### 常見問題
1. **端口占用**: `lsof -i :7890; kill <PID>`
2. **數據庫鎖定**: 檢查文件權限和並發訪問
3. **內存不足**: 調整gunicorn worker數量
4. **預測失敗**: 檢查網路連接

### 性能優化
- 啟用數據庫索引
- 配置Redis緩存
- 使用CDN加速靜態資源
- 調整worker和thread數量

### 安全配置
```bash
# 創建專用用戶
useradd -r lottery

# 設置權限
chown -R lottery:lottery /app
chmod 750 /app

# 防火牆
ufw allow 7890/tcp
```

## 升級指南

```bash
# 1. 備份數據
./backup.sh

# 2. 停止服務
pkill -f gunicorn

# 3. 更新代碼
git pull

# 4. 更新依賴
pip install -r requirements.txt --upgrade

# 5. 數據庫遷移
python system_test.py

# 6. 重啟服務
gunicorn -w 4 -b 0.0.0.0:7890 web.app:app
```

## API文檔

### 核心端點
- `GET /api/health` - 系統健康狀態
- `POST /api/accuracy_update/<type>` - 更新預測準確度
- `GET /api/method_recommendation/<type>` - 獲取方法推薦
- `GET /api/automation/status` - 自動化狀態

### 頁面路由
- `/` - 主頁
- `/prediction_performance_dashboard` - 效果儀表板
- `/prediction_method_analysis` - 方法分析

## 支援資源

- 系統測試: `python system_test.py`
- 部署檢查清單: 見附錄A
- 性能基準: 見附錄B
- 故障排除指南: 見附錄C

---
部署支援: <EMAIL>