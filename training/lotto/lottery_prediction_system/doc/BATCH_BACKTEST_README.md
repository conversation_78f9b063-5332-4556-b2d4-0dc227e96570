# 批次回測功能說明

## 📊 功能概述

批次回測功能提供了使用**單一模型/標準**對彩票預測進行歷史回測分析的能力。與之前的多方法組合不同，此功能專注於評估單一預測方法的表現。

## 🎯 主要特點

- **單一模型預測**: 每次只使用一種預測方法生成一組號碼
- **多遊戲支援**: 支援威力彩、大樂透、今彩539
- **多種預測方法**: 頻率分析、模式分析、連號分析、隨機基準
- **詳細統計報告**: 命中率、中獎率、獎項分佈等
- **Web界面**: 直觀的網頁操作界面
- **結果保存**: 自動保存回測結果供後續查看

## 📁 文件結構

```
lottery_prediction_system/
├── batch_backtest.py          # 核心批次回測引擎
├── batch_backtest_web.py      # Web界面
├── run_batch_backtest.py      # 命令行執行腳本
├── backtest_results/          # 回測結果保存目錄
└── BATCH_BACKTEST_README.md   # 本說明文件
```

## 🚀 快速開始

### 方法1: 使用Web界面 (推薦)

```bash
# 啟動Web界面
streamlit run batch_backtest_web.py
```

然後在瀏覽器中操作：
1. 選擇遊戲類型和預測方法
2. 設定測試期間和訓練窗口
3. 點擊"開始回測"
4. 查看結果和統計圖表
5. 下載詳細結果

### 方法2: 使用命令行腳本

```bash
# 執行互動式回測
python run_batch_backtest.py
```

選項說明：
- **1. 快速測試**: 使用預設參數進行小範圍測試
- **2. 單一回測**: 自訂參數執行單次回測
- **3. 多方法比較**: 比較不同預測方法的表現
- **4. 啟動Web界面**: 開啟網頁界面

### 方法3: 直接使用API

```python
from batch_backtest import BatchBacktester, BacktestConfig, GameType, PredictionMethod

# 創建配置
config = BacktestConfig(
    game_type=GameType.POWERCOLOR,
    method=PredictionMethod.FREQUENCY_ANALYSIS,
    start_period="2024001",
    end_period="2024050",
    training_window=30
)

# 執行回測
backtester = BatchBacktester(config)
results = backtester.run_backtest()

# 保存結果
filepath = backtester.save_results()
print(f"結果已儲存至: {filepath}")
```

## 🎮 支援的遊戲類型

### 威力彩 (PowerColor)
- **主要號碼**: 1-38選6個
- **特別號**: 1-8選1個
- **格式**: n1,n2,n3,n4,n5,n6+special

### 大樂透 (Lotto649)
- **主要號碼**: 1-49選6個
- **特別號**: 1-49選1個
- **格式**: n1,n2,n3,n4,n5,n6+special

### 今彩539 (DailyCash)
- **主要號碼**: 1-39選5個
- **無特別號**
- **格式**: n1,n2,n3,n4,n5

## 🔍 預測方法說明

### 1. 頻率分析 (Frequency Analysis)
- **原理**: 統計歷史開獎號碼的出現頻率
- **預測**: 選擇出現頻率最高的號碼
- **適用**: 相信"熱號"會持續出現的策略
- **信心度**: 基於訓練數據量調整 (最高80%)

### 2. 模式分析 (Pattern Analysis)
- **原理**: 分析號碼的奇偶比例、和值範圍等模式
- **預測**: 基於歷史模式生成符合特徵的號碼組合
- **適用**: 相信開獎有一定規律可循
- **信心度**: 固定60%

### 3. 連號分析 (Consecutive Analysis)
- **原理**: 統計連續號碼的出現情況
- **預測**: 考慮連號機率生成號碼
- **適用**: 關注連續號碼出現頻率的策略
- **信心度**: 固定50%

### 4. 隨機基準 (Random Baseline)
- **原理**: 完全隨機選號
- **預測**: 每次隨機生成號碼組合
- **適用**: 作為對照組，驗證其他方法是否優於隨機
- **信心度**: 固定10%

## 📊 結果分析指標

### 基本統計
- **總測試期數**: 回測覆蓋的開獎期數
- **總命中數**: 所有期數中命中號碼的總數
- **平均命中數**: 每期平均命中的號碼個數
- **號碼準確率**: 命中號碼數 / (總期數 × 每期號碼數)

### 中獎統計
- **中獎率**: 有中獎的期數 / 總期數
- **獎項分佈**: 各等獎的中獎次數統計
- **特別號命中率**: 特別號命中次數 / 總期數 (適用於威力彩和大樂透)

### 預測品質
- **平均信心度**: 預測時的平均信心度百分比
- **命中分佈**: 0個、1個、2個...命中的期數分佈

## 📈 結果文件格式

回測結果保存為JSON格式，包含以下結構：

```json
{
  "config": {
    "game_type": "powercolor",
    "method": "frequency_analysis",
    "test_period": "2024001 - 2024050",
    "training_window": 30
  },
  "statistics": {
    "total_periods": 50,
    "accuracy": 16.67,
    "winning_rate": 12.0,
    "average_confidence": 65.4
  },
  "prize_distribution": {
    "prize_1": 0,
    "prize_2": 0,
    "prize_3": 1,
    ...
  },
  "match_distribution": {
    "matches_0": 15,
    "matches_1": 20,
    "matches_2": 10,
    ...
  },
  "results": [
    {
      "period": "2024001",
      "predicted": [5, 12, 18, 25, 31, 37, 3],
      "actual": [2, 8, 15, 22, 28, 35, 1],
      "matches": 0,
      "special_match": false,
      "prize_level": 0,
      "confidence": 68.2
    },
    ...
  ]
}
```

## 🔧 配置參數說明

### BacktestConfig 參數

- **game_type**: 遊戲類型 (PowerColor/Lotto649/DailyCash)
- **method**: 預測方法 (frequency_analysis/pattern_analysis/consecutive_analysis/random_baseline)
- **start_period**: 測試開始期數 (格式: 114000### 例如 114000030，表示民國114年第30期)
- **end_period**: 測試結束期數 (格式: 114000### 例如 114000058，表示民國114年第58期)
- **training_window**: 訓練窗口大小 (用於預測的歷史期數，建議10-100)
- **confidence_threshold**: 信心度閾值 (暫未使用，預留功能)

## 🎯 使用建議

### 測試期間選擇
- **短期測試**: 10-20期，快速驗證方法
- **中期測試**: 50-100期，較可靠的統計結果
- **長期測試**: 200+期，全面評估方法表現

### 訓練窗口設定
- **小窗口 (10-20期)**: 反應快，但可能不穩定
- **中窗口 (30-50期)**: 平衡穩定性和反應速度
- **大窗口 (50+期)**: 穩定，但反應較慢

### 方法選擇建議
1. **先跑隨機基準**: 了解隨機選號的基準表現
2. **嘗試頻率分析**: 最直觀的預測方法
3. **比較不同方法**: 找出最適合特定遊戲的方法
4. **關注長期表現**: 避免被短期好運誤導

## ⚠️ 注意事項

1. **僅供研究**: 此功能僅用於學術研究和興趣探索，不建議用於實際投注
2. **隨機性質**: 彩票本質是隨機的，任何預測方法都無法保證準確性
3. **歷史表現**: 過去的表現不代表未來的結果
4. **資料依賴**: 結果準確性依賴於歷史資料的完整性和正確性
5. **計算時間**: 大範圍回測可能需要較長時間，請耐心等待

## 🐛 故障排除

### 常見問題

**Q: 回測時顯示"未找到歷史數據"**
A: 檢查數據庫文件 `data/lottery.db` 是否存在，以及期數格式是否正確

**Q: Web界面無法啟動**
A: 確保已安裝streamlit：`pip install streamlit`

**Q: 回測結果不理想**
A: 這是正常的，彩票預測本身就很困難，可以嘗試不同的方法和參數

**Q: 程式執行很慢**
A: 可以減少測試期間範圍或訓練窗口大小來加速

### 日誌檢查

程式運行時會輸出詳細日誌，如遇問題可查看：
- 控制台輸出的錯誤信息
- 數據庫連接狀態
- 預測過程中的異常

## 📞 技術支援

如有問題或建議，請查看：
1. 本README文件
2. 程式內的註釋和文檔字符串
3. 運行時的錯誤日誌輸出

---

*最後更新: 2025年7月24日*