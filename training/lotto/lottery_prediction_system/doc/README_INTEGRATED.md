# 彩票預測系統 - 整合版本

## 🎯 系統概述

這是一個完整的彩票預測系統，整合了機器學習算法和板路分析法，提供分離式預測結果和成功原因分析，並支援每日自動更新和分析。

### 🆕 新增功能

1. **分離式預測** - 機器學習和板路分析獨立運行並記錄各自的預測理由
2. **預測成功分析** - 追蹤和分析為什麼某些預測會成功
3. **每日自動更新** - 定期自動下載開獎結果、更新資料庫、分析預測準確度
4. **策略優化** - 基於成功分析結果動態調整預測策略和參數
5. **優化的結果展示** - 清楚顯示兩種方法的獨立預測和成功原因分析

## 🏗️ 系統架構

```
lottery_prediction_system/
├── prediction/
│   ├── integrated_predictor.py      # 整合預測器（新增）
│   ├── lottery_predictor.py         # 機器學習預測器
│   ├── board_path_engine.py         # 板路分析引擎
│   └── prediction_result.py         # 預測結果管理
├── display/
│   └── prediction_display.py        # 預測結果展示（新增）
├── strategy/
│   └── strategy_optimizer.py        # 策略優化器（新增）
├── daily_automation.py              # 每日自動化任務（新增）
├── test_integrated_system.py        # 整合系統測試（新增）
└── main.py                          # 主程式（已更新）
```

## 🚀 快速開始

### 1. 測試整合系統

```bash
# 運行整合系統測試
python test_integrated_system.py
```

### 2. 分離式預測

```bash
# 啟動主程式
python main.py

# 選擇選項 12: 分離式預測 (機器學習 + 板路分析)
```

### 3. 每日自動化任務

```bash
# 立即執行一次完整任務
python daily_automation.py --run-once

# 啟動定時任務調度器（每日8:00和22:00執行）
python daily_automation.py --schedule
```

## 📊 功能詳解

### 1. 分離式預測

**特點：**
- 機器學習和板路分析獨立運行
- 各自記錄預測理由和信心分數
- 提供多個候選結果
- 分析歷史成功預測模式

**使用方式：**
```python
from prediction.integrated_predictor import IntegratedPredictor
from data.db_manager import DBManager

db_manager = DBManager()
predictor = IntegratedPredictor(db_manager)
predictor.initialize_board_path_engines()

results = predictor.predict_separated(
    lottery_type='powercolor',
    candidates_count=5,
    min_confidence=0.5
)
```

### 2. 預測成功分析

**分析內容：**
- 機器學習成功因素（模型版本、信心分數、預測時間等）
- 板路分析成功因素（板路模式、號碼熱度等）
- 共同成功模式（期數模式、星期模式、月份模式等）

**使用方式：**
```python
from prediction.integrated_predictor import PredictionSuccessAnalyzer

analyzer = PredictionSuccessAnalyzer(db_manager)
success_analysis = analyzer.analyze_successful_predictions('powercolor')
```

### 3. 策略優化

**優化項目：**
- 機器學習和板路分析的權重調整
- 候選數量動態調整
- 信心分數閾值優化
- 時間窗口調整

**使用方式：**
```python
from strategy.strategy_optimizer import StrategyOptimizer

optimizer = StrategyOptimizer(db_manager)
optimized_strategy = optimizer.optimize_strategy('powercolor', success_analysis)
```

### 4. 每日自動化

**自動化任務：**
- 更新開獎結果
- 分析預測準確度
- 生成新的預測
- 生成每日報告

**設置定時任務：**
```bash
# 使用內建調度器
python daily_automation.py --schedule

# 或使用系統cron（Linux/Mac）
# 編輯 crontab -e
0 8 * * * cd /path/to/lottery_prediction_system && python daily_automation.py --run-once
0 22 * * * cd /path/to/lottery_prediction_system && python daily_automation.py --run-once
```

## 🎮 主程式新選項

在主程式選單中新增了以下選項：

- **選項 12**: 分離式預測 (機器學習 + 板路分析)
- **選項 13**: 執行每日自動化任務

## 📈 預測結果展示

### 分離式預測結果示例

```
================================================================================
威力彩 分離式預測結果
================================================================================
預測期數: 114000049
預測時間: 2025-06-20 15:30:00
================================================================================

【機器學習預測】🤖
方法: machine_learning
版本: v2.0
候選數量: 3

  候選 #1 (信心分數: 0.750) ⭐
    第一區: [5, 12, 18, 25, 33, 38]
    第二區: 3
    預測理由:
      • 特徵分析顯示高機率區間
      • 號碼分佈均衡
      • 使用 v2.0 版本模型預測

【板路分析預測】📊
方法: board_path_analysis
版本: v2.0
候選數量: 3

  候選 #1 (信心分數: 0.680) 🔸
    第一區: [3, 8, 15, 22, 28, 35]
    第二區: 5
    板路分析:
      • 基於歷史開獎板路分析
      • 板路顯示號碼分佈平衡
      • 中高信心度預測，主要板路指標支持

【成功分析】📈
  🤖 機器學習成功因素:
    • 最佳模型版本: v2.0 (成功 3 次)
    • 成功預測平均信心分數: 0.720

  📊 板路分析成功因素:
    • 成功板路模式: 號碼熱度分析 (出現 2 次)
    • 匹配 3 個號碼: 1 次

  🎯 共同成功模式:
    • 期數尾數 48: 成功 1 次
    • 最佳預測星期: Monday (成功 2 次)
```

## 🔧 配置說明

### 策略參數

每種彩票類型都有獨立的策略參數：

```python
{
    'ml_weight': 0.6,              # 機器學習權重
    'board_path_weight': 0.4,      # 板路分析權重
    'candidates_count': 5,         # 候選數量
    'min_confidence': 0.5,         # 最低信心分數
    'time_window_days': 30         # 分析時間窗口（天）
}
```

### 日誌配置

系統會自動生成以下日誌文件：

- `logs/integrated_predictor.log` - 整合預測器日誌
- `logs/daily_automation_YYYYMMDD.log` - 每日自動化任務日誌
- `logs/integrated_test_YYYYMMDD.log` - 系統測試日誌

## 📁 輸出文件

### 分析結果

- `analysis_results/success_analysis_*.json` - 成功分析結果
- `analysis_results/daily_analysis_*.json` - 每日分析結果
- `analysis_results/daily_report_*.txt` - 每日報告
- `analysis_results/separated_prediction_*.txt` - 分離式預測報告

### 策略優化

- `analysis_results/current_strategies.json` - 當前策略參數
- `analysis_results/strategy_optimization_history_*.json` - 策略優化歷史

## 🧪 測試和驗證

### 運行完整測試

```bash
python test_integrated_system.py
```

測試項目包括：
1. 顯示功能測試
2. 成功分析測試
3. 策略優化測試
4. 分離式預測測試
5. 每日自動化測試

### 測試結果解讀

- ✅ 通過：功能正常運行
- ⚠️ 警告：功能運行但可能缺少數據
- ❌ 失敗：功能無法正常運行

## 🔍 故障排除

### 常見問題

1. **預測失敗**
   - 檢查是否有足夠的歷史數據
   - 確認模型文件是否存在
   - 查看日誌文件了解詳細錯誤

2. **板路分析無結果**
   - 確認板路分析引擎初始化成功
   - 檢查歷史數據的完整性

3. **自動化任務失敗**
   - 檢查網路連接（用於下載開獎結果）
   - 確認資料庫文件權限
   - 查看自動化任務日誌

### 重置系統

```python
# 重置策略為默認值
from strategy.strategy_optimizer import StrategyOptimizer
optimizer = StrategyOptimizer(db_manager)
optimizer.reset_strategy('powercolor')
optimizer.save_strategies()
```

## 📞 支援

如有問題，請查看：
1. 日誌文件中的詳細錯誤信息
2. 運行測試腳本診斷問題
3. 檢查系統依賴和配置

## 🎉 總結

這個整合版本提供了：
- ✅ 分離式預測，讓機器學習和板路分析獨立運行
- ✅ 詳細的成功原因分析
- ✅ 自動化的每日更新和分析
- ✅ 智能的策略優化
- ✅ 清晰的結果展示

系統現在能夠更好地理解預測成功的原因，並根據歷史表現動態調整預測策略，提供更可靠和透明的預測服務。
