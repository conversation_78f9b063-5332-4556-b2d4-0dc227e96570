# 批次回測管理器使用指南

## 📋 系統概述

批次回測管理器是一個完整的彩票預測回測系統，提供易用的操作界面、完整的結果儲存和強大的分析功能。

## 🎯 主要特點

### ✅ 完整的回測流程
- **互動式操作**: 引導式參數設定，無需程式知識
- **多重驗證**: 自動檢查參數有效性和資料完整性
- **即時回饋**: 執行過程中提供詳細狀態資訊

### ✅ 智能結果管理
- **自動儲存**: 結果自動保存為JSON格式和SQLite資料庫
- **歷史追蹤**: 完整記錄每次回測的參數和結果
- **快速查詢**: 支援多種篩選和排序方式

### ✅ 深度分析功能
- **方法比較**: 自動比較多種預測方法的表現
- **趨勢分析**: 視覺化命中趨勢和分佈統計
- **性能評估**: 多維度評估預測方法效果

## 🚀 快速開始

### 方法1: 命令行界面 (推薦新手)

```bash
# 啟動互動式管理器
python backtest_manager.py
```

**操作流程：**
1. 選擇"1. 執行新回測"
2. 按提示選擇遊戲類型和預測方法
3. 設定測試期間和訓練窗口
4. 確認設定後執行回測
5. 查看結果摘要和詳細分析

### 方法2: Web界面 (推薦高級用戶)

```bash
# 啟動Web界面
streamlit run backtest_web_manager.py
```

**功能頁面：**
- **🔄 執行回測**: 參數設定和回測執行
- **📊 查看歷史**: 歷史記錄瀏覽和篩選
- **📈 結果分析**: 深度分析和圖表展示
- **🔍 方法比較**: 多方法對比分析
- **🧹 系統管理**: 檔案管理和系統維護

### 方法3: 測試驗證

```bash
# 執行系統測試
python test_backtest_manager.py
```

## 📊 操作詳解

### 回測參數設定

#### 🎮 遊戲類型選擇
- **威力彩**: 1-38選6個 + 1-8特別號
- **大樂透**: 1-49選6個 + 1-49特別號
- **今彩539**: 1-39選5個

#### 🔍 預測方法選擇
1. **頻率分析**: 基於歷史號碼出現頻率
   - 適用於相信"熱號"策略的用戶
   - 信心度: 根據數據量動態調整 (最高80%)

2. **模式分析**: 基於奇偶比例和和值範圍
   - 分析號碼分佈模式
   - 信心度: 固定60%

3. **連號分析**: 基於連續號碼出現規律
   - 考慮相鄰號碼組合
   - 信心度: 固定50%

4. **隨機基準**: 完全隨機選號
   - 作為對照組使用
   - 信心度: 固定10%

#### 📅 期間設定
- **格式**: 114000### (民國114年第###期)
- **範圍建議**:
  - 短期測試: 10-20期 (快速驗證)
  - 中期測試: 30-50期 (平衡可靠性)
  - 長期測試: 50+期 (全面評估)

#### 🔧 訓練窗口
- **小窗口 (5-15期)**: 反應快，適合短期趨勢
- **中窗口 (15-30期)**: 平衡穩定性和靈敏度
- **大窗口 (30+期)**: 穩定，適合長期模式

### 結果解讀

#### 📈 統計指標
- **總期數**: 回測覆蓋的開獎期數
- **號碼準確率**: 命中號碼數 ÷ (總期數 × 每期號碼數)
- **中獎率**: 有中獎的期數 ÷ 總期數
- **平均命中數**: 每期平均命中的號碼個數
- **平均信心度**: 預測時的平均信心水準

#### 🎯 命中分佈
顯示0個、1個、2個...命中的期數分佈，幫助了解預測穩定性。

#### 🏆 獎項分析
- 各等獎中獎次數統計
- 獎項分佈比例分析
- 特別號命中情況（威力彩/大樂透）

## 💾 結果儲存說明

### 檔案結構
```
backtest_results/
├── backtest_history.db          # SQLite歷史記錄資料庫
├── powercolor_backtest_report_20250724_*.json  # 詳細結果檔案
├── lotto649_backtest_report_*.json
└── dailycash_backtest_report_*.json
```

### JSON結果格式
```json
{
  "config": {
    "game_type": "powercolor",
    "method": "frequency_analysis",
    "test_period": "114000030 - 114000058",
    "training_window": 20
  },
  "statistics": {
    "total_periods": 29,
    "total_matches": 45,
    "accuracy": 25.86,
    "winning_rate": 6.90,
    "average_confidence": 65.2
  },
  "results": [
    {
      "period": "114000030",
      "predicted": [5, 12, 18, 25, 31, 37, 3],
      "actual": [2, 8, 15, 22, 28, 35, 1],
      "matches": 0,
      "prize_level": 0,
      "confidence": 68.2
    }
  ]
}
```

### 歷史記錄資料庫
- **表結構**: 包含所有回測的基本資訊和統計結果
- **查詢功能**: 支援按遊戲類型、方法、時間等多種方式篩選
- **關聯性**: 與JSON詳細結果檔案關聯，可快速載入完整資料

## 🔧 進階功能

### 方法比較分析
自動執行多種預測方法的對比測試：

1. **批次執行**: 自動測試所有預測方法
2. **結果對比**: 生成詳細的比較表格和圖表
3. **最佳推薦**: 基於多個指標推薦最適合的方法
4. **結果保存**: 每個方法的結果都會保存到歷史記錄

### 歷史結果管理
- **篩選查詢**: 按遊戲類型、預測方法、時間範圍篩選
- **詳細載入**: 載入任何歷史記錄的完整詳細結果
- **統計概覽**: 快速查看系統使用統計和平均表現

### 系統維護
- **檔案清理**: 自動清理指定天數前的舊結果檔案
- **空間管理**: 監控儲存空間使用情況
- **備份建議**: 重要結果的備份和匯出功能

## 📱 使用場景

### 🔬 研究分析
- **學術研究**: 比較不同預測方法的統計表現
- **模式探索**: 發現數字分佈的潛在規律
- **方法驗證**: 驗證新預測思路的有效性

### 🎯 個人使用
- **方法選擇**: 找出最適合特定遊戲的預測方法
- **參數優化**: 測試不同訓練窗口的效果
- **長期追蹤**: 監控預測方法的長期表現

### 📊 數據分析
- **趨勢分析**: 觀察預測準確率的時間趨勢
- **分佈研究**: 分析命中數的統計分佈
- **相關性探索**: 探索不同參數對結果的影響

## ⚠️ 注意事項

### 🔒 使用限制
1. **僅供研究**: 本系統僅用於學術研究和興趣探索
2. **隨機性質**: 彩票本質是隨機的，任何預測都無法保證準確性
3. **歷史表現**: 過去的結果不代表未來的表現
4. **數據依賴**: 結果準確性依賴於歷史資料的完整性

### 💡 使用建議
1. **多方法比較**: 不要依賴單一預測方法
2. **長期觀察**: 關注長期趨勢而非短期波動
3. **參數實驗**: 嘗試不同的訓練窗口和測試範圍
4. **理性分析**: 保持客觀態度，避免過度解讀結果

### 🛠️ 故障排除

#### 常見問題
**Q: 執行回測時顯示"未找到歷史數據"**
- A: 檢查資料庫檔案路徑和期數格式是否正確

**Q: Web界面無法啟動**
- A: 確保已安裝所需套件：`pip install streamlit plotly`

**Q: 回測結果都是0**
- A: 可能是期數範圍過小或資料不足，嘗試擴大測試範圍

**Q: 系統運行緩慢**
- A: 減少測試期間範圍或訓練窗口大小

#### 技術支援
- 查看控制台錯誤訊息
- 檢查資料庫連接狀態
- 驗證期數格式正確性
- 確認依賴套件完整安裝

## 🔄 更新記錄

**v1.0 (2025-07-24)**
- ✅ 完整的回測功能實現
- ✅ 互動式命令行界面
- ✅ 現代化Web界面
- ✅ 完整的結果儲存和管理
- ✅ 多方法比較分析
- ✅ 100%測試覆蓋率

---

**🎯 開始使用**
```bash
# 命令行版本
python backtest_manager.py

# Web版本  
streamlit run backtest_web_manager.py

# 系統測試
python test_backtest_manager.py
```

祝你使用愉快！🎉