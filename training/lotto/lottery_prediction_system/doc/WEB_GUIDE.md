# 彩票預測系統 - Web界面使用指南

## 🌐 Web界面概覽

彩票預測系統現在提供了完整的Web界面，讓您可以通過瀏覽器輕鬆使用所有功能，包括新的分離式預測、成功分析和自動化任務。

## 🚀 快速啟動

### 方法一：使用啟動腳本（推薦）

```bash
# 進入系統目錄
cd training/lotto/lottery_prediction_system

# 運行啟動腳本
python start_web.py
```

### 方法二：手動啟動

```bash
# 確保依賴已安裝
pip install flask schedule

# 創建必要目錄
mkdir -p logs analysis_results test_results web/logs

# 啟動Web應用
python web/app.py
```

### 訪問地址

Web界面將在以下地址啟動：
- **本地訪問**: http://127.0.0.1:5001
- **網路訪問**: http://[您的IP]:5001

## 📱 功能頁面

### 1. 首頁 (/)
- **功能**: 系統概覽和快速導航
- **特色**: 
  - 各彩票類型快速入口
  - 新功能介紹（分離式預測、儀表板）
  - 最新預測結果展示

### 2. 分離式預測 (/separated_prediction) 🆕
- **功能**: 機器學習和板路分析分離式預測
- **特色**:
  - 🤖 機器學習預測：使用多種ML算法
  - 📊 板路分析預測：基於歷史開獎模式
  - 🎯 成功分析：分析預測成功的原因
  - ⚖️ 方法比較：兩種方法的詳細比較

#### 使用步驟：
1. 選擇彩票類型（威力彩/大樂透/今彩539）
2. 設定候選數量（1-10個）
3. 設定最低信心分數（0.1-1.0）
4. 點擊「開始預測」
5. 查看分離式預測結果

#### 結果解讀：
- **⭐ 高信心分數** (≥0.8)：強烈推薦
- **🔸 中等信心分數** (0.6-0.8)：建議參考
- **🔹 低信心分數** (<0.6)：謹慎參考

### 3. 系統儀表板 (/dashboard) 🆕
- **功能**: 實時系統監控和快速操作
- **特色**:
  - 📊 系統狀態監控
  - 📈 預測統計圖表
  - 🔄 最新預測結果
  - ⚡ 快速操作按鈕

#### 儀表板功能：
- **系統狀態**: 運行狀態、最後更新時間、總預測數
- **統計卡片**: 各彩票類型的最新預測
- **圖表分析**: 預測方法分佈、預測趨勢
- **快速操作**: 
  - 更新開獎結果
  - 分析準確度
  - 執行完整自動化任務

### 4. 預測記錄 (/predictions)
- **功能**: 查看歷史預測記錄
- **特色**:
  - 按彩票類型篩選
  - 預測結果與實際開獎對比
  - 準確度統計

### 5. 分析報告 (/analysis)
- **功能**: 查看預測準確度分析
- **特色**:
  - 詳細的準確度統計
  - 不同方法的比較
  - 改進建議

## 🔧 快速操作

### 每日自動化任務
在分離式預測頁面或儀表板中，您可以執行：

1. **更新開獎結果**: 下載最新的開獎數據
2. **分析準確度**: 分析預測的準確度
3. **策略優化**: 基於成功分析優化預測策略

### API接口

系統提供以下API接口：

- `POST /api/separated_predict` - 執行分離式預測
- `POST /api/daily_automation` - 執行自動化任務
- `POST /api/strategy_optimize` - 策略優化
- `GET /api/dashboard_data` - 獲取儀表板數據
- `GET /api/latest_predictions/<lottery_type>` - 獲取最新預測
- `GET /api/accuracy/<lottery_type>` - 獲取準確度數據

## 🎯 使用建議

### 最佳實踐

1. **定期更新**: 每天使用「更新開獎結果」功能
2. **分離式預測**: 使用新的分離式預測功能獲得更詳細的分析
3. **策略優化**: 定期執行策略優化以提高預測準確度
4. **監控儀表板**: 使用儀表板監控系統狀態

### 預測策略

1. **綜合參考**: 同時參考機器學習和板路分析結果
2. **信心分數**: 優先選擇高信心分數的候選
3. **成功分析**: 關注成功分析中的模式和建議
4. **方法比較**: 注意兩種方法的共同推薦號碼

## 🔍 故障排除

### 常見問題

1. **無法啟動Web應用**
   - 檢查端口5001是否被佔用
   - 確認Flask已安裝：`pip install flask`
   - 查看logs目錄中的錯誤日誌

2. **預測失敗**
   - 確認有足夠的歷史數據
   - 檢查模型文件是否存在
   - 查看瀏覽器控制台的錯誤信息

3. **頁面載入緩慢**
   - 首次使用時需要初始化板路分析引擎
   - 特徵工程處理需要一些時間
   - 建議耐心等待

### 日誌查看

- **Web應用日誌**: `logs/web_app_YYYYMMDD.log`
- **整合預測日誌**: `logs/integrated_predictor.log`
- **自動化任務日誌**: `logs/daily_automation_YYYYMMDD.log`

## 📊 系統要求

### 軟體要求
- Python 3.8+
- Flask 2.0+
- 其他依賴見 requirements.txt

### 硬體建議
- RAM: 4GB+ (推薦8GB+)
- 存儲: 2GB+ 可用空間
- 網路: 用於下載開獎數據

## 🔄 更新和維護

### 定期維護
1. 每日執行自動化任務
2. 定期清理舊的日誌文件
3. 備份重要的分析結果

### 系統更新
1. 定期更新依賴包
2. 關注新功能發布
3. 備份配置和數據

## 📞 技術支援

如遇問題，請：
1. 查看相關日誌文件
2. 檢查系統依賴
3. 運行測試腳本診斷
4. 查看GitHub Issues

## 🎉 新功能亮點

### 分離式預測
- ✅ 機器學習和板路分析獨立運行
- ✅ 詳細的預測理由說明
- ✅ 成功原因分析
- ✅ 智能策略優化

### Web界面
- ✅ 現代化響應式設計
- ✅ 實時數據更新
- ✅ 互動式圖表
- ✅ 一鍵操作

### 自動化
- ✅ 每日自動更新
- ✅ 智能分析報告
- ✅ 策略動態優化
- ✅ 系統狀態監控

---

**享受使用彩票預測系統的Web界面！** 🎯✨
