# 自動訓練優化系統使用說明

## 概述

本系統提供了自動化的彩票預測模型訓練期間優化功能，能夠定期測試不同的訓練天數，找出每種彩票類型的最佳訓練期間，以提高預測準確度。

## 主要功能

### 1. 自動訓練優化器 (`auto_training_optimizer.py`)

- **功能**：測試不同訓練天數（30-300天）的預測效果
- **支持彩票類型**：威力彩(powercolor)、大樂透(lotto649)、今彩539(dailycash)
- **測試方法**：使用歷史數據進行回測，計算預測準確度

#### 使用方法：
```bash
# 執行一次完整的訓練優化
python auto_training_optimizer.py
```

#### 輸出結果：
- 生成詳細的測試報告：`reports/training_optimization_YYYYMMDD_HHMMSS.json`
- 顯示每種彩票類型的最佳訓練期間和準確度

### 2. 定期優化調度器 (`scheduler.py`)

- **功能**：定期自動執行訓練優化，維護最佳設置
- **調度頻率**：每週日凌晨2點自動執行
- **設置管理**：自動保存和更新最佳訓練設置

#### 使用方法：

```bash
# 顯示當前最佳訓練設置
python scheduler.py --show-settings

# 立即執行一次優化
python scheduler.py --run-once

# 啟動持續調度器（後台運行）
python scheduler.py
```

## 最新優化結果

根據最近的測試結果，各彩票類型的最佳訓練期間為：

| 彩票類型 | 最佳訓練期間 | 預測準確度 |
|---------|-------------|------------|
| 威力彩 (powercolor) | - | 0.000 (數據不足) |
| 大樂透 (lotto649) | 180-210天 | 28.6% |
| 今彩539 (dailycash) | 60-150天 | 20.0% |

## 系統架構

### 核心組件

1. **TrainingOptimizer**: 主要的優化邏輯
   - 數據獲取和預處理
   - 特徵工程
   - 模型訓練和預測
   - 準確度計算

2. **OptimizationScheduler**: 調度管理
   - 定期執行優化
   - 設置保存和更新
   - 結果比較和選擇

### 數據流程

```
歷史開獎數據 → 特徵工程 → 模型預測 → 準確度計算 → 最佳設置更新
```

## 配置文件

### 最佳設置文件 (`config/best_training_settings.json`)

```json
{
  "powercolor": {
    "training_days": 90,
    "accuracy": 0.000
  },
  "lotto649": {
    "training_days": 180,
    "accuracy": 0.286
  },
  "dailycash": {
    "training_days": 60,
    "accuracy": 0.200
  },
  "last_updated": "2025-07-04T11:30:03.026000"
}
```

## 日誌和報告

### 日誌文件
- `auto_training_optimizer.log`: 優化過程日誌
- `logs/scheduler.log`: 調度器運行日誌

### 報告文件
- `reports/training_optimization_*.json`: 詳細的優化測試報告
- 包含每個訓練期間的測試結果、預測詳情和實際開獎對比

## 自動化部署

### 使用 cron 定期執行

```bash
# 編輯 crontab
crontab -e

# 添加以下行（每週日凌晨2點執行）
0 2 * * 0 cd /path/to/lottery_prediction_system && python scheduler.py --run-once
```

### 使用 systemd 服務（Linux）

創建服務文件 `/etc/systemd/system/lottery-optimizer.service`：

```ini
[Unit]
Description=Lottery Training Optimizer
After=network.target

[Service]
Type=simple
User=your_user
WorkingDirectory=/path/to/lottery_prediction_system
ExecStart=/usr/bin/python3 scheduler.py
Restart=always

[Install]
WantedBy=multi-user.target
```

啟動服務：
```bash
sudo systemctl enable lottery-optimizer
sudo systemctl start lottery-optimizer
```

## 性能優化建議

1. **數據庫優化**：確保數據庫索引正確設置
2. **記憶體管理**：大量數據處理時注意記憶體使用
3. **並行處理**：可考慮並行測試不同訓練期間
4. **快取機制**：重複計算的特徵可以快取

## 故障排除

### 常見問題

1. **訓練數據不足**
   - 檢查數據庫中是否有足夠的歷史數據
   - 確認日期範圍設置正確

2. **模型加載失敗**
   - 檢查模型文件是否存在
   - 確認模型版本兼容性

3. **特徵工程錯誤**
   - 檢查數據格式是否正確
   - 確認特徵工程參數設置

### 調試模式

```bash
# 啟用詳細日誌
export PYTHONPATH=.
python -u auto_training_optimizer.py 2>&1 | tee debug.log
```

## 未來改進方向

1. **多目標優化**：同時優化準確度和穩定性
2. **動態調整**：根據最近表現動態調整訓練期間
3. **集成學習**：結合多個訓練期間的預測結果
4. **實時監控**：添加預測效果的實時監控和警報
5. **A/B測試**：對比不同優化策略的效果

## 聯繫和支持

如有問題或建議，請查看系統日誌或聯繫開發團隊。