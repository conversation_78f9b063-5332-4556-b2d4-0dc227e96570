#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版路追蹤器 - 識別真正有規律可循的號碼
分析哪些號碼是基於可追蹤的模式產生，而非隨機
"""

import sqlite3
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional, Set
from collections import Counter, defaultdict, deque
import logging
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('pattern_tracker')

class PatternTracker:
    """版路追蹤器 - 識別真正有規律的號碼"""
    
    def __init__(self, db_path: str = None):
        if db_path is None:
            self.db_path = os.path.join(
                os.path.dirname(os.path.abspath(__file__)), 
                'data', 'lottery_data.db'
            )
        else:
            self.db_path = db_path
    
    def identify_traceable_patterns(self, lottery_type: str = 'powercolor', 
                                   analysis_periods: int = 100) -> Dict:
        """
        識別可追蹤的版路模式
        找出3-4個真正有規律可循的號碼
        """
        logger.info(f"開始版路追蹤分析 - {lottery_type}")
        
        # 1. 獲取歷史數據
        historical_data = self._get_historical_data(lottery_type, analysis_periods)
        
        if len(historical_data) < 30:
            logger.warning("歷史數據不足，無法進行版路分析")
            return self._get_empty_result()
        
        # 2. 執行多種版路分析
        patterns = {
            'fixed_position': self._analyze_fixed_position_patterns(historical_data, lottery_type),
            'follow_patterns': self._analyze_follow_patterns(historical_data, lottery_type),
            'interval_patterns': self._analyze_interval_patterns(historical_data, lottery_type),
            'hot_cold_cycles': self._analyze_hot_cold_cycles(historical_data, lottery_type),
            'tail_patterns': self._analyze_tail_patterns(historical_data, lottery_type),
            'sum_patterns': self._analyze_sum_patterns(historical_data, lottery_type),
            'consecutive_patterns': self._analyze_consecutive_patterns(historical_data, lottery_type)
        }
        
        # 3. 評估每個號碼的版路強度
        pattern_strength = self._evaluate_pattern_strength(patterns, historical_data)
        
        # 4. 識別最可靠的3-4個號碼
        reliable_numbers = self._identify_most_reliable_numbers(pattern_strength, patterns)
        
        # 5. 生成版路預測報告
        report = self._generate_pattern_report(
            reliable_numbers, 
            patterns, 
            pattern_strength,
            historical_data
        )
        
        return report
    
    def _analyze_fixed_position_patterns(self, data: List[Dict], lottery_type: str) -> Dict:
        """
        分析固定位置模式
        某些號碼是否傾向於出現在特定位置（第1個、第2個等）
        """
        position_frequency = defaultdict(lambda: defaultdict(int))
        
        for record in data:
            sorted_nums = sorted(record['numbers'])
            for pos, num in enumerate(sorted_nums):
                position_frequency[pos][num] += 1
        
        # 找出每個位置最常出現的號碼
        strong_position_patterns = {}
        for pos in position_frequency:
            if position_frequency[pos]:
                # 計算每個位置的號碼出現頻率
                total_count = sum(position_frequency[pos].values())
                for num, count in position_frequency[pos].items():
                    frequency = count / total_count
                    # 如果某號碼在特定位置出現頻率>15%，視為有版路
                    if frequency > 0.15:
                        if num not in strong_position_patterns:
                            strong_position_patterns[num] = []
                        strong_position_patterns[num].append({
                            'position': pos + 1,
                            'frequency': frequency,
                            'count': count,
                            'pattern_type': 'fixed_position'
                        })
        
        return strong_position_patterns
    
    def _analyze_follow_patterns(self, data: List[Dict], lottery_type: str) -> Dict:
        """
        分析跟隨模式
        某些號碼是否會跟隨特定號碼出現
        """
        follow_patterns = defaultdict(lambda: defaultdict(int))
        
        # 分析連續兩期的號碼關係
        for i in range(len(data) - 1):
            current_nums = set(data[i]['numbers'])
            next_nums = set(data[i + 1]['numbers'])
            
            # 記錄跟隨關係
            for current in current_nums:
                for next_num in next_nums:
                    follow_patterns[current][next_num] += 1
        
        # 找出強跟隨關係
        strong_follow_patterns = {}
        for leader, followers in follow_patterns.items():
            total_appearances = sum(followers.values())
            for follower, count in followers.items():
                follow_rate = count / total_appearances if total_appearances > 0 else 0
                # 跟隨率>20%視為有版路
                if follow_rate > 0.20 and count >= 3:
                    if follower not in strong_follow_patterns:
                        strong_follow_patterns[follower] = []
                    strong_follow_patterns[follower].append({
                        'leader': leader,
                        'follow_rate': follow_rate,
                        'count': count,
                        'pattern_type': 'follow'
                    })
        
        return strong_follow_patterns
    
    def _analyze_interval_patterns(self, data: List[Dict], lottery_type: str) -> Dict:
        """
        分析間隔模式
        某些號碼是否有固定的出現間隔
        """
        number_intervals = defaultdict(list)
        last_appearance = {}
        
        # 計算每個號碼的出現間隔
        for idx, record in enumerate(data):
            for num in record['numbers']:
                if num in last_appearance:
                    interval = idx - last_appearance[num]
                    number_intervals[num].append(interval)
                last_appearance[num] = idx
        
        # 找出有規律間隔的號碼
        interval_patterns = {}
        for num, intervals in number_intervals.items():
            if len(intervals) >= 3:
                # 計算間隔的標準差
                mean_interval = np.mean(intervals)
                std_interval = np.std(intervals)
                
                # 標準差小表示間隔穩定
                if std_interval < mean_interval * 0.3 and mean_interval < 15:
                    interval_patterns[num] = {
                        'mean_interval': mean_interval,
                        'std_interval': std_interval,
                        'stability': 1 - (std_interval / mean_interval),
                        'last_seen': last_appearance.get(num, -1),
                        'pattern_type': 'interval'
                    }
        
        return interval_patterns
    
    def _analyze_hot_cold_cycles(self, data: List[Dict], lottery_type: str) -> Dict:
        """
        分析冷熱循環模式
        某些號碼是否有明顯的冷熱循環週期
        """
        # 將數據分成多個時期
        period_size = 10
        num_periods = len(data) // period_size
        
        hot_cold_cycles = defaultdict(list)
        
        for period_idx in range(num_periods):
            start_idx = period_idx * period_size
            end_idx = start_idx + period_size
            period_data = data[start_idx:end_idx]
            
            # 統計這個時期的號碼頻率
            period_counter = Counter()
            for record in period_data:
                for num in record['numbers']:
                    period_counter[num] += 1
            
            # 判斷每個號碼在這個時期是熱還是冷
            avg_frequency = sum(period_counter.values()) / len(period_counter) if period_counter else 0
            
            for num, count in period_counter.items():
                if count > avg_frequency * 1.2:
                    hot_cold_cycles[num].append('hot')
                elif count < avg_frequency * 0.8:
                    hot_cold_cycles[num].append('cold')
                else:
                    hot_cold_cycles[num].append('normal')
        
        # 找出有規律循環的號碼
        cycle_patterns = {}
        for num, cycles in hot_cold_cycles.items():
            if len(cycles) >= 4:
                # 檢查是否有循環模式
                cycle_pattern = self._detect_cycle_pattern(cycles)
                if cycle_pattern:
                    cycle_patterns[num] = {
                        'cycle_pattern': cycle_pattern,
                        'current_state': cycles[-1] if cycles else 'unknown',
                        'pattern_type': 'hot_cold_cycle'
                    }
        
        return cycle_patterns
    
    def _analyze_tail_patterns(self, data: List[Dict], lottery_type: str) -> Dict:
        """
        分析尾數版路
        某些尾數是否有連續出現或間隔出現的規律
        """
        tail_sequences = []
        
        for record in data:
            tails = sorted([num % 10 for num in record['numbers']])
            tail_sequences.append(tails)
        
        # 分析尾數連續性
        tail_continuity = defaultdict(list)
        for i in range(len(tail_sequences) - 1):
            current_tails = set(tail_sequences[i])
            next_tails = set(tail_sequences[i + 1])
            
            # 記錄連續出現的尾數
            continuous_tails = current_tails & next_tails
            for tail in continuous_tails:
                tail_continuity[tail].append(i)
        
        # 找出有規律的尾數對應號碼
        tail_patterns = {}
        for tail, positions in tail_continuity.items():
            if len(positions) >= 5:
                # 檢查連續性規律
                continuity_rate = len(positions) / (len(data) - 1)
                if continuity_rate > 0.3:
                    # 找出這個尾數最常對應的號碼
                    tail_numbers = []
                    for record in data[-20:]:  # 最近20期
                        for num in record['numbers']:
                            if num % 10 == tail:
                                tail_numbers.append(num)
                    
                    if tail_numbers:
                        most_common = Counter(tail_numbers).most_common(2)
                        for num, count in most_common:
                            if count >= 3:
                                tail_patterns[num] = {
                                    'tail': tail,
                                    'continuity_rate': continuity_rate,
                                    'recent_frequency': count / 20,
                                    'pattern_type': 'tail'
                                }
        
        return tail_patterns
    
    def _analyze_sum_patterns(self, data: List[Dict], lottery_type: str) -> Dict:
        """
        分析和值模式
        某些號碼是否傾向於出現在特定和值範圍
        """
        sum_number_frequency = defaultdict(lambda: defaultdict(int))
        
        for record in data:
            total_sum = sum(record['numbers'])
            for num in record['numbers']:
                sum_number_frequency[total_sum][num] += 1
        
        # 計算和值範圍
        all_sums = [sum(record['numbers']) for record in data]
        mean_sum = np.mean(all_sums)
        std_sum = np.std(all_sums)
        
        # 定義和值範圍
        ranges = {
            'low': (mean_sum - std_sum, mean_sum - 0.5 * std_sum),
            'mid': (mean_sum - 0.5 * std_sum, mean_sum + 0.5 * std_sum),
            'high': (mean_sum + 0.5 * std_sum, mean_sum + std_sum)
        }
        
        # 找出在特定和值範圍頻繁出現的號碼
        sum_patterns = {}
        for range_name, (low, high) in ranges.items():
            range_numbers = Counter()
            range_count = 0
            
            for record in data:
                total_sum = sum(record['numbers'])
                if low <= total_sum <= high:
                    range_count += 1
                    for num in record['numbers']:
                        range_numbers[num] += 1
            
            # 找出在這個範圍特別活躍的號碼
            for num, count in range_numbers.items():
                if range_count > 0:
                    frequency = count / range_count
                    if frequency > 0.15:  # 在該範圍出現頻率>15%
                        if num not in sum_patterns:
                            sum_patterns[num] = []
                        sum_patterns[num].append({
                            'sum_range': range_name,
                            'frequency': frequency,
                            'pattern_type': 'sum_range'
                        })
        
        return sum_patterns
    
    def _analyze_consecutive_patterns(self, data: List[Dict], lottery_type: str) -> Dict:
        """
        分析連號模式
        找出經常參與連號的號碼
        """
        consecutive_frequency = Counter()
        consecutive_pairs = defaultdict(int)
        
        for record in data:
            sorted_nums = sorted(record['numbers'])
            for i in range(len(sorted_nums) - 1):
                if sorted_nums[i + 1] - sorted_nums[i] == 1:
                    consecutive_frequency[sorted_nums[i]] += 1
                    consecutive_frequency[sorted_nums[i + 1]] += 1
                    consecutive_pairs[(sorted_nums[i], sorted_nums[i + 1])] += 1
        
        # 找出經常參與連號的號碼
        consecutive_patterns = {}
        total_periods = len(data)
        
        for num, count in consecutive_frequency.items():
            participation_rate = count / total_periods
            if participation_rate > 0.15 and count >= 5:
                # 找出最常配對的號碼
                pairs = []
                for (n1, n2), pair_count in consecutive_pairs.items():
                    if n1 == num or n2 == num:
                        partner = n2 if n1 == num else n1
                        pairs.append({
                            'partner': partner,
                            'count': pair_count
                        })
                
                consecutive_patterns[num] = {
                    'participation_rate': participation_rate,
                    'total_count': count,
                    'common_pairs': sorted(pairs, key=lambda x: x['count'], reverse=True)[:3],
                    'pattern_type': 'consecutive'
                }
        
        return consecutive_patterns
    
    def _detect_cycle_pattern(self, cycles: List[str]) -> Optional[str]:
        """檢測循環模式"""
        if len(cycles) < 4:
            return None
        
        # 嘗試檢測2-4長度的循環
        for cycle_len in range(2, 5):
            if len(cycles) >= cycle_len * 2:
                # 檢查最後的幾個週期是否重複
                is_cycle = True
                for i in range(cycle_len):
                    if cycles[-(cycle_len + i)] != cycles[-i-1]:
                        is_cycle = False
                        break
                
                if is_cycle:
                    pattern = '-'.join(cycles[-cycle_len:])
                    return f"循環: {pattern}"
        
        return None
    
    def _evaluate_pattern_strength(self, patterns: Dict, data: List[Dict]) -> Dict[int, float]:
        """
        評估每個號碼的版路強度
        """
        pattern_strength = defaultdict(float)
        
        # 各種模式的權重
        weights = {
            'fixed_position': 0.25,
            'follow': 0.20,
            'interval': 0.20,
            'hot_cold_cycle': 0.10,
            'tail': 0.10,
            'sum_range': 0.10,
            'consecutive': 0.05
        }
        
        # 計算每個號碼的綜合版路強度
        for pattern_type, pattern_data in patterns.items():
            if pattern_type == 'fixed_position':
                for num, positions in pattern_data.items():
                    max_freq = max(p['frequency'] for p in positions)
                    pattern_strength[num] += max_freq * weights.get('fixed_position', 0.1)
            
            elif pattern_type == 'follow_patterns':
                for num, follows in pattern_data.items():
                    max_rate = max(f['follow_rate'] for f in follows) if follows else 0
                    pattern_strength[num] += max_rate * weights.get('follow', 0.1)
            
            elif pattern_type == 'interval_patterns':
                for num, interval_data in pattern_data.items():
                    stability = interval_data.get('stability', 0)
                    pattern_strength[num] += stability * weights.get('interval', 0.1)
            
            elif pattern_type == 'hot_cold_cycles':
                for num, cycle_data in pattern_data.items():
                    # 有循環模式加分
                    pattern_strength[num] += 0.5 * weights.get('hot_cold_cycle', 0.1)
            
            elif pattern_type == 'tail_patterns':
                for num, tail_data in pattern_data.items():
                    continuity = tail_data.get('continuity_rate', 0)
                    pattern_strength[num] += continuity * weights.get('tail', 0.1)
            
            elif pattern_type == 'sum_patterns':
                for num, sum_data in pattern_data.items():
                    if sum_data:
                        max_freq = max(s['frequency'] for s in sum_data)
                        pattern_strength[num] += max_freq * weights.get('sum_range', 0.1)
            
            elif pattern_type == 'consecutive_patterns':
                for num, cons_data in pattern_data.items():
                    participation = cons_data.get('participation_rate', 0)
                    pattern_strength[num] += participation * weights.get('consecutive', 0.1)
        
        return dict(pattern_strength)
    
    def _identify_most_reliable_numbers(self, pattern_strength: Dict[int, float], 
                                       patterns: Dict) -> List[Dict]:
        """
        識別最可靠的3-4個號碼
        """
        # 按版路強度排序
        sorted_numbers = sorted(pattern_strength.items(), key=lambda x: x[1], reverse=True)
        
        reliable_numbers = []
        selected_count = 0
        max_selection = 4  # 最多選4個
        
        for num, strength in sorted_numbers:
            if selected_count >= max_selection:
                break
            
            if strength < 0.1:  # 強度太低，跳過
                continue
            
            # 收集這個號碼的所有版路證據
            evidence = []
            
            # 檢查各種模式
            if num in patterns.get('fixed_position', {}):
                for pos_data in patterns['fixed_position'][num]:
                    evidence.append({
                        'type': '固定位置',
                        'detail': f"第{pos_data['position']}位出現率{pos_data['frequency']:.1%}"
                    })
            
            if num in patterns.get('follow_patterns', {}):
                for follow_data in patterns['follow_patterns'][num][:1]:  # 只取最強的
                    evidence.append({
                        'type': '跟隨模式',
                        'detail': f"跟隨{follow_data['leader']}號出現率{follow_data['follow_rate']:.1%}"
                    })
            
            if num in patterns.get('interval_patterns', {}):
                interval_data = patterns['interval_patterns'][num]
                evidence.append({
                    'type': '間隔規律',
                    'detail': f"平均{interval_data['mean_interval']:.1f}期出現，穩定度{interval_data['stability']:.1%}"
                })
            
            if num in patterns.get('tail_patterns', {}):
                tail_data = patterns['tail_patterns'][num]
                evidence.append({
                    'type': '尾數版路',
                    'detail': f"尾數{tail_data['tail']}連續率{tail_data['continuity_rate']:.1%}"
                })
            
            if evidence:  # 只選擇有明確證據的號碼
                reliable_numbers.append({
                    'number': num,
                    'strength': strength,
                    'evidence': evidence,
                    'confidence': min(0.85, strength * 2)  # 轉換為信心度
                })
                selected_count += 1
        
        return reliable_numbers
    
    def _generate_pattern_report(self, reliable_numbers: List[Dict], patterns: Dict,
                                pattern_strength: Dict, data: List[Dict]) -> Dict:
        """生成版路分析報告"""
        
        # 提取可追蹤的號碼
        traceable_numbers = [rn['number'] for rn in reliable_numbers]
        
        # 計算整體信心度
        overall_confidence = sum(rn['confidence'] for rn in reliable_numbers) / len(reliable_numbers) if reliable_numbers else 0
        
        report = {
            'traceable_numbers': traceable_numbers,
            'reliable_count': len(traceable_numbers),
            'overall_confidence': overall_confidence,
            'detailed_analysis': reliable_numbers,
            'pattern_summary': {
                'fixed_position_count': len(patterns.get('fixed_position', {})),
                'follow_pattern_count': len(patterns.get('follow_patterns', {})),
                'interval_pattern_count': len(patterns.get('interval_patterns', {})),
                'hot_cold_cycle_count': len(patterns.get('hot_cold_cycles', {})),
                'tail_pattern_count': len(patterns.get('tail_patterns', {})),
                'consecutive_pattern_count': len(patterns.get('consecutive_patterns', {}))
            },
            'recommendation': self._generate_recommendation(reliable_numbers, patterns),
            'analysis_metadata': {
                'total_periods_analyzed': len(data),
                'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'method': '七維度版路追蹤分析'
            }
        }
        
        return report
    
    def _generate_recommendation(self, reliable_numbers: List[Dict], patterns: Dict) -> str:
        """生成使用建議"""
        
        if len(reliable_numbers) >= 3:
            numbers_str = ', '.join(str(rn['number']) for rn in reliable_numbers[:3])
            return f"建議將 {numbers_str} 作為核心號碼，這些號碼有明確的版路支撐，其餘號碼可根據其他策略補充"
        elif len(reliable_numbers) > 0:
            numbers_str = ', '.join(str(rn['number']) for rn in reliable_numbers)
            return f"找到 {numbers_str} 有版路跡象，但建議結合其他分析方法增加選號"
        else:
            return "當前數據未發現明顯版路，建議使用其他預測方法或等待更多數據"
    
    def _get_historical_data(self, lottery_type: str, limit: int) -> List[Dict]:
        """獲取歷史數據"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            if lottery_type == 'powercolor':
                query = """
                SELECT Period, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, 
                       Second_district, Sdate
                FROM Powercolor
                ORDER BY Period DESC
                LIMIT ?
                """
            elif lottery_type == 'lotto649':
                query = """
                SELECT Period, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6,
                       SpecialNumber, Sdate
                FROM Lotto649
                ORDER BY Period DESC
                LIMIT ?
                """
            else:  # dailycash
                query = """
                SELECT Period, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Sdate
                FROM DailyCash
                ORDER BY Period DESC
                LIMIT ?
                """
            
            cursor = conn.execute(query, (limit,))
            rows = cursor.fetchall()
            conn.close()
            
            historical_data = []
            for row in rows:
                if lottery_type in ['powercolor', 'lotto649']:
                    historical_data.append({
                        'period': row[0],
                        'numbers': [row[1], row[2], row[3], row[4], row[5], row[6]],
                        'special': row[7] if len(row) > 7 else None,
                        'date': row[8] if len(row) > 8 else None
                    })
                else:
                    historical_data.append({
                        'period': row[0],
                        'numbers': [row[1], row[2], row[3], row[4], row[5]],
                        'special': None,
                        'date': row[6] if len(row) > 6 else None
                    })
            
            return historical_data
            
        except Exception as e:
            logger.error(f"獲取歷史數據失敗: {str(e)}")
            return []
    
    def _get_empty_result(self) -> Dict:
        """返回空結果"""
        return {
            'traceable_numbers': [],
            'reliable_count': 0,
            'overall_confidence': 0,
            'detailed_analysis': [],
            'pattern_summary': {},
            'recommendation': '數據不足，無法進行版路分析',
            'analysis_metadata': {
                'total_periods_analyzed': 0,
                'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'method': '七維度版路追蹤分析'
            }
        }


def test_pattern_tracker():
    """測試版路追蹤器"""
    
    tracker = PatternTracker()
    
    print("=" * 100)
    print("版路追蹤分析 - 識別真正有規律可循的號碼")
    print("=" * 100)
    
    # 分析威力彩
    result = tracker.identify_traceable_patterns('powercolor', 100)
    
    print(f"\n📊 分析結果:")
    print(f"分析期數: {result['analysis_metadata']['total_periods_analyzed']} 期")
    print(f"分析方法: {result['analysis_metadata']['method']}")
    
    print(f"\n🎯 可追蹤號碼 (共 {result['reliable_count']} 個):")
    if result['traceable_numbers']:
        print(f"核心號碼: {result['traceable_numbers']}")
        print(f"整體信心度: {result['overall_confidence']:.1%}")
        
        print(f"\n📝 詳細版路證據:")
        for idx, detail in enumerate(result['detailed_analysis'], 1):
            print(f"\n{idx}. 號碼 {detail['number']} (強度: {detail['strength']:.2f}, 信心度: {detail['confidence']:.1%})")
            for evidence in detail['evidence']:
                print(f"   - {evidence['type']}: {evidence['detail']}")
    else:
        print("未發現明確版路")
    
    print(f"\n📈 版路統計:")
    if result['pattern_summary']:
        for pattern_type, count in result['pattern_summary'].items():
            if count > 0:
                pattern_name = {
                    'fixed_position_count': '固定位置模式',
                    'follow_pattern_count': '跟隨模式',
                    'interval_pattern_count': '間隔規律',
                    'hot_cold_cycle_count': '冷熱循環',
                    'tail_pattern_count': '尾數版路',
                    'consecutive_pattern_count': '連號模式'
                }.get(pattern_type, pattern_type)
                print(f"  {pattern_name}: {count} 個號碼")
    
    print(f"\n💡 建議:")
    print(f"{result['recommendation']}")


if __name__ == "__main__":
    test_pattern_tracker()