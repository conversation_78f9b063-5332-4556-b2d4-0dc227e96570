#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改進版彩票資料更新器
具備多重驗證、錯誤檢測、和自動修正功能
"""

import requests
import sqlite3
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import time
import re
from dataclasses import dataclass
from bs4 import BeautifulSoup

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/lottery_updater.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class LotteryResult:
    """彩票開獎結果"""
    period: str
    date: str
    numbers: List[int]
    special_number: Optional[int]
    lottery_type: str
    source: str
    confidence: float = 1.0  # 數據可信度 0-1

class ImprovedLotteryUpdater:
    """改進版彩票資料更新器"""
    
    def __init__(self, db_path: str = "data/lottery_data.db"):
        self.db_path = db_path
        self.session = requests.Session()
        
        # 設定多個 User-Agent 輪替使用
        self.user_agents = [
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        
        # 設定請求標頭
        self.session.headers.update({
            'User-Agent': self.user_agents[0],
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # API 端點配置
        self.api_endpoints = {
            'powercolor': {
                'url': 'https://www.taiwanlottery.com.tw/lotto/lotto_details',
                'params': {'gameid': '3'},
                'name': '威力彩',
                'validation': {
                    'main_count': 6,
                    'main_range': (1, 38),
                    'special_range': (1, 8)
                }
            },
            'lotto649': {
                'url': 'https://www.taiwanlottery.com.tw/lotto/lotto_details',
                'params': {'gameid': '1'},
                'name': '大樂透',
                'validation': {
                    'main_count': 6,
                    'main_range': (1, 49),
                    'special_range': (1, 49)
                }
            },
            'dailycash': {
                'url': 'https://www.taiwanlottery.com.tw/lotto/lotto_details',
                'params': {'gameid': '6'},
                'name': '今彩539',
                'validation': {
                    'main_count': 5,
                    'main_range': (1, 39),
                    'special_range': None
                }
            }
        }
    
    def validate_lottery_data(self, result: LotteryResult) -> Tuple[bool, str]:
        """驗證彩票資料的正確性"""
        if result.lottery_type not in self.api_endpoints:
            return False, f"不支援的彩票類型: {result.lottery_type}"
        
        config = self.api_endpoints[result.lottery_type]['validation']
        errors = []
        
        # 檢查主號碼數量
        if len(result.numbers) != config['main_count']:
            errors.append(f"主號碼數量錯誤: 預期 {config['main_count']}, 實際 {len(result.numbers)}")
        
        # 檢查主號碼範圍
        min_num, max_num = config['main_range']
        for num in result.numbers:
            if not (min_num <= num <= max_num):
                errors.append(f"主號碼 {num} 超出範圍 {min_num}-{max_num}")
        
        # 檢查號碼重複
        if len(set(result.numbers)) != len(result.numbers):
            errors.append(f"主號碼有重複: {result.numbers}")
        
        # 檢查特別號
        if config['special_range'] and result.special_number:
            min_special, max_special = config['special_range']
            if not (min_special <= result.special_number <= max_special):
                errors.append(f"特別號 {result.special_number} 超出範圍 {min_special}-{max_special}")
        
        # 檢查期號格式
        if not re.match(r'^\d{9}$', result.period):
            errors.append(f"期號格式錯誤: {result.period}")
        
        # 檢查日期格式
        try:
            datetime.strptime(result.date, '%Y-%m-%d')
        except ValueError:
            errors.append(f"日期格式錯誤: {result.date}")
        
        is_valid = len(errors) == 0
        error_msg = "; ".join(errors)
        
        return is_valid, error_msg
    
    def fetch_from_official_api(self, lottery_type: str, max_results: int = 10) -> List[LotteryResult]:
        """從官方 API 獲取資料"""
        if lottery_type not in self.api_endpoints:
            logger.error(f"不支援的彩票類型: {lottery_type}")
            return []
        
        endpoint = self.api_endpoints[lottery_type]
        results = []
        
        try:
            logger.info(f"🌐 正在從官方 API 獲取 {endpoint['name']} 資料...")
            
            # 發送請求
            response = self.session.get(
                endpoint['url'],
                params=endpoint['params'],
                timeout=30,
                verify=False  # 某些環境可能需要
            )
            
            if response.status_code != 200:
                logger.error(f"API 請求失敗: HTTP {response.status_code}")
                return []
            
            # 嘗試解析 JSON
            try:
                data = response.json()
            except json.JSONDecodeError:
                # 如果不是 JSON，嘗試解析 HTML
                return self.parse_html_response(response.text, lottery_type)
            
            # 解析 JSON 資料
            results = self.parse_json_response(data, lottery_type)
            
        except requests.exceptions.RequestException as e:
            logger.error(f"網路請求錯誤: {str(e)}")
        except Exception as e:
            logger.error(f"獲取資料時發生未知錯誤: {str(e)}")
        
        return results[:max_results]
    
    def parse_html_response(self, html_content: str, lottery_type: str) -> List[LotteryResult]:
        """解析 HTML 回應"""
        results = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 根據彩票類型選擇不同的解析策略
            if lottery_type == 'powercolor':
                results = self.parse_powercolor_html(soup)
            elif lottery_type == 'lotto649':
                results = self.parse_lotto649_html(soup)
            elif lottery_type == 'dailycash':
                results = self.parse_dailycash_html(soup)
                
        except Exception as e:
            logger.error(f"解析 HTML 時發生錯誤: {str(e)}")
        
        return results
    
    def parse_powercolor_html(self, soup: BeautifulSoup) -> List[LotteryResult]:
        """解析威力彩 HTML"""
        results = []
        
        try:
            # 這裡需要根據實際的 HTML 結構來解析
            # 以下是通用的解析邏輯，可能需要調整
            
            # 尋找開獎結果表格
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                
                for row in rows[1:]:  # 跳過標題行
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 8:  # 威力彩需要至少8個欄位
                        try:
                            period = cells[0].get_text().strip()
                            date = cells[1].get_text().strip()
                            
                            # 解析號碼
                            numbers = []
                            for i in range(2, 8):  # 6個主號碼
                                num_text = cells[i].get_text().strip()
                                if num_text.isdigit():
                                    numbers.append(int(num_text))
                            
                            # 特別號
                            special = None
                            if len(cells) > 8:
                                special_text = cells[8].get_text().strip()
                                if special_text.isdigit():
                                    special = int(special_text)
                            
                            if len(numbers) == 6:
                                result = LotteryResult(
                                    period=period,
                                    date=date,
                                    numbers=sorted(numbers),
                                    special_number=special,
                                    lottery_type='powercolor',
                                    source='official_html',
                                    confidence=0.9
                                )
                                
                                # 驗證資料
                                is_valid, error_msg = self.validate_lottery_data(result)
                                if is_valid:
                                    results.append(result)
                                else:
                                    logger.warning(f"HTML 解析的資料無效: {error_msg}")
                                    
                        except (ValueError, IndexError) as e:
                            logger.debug(f"解析行時發生錯誤: {str(e)}")
                            continue
        
        except Exception as e:
            logger.error(f"解析威力彩 HTML 時發生錯誤: {str(e)}")
        
        return results
    
    def parse_json_response(self, data: Dict, lottery_type: str) -> List[LotteryResult]:
        """解析 JSON 回應"""
        results = []
        
        try:
            # 根據實際的 JSON 結構進行解析
            # 這裡是一個通用的解析邏輯
            
            if isinstance(data, dict):
                # 尋找可能包含開獎結果的鍵
                possible_keys = ['data', 'results', 'items', 'list', 'content']
                
                for key in possible_keys:
                    if key in data and isinstance(data[key], list):
                        for item in data[key]:
                            result = self.parse_lottery_item(item, lottery_type)
                            if result:
                                results.append(result)
                        break
            
        except Exception as e:
            logger.error(f"解析 JSON 時發生錯誤: {str(e)}")
        
        return results
    
    def parse_lottery_item(self, item: Dict, lottery_type: str) -> Optional[LotteryResult]:
        """解析單個彩票項目"""
        try:
            # 嘗試從不同的鍵中提取資料
            period_keys = ['period', 'drawNumber', 'issue', 'no']
            date_keys = ['date', 'drawDate', 'lotteryDate', 'time']
            numbers_keys = ['numbers', 'winningNumbers', 'mainNumbers', 'lotteryNumbers']
            special_keys = ['specialNumber', 'bonusNumber', 'special', 'bonus']
            
            # 提取期號
            period = None
            for key in period_keys:
                if key in item:
                    period = str(item[key])
                    break
            
            # 提取日期
            date = None
            for key in date_keys:
                if key in item:
                    date_value = item[key]
                    if isinstance(date_value, str):
                        # 嘗試標準化日期格式
                        try:
                            parsed_date = datetime.strptime(date_value, '%Y/%m/%d')
                            date = parsed_date.strftime('%Y-%m-%d')
                        except ValueError:
                            try:
                                parsed_date = datetime.strptime(date_value, '%Y-%m-%d')
                                date = date_value
                            except ValueError:
                                pass
                    break
            
            # 提取號碼
            numbers = []
            for key in numbers_keys:
                if key in item:
                    if isinstance(item[key], list):
                        numbers = [int(x) for x in item[key] if str(x).isdigit()]
                    elif isinstance(item[key], str):
                        # 嘗試從字符串中提取號碼
                        num_matches = re.findall(r'\d+', item[key])
                        numbers = [int(x) for x in num_matches]
                    break
            
            # 提取特別號
            special = None
            for key in special_keys:
                if key in item and str(item[key]).isdigit():
                    special = int(item[key])
                    break
            
            # 創建結果物件
            if period and date and numbers:
                result = LotteryResult(
                    period=period,
                    date=date,
                    numbers=sorted(numbers),
                    special_number=special,
                    lottery_type=lottery_type,
                    source='official_json',
                    confidence=1.0
                )
                
                # 驗證資料
                is_valid, error_msg = self.validate_lottery_data(result)
                if is_valid:
                    return result
                else:
                    logger.warning(f"解析的資料無效: {error_msg}")
            
        except Exception as e:
            logger.debug(f"解析彩票項目時發生錯誤: {str(e)}")
        
        return None
    
    def save_to_database(self, results: List[LotteryResult]) -> Dict:
        """儲存結果到資料庫"""
        if not results:
            return {'saved': 0, 'updated': 0, 'errors': []}
        
        saved_count = 0
        updated_count = 0
        errors = []
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for result in results:
                try:
                    # 檢查記錄是否已存在
                    cursor.execute(
                        "SELECT Period FROM Powercolor WHERE Period = ?", 
                        (result.period,)
                    )
                    existing = cursor.fetchone()
                    
                    if existing:
                        # 更新現有記錄
                        cursor.execute("""
                            UPDATE Powercolor 
                            SET Sdate = ?, Anumber1 = ?, Anumber2 = ?, Anumber3 = ?,
                                Anumber4 = ?, Anumber5 = ?, Anumber6 = ?, Second_district = ?
                            WHERE Period = ?
                        """, (
                            result.date,
                            *result.numbers,
                            result.special_number,
                            result.period
                        ))
                        updated_count += 1
                        logger.info(f"✅ 已更新期號 {result.period}")
                    else:
                        # 插入新記錄
                        cursor.execute("""
                            INSERT INTO Powercolor 
                            (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3,
                             Anumber4, Anumber5, Anumber6, Second_district)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            self.api_endpoints[result.lottery_type]['name'],
                            result.period,
                            result.date,
                            *result.numbers,
                            result.special_number
                        ))
                        saved_count += 1
                        logger.info(f"✅ 已新增期號 {result.period}")
                
                except Exception as e:
                    error_msg = f"儲存期號 {result.period} 時發生錯誤: {str(e)}"
                    errors.append(error_msg)
                    logger.error(error_msg)
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            error_msg = f"資料庫操作錯誤: {str(e)}"
            errors.append(error_msg)
            logger.error(error_msg)
        
        return {
            'saved': saved_count,
            'updated': updated_count,
            'errors': errors
        }
    
    def update_lottery_data(self, lottery_type: str = 'powercolor') -> Dict:
        """更新彩票資料（主要功能）"""
        logger.info(f"🚀 開始更新 {lottery_type} 資料")
        
        # 從官方 API 獲取資料
        results = self.fetch_from_official_api(lottery_type)
        
        if not results:
            logger.warning(f"沒有獲取到 {lottery_type} 資料")
            return {
                'success': False,
                'message': '沒有獲取到資料',
                'data': None
            }
        
        logger.info(f"📊 獲取到 {len(results)} 筆資料")
        
        # 儲存到資料庫
        save_result = self.save_to_database(results)
        
        success = len(save_result['errors']) == 0
        message = f"成功處理 {save_result['saved'] + save_result['updated']} 筆資料"
        
        if save_result['errors']:
            message += f", 但有 {len(save_result['errors'])} 個錯誤"
        
        return {
            'success': success,
            'message': message,
            'data': {
                'fetched': len(results),
                'saved': save_result['saved'],
                'updated': save_result['updated'],
                'errors': save_result['errors']
            }
        }

def main():
    """主函數"""
    updater = ImprovedLotteryUpdater()
    
    # 更新威力彩資料
    result = updater.update_lottery_data('powercolor')
    
    print(f"✅ 更新結果: {result['message']}")
    if result['data'] and result['data']['errors']:
        print("❌ 錯誤詳情:")
        for error in result['data']['errors']:
            print(f"   - {error}")

if __name__ == "__main__":
    main()