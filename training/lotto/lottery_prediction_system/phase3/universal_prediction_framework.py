#!/usr/bin/env python3
"""
Phase 3 通用預測框架
實現跨彩票類型的統一預測接口和可擴展的策略模式
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple, Union, Type
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import importlib
import sys
import os

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from prediction.enhanced_multi_algorithm_predictor import EnhancedMultiAlgorithmPredictor
from data.db_manager import DBManager
from phase3.accuracy_assessment_engine import AccuracyAssessmentEngine

logger = logging.getLogger('universal_prediction_framework')

class LotteryType(Enum):
    """彩票類型定義"""
    POWERCOLOR = "powercolor"  # 威力彩
    LOTTO649 = "lotto649"      # 大樂透
    DAILYCASH = "dailycash"    # 今彩539
    SUPER_LOTTO = "super_lotto"  # 超級大樂透
    SCRATCH_OFF = "scratch_off"  # 刮刮樂
    
    @classmethod
    def get_all_types(cls) -> List[str]:
        """獲取所有彩票類型"""
        return [lottery.value for lottery in cls]
    
    @classmethod
    def is_supported(cls, lottery_type: str) -> bool:
        """檢查是否支持指定彩票類型"""
        return lottery_type in cls.get_all_types()

class PredictionStrategy(Enum):
    """預測策略類型"""
    FREQUENCY_BASED = "frequency_based"
    PATTERN_BASED = "pattern_based"
    TREND_ANALYSIS = "trend_analysis"
    NEURAL_NETWORK = "neural_network"
    ENSEMBLE = "ensemble"
    CUSTOM = "custom"

@dataclass
class LotteryConfig:
    """彩票配置結構"""
    lottery_type: str
    name: str
    description: str
    main_numbers_count: int
    main_numbers_range: Tuple[int, int]
    special_numbers_count: int
    special_numbers_range: Optional[Tuple[int, int]]
    draw_days: List[str]
    draw_time: str
    prediction_advance_hours: int
    supported_strategies: List[str]
    min_historical_data: int
    confidence_threshold: float

@dataclass
class UniversalPredictionResult:
    """通用預測結果結構"""
    lottery_type: str
    prediction_id: str
    strategy_used: str
    main_numbers: List[int]
    special_numbers: Optional[List[int]]
    confidence: float
    metadata: Dict[str, Any]
    prediction_timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            'lottery_type': self.lottery_type,
            'prediction_id': self.prediction_id,
            'strategy_used': self.strategy_used,
            'main_numbers': self.main_numbers,
            'special_numbers': self.special_numbers,
            'confidence': self.confidence,
            'metadata': self.metadata,
            'prediction_timestamp': self.prediction_timestamp.isoformat()
        }

@dataclass
class CrossLotteryAnalysis:
    """跨彩票分析結果"""
    correlation_matrix: Dict[str, Dict[str, float]]
    trend_similarities: Dict[str, List[str]]
    pattern_transfers: List[Dict[str, Any]]
    recommendation_score: float
    analysis_timestamp: datetime

class IPredictionStrategy(ABC):
    """預測策略接口"""
    
    @abstractmethod
    def initialize(self, lottery_config: LotteryConfig, historical_data: pd.DataFrame) -> bool:
        """初始化策略"""
        pass
    
    @abstractmethod
    def predict(self, data: pd.DataFrame, **kwargs) -> Optional[UniversalPredictionResult]:
        """執行預測"""
        pass
    
    @abstractmethod
    def get_strategy_info(self) -> Dict[str, Any]:
        """獲取策略信息"""
        pass
    
    @abstractmethod
    def update_parameters(self, parameters: Dict[str, Any]) -> bool:
        """更新策略參數"""
        pass

class LotteryConfigManager:
    """彩票配置管理器"""
    
    def __init__(self, config_path: str = "config/lottery_configs.json"):
        self.config_path = config_path
        self.configs: Dict[str, LotteryConfig] = {}
        self._load_default_configs()
        self._load_custom_configs()
    
    def _load_default_configs(self):
        """載入預設配置"""
        default_configs = {
            LotteryType.POWERCOLOR.value: LotteryConfig(
                lottery_type=LotteryType.POWERCOLOR.value,
                name="威力彩",
                description="台灣威力彩，選6個主號碼(1-38)和1個特別號(1-8)",
                main_numbers_count=6,
                main_numbers_range=(1, 38),
                special_numbers_count=1,
                special_numbers_range=(1, 8),
                draw_days=["monday", "thursday"],
                draw_time="20:30",
                prediction_advance_hours=2,
                supported_strategies=[
                    "frequency_based", "pattern_based", "trend_analysis",
                    "neural_network", "ensemble"
                ],
                min_historical_data=50,
                confidence_threshold=70.0
            ),
            
            LotteryType.LOTTO649.value: LotteryConfig(
                lottery_type=LotteryType.LOTTO649.value,
                name="大樂透",
                description="台灣大樂透，選6個主號碼(1-49)和1個特別號(1-10)",
                main_numbers_count=6,
                main_numbers_range=(1, 49),
                special_numbers_count=1,
                special_numbers_range=(1, 10),
                draw_days=["tuesday", "friday"],
                draw_time="20:30",
                prediction_advance_hours=2,
                supported_strategies=[
                    "frequency_based", "pattern_based", "trend_analysis",
                    "neural_network", "ensemble"
                ],
                min_historical_data=50,
                confidence_threshold=70.0
            ),
            
            LotteryType.DAILYCASH.value: LotteryConfig(
                lottery_type=LotteryType.DAILYCASH.value,
                name="今彩539",
                description="台灣今彩539，選5個主號碼(1-39)",
                main_numbers_count=5,
                main_numbers_range=(1, 39),
                special_numbers_count=0,
                special_numbers_range=None,
                draw_days=["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"],
                draw_time="20:30",
                prediction_advance_hours=1,
                supported_strategies=[
                    "frequency_based", "pattern_based", "trend_analysis",
                    "neural_network", "ensemble"
                ],
                min_historical_data=30,
                confidence_threshold=75.0
            )
        }
        
        self.configs.update(default_configs)
    
    def _load_custom_configs(self):
        """載入自定義配置"""
        config_file = Path(self.config_path)
        
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    custom_configs = json.load(f)
                
                for lottery_type, config_data in custom_configs.items():
                    if self._validate_config(config_data):
                        self.configs[lottery_type] = LotteryConfig(**config_data)
                        logger.info(f"載入自定義配置: {lottery_type}")
                    else:
                        logger.warning(f"無效的自定義配置: {lottery_type}")
                        
            except Exception as e:
                logger.error(f"載入自定義配置失敗: {e}")
    
    def _validate_config(self, config_data: Dict[str, Any]) -> bool:
        """驗證配置數據"""
        required_fields = [
            'lottery_type', 'name', 'main_numbers_count', 'main_numbers_range',
            'special_numbers_count', 'draw_days', 'supported_strategies'
        ]
        
        for field in required_fields:
            if field not in config_data:
                logger.error(f"配置缺少必要字段: {field}")
                return False
        
        return True
    
    def get_config(self, lottery_type: str) -> Optional[LotteryConfig]:
        """獲取彩票配置"""
        return self.configs.get(lottery_type)
    
    def get_all_configs(self) -> Dict[str, LotteryConfig]:
        """獲取所有配置"""
        return self.configs.copy()
    
    def add_config(self, lottery_type: str, config: LotteryConfig) -> bool:
        """添加新配置"""
        try:
            self.configs[lottery_type] = config
            self._save_custom_configs()
            logger.info(f"添加新彩票配置: {lottery_type}")
            return True
        except Exception as e:
            logger.error(f"添加配置失敗: {e}")
            return False
    
    def _save_custom_configs(self):
        """保存自定義配置"""
        try:
            config_file = Path(self.config_path)
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 只保存非預設配置
            custom_configs = {}
            default_types = [lt.value for lt in LotteryType]
            
            for lottery_type, config in self.configs.items():
                if lottery_type not in default_types:
                    custom_configs[lottery_type] = asdict(config)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(custom_configs, f, ensure_ascii=False, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"保存配置失敗: {e}")

class StrategyFactory:
    """策略工廠"""
    
    def __init__(self):
        self.strategies: Dict[str, Type[IPredictionStrategy]] = {}
        self._register_builtin_strategies()
    
    def _register_builtin_strategies(self):
        """註冊內置策略"""
        # 這裡註冊各種內置策略
        # 實際實現時會載入各種策略類
        pass
    
    def register_strategy(self, strategy_name: str, strategy_class: Type[IPredictionStrategy]):
        """註冊新策略"""
        self.strategies[strategy_name] = strategy_class
        logger.info(f"註冊預測策略: {strategy_name}")
    
    def create_strategy(self, strategy_name: str) -> Optional[IPredictionStrategy]:
        """創建策略實例"""
        strategy_class = self.strategies.get(strategy_name)
        if strategy_class:
            return strategy_class()
        else:
            logger.error(f"未找到策略: {strategy_name}")
            return None
    
    def get_available_strategies(self) -> List[str]:
        """獲取可用策略列表"""
        return list(self.strategies.keys())

class CrossLotteryAnalyzer:
    """跨彩票分析器"""
    
    def __init__(self, db_manager: DBManager):
        self.db_manager = db_manager
        self.analysis_cache = {}
        self.cache_expiry = timedelta(hours=6)
    
    def analyze_correlations(self, lottery_types: List[str], 
                           analysis_window: int = 100) -> Dict[str, Dict[str, float]]:
        """分析彩票間的相關性"""
        try:
            correlation_matrix = {}
            
            # 載入各彩票類型的歷史數據
            lottery_data = {}
            for lottery_type in lottery_types:
                df = self.db_manager.load_lottery_data(lottery_type)
                if not df.empty:
                    # 提取主號碼序列
                    main_numbers = []
                    for _, row in df.tail(analysis_window).iterrows():
                        numbers = [row[f'Anumber{i}'] for i in range(1, 7) if f'Anumber{i}' in row]
                        main_numbers.extend(numbers)
                    lottery_data[lottery_type] = main_numbers
            
            # 計算相關性
            for type1 in lottery_types:
                correlation_matrix[type1] = {}
                for type2 in lottery_types:
                    if type1 in lottery_data and type2 in lottery_data:
                        if type1 == type2:
                            correlation_matrix[type1][type2] = 1.0
                        else:
                            # 計算號碼出現頻率的相關性
                            freq1 = self._calculate_frequency(lottery_data[type1])
                            freq2 = self._calculate_frequency(lottery_data[type2])
                            
                            # 找出共同號碼範圍
                            common_numbers = set(freq1.keys()).intersection(set(freq2.keys()))
                            
                            if len(common_numbers) > 10:  # 需要足夠的共同號碼
                                freq1_values = [freq1.get(num, 0) for num in common_numbers]
                                freq2_values = [freq2.get(num, 0) for num in common_numbers]
                                
                                correlation = np.corrcoef(freq1_values, freq2_values)[0, 1]
                                correlation_matrix[type1][type2] = correlation if not np.isnan(correlation) else 0.0
                            else:
                                correlation_matrix[type1][type2] = 0.0
                    else:
                        correlation_matrix[type1][type2] = 0.0
            
            return correlation_matrix
            
        except Exception as e:
            logger.error(f"分析相關性失敗: {e}")
            return {}
    
    def _calculate_frequency(self, numbers: List[int]) -> Dict[int, float]:
        """計算號碼頻率"""
        from collections import Counter
        counter = Counter(numbers)
        total = len(numbers)
        return {num: count / total for num, count in counter.items()}
    
    def find_trend_similarities(self, lottery_types: List[str]) -> Dict[str, List[str]]:
        """尋找趨勢相似性"""
        try:
            similarities = {}
            
            # 分析每個彩票類型的趨勢
            trend_data = {}
            for lottery_type in lottery_types:
                df = self.db_manager.load_lottery_data(lottery_type)
                if not df.empty:
                    # 簡化的趨勢分析 - 計算最近期號碼的變化趨勢
                    recent_data = df.tail(20)
                    trend_vector = self._calculate_trend_vector(recent_data)
                    trend_data[lottery_type] = trend_vector
            
            # 計算趨勢相似性
            for type1, trend1 in trend_data.items():
                similar_types = []
                for type2, trend2 in trend_data.items():
                    if type1 != type2:
                        similarity = self._calculate_trend_similarity(trend1, trend2)
                        if similarity > 0.3:  # 相似度閾值
                            similar_types.append(type2)
                similarities[type1] = similar_types
            
            return similarities
            
        except Exception as e:
            logger.error(f"分析趨勢相似性失敗: {e}")
            return {}
    
    def _calculate_trend_vector(self, df: pd.DataFrame) -> List[float]:
        """計算趨勢向量"""
        try:
            # 簡化實現：計算號碼範圍的變化趨勢
            trends = []
            
            for i in range(len(df) - 1):
                current_numbers = [df.iloc[i][f'Anumber{j}'] for j in range(1, 7) if f'Anumber{j}' in df.columns]
                next_numbers = [df.iloc[i + 1][f'Anumber{j}'] for j in range(1, 7) if f'Anumber{j}' in df.columns]
                
                if current_numbers and next_numbers:
                    # 計算平均值變化
                    avg_change = np.mean(next_numbers) - np.mean(current_numbers)
                    trends.append(avg_change)
            
            return trends
            
        except Exception as e:
            logger.error(f"計算趨勢向量失敗: {e}")
            return []
    
    def _calculate_trend_similarity(self, trend1: List[float], trend2: List[float]) -> float:
        """計算趨勢相似性"""
        try:
            if not trend1 or not trend2:
                return 0.0
            
            # 使用相關係數計算相似性
            min_length = min(len(trend1), len(trend2))
            if min_length < 5:
                return 0.0
            
            t1 = trend1[-min_length:]
            t2 = trend2[-min_length:]
            
            correlation = np.corrcoef(t1, t2)[0, 1]
            return abs(correlation) if not np.isnan(correlation) else 0.0
            
        except Exception as e:
            logger.error(f"計算趨勢相似性失敗: {e}")
            return 0.0
    
    def identify_pattern_transfers(self, lottery_types: List[str]) -> List[Dict[str, Any]]:
        """識別模式轉移"""
        try:
            transfers = []
            
            # 分析每個彩票類型的模式
            patterns = {}
            for lottery_type in lottery_types:
                df = self.db_manager.load_lottery_data(lottery_type)
                if not df.empty:
                    patterns[lottery_type] = self._extract_patterns(df.tail(50))
            
            # 尋找模式轉移
            for source_type, source_patterns in patterns.items():
                for target_type, target_patterns in patterns.items():
                    if source_type != target_type:
                        transfer_score = self._calculate_pattern_transfer_score(
                            source_patterns, target_patterns
                        )
                        
                        if transfer_score > 0.2:  # 轉移分數閾值
                            transfers.append({
                                'source_lottery': source_type,
                                'target_lottery': target_type,
                                'transfer_score': transfer_score,
                                'pattern_types': self._identify_transferable_patterns(
                                    source_patterns, target_patterns
                                )
                            })
            
            return transfers
            
        except Exception as e:
            logger.error(f"識別模式轉移失敗: {e}")
            return []
    
    def _extract_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """提取模式"""
        try:
            patterns = {
                'odd_even_ratio': [],
                'sum_ranges': [],
                'gap_patterns': [],
                'consecutive_counts': []
            }
            
            for _, row in df.iterrows():
                numbers = [row[f'Anumber{i}'] for i in range(1, 7) if f'Anumber{i}' in row]
                
                if numbers:
                    # 奇偶比例
                    odd_count = sum(1 for n in numbers if n % 2 == 1)
                    patterns['odd_even_ratio'].append(odd_count / len(numbers))
                    
                    # 數字和範圍
                    patterns['sum_ranges'].append(sum(numbers))
                    
                    # 間隔模式
                    sorted_numbers = sorted(numbers)
                    gaps = [sorted_numbers[i+1] - sorted_numbers[i] for i in range(len(sorted_numbers)-1)]
                    patterns['gap_patterns'].append(np.mean(gaps))
                    
                    # 連續數字計數
                    consecutive = 0
                    for i in range(len(sorted_numbers)-1):
                        if sorted_numbers[i+1] - sorted_numbers[i] == 1:
                            consecutive += 1
                    patterns['consecutive_counts'].append(consecutive)
            
            return patterns
            
        except Exception as e:
            logger.error(f"提取模式失敗: {e}")
            return {}
    
    def _calculate_pattern_transfer_score(self, source_patterns: Dict[str, Any], 
                                        target_patterns: Dict[str, Any]) -> float:
        """計算模式轉移分數"""
        try:
            total_score = 0.0
            pattern_count = 0
            
            for pattern_type in source_patterns:
                if pattern_type in target_patterns:
                    source_values = source_patterns[pattern_type]
                    target_values = target_patterns[pattern_type]
                    
                    if source_values and target_values:
                        # 計算模式相似性
                        correlation = np.corrcoef(
                            source_values[-min(len(source_values), len(target_values)):],
                            target_values[-min(len(source_values), len(target_values)):]
                        )[0, 1]
                        
                        if not np.isnan(correlation):
                            total_score += abs(correlation)
                            pattern_count += 1
            
            return total_score / pattern_count if pattern_count > 0 else 0.0
            
        except Exception as e:
            logger.error(f"計算模式轉移分數失敗: {e}")
            return 0.0
    
    def _identify_transferable_patterns(self, source_patterns: Dict[str, Any], 
                                      target_patterns: Dict[str, Any]) -> List[str]:
        """識別可轉移的模式"""
        transferable = []
        
        for pattern_type in source_patterns:
            if pattern_type in target_patterns:
                source_values = source_patterns[pattern_type]
                target_values = target_patterns[pattern_type]
                
                if source_values and target_values:
                    try:
                        correlation = np.corrcoef(
                            source_values[-min(len(source_values), len(target_values)):],
                            target_values[-min(len(source_values), len(target_values)):]
                        )[0, 1]
                        
                        if not np.isnan(correlation) and abs(correlation) > 0.3:
                            transferable.append(pattern_type)
                    except:
                        continue
        
        return transferable
    
    def generate_cross_lottery_analysis(self, lottery_types: List[str]) -> CrossLotteryAnalysis:
        """生成跨彩票分析報告"""
        try:
            logger.info(f"開始跨彩票分析: {lottery_types}")
            
            # 檢查緩存
            cache_key = "_".join(sorted(lottery_types))
            if cache_key in self.analysis_cache:
                cached_analysis, timestamp = self.analysis_cache[cache_key]
                if datetime.now() - timestamp < self.cache_expiry:
                    logger.info("使用緩存的分析結果")
                    return cached_analysis
            
            # 執行分析
            correlations = self.analyze_correlations(lottery_types)
            similarities = self.find_trend_similarities(lottery_types)
            transfers = self.identify_pattern_transfers(lottery_types)
            
            # 計算推薦分數
            recommendation_score = self._calculate_recommendation_score(
                correlations, similarities, transfers
            )
            
            analysis = CrossLotteryAnalysis(
                correlation_matrix=correlations,
                trend_similarities=similarities,
                pattern_transfers=transfers,
                recommendation_score=recommendation_score,
                analysis_timestamp=datetime.now()
            )
            
            # 緩存結果
            self.analysis_cache[cache_key] = (analysis, datetime.now())
            
            logger.info(f"跨彩票分析完成，推薦分數: {recommendation_score:.3f}")
            return analysis
            
        except Exception as e:
            logger.error(f"跨彩票分析失敗: {e}")
            return CrossLotteryAnalysis(
                correlation_matrix={},
                trend_similarities={},
                pattern_transfers=[],
                recommendation_score=0.0,
                analysis_timestamp=datetime.now()
            )
    
    def _calculate_recommendation_score(self, correlations: Dict[str, Dict[str, float]],
                                      similarities: Dict[str, List[str]],
                                      transfers: List[Dict[str, Any]]) -> float:
        """計算推薦分數"""
        try:
            score = 0.0
            
            # 相關性分數 (40%)
            if correlations:
                total_correlation = 0.0
                count = 0
                for type1, type1_corr in correlations.items():
                    for type2, corr_value in type1_corr.items():
                        if type1 != type2:
                            total_correlation += abs(corr_value)
                            count += 1
                
                if count > 0:
                    score += (total_correlation / count) * 0.4
            
            # 相似性分數 (30%)
            if similarities:
                similarity_score = sum(len(similar_list) for similar_list in similarities.values())
                max_possible = len(similarities) * (len(similarities) - 1)
                if max_possible > 0:
                    score += (similarity_score / max_possible) * 0.3
            
            # 轉移分數 (30%)
            if transfers:
                transfer_score = np.mean([t['transfer_score'] for t in transfers])
                score += transfer_score * 0.3
            
            return min(score, 1.0)  # 限制在0-1範圍
            
        except Exception as e:
            logger.error(f"計算推薦分數失敗: {e}")
            return 0.0

class UniversalPredictor:
    """通用預測器"""
    
    def __init__(self, db_manager: DBManager, assessment_engine: AccuracyAssessmentEngine):
        self.db_manager = db_manager
        self.assessment_engine = assessment_engine
        self.config_manager = LotteryConfigManager()
        self.strategy_factory = StrategyFactory()
        self.cross_lottery_analyzer = CrossLotteryAnalyzer(db_manager)
        
        # 預測器緩存
        self.predictors: Dict[str, EnhancedMultiAlgorithmPredictor] = {}
        
        # 跨彩票學習緩存
        self.cross_learning_cache = {}
        
        # 初始化支持的彩票類型
        self._initialize_predictors()
    
    def _initialize_predictors(self):
        """初始化預測器"""
        try:
            supported_types = [LotteryType.POWERCOLOR.value, LotteryType.LOTTO649.value, LotteryType.DAILYCASH.value]
            
            for lottery_type in supported_types:
                config = self.config_manager.get_config(lottery_type)
                if config:
                    # 檢查是否有足夠的歷史數據
                    df = self.db_manager.load_lottery_data(lottery_type)
                    if len(df) >= config.min_historical_data:
                        predictor = EnhancedMultiAlgorithmPredictor(lottery_type, self.db_manager)
                        self.predictors[lottery_type] = predictor
                        self.assessment_engine.register_predictor(lottery_type, predictor)
                        logger.info(f"初始化 {lottery_type} 預測器成功")
                    else:
                        logger.warning(f"{lottery_type} 歷史數據不足 ({len(df)} < {config.min_historical_data})")
                else:
                    logger.error(f"找不到 {lottery_type} 配置")
                    
        except Exception as e:
            logger.error(f"初始化預測器失敗: {e}")
    
    def add_lottery_type(self, lottery_type: str, config: LotteryConfig) -> bool:
        """添加新的彩票類型支持"""
        try:
            # 添加配置
            if not self.config_manager.add_config(lottery_type, config):
                return False
            
            # 檢查歷史數據
            df = self.db_manager.load_lottery_data(lottery_type)
            if len(df) < config.min_historical_data:
                logger.warning(f"{lottery_type} 歷史數據不足，需要 {config.min_historical_data} 筆")
                return False
            
            # 創建預測器
            predictor = EnhancedMultiAlgorithmPredictor(lottery_type, self.db_manager)
            self.predictors[lottery_type] = predictor
            self.assessment_engine.register_predictor(lottery_type, predictor)
            
            logger.info(f"成功添加新彩票類型: {lottery_type}")
            return True
            
        except Exception as e:
            logger.error(f"添加彩票類型失敗: {e}")
            return False
    
    def predict(self, lottery_type: str, strategy: Optional[str] = None,
                use_cross_learning: bool = True, **kwargs) -> Optional[UniversalPredictionResult]:
        """執行通用預測"""
        try:
            # 檢查彩票類型支持
            if lottery_type not in self.predictors:
                logger.error(f"不支持的彩票類型: {lottery_type}")
                return None
            
            config = self.config_manager.get_config(lottery_type)
            if not config:
                logger.error(f"找不到 {lottery_type} 配置")
                return None
            
            predictor = self.predictors[lottery_type]
            
            # 載入歷史數據
            df = self.db_manager.load_lottery_data(lottery_type)
            if df.empty:
                logger.error(f"無法載入 {lottery_type} 歷史數據")
                return None
            
            # 應用跨彩票學習
            if use_cross_learning:
                enhanced_predictor = self._apply_cross_learning(lottery_type, predictor)
            else:
                enhanced_predictor = predictor
            
            # 執行預測
            if strategy == "ensemble" or strategy is None:
                # 使用集成預測
                ensemble_size = kwargs.get('ensemble_size', 10)
                test_data = df.tail(kwargs.get('data_window', 100))
                
                prediction_result = enhanced_predictor.enhanced_ensemble_predict(
                    test_data, ensemble_size=ensemble_size
                )
            else:
                # 使用指定策略
                test_data = df.tail(kwargs.get('data_window', 100))
                prediction_result = self._predict_with_strategy(
                    enhanced_predictor, strategy, test_data
                )
            
            if not prediction_result:
                logger.error(f"{lottery_type} 預測失敗")
                return None
            
            # 轉換為通用預測結果
            universal_result = self._convert_to_universal_result(
                lottery_type, strategy or "ensemble", prediction_result, config
            )
            
            # 記錄預測結果
            if universal_result:
                self.assessment_engine.record_prediction_result(
                    lottery_type, prediction_result
                )
            
            return universal_result
            
        except Exception as e:
            logger.error(f"通用預測失敗: {e}")
            return None
    
    def _apply_cross_learning(self, target_lottery: str, 
                            predictor: EnhancedMultiAlgorithmPredictor) -> EnhancedMultiAlgorithmPredictor:
        """應用跨彩票學習"""
        try:
            # 獲取相關彩票類型
            related_lotteries = self._find_related_lotteries(target_lottery)
            
            if not related_lotteries:
                return predictor  # 沒有相關彩票，返回原預測器
            
            # 分析跨彩票模式
            all_types = [target_lottery] + related_lotteries
            cross_analysis = self.cross_lottery_analyzer.generate_cross_lottery_analysis(all_types)
            
            # 調整預測器權重
            if cross_analysis.recommendation_score > 0.3:
                adjusted_weights = self._calculate_cross_learning_weights(
                    target_lottery, cross_analysis
                )
                
                # 應用調整後的權重
                if adjusted_weights:
                    for algo_name, weight in adjusted_weights.items():
                        if algo_name in predictor.algorithm_weights:
                            predictor.algorithm_weights[algo_name] = weight
                    
                    logger.info(f"{target_lottery} 應用跨彩票學習，推薦分數: {cross_analysis.recommendation_score:.3f}")
            
            return predictor
            
        except Exception as e:
            logger.error(f"應用跨彩票學習失敗: {e}")
            return predictor
    
    def _find_related_lotteries(self, target_lottery: str) -> List[str]:
        """尋找相關彩票類型"""
        try:
            target_config = self.config_manager.get_config(target_lottery)
            if not target_config:
                return []
            
            related = []
            
            for lottery_type, config in self.config_manager.get_all_configs().items():
                if lottery_type != target_lottery and lottery_type in self.predictors:
                    # 計算相似度
                    similarity_score = self._calculate_lottery_similarity(target_config, config)
                    if similarity_score > 0.5:  # 相似度閾值
                        related.append(lottery_type)
            
            return related
            
        except Exception as e:
            logger.error(f"尋找相關彩票失敗: {e}")
            return []
    
    def _calculate_lottery_similarity(self, config1: LotteryConfig, config2: LotteryConfig) -> float:
        """計算彩票配置相似度"""
        try:
            score = 0.0
            
            # 主號碼數量相似度 (30%)
            if config1.main_numbers_count == config2.main_numbers_count:
                score += 0.3
            elif abs(config1.main_numbers_count - config2.main_numbers_count) == 1:
                score += 0.15
            
            # 號碼範圍相似度 (25%)
            range1_size = config1.main_numbers_range[1] - config1.main_numbers_range[0]
            range2_size = config2.main_numbers_range[1] - config2.main_numbers_range[0]
            
            range_similarity = 1.0 - abs(range1_size - range2_size) / max(range1_size, range2_size)
            score += range_similarity * 0.25
            
            # 特別號相似度 (20%)
            if config1.special_numbers_count == config2.special_numbers_count:
                score += 0.2
            
            # 開獎頻率相似度 (15%)
            freq1 = len(config1.draw_days)
            freq2 = len(config2.draw_days)
            freq_similarity = 1.0 - abs(freq1 - freq2) / max(freq1, freq2)
            score += freq_similarity * 0.15
            
            # 支持策略相似度 (10%)
            common_strategies = set(config1.supported_strategies).intersection(set(config2.supported_strategies))
            all_strategies = set(config1.supported_strategies).union(set(config2.supported_strategies))
            strategy_similarity = len(common_strategies) / len(all_strategies) if all_strategies else 0
            score += strategy_similarity * 0.1
            
            return score
            
        except Exception as e:
            logger.error(f"計算彩票相似度失敗: {e}")
            return 0.0
    
    def _calculate_cross_learning_weights(self, target_lottery: str, 
                                        analysis: CrossLotteryAnalysis) -> Dict[str, float]:
        """計算跨彩票學習權重"""
        try:
            # 獲取目標彩票的當前權重
            predictor = self.predictors[target_lottery]
            current_weights = predictor.algorithm_weights.copy()
            
            # 基於跨彩票分析調整權重
            adjustment_factor = analysis.recommendation_score * 0.1  # 最大調整10%
            
            # 根據相關性調整權重
            if target_lottery in analysis.correlation_matrix:
                correlations = analysis.correlation_matrix[target_lottery]
                avg_correlation = np.mean([abs(corr) for corr in correlations.values() if corr != 1.0])
                
                if avg_correlation > 0.3:
                    # 提升ensemble和trend_analysis權重
                    if 'trend_analysis' in current_weights:
                        current_weights['trend_analysis'] *= (1 + adjustment_factor)
                    if 'enhanced_pattern' in current_weights:
                        current_weights['enhanced_pattern'] *= (1 + adjustment_factor * 0.5)
            
            # 根據模式轉移調整權重
            for transfer in analysis.pattern_transfers:
                if transfer['target_lottery'] == target_lottery and transfer['transfer_score'] > 0.3:
                    # 提升pattern相關權重
                    if 'pattern_based' in current_weights:
                        current_weights['pattern_based'] *= (1 + adjustment_factor)
            
            # 重新標準化權重
            total_weight = sum(current_weights.values())
            if total_weight > 0:
                for algo_name in current_weights:
                    current_weights[algo_name] /= total_weight
            
            return current_weights
            
        except Exception as e:
            logger.error(f"計算跨彩票學習權重失敗: {e}")
            return {}
    
    def _predict_with_strategy(self, predictor: EnhancedMultiAlgorithmPredictor,
                             strategy: str, data: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """使用指定策略預測"""
        try:
            strategy_methods = {
                'frequency_based': predictor.predict_with_enhanced_frequency,
                'pattern_based': predictor.predict_with_enhanced_pattern,
                'trend_analysis': predictor.predict_with_trend_analysis,
                'neural_network': predictor.predict_with_neural_network,
                'board_path': predictor.predict_with_optimized_board_path,
                'feature_enhanced': predictor.predict_with_feature_enhanced
            }
            
            method = strategy_methods.get(strategy)
            if method:
                return method(data)
            else:
                logger.error(f"不支持的策略: {strategy}")
                return None
                
        except Exception as e:
            logger.error(f"策略預測失敗: {e}")
            return None
    
    def _convert_to_universal_result(self, lottery_type: str, strategy: str,
                                   prediction_result: Dict[str, Any],
                                   config: LotteryConfig) -> Optional[UniversalPredictionResult]:
        """轉換為通用預測結果"""
        try:
            main_numbers = prediction_result.get('main_numbers', [])
            special_number = prediction_result.get('special_number')
            
            # 驗證預測結果
            if len(main_numbers) != config.main_numbers_count:
                logger.warning(f"主號碼數量不符: 期望{config.main_numbers_count}, 實際{len(main_numbers)}")
            
            # 檢查號碼範圍
            min_num, max_num = config.main_numbers_range
            for num in main_numbers:
                if not (min_num <= num <= max_num):
                    logger.warning(f"主號碼超出範圍: {num} 不在 [{min_num}, {max_num}]")
            
            # 檢查特別號
            special_numbers = []
            if special_number is not None and config.special_numbers_count > 0:
                special_numbers = [special_number]
                
                if config.special_numbers_range:
                    min_special, max_special = config.special_numbers_range
                    if not (min_special <= special_number <= max_special):
                        logger.warning(f"特別號超出範圍: {special_number} 不在 [{min_special}, {max_special}]")
            
            # 創建通用預測結果
            return UniversalPredictionResult(
                lottery_type=lottery_type,
                prediction_id=f"universal_{lottery_type}_{int(datetime.now().timestamp())}",
                strategy_used=strategy,
                main_numbers=sorted(main_numbers),
                special_numbers=special_numbers if special_numbers else None,
                confidence=prediction_result.get('confidence', 0.0),
                metadata={
                    'algorithm_weights': prediction_result.get('algorithm_weights', {}),
                    'individual_predictions': prediction_result.get('individual_predictions', []),
                    'diversity_bonus': prediction_result.get('diversity_bonus', 0.0),
                    'cross_learning_applied': True,
                    'config_name': config.name
                },
                prediction_timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"轉換預測結果失敗: {e}")
            return None
    
    def batch_predict(self, lottery_types: List[str], **kwargs) -> Dict[str, Optional[UniversalPredictionResult]]:
        """批量預測"""
        results = {}
        
        for lottery_type in lottery_types:
            try:
                result = self.predict(lottery_type, **kwargs)
                results[lottery_type] = result
                logger.info(f"{lottery_type} 批量預測完成")
            except Exception as e:
                logger.error(f"{lottery_type} 批量預測失敗: {e}")
                results[lottery_type] = None
        
        return results
    
    def get_supported_lotteries(self) -> Dict[str, LotteryConfig]:
        """獲取支持的彩票類型"""
        return {
            lottery_type: config 
            for lottery_type, config in self.config_manager.get_all_configs().items()
            if lottery_type in self.predictors
        }
    
    def get_cross_lottery_analysis(self, lottery_types: List[str]) -> CrossLotteryAnalysis:
        """獲取跨彩票分析"""
        return self.cross_lottery_analyzer.generate_cross_lottery_analysis(lottery_types)
    
    def get_prediction_recommendations(self, lottery_type: str) -> Dict[str, Any]:
        """獲取預測建議"""
        try:
            recommendations = {
                'lottery_type': lottery_type,
                'recommended_strategies': [],
                'cross_learning_opportunities': [],
                'confidence_factors': {},
                'optimization_suggestions': []
            }
            
            config = self.config_manager.get_config(lottery_type)
            if not config:
                return recommendations
            
            # 策略推薦
            if lottery_type in self.predictors:
                predictor = self.predictors[lottery_type]
                weights = predictor.algorithm_weights
                
                # 按權重排序推薦策略
                sorted_strategies = sorted(weights.items(), key=lambda x: x[1], reverse=True)
                recommendations['recommended_strategies'] = [
                    {'strategy': strategy, 'weight': weight, 'confidence': weight * 100}
                    for strategy, weight in sorted_strategies[:3]
                ]
            
            # 跨彩票學習機會
            related_lotteries = self._find_related_lotteries(lottery_type)
            if related_lotteries:
                analysis = self.cross_lottery_analyzer.generate_cross_lottery_analysis(
                    [lottery_type] + related_lotteries
                )
                
                recommendations['cross_learning_opportunities'] = [
                    {
                        'related_lottery': related,
                        'similarity_score': analysis.correlation_matrix.get(lottery_type, {}).get(related, 0.0),
                        'recommended': analysis.correlation_matrix.get(lottery_type, {}).get(related, 0.0) > 0.3
                    }
                    for related in related_lotteries
                ]
            
            # 信心度因子
            recent_metrics = self.assessment_engine.tracker.calculate_accuracy_metrics(lottery_type)
            if recent_metrics:
                recommendations['confidence_factors'] = {
                    'historical_accuracy': recent_metrics.get('average_accuracy_score', 0.0),
                    'prediction_consistency': recent_metrics.get('accuracy_consistency', 0.0),
                    'confidence_correlation': recent_metrics.get('confidence_accuracy_correlation', 0.0)
                }
            
            # 優化建議
            if recent_metrics:
                if recent_metrics.get('average_accuracy_score', 0) < 1.5:
                    recommendations['optimization_suggestions'].append("考慮增加歷史數據窗口")
                
                if recent_metrics.get('accuracy_consistency', 1.0) < 0.7:
                    recommendations['optimization_suggestions'].append("建議啟用跨彩票學習")
                
                if recent_metrics.get('confidence_accuracy_correlation', 0) < 0.3:
                    recommendations['optimization_suggestions'].append("重新校準信心度計算")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"獲取預測建議失敗: {e}")
            return {}

# 使用示例
async def main():
    """測試通用預測框架"""
    # 初始化組件
    db_manager = DBManager()
    assessment_engine = AccuracyAssessmentEngine(db_manager)
    universal_predictor = UniversalPredictor(db_manager, assessment_engine)
    
    print("🚀 Phase 3 通用預測框架測試")
    print("=" * 60)
    
    # 獲取支持的彩票類型
    print("\n📋 支持的彩票類型:")
    supported_lotteries = universal_predictor.get_supported_lotteries()
    
    for lottery_type, config in supported_lotteries.items():
        print(f"  {config.name} ({lottery_type})")
        print(f"    主號碼: {config.main_numbers_count}個 ({config.main_numbers_range[0]}-{config.main_numbers_range[1]})")
        if config.special_numbers_count > 0:
            print(f"    特別號: {config.special_numbers_count}個 ({config.special_numbers_range[0]}-{config.special_numbers_range[1]})")
        print(f"    開獎: {', '.join(config.draw_days)} {config.draw_time}")
    
    # 測試跨彩票分析
    print("\n🔗 跨彩票分析:")
    lottery_types = list(supported_lotteries.keys())
    
    if len(lottery_types) > 1:
        analysis = universal_predictor.get_cross_lottery_analysis(lottery_types)
        print(f"  推薦分數: {analysis.recommendation_score:.3f}")
        
        print("  相關性矩陣:")
        for type1, correlations in analysis.correlation_matrix.items():
            for type2, corr in correlations.items():
                if type1 != type2:
                    print(f"    {type1} ↔ {type2}: {corr:.3f}")
        
        if analysis.trend_similarities:
            print("  趨勢相似性:")
            for lottery, similar_types in analysis.trend_similarities.items():
                if similar_types:
                    print(f"    {lottery}: {', '.join(similar_types)}")
    
    # 測試通用預測
    print("\n🎯 通用預測測試:")
    
    for lottery_type in lottery_types:
        try:
            print(f"\n  {lottery_type} 預測:")
            
            # 執行預測
            result = universal_predictor.predict(
                lottery_type, 
                strategy="ensemble",
                use_cross_learning=True,
                ensemble_size=5
            )
            
            if result:
                print(f"    預測號碼: {result.main_numbers}")
                if result.special_numbers:
                    print(f"    特別號: {result.special_numbers}")
                print(f"    信心度: {result.confidence:.1f}%")
                print(f"    策略: {result.strategy_used}")
                print(f"    預測ID: {result.prediction_id}")
            else:
                print("    預測失敗")
                
        except Exception as e:
            print(f"    預測錯誤: {e}")
    
    # 測試批量預測
    print("\n📊 批量預測測試:")
    batch_results = universal_predictor.batch_predict(
        lottery_types, 
        strategy="ensemble", 
        ensemble_size=3
    )
    
    for lottery_type, result in batch_results.items():
        if result:
            print(f"  {lottery_type}: {result.main_numbers} (信心度: {result.confidence:.1f}%)")
        else:
            print(f"  {lottery_type}: 預測失敗")
    
    # 獲取預測建議
    print("\n💡 預測建議:")
    for lottery_type in lottery_types:
        recommendations = universal_predictor.get_prediction_recommendations(lottery_type)
        
        if recommendations.get('recommended_strategies'):
            print(f"\n  {lottery_type} 建議:")
            print("    推薦策略:")
            for strategy_rec in recommendations['recommended_strategies']:
                print(f"      {strategy_rec['strategy']}: {strategy_rec['confidence']:.1f}%")
            
            if recommendations.get('optimization_suggestions'):
                print("    優化建議:")
                for suggestion in recommendations['optimization_suggestions']:
                    print(f"      • {suggestion}")
    
    print("\n✅ 通用預測框架測試完成")

if __name__ == "__main__":
    asyncio.run(main())