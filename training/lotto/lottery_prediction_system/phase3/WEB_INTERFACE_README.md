# Phase 3.4 Web API 和用戶界面

## 概述

Phase 3.4 提供了完整的 Web API 接口和現代化的 Web 用戶界面，實現了彩票預測系統的 Web 化部署和遠程訪問功能。

## 核心組件

### 1. FastAPI 後端服務 (`web_api.py`)
- **RESTful API 設計**: 完整的預測、評估、分析接口
- **異步請求處理**: 支持高並發預測請求
- **WebSocket 通信**: 實時狀態更新和預測通知
- **自動 API 文檔**: Swagger UI 和 ReDoc 支持
- **CORS 支持**: 跨域請求處理
- **系統監控**: 健康檢查和狀態管理

### 2. Web 用戶界面 (`web_interface.html`)
- **響應式設計**: Bootstrap 5 框架，支持移動設備
- **實時預測**: 單個和批量預測功能
- **智能建議**: 預測策略推薦和跨彩票分析
- **數據可視化**: Chart.js 圖表和統計分析
- **實時更新**: WebSocket 實時數據同步
- **現代化 UI**: 漸變色彩和動畫效果

### 3. 服務器啟動器 (`start_web_server.py`)
- **一鍵啟動**: 同時啟動 API 服務器和靜態文件服務
- **端口管理**: 自動檢測和分配可用端口
- **瀏覽器集成**: 自動打開 Web 界面
- **多線程運行**: API 和靜態文件服務並行運行

## API 接口文檔

### 核心端點

#### 系統狀態
```
GET /              # 系統歡迎信息
GET /health        # 系統健康檢查
GET /lotteries     # 獲取支持的彩票類型
```

#### 預測功能
```
POST /predict      # 創建單個預測
POST /predict/batch # 創建批量預測
GET /predictions/{id} # 獲取預測結果
```

#### 評估和分析
```
POST /evaluate     # 評估預測準確度
GET /recommendations/{type} # 獲取預測建議
GET /analytics/{type} # 獲取分析統計
```

#### 實時數據
```
GET /realtime/status # 實時數據狀態
WS /ws             # WebSocket 實時通信
```

### API 請求示例

#### 單個預測
```json
POST /predict
{
  "lottery_type": "powercolor",
  "strategy": "ensemble",
  "use_cross_learning": true,
  "ensemble_size": 10,
  "data_window": 100
}
```

#### 批量預測
```json
POST /predict/batch
{
  "lottery_types": ["powercolor", "lotto649"],
  "strategy": "ensemble",
  "use_cross_learning": true,
  "ensemble_size": 5
}
```

#### 預測評估
```json
POST /evaluate
{
  "prediction_id": "uuid-string",
  "actual_numbers": [1, 15, 22, 28, 31, 38],
  "actual_special": 5
}
```

## 快速開始

### 1. 安裝依賴
```bash
pip install -r requirements_web.txt
```

### 2. 啟動服務器
```bash
# 方法一：使用啟動器 (推薦)
python phase3/start_web_server.py

# 方法二：手動啟動 API
cd phase3
python -m uvicorn web_api:app --host localhost --port 8000 --reload
```

### 3. 訪問界面
- **Web 界面**: http://localhost:8080
- **API 文檔**: http://localhost:8000/docs
- **API 替代文檔**: http://localhost:8000/redoc

### 4. 測試 API
```bash
python test_web_api.py
```

## Web 界面功能

### 主要功能模塊

#### 1. 系統監控儀表板
- 系統狀態實時監控
- 活躍預測數量統計
- 運行時間和健康指標
- WebSocket 連接狀態

#### 2. 智能預測面板
- 彩票類型選擇器
- 預測策略配置
- 集成大小和數據窗口調整
- 跨彩票學習開關
- 單個和批量預測按鈕

#### 3. 預測結果展示
- 號碼球動態顯示
- 信心度進度條
- 預測策略和時間戳
- 結果格式化和美化

#### 4. 智能建議系統
- 推薦策略顯示
- 跨學習機會分析
- 優化建議提示
- 動態內容更新

#### 5. 歷史數據分析
- 預測歷史記錄表格
- 準確度趨勢圖表
- 成功率統計
- 分頁和篩選功能

### UI/UX 特性
- **響應式設計**: 支持手機、平板和桌面
- **暗色主題**: 現代化配色方案
- **動畫效果**: 流暢的過渡和互動
- **實時更新**: WebSocket 驅動的實時數據
- **載入狀態**: 清晰的處理進度指示
- **錯誤處理**: 友好的錯誤提示和恢復

## 技術架構

### 後端技術棧
- **FastAPI**: 現代 Python Web 框架
- **Uvicorn**: ASGI 服務器
- **Pydantic**: 數據驗證和序列化
- **WebSockets**: 實時雙向通信
- **Asyncio**: 異步編程支持

### 前端技術棧
- **Bootstrap 5**: 響應式 CSS 框架
- **Chart.js**: 數據可視化圖表庫
- **Font Awesome**: 圖標字體庫
- **WebSocket API**: 瀏覽器原生 WebSocket
- **Fetch API**: 現代 HTTP 客戶端

### 系統集成
- **Universal Predictor**: 統一預測接口
- **Accuracy Assessment**: 準確度評估引擎
- **Real-time Manager**: 實時數據管理
- **Auto Scheduler**: 自動預測調度

## 性能優化

### API 性能
- **異步處理**: 所有 I/O 操作異步化
- **背景任務**: 預測記錄和日誌異步處理
- **連接池**: 數據庫連接復用
- **緩存策略**: 預測結果和配置緩存

### 前端性能
- **懶加載**: 圖表和數據按需載入
- **防抖處理**: 用戶輸入防抖優化
- **內容壓縮**: 靜態資源壓縮
- **CDN 加速**: 第三方庫 CDN 加載

## 安全考慮

### API 安全
- **CORS 配置**: 限制跨域訪問
- **輸入驗證**: Pydantic 模型驗證
- **錯誤處理**: 安全的錯誤信息返回
- **超時設置**: 防止長時間阻塞

### 部署安全
- **HTTPS 支持**: 生產環境 SSL 配置
- **防火牆配置**: 端口和訪問控制
- **認證機制**: 可選的用戶認證 (預留)
- **日誌監控**: 安全事件記錄

## 監控和維護

### 系統監控
- **健康檢查**: `/health` 端點監控
- **性能指標**: 響應時間和成功率
- **資源使用**: CPU、內存和網絡監控
- **錯誤日誌**: 詳細的錯誤追蹤

### 維護操作
- **日誌輪轉**: 防止日誌文件過大
- **數據清理**: 定期清理歷史預測數據
- **性能調優**: 基於監控數據的優化
- **版本更新**: 平滑的系統升級

## 故障排除

### 常見問題

#### API 無法啟動
```bash
# 檢查端口占用
netstat -an | grep 8000

# 檢查依賴安裝
pip list | grep fastapi

# 查看詳細錯誤
python phase3/web_api.py
```

#### Web 界面無法訪問
```bash
# 檢查靜態服務器
curl http://localhost:8080

# 檢查 CORS 設置
curl -H "Origin: http://localhost:8080" http://localhost:8000/lotteries
```

#### WebSocket 連接失敗
```javascript
// 瀏覽器控制台檢查
const ws = new WebSocket('ws://localhost:8000/ws');
ws.onopen = () => console.log('Connected');
ws.onerror = (e) => console.error('Error:', e);
```

### 性能問題診斷
1. **API 響應慢**: 檢查預測器初始化和數據庫查詢
2. **內存使用高**: 檢查預測結果緩存和清理策略
3. **CPU 使用高**: 檢查並發預測數量和算法複雜度

## 擴展開發

### 添加新 API 端點
```python
@app.get("/custom-endpoint", tags=["自定義"])
async def custom_endpoint():
    return {"message": "自定義功能"}
```

### 自定義前端組件
```javascript
function createCustomChart(data) {
    // 自定義圖表實現
}
```

### 集成第三方服務
```python
# 添加外部 API 集成
async def external_service_integration():
    async with httpx.AsyncClient() as client:
        response = await client.get("https://api.example.com/data")
        return response.json()
```

## 版本歷史

### v3.4.0 (當前版本)
- ✅ 完整的 FastAPI 後端服務
- ✅ 響應式 Web 用戶界面
- ✅ WebSocket 實時通信
- ✅ 批量預測支持
- ✅ 系統監控和健康檢查
- ✅ 自動 API 文檔生成

### 未來版本規劃
- 🔄 用戶認證和權限管理
- 🔄 預測結果導出功能
- 🔄 多語言國際化支持
- 🔄 移動應用 API 接口
- 🔄 實時推送通知
- 🔄 高級分析和報表

## 聯絡和支持

如有問題或建議，請通過以下方式聯絡：
- 系統日誌檢查
- API 文檔參考 (`/docs`)
- 測試腳本執行 (`test_web_api.py`)
- 性能監控分析