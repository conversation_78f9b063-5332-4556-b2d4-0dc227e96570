#!/usr/bin/env python3
"""
Phase 3.5 預測結果追蹤和統計分析系統
全面的預測追蹤、結果驗證、統計分析和報告生成系統
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import json
import logging
from pathlib import Path
import asyncio
from concurrent.futures import ThreadPoolExecutor
import statistics

# 本地導入
from data.db_manager import DBManager

# 配置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# === 數據模型定義 ===

class PredictionStatus(Enum):
    """預測狀態枚舉"""
    PENDING = "pending"           # 待驗證
    VERIFIED = "verified"         # 已驗證
    EXPIRED = "expired"           # 已過期
    CANCELLED = "cancelled"       # 已取消

class AnalysisTimeframe(Enum):
    """分析時間框架"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"

@dataclass
class PredictionRecord:
    """預測記錄數據結構"""
    prediction_id: str
    lottery_type: str
    period: str
    predicted_main_numbers: List[int]
    predicted_special_numbers: Optional[List[int]]
    actual_main_numbers: Optional[List[int]]
    actual_special_numbers: Optional[List[int]]
    strategy_used: str
    confidence: float
    algorithm_weights: Dict[str, float]
    prediction_timestamp: datetime
    draw_timestamp: Optional[datetime]
    verification_timestamp: Optional[datetime]
    status: PredictionStatus
    match_count: Optional[int]
    special_match: Optional[bool]
    prize_level: Optional[int]
    metadata: Dict[str, Any]

@dataclass
class StatisticsReport:
    """統計報告數據結構"""
    report_id: str
    lottery_type: str
    timeframe: AnalysisTimeframe
    start_date: datetime
    end_date: datetime
    total_predictions: int
    verified_predictions: int
    accuracy_metrics: Dict[str, float]
    strategy_performance: Dict[str, Dict[str, float]]
    trend_analysis: Dict[str, Any]
    recommendations: List[str]
    generated_timestamp: datetime

@dataclass
class PerformanceMetrics:
    """性能指標數據結構"""
    accuracy_rate: float
    average_confidence: float
    confidence_accuracy_correlation: float
    strategy_success_rates: Dict[str, float]
    temporal_consistency: float
    improvement_trend: float

# === 預測追蹤管理器 ===

class PredictionTracker:
    """預測追蹤管理器"""
    
    def __init__(self, db_manager: DBManager):
        self.db_manager = db_manager
        self.tracking_db_path = "data/prediction_tracking.db"
        self._initialize_tracking_database()
        
    def _initialize_tracking_database(self):
        """初始化追蹤數據庫"""
        try:
            with sqlite3.connect(self.tracking_db_path) as conn:
                cursor = conn.cursor()
                
                # 預測記錄表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS prediction_records (
                        prediction_id TEXT PRIMARY KEY,
                        lottery_type TEXT NOT NULL,
                        period TEXT,
                        predicted_main_numbers TEXT NOT NULL,
                        predicted_special_numbers TEXT,
                        actual_main_numbers TEXT,
                        actual_special_numbers TEXT,
                        strategy_used TEXT NOT NULL,
                        confidence REAL NOT NULL,
                        algorithm_weights TEXT,
                        prediction_timestamp TIMESTAMP NOT NULL,
                        draw_timestamp TIMESTAMP,
                        verification_timestamp TIMESTAMP,
                        status TEXT NOT NULL DEFAULT 'pending',
                        match_count INTEGER,
                        special_match BOOLEAN,
                        prize_level INTEGER,
                        metadata TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 統計報告表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS statistics_reports (
                        report_id TEXT PRIMARY KEY,
                        lottery_type TEXT NOT NULL,
                        timeframe TEXT NOT NULL,
                        start_date TIMESTAMP NOT NULL,
                        end_date TIMESTAMP NOT NULL,
                        total_predictions INTEGER NOT NULL,
                        verified_predictions INTEGER NOT NULL,
                        accuracy_metrics TEXT,
                        strategy_performance TEXT,
                        trend_analysis TEXT,
                        recommendations TEXT,
                        generated_timestamp TIMESTAMP NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 性能指標歷史表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS performance_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        lottery_type TEXT NOT NULL,
                        calculation_date TIMESTAMP NOT NULL,
                        timeframe TEXT NOT NULL,
                        metrics TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 創建索引
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_lottery_type ON prediction_records(lottery_type)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_status ON prediction_records(status)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_prediction_timestamp ON prediction_records(prediction_timestamp)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_draw_timestamp ON prediction_records(draw_timestamp)")
                
                conn.commit()
                logger.info("✅ 預測追蹤數據庫初始化完成")
                
        except Exception as e:
            logger.error(f"❌ 追蹤數據庫初始化失敗: {e}")
            raise
    
    def record_prediction(self, 
                         prediction_id: str,
                         lottery_type: str,
                         predicted_main: List[int],
                         predicted_special: Optional[List[int]],
                         strategy: str,
                         confidence: float,
                         algorithm_weights: Dict[str, float],
                         period: Optional[str] = None,
                         metadata: Optional[Dict[str, Any]] = None) -> bool:
        """記錄預測結果"""
        try:
            with sqlite3.connect(self.tracking_db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT OR REPLACE INTO prediction_records (
                        prediction_id, lottery_type, period,
                        predicted_main_numbers, predicted_special_numbers,
                        strategy_used, confidence, algorithm_weights,
                        prediction_timestamp, status, metadata
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    prediction_id,
                    lottery_type,
                    period,
                    json.dumps(predicted_main),
                    json.dumps(predicted_special) if predicted_special else None,
                    strategy,
                    confidence,
                    json.dumps(algorithm_weights),
                    datetime.now(),
                    PredictionStatus.PENDING.value,
                    json.dumps(metadata) if metadata else None
                ))
                
                conn.commit()
                logger.info(f"✅ 預測記錄已保存: {prediction_id}")
                return True
                
        except Exception as e:
            logger.error(f"❌ 預測記錄失敗: {e}")
            return False
    
    def verify_prediction(self,
                         prediction_id: str,
                         actual_main: List[int],
                         actual_special: Optional[List[int]] = None,
                         draw_timestamp: Optional[datetime] = None) -> bool:
        """驗證預測結果"""
        try:
            with sqlite3.connect(self.tracking_db_path) as conn:
                cursor = conn.cursor()
                
                # 獲取預測記錄
                cursor.execute("""
                    SELECT predicted_main_numbers, predicted_special_numbers, lottery_type
                    FROM prediction_records WHERE prediction_id = ?
                """, (prediction_id,))
                
                result = cursor.fetchone()
                if not result:
                    logger.warning(f"⚠️ 未找到預測記錄: {prediction_id}")
                    return False
                
                predicted_main = json.loads(result[0])
                predicted_special = json.loads(result[1]) if result[1] else None
                lottery_type = result[2]
                
                # 計算匹配結果
                match_count = len(set(predicted_main) & set(actual_main))
                special_match = False
                if predicted_special and actual_special:
                    special_match = bool(set(predicted_special) & set(actual_special))
                
                # 計算獎級
                prize_level = self._calculate_prize_level(
                    lottery_type, match_count, special_match
                )
                
                # 更新記錄
                cursor.execute("""
                    UPDATE prediction_records SET
                        actual_main_numbers = ?,
                        actual_special_numbers = ?,
                        draw_timestamp = ?,
                        verification_timestamp = ?,
                        status = ?,
                        match_count = ?,
                        special_match = ?,
                        prize_level = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE prediction_id = ?
                """, (
                    json.dumps(actual_main),
                    json.dumps(actual_special) if actual_special else None,
                    draw_timestamp or datetime.now(),
                    datetime.now(),
                    PredictionStatus.VERIFIED.value,
                    match_count,
                    special_match,
                    prize_level,
                    prediction_id
                ))
                
                conn.commit()
                logger.info(f"✅ 預測驗證完成: {prediction_id} (匹配: {match_count}, 獎級: {prize_level})")
                return True
                
        except Exception as e:
            logger.error(f"❌ 預測驗證失敗: {e}")
            return False
    
    def _calculate_prize_level(self, lottery_type: str, match_count: int, special_match: bool) -> int:
        """計算獎級"""
        if lottery_type == "powercolor":
            if match_count == 6 and special_match:
                return 1  # 頭獎
            elif match_count == 6:
                return 2  # 貳獎
            elif match_count == 5 and special_match:
                return 3  # 參獎
            elif match_count == 5:
                return 4  # 肆獎
            elif match_count == 4 and special_match:
                return 5  # 伍獎
            elif match_count == 4:
                return 6  # 陸獎
            elif match_count == 3 and special_match:
                return 7  # 柒獎
            elif match_count == 3:
                return 8  # 捌獎
            elif special_match:
                return 9  # 玖獎
        
        elif lottery_type == "lotto649":
            if match_count == 6:
                return 1  # 頭獎
            elif match_count == 5:
                return 2  # 貳獎
            elif match_count == 4:
                return 3  # 參獎
            elif match_count == 3:
                return 4  # 普獎
        
        return 0  # 未中獎
    
    def get_prediction_records(self,
                              lottery_type: Optional[str] = None,
                              status: Optional[PredictionStatus] = None,
                              start_date: Optional[datetime] = None,
                              end_date: Optional[datetime] = None,
                              limit: Optional[int] = None) -> List[PredictionRecord]:
        """獲取預測記錄"""
        try:
            with sqlite3.connect(self.tracking_db_path) as conn:
                query = "SELECT * FROM prediction_records WHERE 1=1"
                params = []
                
                if lottery_type:
                    query += " AND lottery_type = ?"
                    params.append(lottery_type)
                
                if status:
                    query += " AND status = ?"
                    params.append(status.value)
                
                if start_date:
                    query += " AND prediction_timestamp >= ?"
                    params.append(start_date)
                
                if end_date:
                    query += " AND prediction_timestamp <= ?"
                    params.append(end_date)
                
                query += " ORDER BY prediction_timestamp DESC"
                
                if limit:
                    query += f" LIMIT {limit}"
                
                cursor = conn.cursor()
                cursor.execute(query, params)
                
                records = []
                for row in cursor.fetchall():
                    record = PredictionRecord(
                        prediction_id=row[0],
                        lottery_type=row[1],
                        period=row[2],
                        predicted_main_numbers=json.loads(row[3]),
                        predicted_special_numbers=json.loads(row[4]) if row[4] else None,
                        actual_main_numbers=json.loads(row[5]) if row[5] else None,
                        actual_special_numbers=json.loads(row[6]) if row[6] else None,
                        strategy_used=row[7],
                        confidence=row[8],
                        algorithm_weights=json.loads(row[9]),
                        prediction_timestamp=datetime.fromisoformat(row[10]),
                        draw_timestamp=datetime.fromisoformat(row[11]) if row[11] else None,
                        verification_timestamp=datetime.fromisoformat(row[12]) if row[12] else None,
                        status=PredictionStatus(row[13]),
                        match_count=row[14],
                        special_match=row[15],
                        prize_level=row[16],
                        metadata=json.loads(row[17]) if row[17] else {}
                    )
                    records.append(record)
                
                return records
                
        except Exception as e:
            logger.error(f"❌ 獲取預測記錄失敗: {e}")
            return []
    
    def mark_expired_predictions(self, expiry_days: int = 30) -> int:
        """標記過期預測"""
        try:
            expiry_date = datetime.now() - timedelta(days=expiry_days)
            
            with sqlite3.connect(self.tracking_db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    UPDATE prediction_records 
                    SET status = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE status = ? AND prediction_timestamp < ?
                """, (
                    PredictionStatus.EXPIRED.value,
                    PredictionStatus.PENDING.value,
                    expiry_date
                ))
                
                expired_count = cursor.rowcount
                conn.commit()
                
                if expired_count > 0:
                    logger.info(f"✅ 標記 {expired_count} 個過期預測")
                
                return expired_count
                
        except Exception as e:
            logger.error(f"❌ 標記過期預測失敗: {e}")
            return 0

# === 統計分析引擎 ===

class StatisticsAnalyzer:
    """統計分析引擎"""
    
    def __init__(self, prediction_tracker: PredictionTracker):
        self.tracker = prediction_tracker
        
    def calculate_performance_metrics(self,
                                    lottery_type: str,
                                    timeframe: AnalysisTimeframe = AnalysisTimeframe.MONTHLY) -> PerformanceMetrics:
        """計算性能指標"""
        try:
            # 獲取時間範圍
            end_date = datetime.now()
            if timeframe == AnalysisTimeframe.DAILY:
                start_date = end_date - timedelta(days=1)
            elif timeframe == AnalysisTimeframe.WEEKLY:
                start_date = end_date - timedelta(weeks=1)
            elif timeframe == AnalysisTimeframe.MONTHLY:
                start_date = end_date - timedelta(days=30)
            elif timeframe == AnalysisTimeframe.QUARTERLY:
                start_date = end_date - timedelta(days=90)
            else:  # YEARLY
                start_date = end_date - timedelta(days=365)
            
            # 獲取已驗證的預測記錄
            records = self.tracker.get_prediction_records(
                lottery_type=lottery_type,
                status=PredictionStatus.VERIFIED,
                start_date=start_date,
                end_date=end_date
            )
            
            if not records:
                logger.warning(f"⚠️ 無已驗證的預測記錄: {lottery_type}")
                return PerformanceMetrics(0, 0, 0, {}, 0, 0)
            
            # 計算基礎指標
            total_predictions = len(records)
            successful_predictions = sum(1 for r in records if r.match_count >= 2)
            accuracy_rate = successful_predictions / total_predictions if total_predictions > 0 else 0
            
            # 計算平均信心度
            confidences = [r.confidence for r in records]
            average_confidence = statistics.mean(confidences)
            
            # 計算信心度與準確度的相關性
            successes = [1 if r.match_count >= 2 else 0 for r in records]
            confidence_accuracy_correlation = self._calculate_correlation(confidences, successes)
            
            # 計算策略成功率
            strategy_stats = {}
            for record in records:
                strategy = record.strategy_used
                if strategy not in strategy_stats:
                    strategy_stats[strategy] = {'total': 0, 'success': 0}
                
                strategy_stats[strategy]['total'] += 1
                if record.match_count >= 2:
                    strategy_stats[strategy]['success'] += 1
            
            strategy_success_rates = {
                strategy: stats['success'] / stats['total'] if stats['total'] > 0 else 0
                for strategy, stats in strategy_stats.items()
            }
            
            # 計算時間一致性
            temporal_consistency = self._calculate_temporal_consistency(records)
            
            # 計算改進趨勢
            improvement_trend = self._calculate_improvement_trend(records)
            
            return PerformanceMetrics(
                accuracy_rate=accuracy_rate,
                average_confidence=average_confidence,
                confidence_accuracy_correlation=confidence_accuracy_correlation,
                strategy_success_rates=strategy_success_rates,
                temporal_consistency=temporal_consistency,
                improvement_trend=improvement_trend
            )
            
        except Exception as e:
            logger.error(f"❌ 性能指標計算失敗: {e}")
            return PerformanceMetrics(0, 0, 0, {}, 0, 0)
    
    def _calculate_correlation(self, x: List[float], y: List[float]) -> float:
        """計算相關係數"""
        if len(x) != len(y) or len(x) < 2:
            return 0.0
        
        try:
            x_mean = statistics.mean(x)
            y_mean = statistics.mean(y)
            
            numerator = sum((x[i] - x_mean) * (y[i] - y_mean) for i in range(len(x)))
            
            x_var = sum((xi - x_mean) ** 2 for xi in x)
            y_var = sum((yi - y_mean) ** 2 for yi in y)
            
            denominator = (x_var * y_var) ** 0.5
            
            if denominator == 0:
                return 0.0
            
            return numerator / denominator
            
        except:
            return 0.0
    
    def _calculate_temporal_consistency(self, records: List[PredictionRecord]) -> float:
        """計算時間一致性"""
        if len(records) < 7:  # 至少需要一週的數據
            return 0.0
        
        try:
            # 按天分組計算成功率
            daily_success_rates = {}
            for record in records:
                day = record.prediction_timestamp.date()
                if day not in daily_success_rates:
                    daily_success_rates[day] = {'total': 0, 'success': 0}
                
                daily_success_rates[day]['total'] += 1
                if record.match_count >= 2:
                    daily_success_rates[day]['success'] += 1
            
            # 計算每日成功率
            rates = []
            for day_stats in daily_success_rates.values():
                if day_stats['total'] > 0:
                    rate = day_stats['success'] / day_stats['total']
                    rates.append(rate)
            
            if len(rates) < 2:
                return 0.0
            
            # 計算變異係數（標準差/平均值）
            mean_rate = statistics.mean(rates)
            if mean_rate == 0:
                return 0.0
            
            std_dev = statistics.stdev(rates)
            coefficient_of_variation = std_dev / mean_rate
            
            # 一致性 = 1 - 變異係數（越小越一致）
            consistency = max(0, 1 - coefficient_of_variation)
            return consistency
            
        except:
            return 0.0
    
    def _calculate_improvement_trend(self, records: List[PredictionRecord]) -> float:
        """計算改進趨勢"""
        if len(records) < 10:
            return 0.0
        
        try:
            # 按時間排序
            sorted_records = sorted(records, key=lambda r: r.prediction_timestamp)
            
            # 分為前半和後半
            mid_point = len(sorted_records) // 2
            first_half = sorted_records[:mid_point]
            second_half = sorted_records[mid_point:]
            
            # 計算兩半的成功率
            first_success_rate = sum(1 for r in first_half if r.match_count >= 2) / len(first_half)
            second_success_rate = sum(1 for r in second_half if r.match_count >= 2) / len(second_half)
            
            # 改進趨勢 = 後半成功率 - 前半成功率
            improvement = second_success_rate - first_success_rate
            return improvement
            
        except:
            return 0.0
    
    def generate_statistics_report(self,
                                  lottery_type: str,
                                  timeframe: AnalysisTimeframe = AnalysisTimeframe.MONTHLY) -> StatisticsReport:
        """生成統計報告"""
        try:
            # 計算時間範圍
            end_date = datetime.now()
            if timeframe == AnalysisTimeframe.DAILY:
                start_date = end_date - timedelta(days=1)
            elif timeframe == AnalysisTimeframe.WEEKLY:
                start_date = end_date - timedelta(weeks=1)
            elif timeframe == AnalysisTimeframe.MONTHLY:
                start_date = end_date - timedelta(days=30)
            elif timeframe == AnalysisTimeframe.QUARTERLY:
                start_date = end_date - timedelta(days=90)
            else:  # YEARLY
                start_date = end_date - timedelta(days=365)
            
            # 獲取所有預測記錄
            all_records = self.tracker.get_prediction_records(
                lottery_type=lottery_type,
                start_date=start_date,
                end_date=end_date
            )
            
            # 獲取已驗證記錄
            verified_records = [r for r in all_records if r.status == PredictionStatus.VERIFIED]
            
            # 計算性能指標
            metrics = self.calculate_performance_metrics(lottery_type, timeframe)
            
            # 分析策略性能
            strategy_performance = self._analyze_strategy_performance(verified_records)
            
            # 趨勢分析
            trend_analysis = self._analyze_trends(verified_records)
            
            # 生成建議
            recommendations = self._generate_recommendations(metrics, strategy_performance, trend_analysis)
            
            # 創建報告
            report_id = f"{lottery_type}_{timeframe.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            report = StatisticsReport(
                report_id=report_id,
                lottery_type=lottery_type,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date,
                total_predictions=len(all_records),
                verified_predictions=len(verified_records),
                accuracy_metrics={
                    'accuracy_rate': metrics.accuracy_rate,
                    'average_confidence': metrics.average_confidence,
                    'confidence_accuracy_correlation': metrics.confidence_accuracy_correlation,
                    'temporal_consistency': metrics.temporal_consistency,
                    'improvement_trend': metrics.improvement_trend
                },
                strategy_performance=strategy_performance,
                trend_analysis=trend_analysis,
                recommendations=recommendations,
                generated_timestamp=datetime.now()
            )
            
            # 保存報告到數據庫
            self._save_report(report)
            
            return report
            
        except Exception as e:
            logger.error(f"❌ 統計報告生成失敗: {e}")
            raise
    
    def _analyze_strategy_performance(self, records: List[PredictionRecord]) -> Dict[str, Dict[str, float]]:
        """分析策略性能"""
        strategy_stats = {}
        
        for record in records:
            strategy = record.strategy_used
            if strategy not in strategy_stats:
                strategy_stats[strategy] = {
                    'total_predictions': 0,
                    'successful_predictions': 0,
                    'total_confidence': 0,
                    'total_matches': 0,
                    'prize_wins': 0
                }
            
            stats = strategy_stats[strategy]
            stats['total_predictions'] += 1
            stats['total_confidence'] += record.confidence
            stats['total_matches'] += record.match_count or 0
            
            if record.match_count >= 2:
                stats['successful_predictions'] += 1
            
            if record.prize_level and record.prize_level > 0:
                stats['prize_wins'] += 1
        
        # 計算最終指標
        performance = {}
        for strategy, stats in strategy_stats.items():
            total = stats['total_predictions']
            if total > 0:
                performance[strategy] = {
                    'success_rate': stats['successful_predictions'] / total,
                    'average_confidence': stats['total_confidence'] / total,
                    'average_matches': stats['total_matches'] / total,
                    'prize_win_rate': stats['prize_wins'] / total,
                    'total_predictions': total
                }
        
        return performance
    
    def _analyze_trends(self, records: List[PredictionRecord]) -> Dict[str, Any]:
        """分析趨勢"""
        if not records:
            return {}
        
        # 按時間排序
        sorted_records = sorted(records, key=lambda r: r.prediction_timestamp)
        
        # 時間序列分析
        daily_stats = {}
        for record in sorted_records:
            day = record.prediction_timestamp.date()
            if day not in daily_stats:
                daily_stats[day] = {
                    'predictions': 0,
                    'successes': 0,
                    'total_confidence': 0,
                    'total_matches': 0
                }
            
            stats = daily_stats[day]
            stats['predictions'] += 1
            stats['total_confidence'] += record.confidence
            stats['total_matches'] += record.match_count or 0
            
            if record.match_count >= 2:
                stats['successes'] += 1
        
        # 計算趨勢指標
        dates = sorted(daily_stats.keys())
        success_rates = []
        confidence_levels = []
        
        for date in dates:
            stats = daily_stats[date]
            if stats['predictions'] > 0:
                success_rates.append(stats['successes'] / stats['predictions'])
                confidence_levels.append(stats['total_confidence'] / stats['predictions'])
        
        trend_analysis = {
            'total_days': len(dates),
            'success_rate_trend': self._calculate_linear_trend(success_rates),
            'confidence_trend': self._calculate_linear_trend(confidence_levels),
            'daily_stats': {
                str(date): stats for date, stats in daily_stats.items()
            }
        }
        
        return trend_analysis
    
    def _calculate_linear_trend(self, values: List[float]) -> float:
        """計算線性趨勢"""
        if len(values) < 2:
            return 0.0
        
        try:
            n = len(values)
            x = list(range(n))
            
            # 計算線性回歸斜率
            x_mean = statistics.mean(x)
            y_mean = statistics.mean(values)
            
            numerator = sum((x[i] - x_mean) * (values[i] - y_mean) for i in range(n))
            denominator = sum((x[i] - x_mean) ** 2 for i in range(n))
            
            if denominator == 0:
                return 0.0
            
            slope = numerator / denominator
            return slope
            
        except:
            return 0.0
    
    def _generate_recommendations(self,
                                 metrics: PerformanceMetrics,
                                 strategy_performance: Dict[str, Dict[str, float]],
                                 trend_analysis: Dict[str, Any]) -> List[str]:
        """生成建議"""
        recommendations = []
        
        # 基於準確率的建議
        if metrics.accuracy_rate < 0.2:
            recommendations.append("準確率偏低，建議檢查算法參數和數據質量")
        elif metrics.accuracy_rate > 0.4:
            recommendations.append("準確率表現良好，可以考慮增加預測頻率")
        
        # 基於信心度相關性的建議
        if metrics.confidence_accuracy_correlation < 0.1:
            recommendations.append("信心度與準確度相關性較低，建議重新校準信心度計算")
        elif metrics.confidence_accuracy_correlation > 0.3:
            recommendations.append("信心度校準良好，可作為可靠的預測指標")
        
        # 基於策略性能的建議
        if strategy_performance:
            best_strategy = max(strategy_performance.items(), 
                              key=lambda x: x[1]['success_rate'])
            worst_strategy = min(strategy_performance.items(), 
                               key=lambda x: x[1]['success_rate'])
            
            recommendations.append(f"最佳策略：{best_strategy[0]} (成功率: {best_strategy[1]['success_rate']:.1%})")
            
            if worst_strategy[1]['success_rate'] < 0.15:
                recommendations.append(f"建議優化或停用策略：{worst_strategy[0]}")
        
        # 基於趨勢的建議
        if trend_analysis.get('success_rate_trend', 0) > 0.01:
            recommendations.append("預測性能呈上升趨勢，當前策略有效")
        elif trend_analysis.get('success_rate_trend', 0) < -0.01:
            recommendations.append("預測性能呈下降趨勢，建議檢查和調整策略")
        
        # 基於一致性的建議
        if metrics.temporal_consistency < 0.3:
            recommendations.append("預測一致性較低，建議穩定算法參數")
        elif metrics.temporal_consistency > 0.7:
            recommendations.append("預測一致性良好，系統運行穩定")
        
        return recommendations
    
    def _save_report(self, report: StatisticsReport):
        """保存報告到數據庫"""
        try:
            with sqlite3.connect(self.tracker.tracking_db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO statistics_reports (
                        report_id, lottery_type, timeframe, start_date, end_date,
                        total_predictions, verified_predictions, accuracy_metrics,
                        strategy_performance, trend_analysis, recommendations,
                        generated_timestamp
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    report.report_id,
                    report.lottery_type,
                    report.timeframe.value,
                    report.start_date,
                    report.end_date,
                    report.total_predictions,
                    report.verified_predictions,
                    json.dumps(report.accuracy_metrics),
                    json.dumps(report.strategy_performance),
                    json.dumps(report.trend_analysis),
                    json.dumps(report.recommendations),
                    report.generated_timestamp
                ))
                
                conn.commit()
                logger.info(f"✅ 統計報告已保存: {report.report_id}")
                
        except Exception as e:
            logger.error(f"❌ 保存統計報告失敗: {e}")

# === 統合追蹤系統 ===

class IntegratedTrackingSystem:
    """統合追蹤系統"""
    
    def __init__(self, db_manager: DBManager):
        self.db_manager = db_manager
        self.tracker = PredictionTracker(db_manager)
        self.analyzer = StatisticsAnalyzer(self.tracker)
        self.executor = ThreadPoolExecutor(max_workers=4)
        
    async def start_tracking_service(self):
        """啟動追蹤服務"""
        logger.info("🚀 啟動預測追蹤服務")
        
        # 標記過期預測
        expired_count = self.tracker.mark_expired_predictions()
        if expired_count > 0:
            logger.info(f"✅ 處理 {expired_count} 個過期預測")
        
        # 啟動定期任務
        asyncio.create_task(self._periodic_tasks())
        
    async def _periodic_tasks(self):
        """定期任務"""
        while True:
            try:
                # 每小時執行一次清理
                await asyncio.sleep(3600)
                
                # 標記過期預測
                self.tracker.mark_expired_predictions()
                
                # 清理舊報告（保留90天）
                await self._cleanup_old_reports()
                
            except Exception as e:
                logger.error(f"❌ 定期任務執行失敗: {e}")
    
    async def _cleanup_old_reports(self):
        """清理舊報告"""
        try:
            cutoff_date = datetime.now() - timedelta(days=90)
            
            with sqlite3.connect(self.tracker.tracking_db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    DELETE FROM statistics_reports 
                    WHERE generated_timestamp < ?
                """, (cutoff_date,))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                if deleted_count > 0:
                    logger.info(f"✅ 清理 {deleted_count} 個舊報告")
                    
        except Exception as e:
            logger.error(f"❌ 清理舊報告失敗: {e}")
    
    def get_dashboard_data(self, lottery_type: str) -> Dict[str, Any]:
        """獲取儀表板數據"""
        try:
            # 獲取基礎統計
            total_predictions = len(self.tracker.get_prediction_records(lottery_type=lottery_type))
            verified_predictions = len(self.tracker.get_prediction_records(
                lottery_type=lottery_type, 
                status=PredictionStatus.VERIFIED
            ))
            pending_predictions = len(self.tracker.get_prediction_records(
                lottery_type=lottery_type, 
                status=PredictionStatus.PENDING
            ))
            
            # 獲取最新性能指標
            metrics = self.analyzer.calculate_performance_metrics(lottery_type)
            
            # 獲取最近的預測記錄
            recent_records = self.tracker.get_prediction_records(
                lottery_type=lottery_type,
                limit=10
            )
            
            return {
                'lottery_type': lottery_type,
                'total_predictions': total_predictions,
                'verified_predictions': verified_predictions,
                'pending_predictions': pending_predictions,
                'accuracy_rate': metrics.accuracy_rate,
                'average_confidence': metrics.average_confidence,
                'strategy_performance': metrics.strategy_success_rates,
                'recent_records': [asdict(record) for record in recent_records]
            }
            
        except Exception as e:
            logger.error(f"❌ 獲取儀表板數據失敗: {e}")
            return {}
    
    async def generate_comprehensive_report(self, lottery_type: str) -> Dict[str, Any]:
        """生成綜合報告"""
        try:
            reports = {}
            
            # 並行生成不同時間框架的報告
            tasks = []
            for timeframe in [AnalysisTimeframe.WEEKLY, AnalysisTimeframe.MONTHLY, AnalysisTimeframe.QUARTERLY]:
                task = asyncio.create_task(
                    asyncio.get_event_loop().run_in_executor(
                        self.executor,
                        self.analyzer.generate_statistics_report,
                        lottery_type,
                        timeframe
                    )
                )
                tasks.append((timeframe, task))
            
            # 收集結果
            for timeframe, task in tasks:
                try:
                    report = await task
                    reports[timeframe.value] = asdict(report)
                except Exception as e:
                    logger.error(f"❌ {timeframe.value} 報告生成失敗: {e}")
                    reports[timeframe.value] = None
            
            return {
                'lottery_type': lottery_type,
                'generated_timestamp': datetime.now().isoformat(),
                'reports': reports
            }
            
        except Exception as e:
            logger.error(f"❌ 綜合報告生成失敗: {e}")
            return {}

if __name__ == "__main__":
    # 測試示例
    async def test_tracking_system():
        db_manager = DBManager()
        tracking_system = IntegratedTrackingSystem(db_manager)
        
        # 啟動追蹤服務
        await tracking_system.start_tracking_service()
        
        print("✅ 預測追蹤系統測試完成")
    
    asyncio.run(test_tracking_system())