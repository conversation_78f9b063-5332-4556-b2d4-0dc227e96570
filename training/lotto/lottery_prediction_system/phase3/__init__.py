#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phase 3 通用預測框架
包含進階預測算法和框架整合
"""

__version__ = "3.0.0"
__author__ = "Lottery Prediction System"

# 嘗試導入核心組件
try:
    from .universal_prediction_framework import UniversalPredictionFramework
    from .accuracy_assessment_engine import AccuracyAssessmentEngine
    from .prediction_tracking_system import PredictionTrackingSystem
    PHASE3_AVAILABLE = True
except ImportError as e:
    print(f"警告: Phase 3 部分組件導入失敗: {e}")
    PHASE3_AVAILABLE = False

__all__ = ['UniversalPredictionFramework', 'AccuracyAssessmentEngine', 'PredictionTrackingSystem']