#!/usr/bin/env python3
"""
測試多算法集成預測系統
驗證各種算法整合的效果和性能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import DBManager
from prediction.multi_algorithm_predictor import MultiAlgorithmPredictor
from prediction.optimized_board_path_analyzer import OptimizedBoardPathAnalyzer
from prediction.feature_enhanced_predictor import FeatureEnhancedPredictor
import logging
import numpy as np
import pandas as pd
from datetime import datetime

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_multi_algorithm_predictor():
    """測試多算法集成預測器"""
    
    print("🚀 Phase 2 多算法集成預測器測試")
    print("=" * 80)
    
    # 初始化數據庫管理器
    db_manager = DBManager()
    
    # 載入威力彩歷史數據
    print("📊 載入威力彩歷史數據...")
    df = db_manager.load_lottery_data('powercolor')
    
    if df.empty:
        print("❌ 無法載入歷史數據")
        return False
    
    print(f"✅ 載入完成，共 {len(df)} 筆歷史記錄")
    
    # 使用最近200期數據進行測試
    test_data = df.tail(200)
    
    # 初始化多算法預測器
    print("\n🧠 初始化多算法集成預測器...")
    predictor = MultiAlgorithmPredictor('powercolor', db_manager)
    
    # 測試單個算法預測
    print("\n📈 測試各個算法預測效果...")
    
    # 1. 板路分析
    print("  1. 板路分析...")
    try:
        board_result = predictor.predict_with_board_path(test_data)
        if board_result:
            print(f"     ✅ 預測結果: {board_result['main_numbers']} (信心度: {board_result['confidence']:.1f}%)")
        else:
            print("     ❌ 預測失敗")
    except Exception as e:
        print(f"     ❌ 預測失敗: {e}")
    
    # 2. 優化板路分析
    print("  2. 優化板路分析...")
    try:
        optimized_result = predictor.predict_with_optimized_board_path(test_data)
        if optimized_result:
            print(f"     ✅ 預測結果: {optimized_result['main_numbers']} (信心度: {optimized_result['confidence']:.1f}%)")
        else:
            print("     ❌ 預測失敗")
    except Exception as e:
        print(f"     ❌ 預測失敗: {e}")
    
    # 3. 特徵增強預測
    print("  3. 特徵增強預測...")
    try:
        feature_result = predictor.predict_with_feature_enhanced(test_data)
        if feature_result:
            print(f"     ✅ 預測結果: {feature_result['main_numbers']} (信心度: {feature_result['confidence']:.1f}%)")
        else:
            print("     ❌ 預測失敗")
    except Exception as e:
        print(f"     ❌ 預測失敗: {e}")
    
    # 4. 基於頻率的預測
    print("  4. 基於頻率的預測...")
    try:
        freq_result = predictor.predict_with_frequency_based(test_data)
        if freq_result:
            print(f"     ✅ 預測結果: {freq_result['main_numbers']} (信心度: {freq_result['confidence']:.1f}%)")
        else:
            print("     ❌ 預測失敗")
    except Exception as e:
        print(f"     ❌ 預測失敗: {e}")
    
    # 5. 基於模式的預測
    print("  5. 基於模式的預測...")
    try:
        pattern_result = predictor.predict_with_pattern_based(test_data)
        if pattern_result:
            print(f"     ✅ 預測結果: {pattern_result['main_numbers']} (信心度: {pattern_result['confidence']:.1f}%)")
        else:
            print("     ❌ 預測失敗")
    except Exception as e:
        print(f"     ❌ 預測失敗: {e}")
    
    # 6. 統計機器學習預測
    print("  6. 統計機器學習預測...")
    try:
        ml_result = predictor.predict_with_statistical_ml(test_data)
        if ml_result:
            print(f"     ✅ 預測結果: {ml_result['main_numbers']} (信心度: {ml_result['confidence']:.1f}%)")
        else:
            print("     ❌ 預測失敗")
    except Exception as e:
        print(f"     ❌ 預測失敗: {e}")
    
    return True

def test_ensemble_prediction():
    """測試集成預測"""
    
    print("\n\n⚡ Phase 2 集成預測測試")
    print("=" * 80)
    
    # 初始化數據庫管理器
    db_manager = DBManager()
    
    # 載入威力彩歷史數據
    print("📊 載入威力彩歷史數據...")
    df = db_manager.load_lottery_data('powercolor')
    
    if df.empty:
        print("❌ 無法載入歷史數據")
        return False
    
    print(f"✅ 載入完成，共 {len(df)} 筆歷史記錄")
    
    # 使用最近150期數據進行測試
    test_data = df.tail(150)
    
    # 初始化多算法預測器
    print("\n🧠 初始化多算法集成預測器...")
    predictor = MultiAlgorithmPredictor('powercolor', db_manager)
    
    # 測試不同集成大小
    print("\n🚀 測試不同集成大小的效果...")
    
    ensemble_sizes = [5, 10, 15]
    
    for ensemble_size in ensemble_sizes:
        print(f"\n--- 集成大小: {ensemble_size} ---")
        
        try:
            # 進行集成預測
            result = predictor.predict_with_comprehensive_analysis(
                test_data, 
                ensemble_size=ensemble_size,
                generate_report=False
            )
            
            if result:
                print(f"集成預測: {result.get('main_numbers', 'N/A')}")
                print(f"信心度: {result.get('confidence', 0):.1f}%")
                print(f"參與算法數: {len(result.get('individual_predictions', []))}")
                
                # 顯示各算法權重
                weights = result.get('algorithm_weights', {})
                print("算法權重:")
                for method, weight in weights.items():
                    print(f"  {method}: {weight:.3f}")
                
                # 顯示投票統計前5名
                voting_summary = result.get('voting_summary', {})
                if voting_summary:
                    sorted_votes = sorted(voting_summary.items(), key=lambda x: x[1], reverse=True)
                    print("投票統計 Top 5:")
                    for i, (number, votes) in enumerate(sorted_votes[:5], 1):
                        print(f"  {i}. 號碼 {number}: {votes:.3f} 票")
            else:
                print("❌ 集成預測失敗")
                
        except Exception as e:
            print(f"❌ 集成預測失敗: {e}")
            import traceback
            traceback.print_exc()
    
    return True

def test_comprehensive_prediction_with_report():
    """測試綜合預測並生成報告"""
    
    print("\n\n📋 Phase 2 綜合預測報告測試")
    print("=" * 80)
    
    # 初始化數據庫管理器
    db_manager = DBManager()
    
    # 載入威力彩歷史數據
    print("📊 載入威力彩歷史數據...")
    df = db_manager.load_lottery_data('powercolor')
    
    if df.empty:
        print("❌ 無法載入歷史數據")
        return False
    
    print(f"✅ 載入完成，共 {len(df)} 筆歷史記錄")
    
    # 使用最近100期數據進行測試
    test_data = df.tail(100)
    
    # 初始化多算法預測器
    print("\n🧠 初始化多算法集成預測器...")
    predictor = MultiAlgorithmPredictor('powercolor', db_manager)
    
    # 進行綜合預測
    print("\n🚀 進行綜合預測...")
    
    try:
        result = predictor.predict_with_comprehensive_analysis(
            test_data,
            ensemble_size=10,
            generate_report=True
        )
        
        if result:
            print("✅ 綜合預測完成")
            print(f"最終預測: {result.get('main_numbers', 'N/A')}")
            print(f"信心度: {result.get('confidence', 0):.1f}%")
            
            # 顯示報告
            report = result.get('report', '')
            if report:
                print("\n📋 詳細報告:")
                print(report)
            
        else:
            print("❌ 綜合預測失敗")
            
    except Exception as e:
        print(f"❌ 綜合預測失敗: {e}")
        import traceback
        traceback.print_exc()
    
    return True

def backtest_multi_algorithm_predictor():
    """回測多算法集成預測器"""
    
    print("\n\n📈 Phase 2 多算法集成預測器回測")
    print("=" * 80)
    
    # 初始化數據庫管理器
    db_manager = DBManager()
    
    # 載入威力彩歷史數據
    print("📊 載入威力彩歷史數據...")
    df = db_manager.load_lottery_data('powercolor')
    
    if df.empty:
        print("❌ 無法載入歷史數據")
        return False
    
    print(f"✅ 載入完成，共 {len(df)} 筆歷史記錄")
    
    # 初始化預測器
    predictor = MultiAlgorithmPredictor('powercolor', db_manager)
    
    # 回測設置
    test_periods = 6
    print(f"\n🎯 開始回測 (最近{test_periods}期)")
    print("-" * 60)
    
    results = []
    
    for i in range(test_periods):
        # 使用前N-i期數據進行預測
        train_data = df.iloc[:-i-1] if i > 0 else df.iloc[:-1]
        actual_data = df.iloc[-i-1]
        
        try:
            # 進行預測
            prediction = predictor.predict_with_comprehensive_analysis(
                train_data,
                ensemble_size=8,
                generate_report=False
            )
            
            if prediction:
                # 獲取實際結果
                actual_main = [
                    actual_data['Anumber1'], actual_data['Anumber2'], actual_data['Anumber3'],
                    actual_data['Anumber4'], actual_data['Anumber5'], actual_data['Anumber6']
                ]
                actual_special = actual_data['Second_district']
                
                # 計算匹配數
                pred_main = prediction.get('main_numbers', [])
                match_count = len(set(pred_main).intersection(set(actual_main)))
                
                # 檢查特別號匹配
                pred_special = prediction.get('special_number', 0)
                special_match = (pred_special == actual_special)
                
                result = {
                    'period': actual_data['Period'],
                    'predicted_main': pred_main,
                    'actual_main': actual_main,
                    'match_count': match_count,
                    'predicted_special': pred_special,
                    'actual_special': actual_special,
                    'special_match': special_match,
                    'confidence': prediction.get('confidence', 0),
                    'algorithms_used': len(prediction.get('individual_predictions', [])),
                    'method': prediction.get('method', 'unknown')
                }
                
                results.append(result)
                
                print(f"期數 {actual_data['Period']}: 預測 {pred_main} | 實際 {actual_main} | 匹配 {match_count}個 | 信心度 {result['confidence']:.1f}%")
                
            else:
                print(f"期數 {actual_data['Period']}: 預測失敗")
                
        except Exception as e:
            print(f"❌ 回測期數 {actual_data['Period']} 失敗: {e}")
    
    # 分析回測結果
    if results:
        print("\n📊 回測結果分析:")
        
        match_counts = [r['match_count'] for r in results]
        confidences = [r['confidence'] for r in results]
        algorithms_used = [r['algorithms_used'] for r in results]
        
        print(f"平均匹配數: {np.mean(match_counts):.2f}")
        print(f"最高匹配數: {max(match_counts)}")
        print(f"最低匹配數: {min(match_counts)}")
        print(f"平均信心度: {np.mean(confidences):.1f}%")
        print(f"平均使用算法數: {np.mean(algorithms_used):.1f}")
        
        # 匹配分布
        print("\n匹配分布:")
        for i in range(7):
            count = match_counts.count(i)
            percentage = count / len(match_counts) * 100
            print(f"  {i}個匹配: {count}次 ({percentage:.1f}%)")
        
        # 特別號匹配
        special_matches = sum(1 for r in results if r['special_match'])
        special_rate = special_matches / len(results) * 100
        print(f"\n特別號匹配: {special_matches}次 ({special_rate:.1f}%)")
        
        # 與之前方法比較
        avg_match = np.mean(match_counts)
        print(f"\n💡 與之前方法比較:")
        print(f"多算法集成平均匹配數: {avg_match:.2f}")
        print(f"特徵增強版平均匹配數: 0.75 (來自之前測試)")
        print(f"優化版平均匹配數: 1.10 (來自之前測試)")
        
        if avg_match > 1.10:
            improvement = ((avg_match - 1.10) / 1.10) * 100
            print(f"相比優化版改進: +{improvement:.1f}%")
        elif avg_match > 0.75:
            improvement = ((avg_match - 0.75) / 0.75) * 100
            print(f"相比特徵增強版改進: +{improvement:.1f}%")
        else:
            decline = ((1.10 - avg_match) / 1.10) * 100
            print(f"相比優化版變化: -{decline:.1f}%")
        
        # 高信心度預測分析
        high_confidence_results = [r for r in results if r['confidence'] > 80]
        if high_confidence_results:
            high_conf_matches = [r['match_count'] for r in high_confidence_results]
            print(f"\n🎯 高信心度預測 (>80%):")
            print(f"  高信心度預測數: {len(high_confidence_results)}")
            print(f"  高信心度平均匹配數: {np.mean(high_conf_matches):.2f}")
    
    return True

def compare_all_prediction_methods():
    """比較所有預測方法"""
    
    print("\n\n🏆 Phase 2 所有預測方法綜合比較")
    print("=" * 80)
    
    # 初始化數據庫管理器
    db_manager = DBManager()
    
    # 載入威力彩歷史數據
    print("📊 載入威力彩歷史數據...")
    df = db_manager.load_lottery_data('powercolor')
    
    if df.empty:
        print("❌ 無法載入歷史數據")
        return False
    
    print(f"✅ 載入完成，共 {len(df)} 筆歷史記錄")
    
    # 使用最近80期數據
    test_data = df.tail(80)
    
    methods = [
        {
            'name': '優化版板路分析',
            'predictor': OptimizedBoardPathAnalyzer('powercolor', db_manager, 2),
            'method': 'predict_next_numbers_optimized',
            'params': {'candidate_count': 5}
        },
        {
            'name': '特徵增強預測器',
            'predictor': FeatureEnhancedPredictor('powercolor', db_manager, 2),
            'method': 'predict_with_enhanced_features',
            'params': {'candidate_count': 5}
        },
        {
            'name': '多算法集成預測器',
            'predictor': MultiAlgorithmPredictor('powercolor', db_manager),
            'method': 'predict_with_comprehensive_analysis',
            'params': {'ensemble_size': 10, 'generate_report': False}
        }
    ]
    
    print("\n📊 預測方法比較:")
    print("-" * 60)
    
    for method_info in methods:
        try:
            predictor = method_info['predictor']
            method_name = method_info['name']
            method_func = getattr(predictor, method_info['method'])
            params = method_info['params']
            
            print(f"\n{method_name}:")
            
            result = method_func(test_data, **params)
            
            if result:
                print(f"  主要預測: {result.get('main_numbers', 'N/A')}")
                print(f"  信心度: {result.get('confidence', 0):.1f}%")
                
                if 'feature_count' in result:
                    print(f"  特徵數量: {result.get('feature_count', 0)}")
                
                if 'individual_predictions' in result:
                    print(f"  參與算法數: {len(result.get('individual_predictions', []))}")
                    
                    # 顯示算法權重
                    weights = result.get('algorithm_weights', {})
                    if weights:
                        print("  算法權重:")
                        for alg, weight in weights.items():
                            print(f"    {alg}: {weight:.3f}")
                
                candidates = result.get('candidates', [])
                if candidates:
                    print(f"  候選數量: {len(candidates)}")
                    avg_confidence = np.mean([c.get('confidence', 0) for c in candidates])
                    print(f"  平均信心度: {avg_confidence:.1f}%")
            else:
                print("  ❌ 預測失敗")
        
        except Exception as e:
            print(f"❌ {method_name} 測試失敗: {e}")
    
    return True

def main():
    """主測試函數"""
    
    print("🚀 Phase 2 多算法集成預測系統綜合測試")
    print("=" * 90)
    
    # 測試序列
    test_results = []
    
    # 1. 測試多算法預測器
    print("\n1. 測試多算法預測器...")
    test_results.append(test_multi_algorithm_predictor())
    
    # 2. 測試集成預測
    print("\n2. 測試集成預測...")
    test_results.append(test_ensemble_prediction())
    
    # 3. 測試綜合預測和報告
    print("\n3. 測試綜合預測和報告...")
    test_results.append(test_comprehensive_prediction_with_report())
    
    # 4. 回測多算法集成預測器
    print("\n4. 回測多算法集成預測器...")
    test_results.append(backtest_multi_algorithm_predictor())
    
    # 5. 比較所有預測方法
    print("\n5. 比較所有預測方法...")
    test_results.append(compare_all_prediction_methods())
    
    # 總結
    print("\n\n🎯 測試總結")
    print("=" * 90)
    
    test_names = [
        "多算法預測器",
        "集成預測",
        "綜合預測和報告",
        "回測測試",
        "方法比較"
    ]
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"總測試數: {total_tests}")
    print(f"通過測試: {passed_tests}")
    print(f"測試通過率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n各項測試結果:")
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {i+1}. {name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有測試通過！多算法集成預測系統運行正常")
    else:
        print(f"\n⚠️  {total_tests - passed_tests} 項測試失敗，需要檢查")
    
    print("=" * 90)

if __name__ == "__main__":
    main()