#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
威力彩歷史資料功能測試腳本
"""

import requests
import json
import sys
import os

# 測試基礎URL
BASE_URL = "http://localhost:5002"

def test_api_endpoint(endpoint, params=None):
    """
    測試API端點
    """
    try:
        url = f"{BASE_URL}{endpoint}"
        response = requests.get(url, params=params, timeout=10)
        
        print(f"\n測試端點: {endpoint}")
        print(f"狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('success'):
                    print("✅ API調用成功")
                    if 'records' in data:
                        print(f"   返回記錄數: {len(data['records'])}")
                    if 'statistics' in data:
                        print("   包含統計資料")
                    if 'patterns' in data:
                        print("   包含模式分析")
                    if 'trends' in data:
                        print("   包含趨勢分析")
                else:
                    print(f"❌ API返回錯誤: {data.get('error', '未知錯誤')}")
            except json.JSONDecodeError:
                print("❌ 響應不是有效的JSON格式")
        else:
            print(f"❌ HTTP錯誤: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 請求錯誤: {str(e)}")
    except Exception as e:
        print(f"❌ 未知錯誤: {str(e)}")

def test_page_access(page_url):
    """
    測試頁面訪問
    """
    try:
        url = f"{BASE_URL}{page_url}"
        response = requests.get(url, timeout=10)
        
        print(f"\n測試頁面: {page_url}")
        print(f"狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 頁面訪問成功")
            if "威力彩歷史資料查詢系統" in response.text:
                print("   頁面內容正確")
            else:
                print("   ⚠️ 頁面內容可能不完整")
        else:
            print(f"❌ 頁面訪問失敗: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 請求錯誤: {str(e)}")
    except Exception as e:
        print(f"❌ 未知錯誤: {str(e)}")

def main():
    """
    主測試函數
    """
    print("=" * 60)
    print("威力彩歷史資料功能測試")
    print("=" * 60)
    
    # 測試主頁面訪問
    test_page_access("/")
    
    # 測試威力彩歷史頁面
    test_page_access("/powercolor_history")
    
    print("\n" + "=" * 60)
    print("API端點測試")
    print("=" * 60)
    
    # 測試歷史資料API
    test_api_endpoint("/api/powercolor/history", {"limit": 10})
    
    # 測試統計分析API
    test_api_endpoint("/api/powercolor/statistics", {"period": 50})
    
    # 測試模式分析API
    test_api_endpoint("/api/powercolor/patterns", {"period": 30})
    
    # 測試趨勢分析API
    test_api_endpoint("/api/powercolor/trends", {"periods": 15})
    
    # 測試號碼搜尋API
    test_api_endpoint("/api/powercolor/search", {
        "numbers": "1,5,12,23,35,38",
        "type": "contains"
    })
    
    print("\n" + "=" * 60)
    print("測試完成")
    print("=" * 60)
    print("\n如果所有測試都顯示 ✅，表示威力彩歷史資料功能正常運作")
    print("您可以在瀏覽器中訪問 http://localhost:5002/powercolor_history 來使用功能")

if __name__ == "__main__":
    main()