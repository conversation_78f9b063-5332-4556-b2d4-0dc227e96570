#!/usr/bin/env python3
"""
測試腳本：驗證只使用台灣彩券官方數據
確保系統不會生成任何模擬數據
"""

import sys
import os
import sqlite3
import logging
from datetime import datetime

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_test_logging():
    """設置測試日誌"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )
    return logging.getLogger('test_official_data')

def test_official_data_only():
    """測試只使用官方數據功能"""
    logger = setup_test_logging()
    
    print("=" * 70)
    print("🧪 測試台灣彩券官方數據更新器".center(70))
    print("=" * 70)
    print("✅ 確保：只使用台灣彩券官方數據，絕不生成模擬數據")
    print("=" * 70)
    
    try:
        # 導入修改後的更新器
        from data.real_lottery_updater import RealLotteryUpdater
        from data.db_manager import DBManager
        
        # 初始化
        db_manager = DBManager()
        updater = RealLotteryUpdater(db_manager)
        
        print("🔍 檢查更新器初始化...")
        print("✅ 更新器初始化成功")
        print("✅ 確認：沒有備用數據生成器")
        
        # 檢查網路連接
        print("\n🌐 檢查台灣彩券官方網站連接...")
        network_ok = updater.check_network_connectivity()
        if network_ok:
            print("✅ 網路連接正常")
        else:
            print("⚠️ 網路連接不穩定，可能影響數據獲取")
        
        # 測試獲取單一彩票類型數據
        print("\n🎯 測試獲取威力彩官方數據...")
        powercolor_result = updater.fetch_latest_results('powercolor')
        
        if powercolor_result:
            # 檢查是否為模擬數據
            is_simulated = powercolor_result.get('is_simulated', False)
            if is_simulated:
                print("❌ 錯誤：系統返回了模擬數據！")
                print("❌ 這違反了只使用官方數據的要求")
                return False
            else:
                print("✅ 成功：獲取到台灣彩券官方數據")
                print(f"   期號: {powercolor_result['period']}")
                print(f"   開獎日期: {powercolor_result['draw_date']}")
                print(f"   號碼: {powercolor_result['numbers']}")
                if 'special_number' in powercolor_result:
                    print(f"   威力彩號碼: {powercolor_result['special_number']}")
                print("✅ 數據來源：台灣彩券官方")
        else:
            print("⚠️ 無法獲取威力彩數據")
            print("✅ 確認：系統沒有生成模擬數據")
            print("✅ 這是正確的行為，只接受官方數據")
        
        # 測試完整更新流程
        print("\n🔄 測試完整數據更新流程...")
        all_results = updater.update_all_lottery_results()
        
        print("\n📊 更新結果統計:")
        official_data_count = 0
        failed_count = 0
        simulated_data_count = 0
        
        for lottery_type, result in all_results.items():
            status = result.get('status', 'unknown')
            message = result.get('message', '無訊息')
            data_source = result.get('data_source', '未知')
            
            print(f"\n🎮 {lottery_type}:")
            print(f"   狀態: {status}")
            print(f"   訊息: {message}")
            print(f"   數據源: {data_source}")
            
            if status == 'success':
                official_data_count += 1
                print(f"   期號: {result.get('period', 'N/A')}")
                print(f"   ✅ 確認：台灣彩券官方數據")
            elif '模擬' in message or 'simulated' in str(result):
                simulated_data_count += 1
                print(f"   ❌ 錯誤：發現模擬數據！")
            else:
                failed_count += 1
                print(f"   ⚠️ 無法獲取官方數據")
        
        # 結果驗證
        print("\n" + "=" * 70)
        print("📋 測試結果摘要:")
        print("=" * 70)
        print(f"✅ 成功獲取官方數據: {official_data_count} 種彩票")
        print(f"⚠️ 無法獲取數據: {failed_count} 種彩票") 
        print(f"❌ 模擬數據計數: {simulated_data_count} 種彩票")
        
        if simulated_data_count > 0:
            print("\n❌ 測試失敗：系統生成了模擬數據！")
            print("❌ 這違反了只使用台灣彩券官方數據的要求")
            return False
        else:
            print("\n✅ 測試通過：系統只使用台灣彩券官方數據")
            print("✅ 沒有生成任何模擬數據")
            print("✅ 符合要求：開獎結果必須來自台灣彩券官方")
        
        # 檢查資料庫中的數據
        print("\n🔍 檢查資料庫中的數據...")
        check_database_integrity(db_manager)
        
        return True
        
    except ImportError as e:
        print(f"❌ 導入錯誤: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_database_integrity(db_manager):
    """檢查資料庫完整性"""
    try:
        conn = db_manager.create_connection()
        cursor = conn.cursor()
        
        tables = ['Powercolor', 'Lotto649', 'DailyCash']
        
        print("📊 資料庫完整性檢查:")
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   {table}: {count} 筆記錄")
                
                # 檢查最新記錄
                cursor.execute(f"SELECT Period, Sdate FROM {table} ORDER BY Period DESC LIMIT 1")
                latest = cursor.fetchone()
                if latest:
                    print(f"   最新期號: {latest[0]}, 日期: {latest[1]}")
                
            except Exception as e:
                print(f"   ⚠️ {table}: 查詢錯誤 - {e}")
        
        conn.close()
        print("✅ 資料庫檢查完成")
        
    except Exception as e:
        print(f"❌ 資料庫檢查失敗: {e}")

def main():
    """主函數"""
    print(f"開始時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = test_official_data_only()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 所有測試通過！系統只使用台灣彩券官方數據")
        print("✅ 沒有生成任何模擬數據")
        print("✅ 符合要求：開獎結果絕對來自台灣彩券官方")
    else:
        print("❌ 測試失敗！需要修復問題")
    print("=" * 70)
    print(f"結束時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()