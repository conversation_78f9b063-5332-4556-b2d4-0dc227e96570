#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試API響應內容
"""

import requests
import json

def debug_api_response():
    """
    調試API響應的詳細內容
    """
    base_url = "http://127.0.0.1:5002"
    
    lottery_types = [
        ('powercolor', '威力彩'),
        ('lotto649', '大樂透'),
        ('dailycash', '今彩539')
    ]
    
    for lottery_type, name in lottery_types:
        print(f"\n=== {name} API響應調試 ===")
        
        try:
            url = f"{base_url}/api/results"
            params = {
                'lottery_type': lottery_type,
                'limit': 5
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                print("完整響應內容:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                
                # 檢查各個字段
                print(f"\n字段檢查:")
                print(f"- success: {data.get('success')}")
                print(f"- results存在: {'results' in data}")
                print(f"- statistics存在: {'statistics' in data}")
                print(f"- pagination存在: {'pagination' in data}")
                
                if 'statistics' in data:
                    stats = data['statistics']
                    print(f"\n統計信息詳情:")
                    for key, value in stats.items():
                        print(f"  {key}: {value}")
                else:
                    print("\n沒有統計信息字段")
                    
            else:
                print(f"API請求失敗: {response.status_code}")
                print(f"響應內容: {response.text}")
                
        except Exception as e:
            print(f"調試失敗: {str(e)}")
        
        print("-" * 60)

if __name__ == '__main__':
    debug_api_response()