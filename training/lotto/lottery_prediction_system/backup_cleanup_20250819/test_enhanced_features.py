#!/usr/bin/env python3
"""
測試增強特徵工程系統
驗證特徵提取、特徵增強預測器的效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import DBManager
from prediction.enhanced_feature_analyzer import EnhancedFeatureAnalyzer
from prediction.feature_enhanced_predictor import FeatureEnhancedPredictor
from prediction.optimized_board_path_analyzer import OptimizedBoardPathAnalyzer
import logging
import numpy as np
import pandas as pd

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_feature_analyzer():
    """測試增強特徵分析器"""
    
    print("🔍 Phase 2 特徵工程測試")
    print("=" * 70)
    
    # 初始化數據庫管理器
    db_manager = DBManager()
    
    # 載入威力彩歷史數據
    print("📊 載入威力彩歷史數據...")
    df = db_manager.load_lottery_data('powercolor')
    
    if df.empty:
        print("❌ 無法載入歷史數據")
        return False
    
    print(f"✅ 載入完成，共 {len(df)} 筆歷史記錄")
    
    # 初始化特徵分析器
    print("\n🧠 初始化增強特徵分析器...")
    feature_analyzer = EnhancedFeatureAnalyzer('powercolor')
    
    # 使用最近200期數據進行測試
    test_data = df.tail(200)
    
    # 測試各類特徵提取
    print("\n📈 測試特徵提取功能...")
    
    # 1. 基本統計特徵
    print("  1. 基本統計特徵...")
    basic_features = feature_analyzer.extract_basic_statistical_features(test_data)
    print(f"     ✅ 提取完成，共 {len(basic_features)} 個特徵")
    
    # 2. 時間序列特徵
    print("  2. 時間序列特徵...")
    time_series_features = feature_analyzer.extract_time_series_features(test_data)
    print(f"     ✅ 提取完成，共 {len(time_series_features)} 個特徵")
    
    # 3. 模式特徵
    print("  3. 模式特徵...")
    pattern_features = feature_analyzer.extract_pattern_features(test_data)
    print(f"     ✅ 提取完成，共 {len(pattern_features)} 個特徵")
    
    # 4. 高級統計特徵
    print("  4. 高級統計特徵...")
    advanced_features = feature_analyzer.extract_advanced_statistical_features(test_data)
    print(f"     ✅ 提取完成，共 {len(advanced_features)} 個特徵")
    
    # 5. 綜合特徵提取
    print("  5. 綜合特徵提取...")
    all_features = feature_analyzer.extract_all_features(test_data)
    print(f"     ✅ 提取完成，共 {len(all_features)} 個有效特徵")
    
    # 6. 特徵重要性分析
    print("  6. 特徵重要性分析...")
    feature_importance = feature_analyzer.get_feature_importance(test_data)
    print(f"     ✅ 分析完成，Top 5 重要特徵:")
    for i, (feature, importance) in enumerate(list(feature_importance.items())[:5]):
        print(f"        {i+1}. {feature}: {importance:.4f}")
    
    # 7. 特徵矩陣創建
    print("  7. 特徵矩陣創建...")
    feature_matrix, feature_names = feature_analyzer.create_feature_matrix(test_data)
    print(f"     ✅ 創建完成，矩陣形狀: {feature_matrix.shape}")
    
    # 8. 生成特徵報告
    print("  8. 生成特徵報告...")
    report = feature_analyzer.generate_feature_report(test_data)
    print("     ✅ 報告生成完成")
    
    # 顯示報告摘要
    print("\n📋 特徵分析報告摘要:")
    report_lines = report.split('\n')
    for line in report_lines:
        if any(keyword in line for keyword in ['數據概況', '特徵統計', '重要特徵', '關鍵統計']):
            print(f"  {line}")
    
    return True

def test_feature_enhanced_predictor():
    """測試特徵增強預測器"""
    
    print("\n\n⚡ Phase 2 特徵增強預測器測試")
    print("=" * 70)
    
    # 初始化數據庫管理器
    db_manager = DBManager()
    
    # 載入威力彩歷史數據
    print("📊 載入威力彩歷史數據...")
    df = db_manager.load_lottery_data('powercolor')
    
    if df.empty:
        print("❌ 無法載入歷史數據")
        return False
    
    print(f"✅ 載入完成，共 {len(df)} 筆歷史記錄")
    
    # 使用最近150期數據進行測試
    test_data = df.tail(150)
    
    # 測試不同優化級別
    print("\n🚀 測試不同優化級別的特徵增強預測器...")
    
    for optimization_level in [1, 2, 3]:
        print(f"\n--- 優化級別 {optimization_level} ---")
        
        try:
            # 初始化特徵增強預測器
            predictor = FeatureEnhancedPredictor(
                lottery_type='powercolor',
                db_manager=db_manager,
                optimization_level=optimization_level
            )
            
            # 進行預測
            result = predictor.predict_with_enhanced_features(
                test_data, 
                candidate_count=5, 
                generate_report=False
            )
            
            # 顯示結果
            print(f"主要預測: {result.get('main_numbers', 'N/A')}")
            print(f"信心度: {result.get('confidence', 0):.1f}%")
            print(f"特徵數量: {result.get('feature_count', 0)}")
            print(f"板路信心度: {result.get('board_confidence', 0):.1f}%")
            print(f"特徵貢獻度: {result.get('feature_contribution', 0):.1f}%")
            
            # 顯示候選組合
            candidates = result.get('candidates', [])
            if candidates:
                print("候選組合:")
                for i, candidate in enumerate(candidates, 1):
                    main_nums = candidate.get('main_numbers', [])
                    confidence = candidate.get('confidence', 0)
                    feature_contrib = candidate.get('feature_contribution', 0)
                    print(f"  {i}. {main_nums} (信心度: {confidence:.1f}%, 特徵貢獻: {feature_contrib:.1f}%)")
            
        except Exception as e:
            print(f"❌ 優化級別 {optimization_level} 測試失敗: {e}")
            import traceback
            traceback.print_exc()
    
    return True

def backtest_enhanced_predictor():
    """回測特徵增強預測器"""
    
    print("\n\n📈 Phase 2 特徵增強預測器回測")
    print("=" * 70)
    
    # 初始化數據庫管理器
    db_manager = DBManager()
    
    # 載入威力彩歷史數據
    print("📊 載入威力彩歷史數據...")
    df = db_manager.load_lottery_data('powercolor')
    
    if df.empty:
        print("❌ 無法載入歷史數據")
        return False
    
    print(f"✅ 載入完成，共 {len(df)} 筆歷史記錄")
    
    # 初始化預測器
    predictor = FeatureEnhancedPredictor(
        lottery_type='powercolor',
        db_manager=db_manager,
        optimization_level=2
    )
    
    # 回測設置
    test_periods = 8
    print(f"\n🎯 開始回測 (最近{test_periods}期)")
    print("-" * 50)
    
    results = []
    
    for i in range(test_periods):
        # 使用前N-i期數據進行預測
        train_data = df.iloc[:-i-1] if i > 0 else df.iloc[:-1]
        actual_data = df.iloc[-i-1]
        
        try:
            # 進行預測
            prediction = predictor.predict_with_enhanced_features(
                train_data, 
                candidate_count=3, 
                generate_report=False
            )
            
            # 獲取實際結果
            actual_main = [
                actual_data['Anumber1'], actual_data['Anumber2'], actual_data['Anumber3'],
                actual_data['Anumber4'], actual_data['Anumber5'], actual_data['Anumber6']
            ]
            actual_special = actual_data['Second_district']
            
            # 計算匹配數
            pred_main = prediction.get('main_numbers', [])
            match_count = len(set(pred_main).intersection(set(actual_main)))
            
            # 檢查特別號匹配
            pred_special = prediction.get('special_number', 0)
            special_match = (pred_special == actual_special)
            
            result = {
                'period': actual_data['Period'],
                'predicted_main': pred_main,
                'actual_main': actual_main,
                'match_count': match_count,
                'predicted_special': pred_special,
                'actual_special': actual_special,
                'special_match': special_match,
                'confidence': prediction.get('confidence', 0),
                'feature_count': prediction.get('feature_count', 0),
                'feature_contribution': prediction.get('feature_contribution', 0)
            }
            
            results.append(result)
            
            print(f"期數 {actual_data['Period']}: 預測 {pred_main} | 實際 {actual_main} | 匹配 {match_count}個 | 信心度 {result['confidence']:.1f}%")
            
        except Exception as e:
            print(f"❌ 回測期數 {actual_data['Period']} 失敗: {e}")
    
    # 分析回測結果
    if results:
        print("\n📊 回測結果分析:")
        
        match_counts = [r['match_count'] for r in results]
        confidences = [r['confidence'] for r in results]
        feature_contributions = [r['feature_contribution'] for r in results]
        
        print(f"平均匹配數: {np.mean(match_counts):.2f}")
        print(f"最高匹配數: {max(match_counts)}")
        print(f"最低匹配數: {min(match_counts)}")
        print(f"平均信心度: {np.mean(confidences):.1f}%")
        print(f"平均特徵貢獻: {np.mean(feature_contributions):.1f}%")
        
        # 匹配分布
        print("\n匹配分布:")
        for i in range(7):
            count = match_counts.count(i)
            percentage = count / len(match_counts) * 100
            print(f"  {i}個匹配: {count}次 ({percentage:.1f}%)") 
        
        # 特別號匹配
        special_matches = sum(1 for r in results if r['special_match'])
        special_rate = special_matches / len(results) * 100
        print(f"\n特別號匹配: {special_matches}次 ({special_rate:.1f}%)")
        
        # 與優化版比較
        avg_match = np.mean(match_counts)
        print(f"\n💡 與優化版比較:")
        print(f"特徵增強版平均匹配數: {avg_match:.2f}")
        print(f"優化版平均匹配數: 1.10 (來自之前測試)")
        
        if avg_match > 1.10:
            improvement = ((avg_match - 1.10) / 1.10) * 100
            print(f"改進幅度: +{improvement:.1f}%")
        else:
            decline = ((1.10 - avg_match) / 1.10) * 100
            print(f"變化幅度: -{decline:.1f}%")
        
        # 高信心度預測分析
        high_confidence_results = [r for r in results if r['confidence'] > 70]
        if high_confidence_results:
            high_conf_matches = [r['match_count'] for r in high_confidence_results]
            print(f"\n🎯 高信心度預測 (>70%):")
            print(f"  高信心度預測數: {len(high_confidence_results)}")
            print(f"  高信心度平均匹配數: {np.mean(high_conf_matches):.2f}")
    
    return True

def compare_all_methods():
    """比較所有預測方法"""
    
    print("\n\n🏆 Phase 2 預測方法綜合比較")
    print("=" * 70)
    
    # 初始化數據庫管理器
    db_manager = DBManager()
    
    # 載入威力彩歷史數據
    print("📊 載入威力彩歷史數據...")
    df = db_manager.load_lottery_data('powercolor')
    
    if df.empty:
        print("❌ 無法載入歷史數據")
        return False
    
    print(f"✅ 載入完成，共 {len(df)} 筆歷史記錄")
    
    # 使用最近100期數據
    test_data = df.tail(100)
    
    methods = [
        {
            'name': '優化版板路分析',
            'predictor': OptimizedBoardPathAnalyzer('powercolor', db_manager, 2),
            'method': 'predict_next_numbers_optimized'
        },
        {
            'name': '特徵增強預測器',
            'predictor': FeatureEnhancedPredictor('powercolor', db_manager, 2),
            'method': 'predict_with_enhanced_features'
        }
    ]
    
    print("\n📊 預測方法比較:")
    print("-" * 50)
    
    for method_info in methods:
        try:
            predictor = method_info['predictor']
            method_name = method_info['name']
            
            print(f"\n{method_name}:")
            
            if method_info['method'] == 'predict_next_numbers_optimized':
                result = predictor.predict_next_numbers_optimized(test_data, candidate_count=5)
            else:
                result = predictor.predict_with_enhanced_features(test_data, candidate_count=5)
            
            print(f"  主要預測: {result.get('main_numbers', 'N/A')}")
            print(f"  信心度: {result.get('confidence', 0):.1f}%")
            
            if 'feature_count' in result:
                print(f"  特徵數量: {result.get('feature_count', 0)}")
                print(f"  特徵貢獻度: {result.get('feature_contribution', 0):.1f}%")
            
            candidates = result.get('candidates', [])
            if candidates:
                print(f"  候選數量: {len(candidates)}")
                avg_confidence = np.mean([c.get('confidence', 0) for c in candidates])
                print(f"  平均信心度: {avg_confidence:.1f}%")
        
        except Exception as e:
            print(f"❌ {method_name} 測試失敗: {e}")
    
    return True

def main():
    """主測試函數"""
    
    print("🚀 Phase 2 特徵工程系統綜合測試")
    print("=" * 80)
    
    # 測試序列
    test_results = []
    
    # 1. 測試增強特徵分析器
    print("\n1. 測試增強特徵分析器...")
    test_results.append(test_enhanced_feature_analyzer())
    
    # 2. 測試特徵增強預測器
    print("\n2. 測試特徵增強預測器...")
    test_results.append(test_feature_enhanced_predictor())
    
    # 3. 回測特徵增強預測器
    print("\n3. 回測特徵增強預測器...")
    test_results.append(backtest_enhanced_predictor())
    
    # 4. 比較所有方法
    print("\n4. 比較所有方法...")
    test_results.append(compare_all_methods())
    
    # 總結
    print("\n\n🎯 測試總結")
    print("=" * 80)
    
    test_names = [
        "增強特徵分析器",
        "特徵增強預測器",
        "回測測試",
        "方法比較"
    ]
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"總測試數: {total_tests}")
    print(f"通過測試: {passed_tests}")
    print(f"測試通過率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n各項測試結果:")
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {i+1}. {name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有測試通過！特徵工程系統運行正常")
    else:
        print(f"\n⚠️  {total_tests - passed_tests} 項測試失敗，需要檢查")
    
    print("=" * 80)

if __name__ == "__main__":
    main()