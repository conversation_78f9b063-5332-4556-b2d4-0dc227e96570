#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
綜合訓練效果測試腳本
測試三種彩票在不同訓練天數下的預測準確度
"""

import sys
import os
import sqlite3
import pandas as pd
import json
from datetime import datetime, timedelta
from pathlib import Path

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from prediction.lottery_predictor import LotteryPredictor
from data.feature_engineering import FeatureEngineer

def get_actual_result(lottery_type, target_date):
    """
    獲取指定日期的實際開獎結果
    """
    db_path = 'lottery_data.db'
    conn = sqlite3.connect(db_path)
    
    try:
        if lottery_type == 'powercolor':
            query = """
            SELECT Anumber1, Anumber2, An<PERSON>ber3, An<PERSON>ber4, Anumber5, Anumber6, Second_district
            FROM powercolor 
            WHERE Sdate = ?
            """
        elif lottery_type == 'lotto649':
            query = """
            SELECT Anumber1, <PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON>3, <PERSON><PERSON><PERSON>4, <PERSON><PERSON><PERSON>5, <PERSON><PERSON><PERSON>6, SpecialNumber
            FROM lotto649 
            WHERE Sdate = ?
            """
        elif lottery_type == 'dailycash':
            query = """
            SELECT Anumber1, Anumber2, Anumber3, Anumber4, Anumber5
            FROM dailycash 
            WHERE Sdate = ?
            """
        else:
            return None, None
            
        cursor = conn.execute(query, (target_date,))
        result = cursor.fetchone()
        
        if result:
            if lottery_type == 'dailycash':
                main_numbers = [str(num) for num in result[:5]]
                special_number = None
            else:
                main_numbers = [str(num) for num in result[:6]]
                special_number = str(result[6]) if result[6] else None
            return main_numbers, special_number
        else:
            return None, None
            
    except Exception as e:
        print(f"獲取實際結果時發生錯誤: {e}")
        return None, None
    finally:
        conn.close()

def calculate_accuracy(prediction, actual_main, actual_special, lottery_type):
    """
    計算預測準確度
    """
    try:
        # 提取預測號碼
        if hasattr(prediction, 'numbers') and prediction.numbers:
            if isinstance(prediction.numbers, dict):
                # 處理字典格式
                pred_main = prediction.numbers.get('第一區', 
                           prediction.numbers.get('main_numbers', 
                           prediction.numbers.get('numbers', [])))
                pred_special = prediction.numbers.get('第二區', 
                              prediction.numbers.get('special_number', 
                              prediction.numbers.get('special', 0)))
            else:
                # 處理列表格式
                if lottery_type == 'dailycash':
                    pred_main = prediction.numbers[:5] if len(prediction.numbers) >= 5 else prediction.numbers
                    pred_special = None
                else:
                    pred_main = prediction.numbers[:6] if len(prediction.numbers) >= 6 else prediction.numbers
                    pred_special = prediction.numbers[6] if len(prediction.numbers) > 6 else 0
        else:
            pred_main = []
            pred_special = 0
            
        # 確保預測號碼為字串列表
        if pred_main:
            pred_main = [str(num) for num in pred_main]
        if pred_special is not None:
            pred_special = str(pred_special)
            
        # 計算主號碼匹配數
        main_matches = 0
        if actual_main and pred_main:
            main_matches = len(set(actual_main) & set(pred_main))
            
        # 計算特別號匹配
        special_match = 0
        if lottery_type != 'dailycash' and actual_special and pred_special:
            special_match = 1 if actual_special == pred_special else 0
            
        # 計算總體準確度
        if lottery_type == 'dailycash':
            total_possible = 5
            accuracy = main_matches / total_possible
        else:
            total_possible = 7  # 6個主號碼 + 1個特別號
            accuracy = (main_matches + special_match) / total_possible
            
        return {
            'main_matches': main_matches,
            'special_match': special_match,
            'accuracy': accuracy,
            'predicted_main': pred_main,
            'predicted_special': pred_special,
            'actual_main': actual_main,
            'actual_special': actual_special
        }
        
    except Exception as e:
        print(f"計算準確度時發生錯誤: {e}")
        return {
            'main_matches': 0,
            'special_match': 0,
            'accuracy': 0.0,
            'predicted_main': [],
            'predicted_special': None,
            'actual_main': actual_main,
            'actual_special': actual_special,
            'error': str(e)
        }

def test_training_periods(lottery_type, target_date, training_periods):
    """
    測試不同訓練期間的效果
    """
    print(f"\n開始測試 {lottery_type} 在不同訓練期間的效果...")
    print(f"目標預測日期: {target_date}")
    
    # 獲取實際結果
    actual_main, actual_special = get_actual_result(lottery_type, target_date)
    if not actual_main:
        print(f"無法獲取 {target_date} 的實際開獎結果")
        return None
        
    print(f"實際開獎結果 - 主號碼: {actual_main}, 特別號: {actual_special}")
    
    results = {}
    
    for days in training_periods:
        print(f"\n測試 {days} 天訓練資料...")
        
        try:
            # 計算訓練資料的開始日期
            target_dt = datetime.strptime(target_date, '%Y-%m-%d')
            start_date = (target_dt - timedelta(days=days)).strftime('%Y-%m-%d')
            end_date = (target_dt - timedelta(days=1)).strftime('%Y-%m-%d')
            
            print(f"訓練資料期間: {start_date} 到 {end_date}")
            
            # 獲取訓練資料
            db_path = 'lottery_data.db'
            conn = sqlite3.connect(db_path)
            
            if lottery_type == 'powercolor':
                query = """
                SELECT Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, Second_district
                FROM powercolor 
                WHERE Sdate >= ? AND Sdate <= ?
                ORDER BY Sdate
                """
            elif lottery_type == 'lotto649':
                query = """
                SELECT Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, SpecialNumber
                FROM lotto649 
                WHERE Sdate >= ? AND Sdate <= ?
                ORDER BY Sdate
                """
            elif lottery_type == 'dailycash':
                query = """
                SELECT Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5
                FROM dailycash 
                WHERE Sdate >= ? AND Sdate <= ?
                ORDER BY Sdate
                """
                
            training_data = pd.read_sql_query(query, conn, params=(start_date, end_date))
            conn.close()
            
            # 添加Period欄位（期數）
            training_data['Period'] = range(1, len(training_data) + 1)
            
            # 轉換Sdate為datetime格式
            training_data['Sdate'] = pd.to_datetime(training_data['Sdate'])
            
            # 確保數字欄位為整數類型
            number_columns = ['Anumber1', 'Anumber2', 'Anumber3', 'Anumber4', 'Anumber5']
            if lottery_type in ['powercolor', 'lotto649']:
                number_columns.append('Anumber6')
            if lottery_type == 'powercolor':
                number_columns.append('Second_district')
            elif lottery_type == 'lotto649':
                number_columns.append('SpecialNumber')
                
            for col in number_columns:
                if col in training_data.columns:
                    # 轉換為數字，無效值設為NaN
                    training_data[col] = pd.to_numeric(training_data[col], errors='coerce')
                    # 移除包含NaN的行
                    training_data = training_data.dropna(subset=[col])
                    # 轉換為整數
                    training_data[col] = training_data[col].astype(int)
            
            if len(training_data) < 10:
                print(f"訓練資料不足 ({len(training_data)} 筆)，跳過此測試")
                results[f"{days}_days"] = {
                    'status': 'failed',
                    'reason': f'訓練資料不足 ({len(training_data)} 筆)',
                    'training_records': len(training_data)
                }
                continue
                
            print(f"獲得 {len(training_data)} 筆訓練資料")
            
            # 特徵工程
            feature_eng = FeatureEngineer()
            features_df = feature_eng.create_basic_features(training_data, lottery_type)
            
            if features_df.empty:
                print("特徵工程失敗，跳過此測試")
                results[f"{days}_days"] = {
                    'status': 'failed',
                    'reason': '特徵工程失敗',
                    'training_records': len(training_data)
                }
                continue
                
            # 獲取最新特徵
            latest_features = features_df.iloc[-1:]
            
            # 初始化預測器並進行預測
            predictor = LotteryPredictor()
            prediction = predictor.predict(
                df=training_data,
                features=latest_features,
                lottery_type=lottery_type,
                prediction_methods=['ml']
            )
            
            if prediction:
                # 計算準確度
                accuracy_result = calculate_accuracy(prediction, actual_main, actual_special, lottery_type)
                
                results[f"{days}_days"] = {
                    'status': 'success',
                    'training_records': len(training_data),
                    'training_period': f"{start_date} 到 {end_date}",
                    'prediction': {
                        'main_numbers': accuracy_result['predicted_main'],
                        'special_number': accuracy_result['predicted_special']
                    },
                    'accuracy': accuracy_result['accuracy'],
                    'main_matches': accuracy_result['main_matches'],
                    'special_match': accuracy_result['special_match']
                }
                
                print(f"預測結果 - 主號碼: {accuracy_result['predicted_main']}, 特別號: {accuracy_result['predicted_special']}")
                print(f"匹配結果 - 主號碼匹配: {accuracy_result['main_matches']}, 特別號匹配: {accuracy_result['special_match']}")
                print(f"準確度: {accuracy_result['accuracy']:.3f}")
            else:
                print("預測失敗")
                results[f"{days}_days"] = {
                    'status': 'failed',
                    'reason': '預測失敗',
                    'training_records': len(training_data)
                }
                
        except Exception as e:
            print(f"測試 {days} 天訓練時發生錯誤: {e}")
            results[f"{days}_days"] = {
                'status': 'failed',
                'reason': str(e),
                'training_records': 0
            }
            
    return results

def main():
    """
    主函數
    """
    # 測試參數
    lottery_types = ['powercolor', 'lotto649', 'dailycash']
    target_date = '2025-06-20'  # 可以修改為其他日期
    training_periods = [30, 60, 90, 120, 180, 365, 730]  # 不同的訓練天數
    
    print("=" * 60)
    print("綜合訓練效果測試")
    print(f"目標預測日期: {target_date}")
    print(f"測試訓練期間: {training_periods} 天")
    print("=" * 60)
    
    all_results = {}
    
    for lottery_type in lottery_types:
        print(f"\n{'='*20} {lottery_type.upper()} {'='*20}")
        results = test_training_periods(lottery_type, target_date, training_periods)
        if results:
            all_results[lottery_type] = results
            
            # 找出最佳訓練期間
            best_period = None
            best_accuracy = -1
            
            for period, result in results.items():
                if result['status'] == 'success' and result['accuracy'] > best_accuracy:
                    best_accuracy = result['accuracy']
                    best_period = period
                    
            if best_period:
                print(f"\n{lottery_type} 最佳訓練期間: {best_period} (準確度: {best_accuracy:.3f})")
            else:
                print(f"\n{lottery_type} 所有測試均失敗")
    
    # 保存結果
    os.makedirs('reports', exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f'reports/comprehensive_training_test_{timestamp}.json'
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump({
            'test_date': datetime.now().isoformat(),
            'target_date': target_date,
            'training_periods': training_periods,
            'results': all_results
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n詳細結果已保存到: {report_file}")
    
    # 生成摘要報告
    print("\n" + "="*60)
    print("摘要報告")
    print("="*60)
    
    for lottery_type, results in all_results.items():
        print(f"\n{lottery_type.upper()}:")
        
        successful_tests = [(period, result) for period, result in results.items() 
                          if result['status'] == 'success']
        
        if successful_tests:
            # 按準確度排序
            successful_tests.sort(key=lambda x: x[1]['accuracy'], reverse=True)
            
            print("  排名 | 訓練期間 | 準確度 | 主號匹配 | 特號匹配 | 訓練筆數")
            print("  " + "-"*55)
            
            for i, (period, result) in enumerate(successful_tests, 1):
                period_days = period.replace('_days', '')
                accuracy = result['accuracy']
                main_matches = result['main_matches']
                special_match = result['special_match']
                training_records = result['training_records']
                
                print(f"  {i:2d}   | {period_days:6s}天 | {accuracy:6.3f} | {main_matches:6d}個 | {special_match:6d}個 | {training_records:6d}筆")
        else:
            print("  所有測試均失敗")

if __name__ == "__main__":
    main()