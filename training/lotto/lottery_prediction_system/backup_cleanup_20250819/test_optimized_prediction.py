#!/usr/bin/env python3
"""
測試優化版預測算法
比較原版與優化版的效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import DBManager
from prediction.optimized_board_path_analyzer import OptimizedBoardPathAnalyzer
from prediction.board_path_analyzer import BoardPathAnalyzer
import logging
import numpy as np

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_optimized_prediction():
    """測試優化版預測算法"""
    
    print("🚀 Phase 2 算法優化測試")
    print("=" * 60)
    
    # 初始化數據庫管理器
    db_manager = DBManager()
    
    # 載入威力彩歷史數據
    print("📊 載入威力彩歷史數據...")
    df = db_manager.load_lottery_data('powercolor')
    
    if df.empty:
        print("❌ 無法載入歷史數據")
        return
    
    print(f"✅ 載入完成，共 {len(df)} 筆歷史記錄")
    
    # 使用最近100期作為測試數據
    test_data = df.tail(100)
    
    # 測試原版算法
    print("\n🔧 測試原版板路分析算法...")
    original_analyzer = BoardPathAnalyzer('powercolor', db_manager)
    
    try:
        original_result = original_analyzer.predict_next_numbers(test_data)
        print(f"原版預測結果: {original_result.get('主區', 'N/A')}")
    except Exception as e:
        print(f"原版算法測試失敗: {e}")
        original_result = None
    
    # 測試優化版算法
    print("\n⚡ 測試優化版板路分析算法...")
    
    for optimization_level in [1, 2, 3]:
        print(f"\n--- 優化級別 {optimization_level} ---")
        
        optimized_analyzer = OptimizedBoardPathAnalyzer(
            'powercolor', 
            db_manager, 
            optimization_level=optimization_level
        )
        
        try:
            optimized_result = optimized_analyzer.predict_next_numbers_optimized(
                test_data, 
                candidate_count=5
            )
            
            print(f"主要預測: {optimized_result.get('main_numbers', 'N/A')}")
            print(f"信心度: {optimized_result.get('confidence', 0):.1f}%")
            print(f"候選數量: {len(optimized_result.get('candidates', []))}")
            
            # 顯示所有候選
            candidates = optimized_result.get('candidates', [])
            if candidates:
                print("候選組合:")
                for i, candidate in enumerate(candidates, 1):
                    main_nums = candidate.get('main_numbers', [])
                    confidence = candidate.get('confidence', 0)
                    print(f"  {i}. {main_nums} (信心度: {confidence:.1f}%)")
                    
        except Exception as e:
            print(f"優化版算法測試失敗 (級別{optimization_level}): {e}")
            import traceback
            traceback.print_exc()
    
    # 進行歷史回測
    print("\n📈 進行歷史回測...")
    backtest_optimized_algorithm(db_manager, df)

def backtest_optimized_algorithm(db_manager, df, test_periods=10):
    """對優化算法進行歷史回測"""
    
    print(f"🎯 開始歷史回測 (最近{test_periods}期)")
    print("-" * 40)
    
    # 使用優化級別2進行回測
    analyzer = OptimizedBoardPathAnalyzer('powercolor', db_manager, optimization_level=2)
    
    results = []
    
    for i in range(test_periods):
        # 使用前N-i期數據進行預測
        train_data = df.iloc[:-i-1] if i > 0 else df.iloc[:-1]
        actual_data = df.iloc[-i-1]
        
        try:
            # 進行預測
            prediction = analyzer.predict_next_numbers_optimized(train_data, candidate_count=3)
            
            # 獲取實際結果
            actual_main = [
                actual_data['Anumber1'], actual_data['Anumber2'], actual_data['Anumber3'],
                actual_data['Anumber4'], actual_data['Anumber5'], actual_data['Anumber6']
            ]
            actual_special = actual_data['Second_district']
            
            # 計算匹配數
            pred_main = prediction.get('main_numbers', [])
            match_count = len(set(pred_main).intersection(set(actual_main)))
            
            # 檢查特別號匹配
            pred_special = prediction.get('special_number', 0)
            special_match = (pred_special == actual_special)
            
            result = {
                'period': actual_data['Period'],
                'predicted_main': pred_main,
                'actual_main': actual_main,
                'match_count': match_count,
                'predicted_special': pred_special,
                'actual_special': actual_special,
                'special_match': special_match,
                'confidence': prediction.get('confidence', 0)
            }
            
            results.append(result)
            
            print(f"期數 {actual_data['Period']}: 預測 {pred_main} | 實際 {actual_main} | 匹配 {match_count}個")
            
        except Exception as e:
            print(f"回測期數 {actual_data['Period']} 失敗: {e}")
    
    # 分析回測結果
    if results:
        print("\n📊 回測結果分析:")
        
        match_counts = [r['match_count'] for r in results]
        avg_match = np.mean(match_counts)
        
        print(f"平均匹配數: {avg_match:.2f}")
        print(f"最高匹配數: {max(match_counts)}")
        print(f"最低匹配數: {min(match_counts)}")
        
        # 匹配分布
        print("\n匹配分布:")
        for i in range(7):
            count = match_counts.count(i)
            percentage = count / len(match_counts) * 100
            print(f"  {i}個匹配: {count}次 ({percentage:.1f}%)")
        
        # 特別號匹配
        special_matches = sum(1 for r in results if r['special_match'])
        special_rate = special_matches / len(results) * 100
        print(f"\n特別號匹配: {special_matches}次 ({special_rate:.1f}%)")
        
        # 計算改進情況
        print(f"\n💡 與原版比較:")
        print(f"優化版平均匹配數: {avg_match:.2f}")
        print(f"原版平均匹配數: 0.78 (來自之前分析)")
        
        if avg_match > 0.78:
            improvement = ((avg_match - 0.78) / 0.78) * 100
            print(f"改進幅度: +{improvement:.1f}%")
        else:
            decline = ((0.78 - avg_match) / 0.78) * 100
            print(f"下降幅度: -{decline:.1f}%")

if __name__ == "__main__":
    test_optimized_prediction()