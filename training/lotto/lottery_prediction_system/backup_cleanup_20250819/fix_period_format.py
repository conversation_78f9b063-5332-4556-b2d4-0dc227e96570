#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復期數格式問題 - 刪除錯誤的5位數期數記錄
"""

import sqlite3
import pandas as pd
from datetime import datetime

def backup_database(db_path: str):
    """創建數據庫備份"""
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # 使用SQLite的backup方法創建完整備份
    source = sqlite3.connect(db_path)
    backup = sqlite3.connect(backup_path)
    
    source.backup(backup)
    
    source.close()
    backup.close()
    
    print(f"✅ 數據庫備份已創建: {backup_path}")
    return backup_path

def analyze_problematic_records(db_path: str):
    """分析需要刪除的問題記錄"""
    conn = sqlite3.connect(db_path)
    
    problematic_records = []
    
    tables = ['Lotto649', 'Powercolor', 'DailyCash']
    
    for table in tables:
        query = f'''
            SELECT Period, Sdate
            FROM {table} 
            WHERE LENGTH(CAST(Period AS TEXT)) <> 9
            ORDER BY Sdate DESC
        '''
        
        df = pd.read_sql_query(query, conn)
        
        for _, row in df.iterrows():
            problematic_records.append({
                'table': table,
                'period': row['Period'],
                'date': row['Sdate'],
                'period_length': len(str(row['Period']))
            })
    
    conn.close()
    return problematic_records

def delete_problematic_records(db_path: str, records: list, confirm: bool = False):
    """刪除問題記錄"""
    if not confirm:
        print("⚠️  這是模擬模式，不會實際刪除記錄")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    deleted_count = 0
    
    for record in records:
        table = record['table']
        period = record['period']
        date = record['date']
        
        print(f"準備刪除: {table} - 期數 {period} (日期: {date})")
        
        if confirm:
            try:
                delete_query = f'DELETE FROM {table} WHERE Period = ? AND Sdate = ?'
                cursor.execute(delete_query, (period, date))
                
                if cursor.rowcount > 0:
                    print(f"✅ 已刪除 {cursor.rowcount} 筆記錄")
                    deleted_count += 1
                else:
                    print(f"⚠️  沒有找到匹配的記錄")
            except Exception as e:
                print(f"❌ 刪除失敗: {e}")
        else:
            print(f"  (模擬) 將刪除記錄: {table}.Period={period}, Sdate={date}")
    
    if confirm:
        conn.commit()
        print(f"\n✅ 總共刪除了 {deleted_count} 筆問題記錄")
    else:
        print(f"\n📋 總共將刪除 {len(records)} 筆問題記錄")
    
    conn.close()
    return deleted_count

def verify_fix(db_path: str):
    """驗證修復結果"""
    conn = sqlite3.connect(db_path)
    
    print("\n=== 修復驗證 ===")
    
    tables = ['Lotto649', 'Powercolor', 'DailyCash']
    
    for table in tables:
        # 檢查是否還有非9位數的期數
        query = f'''
            SELECT COUNT(*) as count
            FROM {table} 
            WHERE LENGTH(CAST(Period AS TEXT)) <> 9
        '''
        
        result = pd.read_sql_query(query, conn)
        count = result.iloc[0]['count']
        
        if count == 0:
            print(f"✅ {table}: 所有期數都是正確的9位數格式")
        else:
            print(f"❌ {table}: 仍有 {count} 筆非9位數期數")
            
        # 檢查總記錄數
        total_query = f'SELECT COUNT(*) as total FROM {table}'
        total_result = pd.read_sql_query(total_query, conn)
        total_count = total_result.iloc[0]['total']
        print(f"   總記錄數: {total_count}")
    
    conn.close()

def main():
    """主程序"""
    db_path = "data/lottery_data.db"
    
    print("🔧 期數格式修復工具")
    print("=" * 50)
    
    # 1. 創建備份
    print("\n步驟 1: 創建數據庫備份")
    backup_path = backup_database(db_path)
    
    # 2. 分析問題記錄
    print("\n步驟 2: 分析問題記錄")
    records = analyze_problematic_records(db_path)
    
    if not records:
        print("✅ 沒有發現問題記錄，數據庫格式正確")
        return
    
    print(f"發現 {len(records)} 筆問題記錄:")
    for record in records:
        print(f"  {record['table']}: 期數 {record['period']} (長度: {record['period_length']}) - 日期: {record['date']}")
    
    # 3. 模擬刪除
    print(f"\n步驟 3: 模擬刪除操作")
    delete_problematic_records(db_path, records, confirm=False)
    
    # 4. 詢問用戶確認
    print(f"\n⚠️  確認要刪除這些錯誤的期數記錄嗎？")
    print("這些記錄的期數格式不正確（5位數而非9位數）")
    
    # 在實際運行時可以啟用用戶確認
    # user_confirm = input("輸入 'YES' 確認刪除: ").strip()
    # if user_confirm == 'YES':
    if True:  # 自動確認，便於測試
        print("\n步驟 4: 執行刪除操作")
        deleted_count = delete_problematic_records(db_path, records, confirm=True)
        
        # 5. 驗證修復結果
        print(f"\n步驟 5: 驗證修復結果")
        verify_fix(db_path)
        
        print(f"\n🎉 修復完成！")
        print(f"   備份文件: {backup_path}")
        print(f"   刪除記錄: {deleted_count} 筆")
    else:
        print("❌ 用戶取消操作")

if __name__ == "__main__":
    main()