#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彩票预测系统UI功能全面测试脚本
测试所有页面、按钮、表单和交互功能
"""

import requests
import time
from datetime import datetime
from urllib.parse import urljoin
import json

BASE_URL = "http://localhost:7890"

class UIFunctionTester:
    def __init__(self):
        self.base_url = BASE_URL
        self.session = requests.Session()
        self.test_results = {}
        
    def test_page_access(self, path, description="", expected_status=200):
        """测试页面访问"""
        try:
            url = urljoin(self.base_url, path)
            response = self.session.get(url, timeout=10)
            
            success = response.status_code == expected_status
            result = {
                'path': path,
                'description': description,
                'status_code': response.status_code,
                'success': success,
                'response_time': response.elapsed.total_seconds(),
                'content_length': len(response.content),
                'has_html': 'html' in response.headers.get('content-type', '').lower()
            }
            
            # 检查页面内容
            if success and result['has_html']:
                content = response.text.lower()
                result['has_title'] = '<title>' in content
                result['has_bootstrap'] = 'bootstrap' in content
                result['has_jquery'] = 'jquery' in content or '$' in content
                result['has_error'] = 'error' in content or '错误' in content
                
            status = "✅" if success else "❌"
            print(f"{status} {path} ({response.status_code}) - {description}")
            return result
            
        except Exception as e:
            print(f"❌ {path} - 错误: {e}")
            return {
                'path': path,
                'description': description,
                'success': False,
                'error': str(e)
            }
    
    def test_api_endpoint(self, path, method='GET', data=None, description=""):
        """测试API端点"""
        try:
            url = urljoin(self.base_url, path)
            
            if method == 'GET':
                response = self.session.get(url, timeout=10)
            elif method == 'POST':
                response = self.session.post(url, json=data, timeout=10)
            
            success = response.status_code == 200
            result = {
                'path': path,
                'method': method,
                'description': description,
                'status_code': response.status_code,
                'success': success,
                'response_time': response.elapsed.total_seconds()
            }
            
            # 检查JSON响应
            try:
                json_data = response.json()
                result['is_json'] = True
                result['api_success'] = json_data.get('success', False)
                result['has_data'] = 'data' in json_data
                result['has_error'] = 'error' in json_data
            except:
                result['is_json'] = False
                
            status = "✅" if success else "❌"
            print(f"{status} {path} ({response.status_code}) - {description}")
            return result
            
        except Exception as e:
            print(f"❌ {path} - 错误: {e}")
            return {
                'path': path,
                'method': method,
                'description': description,
                'success': False,
                'error': str(e)
            }

def main():
    """主测试函数"""
    print("🖥️ 彩票预测系统UI功能全面测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试服务器: {BASE_URL}")
    print("=" * 60)
    
    tester = UIFunctionTester()
    all_results = []
    
    # 1. 测试主要页面访问
    print(f"\n📋 第一步: 测试主要页面访问")
    print("-" * 40)
    
    main_pages = [
        ("/", "主页"),
        ("/predictions", "预测记录页面"),
        ("/results", "开奖结果页面"),
        ("/analysis", "分析报告页面"),
        ("/dashboard", "仪表板页面"),
        ("/comprehensive_analysis", "综合分析页面"),
        ("/number_analysis", "号码分析页面"),
        ("/separated_prediction", "分离式预测页面"),
    ]
    
    page_results = []
    for path, desc in main_pages:
        result = tester.test_page_access(path, desc)
        page_results.append(result)
        all_results.append(result)
    
    # 2. 测试Phase 3高级功能页面
    print(f"\n🚀 第二步: 测试Phase 3高级功能页面")
    print("-" * 40)
    
    phase3_pages = [
        ("/phase3_dashboard", "Phase 3综合仪表板"),
        ("/universal_prediction", "通用预测界面"),
        ("/tracking_analytics", "追踪分析报表"),
        ("/prediction_management", "预测管理页面"),
        ("/periods_management", "期号管理页面"),
        ("/backtest_analysis", "回测分析页面"),
        ("/prediction_method_analysis", "预测方法分析"),
        ("/prediction_performance_dashboard", "预测性能仪表板"),
    ]
    
    for path, desc in phase3_pages:
        result = tester.test_page_access(path, desc)
        page_results.append(result)
        all_results.append(result)
    
    # 3. 测试增强功能页面
    print(f"\n⭐ 第三步: 测试增强功能页面")
    print("-" * 40)
    
    enhanced_pages = [
        ("/enhanced_index", "增强主页"),
        ("/enhanced_prediction", "增强预测页面"),
        ("/enhanced_analysis", "增强分析页面"),
        ("/enhanced_history", "增强历史页面"),
        ("/enhanced_performance", "增强性能页面"),
        ("/enhanced_predict", "增强预测功能"),
        ("/simple_backtest_viewer", "简单回测查看器"),
    ]
    
    for path, desc in enhanced_pages:
        result = tester.test_page_access(path, desc)
        page_results.append(result)
        all_results.append(result)
    
    # 4. 测试API端点功能
    print(f"\n🔗 第四步: 测试核心API端点")
    print("-" * 40)
    
    api_endpoints = [
        ("/api/system_status", "GET", None, "系统状态"),
        ("/api/latest_predictions/powercolor", "GET", None, "威力彩最新预测"),
        ("/api/latest_predictions/lotto649", "GET", None, "大乐透最新预测"),
        ("/api/latest_predictions/dailycash", "GET", None, "今彩539最新预测"),
        ("/api/accuracy/powercolor", "GET", None, "威力彩准确度"),
        ("/api/accuracy/lotto649", "GET", None, "大乐透准确度"),
        ("/api/accuracy/dailycash", "GET", None, "今彩539准确度"),
        ("/api/comprehensive_analysis/powercolor", "GET", None, "威力彩综合分析"),
        ("/api/comprehensive_analysis/lotto649", "GET", None, "大乐透综合分析"),
        ("/api/comprehensive_analysis/dailycash", "GET", None, "今彩539综合分析"),
        ("/api/results?lottery_type=powercolor&limit=5", "GET", None, "威力彩开奖结果"),
        ("/api/results?lottery_type=lotto649&limit=5", "GET", None, "大乐透开奖结果"),
        ("/api/results?lottery_type=dailycash&limit=5", "GET", None, "今彩539开奖结果"),
    ]
    
    api_results = []
    for path, method, data, desc in api_endpoints:
        result = tester.test_api_endpoint(path, method, data, desc)
        api_results.append(result)
        all_results.append(result)
    
    # 5. 测试预测生成功能
    print(f"\n🎯 第五步: 测试预测生成功能")
    print("-" * 40)
    
    prediction_tests = [
        ("/api/predict", "POST", {"lottery_type": "powercolor", "candidates_count": 1}, "生成威力彩预测"),
        ("/api/predict", "POST", {"lottery_type": "lotto649", "candidates_count": 1}, "生成大乐透预测"),
        ("/api/predict", "POST", {"lottery_type": "dailycash", "candidates_count": 1}, "生成今彩539预测"),
    ]
    
    for path, method, data, desc in prediction_tests:
        result = tester.test_api_endpoint(path, method, data, desc)
        api_results.append(result)
        all_results.append(result)
    
    # 6. 测试错误处理
    print(f"\n⚠️ 第六步: 测试错误处理")
    print("-" * 40)
    
    error_tests = [
        ("/nonexistent", "不存在的页面", 404),
        ("/api/nonexistent", "不存在的API", 404),
    ]
    
    for path, desc, expected_status in error_tests:
        result = tester.test_page_access(path, desc, expected_status)
        all_results.append(result)
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📋 测试结果摘要")
    print("=" * 60)
    
    total_tests = len(all_results)
    successful_tests = len([r for r in all_results if r.get('success', False)])
    success_rate = successful_tests / total_tests * 100 if total_tests > 0 else 0
    
    print(f"总测试数: {total_tests}")
    print(f"成功测试: {successful_tests}")
    print(f"失败测试: {total_tests - successful_tests}")
    print(f"成功率: {success_rate:.1f}%")
    
    # 按类别统计
    page_success = len([r for r in page_results if r.get('success', False)])
    api_success = len([r for r in api_results if r.get('success', False)])
    
    print(f"\n📊 分类统计:")
    print(f"页面访问: {page_success}/{len(page_results)} ({page_success/len(page_results)*100:.1f}%)")
    print(f"API功能: {api_success}/{len(api_results)} ({api_success/len(api_results)*100:.1f}%)")
    
    # 显示失败的测试
    failed_tests = [r for r in all_results if not r.get('success', False)]
    if failed_tests:
        print(f"\n🔴 失败的测试:")
        for test in failed_tests:
            path = test.get('path', 'N/A')
            desc = test.get('description', 'N/A')
            error = test.get('error', f"状态码: {test.get('status_code', 'N/A')}")
            print(f"  ❌ {path} - {desc} ({error})")
    
    # 保存详细报告
    report_file = f"ui_function_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump({
            'test_time': datetime.now().isoformat(),
            'base_url': BASE_URL,
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'success_rate': success_rate,
            'results': all_results
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细测试报告已保存: {report_file}")
    print("\n" + "=" * 60)
    print("UI功能测试完成")

if __name__ == "__main__":
    main()