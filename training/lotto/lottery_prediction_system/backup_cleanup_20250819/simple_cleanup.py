"""
簡單直接的清理腳本
清理期數 114000050 的重複和無效預測記錄
"""

import sqlite3
from datetime import datetime

def cleanup_period_114000050():
    """清理期數 114000050 的記錄"""
    
    try:
        conn = sqlite3.connect('data/lottery_data.db')
        cursor = conn.cursor()
        
        print("=== 清理期數 114000050 的記錄 ===")
        
        # 1. 查看當前記錄
        print("\n1. 查看當前記錄...")
        cursor.execute('''
        SELECT ID, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredS, 
               PredictionDate, PredictionMethod
        FROM PowerColorPredictions 
        WHERE Period = '114000050'
        ORDER BY PredictionDate DESC
        ''')
        
        records = cursor.fetchall()
        print(f"期數 114000050 共有 {len(records)} 筆記錄")
        
        # 統計重複記錄
        number_combinations = {}
        for record in records:
            id_val, a1, a2, a3, a4, a5, a6, s, date, method = record
            combo = f"{a1}-{a2}-{a3}-{a4}-{a5}-{a6}-{s}"
            if combo not in number_combinations:
                number_combinations[combo] = []
            number_combinations[combo].append(id_val)
        
        # 顯示重複情況
        print("\n重複記錄統計:")
        duplicates_to_delete = []
        
        for combo, ids in number_combinations.items():
            if len(ids) > 1:
                print(f"  {combo}: {len(ids)} 筆記錄")
                # 保留最新的（ID最大的），刪除其餘的
                # 過濾掉 None 值並排序
                valid_ids = [id_val for id_val in ids if id_val is not None]
                valid_ids.sort(reverse=True)
                duplicates_to_delete.extend(valid_ids[1:])  # 除了第一個，其餘都刪除
        
        print(f"\n總共需要刪除 {len(duplicates_to_delete)} 筆重複記錄")
        
        # 2. 詢問是否刪除
        if duplicates_to_delete:
            response = input("\n是否要刪除這些重複記錄？(y/N): ").strip().lower()
            
            if response == 'y':
                # 分批刪除（SQLite 有參數限制）
                batch_size = 100
                deleted_count = 0
                
                for i in range(0, len(duplicates_to_delete), batch_size):
                    batch = duplicates_to_delete[i:i+batch_size]
                    placeholders = ','.join(['?' for _ in batch])
                    
                    cursor.execute(f'''
                    DELETE FROM PowerColorPredictions 
                    WHERE ID IN ({placeholders})
                    ''', batch)
                    
                    deleted_count += cursor.rowcount
                
                conn.commit()
                print(f"✅ 已刪除 {deleted_count} 筆重複記錄")
                
                # 驗證結果
                cursor.execute('''
                SELECT COUNT(*) FROM PowerColorPredictions WHERE Period = '114000050'
                ''')
                remaining_count = cursor.fetchone()[0]
                print(f"期數 114000050 剩餘記錄數: {remaining_count}")
                
                # 顯示剩餘的唯一記錄
                print("\n剩餘的唯一記錄:")
                cursor.execute('''
                SELECT DISTINCT PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredS, 
                       PredictionMethod
                FROM PowerColorPredictions 
                WHERE Period = '114000050'
                ORDER BY PredA1, PredA2, PredA3, PredA4, PredA5, PredA6
                ''')
                
                unique_records = cursor.fetchall()
                for i, record in enumerate(unique_records):
                    a1, a2, a3, a4, a5, a6, s, method = record
                    print(f"  {i+1}. {a1}-{a2}-{a3}-{a4}-{a5}-{a6} (第二區:{s}) - {method}")
            else:
                print("取消刪除操作")
        else:
            print("沒有找到重複記錄")
        
        conn.close()
        
    except Exception as e:
        print(f"錯誤: {e}")

def delete_all_period_114000050():
    """刪除期數 114000050 的所有記錄"""
    
    try:
        conn = sqlite3.connect('data/lottery_data.db')
        cursor = conn.cursor()
        
        print("=== 刪除期數 114000050 的所有記錄 ===")
        
        # 查看當前記錄數
        cursor.execute('''
        SELECT COUNT(*) FROM PowerColorPredictions WHERE Period = '114000050'
        ''')
        current_count = cursor.fetchone()[0]
        print(f"期數 114000050 當前有 {current_count} 筆記錄")
        
        if current_count > 0:
            response = input(f"\n是否要刪除期數 114000050 的所有 {current_count} 筆記錄？(y/N): ").strip().lower()
            
            if response == 'y':
                cursor.execute('''
                DELETE FROM PowerColorPredictions WHERE Period = '114000050'
                ''')
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                print(f"✅ 已刪除期數 114000050 的所有 {deleted_count} 筆記錄")
            else:
                print("取消刪除操作")
        else:
            print("期數 114000050 沒有記錄")
        
        conn.close()
        
    except Exception as e:
        print(f"錯誤: {e}")

def show_period_114000050_summary():
    """顯示期數 114000050 的記錄摘要"""
    
    try:
        conn = sqlite3.connect('data/lottery_data.db')
        cursor = conn.cursor()
        
        print("=== 期數 114000050 記錄摘要 ===")
        
        # 查看記錄總數
        cursor.execute('''
        SELECT COUNT(*) FROM PowerColorPredictions WHERE Period = '114000050'
        ''')
        total_count = cursor.fetchone()[0]
        print(f"總記錄數: {total_count}")
        
        if total_count > 0:
            # 查看唯一記錄數
            cursor.execute('''
            SELECT COUNT(*) FROM (
                SELECT DISTINCT PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredS
                FROM PowerColorPredictions WHERE Period = '114000050'
            )
            ''')
            unique_count = cursor.fetchone()[0]
            print(f"唯一記錄數: {unique_count}")
            print(f"重複記錄數: {total_count - unique_count}")
            
            # 顯示所有唯一記錄
            print("\n所有唯一記錄:")
            cursor.execute('''
            SELECT DISTINCT PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredS
            FROM PowerColorPredictions 
            WHERE Period = '114000050'
            ORDER BY PredA1, PredA2, PredA3, PredA4, PredA5, PredA6
            ''')
            
            unique_records = cursor.fetchall()
            for i, record in enumerate(unique_records):
                a1, a2, a3, a4, a5, a6, s = record
                print(f"  {i+1}. {a1}-{a2}-{a3}-{a4}-{a5}-{a6} (第二區:{s})")
        
        conn.close()
        
    except Exception as e:
        print(f"錯誤: {e}")

if __name__ == "__main__":
    print("選擇操作:")
    print("1. 查看期數 114000050 記錄摘要")
    print("2. 清理期數 114000050 的重複記錄")
    print("3. 刪除期數 114000050 的所有記錄")
    
    choice = input("\n請選擇 (1/2/3): ").strip()
    
    if choice == '1':
        show_period_114000050_summary()
    elif choice == '2':
        cleanup_period_114000050()
    elif choice == '3':
        delete_all_period_114000050()
    else:
        print("無效選擇")
