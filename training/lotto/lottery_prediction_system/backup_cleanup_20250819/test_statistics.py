#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試統計分析功能
"""

import sys
import os

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import DBManager
import pandas as pd

def calculate_results_statistics(df, lottery_type):
    """
    計算開獎結果統計信息
    """
    try:
        if df.empty:
            return {}
        
        total_draws = len(df)
        
        # 日期範圍
        if 'Sdate' in df.columns and not df['Sdate'].empty:
            min_date = df['Sdate'].min().strftime('%Y-%m-%d')
            max_date = df['Sdate'].max().strftime('%Y-%m-%d')
            date_range = f"{min_date} ~ {max_date}"
        else:
            date_range = "無資料"
        
        # 計算號碼出現頻率
        number_counts = {}
        
        if lottery_type == 'powercolor':
            # 統計第一區號碼
            for i in range(1, 7):
                col = f'Anumber{i}'
                if col in df.columns:
                    for num in df[col].dropna():
                        number_counts[int(num)] = number_counts.get(int(num), 0) + 1
        elif lottery_type == 'lotto649':
            # 統計一般號碼
            for i in range(1, 7):
                col = f'Anumber{i}'
                if col in df.columns:
                    for num in df[col].dropna():
                        number_counts[int(num)] = number_counts.get(int(num), 0) + 1
        elif lottery_type == 'dailycash':
            # 統計號碼
            for i in range(1, 6):
                col = f'Anumber{i}'
                if col in df.columns:
                    for num in df[col].dropna():
                        number_counts[int(num)] = number_counts.get(int(num), 0) + 1
        
        # 找出最常和最少出現的號碼
        if number_counts:
            most_frequent_number = max(number_counts.items(), key=lambda x: x[1])[0]
            least_frequent_number = min(number_counts.items(), key=lambda x: x[1])[0]
            most_frequent_count = number_counts[most_frequent_number]
            least_frequent_count = number_counts[least_frequent_number]
        else:
            most_frequent_number = "-"
            least_frequent_number = "-"
            most_frequent_count = 0
            least_frequent_count = 0
        
        return {
            'total_draws': total_draws,
            'date_range': date_range,
            'most_frequent_number': most_frequent_number,
            'most_frequent_count': most_frequent_count,
            'least_frequent_number': least_frequent_number,
            'least_frequent_count': least_frequent_count,
            'number_frequency': number_counts
        }
        
    except Exception as e:
        print(f"統計計算錯誤: {str(e)}")
        return {
            'total_draws': 0,
            'date_range': "無資料",
            'most_frequent_number': "-",
            'least_frequent_number': "-",
            'number_frequency': {}
        }

def test_lottery_statistics():
    """
    測試各種彩票的統計分析
    """
    try:
        # 初始化數據庫管理器
        db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'lottery_data.db')
        db_manager = DBManager(db_path)
        
        lottery_types = [
            ('powercolor', '威力彩'),
            ('lotto649', '大樂透'),
            ('dailycash', '今彩539')
        ]
        
        for lottery_type, name in lottery_types:
            print(f"\n=== {name} 統計分析 ===")
            
            # 載入數據
            df = db_manager.load_lottery_data(lottery_type=lottery_type)
            
            if df.empty:
                print(f"沒有找到{name}的數據")
                continue
            
            print(f"數據筆數: {len(df)}")
            
            # 計算統計
            stats = calculate_results_statistics(df, lottery_type)
            
            print(f"總開獎次數: {stats['total_draws']}")
            print(f"日期範圍: {stats['date_range']}")
            print(f"最常出現號碼: {stats['most_frequent_number']} (出現{stats.get('most_frequent_count', 0)}次)")
            print(f"最少出現號碼: {stats['least_frequent_number']} (出現{stats.get('least_frequent_count', 0)}次)")
            
            # 顯示前10個最常出現的號碼
            if stats['number_frequency']:
                sorted_numbers = sorted(stats['number_frequency'].items(), key=lambda x: x[1], reverse=True)
                print("前10個最常出現的號碼:")
                for i, (num, count) in enumerate(sorted_numbers[:10]):
                    print(f"  {i+1}. 號碼 {num}: {count}次")
            
            print("-" * 50)
    
    except Exception as e:
        print(f"測試失敗: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_lottery_statistics()