#!/usr/bin/env python3
"""
Phase 3.5 預測結果追蹤和統計分析系統測試
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
import time
import uuid
from datetime import datetime, timedelta
from typing import List, Dict, Any

# 導入測試模組
from data.db_manager import DBManager
from phase3.prediction_tracking_system import (
    PredictionTracker, StatisticsAnalyzer, IntegratedTrackingSystem,
    PredictionStatus, AnalysisTimeframe
)
from phase3.visualization_reports import VisualizationGenerator, ReportGenerator

import logging
logging.basicConfig(level=logging.WARNING)

def test_prediction_tracker():
    """測試預測追蹤器"""
    print("🎯 測試預測追蹤器")
    print("-" * 50)
    
    try:
        db_manager = DBManager()
        tracker = PredictionTracker(db_manager)
        
        # 測試記錄預測
        print("📝 測試預測記錄...")
        test_predictions = []
        
        for i in range(10):
            prediction_id = str(uuid.uuid4())
            success = tracker.record_prediction(
                prediction_id=prediction_id,
                lottery_type="powercolor",
                predicted_main=[1, 7, 12, 23, 28, 35],
                predicted_special=[3],
                strategy=["ensemble", "frequency_based", "pattern_based"][i % 3],
                confidence=75.0 + i * 2,
                algorithm_weights={
                    'frequency_based': 0.3,
                    'pattern_based': 0.4,
                    'trend_analysis': 0.3
                },
                period=f"11300{50+i:02d}",
                metadata={"test_data": True, "iteration": i}
            )
            
            if success:
                test_predictions.append(prediction_id)
                print(f"  ✅ 預測記錄 {i+1}: {prediction_id[:8]}...")
            else:
                print(f"  ❌ 預測記錄 {i+1}: 失敗")
        
        # 測試預測驗證
        print(f"\n✅ 測試預測驗證...")
        verification_count = 0
        
        for i, prediction_id in enumerate(test_predictions[:7]):  # 驗證前7個
            # 模擬實際開獎結果
            actual_main = [1, 15, 22, 28, 31, 38]  # 假設2個匹配
            actual_special = [5] if i % 3 == 0 else [3]  # 部分特別號匹配
            
            success = tracker.verify_prediction(
                prediction_id=prediction_id,
                actual_main=actual_main,
                actual_special=actual_special,
                draw_timestamp=datetime.now() - timedelta(hours=i)
            )
            
            if success:
                verification_count += 1
                print(f"  ✅ 驗證 {i+1}: 匹配2個號碼")
            else:
                print(f"  ❌ 驗證 {i+1}: 失敗")
        
        # 測試獲取記錄
        print(f"\n📊 測試記錄查詢...")
        
        all_records = tracker.get_prediction_records(lottery_type="powercolor")
        verified_records = tracker.get_prediction_records(
            lottery_type="powercolor",
            status=PredictionStatus.VERIFIED
        )
        pending_records = tracker.get_prediction_records(
            lottery_type="powercolor",
            status=PredictionStatus.PENDING
        )
        
        print(f"  總記錄數: {len(all_records)}")
        print(f"  已驗證記錄: {len(verified_records)}")
        print(f"  待驗證記錄: {len(pending_records)}")
        
        # 測試過期標記
        print(f"\n⏰ 測試過期標記...")
        expired_count = tracker.mark_expired_predictions(expiry_days=0)  # 立即過期用於測試
        print(f"  標記過期預測: {expired_count}個")
        
        return len(test_predictions) >= 5 and verification_count >= 3
        
    except Exception as e:
        print(f"❌ 預測追蹤器測試失敗: {e}")
        return False

def test_statistics_analyzer():
    """測試統計分析器"""
    print("\n📊 測試統計分析器")
    print("-" * 50)
    
    try:
        db_manager = DBManager()
        tracker = PredictionTracker(db_manager)
        analyzer = StatisticsAnalyzer(tracker)
        
        # 測試性能指標計算
        print("📈 測試性能指標計算...")
        
        metrics = analyzer.calculate_performance_metrics(
            lottery_type="powercolor",
            timeframe=AnalysisTimeframe.MONTHLY
        )
        
        print(f"  準確率: {metrics.accuracy_rate:.3f}")
        print(f"  平均信心度: {metrics.average_confidence:.1f}%")
        print(f"  信心度相關性: {metrics.confidence_accuracy_correlation:.3f}")
        print(f"  時間一致性: {metrics.temporal_consistency:.3f}")
        print(f"  改進趨勢: {metrics.improvement_trend:.3f}")
        
        if metrics.strategy_success_rates:
            print("  策略成功率:")
            for strategy, rate in metrics.strategy_success_rates.items():
                print(f"    {strategy}: {rate:.1%}")
        
        # 測試統計報告生成
        print(f"\n📋 測試統計報告生成...")
        
        for timeframe in [AnalysisTimeframe.WEEKLY, AnalysisTimeframe.MONTHLY]:
            try:
                report = analyzer.generate_statistics_report(
                    lottery_type="powercolor",
                    timeframe=timeframe
                )
                
                print(f"  ✅ {timeframe.value} 報告:")
                print(f"    報告ID: {report.report_id}")
                print(f"    總預測數: {report.total_predictions}")
                print(f"    已驗證數: {report.verified_predictions}")
                print(f"    準確率: {report.accuracy_metrics.get('accuracy_rate', 0):.1%}")
                print(f"    建議數量: {len(report.recommendations)}")
                
                if report.recommendations:
                    print("    主要建議:")
                    for rec in report.recommendations[:2]:
                        print(f"      • {rec}")
                
            except Exception as e:
                print(f"  ❌ {timeframe.value} 報告生成失敗: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 統計分析器測試失敗: {e}")
        return False

async def test_integrated_tracking_system():
    """測試統合追蹤系統"""
    print("\n🔗 測試統合追蹤系統")
    print("-" * 50)
    
    try:
        db_manager = DBManager()
        tracking_system = IntegratedTrackingSystem(db_manager)
        
        # 測試系統啟動
        print("🚀 測試系統啟動...")
        await tracking_system.start_tracking_service()
        print("  ✅ 追蹤服務已啟動")
        
        # 測試儀表板數據
        print("\n📊 測試儀表板數據...")
        dashboard_data = tracking_system.get_dashboard_data("powercolor")
        
        if dashboard_data:
            print(f"  彩票類型: {dashboard_data.get('lottery_type', 'N/A')}")
            print(f"  總預測數: {dashboard_data.get('total_predictions', 0)}")
            print(f"  已驗證數: {dashboard_data.get('verified_predictions', 0)}")
            print(f"  待驗證數: {dashboard_data.get('pending_predictions', 0)}")
            print(f"  準確率: {dashboard_data.get('accuracy_rate', 0):.1%}")
            print(f"  平均信心度: {dashboard_data.get('average_confidence', 0):.1f}%")
            
            strategy_perf = dashboard_data.get('strategy_performance', {})
            if strategy_perf:
                print("  策略性能:")
                for strategy, rate in strategy_perf.items():
                    print(f"    {strategy}: {rate:.1%}")
        else:
            print("  ❌ 無法獲取儀表板數據")
        
        # 測試綜合報告生成
        print(f"\n📄 測試綜合報告生成...")
        comprehensive_report = await tracking_system.generate_comprehensive_report("powercolor")
        
        if comprehensive_report:
            print(f"  ✅ 綜合報告生成成功")
            print(f"  彩票類型: {comprehensive_report.get('lottery_type', 'N/A')}")
            print(f"  生成時間: {comprehensive_report.get('generated_timestamp', 'N/A')}")
            
            reports = comprehensive_report.get('reports', {})
            for timeframe, report_data in reports.items():
                if report_data:
                    print(f"    {timeframe} 報告: ✅")
                else:
                    print(f"    {timeframe} 報告: ❌")
        else:
            print("  ❌ 綜合報告生成失敗")
        
        return bool(dashboard_data and comprehensive_report)
        
    except Exception as e:
        print(f"❌ 統合追蹤系統測試失敗: {e}")
        return False

def test_visualization_generator():
    """測試可視化生成器"""
    print("\n📈 測試可視化生成器")
    print("-" * 50)
    
    try:
        db_manager = DBManager()
        tracking_system = IntegratedTrackingSystem(db_manager)
        visualizer = VisualizationGenerator(tracking_system)
        
        # 測試各種圖表生成
        chart_tests = [
            ("準確度趨勢圖", lambda: visualizer.create_accuracy_trend_chart("powercolor")),
            ("策略性能圖", lambda: visualizer.create_strategy_performance_chart("powercolor")),
            ("信心度散點圖", lambda: visualizer.create_confidence_accuracy_scatter("powercolor")),
            ("月度總結圖", lambda: visualizer.create_monthly_summary_chart("powercolor")),
            ("獎項分布圖", lambda: visualizer.create_prize_distribution_chart("powercolor"))
        ]
        
        successful_charts = 0
        
        for chart_name, chart_func in chart_tests:
            try:
                print(f"  生成{chart_name}...")
                chart_data = chart_func()
                
                if chart_data and len(chart_data) > 100:  # Base64數據應該很長
                    print(f"    ✅ {chart_name}: 成功 ({len(chart_data)} 字節)")
                    successful_charts += 1
                else:
                    print(f"    ⚠️  {chart_name}: 數據不足或生成失敗")
                    
            except Exception as e:
                print(f"    ❌ {chart_name}: 失敗 ({e})")
        
        print(f"\n📊 圖表生成總結: {successful_charts}/{len(chart_tests)} 成功")
        
        return successful_charts >= len(chart_tests) // 2  # 至少一半成功
        
    except Exception as e:
        print(f"❌ 可視化生成器測試失敗: {e}")
        return False

def test_report_generator():
    """測試報告生成器"""
    print("\n📄 測試報告生成器")
    print("-" * 50)
    
    try:
        db_manager = DBManager()
        tracking_system = IntegratedTrackingSystem(db_manager)
        report_generator = ReportGenerator(tracking_system)
        
        # 測試HTML報告生成
        print("🌐 測試HTML報告生成...")
        html_report = report_generator.generate_comprehensive_html_report("powercolor")
        
        if html_report and "<html>" in html_report and "</html>" in html_report:
            print(f"  ✅ HTML報告生成成功 ({len(html_report)} 字節)")
            
            # 測試報告保存
            print("💾 測試報告保存...")
            filepath = report_generator.save_report_to_file("powercolor", html_report)
            
            if filepath and os.path.exists(filepath):
                print(f"  ✅ 報告已保存: {filepath}")
                file_size = os.path.getsize(filepath)
                print(f"  文件大小: {file_size} 字節")
            else:
                print("  ❌ 報告保存失敗")
                
        else:
            print("  ❌ HTML報告生成失敗")
            return False
        
        # 測試CSV導出
        print("\n📊 測試CSV數據導出...")
        csv_filepath = report_generator.export_data_to_csv("powercolor")
        
        if csv_filepath and os.path.exists(csv_filepath):
            print(f"  ✅ CSV已導出: {csv_filepath}")
            file_size = os.path.getsize(csv_filepath)
            print(f"  文件大小: {file_size} 字節")
        else:
            print("  ⚠️  CSV導出失敗或無數據")
        
        return True
        
    except Exception as e:
        print(f"❌ 報告生成器測試失敗: {e}")
        return False

def test_database_performance():
    """測試數據庫性能"""
    print("\n⚡ 測試數據庫性能")
    print("-" * 50)
    
    try:
        db_manager = DBManager()
        tracker = PredictionTracker(db_manager)
        
        # 測試批量插入性能
        print("📊 測試批量預測記錄性能...")
        
        start_time = time.time()
        batch_size = 100
        successful_records = 0
        
        for i in range(batch_size):
            prediction_id = str(uuid.uuid4())
            success = tracker.record_prediction(
                prediction_id=prediction_id,
                lottery_type="lotto649",
                predicted_main=[1, 7, 12, 23, 28, 35],
                predicted_special=None,
                strategy="performance_test",
                confidence=70.0 + (i % 30),
                algorithm_weights={'test': 1.0},
                period=f"test_{i:04d}",
                metadata={"performance_test": True}
            )
            if success:
                successful_records += 1
        
        insert_time = time.time() - start_time
        
        print(f"  批量插入: {successful_records}/{batch_size} 成功")
        print(f"  耗時: {insert_time:.2f}秒")
        print(f"  平均: {insert_time/batch_size*1000:.1f}ms/記錄")
        
        # 測試查詢性能
        print("\n🔍 測試查詢性能...")
        
        start_time = time.time()
        records = tracker.get_prediction_records(lottery_type="lotto649", limit=50)
        query_time = time.time() - start_time
        
        print(f"  查詢結果: {len(records)} 條記錄")
        print(f"  查詢耗時: {query_time*1000:.1f}ms")
        
        # 測試批量驗證性能
        print("\n✅ 測試批量驗證性能...")
        
        start_time = time.time()
        verification_count = 0
        
        # 驗證前20個記錄
        for i, record in enumerate(records[:20]):
            success = tracker.verify_prediction(
                prediction_id=record.prediction_id,
                actual_main=[1, 15, 22, 28, 31, 38],
                actual_special=None
            )
            if success:
                verification_count += 1
        
        verification_time = time.time() - start_time
        
        print(f"  批量驗證: {verification_count}/20 成功")
        print(f"  耗時: {verification_time:.2f}秒")
        print(f"  平均: {verification_time/20*1000:.1f}ms/驗證")
        
        # 性能評估
        performance_good = (
            insert_time/batch_size < 0.1 and  # 每條記錄<100ms
            query_time < 1.0 and              # 查詢<1秒
            verification_time/20 < 0.05       # 每次驗證<50ms
        )
        
        print(f"\n📊 性能評估: {'✅ 良好' if performance_good else '⚠️ 需優化'}")
        
        return performance_good
        
    except Exception as e:
        print(f"❌ 數據庫性能測試失敗: {e}")
        return False

async def test_integration_with_existing_systems():
    """測試與現有系統的集成"""
    print("\n🔗 測試與現有系統集成")
    print("-" * 50)
    
    try:
        # 測試與Phase 2系統的集成
        from prediction.enhanced_multi_algorithm_predictor import EnhancedMultiAlgorithmPredictor
        from phase3.accuracy_assessment_engine import AccuracyAssessmentEngine
        from phase3.universal_prediction_framework import UniversalPredictor
        
        db_manager = DBManager()
        tracking_system = IntegratedTrackingSystem(db_manager)
        
        print("🧠 測試與預測器集成...")
        
        # 創建預測器實例
        try:
            predictor = EnhancedMultiAlgorithmPredictor("powercolor", db_manager)
            print("  ✅ 增強多算法預測器創建成功")
        except Exception as e:
            print(f"  ⚠️ 預測器創建失敗: {e}")
            predictor = None
        
        # 測試與評估引擎集成
        try:
            assessment_engine = AccuracyAssessmentEngine(db_manager)
            print("  ✅ 評估引擎創建成功")
        except Exception as e:
            print(f"  ⚠️ 評估引擎創建失敗: {e}")
            assessment_engine = None
        
        # 測試與通用預測器集成
        try:
            universal_predictor = UniversalPredictor(db_manager, assessment_engine)
            print("  ✅ 通用預測器創建成功")
        except Exception as e:
            print(f"  ⚠️ 通用預測器創建失敗: {e}")
            universal_predictor = None
        
        # 測試端到端預測和追蹤流程
        if universal_predictor:
            print("\n🎯 測試端到端預測追蹤流程...")
            
            try:
                # 執行預測
                prediction_result = universal_predictor.predict(
                    lottery_type="powercolor",
                    strategy="ensemble",
                    ensemble_size=3,
                    use_cross_learning=False
                )
                
                if prediction_result:
                    print(f"  ✅ 預測執行成功: {prediction_result.main_numbers}")
                    
                    # 記錄到追蹤系統
                    tracking_success = tracking_system.tracker.record_prediction(
                        prediction_id=prediction_result.prediction_id,
                        lottery_type=prediction_result.lottery_type,
                        predicted_main=prediction_result.main_numbers,
                        predicted_special=prediction_result.special_numbers,
                        strategy=prediction_result.strategy_used,
                        confidence=prediction_result.confidence,
                        algorithm_weights={},
                        metadata=prediction_result.metadata
                    )
                    
                    if tracking_success:
                        print("  ✅ 預測記錄到追蹤系統成功")
                        
                        # 模擬驗證
                        verification_success = tracking_system.tracker.verify_prediction(
                            prediction_id=prediction_result.prediction_id,
                            actual_main=[1, 15, 22, 28, 31, 38],
                            actual_special=[5]
                        )
                        
                        if verification_success:
                            print("  ✅ 預測驗證成功")
                            return True
                        else:
                            print("  ❌ 預測驗證失敗")
                    else:
                        print("  ❌ 預測記錄失敗")
                else:
                    print("  ❌ 預測執行失敗")
                    
            except Exception as e:
                print(f"  ❌ 端到端流程測試失敗: {e}")
        
        return False
        
    except Exception as e:
        print(f"❌ 系統集成測試失敗: {e}")
        return False

async def main():
    """主測試函數"""
    print("🚀 Phase 3.5 預測結果追蹤和統計分析系統測試")
    print("=" * 70)
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # 測試序列
    test_results = []
    
    # 1. 預測追蹤器測試
    print("\n1. 預測追蹤器測試...")
    test_results.append(test_prediction_tracker())
    
    # 2. 統計分析器測試
    print("\n2. 統計分析器測試...")
    test_results.append(test_statistics_analyzer())
    
    # 3. 統合追蹤系統測試
    print("\n3. 統合追蹤系統測試...")
    test_results.append(await test_integrated_tracking_system())
    
    # 4. 可視化生成器測試
    print("\n4. 可視化生成器測試...")
    test_results.append(test_visualization_generator())
    
    # 5. 報告生成器測試
    print("\n5. 報告生成器測試...")
    test_results.append(test_report_generator())
    
    # 6. 數據庫性能測試
    print("\n6. 數據庫性能測試...")
    test_results.append(test_database_performance())
    
    # 7. 系統集成測試
    print("\n7. 系統集成測試...")
    test_results.append(await test_integration_with_existing_systems())
    
    # 總結
    print("\n\n🎯 Phase 3.5 預測結果追蹤和統計分析系統測試總結")
    print("=" * 70)
    
    test_names = [
        "預測追蹤器測試",
        "統計分析器測試",
        "統合追蹤系統測試",
        "可視化生成器測試",
        "報告生成器測試",
        "數據庫性能測試",
        "系統集成測試"
    ]
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"總測試數: {total_tests}")
    print(f"通過測試: {passed_tests}")
    print(f"測試通過率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n各項測試結果:")
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {i+1}. {name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有測試通過！Phase 3.5 預測結果追蹤和統計分析系統運行正常")
        print("📋 Phase 3.5 主要功能:")
        print("  ✅ 完整的預測記錄追蹤")
        print("  ✅ 自動預測驗證和評估")
        print("  ✅ 多維度統計分析")
        print("  ✅ 性能指標計算和趨勢分析")
        print("  ✅ 策略性能對比和優化建議")
        print("  ✅ 可視化圖表生成")
        print("  ✅ 綜合HTML報告生成")
        print("  ✅ 數據導出和備份")
        
        print("\n💡 核心特性:")
        print("  • 全生命週期預測追蹤管理")
        print("  • 多時間框架統計分析")
        print("  • 智能性能指標計算")
        print("  • 可視化報告和圖表生成")
        print("  • 高性能數據庫存儲")
        print("  • 與現有系統完整集成")
        print("  • 自動化報告生成和導出")
        print("  • 實時性能監控和優化建議")
    else:
        print(f"\n⚠️  {total_tests - passed_tests} 項測試失敗，需要進一步優化")
    
    print("=" * 70)

if __name__ == "__main__":
    asyncio.run(main())