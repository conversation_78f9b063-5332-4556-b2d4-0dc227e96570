#!/usr/bin/env python3
"""
彩票預測系統 - 整合系統測試
測試分離式預測、成功分析和策略優化功能
"""

import os
import sys
import logging
from datetime import datetime

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/integrated_test_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('integrated_test')

def test_separated_prediction():
    """測試分離式預測功能"""
    print("\n" + "="*60)
    print("測試分離式預測功能")
    print("="*60)
    
    try:
        from data.db_manager import DBManager
        from prediction.integrated_predictor import IntegratedPredictor
        from display.prediction_display import PredictionDisplay
        from config.config_manager import ConfigManager
        
        # 初始化組件
        config_manager = ConfigManager()
        db_manager = DBManager(config_manager=config_manager)
        integrated_predictor = IntegratedPredictor(db_manager)
        integrated_predictor.initialize_board_path_engines()
        display = PredictionDisplay()
        
        # 測試威力彩預測
        print("\n測試威力彩分離式預測...")
        results = integrated_predictor.predict_separated(
            lottery_type='powercolor',
            candidates_count=3,
            min_confidence=0.4
        )
        
        if results:
            print("✅ 威力彩分離式預測成功")
            display.display_separated_predictions(results)
            
            # 保存測試報告
            report_path = display.save_prediction_report(results, 'test_results')
            if report_path:
                print(f"✅ 測試報告已保存: {report_path}")
        else:
            print("❌ 威力彩分離式預測失敗")
        
        return results is not None
        
    except Exception as e:
        logger.error(f"測試分離式預測時出錯: {str(e)}")
        print(f"❌ 測試失敗: {str(e)}")
        return False

def test_success_analysis():
    """測試成功分析功能"""
    print("\n" + "="*60)
    print("測試成功分析功能")
    print("="*60)
    
    try:
        from data.db_manager import DBManager
        from prediction.integrated_predictor import PredictionSuccessAnalyzer
        from config.config_manager import ConfigManager
        
        # 初始化組件
        config_manager = ConfigManager()
        db_manager = DBManager(config_manager=config_manager)
        analyzer = PredictionSuccessAnalyzer(db_manager)
        
        # 測試威力彩成功分析
        print("\n測試威力彩成功分析...")
        success_analysis = analyzer.analyze_successful_predictions('powercolor')
        
        if success_analysis:
            print("✅ 威力彩成功分析完成")
            
            # 顯示分析結果摘要
            ml_factors = success_analysis.get('ml_success_factors', [])
            bp_factors = success_analysis.get('board_path_success_factors', [])
            common_patterns = success_analysis.get('common_success_patterns', [])
            
            print(f"  機器學習成功因素: {len(ml_factors)} 個")
            print(f"  板路分析成功因素: {len(bp_factors)} 個")
            print(f"  共同成功模式: {len(common_patterns)} 個")
            
            if ml_factors:
                print("  機器學習成功因素示例:")
                for factor in ml_factors[:2]:
                    print(f"    • {factor}")
            
            if bp_factors:
                print("  板路分析成功因素示例:")
                for factor in bp_factors[:2]:
                    print(f"    • {factor}")
        else:
            print("⚠️ 威力彩成功分析無結果（可能缺少歷史數據）")
        
        return True
        
    except Exception as e:
        logger.error(f"測試成功分析時出錯: {str(e)}")
        print(f"❌ 測試失敗: {str(e)}")
        return False

def test_strategy_optimization():
    """測試策略優化功能"""
    print("\n" + "="*60)
    print("測試策略優化功能")
    print("="*60)
    
    try:
        from data.db_manager import DBManager
        from strategy.strategy_optimizer import StrategyOptimizer
        from prediction.integrated_predictor import PredictionSuccessAnalyzer
        from config.config_manager import ConfigManager
        
        # 初始化組件
        config_manager = ConfigManager()
        db_manager = DBManager(config_manager=config_manager)
        optimizer = StrategyOptimizer(db_manager)
        analyzer = PredictionSuccessAnalyzer(db_manager)
        
        # 獲取成功分析結果
        success_analysis = analyzer.analyze_successful_predictions('powercolor')
        if not success_analysis:
            # 創建模擬的成功分析結果用於測試
            success_analysis = {
                'ml_success_factors': ['測試ML因素1', '測試ML因素2'],
                'board_path_success_factors': ['測試板路因素1'],
                'common_success_patterns': ['測試共同模式1', '測試共同模式2']
            }
        
        # 測試策略優化
        print("\n測試威力彩策略優化...")
        original_strategy = optimizer.get_current_strategy('powercolor')
        print(f"原始策略: ML權重={original_strategy['ml_weight']:.2f}, "
              f"板路權重={original_strategy['board_path_weight']:.2f}")
        
        optimized_strategy = optimizer.optimize_strategy('powercolor', success_analysis)
        
        if optimized_strategy:
            print("✅ 威力彩策略優化完成")
            print(f"優化策略: ML權重={optimized_strategy['ml_weight']:.2f}, "
                  f"板路權重={optimized_strategy['board_path_weight']:.2f}")
            print(f"候選數量: {optimized_strategy['candidates_count']}")
            print(f"最低信心分數: {optimized_strategy['min_confidence']:.2f}")
            
            # 保存策略
            optimizer.save_strategies()
            print("✅ 策略已保存")
        else:
            print("❌ 威力彩策略優化失敗")
        
        return optimized_strategy is not None
        
    except Exception as e:
        logger.error(f"測試策略優化時出錯: {str(e)}")
        print(f"❌ 測試失敗: {str(e)}")
        return False

def test_daily_automation():
    """測試每日自動化功能"""
    print("\n" + "="*60)
    print("測試每日自動化功能")
    print("="*60)
    
    try:
        from daily_automation import DailyAutomation
        from config.config_manager import ConfigManager
        
        # 初始化自動化管理器
        config_manager = ConfigManager()
        automation = DailyAutomation(config_manager=config_manager)
        
        print("\n測試每日自動化任務組件...")
        
        # 測試分析功能
        print("  測試預測準確度分析...")
        try:
            automation.analyze_prediction_accuracy()
            print("  ✅ 預測準確度分析測試通過")
        except Exception as e:
            print(f"  ⚠️ 預測準確度分析測試警告: {str(e)}")
        
        # 測試預測生成功能
        print("  測試每日預測生成...")
        try:
            automation.generate_daily_predictions()
            print("  ✅ 每日預測生成測試通過")
        except Exception as e:
            print(f"  ⚠️ 每日預測生成測試警告: {str(e)}")
        
        # 測試報告生成功能
        print("  測試每日報告生成...")
        try:
            automation.generate_daily_report()
            print("  ✅ 每日報告生成測試通過")
        except Exception as e:
            print(f"  ⚠️ 每日報告生成測試警告: {str(e)}")
        
        print("✅ 每日自動化功能測試完成")
        return True
        
    except Exception as e:
        logger.error(f"測試每日自動化時出錯: {str(e)}")
        print(f"❌ 測試失敗: {str(e)}")
        return False

def test_display_functionality():
    """測試顯示功能"""
    print("\n" + "="*60)
    print("測試顯示功能")
    print("="*60)
    
    try:
        from display.prediction_display import PredictionDisplay
        from prediction.prediction_result import PredictionResult
        
        # 創建測試預測結果
        display = PredictionDisplay()
        
        # 創建模擬的分離式預測結果
        mock_results = {
            'period': '114000999',
            'lottery_type': 'powercolor',
            'prediction_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'ml_prediction': PredictionResult('powercolor', 'machine_learning', 'v2.0'),
            'board_path_prediction': PredictionResult('powercolor', 'board_path_analysis', 'v2.0'),
            'success_analysis': {
                'ml_success_factors': ['測試ML成功因素'],
                'board_path_success_factors': ['測試板路成功因素'],
                'common_success_patterns': ['測試共同模式']
            }
        }
        
        # 添加模擬候選結果
        mock_results['ml_prediction'].add_candidate(
            main_numbers=[1, 5, 12, 18, 25, 33],
            special_number=3,
            confidence=0.75,
            explanation=['測試ML預測理由']
        )
        
        mock_results['board_path_prediction'].add_candidate(
            main_numbers=[3, 8, 15, 22, 28, 35],
            special_number=5,
            confidence=0.68,
            explanation=['測試板路預測理由']
        )
        
        print("\n測試分離式預測結果顯示...")
        display.display_separated_predictions(mock_results)
        display.display_comparison_summary(mock_results)
        
        print("\n✅ 顯示功能測試完成")
        return True
        
    except Exception as e:
        logger.error(f"測試顯示功能時出錯: {str(e)}")
        print(f"❌ 測試失敗: {str(e)}")
        return False

def main():
    """主測試函數"""
    print("彩票預測系統 - 整合功能測試")
    print("="*60)
    
    # 確保必要目錄存在
    os.makedirs('test_results', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    test_results = []
    
    # 執行各項測試
    tests = [
        ("顯示功能", test_display_functionality),
        ("成功分析", test_success_analysis),
        ("策略優化", test_strategy_optimization),
        ("分離式預測", test_separated_prediction),
        ("每日自動化", test_daily_automation),
    ]
    
    for test_name, test_func in tests:
        print(f"\n開始測試: {test_name}")
        try:
            result = test_func()
            test_results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 測試通過")
            else:
                print(f"❌ {test_name} 測試失敗")
        except Exception as e:
            logger.error(f"{test_name} 測試出錯: {str(e)}")
            print(f"❌ {test_name} 測試出錯: {str(e)}")
            test_results.append((test_name, False))
    
    # 顯示測試總結
    print("\n" + "="*60)
    print("測試總結")
    print("="*60)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name}: {status}")
    
    print(f"\n總計: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！整合系統運行正常。")
    elif passed > total // 2:
        print("⚠️ 大部分測試通過，系統基本可用。")
    else:
        print("❌ 多項測試失敗，請檢查系統配置。")

if __name__ == "__main__":
    main()
