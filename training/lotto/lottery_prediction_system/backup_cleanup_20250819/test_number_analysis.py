#!/usr/bin/env python3
"""
測試號碼分析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import DBManager
from analysis.number_pattern_analyzer import NumberPatternAnalyzer
import json

def test_consecutive_numbers():
    """測試連號分析 - 1,2,3,4,5,6"""
    print("=" * 60)
    print("🔗 測試連號分析: 1,2,3,4,5,6")
    print("=" * 60)
    
    from config_manager import ConfigManager
    config_manager = ConfigManager()
    db = DBManager(config_manager)
    analyzer = NumberPatternAnalyzer(db)
    
    # 分析連號組合
    numbers = [1, 2, 3, 4, 5, 6]
    result = analyzer.analyze_number_appearance_reasons('powercolor', numbers)
    
    print(f"分析號碼: {numbers}")
    print(f"分析時間: {result['analysis_time']}")
    print()
    
    # 連號模式分析
    consecutive = result['pattern_analysis']['consecutive']
    print("📊 連號模式分析:")
    print(f"  - 是否有連號: {consecutive['has_consecutive']}")
    print(f"  - 連續組合: {consecutive['consecutive_groups']}")
    print(f"  - 最長連續: {consecutive['max_consecutive_length']} 個")
    print(f"  - 連號比例: {consecutive['consecutive_percentage']:.1f}%")
    if 'special_pattern' in consecutive:
        print(f"  - 特殊模式: {consecutive['special_pattern']}")
    print()
    
    # 數學關係分析
    math_rel = result['mathematical_relationships']
    print("🔢 數學關係分析:")
    print(f"  - 總和: {math_rel['sum_analysis']['total_sum']}")
    print(f"  - 平均: {math_rel['sum_analysis']['average']:.1f}")
    print(f"  - 類別: {math_rel['sum_analysis']['sum_category']}")
    if math_rel['special_relationships']:
        print(f"  - 特殊關係: {math_rel['special_relationships']}")
    print()
    
    # 出現原因
    print("🎯 出現原因推論:")
    for i, reason in enumerate(result['appearance_reasons'], 1):
        print(f"  {i}. {reason}")
    print()

def test_arithmetic_sequence():
    """測試等差數列 - 7,14,21,28,35,42"""
    print("=" * 60)
    print("📐 測試等差數列分析: 7,14,21,28,35,42")
    print("=" * 60)
    
    from config_manager import ConfigManager
    config_manager = ConfigManager()
    db = DBManager(config_manager)
    analyzer = NumberPatternAnalyzer(db)
    
    numbers = [7, 14, 21, 28, 35, 42]
    result = analyzer.analyze_number_appearance_reasons('powercolor', numbers)
    
    print(f"分析號碼: {numbers}")
    print()
    
    # 數學關係分析
    math_rel = result['mathematical_relationships']
    print("🔢 數學關係分析:")
    print(f"  - 總和: {math_rel['sum_analysis']['total_sum']}")
    print(f"  - 平均: {math_rel['sum_analysis']['average']:.1f}")
    
    if math_rel['arithmetic_sequences']:
        print("  - 等差數列:")
        for seq in math_rel['arithmetic_sequences']:
            print(f"    {seq['numbers']} (公差: {seq['common_difference']})")
    
    if math_rel['special_relationships']:
        print(f"  - 特殊關係: {math_rel['special_relationships']}")
    print()
    
    # 出現原因
    print("🎯 出現原因推論:")
    for i, reason in enumerate(result['appearance_reasons'], 1):
        print(f"  {i}. {reason}")
    print()

def test_square_numbers():
    """測試平方數 - 1,4,9,16,25,36"""
    print("=" * 60)
    print("🔲 測試平方數分析: 1,4,9,16,25,36")
    print("=" * 60)
    
    from config_manager import ConfigManager
    config_manager = ConfigManager()
    db = DBManager(config_manager)
    analyzer = NumberPatternAnalyzer(db)
    
    numbers = [1, 4, 9, 16, 25, 36]
    result = analyzer.analyze_number_appearance_reasons('powercolor', numbers)
    
    print(f"分析號碼: {numbers}")
    print()
    
    # 數學關係分析
    math_rel = result['mathematical_relationships']
    print("🔢 數學關係分析:")
    print(f"  - 總和: {math_rel['sum_analysis']['total_sum']}")
    print(f"  - 平均: {math_rel['sum_analysis']['average']:.1f}")
    
    if math_rel['special_relationships']:
        print("  - 特殊關係:")
        for rel in math_rel['special_relationships']:
            print(f"    {rel}")
    print()
    
    # 出現原因
    print("🎯 出現原因推論:")
    for i, reason in enumerate(result['appearance_reasons'], 1):
        print(f"  {i}. {reason}")
    print()

def test_random_numbers():
    """測試隨機數組 - 3,17,22,31,38,45"""
    print("=" * 60)
    print("🎲 測試隨機數組分析: 3,17,22,31,38,45")
    print("=" * 60)
    
    from config_manager import ConfigManager
    config_manager = ConfigManager()
    db = DBManager(config_manager)
    analyzer = NumberPatternAnalyzer(db)
    
    numbers = [3, 17, 22, 31, 38, 45]
    result = analyzer.analyze_number_appearance_reasons('powercolor', numbers)
    
    print(f"分析號碼: {numbers}")
    print()
    
    # 歷史分析
    historical = result['historical_context']
    print("📊 歷史出現分析:")
    for num in numbers:
        freq_data = historical['individual_frequencies'][num]
        hot_cold = historical['cold_hot_analysis'][num]
        print(f"  - 號碼 {num}: 出現 {freq_data['frequency']} 次 ({freq_data['percentage']:.1f}%) - {hot_cold}")
    print()
    
    # 機率分析
    probability = result['probability_analysis']
    print("📈 機率分析:")
    print(f"  - 理論機率: {probability['theoretical_probability']['percentage']:.3f}%")
    print("  - 觀察機率偏差:")
    for num in numbers:
        obs_data = probability['observed_probability'][num]
        deviation = obs_data['deviation_percentage']
        print(f"    號碼 {num}: {obs_data['percentage']:.2f}% ({deviation:+.1f}%)")
    print()
    
    # 出現原因
    print("🎯 出現原因推論:")
    for i, reason in enumerate(result['appearance_reasons'], 1):
        print(f"  {i}. {reason}")
    print()

def test_board_path_analysis():
    """測試板路分析功能"""
    print("=" * 60)
    print("🔄 測試板路分析功能")
    print("=" * 60)
    
    from config_manager import ConfigManager
    config_manager = ConfigManager()
    db = DBManager(config_manager)
    analyzer = NumberPatternAnalyzer(db)
    
    # 使用最近出現過的號碼組合
    numbers = [1, 7, 20, 21, 38, 39]  # 基於最近的開獎結果
    result = analyzer.analyze_number_appearance_reasons('powercolor', numbers)
    
    print(f"分析號碼: {numbers}")
    print()
    
    # 板路關聯分析
    board_path = result['board_path_connections']
    print("🔄 板路關聯分析:")
    
    if board_path['follow_patterns']:
        print("  - 跟隨模式:")
        for pattern in board_path['follow_patterns'][:5]:
            print(f"    號碼 {pattern['previous_number']} → {pattern['target_number']} "
                  f"(關聯 {pattern['correlation_count']} 次, "
                  f"關聯率 {pattern['correlation_rate']*100:.1f}%)")
    
    if board_path['skip_patterns']:
        print("  - 跳號模式:")
        for pattern in board_path['skip_patterns']:
            print(f"    號碼 {pattern['number']} 間隔 {pattern['common_interval']} 期 "
                  f"(頻率 {pattern['frequency']} 次)")
    
    if not board_path['follow_patterns'] and not board_path['skip_patterns']:
        print("  - 未發現明顯的板路關聯模式")
    print()
    
    # 出現原因
    print("🎯 出現原因推論:")
    for i, reason in enumerate(result['appearance_reasons'], 1):
        print(f"  {i}. {reason}")
    print()

def main():
    """主測試函數"""
    print("🧪 號碼模式分析器測試")
    print("=" * 60)
    print("測試各種號碼組合的分析功能...")
    print()
    
    try:
        # 測試連號
        test_consecutive_numbers()
        
        # 測試等差數列
        test_arithmetic_sequence()
        
        # 測試平方數
        test_square_numbers()
        
        # 測試隨機數組
        test_random_numbers()
        
        # 測試板路分析
        test_board_path_analysis()
        
        print("=" * 60)
        print("✅ 所有測試完成！")
        print("=" * 60)
        print()
        print("💡 測試結果說明:")
        print("1. 連號分析能正確識別 1,2,3,4,5,6 的完全連續模式")
        print("2. 數學關係分析能識別等差數列和平方數")
        print("3. 歷史分析提供號碼出現頻率和冷熱狀態")
        print("4. 板路分析探討號碼間的關聯模式")
        print("5. 機率分析比較理論值與觀察值的偏差")
        print("6. 綜合推論提供號碼出現的可能原因")
        print()
        print("🌐 Web界面測試:")
        print("請訪問 http://127.0.0.1:5001/number_analysis")
        print("輸入號碼組合進行互動式分析")
        
    except Exception as e:
        print(f"❌ 測試過程中出現錯誤: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
