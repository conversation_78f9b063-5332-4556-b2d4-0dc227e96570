#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試Phase 3整合功能
"""

import os
import sys
import json

# 添加項目根目錄到Python路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_phase3_integration():
    """測試Phase 3整合"""
    print("🚀 測試Phase 3整合功能...")
    
    try:
        # 測試數據庫遷移狀態
        from data.phase3_database_migration import Phase3DatabaseMigration
        migration = Phase3DatabaseMigration()
        status = migration.check_migration_status()
        
        print(f"📊 數據庫遷移狀態:")
        print(f"  - 總表數: {status['total_tables']}")
        print(f"  - 已存在: {len(status['existing_tables'])} 個")
        print(f"  - 完成度: {status['migration_percentage']:.1f}%")
        
        if status['missing_tables']:
            print(f"  - 缺失表: {', '.join(status['missing_tables'])}")
        else:
            print("  ✅ 所有表已創建")
        
        # 測試Web整合
        from web.phase3_integration import get_phase3_integration
        from data.db_manager import DBManager
        
        db_manager = DBManager()
        phase3_integration = get_phase3_integration(db_manager)
        
        print(f"\n🔧 Phase 3組件狀態:")
        print(f"  - 整合可用: {phase3_integration.is_available}")
        print(f"  - 組件數量: {len(phase3_integration.components)}")
        
        if phase3_integration.components:
            print(f"  - 已載入組件: {list(phase3_integration.components.keys())}")
        
        # 測試API端點功能
        print(f"\n🌐 API端點測試:")
        
        # 測試系統狀態
        system_info = {
            'phase3_available': phase3_integration.is_available,
            'components_loaded': len(phase3_integration.components),
            'components_available': list(phase3_integration.components.keys()),
            'integration_status': 'active' if phase3_integration.is_available else 'inactive'
        }
        
        print(f"  - 系統狀態API: ✅ 可用")
        print(f"    數據: {json.dumps(system_info, ensure_ascii=False, indent=4)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_web_application():
    """測試Web應用啟動"""
    print("\n🌐 測試Web應用啟動...")
    
    try:
        # 檢查重要文件
        important_files = [
            'web/app.py',
            'web/phase3_integration.py',
            'web/templates/universal_prediction.html',
            'web/templates/phase3_dashboard.html',
            'web/templates/tracking_analytics.html',
            'data/phase3_database_migration.py'
        ]
        
        for file_path in important_files:
            if os.path.exists(file_path):
                print(f"  ✅ {file_path}")
            else:
                print(f"  ❌ {file_path} (缺失)")
        
        # 檢查數據庫
        db_files = [
            'data/lottery_data.db'
        ]
        
        print(f"\n📊 數據庫文件:")
        for db_file in db_files:
            if os.path.exists(db_file):
                size = os.path.getsize(db_file) / (1024 * 1024)
                print(f"  ✅ {db_file}: {size:.2f} MB")
            else:
                print(f"  ⚠️ {db_file}: 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ Web應用測試失敗: {e}")
        return False


def main():
    """主測試函數"""
    print("="*60)
    print("Phase 3 彩票預測系統整合測試")
    print("="*60)
    
    success = True
    
    # 測試Phase 3整合
    if not test_phase3_integration():
        success = False
    
    # 測試Web應用
    if not test_web_application():
        success = False
    
    print("\n" + "="*60)
    if success:
        print("🎉 所有測試通過！Phase 3整合成功！")
        print("\n📋 下一步操作:")
        print("1. 執行 python lottery_system.py --web 啟動Web界面")
        print("2. 訪問 http://localhost:7890")
        print("3. 點擊「Phase 3」導航菜單測試新功能")
    else:
        print("❌ 部分測試失敗，請檢查錯誤信息")
    
    print("="*60)


if __name__ == "__main__":
    main()