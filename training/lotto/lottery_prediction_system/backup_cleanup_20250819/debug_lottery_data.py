#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試彩票數據
"""

import sys
import os

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import DBManager
import pandas as pd

def debug_lottery_data():
    """
    調試各種彩票的數據
    """
    try:
        # 初始化數據庫管理器
        db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'lottery_data.db')
        db_manager = DBManager(db_path)
        
        lottery_types = [
            ('powercolor', '威力彩'),
            ('lotto649', '大樂透'),
            ('dailycash', '今彩539')
        ]
        
        for lottery_type, name in lottery_types:
            print(f"\n=== {name} 數據調試 ===")
            
            # 載入數據
            df = db_manager.load_lottery_data(lottery_type=lottery_type)
            
            if df.empty:
                print(f"沒有找到{name}的數據")
                continue
            
            print(f"數據筆數: {len(df)}")
            print(f"數據列: {df.columns.tolist()}")
            
            # 檢查關鍵列
            print("\n關鍵列檢查:")
            for i in range(1, 7):
                col = f'Anumber{i}'
                if col in df.columns:
                    non_null_count = df[col].count()
                    print(f"  {col}: {non_null_count} 個非空值 (空值比例: {(len(df) - non_null_count) / len(df):.2%})")
                    if non_null_count > 0:
                        print(f"    - 數據類型: {df[col].dtype}")
                        print(f"    - 示例值: {df[col].dropna().head(3).tolist()}")
                else:
                    print(f"  {col}: 列不存在")
            
            # 檢查特殊列
            special_cols = []
            if lottery_type == 'powercolor':
                special_cols = ['Second_district']
            elif lottery_type == 'lotto649':
                special_cols = ['Special_number']
            
            for col in special_cols:
                if col in df.columns:
                    non_null_count = df[col].count()
                    print(f"  {col}: {non_null_count} 個非空值 (空值比例: {(len(df) - non_null_count) / len(df):.2%})")
                    if non_null_count > 0:
                        print(f"    - 數據類型: {df[col].dtype}")
                        print(f"    - 示例值: {df[col].dropna().head(3).tolist()}")
                else:
                    print(f"  {col}: 列不存在")
            
            # 檢查日期列
            if 'Sdate' in df.columns:
                non_null_count = df['Sdate'].count()
                print(f"  Sdate: {non_null_count} 個非空值 (空值比例: {(len(df) - non_null_count) / len(df):.2%})")
                if non_null_count > 0:
                    print(f"    - 數據類型: {df['Sdate'].dtype}")
                    print(f"    - 日期範圍: {df['Sdate'].min()} ~ {df['Sdate'].max()}")
            else:
                print("  Sdate: 列不存在")
            
            print("-" * 60)
    
    except Exception as e:
        print(f"調試失敗: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    debug_lottery_data()