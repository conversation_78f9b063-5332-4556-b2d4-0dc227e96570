#!/usr/bin/env python3
"""
驗證修復後的開獎結果顯示
"""

import sqlite3
import pandas as pd

def test_most_common_calculation():
    """測試最常出現號碼的計算邏輯"""
    print("=== 測試最常出現號碼計算邏輯 ===")
    
    try:
        # 連接資料庫
        db_path = "data/lottery_data.db"
        conn = sqlite3.connect(db_path)
        
        # 測試每個彩票類型
        test_cases = {
            "威力彩": ("Powercolor", 6),
            "大樂透": ("Lotto649", 6),
            "今彩539": ("DailyCash", 5)
        }
        
        for game_type, (table_name, num_columns) in test_cases.items():
            print(f"\n--- 測試 {game_type} ---")
            
            # 獲取最新30筆數據
            query = f"SELECT * FROM {table_name} ORDER BY CAST(Period AS INTEGER) DESC LIMIT 30"
            df = pd.read_sql_query(query, conn)
            
            if df.empty:
                print(f"❌ {game_type}: 沒有數據")
                continue
            
            print(f"✅ {game_type}: 載入 {len(df)} 筆數據")
            
            # 模擬修復後的邏輯
            numbers = []
            if game_type in ["威力彩", "大樂透"]:
                for _, row in df.iterrows():
                    numbers.extend([
                        row.get('Anumber1', 0), row.get('Anumber2', 0), 
                        row.get('Anumber3', 0), row.get('Anumber4', 0),
                        row.get('Anumber5', 0), row.get('Anumber6', 0)
                    ])
            elif game_type == "今彩539":
                for _, row in df.iterrows():
                    numbers.extend([
                        row.get('Anumber1', 0), row.get('Anumber2', 0), 
                        row.get('Anumber3', 0), row.get('Anumber4', 0),
                        row.get('Anumber5', 0)
                    ])
            
            numbers = [n for n in numbers if n > 0]
            
            if numbers:
                from collections import Counter
                counter = Counter(numbers)
                most_common = counter.most_common(3)  # 取前3名
                
                print(f"✅ 成功計算最常出現號碼:")
                for i, (num, count) in enumerate(most_common, 1):
                    print(f"  第{i}名: {num} (出現{count}次)")
                    
                # 驗證不會再顯示"計算中..."
                result = f"{most_common[0][0]} ({most_common[0][1]}次)"
                print(f"✅ 顯示結果: {result} (不再是'計算中...')")
            else:
                print(f"⚠️  {game_type}: 沒有有效號碼")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

def verify_streamlit_fix():
    """驗證Streamlit修復結果"""
    print("\n=== 驗證Streamlit修復 ===")
    
    # 檢查修復後的代碼
    print("檢查修復後的代碼...")
    
    try:
        # 檢查 integrated_web_system.py
        with open('integrated_web_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if '計算中...' in content:
            print("⚠️  integrated_web_system.py 中仍有'計算中...'文字")
            
            # 檢查是否在正確的位置
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                if '計算中' in line and 'st.metric' in line:
                    print(f"  第 {i} 行: {line.strip()}")
        else:
            print("✅ integrated_web_system.py 中已移除所有'計算中...'")
        
        # 檢查 enhanced_integrated_web_system.py
        with open('enhanced_integrated_web_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if '計算中...' in content:
            print("⚠️  enhanced_integrated_web_system.py 中仍有'計算中...'文字")
            
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                if '計算中' in line and 'st.metric' in line:
                    print(f"  第 {i} 行: {line.strip()}")
        else:
            print("✅ enhanced_integrated_web_system.py 中已移除所有'計算中...'")
            
    except FileNotFoundError as e:
        print(f"❌ 文件不存在: {e}")
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")

def show_fix_summary():
    """顯示修復摘要"""
    print("\n=== 修復摘要 ===")
    print("🔧 修復的問題:")
    print("  1. integrated_web_system.py 中大樂透和今彩539顯示'計算中...'")
    print("  2. enhanced_integrated_web_system.py 中今彩539顯示'計算中...'")
    print()
    print("✅ 修復方案:")
    print("  1. 為大樂透和今彩539添加了最常出現號碼的計算邏輯")
    print("  2. 所有彩票類型現在都會顯示實際的統計結果")
    print("  3. 移除了所有'計算中...'的硬編碼文字")
    print()
    print("📊 修復效果:")
    print("  - 威力彩: 原本正常，繼續正常顯示")
    print("  - 大樂透: 從'計算中...' → 顯示實際最常出現號碼")
    print("  - 今彩539: 從'計算中...' → 顯示實際最常出現號碼")

if __name__ == "__main__":
    test_most_common_calculation()
    verify_streamlit_fix()
    show_fix_summary()