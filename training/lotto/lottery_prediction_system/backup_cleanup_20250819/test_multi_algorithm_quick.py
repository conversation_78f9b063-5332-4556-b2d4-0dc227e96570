#!/usr/bin/env python3
"""
快速測試多算法集成預測系統
驗證核心功能運作正常
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import DBManager
from prediction.multi_algorithm_predictor import MultiAlgorithmPredictor
import logging
import numpy as np

# 設置日誌
logging.basicConfig(level=logging.WARNING)  # 減少日誌輸出
logger = logging.getLogger(__name__)

def test_multi_algorithm_quick():
    """快速測試多算法集成預測器"""
    
    print("🚀 Phase 2 多算法集成預測器快速測試")
    print("=" * 60)
    
    # 初始化數據庫管理器
    db_manager = DBManager()
    
    # 載入威力彩歷史數據
    print("📊 載入威力彩歷史數據...")
    df = db_manager.load_lottery_data('powercolor')
    
    if df.empty:
        print("❌ 無法載入歷史數據")
        return False
    
    print(f"✅ 載入完成，共 {len(df)} 筆歷史記錄")
    
    # 使用最近50期數據進行測試 (減少數據量)
    test_data = df.tail(50)
    
    # 初始化多算法預測器
    print("\n🧠 初始化多算法集成預測器...")
    predictor = MultiAlgorithmPredictor('powercolor', db_manager)
    
    # 測試核心功能
    print("\n📈 測試核心功能...")
    
    # 1. 測試基於頻率的預測
    print("  1. 基於頻率的預測...")
    try:
        freq_result = predictor.predict_with_frequency_based(test_data)
        if freq_result:
            print(f"     ✅ 預測結果: {freq_result['main_numbers']} (信心度: {freq_result['confidence']:.1f}%)")
        else:
            print("     ❌ 預測失敗")
    except Exception as e:
        print(f"     ❌ 預測失敗: {e}")
    
    # 2. 測試基於模式的預測
    print("  2. 基於模式的預測...")
    try:
        pattern_result = predictor.predict_with_pattern_based(test_data)
        if pattern_result:
            print(f"     ✅ 預測結果: {pattern_result['main_numbers']} (信心度: {pattern_result['confidence']:.1f}%)")
        else:
            print("     ❌ 預測失敗")
    except Exception as e:
        print(f"     ❌ 預測失敗: {e}")
    
    # 3. 測試優化板路分析
    print("  3. 優化板路分析...")
    try:
        optimized_result = predictor.predict_with_optimized_board_path(test_data)
        if optimized_result:
            print(f"     ✅ 預測結果: {optimized_result['main_numbers']} (信心度: {optimized_result['confidence']:.1f}%)")
        else:
            print("     ❌ 預測失敗")
    except Exception as e:
        print(f"     ❌ 預測失敗: {e}")
    
    # 4. 測試集成預測 (核心功能)
    print("  4. 集成預測...")
    try:
        ensemble_result = predictor.ensemble_predict(test_data, ensemble_size=5)
        if ensemble_result:
            print(f"     ✅ 集成預測: {ensemble_result['main_numbers']} (信心度: {ensemble_result['confidence']:.1f}%)")
            print(f"     ✅ 參與算法數: {len(ensemble_result.get('individual_predictions', []))}")
            
            # 顯示算法權重
            weights = ensemble_result.get('algorithm_weights', {})
            print("     算法權重:")
            for method, weight in weights.items():
                print(f"       {method}: {weight:.3f}")
        else:
            print("     ❌ 集成預測失敗")
    except Exception as e:
        print(f"     ❌ 集成預測失敗: {e}")
    
    return True

def test_quick_backtest():
    """快速回測"""
    
    print("\n\n📈 快速回測測試")
    print("=" * 60)
    
    # 初始化數據庫管理器
    db_manager = DBManager()
    
    # 載入威力彩歷史數據
    df = db_manager.load_lottery_data('powercolor')
    
    if df.empty:
        print("❌ 無法載入歷史數據")
        return False
    
    print(f"✅ 載入完成，共 {len(df)} 筆歷史記錄")
    
    # 初始化預測器
    predictor = MultiAlgorithmPredictor('powercolor', db_manager)
    
    # 回測最近3期
    test_periods = 3
    print(f"\n🎯 開始回測 (最近{test_periods}期)")
    print("-" * 40)
    
    results = []
    
    for i in range(test_periods):
        # 使用前N-i期數據進行預測
        train_data = df.iloc[:-i-1] if i > 0 else df.iloc[:-1]
        actual_data = df.iloc[-i-1]
        
        # 減少訓練數據量以加快速度
        train_data = train_data.tail(30)
        
        try:
            # 進行預測
            prediction = predictor.ensemble_predict(train_data, ensemble_size=3)
            
            if prediction:
                # 獲取實際結果
                actual_main = [
                    actual_data['Anumber1'], actual_data['Anumber2'], actual_data['Anumber3'],
                    actual_data['Anumber4'], actual_data['Anumber5'], actual_data['Anumber6']
                ]
                
                # 計算匹配數
                pred_main = prediction.get('main_numbers', [])
                match_count = len(set(pred_main).intersection(set(actual_main)))
                
                result = {
                    'period': actual_data['Period'],
                    'predicted_main': pred_main,
                    'actual_main': actual_main,
                    'match_count': match_count,
                    'confidence': prediction.get('confidence', 0),
                    'algorithms_used': len(prediction.get('individual_predictions', []))
                }
                
                results.append(result)
                
                print(f"期數 {actual_data['Period']}: 預測 {pred_main} | 實際 {actual_main} | 匹配 {match_count}個")
                
            else:
                print(f"期數 {actual_data['Period']}: 預測失敗")
                
        except Exception as e:
            print(f"❌ 回測期數 {actual_data['Period']} 失敗: {e}")
    
    # 分析結果
    if results:
        print("\n📊 回測結果:")
        
        match_counts = [r['match_count'] for r in results]
        confidences = [r['confidence'] for r in results]
        
        print(f"平均匹配數: {np.mean(match_counts):.2f}")
        print(f"平均信心度: {np.mean(confidences):.1f}%")
        print(f"最高匹配數: {max(match_counts)}")
        
        # 匹配分布
        print("匹配分布:")
        for i in range(max(match_counts) + 1):
            count = match_counts.count(i)
            percentage = count / len(match_counts) * 100
            print(f"  {i}個匹配: {count}次 ({percentage:.1f}%)")
    
    return True

def test_algorithm_comparison():
    """算法比較測試"""
    
    print("\n\n🏆 算法比較測試")
    print("=" * 60)
    
    # 初始化數據庫管理器
    db_manager = DBManager()
    
    # 載入威力彩歷史數據
    df = db_manager.load_lottery_data('powercolor')
    
    if df.empty:
        print("❌ 無法載入歷史數據")
        return False
    
    # 使用最近50期數據
    test_data = df.tail(50)
    
    # 初始化預測器
    predictor = MultiAlgorithmPredictor('powercolor', db_manager)
    
    print("\n📊 各算法預測比較:")
    print("-" * 40)
    
    # 測試各種算法
    algorithms = [
        ('基於頻率', predictor.predict_with_frequency_based),
        ('基於模式', predictor.predict_with_pattern_based),
        ('優化板路', predictor.predict_with_optimized_board_path),
    ]
    
    for name, method in algorithms:
        try:
            result = method(test_data)
            if result:
                print(f"{name}: {result['main_numbers']} (信心度: {result['confidence']:.1f}%)")
            else:
                print(f"{name}: 預測失敗")
        except Exception as e:
            print(f"{name}: 預測失敗 ({e})")
    
    # 集成預測
    print("\n🚀 集成預測:")
    try:
        ensemble_result = predictor.ensemble_predict(test_data, ensemble_size=3)
        if ensemble_result:
            print(f"集成結果: {ensemble_result['main_numbers']} (信心度: {ensemble_result['confidence']:.1f}%)")
            print(f"參與算法數: {len(ensemble_result.get('individual_predictions', []))}")
        else:
            print("集成預測失敗")
    except Exception as e:
        print(f"集成預測失敗: {e}")
    
    return True

def main():
    """主測試函數"""
    
    print("🚀 Phase 2 多算法集成預測系統快速測試")
    print("=" * 70)
    
    # 測試序列
    test_results = []
    
    # 1. 多算法快速測試
    print("\n1. 多算法快速測試...")
    test_results.append(test_multi_algorithm_quick())
    
    # 2. 快速回測
    print("\n2. 快速回測...")
    test_results.append(test_quick_backtest())
    
    # 3. 算法比較
    print("\n3. 算法比較...")
    test_results.append(test_algorithm_comparison())
    
    # 總結
    print("\n\n🎯 測試總結")
    print("=" * 70)
    
    test_names = [
        "多算法快速測試",
        "快速回測",
        "算法比較"
    ]
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"總測試數: {total_tests}")
    print(f"通過測試: {passed_tests}")
    print(f"測試通過率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n各項測試結果:")
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {i+1}. {name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有測試通過！多算法集成預測系統運行正常")
        print("📋 主要特點:")
        print("  • 多算法集成投票機制")
        print("  • 動態權重調整")
        print("  • 基於頻率、模式、板路分析的預測")
        print("  • 綜合信心度評估")
    else:
        print(f"\n⚠️  {total_tests - passed_tests} 項測試失敗，需要檢查")
    
    print("=" * 70)

if __name__ == "__main__":
    main()