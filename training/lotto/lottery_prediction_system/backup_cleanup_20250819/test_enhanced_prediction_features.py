#!/usr/bin/env python3
"""
增强版预测功能测试与演示
展示多组预测和准确性跟踪的核心功能
"""

import os
import sys
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from prediction.enhanced_prediction_system import EnhancedPredictionSystem
from prediction.multi_group_predictor import MultiGroupPredictor
from prediction.accuracy_tracker import AccuracyTracker

def create_test_data(periods=100):
    """创建测试数据"""
    print("📊 创建测试数据...")
    
    # 威力彩测试数据
    data = {
        'Period': [f'114{str(i).zfill(6)}' for i in range(periods)],
        'Date': [(datetime.now() - timedelta(days=periods-i)).strftime('%Y-%m-%d') for i in range(periods)],
        'Anumber1': np.random.randint(1, 39, periods),
        'Anumber2': np.random.randint(1, 39, periods),
        'Anumber3': np.random.randint(1, 39, periods),
        'Anumber4': np.random.randint(1, 39, periods),
        'Anumber5': np.random.randint(1, 39, periods),
        'Anumber6': np.random.randint(1, 39, periods),
        'Snumber': np.random.randint(1, 9, periods)
    }
    
    df = pd.DataFrame(data)
    
    # 确保每行的号码不重复
    for idx, row in df.iterrows():
        numbers = [row[f'Anumber{i}'] for i in range(1, 7)]
        unique_numbers = list(set(numbers))
        
        # 如果有重复，重新生成
        while len(unique_numbers) < 6:
            new_number = np.random.randint(1, 39)
            if new_number not in unique_numbers:
                unique_numbers.append(new_number)
        
        # 更新数据
        for i, num in enumerate(unique_numbers[:6], 1):
            df.at[idx, f'Anumber{i}'] = num
    
    print(f"✅ 测试数据创建完成 - 共{periods}期数据")
    return df

def test_single_prediction(prediction_system, df):
    """测试单组预测"""
    print("\n" + "="*60)
    print("🎯 测试单组预测功能")
    print("="*60)
    
    result = prediction_system.generate_comprehensive_prediction(
        df, 
        strategy='balanced',
        enable_multi_group=False,
        next_draw_date='2025-08-19'
    )
    
    if result:
        prediction_data = result['prediction_data']
        print(f"✅ 单组预测成功!")
        print(f"   预测号码: {prediction_data.get('main_numbers', 'N/A')}")
        print(f"   特别号: {prediction_data.get('special_number', 'N/A')}")
        print(f"   信心度: {prediction_data.get('confidence', 0):.1f}%")
        print(f"   预测方法: {prediction_data.get('method', 'N/A')}")
        
        # 显示建议
        print(f"\n💡 系统建议:")
        for rec in result['recommendations'][:3]:
            print(f"   • {rec}")
        
        return result
    else:
        print("❌ 单组预测失败")
        return None

def test_multi_group_prediction(prediction_system, df):
    """测试多组预测"""
    print("\n" + "="*60)
    print("🎯 测试多组预测功能")
    print("="*60)
    
    # 测试不同策略
    strategies = ['conservative', 'balanced', 'aggressive']
    
    for strategy in strategies:
        print(f"\n📊 测试策略: {strategy}")
        print("-" * 40)
        
        result = prediction_system.generate_comprehensive_prediction(
            df, 
            strategy=strategy,
            enable_multi_group=True,
            next_draw_date='2025-08-19'
        )
        
        if result and 'groups' in result['prediction_data']:
            groups = result['prediction_data']['groups']
            print(f"✅ {strategy} 策略成功生成 {len(groups)} 组预测:")
            
            for i, group in enumerate(groups[:3], 1):  # 显示前3组
                print(f"   第{i}组: {group['main_numbers']} "
                      f"(信心度: {group['confidence']:.1f}%, "
                      f"权重: {group.get('recommendation_weight', 0):.1%})")
            
            # 显示汇总信息
            summary = result['prediction_data'].get('summary', {})
            if summary:
                coverage = summary.get('number_coverage', {})
                print(f"   号码覆盖率: {coverage.get('coverage_percentage', 0):.1f}%")
                print(f"   平均信心度: {summary.get('confidence_range', {}).get('average', 0):.1f}%")
        else:
            print(f"❌ {strategy} 策略预测失败")
    
    return result

def test_accuracy_tracking(prediction_system):
    """测试准确性跟踪"""
    print("\n" + "="*60)
    print("🎯 测试准确性跟踪功能")
    print("="*60)
    
    # 模拟一些历史预测记录
    tracker = prediction_system.accuracy_tracker
    
    # 添加测试预测记录
    test_predictions = [
        {
            'main_numbers': [5, 12, 18, 25, 33, 36],
            'special_number': 3,
            'confidence': 75.5,
            'method': 'enhanced_multi_algorithm'
        },
        {
            'main_numbers': [8, 15, 22, 28, 31, 37],
            'special_number': 7,
            'confidence': 68.2,
            'method': 'neural_network'
        },
        {
            'main_numbers': [3, 11, 19, 26, 29, 35],
            'special_number': 4,
            'confidence': 82.1,
            'method': 'trend_analysis'
        }
    ]
    
    # 记录预测
    prediction_ids = []
    for i, pred in enumerate(test_predictions):
        pred_id = tracker.record_prediction(
            pred, 
            'powercolor', 
            f'2025-08-{15+i}',
            'balanced'
        )
        prediction_ids.append(pred_id)
        print(f"📝 记录预测 {i+1}: {pred['main_numbers']}")
    
    # 模拟验证结果
    actual_results = [
        ([3, 12, 18, 22, 33, 38], 5),  # 3个匹配
        ([8, 14, 22, 27, 31, 39], 7),  # 4个匹配
        ([2, 11, 19, 24, 29, 36], 4)   # 4个匹配
    ]
    
    print(f"\n🔍 验证预测结果:")
    for i, (actual_numbers, actual_special) in enumerate(actual_results):
        verification = tracker.verify_predictions(
            f'2025-08-{15+i}',
            actual_numbers,
            actual_special,
            'powercolor'
        )
        
        print(f"   日期 2025-08-{15+i}: 验证 {verification['verified_count']} 个预测")
        if verification['best_prediction']:
            best = verification['best_prediction']
            print(f"   最佳预测: {best['matches']} 个匹配 (分数: {best['score']:.1f})")
    
    # 获取性能报告
    print(f"\n📊 性能报告:")
    report = tracker.get_overall_performance_report('powercolor', 30)
    stats = report['overall_statistics']
    print(f"   总预测数: {stats['total_predictions']}")
    print(f"   成功预测数: {stats['successful_predictions']}")
    print(f"   成功率: {stats['success_rate']:.1f}%")
    print(f"   平均匹配数: {stats['average_matches']:.2f}")
    
    return report

def test_prediction_analysis(prediction_system, df):
    """测试预测分析功能"""
    print("\n" + "="*60)
    print("🎯 测试预测分析功能")
    print("="*60)
    
    # 生成带分析的预测
    result = prediction_system.generate_comprehensive_prediction(
        df, 
        strategy='balanced',
        enable_multi_group=True,
        next_draw_date='2025-08-19'
    )
    
    if not result:
        print("❌ 预测分析测试失败")
        return
    
    prediction_data = result['prediction_data']
    
    # 显示历史分析
    historical = prediction_data.get('historical_analysis', {})
    if historical:
        print(f"📈 历史分析:")
        if historical.get('hot_numbers'):
            print(f"   热门号码: {historical['hot_numbers'][:8]}")
        if historical.get('cold_numbers'):
            print(f"   冷门号码: {historical['cold_numbers'][:8]}")
        
        consecutive = historical.get('consecutive_trend', {})
        if consecutive:
            print(f"   平均连续数: {consecutive.get('avg_consecutive', 0):.2f}")
        
        sum_trend = historical.get('sum_trend', {})
        if sum_trend:
            print(f"   平均和值: {sum_trend.get('avg_sum', 0):.1f}")
    
    # 显示风险评估
    risk = prediction_data.get('risk_assessment', {})
    if risk:
        print(f"\n⚠️ 风险评估:")
        print(f"   整体风险: {risk.get('overall_risk', 'N/A')}")
        print(f"   风险分数: {risk.get('risk_score', 0):.1f}")
        risk_factors = risk.get('risk_factors', [])
        if risk_factors:
            print(f"   风险因素: {', '.join(risk_factors)}")
    
    # 显示期望分析
    expectation = prediction_data.get('expectation_analysis', {})
    if expectation:
        print(f"\n📊 期望分析:")
        print(f"   成功概率: {expectation.get('expected_success_probability', 0):.1%}")
        print(f"   风险收益比: {expectation.get('risk_return_ratio', 0):.2f}")
        
        allocation = expectation.get('optimal_allocation', {})
        if allocation:
            print(f"   建议策略: {allocation.get('strategy', 'N/A')}")
            print(f"   资金分配: {allocation.get('allocation', 'N/A')}")

def test_system_optimization(prediction_system):
    """测试系统优化功能"""
    print("\n" + "="*60)
    print("🎯 测试系统优化功能")
    print("="*60)
    
    # 获取当前算法权重
    current_weights = prediction_system.enhanced_predictor.algorithm_weights.copy()
    print(f"📊 当前算法权重:")
    for algorithm, weight in current_weights.items():
        print(f"   {algorithm}: {weight:.3f}")
    
    # 获取系统信息
    system_info = prediction_system.get_system_info()
    print(f"\n⚙️ 系统信息:")
    print(f"   彩票类型: {system_info['lottery_type']}")
    print(f"   自动优化: {system_info['system_config']['auto_optimization']}")
    print(f"   跟踪状态: {system_info['system_config']['tracking_enabled']}")
    
    # 更新系统配置
    new_config = {
        'auto_optimization': True,
        'min_confidence_threshold': 65,
        'max_groups': 6
    }
    prediction_system.update_system_config(new_config)
    print(f"\n✅ 系统配置已更新: {new_config}")

def generate_comprehensive_report(prediction_system):
    """生成综合报告"""
    print("\n" + "="*60)
    print("📋 生成综合系统报告")
    print("="*60)
    
    report = prediction_system.generate_system_report(30)
    print(report)

def main():
    """主测试函数"""
    print("🚀 增强版预测系统功能测试")
    print("="*80)
    
    # 1. 初始化系统
    print("🔧 初始化增强版预测系统...")
    prediction_system = EnhancedPredictionSystem('powercolor')
    
    # 2. 创建测试数据
    df = create_test_data(200)
    
    # 3. 测试单组预测
    single_result = test_single_prediction(prediction_system, df)
    
    # 4. 测试多组预测
    multi_result = test_multi_group_prediction(prediction_system, df)
    
    # 5. 测试准确性跟踪
    tracking_report = test_accuracy_tracking(prediction_system)
    
    # 6. 测试预测分析
    test_prediction_analysis(prediction_system, df)
    
    # 7. 测试系统优化
    test_system_optimization(prediction_system)
    
    # 8. 生成综合报告
    generate_comprehensive_report(prediction_system)
    
    print("\n" + "="*80)
    print("🎉 所有功能测试完成！")
    print("="*80)
    
    # 9. 总结主要改进
    print("\n💡 主要功能亮点:")
    print("   ✅ 多组预测 - 显著提升命中概率")
    print("   ✅ 准确性跟踪 - 实时监控预测效果")
    print("   ✅ 智能优化 - 动态调整算法权重")
    print("   ✅ 风险评估 - 全面的风险分析")
    print("   ✅ 综合分析 - 多维度预测洞察")
    print("   ✅ 策略建议 - 个性化投注建议")
    
    print("\n🎯 使用建议:")
    print("   1. 对于稳健投注者，推荐使用 'conservative' 策略")
    print("   2. 对于平衡型投注者，推荐使用 'balanced' 策略")
    print("   3. 对于激进型投注者，推荐使用 'aggressive' 策略")
    print("   4. 建议根据风险评估调整投注金额")
    print("   5. 定期查看准确性报告，了解系统表现")

if __name__ == "__main__":
    main()