#!/usr/bin/env python3
"""
Phase 3 準確度評估引擎測試
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
import time
import json
from datetime import datetime, timedelta

from data.db_manager import DBManager
from prediction.enhanced_multi_algorithm_predictor import EnhancedMultiAlgorithmPredictor
from phase3.accuracy_assessment_engine import (
    AccuracyAssessmentEngine, AccuracyTracker, ModelOptimizer, 
    AdaptiveWeightManager, OptimizationStrategy
)

import logging
logging.basicConfig(level=logging.WARNING)

def test_accuracy_tracker():
    """測試準確度追蹤器"""
    print("🎯 測試準確度追蹤器")
    print("-" * 50)
    
    try:
        tracker = AccuracyTracker(db_path="data/test_accuracy.db")
        
        # 模擬預測結果
        from phase3.accuracy_assessment_engine import PredictionResult
        
        for i in range(5):
            result = PredictionResult(
                prediction_id=f"test_pred_{i}",
                lottery_type='powercolor',
                period=f'11300{50+i:02d}',
                predicted_numbers=[1, 7, 12, 23, 28, 35],
                predicted_special=3,
                confidence=85.0 + i * 2,
                algorithm_weights={'frequency_based': 0.3, 'pattern_based': 0.4, 'trend_analysis': 0.3},
                prediction_timestamp=datetime.now() - timedelta(hours=i)
            )
            
            success = tracker.record_prediction(result)
            print(f"  預測記錄 {i+1}: {'✅' if success else '❌'}")
            
            # 模擬評估
            actual_numbers = [1, 15, 22, 28, 31, 38]  # 2個匹配
            evaluation_success = tracker.evaluate_prediction(
                result.prediction_id, actual_numbers, 5
            )
            print(f"  預測評估 {i+1}: {'✅' if evaluation_success else '❌'}")
        
        # 計算準確度指標
        print("\n📊 計算準確度指標...")
        metrics = tracker.calculate_accuracy_metrics('powercolor')
        
        if metrics:
            print(f"  平均匹配數: {metrics.get('average_match_count', 0):.2f}")
            print(f"  平均信心度: {metrics.get('average_confidence', 0):.1f}%")
            print(f"  成功率(≥2): {metrics.get('success_rate_2plus', 0):.1%}")
            print(f"  一致性分數: {metrics.get('accuracy_consistency', 0):.3f}")
        else:
            print("  ❌ 無法計算指標")
        
        # 獲取算法性能
        print("\n🧠 算法性能分析...")
        performance = tracker.get_algorithm_performance('powercolor')
        
        for algo_name, perf in performance.items():
            print(f"  {algo_name}:")
            print(f"    成功率: {perf.success_rate:.1%}")
            print(f"    穩定性: {perf.stability_score:.3f}")
            print(f"    平均信心度: {perf.average_confidence:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 準確度追蹤器測試失敗: {e}")
        return False

def test_model_optimizer():
    """測試模型優化器"""
    print("\n🔧 測試模型優化器")
    print("-" * 50)
    
    try:
        tracker = AccuracyTracker(db_path="data/test_accuracy.db")
        optimizer = ModelOptimizer(tracker)
        
        # 測試性能趨勢分析
        print("📈 性能趨勢分析...")
        trends = optimizer.analyze_performance_trends('powercolor')
        
        if trends:
            print(f"  算法排名: {trends.get('algorithm_ranking', [])}")
            print(f"  表現不佳: {trends.get('underperforming_algorithms', [])}")
            print(f"  穩定算法: {trends.get('stable_algorithms', [])}")
        
        # 測試權重優化
        print("\n⚖️ 權重優化測試...")
        current_weights = {
            'frequency_based': 0.25,
            'pattern_based': 0.35,
            'trend_analysis': 0.25,
            'neural_network': 0.15
        }
        
        for strategy in [OptimizationStrategy.CONSERVATIVE, OptimizationStrategy.BALANCED, OptimizationStrategy.AGGRESSIVE]:
            try:
                optimal_weights = optimizer.calculate_optimal_weights('powercolor', strategy)
                print(f"  {strategy.value} 策略最優權重: {optimal_weights}")
                
                if optimal_weights:
                    optimization_result = optimizer.optimize_weights('powercolor', current_weights, strategy)
                    print(f"    性能改進預期: {optimization_result.performance_improvement:.3f}")
                    print(f"    驗證分數: {optimization_result.validation_score:.3f}")
                    
            except Exception as e:
                print(f"    ❌ {strategy.value} 策略優化失敗: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型優化器測試失敗: {e}")
        return False

def test_adaptive_weight_manager():
    """測試自適應權重管理器"""
    print("\n🤖 測試自適應權重管理器")
    print("-" * 50)
    
    try:
        tracker = AccuracyTracker(db_path="data/test_accuracy.db")
        optimizer = ModelOptimizer(tracker)
        weight_manager = AdaptiveWeightManager(optimizer, tracker)
        
        # 模擬性能數據
        performance_data = {
            'average_accuracy_score': 1.8,
            'accuracy_consistency': 0.65,
            'confidence_accuracy_correlation': 0.25
        }
        
        # 測試優化需求判斷
        print("🔍 優化需求判斷...")
        should_optimize, reason = weight_manager.should_optimize('powercolor', performance_data)
        print(f"  需要優化: {should_optimize}")
        print(f"  原因: {reason}")
        
        # 測試策略選擇
        print("\n📋 策略選擇測試...")
        strategy = weight_manager._select_optimization_strategy('powercolor', performance_data)
        print(f"  推薦策略: {strategy.value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 自適應權重管理器測試失敗: {e}")
        return False

async def test_accuracy_assessment_engine():
    """測試準確度評估引擎"""
    print("\n🚀 測試準確度評估引擎")
    print("-" * 50)
    
    try:
        # 初始化組件
        db_manager = DBManager()
        assessment_engine = AccuracyAssessmentEngine(db_manager)
        
        print("🧠 初始化預測器...")
        predictor = EnhancedMultiAlgorithmPredictor('powercolor', db_manager)
        assessment_engine.register_predictor('powercolor', predictor)
        
        # 載入測試數據
        print("📊 載入測試數據...")
        df = db_manager.load_lottery_data('powercolor')
        if df.empty:
            print("❌ 無法載入歷史數據")
            return False
        
        test_data = df.tail(50)
        
        # 模擬預測和評估循環
        print("🔄 模擬預測和評估循環...")
        
        prediction_ids = []
        
        # 生成多個預測
        for i in range(3):
            try:
                # 手動初始化核心預測器
                predictor.predictors['frequency_based'] = predictor._init_robust_frequency_predictor(test_data)
                predictor.predictors['pattern_based'] = predictor._init_enhanced_pattern_predictor(test_data)
                predictor.predictors['trend_analysis'] = predictor._init_trend_analysis_predictor(test_data)
                
                prediction = predictor.enhanced_ensemble_predict(test_data, ensemble_size=5)
                
                if prediction:
                    # 添加期號信息
                    prediction['period'] = f'11300{60+i:02d}'
                    
                    # 記錄預測
                    prediction_id = assessment_engine.record_prediction_result('powercolor', prediction)
                    prediction_ids.append(prediction_id)
                    
                    print(f"  預測 {i+1}: {prediction['main_numbers']} (ID: {prediction_id[:8]}...)")
                else:
                    print(f"  預測 {i+1}: 失敗")
                    
            except Exception as e:
                print(f"  預測 {i+1} 錯誤: {e}")
        
        # 模擬評估
        print("\n📝 模擬評估過程...")
        
        for i, prediction_id in enumerate(prediction_ids):
            try:
                # 使用歷史數據作為實際結果
                actual_data = df.iloc[-i-1]
                actual_result = {
                    'Anumber1': actual_data['Anumber1'],
                    'Anumber2': actual_data['Anumber2'],
                    'Anumber3': actual_data['Anumber3'],
                    'Anumber4': actual_data['Anumber4'],
                    'Anumber5': actual_data['Anumber5'],
                    'Anumber6': actual_data['Anumber6'],
                    'Bnumber': actual_data['Bnumber']
                }
                
                success = assessment_engine.evaluate_prediction_accuracy(prediction_id, actual_result)
                actual_numbers = [actual_result[f'Anumber{j}'] for j in range(1, 7)]
                print(f"  評估 {i+1}: 實際 {actual_numbers} ({'✅' if success else '❌'})")
                
            except Exception as e:
                print(f"  評估 {i+1} 錯誤: {e}")
        
        # 等待處理
        await asyncio.sleep(1)
        
        # 測試自動權重調整
        print("\n⚖️ 測試自動權重調整...")
        
        # 獲取當前權重
        original_weights = predictor.algorithm_weights.copy()
        print(f"  原始權重: {original_weights}")
        
        # 執行權重調整
        adjustment_success = assessment_engine.weight_manager.auto_adjust_weights('powercolor', predictor)
        print(f"  權重調整: {'✅' if adjustment_success else '❌'}")
        
        if adjustment_success:
            new_weights = predictor.algorithm_weights
            print(f"  新權重: {new_weights}")
            
            # 顯示變化
            for algo_name, new_weight in new_weights.items():
                old_weight = original_weights.get(algo_name, 0)
                change = new_weight - old_weight
                if abs(change) > 0.01:
                    print(f"    {algo_name}: {old_weight:.3f} → {new_weight:.3f} ({change:+.3f})")
        
        # 生成綜合報告
        print("\n📊 生成綜合報告...")
        report = assessment_engine.get_comprehensive_report('powercolor')
        
        if 'error' not in report:
            metrics = report.get('accuracy_metrics', {})
            print(f"  平均匹配數: {metrics.get('average_match_count', 0):.2f}")
            print(f"  成功率(≥2): {metrics.get('success_rate_2plus', 0):.1%}")
            print(f"  一致性分數: {metrics.get('accuracy_consistency', 0):.3f}")
            print(f"  總預測數: {metrics.get('total_predictions', 0)}")
            
            print("\n💡 系統建議:")
            for rec in report.get('recommendations', []):
                print(f"    • {rec}")
        else:
            print(f"  ❌ 報告生成失敗: {report['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 準確度評估引擎測試失敗: {e}")
        return False

async def test_integration():
    """集成測試"""
    print("\n🔗 集成測試")
    print("-" * 50)
    
    try:
        # 初始化組件
        db_manager = DBManager()
        assessment_engine = AccuracyAssessmentEngine(db_manager)
        
        # 註冊多個預測器
        lottery_types = ['powercolor', 'lotto649']
        
        for lottery_type in lottery_types:
            predictor = EnhancedMultiAlgorithmPredictor(lottery_type, db_manager)
            assessment_engine.register_predictor(lottery_type, predictor)
            print(f"  已註冊 {lottery_type} 預測器")
        
        # 測試持續評估 (短時間)
        print("\n⏱️ 測試持續評估...")
        
        # 啟動持續評估
        assessment_engine.start_continuous_assessment()
        print("  持續評估已啟動")
        
        # 等待一段時間
        await asyncio.sleep(3)
        
        # 停止持續評估
        assessment_engine.stop_continuous_assessment()
        print("  持續評估已停止")
        
        # 檢查狀態
        status = assessment_engine.assessment_status
        print(f"  評估完成次數: {status['assessments_completed']}")
        print(f"  優化執行次數: {status['optimizations_performed']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成測試失敗: {e}")
        return False

async def main():
    """主測試函數"""
    print("🚀 Phase 3 準確度評估引擎測試")
    print("=" * 70)
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # 測試序列
    test_results = []
    
    # 1. 準確度追蹤器測試
    print("\n1. 準確度追蹤器測試...")
    test_results.append(test_accuracy_tracker())
    
    # 2. 模型優化器測試
    print("\n2. 模型優化器測試...")
    test_results.append(test_model_optimizer())
    
    # 3. 自適應權重管理器測試
    print("\n3. 自適應權重管理器測試...")
    test_results.append(test_adaptive_weight_manager())
    
    # 4. 準確度評估引擎測試
    print("\n4. 準確度評估引擎測試...")
    test_results.append(await test_accuracy_assessment_engine())
    
    # 5. 集成測試
    print("\n5. 集成測試...")
    test_results.append(await test_integration())
    
    # 總結
    print("\n\n🎯 Phase 3 準確度評估引擎測試總結")
    print("=" * 70)
    
    test_names = [
        "準確度追蹤器測試",
        "模型優化器測試",
        "自適應權重管理器測試",
        "準確度評估引擎測試",
        "集成測試"
    ]
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"總測試數: {total_tests}")
    print(f"通過測試: {passed_tests}")
    print(f"測試通過率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n各項測試結果:")
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {i+1}. {name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有測試通過！Phase 3 準確度評估引擎運行正常")
        print("📋 Phase 3.2 主要功能:")
        print("  ✅ 實時準確度追蹤和評估")
        print("  ✅ 自動模型優化和權重調整") 
        print("  ✅ 多策略優化算法")
        print("  ✅ 自適應權重管理")
        print("  ✅ 綜合性能分析報告")
        print("  ✅ 持續評估和監控")
        
        print("\n💡 核心特性:")
        print("  • 自動檢測性能下降並觸發優化")
        print("  • 支持多種優化策略 (保守/平衡/激進/自適應)")
        print("  • 實時算法性能追蹤和趨勢分析")
        print("  • 智能權重調整，防止過度優化")
        print("  • 完整的預測評估生命週期管理")
    else:
        print(f"\n⚠️  {total_tests - passed_tests} 項測試失敗，需要進一步優化")
    
    print("=" * 70)

if __name__ == "__main__":
    asyncio.run(main())