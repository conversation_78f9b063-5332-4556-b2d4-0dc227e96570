#!/usr/bin/env python3
"""
測試API響應格式修復
"""

import requests
import json
import sys

def test_api_response():
    """測試API響應格式"""
    try:
        # 測試API端點
        url = 'http://localhost:5000/api/latest_predictions/powercolor?limit=3'
        
        print("正在測試API響應格式...")
        print(f"URL: {url}")
        
        response = requests.get(url, timeout=10)
        
        print(f"HTTP狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("\n原始API響應:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 檢查響應格式
            if 'success' in data:
                print(f"\n✅ 成功字段: {data['success']}")
                
                if data['success'] and 'data' in data:
                    if 'predictions' in data['data']:
                        predictions = data['data']['predictions']
                        print(f"✅ 預測數據: {len(predictions)} 筆記錄")
                        
                        if predictions:
                            print("\n第一筆預測數據結構:")
                            print(json.dumps(predictions[0], indent=2, ensure_ascii=False))
                    else:
                        print("❌ 缺少 predictions 字段")
                else:
                    print("❌ 缺少 data 字段或API失敗")
            else:
                print("❌ 缺少 success 字段")
                
        else:
            print(f"❌ HTTP錯誤: {response.status_code}")
            print(response.text)
            
    except requests.exceptions.ConnectionError:
        print("❌ 無法連接到服務器 (http://localhost:5000)")
        print("請確認Web服務器是否正在運行")
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

if __name__ == "__main__":
    test_api_response()