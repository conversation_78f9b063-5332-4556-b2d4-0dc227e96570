#!/usr/bin/env python3
"""
Phase 3.6 生產部署系統簡化測試
專注於核心功能驗證
"""

import unittest
import tempfile
from pathlib import Path

class TestProductionDeployment(unittest.TestCase):
    """生產部署系統簡化測試"""
    
    def setUp(self):
        """測試初始化"""
        self.test_dir = Path(tempfile.mkdtemp())
    
    def tearDown(self):
        """測試清理"""
        import shutil
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
    
    def test_deployment_script_exists(self):
        """測試部署腳本存在"""
        from deploy import DeploymentScript
        script = DeploymentScript()
        self.assertIsNotNone(script)
    
    def test_production_system_exists(self):
        """測試生產系統模塊存在"""
        try:
            from phase3.production_deployment_system import (
                SystemMonitor, HealthChecker, LogManager,
                BackupManager, DeploymentManager, DeploymentConfig,
                DeploymentMode
            )
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"導入生產系統模塊失敗: {e}")
    
    def test_docker_files_exist(self):
        """測試Docker文件存在"""
        docker_files = [
            "Dockerfile",
            "docker-compose.yml",
            "docker/nginx.conf",
            "docker/prometheus.yml"
        ]
        
        for file_path in docker_files:
            full_path = Path(file_path)
            self.assertTrue(full_path.exists(), f"Docker文件不存在: {file_path}")
    
    def test_deployment_config_creation(self):
        """測試部署配置創建"""
        from phase3.production_deployment_system import DeploymentConfig, DeploymentMode
        
        config = DeploymentConfig(
            mode=DeploymentMode.DEVELOPMENT,
            host="localhost",
            port=8000,
            workers=2,
            max_requests=1000,
            timeout=30,
            ssl_enabled=False,
            ssl_cert_path=None,
            ssl_key_path=None,
            database_url="sqlite:///test.db",
            redis_url="redis://localhost:6379",
            log_level="INFO",
            backup_enabled=True,
            monitoring_enabled=True
        )
        
        self.assertEqual(config.mode, DeploymentMode.DEVELOPMENT)
        self.assertEqual(config.host, "localhost")
        self.assertEqual(config.port, 8000)
    
    def test_system_monitor_creation(self):
        """測試系統監控器創建"""
        from phase3.production_deployment_system import (
            SystemMonitor, DeploymentConfig, DeploymentMode
        )
        
        config = DeploymentConfig(
            mode=DeploymentMode.DEVELOPMENT,
            host="localhost",
            port=8000,
            workers=2,
            max_requests=1000,
            timeout=30,
            ssl_enabled=False,
            ssl_cert_path=None,
            ssl_key_path=None,
            database_url="sqlite:///test.db",
            redis_url="redis://localhost:6379",
            log_level="INFO",
            backup_enabled=True,
            monitoring_enabled=True
        )
        
        monitor = SystemMonitor(config)
        self.assertIsNotNone(monitor)
        self.assertEqual(monitor.config, config)
    
    def test_deployment_manager_creation(self):
        """測試部署管理器創建"""
        from phase3.production_deployment_system import (
            DeploymentManager, DeploymentConfig, DeploymentMode
        )
        
        config = DeploymentConfig(
            mode=DeploymentMode.DEVELOPMENT,
            host="localhost",
            port=8000,
            workers=2,
            max_requests=1000,
            timeout=30,
            ssl_enabled=False,
            ssl_cert_path=None,
            ssl_key_path=None,
            database_url="sqlite:///test.db",
            redis_url="redis://localhost:6379",
            log_level="INFO",
            backup_enabled=True,
            monitoring_enabled=True
        )
        
        manager = DeploymentManager(config)
        self.assertIsNotNone(manager)
        self.assertIsNotNone(manager.system_monitor)
        self.assertIsNotNone(manager.health_checker)
        self.assertIsNotNone(manager.log_manager)
        self.assertIsNotNone(manager.backup_manager)
    
    def test_deployment_script_config_creation(self):
        """測試部署腳本配置創建"""
        from deploy import DeploymentScript
        
        script = DeploymentScript()
        script.project_root = self.test_dir
        script.config_file = self.test_dir / "test_config.yaml"
        
        config = script.create_default_config()
        
        # 檢查配置結構
        self.assertIn('deployment', config)
        self.assertIn('services', config)
        self.assertIn('database', config)
        self.assertIn('security', config)
        
        # 檢查配置值
        self.assertEqual(config['deployment']['environment'], 'production')
        self.assertEqual(config['services']['api']['port'], 8000)

def run_simple_tests():
    """運行簡化測試"""
    print("🧪 開始運行 Phase 3.6 生產部署系統簡化測試...")
    print("=" * 60)
    
    # 創建測試套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestProductionDeployment)
    
    # 運行測試
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 生成測試報告
    print("\n" + "=" * 60)
    print(f"📊 測試結果:")
    print(f"   總測試數: {result.testsRun}")
    print(f"   成功數: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   失敗數: {len(result.failures)}")
    print(f"   錯誤數: {len(result.errors)}")
    
    if result.failures:
        print(f"\n❌ 失敗的測試:")
        for test, traceback in result.failures:
            print(f"   - {test}")
    
    if result.errors:
        print(f"\n❌ 錯誤的測試:")
        for test, traceback in result.errors:
            print(f"   - {test}")
    
    success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100) if result.testsRun > 0 else 0
    print(f"\n🎯 成功率: {success_rate:.1f}%")
    
    if result.wasSuccessful():
        print("✅ 所有核心功能測試通過！")
    else:
        print("⚠️ 部分測試未通過，但核心功能基本可用。")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_simple_tests()
    exit(0 if success else 1)