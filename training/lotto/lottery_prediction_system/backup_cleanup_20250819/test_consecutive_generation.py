#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試連續號碼生成條件
分析在什麼情況下系統會預測出123456這樣的連續號碼
"""

import pandas as pd
import numpy as np
from prediction.board_path_engine import BoardPathEngine
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_data_with_consecutive_patterns():
    """創建包含連續模式的測試數據"""
    
    # 創建一個包含連續號碼模式的歷史數據
    test_data = []
    
    # 情況1: 歷史中經常出現連續號碼
    consecutive_patterns = [
        [1, 2, 3, 15, 20, 25],  # 包含1,2,3連續
        [2, 3, 4, 16, 21, 26],  # 包含2,3,4連續
        [3, 4, 5, 17, 22, 27],  # 包含3,4,5連續
        [4, 5, 6, 18, 23, 28],  # 包含4,5,6連續
        [5, 6, 7, 19, 24, 29],  # 包含5,6,7連續
    ]
    
    # 添加連續模式數據
    for i, pattern in enumerate(consecutive_patterns):
        test_data.append({
            'Period': f'11400{i+1:04d}',
            'Anumber1': pattern[0],
            'Anumber2': pattern[1], 
            'Anumber3': pattern[2],
            'Anumber4': pattern[3],
            'Anumber5': pattern[4],
            'Anumber6': pattern[5],
            'Bnumber': 1
        })
    
    # 情況2: 最近一期包含1,2,3,4,5的部分連續
    test_data.append({
        'Period': '114000006',
        'Anumber1': 1,
        'Anumber2': 2,
        'Anumber3': 3,
        'Anumber4': 4,
        'Anumber5': 5,
        'Anumber6': 20,
        'Bnumber': 2
    })
    
    return pd.DataFrame(test_data)

def create_extreme_consecutive_data():
    """創建極端連續模式的測試數據"""
    
    test_data = []
    
    # 創建一個歷史中多次出現完全連續號碼的情況
    extreme_patterns = [
        [1, 2, 3, 4, 5, 6],    # 完全連續
        [2, 3, 4, 5, 6, 7],    # 完全連續
        [10, 11, 12, 13, 14, 15], # 完全連續
        [20, 21, 22, 23, 24, 25], # 完全連續
    ]
    
    # 添加極端連續模式
    for i, pattern in enumerate(extreme_patterns):
        test_data.append({
            'Period': f'11400{i+1:04d}',
            'Anumber1': pattern[0],
            'Anumber2': pattern[1],
            'Anumber3': pattern[2], 
            'Anumber4': pattern[3],
            'Anumber5': pattern[4],
            'Anumber6': pattern[5],
            'Bnumber': 1
        })
    
    # 最近一期：包含1,2,3,4,5，缺少6
    test_data.append({
        'Period': '114000005',
        'Anumber1': 1,
        'Anumber2': 2,
        'Anumber3': 3,
        'Anumber4': 4,
        'Anumber5': 5,
        'Anumber6': 30,
        'Bnumber': 2
    })
    
    return pd.DataFrame(test_data)

def test_consecutive_prediction_conditions():
    """測試連續號碼預測的條件"""
    
    print("\n" + "="*60)
    print("🔍 測試連續號碼生成條件")
    print("="*60)
    
    # 初始化預測器
    try:
        board_engine = BoardPathEngine(db_manager=None, lottery_type='powercolor')
    except Exception as e:
        print(f"初始化失敗: {e}")
        return
    
    # 測試情況1: 包含連續模式的歷史數據
    print("\n📊 測試情況1: 歷史中包含連續模式")
    print("-" * 40)
    
    test_df1 = create_test_data_with_consecutive_patterns()
    print(f"測試數據包含 {len(test_df1)} 期歷史")
    print("最近幾期的號碼:")
    for _, row in test_df1.tail(3).iterrows():
        numbers = [row[f'Anumber{i}'] for i in range(1, 7)]
        print(f"  期數 {row['Period']}: {numbers}")
    
    # 使用板路分析預測
    try:
        result1 = board_engine.predict(test_df1)
        if result1 and result1.candidates:
            best_candidate = result1.candidates[0]
            predicted_numbers = best_candidate['main_numbers']
            confidence = best_candidate['confidence']
            explanation = best_candidate.get('explanation', [])
            
            print(f"\n🎯 預測結果: {predicted_numbers}")
            print(f"📈 信心分數: {confidence:.3f}")
            print("💡 預測解釋:")
            for exp in explanation:
                print(f"  - {exp}")
            
            # 檢查是否為連續號碼
            is_consecutive = check_consecutive_pattern(predicted_numbers)
            print(f"\n🔗 是否為連續號碼: {'是' if is_consecutive else '否'}")
        else:
            print("❌ 預測失敗")
    except Exception as e:
        print(f"❌ 預測出錯: {e}")
    
    # 測試情況2: 極端連續模式
    print("\n\n📊 測試情況2: 極端連續模式歷史")
    print("-" * 40)
    
    test_df2 = create_extreme_consecutive_data()
    print(f"測試數據包含 {len(test_df2)} 期歷史")
    print("歷史號碼模式:")
    for _, row in test_df2.iterrows():
        numbers = [row[f'Anumber{i}'] for i in range(1, 7)]
        print(f"  期數 {row['Period']}: {numbers}")
    
    try:
        result2 = board_engine.predict(test_df2)
        if result2 and result2.candidates:
            best_candidate = result2.candidates[0]
            predicted_numbers = best_candidate['main_numbers']
            confidence = best_candidate['confidence']
            explanation = best_candidate.get('explanation', [])
            
            print(f"\n🎯 預測結果: {predicted_numbers}")
            print(f"📈 信心分數: {confidence:.3f}")
            print("💡 預測解釋:")
            for exp in explanation:
                print(f"  - {exp}")
            
            # 檢查是否為連續號碼
            is_consecutive = check_consecutive_pattern(predicted_numbers)
            print(f"\n🔗 是否為連續號碼: {'是' if is_consecutive else '否'}")
            
            # 特別檢查是否為123456
            if predicted_numbers == [1, 2, 3, 4, 5, 6]:
                print("🎉 成功預測出123456!")
        else:
            print("❌ 預測失敗")
    except Exception as e:
        print(f"❌ 預測出錯: {e}")

def check_consecutive_pattern(numbers):
    """檢查號碼是否為連續模式"""
    if len(numbers) < 2:
        return False
    
    sorted_nums = sorted(numbers)
    
    # 檢查是否為完全連續
    for i in range(1, len(sorted_nums)):
        if sorted_nums[i] != sorted_nums[i-1] + 1:
            return False
    
    return True

def analyze_consecutive_generation_mechanism():
    """分析連續號碼生成機制"""
    
    print("\n" + "="*60)
    print("🔬 分析連續號碼生成機制")
    print("="*60)
    
    print("\n📋 連續號碼生成的可能條件:")
    print("1. 歷史數據中存在大量連續號碼模式")
    print("2. 板路分析發現強烈的連續關聯模式")
    print("3. 最近期數據顯示連續趨勢")
    print("4. 特殊號碼組合觸發連續模式")
    
    print("\n🎯 系統預測123456的條件:")
    print("- 歷史中1→2, 2→3, 3→4, 4→5, 5→6的關聯模式強烈")
    print("- 最近一期包含1,2,3,4,5的部分連續")
    print("- 板路分析權重計算傾向於連續模式")
    print("- 機器學習模型從訓練數據中學習到連續模式")
    
    print("\n⚠️  現實中的問題:")
    print("- 真實彩票中連續號碼極其罕見")
    print("- 系統可能過度學習了測試數據的模式")
    print("- 需要增加隨機性和現實性約束")
    
    print("\n🔧 改進建議:")
    print("1. 增加連續號碼的懲罰機制")
    print("2. 限制完全連續組合的生成概率")
    print("3. 加入現實性檢查和約束")
    print("4. 平衡歷史模式學習和隨機性")

def main():
    """主函數"""
    print("🎲 連續號碼生成條件測試")
    print("分析系統在什麼條件下會預測出123456這樣的連續號碼")
    
    # 測試連續號碼預測條件
    test_consecutive_prediction_conditions()
    
    # 分析生成機制
    analyze_consecutive_generation_mechanism()
    
    print("\n" + "="*60)
    print("✅ 測試完成")
    print("="*60)

if __name__ == "__main__":
    main()