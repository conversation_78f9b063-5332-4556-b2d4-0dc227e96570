#!/usr/bin/env python3
"""
測試批次回測功能
驗證真實期數格式的處理
"""

import sys
import os
from batch_backtest import BatchBacktester, BacktestConfig, GameType, PredictionMethod, DatabaseManager


def test_database_connection():
    """測試資料庫連接和數據獲取"""
    print("🔌 測試資料庫連接...")
    
    db_manager = DatabaseManager()
    
    # 測試獲取威力彩數據
    data = db_manager.get_historical_data(
        GameType.POWERCOLOR, 
        start_period="114000050", 
        end_period="114000058"
    )
    
    if data:
        print(f"✅ 成功獲取 {len(data)} 筆威力彩數據")
        print("📊 數據範例:")
        for i, record in enumerate(data[:3]):
            period = record.get('Period', 'Unknown')
            numbers = f"{record.get('Anumber1', '?')},{record.get('Anumber2', '?')},{record.get('Anumber3', '?')},{record.get('Anumber4', '?')},{record.get('Anumber5', '?')},{record.get('Anumber6', '?')}+{record.get('Second_district', '?')}"
            print(f"   期數 {period}: {numbers}")
        return True
    else:
        print("❌ 無法獲取數據")
        return False


def test_period_format():
    """測試期數格式處理"""
    print("\n🔢 測試期數格式處理...")
    
    # 測試期數比較邏輯
    periods = ["114000050", "114000051", "114000052", "114000058"]
    
    print("期數排序測試:")
    for period in periods:
        print(f"   {period}")
    
    # 測試數值比較
    start = "114000050"
    end = "114000058"
    test_period = "114000055"
    
    if int(start) <= int(test_period) <= int(end):
        print(f"✅ 期數 {test_period} 在範圍 {start}-{end} 內")
    else:
        print(f"❌ 期數 {test_period} 不在範圍 {start}-{end} 內")
    
    return True


def test_simple_backtest():
    """測試簡單回測"""
    print("\n⚡ 執行簡單回測測試...")
    
    try:
        config = BacktestConfig(
            game_type=GameType.POWERCOLOR,
            method=PredictionMethod.FREQUENCY_ANALYSIS,
            start_period="114000010",  # 從第10期開始
            end_period="114000058",    # 到第58期
            training_window=5          # 使用較小的訓練窗口
        )
        
        backtester = BatchBacktester(config)
        results = backtester.run_backtest()
        
        if "error" in results:
            print(f"❌ 回測失敗: {results['error']}")
            return False
        
        stats = results.get('statistics', {})
        print(f"✅ 回測成功!")
        print(f"   總期數: {stats.get('total_periods', 0)}")
        print(f"   平均命中: {stats.get('average_matches_per_period', 0):.2f}")
        print(f"   準確率: {stats.get('accuracy', 0):.2f}%")
        
        # 顯示詳細結果
        detailed_results = results.get('results', [])
        if detailed_results:
            print("\n📋 詳細結果:")
            for result in detailed_results[:3]:  # 只顯示前3個
                period = result['period']
                predicted = result['predicted']
                actual = result['actual']
                matches = result['matches']
                print(f"   期數 {period}: 預測 {predicted}, 實際 {actual}, 命中 {matches}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False


def test_all_methods():
    """測試所有預測方法"""
    print("\n🎯 測試所有預測方法...")
    
    methods = [
        PredictionMethod.FREQUENCY_ANALYSIS,
        PredictionMethod.PATTERN_ANALYSIS,
        PredictionMethod.CONSECUTIVE_ANALYSIS,
        PredictionMethod.RANDOM_BASELINE
    ]
    
    method_names = {
        PredictionMethod.FREQUENCY_ANALYSIS: "頻率分析",
        PredictionMethod.PATTERN_ANALYSIS: "模式分析",
        PredictionMethod.CONSECUTIVE_ANALYSIS: "連號分析",
        PredictionMethod.RANDOM_BASELINE: "隨機基準"
    }
    
    results_summary = []
    
    for method in methods:
        print(f"\n>>> 測試 {method_names[method]}...")
        
        try:
            config = BacktestConfig(
                game_type=GameType.POWERCOLOR,
                method=method,
                start_period="114000010",
                end_period="114000058", 
                training_window=3
            )
            
            backtester = BatchBacktester(config)
            results = backtester.run_backtest()
            
            if "error" not in results:
                stats = results['statistics']
                results_summary.append({
                    'method': method_names[method],
                    'periods': stats['total_periods'],
                    'accuracy': stats['accuracy'],
                    'avg_matches': stats['average_matches_per_period']
                })
                print(f"   ✅ 成功 - 準確率: {stats['accuracy']:.2f}%")
            else:
                print(f"   ❌ 失敗: {results['error']}")
                
        except Exception as e:
            print(f"   ❌ 異常: {e}")
    
    # 比較結果
    if results_summary:
        print(f"\n📊 方法比較結果:")
        print(f"{'方法':<12} {'期數':<6} {'準確率':<10} {'平均命中':<10}")
        print("-" * 45)
        
        for result in results_summary:
            print(f"{result['method']:<12} {result['periods']:<6} {result['accuracy']:<10.2f} {result['avg_matches']:<10.2f}")
    
    return len(results_summary) > 0


def main():
    """主測試函數"""
    print("=" * 60)
    print(" 批次回測功能測試 ".center(60, "="))
    print("=" * 60)
    
    tests = [
        ("資料庫連接測試", test_database_connection),
        ("期數格式測試", test_period_format),
        ("簡單回測測試", test_simple_backtest),
        ("所有方法測試", test_all_methods)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通過")
            else:
                print(f"❌ {test_name} 失敗")
        except Exception as e:
            print(f"❌ {test_name} 異常: {e}")
    
    # 總結
    print(f"\n{'='*60}")
    print(f" 測試結果總結 ".center(60, "="))
    print(f"{'='*60}")
    print(f"通過: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有測試都通過！批次回測功能運作正常。")
        return True
    else:
        print("⚠️ 部分測試失敗，請檢查上述錯誤訊息。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)