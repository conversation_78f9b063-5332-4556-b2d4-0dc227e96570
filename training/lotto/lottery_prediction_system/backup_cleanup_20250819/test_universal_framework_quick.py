#!/usr/bin/env python3
"""
Phase 3 通用預測框架快速測試
專注核心功能，跳過耗時的神經網絡訓練
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
import time
import json
from datetime import datetime, timedelta

from data.db_manager import DBManager
from phase3.accuracy_assessment_engine import AccuracyAssessmentEngine
from phase3.universal_prediction_framework import (
    UniversalPredictor, LotteryConfigManager, StrategyFactory,
    CrossLotteryAnalyzer, LotteryType, LotteryConfig, UniversalPredictionResult
)

import logging
logging.basicConfig(level=logging.WARNING)

def test_config_and_analyzer():
    """測試配置管理和分析器"""
    print("🔧 測試配置管理和分析器")
    print("-" * 50)
    
    try:
        # 測試配置管理器
        print("📋 配置管理器:")
        config_manager = LotteryConfigManager()
        all_configs = config_manager.get_all_configs()
        
        print(f"  支持的彩票類型: {len(all_configs)}")
        for lottery_type, config in all_configs.items():
            print(f"    {config.name}: {config.main_numbers_count}+{config.special_numbers_count}號碼")
        
        # 測試跨彩票分析器
        print("\n🔗 跨彩票分析器:")
        db_manager = DBManager()
        analyzer = CrossLotteryAnalyzer(db_manager)
        
        lottery_types = ['powercolor', 'lotto649']
        correlations = analyzer.analyze_correlations(lottery_types, analysis_window=30)
        
        if correlations:
            print("  相關性分析:")
            for type1, type1_corr in correlations.items():
                for type2, corr_value in type1_corr.items():
                    if type1 != type2:
                        print(f"    {type1} ↔ {type2}: {corr_value:.3f}")
        
        # 綜合分析
        analysis = analyzer.generate_cross_lottery_analysis(lottery_types)
        print(f"  推薦分數: {analysis.recommendation_score:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

async def test_universal_predictor_core():
    """測試通用預測器核心功能"""
    print("\n🎯 測試通用預測器核心功能")
    print("-" * 50)
    
    try:
        # 初始化
        db_manager = DBManager()
        assessment_engine = AccuracyAssessmentEngine(db_manager)
        universal_predictor = UniversalPredictor(db_manager, assessment_engine)
        
        # 檢查初始化
        print("🔧 初始化檢查:")
        supported_lotteries = universal_predictor.get_supported_lotteries()
        print(f"  支持的彩票類型: {len(supported_lotteries)}")
        
        for lottery_type, config in supported_lotteries.items():
            print(f"    ✅ {config.name} ({lottery_type})")
        
        # 測試快速預測 (使用較小的ensemble_size)
        print("\n🎲 快速預測測試:")
        
        for lottery_type in list(supported_lotteries.keys())[:2]:  # 只測試前2個
            try:
                print(f"\n  {lottery_type} 預測:")
                
                # 執行快速預測
                result = universal_predictor.predict(
                    lottery_type,
                    strategy="frequency_based",  # 使用快速策略
                    use_cross_learning=False,     # 跳過跨學習
                    data_window=30                # 減少數據窗口
                )
                
                if result:
                    print(f"    ✅ 主號碼: {result.main_numbers}")
                    if result.special_numbers:
                        print(f"    特別號: {result.special_numbers}")
                    print(f"    信心度: {result.confidence:.1f}%")
                    print(f"    策略: {result.strategy_used}")
                    
                    # 驗證結果格式
                    config = supported_lotteries[lottery_type]
                    valid_count = len(result.main_numbers) == config.main_numbers_count
                    print(f"    號碼數量: {'✅' if valid_count else '❌'}")
                else:
                    print(f"    ❌ 預測失敗")
                    
            except Exception as e:
                print(f"    ❌ 預測錯誤: {e}")
        
        # 測試批量預測
        print(f"\n📊 批量預測測試:")
        batch_results = universal_predictor.batch_predict(
            list(supported_lotteries.keys()),
            strategy="frequency_based",
            use_cross_learning=False,
            data_window=20
        )
        
        success_count = 0
        for lottery_type, result in batch_results.items():
            if result:
                print(f"  ✅ {lottery_type}: {result.main_numbers[:3]}... (信心度: {result.confidence:.1f}%)")
                success_count += 1
            else:
                print(f"  ❌ {lottery_type}: 失敗")
        
        print(f"\n批量預測成功率: {success_count}/{len(batch_results)} ({success_count/len(batch_results)*100:.1f}%)")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 通用預測器測試失敗: {e}")
        return False

def test_cross_learning_basic():
    """測試基礎跨彩票學習"""
    print("\n🧠 測試基礎跨彩票學習")
    print("-" * 50)
    
    try:
        # 初始化
        db_manager = DBManager()
        assessment_engine = AccuracyAssessmentEngine(db_manager)
        universal_predictor = UniversalPredictor(db_manager, assessment_engine)
        
        supported_lotteries = universal_predictor.get_supported_lotteries()
        lottery_types = list(supported_lotteries.keys())
        
        if len(lottery_types) < 2:
            print("❌ 需要至少2種彩票類型")
            return False
        
        # 跨彩票分析
        print("📊 跨彩票分析:")
        analysis = universal_predictor.get_cross_lottery_analysis(lottery_types[:2])
        print(f"  推薦分數: {analysis.recommendation_score:.3f}")
        
        # 相似度分析
        target_lottery = lottery_types[0]
        related_lotteries = universal_predictor._find_related_lotteries(target_lottery)
        print(f"  {target_lottery} 相關彩票: {related_lotteries}")
        
        # 測試預測建議
        print(f"\n💡 預測建議 ({target_lottery}):")
        recommendations = universal_predictor.get_prediction_recommendations(target_lottery)
        
        if recommendations.get('recommended_strategies'):
            print("  推薦策略:")
            for strategy_rec in recommendations['recommended_strategies'][:3]:
                print(f"    {strategy_rec['strategy']}: {strategy_rec['confidence']:.1f}%")
        
        if recommendations.get('cross_learning_opportunities'):
            print("  跨學習機會:")
            for opp in recommendations['cross_learning_opportunities'][:2]:
                status = "✅" if opp['recommended'] else "❌"
                print(f"    {status} {opp['related_lottery']}: {opp['similarity_score']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 跨彩票學習測試失敗: {e}")
        return False

def test_result_format_and_integration():
    """測試結果格式和系統整合"""
    print("\n📋 測試結果格式和系統整合")
    print("-" * 50)
    
    try:
        # 初始化
        db_manager = DBManager()
        assessment_engine = AccuracyAssessmentEngine(db_manager)
        universal_predictor = UniversalPredictor(db_manager, assessment_engine)
        
        supported_lotteries = universal_predictor.get_supported_lotteries()
        
        if not supported_lotteries:
            print("❌ 無支持的彩票類型")
            return False
        
        lottery_type = list(supported_lotteries.keys())[0]
        
        # 執行預測
        print(f"🎯 測試 {lottery_type} 結果格式:")
        result = universal_predictor.predict(
            lottery_type,
            strategy="frequency_based",
            use_cross_learning=False,
            data_window=20
        )
        
        if result:
            # 測試結果結構
            print(f"  預測ID: {result.prediction_id[:20]}...")
            print(f"  彩票類型: {result.lottery_type}")
            print(f"  使用策略: {result.strategy_used}")
            print(f"  主號碼: {result.main_numbers}")
            print(f"  特別號: {result.special_numbers}")
            print(f"  信心度: {result.confidence}")
            print(f"  預測時間: {result.prediction_timestamp.strftime('%H:%M:%S')}")
            
            # 測試轉換為字典
            result_dict = result.to_dict()
            print(f"  字典轉換: {'✅' if result_dict else '❌'}")
            
            # 檢查必要字段
            required_fields = ['lottery_type', 'main_numbers', 'confidence', 'prediction_timestamp']
            has_required = all(field in result_dict for field in required_fields)
            print(f"  必要字段: {'✅' if has_required else '❌'}")
            
            # 測試JSON序列化
            try:
                json_str = json.dumps(result_dict, default=str)
                print(f"  JSON序列化: ✅ ({len(json_str)} 字節)")
            except Exception as e:
                print(f"  JSON序列化: ❌ {e}")
            
            # 檢查系統整合
            print(f"\n🔗 系統整合檢查:")
            print(f"  預測器數量: {len(universal_predictor.predictors)}")
            print(f"  評估引擎預測器: {len(assessment_engine.predictors)}")
            
            # 檢查是否註冊一致
            registered_match = set(universal_predictor.predictors.keys()) == set(assessment_engine.predictors.keys())
            print(f"  註冊一致性: {'✅' if registered_match else '❌'}")
            
            return True
        else:
            print("❌ 預測失敗")
            return False
        
    except Exception as e:
        print(f"❌ 結果格式測試失敗: {e}")
        return False

async def main():
    """主測試函數"""
    print("🚀 Phase 3 通用預測框架快速測試")
    print("=" * 70)
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # 測試序列
    test_results = []
    
    # 1. 配置管理和分析器測試
    print("\n1. 配置管理和分析器測試...")
    test_results.append(test_config_and_analyzer())
    
    # 2. 通用預測器核心功能測試
    print("\n2. 通用預測器核心功能測試...")
    test_results.append(await test_universal_predictor_core())
    
    # 3. 基礎跨彩票學習測試
    print("\n3. 基礎跨彩票學習測試...")
    test_results.append(test_cross_learning_basic())
    
    # 4. 結果格式和系統整合測試
    print("\n4. 結果格式和系統整合測試...")
    test_results.append(test_result_format_and_integration())
    
    # 總結
    print("\n\n🎯 Phase 3 通用預測框架測試總結")
    print("=" * 70)
    
    test_names = [
        "配置管理和分析器測試",
        "通用預測器核心功能測試",
        "基礎跨彩票學習測試",
        "結果格式和系統整合測試"
    ]
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"總測試數: {total_tests}")
    print(f"通過測試: {passed_tests}")
    print(f"測試通過率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n各項測試結果:")
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {i+1}. {name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有測試通過！Phase 3 通用預測框架運行正常")
        print("📋 Phase 3.3 主要功能:")
        print("  ✅ 跨彩票類型統一預測接口")
        print("  ✅ 可擴展的彩票配置管理")
        print("  ✅ 智能跨彩票分析和學習")
        print("  ✅ 通用預測結果結構")
        print("  ✅ 批量預測和建議系統")
        print("  ✅ 完整的系統整合支持")
        
        print("\n💡 核心特性:")
        print("  • 支持威力彩、大樂透、今彩539等多種彩票")
        print("  • 智能跨彩票相關性分析和學習")
        print("  • 統一的預測接口和結果格式")
        print("  • 可擴展的預測策略框架")
        print("  • 自動化的配置管理和驗證")
        print("  • 完整的預測建議和優化系統")
    else:
        print(f"\n⚠️  {total_tests - passed_tests} 項測試失敗，需要進一步優化")
    
    print("=" * 70)

if __name__ == "__main__":
    asyncio.run(main())