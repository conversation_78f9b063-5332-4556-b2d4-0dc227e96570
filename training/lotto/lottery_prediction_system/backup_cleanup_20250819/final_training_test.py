#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的訓練測試腳本
測試三種彩票類型在不同訓練天數下的效果
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
from prediction.lottery_predictor import LotteryPredictor
from data.feature_engineering import FeatureEngineer

def decode_binary_number(binary_data):
    """解碼二進制數據為整數"""
    if isinstance(binary_data, bytes):
        # 從二進制數據中提取整數（小端序）
        return int.from_bytes(binary_data[:4], byteorder='little')
    elif isinstance(binary_data, (int, float)):
        return int(binary_data)
    else:
        try:
            return int(binary_data)
        except:
            return 0

def get_training_data(lottery_type, start_date, end_date):
    """獲取訓練資料並處理二進制數據"""
    try:
        db_path = 'lottery_data.db'
        conn = sqlite3.connect(db_path)
        
        if lottery_type == 'powercolor':
            query = """
            SELECT Sdate, An<PERSON>ber1, <PERSON><PERSON>ber2, <PERSON><PERSON>ber3, <PERSON><PERSON>ber4, <PERSON><PERSON>ber5, <PERSON><PERSON><PERSON>6, Second_district
            FROM powercolor 
            WHERE Sdate >= ? AND Sdate <= ?
            ORDER BY Sdate
            """
        elif lottery_type == 'lotto649':
            query = """
            SELECT Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, SpecialNumber
            FROM lotto649 
            WHERE Sdate >= ? AND Sdate <= ?
            ORDER BY Sdate
            """
        elif lottery_type == 'dailycash':
            query = """
            SELECT Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5
            FROM dailycash 
            WHERE Sdate >= ? AND Sdate <= ?
            ORDER BY Sdate
            """
        else:
            return None
            
        training_data = pd.read_sql_query(query, conn, params=(start_date, end_date))
        conn.close()
        
        if training_data.empty:
            return None
            
        # 添加Period欄位
        training_data['Period'] = range(1, len(training_data) + 1)
        
        # 轉換Sdate為datetime格式
        training_data['Sdate'] = pd.to_datetime(training_data['Sdate'])
        
        # 處理二進制數據
        number_columns = ['Anumber1', 'Anumber2', 'Anumber3', 'Anumber4', 'Anumber5']
        if lottery_type in ['powercolor', 'lotto649']:
            number_columns.append('Anumber6')
        if lottery_type == 'powercolor':
            number_columns.append('Second_district')
        elif lottery_type == 'lotto649':
            number_columns.append('SpecialNumber')
            
        for col in number_columns:
            if col in training_data.columns:
                training_data[col] = training_data[col].apply(decode_binary_number)
                
        return training_data
        
    except Exception as e:
        print(f"獲取訓練資料時發生錯誤: {e}")
        return None

def get_actual_result(lottery_type, target_date):
    """獲取實際開獎結果"""
    try:
        db_path = 'lottery_data.db'
        conn = sqlite3.connect(db_path)
        
        if lottery_type == 'powercolor':
            query = """
            SELECT Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, Second_district
            FROM powercolor 
            WHERE Sdate = ?
            """
        elif lottery_type == 'lotto649':
            query = """
            SELECT Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, SpecialNumber
            FROM lotto649 
            WHERE Sdate = ?
            """
        elif lottery_type == 'dailycash':
            query = """
            SELECT Anumber1, Anumber2, Anumber3, Anumber4, Anumber5
            FROM dailycash 
            WHERE Sdate = ?
            """
        else:
            return None, None
            
        cursor = conn.execute(query, (target_date,))
        result = cursor.fetchone()
        conn.close()
        
        if result:
            # 處理二進制數據
            decoded_result = [decode_binary_number(num) for num in result]
            
            if lottery_type == 'dailycash':
                main_numbers = [str(num) for num in decoded_result[:5]]
                special_number = None
            else:
                main_numbers = [str(num) for num in decoded_result[:6]]
                special_number = str(decoded_result[6]) if decoded_result[6] else None
            return main_numbers, special_number
        else:
            return None, None
            
    except Exception as e:
        print(f"獲取實際結果時發生錯誤: {e}")
        return None, None

def calculate_accuracy(prediction, actual_main, actual_special, lottery_type):
    """計算預測準確度"""
    try:
        # 提取預測號碼
        if isinstance(prediction, dict):
            if lottery_type == 'powercolor':
                pred_main = prediction.get('第一區', prediction.get('numbers', []))
                pred_special = prediction.get('第二區', 0)
            elif lottery_type == 'lotto649':
                pred_main = prediction.get('第一區', prediction.get('numbers', []))
                pred_special = prediction.get('特別號', 0)
            elif lottery_type == 'dailycash':
                pred_main = prediction.get('numbers', [])
                pred_special = None
            else:
                pred_main = prediction.get('numbers', [])
                pred_special = 0
        else:
            pred_main = []
            pred_special = 0
            
        # 確保預測號碼為字串列表
        if pred_main:
            pred_main = [str(num) for num in pred_main]
        if pred_special is not None:
            pred_special = str(pred_special)
            
        # 計算主號碼匹配數
        main_matches = 0
        if actual_main and pred_main:
            main_matches = len(set(actual_main) & set(pred_main))
            
        # 計算特別號匹配
        special_match = 0
        if lottery_type != 'dailycash' and actual_special and pred_special:
            special_match = 1 if actual_special == pred_special else 0
            
        # 計算總體準確度
        if lottery_type == 'dailycash':
            total_possible = 5
            accuracy = main_matches / total_possible
        else:
            total_possible = 7  # 6個主號碼 + 1個特別號
            accuracy = (main_matches + special_match) / total_possible
            
        return {
            'main_matches': main_matches,
            'special_match': special_match,
            'accuracy': accuracy,
            'predicted_main': pred_main,
            'predicted_special': pred_special,
            'actual_main': actual_main,
            'actual_special': actual_special
        }
        
    except Exception as e:
        print(f"計算準確度時發生錯誤: {e}")
        return {
            'main_matches': 0,
            'special_match': 0,
            'accuracy': 0.0,
            'predicted_main': [],
            'predicted_special': None,
            'actual_main': actual_main,
            'actual_special': actual_special,
            'error': str(e)
        }

def test_lottery_training_periods(lottery_type, target_date='2025-06-20', training_periods=[30, 60, 90, 120]):
    """測試單一彩票類型在不同訓練期間的效果"""
    print(f"\n開始測試 {lottery_type}...")
    print(f"目標預測日期: {target_date}")
    
    # 獲取實際結果
    actual_main, actual_special = get_actual_result(lottery_type, target_date)
    if not actual_main:
        print(f"無法獲取 {target_date} 的實際開獎結果")
        return None
        
    print(f"實際開獎結果 - 主號碼: {actual_main}, 特別號: {actual_special}")
    
    results = {}
    
    for days in training_periods:
        print(f"\n測試 {days} 天訓練資料...")
        
        try:
            # 計算訓練資料的開始日期
            target_dt = datetime.strptime(target_date, '%Y-%m-%d')
            start_date = (target_dt - timedelta(days=days)).strftime('%Y-%m-%d')
            end_date = (target_dt - timedelta(days=1)).strftime('%Y-%m-%d')
            
            print(f"訓練資料期間: {start_date} 到 {end_date}")
            
            # 獲取訓練資料
            training_data = get_training_data(lottery_type, start_date, end_date)
            
            if training_data is None or len(training_data) < 10:
                print(f"訓練資料不足 ({len(training_data) if training_data is not None else 0} 筆)")
                results[f"{days}_days"] = {
                    'status': 'failed',
                    'reason': f'訓練資料不足 ({len(training_data) if training_data is not None else 0} 筆)',
                    'training_records': len(training_data) if training_data is not None else 0
                }
                continue
                
            print(f"獲得 {len(training_data)} 筆訓練資料")
            
            # 特徵工程
            feature_eng = FeatureEngineer()
            features_df = feature_eng.create_basic_features(training_data, lottery_type)
            
            if features_df.empty:
                print("特徵工程失敗")
                results[f"{days}_days"] = {
                    'status': 'failed',
                    'reason': '特徵工程失敗',
                    'training_records': len(training_data)
                }
                continue
                
            print(f"特徵工程成功，創建 {len(features_df)} 筆特徵")
            
            # 獲取最新特徵
            latest_features = features_df.iloc[-1:]
            
            # 初始化預測器並進行預測
            predictor = LotteryPredictor()
            prediction = predictor.predict(
                df=training_data,
                features=latest_features,
                lottery_type=lottery_type,
                prediction_methods=['enhanced']  # 只使用enhanced方法，因為ML模型特徵不匹配
            )
            
            if prediction:
                print(f"預測成功: {prediction}")
                
                # 計算準確度
                accuracy_result = calculate_accuracy(prediction, actual_main, actual_special, lottery_type)
                
                results[f"{days}_days"] = {
                    'status': 'success',
                    'prediction': prediction,
                    'training_records': len(training_data),
                    'features_count': len(features_df),
                    'accuracy': accuracy_result
                }
                
                print(f"準確度分析: 主號碼匹配 {accuracy_result['main_matches']} 個, 特別號匹配 {accuracy_result['special_match']} 個, 總準確度 {accuracy_result['accuracy']:.3f}")
            else:
                print("預測失敗")
                results[f"{days}_days"] = {
                    'status': 'failed',
                    'reason': '預測失敗',
                    'training_records': len(training_data)
                }
                
        except Exception as e:
            print(f"測試 {days} 天訓練時發生錯誤: {e}")
            results[f"{days}_days"] = {
                'status': 'failed',
                'reason': str(e),
                'training_records': 0
            }
    
    return results

def main():
    """主函數"""
    target_date = '2025-06-18'
    training_periods = [30, 60, 90, 120, 180]
    lottery_types = ['powercolor', 'lotto649', 'dailycash']
    
    all_results = {
        'test_date': datetime.now().isoformat(),
        'target_date': target_date,
        'training_periods': training_periods,
        'results': {}
    }
    
    for lottery_type in lottery_types:
        print(f"\n{'='*60}")
        print(f"測試 {lottery_type.upper()}")
        print(f"{'='*60}")
        
        results = test_lottery_training_periods(lottery_type, target_date, training_periods)
        if results:
            all_results['results'][lottery_type] = results
        else:
            all_results['results'][lottery_type] = {'error': '無法獲取實際開獎結果'}
    
    # 保存結果
    os.makedirs('reports', exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f'reports/final_training_test_{timestamp}.json'
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n詳細結果已保存到: {report_file}")
    
    # 打印摘要報告
    print("\n" + "="*60)
    print("摘要報告")
    print("="*60)
    
    for lottery_type, results in all_results['results'].items():
        print(f"\n{lottery_type.upper()}:")
        if 'error' in results:
            print(f"  {results['error']}")
        else:
            best_accuracy = 0
            best_period = None
            
            for period, result in results.items():
                if result['status'] == 'success':
                    accuracy = result['accuracy']['accuracy']
                    main_matches = result['accuracy']['main_matches']
                    special_match = result['accuracy']['special_match']
                    print(f"  {period}: 準確度 {accuracy:.3f} (主號碼匹配 {main_matches} 個, 特別號匹配 {special_match} 個)")
                    
                    if accuracy > best_accuracy:
                        best_accuracy = accuracy
                        best_period = period
                else:
                    print(f"  {period}: 失敗 - {result['reason']}")
            
            if best_period:
                print(f"  最佳訓練期間: {best_period} (準確度 {best_accuracy:.3f})")
            else:
                print(f"  所有測試均失敗")

if __name__ == "__main__":
    main()