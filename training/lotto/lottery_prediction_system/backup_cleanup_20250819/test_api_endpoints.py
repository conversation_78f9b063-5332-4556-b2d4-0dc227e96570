#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API端点全面测试脚本
测试报告中提到的25+个API端点功能
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:7890"

def test_api_endpoint(endpoint, method='GET', data=None, description=""):
    """测试API端点"""
    try:
        full_url = f"{BASE_URL}{endpoint}"
        
        if method == 'GET':
            response = requests.get(full_url, timeout=10)
        elif method == 'POST':
            response = requests.post(full_url, json=data, timeout=10)
        
        status = "✅" if response.status_code == 200 else "❌"
        result = {
            'endpoint': endpoint,
            'method': method,
            'status_code': response.status_code,
            'success': response.status_code == 200,
            'description': description,
            'response_size': len(response.text) if response.text else 0
        }
        
        # 解析JSON响应
        try:
            json_data = response.json()
            result['has_data'] = 'data' in json_data
            result['api_success'] = json_data.get('success', False)
        except:
            result['has_data'] = False
            result['api_success'] = False
            
        print(f"{status} {endpoint} ({response.status_code}) - {description}")
        return result
    except Exception as e:
        print(f"❌ {endpoint} - 错误: {e}")
        return {
            'endpoint': endpoint,
            'method': method,
            'success': False,
            'error': str(e),
            'description': description
        }

def main():
    """主测试函数"""
    print("🔍 彩票预测系统 - API端点全面测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试服务器: {BASE_URL}")
    print("=" * 60)
    
    # 定义测试端点
    test_endpoints = [
        # 基础功能API
        ("/api/system_status", "GET", None, "系统状态"),
        ("/api/dashboard_data", "GET", None, "仪表板数据"),
        
        # 预测相关API - 威力彩
        ("/api/latest_predictions/powercolor", "GET", None, "威力彩最新预测"),
        ("/api/accuracy/powercolor", "GET", None, "威力彩准确度统计"),
        ("/api/comprehensive_analysis/powercolor", "GET", None, "威力彩综合分析"),
        ("/api/number_combination_analysis/powercolor", "GET", None, "威力彩号码组合分析"),
        
        # 预测相关API - 大乐透
        ("/api/latest_predictions/lotto649", "GET", None, "大乐透最新预测"),
        ("/api/accuracy/lotto649", "GET", None, "大乐透准确度统计"),
        ("/api/comprehensive_analysis/lotto649", "GET", None, "大乐透综合分析"),
        ("/api/number_combination_analysis/lotto649", "GET", None, "大乐透号码组合分析"),
        
        # 预测相关API - 今彩539
        ("/api/latest_predictions/dailycash", "GET", None, "今彩539最新预测"),
        ("/api/accuracy/dailycash", "GET", None, "今彩539准确度统计"),
        ("/api/comprehensive_analysis/dailycash", "GET", None, "今彩539综合分析"),
        ("/api/number_combination_analysis/dailycash", "GET", None, "今彩539号码组合分析"),
        
        # 开奖结果API
        ("/api/results?lottery_type=powercolor&limit=5", "GET", None, "威力彩开奖结果"),
        ("/api/results?lottery_type=lotto649&limit=5", "GET", None, "大乐透开奖结果"),
        ("/api/results?lottery_type=dailycash&limit=5", "GET", None, "今彩539开奖结果"),
        
        # 预测生成API (POST)
        ("/api/predict", "POST", {"lottery_type": "powercolor", "candidates_count": 3}, "生成威力彩预测"),
        ("/api/predict", "POST", {"lottery_type": "lotto649", "candidates_count": 3}, "生成大乐透预测"),
        ("/api/predict", "POST", {"lottery_type": "dailycash", "candidates_count": 3}, "生成今彩539预测"),
        
        # 预测验证API (如果有prediction_id的话，这里用示例ID)
        ("/api/verify_prediction/1", "GET", None, "验证预测结果"),
        
        # 其他API
        ("/", "GET", None, "主页"),
        ("/predictions", "GET", None, "预测页面"),
        ("/results", "GET", None, "开奖结果页面"),
        ("/analysis", "GET", None, "分析页面"),
        ("/dashboard", "GET", None, "仪表板页面"),
    ]
    
    # 执行测试
    results = []
    success_count = 0
    total_count = len(test_endpoints)
    
    print(f"\n📊 开始测试 {total_count} 个端点...\n")
    
    for endpoint, method, data, description in test_endpoints:
        result = test_api_endpoint(endpoint, method, data, description)
        results.append(result)
        if result.get('success', False):
            success_count += 1
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📋 测试结果摘要")
    print("=" * 60)
    print(f"总测试端点: {total_count}")
    print(f"成功端点: {success_count}")
    print(f"失败端点: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    print(f"\n🟢 成功的端点 ({success_count}):")
    for result in results:
        if result.get('success', False):
            print(f"  ✅ {result['endpoint']} - {result['description']}")
    
    print(f"\n🔴 失败的端点 ({total_count - success_count}):")
    for result in results:
        if not result.get('success', False):
            endpoint = result['endpoint']
            desc = result['description']
            error = result.get('error', f"状态码: {result.get('status_code', 'N/A')}")
            print(f"  ❌ {endpoint} - {desc} ({error})")
    
    print("\n" + "=" * 60)
    print("测试完成")
    
    # 保存结果到文件
    report_file = f"api_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump({
            'test_time': datetime.now().isoformat(),
            'base_url': BASE_URL,
            'total_endpoints': total_count,
            'success_count': success_count,
            'success_rate': success_count/total_count,
            'results': results
        }, f, ensure_ascii=False, indent=2)
    
    print(f"📄 详细测试报告已保存: {report_file}")

if __name__ == "__main__":
    main()