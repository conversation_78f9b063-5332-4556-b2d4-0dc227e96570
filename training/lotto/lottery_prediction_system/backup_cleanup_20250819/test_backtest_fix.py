#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修復後的歷史回測是否能正常生成候選
"""

import os
import sys
import logging
from datetime import datetime

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from historical_backtest_2025 import Historical2025Backtest

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_single_period_prediction():
    """測試單一期數預測是否能生成候選"""
    print("🧪 測試修復後的歷史回測功能...")
    
    # 初始化回測系統
    backtest = Historical2025Backtest()
    
    # 載入2025年期數
    periods_2025 = backtest.load_2025_periods()
    
    if len(periods_2025) == 0:
        print("❌ 沒有找到2025年期數")
        return
    
    # 測試第一個期數
    test_row = periods_2025.iloc[0]
    period = str(test_row['Period'])
    target_date = test_row['Sdate']
    
    print(f"📅 測試期數: {period} ({target_date})")
    
    # 載入歷史數據
    all_historical_data = backtest.db_manager.load_lottery_data(backtest.lottery_type)
    
    # 獲取訓練數據
    training_data = backtest.get_training_data(target_date, all_historical_data)
    print(f"📊 訓練數據期數: {len(training_data)}")
    
    # 執行預測
    prediction_result = backtest.predict_single_period(period, training_data)
    
    if prediction_result and prediction_result.get('success'):
        candidates = prediction_result.get('candidates', [])
        best_prediction = prediction_result.get('best_prediction')
        
        print(f"✅ 預測成功!")
        print(f"   候選數量: {len(candidates)}")
        print(f"   最佳預測: {'有' if best_prediction else '無'}")
        
        # 顯示候選詳情
        for i, candidate in enumerate(candidates):
            main_numbers = candidate.get('main_numbers', [])
            special_number = candidate.get('special_number')
            confidence = candidate.get('confidence', 0)
            method = candidate.get('method', 'unknown')
            
            print(f"   候選 #{i+1}: {main_numbers} (特別號: {special_number}) - 信心: {confidence:.3f} - 方法: {method}")
        
        # 顯示最佳預測
        if best_prediction:
            print(f"   最佳預測: {best_prediction}")
        
        # 提取實際開獎號碼進行比較
        actual_numbers = []
        for i in range(1, 7):
            col = f'Anumber{i}'
            if col in test_row and pd.notna(test_row[col]):
                actual_numbers.append(int(test_row[col]))
        
        actual_special = None
        if 'PowerBall' in test_row and pd.notna(test_row['PowerBall']):
            actual_special = int(test_row['PowerBall'])
        
        print(f"   實際開獎: {actual_numbers} (特別號: {actual_special})")
        
        # 計算準確度
        accuracy = backtest.calculate_accuracy(prediction_result, actual_numbers, actual_special)
        max_matches = accuracy['max_matches']
        avg_matches = accuracy['avg_matches']
        
        print(f"   最高匹配: {max_matches} 個號碼")
        print(f"   平均匹配: {avg_matches:.2f} 個號碼")
        
        return True
        
    else:
        print("❌ 預測失敗")
        if prediction_result:
            print(f"   錯誤: {prediction_result.get('error', 'unknown error')}")
        return False

if __name__ == "__main__":
    import pandas as pd
    success = test_single_period_prediction()
    if success:
        print("\n🎉 修復驗證成功! 歷史回測現在可以正常生成候選預測了")
    else:
        print("\n❌ 修復驗證失敗")