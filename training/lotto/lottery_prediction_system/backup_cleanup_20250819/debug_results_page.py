#!/usr/bin/env python3
"""
調試開獎結果頁面
直接測試HTML頁面和API的集成
"""

import requests
from bs4 import BeautifulSoup
import json

def debug_results_page():
    """調試開獎結果頁面"""
    print("=== 調試開獎結果頁面 ===")
    
    try:
        # 訪問開獎結果頁面
        page_url = "http://localhost:7890/results"
        print(f"訪問頁面: {page_url}")
        
        page_response = requests.get(page_url, timeout=10)
        
        if page_response.status_code != 200:
            print(f"❌ 頁面訪問失敗: {page_response.status_code}")
            return
        
        print("✅ 頁面訪問成功")
        
        # 檢查頁面內容
        soup = BeautifulSoup(page_response.text, 'html.parser')
        
        # 尋找可能包含"計算中..."的元素
        calculating_elements = soup.find_all(text=lambda text: text and '計算中' in text)
        if calculating_elements:
            print(f"⚠️  發現'計算中...'文字: {len(calculating_elements)} 處")
            for i, elem in enumerate(calculating_elements[:3], 1):
                print(f"  {i}. {elem.strip()}")
        else:
            print("✅ 頁面中沒有發現'計算中...'文字")
        
        # 檢查JavaScript是否正確載入API URL
        script_tags = soup.find_all('script')
        api_found = False
        
        for script in script_tags:
            if script.string and '/api/results' in script.string:
                api_found = True
                print("✅ 發現API調用代碼")
                
                # 檢查是否有正確的API基礎URL
                if 'localhost' in script.string or '127.0.0.1' in script.string:
                    print("⚠️  發現硬編碼的URL")
                else:
                    print("✅ 使用相對URL")
                break
        
        if not api_found:
            print("❌ 未發現API調用代碼")
        
        # 測試頁面載入後的API調用
        print("\n=== 測試API響應格式 ===")
        
        api_url = "http://localhost:7890/api/results?lottery_type=lotto649&limit=3"
        api_response = requests.get(api_url, timeout=10)
        
        if api_response.status_code == 200:
            data = api_response.json()
            print("✅ API響應成功")
            
            if data.get('success'):
                results = data.get('data', {}).get('results', [])
                print(f"✅ 成功獲取 {len(results)} 筆結果")
                
                # 檢查數據格式
                if results:
                    first_result = results[0]
                    print(f"✅ 第一筆數據: 期號={first_result.get('period')}, 日期={first_result.get('date')}")
                    
                    numbers = first_result.get('numbers', {})
                    if 'main_numbers' in numbers:
                        print(f"✅ 主要號碼: {numbers['main_numbers']}")
                        print(f"✅ 特別號: {numbers['special_number']}")
                    else:
                        print("❌ 號碼格式不正確")
                else:
                    print("❌ 沒有結果數據")
            else:
                print(f"❌ API返回錯誤: {data.get('error')}")
        else:
            print(f"❌ API請求失敗: {api_response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 連接失敗 - 請確認Web服務器正在運行")
    except Exception as e:
        print(f"❌ 調試失敗: {e}")

def test_browser_console_simulation():
    """模擬瀏覽器控制台調試"""
    print("\n=== 模擬瀏覽器控制台測試 ===")
    
    try:
        # 模擬頁面載入後的JavaScript行為
        api_url = "http://localhost:7890/api/results"
        params = {
            'lottery_type': 'lotto649',
            'page': 1,
            'limit': 20
        }
        
        print(f"模擬JavaScript fetch調用: {api_url}")
        print(f"參數: {params}")
        
        response = requests.get(api_url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 模擬fetch成功")
            
            # 模擬JavaScript處理邏輯
            if data.get('success') and data.get('data'):
                results = data['data'].get('results', [])
                print(f"模擬displayResults(): 收到 {len(results)} 筆結果")
                
                if results:
                    # 模擬渲染邏輯
                    for i, result in enumerate(results[:2], 1):
                        print(f"  模擬渲染結果 {i}:")
                        print(f"    期號: {result['period']}")
                        print(f"    日期: {result['date']}")
                        
                        numbers = result['numbers']
                        if 'main_numbers' in numbers:
                            print(f"    主要號碼: {numbers['main_numbers']}")
                            print(f"    特別號: {numbers['special_number']}")
                            
                            # 檢查是否有任何"計算中..."的邏輯
                            if not numbers['main_numbers']:
                                print("    ⚠️  主要號碼為空，可能顯示'計算中...'")
                            if numbers['special_number'] is None or numbers['special_number'] == 0:
                                print("    ⚠️  特別號為空或0")
                else:
                    print("❌ 沒有結果，可能顯示'計算中...'或無數據信息")
            else:
                print(f"❌ API數據格式錯誤，可能顯示'計算中...': {data}")
        else:
            print(f"❌ 模擬fetch失敗: {response.status_code}")
            print("可能在錯誤處理中顯示'計算中...'")
            
    except Exception as e:
        print(f"❌ 模擬測試失敗: {e}")

if __name__ == "__main__":
    debug_results_page()
    test_browser_console_simulation()