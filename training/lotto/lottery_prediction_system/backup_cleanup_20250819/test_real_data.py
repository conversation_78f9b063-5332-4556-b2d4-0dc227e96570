#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import DBManager
import json

def test_real_data():
    """測試真實彩票數據的加載"""
    try:
        # 初始化數據庫管理器
        db_manager = DBManager()
        print("數據庫管理器初始化成功")
        
        # 測試威力彩數據
        print("\n=== 測試威力彩數據 ===")
        powercolor_data = db_manager.load_lottery_data('powercolor')
        if powercolor_data is not None and len(powercolor_data) > 0:
            print(f"威力彩數據筆數: {len(powercolor_data)}")
            print(f"數據欄位: {list(powercolor_data.columns)}")
            print("最新5筆數據:")
            print(powercolor_data.tail(5).to_string())
        else:
            print("威力彩數據為空")
            
        # 測試大樂透數據
        print("\n=== 測試大樂透數據 ===")
        lotto649_data = db_manager.load_lottery_data('lotto649')
        if lotto649_data is not None and len(lotto649_data) > 0:
            print(f"大樂透數據筆數: {len(lotto649_data)}")
            print(f"數據欄位: {list(lotto649_data.columns)}")
            print("最新5筆數據:")
            print(lotto649_data.tail(5).to_string())
        else:
            print("大樂透數據為空")
            
        # 測試今彩539數據
        print("\n=== 測試今彩539數據 ===")
        dailycash_data = db_manager.load_lottery_data('dailycash')
        if dailycash_data is not None and len(dailycash_data) > 0:
            print(f"今彩539數據筆數: {len(dailycash_data)}")
            print(f"數據欄位: {list(dailycash_data.columns)}")
            print("最新5筆數據:")
            print(dailycash_data.tail(5).to_string())
        else:
            print("今彩539數據為空")
            
        return True
        
    except Exception as e:
        print(f"測試失敗: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("開始測試真實彩票數據...")
    success = test_real_data()
    if success:
        print("\n✅ 真實數據測試成功！")
    else:
        print("\n❌ 真實數據測試失敗！")