"""
清理所有無效和重複的預測記錄
"""

import sqlite3
from datetime import datetime

def cleanup_all_invalid_records():
    """清理所有無效記錄"""
    
    try:
        conn = sqlite3.connect('data/lottery_data.db')
        cursor = conn.cursor()
        
        print("=== 清理所有無效預測記錄 ===")
        
        # 1. 清理威力彩無效記錄（第一區全為0或空）
        print("\n1. 清理威力彩無效記錄...")
        cursor.execute('''
        SELECT COUNT(*) FROM PowerColorPredictions 
        WHERE (PredA1 = 0 OR PredA1 IS NULL) 
           AND (PredA2 = 0 OR PredA2 IS NULL) 
           AND (PredA3 = 0 OR PredA3 IS NULL) 
           AND (PredA4 = 0 OR PredA4 IS NULL) 
           AND (PredA5 = 0 OR PredA5 IS NULL) 
           AND (PredA6 = 0 OR PredA6 IS NULL)
        ''')
        
        invalid_count = cursor.fetchone()[0]
        print(f"找到 {invalid_count} 筆威力彩無效記錄")
        
        if invalid_count > 0:
            cursor.execute('''
            DELETE FROM PowerColorPredictions 
            WHERE (PredA1 = 0 OR PredA1 IS NULL) 
               AND (PredA2 = 0 OR PredA2 IS NULL) 
               AND (PredA3 = 0 OR PredA3 IS NULL) 
               AND (PredA4 = 0 OR PredA4 IS NULL) 
               AND (PredA5 = 0 OR PredA5 IS NULL) 
               AND (PredA6 = 0 OR PredA6 IS NULL)
            ''')
            print(f"✅ 已刪除 {cursor.rowcount} 筆威力彩無效記錄")
        
        # 2. 清理大樂透無效記錄
        print("\n2. 清理大樂透無效記錄...")
        cursor.execute('''
        SELECT COUNT(*) FROM Lotto649Predictions 
        WHERE (PredA1 = 0 OR PredA1 IS NULL) 
           AND (PredA2 = 0 OR PredA2 IS NULL) 
           AND (PredA3 = 0 OR PredA3 IS NULL) 
           AND (PredA4 = 0 OR PredA4 IS NULL) 
           AND (PredA5 = 0 OR PredA5 IS NULL) 
           AND (PredA6 = 0 OR PredA6 IS NULL)
        ''')
        
        invalid_lotto_count = cursor.fetchone()[0]
        print(f"找到 {invalid_lotto_count} 筆大樂透無效記錄")
        
        if invalid_lotto_count > 0:
            cursor.execute('''
            DELETE FROM Lotto649Predictions 
            WHERE (PredA1 = 0 OR PredA1 IS NULL) 
               AND (PredA2 = 0 OR PredA2 IS NULL) 
               AND (PredA3 = 0 OR PredA3 IS NULL) 
               AND (PredA4 = 0 OR PredA4 IS NULL) 
               AND (PredA5 = 0 OR PredA5 IS NULL) 
               AND (PredA6 = 0 OR PredA6 IS NULL)
            ''')
            print(f"✅ 已刪除 {cursor.rowcount} 筆大樂透無效記錄")
        
        # 3. 清理今彩539無效記錄
        print("\n3. 清理今彩539無效記錄...")
        cursor.execute('''
        SELECT COUNT(*) FROM DailyCashPredictions 
        WHERE (PredA1 = 0 OR PredA1 IS NULL) 
           AND (PredA2 = 0 OR PredA2 IS NULL) 
           AND (PredA3 = 0 OR PredA3 IS NULL) 
           AND (PredA4 = 0 OR PredA4 IS NULL) 
           AND (PredA5 = 0 OR PredA5 IS NULL)
        ''')
        
        invalid_daily_count = cursor.fetchone()[0]
        print(f"找到 {invalid_daily_count} 筆今彩539無效記錄")
        
        if invalid_daily_count > 0:
            cursor.execute('''
            DELETE FROM DailyCashPredictions 
            WHERE (PredA1 = 0 OR PredA1 IS NULL) 
               AND (PredA2 = 0 OR PredA2 IS NULL) 
               AND (PredA3 = 0 OR PredA3 IS NULL) 
               AND (PredA4 = 0 OR PredA4 IS NULL) 
               AND (PredA5 = 0 OR PredA5 IS NULL)
            ''')
            print(f"✅ 已刪除 {cursor.rowcount} 筆今彩539無效記錄")
        
        conn.commit()
        print("\n✅ 無效記錄清理完成！")
        
        conn.close()
        
    except Exception as e:
        print(f"錯誤: {e}")

def cleanup_test_data():
    """清理明顯的測試數據（如1-2-3-4-5-6等）"""
    
    try:
        conn = sqlite3.connect('data/lottery_data.db')
        cursor = conn.cursor()
        
        print("=== 清理測試數據 ===")
        
        # 清理威力彩測試數據
        print("\n1. 清理威力彩測試數據...")
        
        # 清理 1-2-3-4-5-6 組合
        cursor.execute('''
        SELECT COUNT(*) FROM PowerColorPredictions 
        WHERE PredA1 = 1 AND PredA2 = 2 AND PredA3 = 3 
          AND PredA4 = 4 AND PredA5 = 5 AND PredA6 = 6
        ''')
        test_count = cursor.fetchone()[0]
        print(f"找到 {test_count} 筆 1-2-3-4-5-6 測試記錄")
        
        if test_count > 0:
            response = input(f"是否要刪除這 {test_count} 筆測試記錄？(y/N): ").strip().lower()
            if response == 'y':
                cursor.execute('''
                DELETE FROM PowerColorPredictions 
                WHERE PredA1 = 1 AND PredA2 = 2 AND PredA3 = 3 
                  AND PredA4 = 4 AND PredA5 = 5 AND PredA6 = 6
                ''')
                print(f"✅ 已刪除 {cursor.rowcount} 筆測試記錄")
        
        # 清理 2-3-4-5-6-7 組合
        cursor.execute('''
        SELECT COUNT(*) FROM PowerColorPredictions 
        WHERE PredA1 = 2 AND PredA2 = 3 AND PredA3 = 4 
          AND PredA4 = 5 AND PredA5 = 6 AND PredA6 = 7
        ''')
        test_count2 = cursor.fetchone()[0]
        print(f"找到 {test_count2} 筆 2-3-4-5-6-7 測試記錄")
        
        if test_count2 > 0:
            response = input(f"是否要刪除這 {test_count2} 筆測試記錄？(y/N): ").strip().lower()
            if response == 'y':
                cursor.execute('''
                DELETE FROM PowerColorPredictions 
                WHERE PredA1 = 2 AND PredA2 = 3 AND PredA3 = 4 
                  AND PredA4 = 5 AND PredA5 = 6 AND PredA6 = 7
                ''')
                print(f"✅ 已刪除 {cursor.rowcount} 筆測試記錄")
        
        conn.commit()
        print("\n✅ 測試數據清理完成！")
        
        conn.close()
        
    except Exception as e:
        print(f"錯誤: {e}")

def show_database_summary():
    """顯示數據庫摘要"""
    
    try:
        conn = sqlite3.connect('data/lottery_data.db')
        cursor = conn.cursor()
        
        print("=== 數據庫摘要 ===")
        
        # 威力彩統計
        cursor.execute('SELECT COUNT(*) FROM PowerColorPredictions')
        pc_total = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(DISTINCT Period) FROM PowerColorPredictions')
        pc_periods = cursor.fetchone()[0]
        
        print(f"\n威力彩預測記錄:")
        print(f"  總記錄數: {pc_total}")
        print(f"  期數數量: {pc_periods}")
        
        # 大樂透統計
        cursor.execute('SELECT COUNT(*) FROM Lotto649Predictions')
        lotto_total = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(DISTINCT Period) FROM Lotto649Predictions')
        lotto_periods = cursor.fetchone()[0]
        
        print(f"\n大樂透預測記錄:")
        print(f"  總記錄數: {lotto_total}")
        print(f"  期數數量: {lotto_periods}")
        
        # 今彩539統計
        cursor.execute('SELECT COUNT(*) FROM DailyCashPredictions')
        daily_total = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(DISTINCT Period) FROM DailyCashPredictions')
        daily_periods = cursor.fetchone()[0]
        
        print(f"\n今彩539預測記錄:")
        print(f"  總記錄數: {daily_total}")
        print(f"  期數數量: {daily_periods}")
        
        print(f"\n總計: {pc_total + lotto_total + daily_total} 筆預測記錄")
        
        # 顯示最近的期數
        print(f"\n最近的威力彩期數:")
        cursor.execute('''
        SELECT DISTINCT Period FROM PowerColorPredictions 
        ORDER BY Period DESC LIMIT 5
        ''')
        recent_periods = cursor.fetchall()
        for period in recent_periods:
            print(f"  {period[0]}")
        
        conn.close()
        
    except Exception as e:
        print(f"錯誤: {e}")

if __name__ == "__main__":
    print("選擇操作:")
    print("1. 顯示數據庫摘要")
    print("2. 清理所有無效記錄")
    print("3. 清理測試數據")
    print("4. 全部清理")
    
    choice = input("\n請選擇 (1/2/3/4): ").strip()
    
    if choice == '1':
        show_database_summary()
    elif choice == '2':
        cleanup_all_invalid_records()
    elif choice == '3':
        cleanup_test_data()
    elif choice == '4':
        cleanup_all_invalid_records()
        cleanup_test_data()
        show_database_summary()
    else:
        print("無效選擇")
