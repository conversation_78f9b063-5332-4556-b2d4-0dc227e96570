#!/usr/bin/env python3

import sys
import inspect
from prediction.lottery_predictor import LotteryPredictor

def debug_class_methods():
    """详细调试LotteryPredictor类的方法"""
    print("=== LotteryPredictor 类调试 ===")
    
    # 创建实例
    predictor = LotteryPredictor()
    
    # 获取类的所有属性和方法
    all_attrs = dir(predictor)
    print(f"\n总共有 {len(all_attrs)} 个属性/方法")
    
    # 分类显示
    methods = []
    properties = []
    private_methods = []
    
    for attr in all_attrs:
        if callable(getattr(predictor, attr)):
            if attr.startswith('_'):
                private_methods.append(attr)
            else:
                methods.append(attr)
        else:
            properties.append(attr)
    
    print(f"\n=== 公共方法 ({len(methods)}) ===")
    for method in sorted(methods):
        print(f"  ✓ {method}")
    
    print(f"\n=== 私有方法 ({len(private_methods)}) ===")
    for method in sorted(private_methods):
        print(f"  ✓ {method}")
    
    print(f"\n=== 属性 ({len(properties)}) ===")
    for prop in sorted(properties):
        print(f"  ✓ {prop}")
    
    # 检查特定方法
    target_methods = [
        'update_actual_numbers',
        'get_prediction_accuracy', 
        'predict_with_enhanced_analysis',
        '_merge_predictions',
        '_get_lottery_name'
    ]
    
    print(f"\n=== 目标方法检查 ===")
    for method_name in target_methods:
        if hasattr(predictor, method_name):
            method = getattr(predictor, method_name)
            if callable(method):
                try:
                    sig = inspect.signature(method)
                    print(f"  ✓ {method_name}{sig}")
                except Exception as e:
                    print(f"  ✓ {method_name} (无法获取签名: {e})")
            else:
                print(f"  ✗ {method_name} 不是可调用对象")
        else:
            print(f"  ✗ {method_name} 不存在")
    
    # 检查类的MRO
    print(f"\n=== 方法解析顺序 (MRO) ===")
    for i, cls in enumerate(LotteryPredictor.__mro__):
        print(f"  {i}: {cls}")
    
    # 检查类字典中的方法
    print(f"\n=== 类字典中的方法 ===")
    class_methods = [name for name in LotteryPredictor.__dict__ if callable(LotteryPredictor.__dict__[name])]
    for method in sorted(class_methods):
        print(f"  ✓ {method}")
    
    print(f"\n=== 实例字典 ===")
    print(f"实例字典内容: {list(predictor.__dict__.keys())}")

if __name__ == "__main__":
    debug_class_methods()