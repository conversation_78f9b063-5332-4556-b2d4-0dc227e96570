#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速預測測試腳本
用於測試預測功能並保存結果
"""

import os
import sys
import json
from datetime import datetime, timedelta
import logging

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from data.db_manager import DBManager
    from prediction.enhanced_lottery_predictor import EnhancedLotteryPredictor
except ImportError as e:
    print(f"導入模組失敗: {e}")
    print("請確保所有必要的模組都存在")
    sys.exit(1)

class QuickPredictionTest:
    """
    快速預測測試器
    """
    
    def __init__(self, lottery_type='powercolor'):
        self.lottery_type = lottery_type
        self.results = []
        
        # 設置日誌
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        try:
            from config_manager import ConfigManager
            config_manager = ConfigManager()
            self.db_manager = DBManager(config_manager)
            self.predictor = EnhancedLotteryPredictor(lottery_type)
            self.logger.info(f"初始化完成 - 彩票類型: {lottery_type}")
        except Exception as e:
            self.logger.error(f"初始化失敗: {e}")
            raise
    
    def generate_period_number(self, base_date, offset_days=0):
        """
        生成期號
        """
        date = base_date + timedelta(days=offset_days)
        year = date.year
        
        if self.lottery_type == 'powercolor':
            # 威力彩：每週一、四開獎
            # 簡化期號生成
            week_of_year = date.isocalendar()[1]
            period_in_year = week_of_year * 2 + (1 if date.weekday() <= 3 else 0)
            return f"{year}{period_in_year:03d}"
            
        elif self.lottery_type == 'lotto649':
            # 大樂透：每週二、五開獎
            week_of_year = date.isocalendar()[1]
            period_in_year = week_of_year * 2 + (1 if date.weekday() <= 4 else 0)
            return f"{year}{period_in_year:03d}"
            
        elif self.lottery_type == 'dailycash':
            # 今彩539：每日開獎
            day_of_year = date.timetuple().tm_yday
            return f"{year}{day_of_year:03d}"
            
        return f"{year}001"
    
    def make_single_prediction(self, period):
        """
        進行單次預測
        """
        try:
            self.logger.info(f"開始預測第 {period} 期")
            
            # 載入歷史數據
            historical_data = self.db_manager.load_lottery_data(self.lottery_type)
            
            if not historical_data:
                self.logger.warning("沒有歷史數據，使用隨機預測")
                # 如果沒有歷史數據，生成隨機預測
                if self.lottery_type == 'powercolor':
                    import random
                    numbers = sorted(random.sample(range(1, 39), 6))
                    special = random.randint(1, 8)
                    prediction_result = {
                        'numbers': numbers,
                        'special_number': special,
                        'confidence': 0.5,
                        'method': 'random_fallback'
                    }
                else:
                    return None
            else:
                # 使用預測器進行預測
                prediction_result = self.predictor.predict()
            
            if not prediction_result:
                self.logger.error(f"預測失敗：第 {period} 期")
                return None
            
            # 構建預測記錄
            prediction_record = {
                'period': period,
                'prediction_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'lottery_type': self.lottery_type,
                'predicted_numbers': prediction_result.get('numbers', []),
                'special_number': prediction_result.get('special_number'),
                'confidence': prediction_result.get('confidence', 0),
                'method_used': prediction_result.get('method', 'unknown')
            }
            
            # 保存到數據庫
            try:
                numbers_str = ','.join(map(str, prediction_record['predicted_numbers']))
                special_str = str(prediction_record.get('special_number', ''))
                
                self.db_manager.save_prediction(
                    lottery_type=self.lottery_type,
                    period=period,
                    numbers=numbers_str,
                    special_number=special_str,
                    confidence=prediction_record['confidence'],
                    method=prediction_record['method_used']
                )
                self.logger.info("預測結果已保存到數據庫")
            except Exception as e:
                self.logger.warning(f"保存到數據庫失敗: {e}")
            
            # 添加到結果列表
            self.results.append(prediction_record)
            
            self.logger.info(f"預測完成：第 {period} 期")
            self.logger.info(f"預測號碼: {prediction_record['predicted_numbers']}")
            if prediction_record.get('special_number'):
                self.logger.info(f"特別號: {prediction_record['special_number']}")
            self.logger.info(f"信心度: {prediction_record['confidence']:.2f}")
            self.logger.info(f"使用方法: {prediction_record['method_used']}")
            
            return prediction_record
            
        except Exception as e:
            self.logger.error(f"預測過程發生錯誤：{str(e)}")
            return None
    
    def run_multiple_predictions(self, num_periods=10):
        """
        執行多期預測
        """
        self.logger.info(f"開始進行 {num_periods} 期預測測試")
        
        base_date = datetime(2025, 1, 1)
        successful_predictions = 0
        
        for i in range(num_periods):
            period = self.generate_period_number(base_date, i * 3)  # 每3天一期
            
            self.logger.info(f"\n=== 第 {i+1}/{num_periods} 次預測 ===")
            
            result = self.make_single_prediction(period)
            
            if result:
                successful_predictions += 1
            
        # 保存結果
        self.save_results()
        
        # 顯示統計
        self.logger.info(f"\n=== 預測測試完成 ===")
        self.logger.info(f"總預測期數: {num_periods}")
        self.logger.info(f"成功預測: {successful_predictions}")
        self.logger.info(f"成功率: {successful_predictions/num_periods*100:.2f}%")
        
        return self.results
    
    def save_results(self):
        """
        保存預測結果
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 創建結果目錄
            results_dir = 'prediction_test_results'
            os.makedirs(results_dir, exist_ok=True)
            
            # 保存JSON格式
            json_file = os.path.join(results_dir, f'{self.lottery_type}_predictions_{timestamp}.json')
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            
            # 保存文本格式
            txt_file = os.path.join(results_dir, f'{self.lottery_type}_predictions_{timestamp}.txt')
            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write(f"=== {self.lottery_type.upper()} 預測結果 ===\n\n")
                f.write(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"總預測期數: {len(self.results)}\n\n")
                
                for i, record in enumerate(self.results, 1):
                    f.write(f"第 {i} 期預測:\n")
                    f.write(f"  期號: {record['period']}\n")
                    f.write(f"  預測時間: {record['prediction_time']}\n")
                    f.write(f"  預測號碼: {record['predicted_numbers']}\n")
                    if record.get('special_number'):
                        f.write(f"  特別號: {record['special_number']}\n")
                    f.write(f"  信心度: {record['confidence']:.2f}\n")
                    f.write(f"  使用方法: {record['method_used']}\n")
                    f.write("\n")
            
            self.logger.info(f"預測結果已保存到：")
            self.logger.info(f"  JSON格式: {json_file}")
            self.logger.info(f"  文本格式: {txt_file}")
            
        except Exception as e:
            self.logger.error(f"保存結果失敗：{str(e)}")

def main():
    """
    主函數
    """
    print("=== 快速預測測試系統 ===")
    print("1. 威力彩 (powercolor)")
    print("2. 大樂透 (lotto649)")
    print("3. 今彩539 (dailycash)")
    
    choice = input("請選擇彩票類型 (1-3，默認1): ").strip()
    
    lottery_types = {
        '1': 'powercolor',
        '2': 'lotto649', 
        '3': 'dailycash'
    }
    
    lottery_type = lottery_types.get(choice, 'powercolor')
    
    num_periods_str = input("請輸入預測期數 (默認10期): ").strip()
    num_periods = int(num_periods_str) if num_periods_str else 10
    
    try:
        # 創建測試器並執行
        tester = QuickPredictionTest(lottery_type)
        results = tester.run_multiple_predictions(num_periods)
        
        print(f"\n測試完成！共生成 {len(results)} 期預測結果")
        print("結果已保存在 prediction_test_results 目錄中")
        
    except Exception as e:
        print(f"測試過程發生錯誤：{str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()