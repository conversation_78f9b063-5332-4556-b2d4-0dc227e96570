#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試API統計分析功能
"""

import requests
import json

def test_api_statistics():
    """
    測試各種彩票的API統計分析
    """
    base_url = "http://127.0.0.1:5002"
    
    lottery_types = [
        ('powercolor', '威力彩'),
        ('lotto649', '大樂透'),
        ('dailycash', '今彩539')
    ]
    
    for lottery_type, name in lottery_types:
        print(f"\n=== {name} API統計測試 ===")
        
        try:
            # 測試API端點
            url = f"{base_url}/api/results"
            params = {
                'lottery_type': lottery_type,
                'limit': 5
            }
            
            print(f"請求URL: {url}")
            print(f"參數: {params}")
            
            response = requests.get(url, params=params, timeout=10)
            
            print(f"響應狀態碼: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                
                # 顯示統計信息
                if 'statistics' in data:
                    stats = data['statistics']
                    print(f"總開獎次數: {stats.get('total_draws', 'N/A')}")
                    print(f"日期範圍: {stats.get('date_range', 'N/A')}")
                    print(f"最常出現號碼: {stats.get('most_frequent_number', 'N/A')} (出現{stats.get('most_frequent_count', 0)}次)")
                    print(f"最少出現號碼: {stats.get('least_frequent_number', 'N/A')} (出現{stats.get('least_frequent_count', 0)}次)")
                else:
                    print("響應中沒有統計信息")
                
                # 顯示結果數量
                if 'results' in data:
                    print(f"返回結果數量: {len(data['results'])}")
                else:
                    print("響應中沒有結果數據")
                    
                print("API測試成功")
            else:
                print(f"API請求失敗: {response.status_code}")
                print(f"響應內容: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print(f"連接錯誤: 無法連接到服務器 {base_url}")
        except requests.exceptions.Timeout:
            print("請求超時")
        except Exception as e:
            print(f"測試失敗: {str(e)}")
        
        print("-" * 50)

if __name__ == '__main__':
    test_api_statistics()