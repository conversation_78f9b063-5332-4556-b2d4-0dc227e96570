#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試預測生成問題
找出為什麼增強分析能找到數學關係，但無法生成候選預測
"""

import os
import sys
import logging
import pandas as pd
from datetime import datetime

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import DBManager
from prediction.enhanced_board_path_analyzer import EnhancedBoardPathAnalyzer

# 設置詳細日誌
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_prediction_generation():
    """調試預測生成過程"""
    print("🔍 開始調試預測生成問題...")
    
    # 初始化
    db_manager = DBManager()
    analyzer = EnhancedBoardPathAnalyzer(db_manager, 'powercolor')
    
    # 載入歷史數據
    df = db_manager.load_lottery_data('powercolor')
    
    # 使用最近100期作為訓練數據
    training_data = df.head(100)
    print(f"📊 使用訓練數據: {len(training_data)} 期")
    
    # 1. 測試數學關係分析
    print("\n1️⃣ 測試數學關係分析...")
    analysis = analyzer.analyze_mathematical_relationships(training_data)
    
    print(f"   算術關係: {len(analysis.get('arithmetic_relationships', []))}")
    print(f"   數列關係: {len(analysis.get('sequence_relationships', []))}")
    print(f"   模運算關係: {len(analysis.get('modular_relationships', []))}")
    
    # 顯示前幾個關係
    if analysis.get('arithmetic_relationships'):
        print("   前3個算術關係:")
        for i, rel in enumerate(analysis['arithmetic_relationships'][:3]):
            print(f"     {i+1}. {rel.get('type', 'unknown')}: {rel.get('formula', 'no formula')}")
    
    # 2. 測試候選生成
    print("\n2️⃣ 測試候選生成...")
    
    # 獲取最近一期號碼
    last_row = training_data.iloc[-1]
    last_numbers = []
    number_columns = ['Anumber1', 'Anumber2', 'Anumber3', 'Anumber4', 'Anumber5', 'Anumber6']
    for col in number_columns:
        if pd.notna(last_row[col]):
            last_numbers.append(int(last_row[col]))
    
    print(f"   最近一期號碼: {last_numbers}")
    
    # 3. 逐個測試預測方法
    print("\n3️⃣ 測試各個預測方法...")
    
    prediction_methods = [
        ('算術預測', analyzer._predict_from_arithmetic),
        ('數列預測', analyzer._predict_from_sequences),
        ('模運算預測', analyzer._predict_from_modular),
        ('統計預測', analyzer._predict_from_statistical),
        ('跨期預測', analyzer._predict_from_cross_period)
    ]
    
    for method_name, method in prediction_methods:
        try:
            print(f"\n   測試 {method_name}...")
            result = method(analysis, last_numbers, training_data)
            print(f"     結果: {result}")
            print(f"     數量: {len(result) if result else 0}")
            
            if result:
                print(f"     範圍: {min(result)}-{max(result)}")
                print(f"     重複: {len(result) != len(set(result))}")
            
        except Exception as e:
            print(f"     錯誤: {str(e)}")
            import traceback
            traceback.print_exc()
    
    # 4. 測試完整候選生成
    print("\n4️⃣ 測試完整候選生成...")
    
    try:
        for i in range(5):
            print(f"\n   生成候選 #{i+1}...")
            candidate = analyzer._generate_candidate_from_analysis(analysis, training_data, i)
            
            if candidate:
                print(f"     成功: {candidate.get('main_numbers', [])}")
                print(f"     特別號: {candidate.get('special_number')}")
                print(f"     信心: {candidate.get('confidence', 0):.3f}")
                print(f"     方法: {candidate.get('method', 'unknown')}")
            else:
                print(f"     失敗: None")
                
    except Exception as e:
        print(f"     錯誤: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # 5. 測試完整預測流程
    print("\n5️⃣ 測試完整預測流程...")
    
    try:
        prediction_result = analyzer.predict_with_enhanced_analysis(training_data, candidates_count=3)
        
        print(f"   預測結果類型: {type(prediction_result)}")
        print(f"   包含鍵: {list(prediction_result.keys()) if prediction_result else 'None'}")
        
        if prediction_result and 'predictions' in prediction_result:
            predictions = prediction_result['predictions']
            print(f"   候選數量: {len(predictions)}")
            
            for i, pred in enumerate(predictions):
                print(f"     候選 #{i+1}: {pred.get('main_numbers', [])}")
        
    except Exception as e:
        print(f"   錯誤: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n✅ 調試完成!")

if __name__ == "__main__":
    debug_prediction_generation()