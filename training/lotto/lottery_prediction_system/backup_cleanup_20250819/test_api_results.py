#!/usr/bin/env python3
"""
測試 API results endpoint
檢查開獎結果API的實現
"""

from data.db_manager import DBManager
import pandas as pd
import json

def test_lottery_data_loading():
    """測試彩票數據載入"""
    print("=== 測試彩票數據載入 ===")
    
    # 初始化數據庫管理器
    db_manager = DBManager()
    
    lottery_types = ['powercolor', 'lotto649', 'dailycash']
    
    for lottery_type in lottery_types:
        print(f"\n--- 測試 {lottery_type} ---")
        
        try:
            # 載入數據
            df = db_manager.load_lottery_data(lottery_type=lottery_type)
            
            if df.empty:
                print(f"❌ {lottery_type}: 沒有數據")
                continue
                
            print(f"✅ {lottery_type}: 成功載入 {len(df)} 筆數據")
            print(f"欄位: {list(df.columns)}")
            
            # 顯示最新3筆數據
            print("最新3筆數據:")
            latest_data = df.tail(3)
            for _, row in latest_data.iterrows():
                print(f"  期號: {row['Period']}, 日期: {row['Sdate']}")
                
                # 根據彩票類型顯示號碼
                if lottery_type == 'powercolor':
                    numbers = [row[f'Anumber{i}'] for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])]
                    special = row['Second_district'] if pd.notna(row['Second_district']) else 0
                    print(f"    第一區: {numbers}, 第二區: {special}")
                    
                elif lottery_type == 'lotto649':
                    numbers = [row[f'Anumber{i}'] for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])]
                    special = row['SpecialNumber'] if pd.notna(row['SpecialNumber']) else 0
                    print(f"    一般號碼: {numbers}, 特別號: {special}")
                    
                elif lottery_type == 'dailycash':
                    numbers = [row[f'Anumber{i}'] for i in range(1, 6) if pd.notna(row[f'Anumber{i}'])]
                    print(f"    開獎號碼: {numbers}")
                    
        except Exception as e:
            print(f"❌ {lottery_type}: 載入失敗 - {e}")


def test_api_format_conversion():
    """測試API格式轉換"""
    print("\n=== 測試API格式轉換 ===")
    
    db_manager = DBManager()
    
    for lottery_type in ['powercolor', 'lotto649', 'dailycash']:
        print(f"\n--- 測試 {lottery_type} API格式 ---")
        
        try:
            df = db_manager.load_lottery_data(lottery_type=lottery_type)
            
            if df.empty:
                print(f"❌ {lottery_type}: 沒有數據")
                continue
            
            # 轉換為API格式 (模擬web/app.py中的邏輯)
            all_results = []
            for _, row in df.tail(3).iterrows():  # 只測試最新3筆
                result = {
                    'period': str(int(row['Period'])),
                    'date': row['Sdate'].strftime('%Y-%m-%d') if pd.notna(row['Sdate']) else '',
                    'numbers': {}
                }
                
                # 根據彩票類型格式化號碼
                if lottery_type == 'powercolor':
                    result['numbers'] = {
                        'first_area': [int(row[f'Anumber{i}']) for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])],
                        'second_area': int(row['Second_district']) if pd.notna(row['Second_district']) else 0
                    }
                elif lottery_type == 'lotto649':
                    result['numbers'] = {
                        'main_numbers': [int(row[f'Anumber{i}']) for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])],
                        'special_number': int(row['SpecialNumber']) if pd.notna(row['SpecialNumber']) else 0
                    }
                elif lottery_type == 'dailycash':
                    result['numbers'] = {
                        'numbers': [int(row[f'Anumber{i}']) for i in range(1, 6) if pd.notna(row[f'Anumber{i}'])]
                    }
                
                all_results.append(result)
            
            print(f"✅ {lottery_type}: 成功轉換 {len(all_results)} 筆數據")
            
            # 顯示轉換後的格式
            for result in all_results:
                print(f"  期號: {result['period']}, 日期: {result['date']}")
                print(f"  號碼: {json.dumps(result['numbers'], ensure_ascii=False)}")
                
        except Exception as e:
            print(f"❌ {lottery_type}: 轉換失敗 - {e}")
            import traceback
            traceback.print_exc()


def test_web_api_simulation():
    """模擬 Web API 調用"""
    print("\n=== 模擬 Web API 調用 ===")
    
    # 模擬 /api/results 的邏輯
    from data.db_manager import DBManager
    import pandas as pd
    from datetime import datetime
    
    lottery_type = 'lotto649'  # 測試大樂透
    page = 1
    per_page = 5
    
    try:
        print(f"測試參數: lottery_type={lottery_type}, page={page}, per_page={per_page}")
        
        # 初始化數據庫管理器
        db_manager = DBManager()
        
        # 載入真實歷史數據
        df = db_manager.load_lottery_data(lottery_type=lottery_type)
        
        if df.empty:
            print("❌ 沒有數據")
            return
            
        print(f"✅ 載入數據: {len(df)} 筆")
        
        # 轉換真實數據為API格式
        all_results = []
        for _, row in df.iterrows():
            result = {
                'period': str(int(row['Period'])),
                'date': row['Sdate'].strftime('%Y-%m-%d') if pd.notna(row['Sdate']) else '',
                'numbers': {}
            }
            
            # 根據彩票類型格式化號碼
            if lottery_type == 'lotto649':
                result['numbers'] = {
                    'main_numbers': [int(row[f'Anumber{i}']) for i in range(1, 7) if pd.notna(row[f'Anumber{i}'])],
                    'special_number': int(row['SpecialNumber']) if pd.notna(row['SpecialNumber']) else 0
                }
            
            all_results.append(result)
        
        # 按期號降序排列
        all_results.sort(key=lambda x: int(x['period']), reverse=True)
        
        # 計算分頁
        total_filtered = len(all_results)
        start_idx = (page - 1) * per_page
        end_idx = min(start_idx + per_page, total_filtered)
        
        results = all_results[start_idx:end_idx]
        
        print(f"✅ 分頁結果: 顯示第 {start_idx+1}-{end_idx} 筆，共 {total_filtered} 筆")
        
        # 顯示結果
        for i, result in enumerate(results, 1):
            print(f"  {i}. 期號: {result['period']}, 日期: {result['date']}")
            numbers = result['numbers']
            if 'main_numbers' in numbers:
                print(f"     一般號碼: {numbers['main_numbers']}, 特別號: {numbers['special_number']}")
            
        response_data = {
            'results': results,
            'pagination': {
                'current_page': page,
                'per_page': per_page,
                'total': total_filtered,
                'total_pages': (total_filtered + per_page - 1) // per_page if total_filtered > 0 else 1
            }
        }
        
        print(f"✅ API響應格式正確，總頁數: {response_data['pagination']['total_pages']}")
        
    except Exception as e:
        print(f"❌ API模擬失敗: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_lottery_data_loading()
    test_api_format_conversion()
    test_web_api_simulation()