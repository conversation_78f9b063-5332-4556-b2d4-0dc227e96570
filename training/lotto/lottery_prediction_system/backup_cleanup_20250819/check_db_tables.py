#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查數據庫表結構
"""

import sqlite3
import os

def check_database_tables():
    """
    檢查數據庫中的表和記錄數
    """
    try:
        db_path = os.path.join('data', 'lottery_data.db')
        
        if not os.path.exists(db_path):
            print(f"數據庫文件不存在: {db_path}")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 獲取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"數據庫中的表: {[table[0] for table in tables]}")
        
        # 檢查每個表的記錄數
        for table in tables:
            table_name = table[0]
            cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
            count = cursor.fetchone()[0]
            print(f'{table_name}: {count} 條記錄')
            
            # 檢查表結構
            cursor.execute(f'PRAGMA table_info({table_name})')
            columns = cursor.fetchall()
            print(f'  列: {[col[1] for col in columns]}')
        
        conn.close()
        
    except Exception as e:
        print(f"檢查數據庫失敗: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    check_database_tables()