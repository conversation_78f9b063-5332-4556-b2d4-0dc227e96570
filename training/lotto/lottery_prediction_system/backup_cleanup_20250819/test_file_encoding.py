#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件编码和隐藏字符
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def check_file_encoding():
    """检查文件编码和隐藏字符"""
    file_path = '/Users/<USER>/python/training/lotto/lottery_prediction_system/prediction/lottery_predictor.py'
    
    print("=== 检查文件编码 ===")
    
    # 读取文件的原始字节
    with open(file_path, 'rb') as f:
        raw_bytes = f.read()
    
    print(f"文件大小: {len(raw_bytes)} 字节")
    
    # 尝试不同的编码
    encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252']
    
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            print(f"使用 {encoding} 编码成功读取，长度: {len(content)} 字符")
            
            # 检查第1180行附近的内容
            lines = content.split('\n')
            if len(lines) > 1180:
                print(f"\n第1175-1185行内容 (使用 {encoding})：")
                for i in range(1174, min(1185, len(lines))):
                    line = lines[i]
                    print(f"{i+1:4d}: {repr(line)}")
            break
        except Exception as e:
            print(f"使用 {encoding} 编码失败: {e}")
    
    # 检查特定行的字节表示
    print("\n=== 检查第1180行附近的字节表示 ===")
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for i in range(1174, min(1185, len(lines))):
            line = lines[i]
            line_bytes = line.encode('utf-8')
            print(f"第{i+1}行字节: {line_bytes}")
            print(f"第{i+1}行文本: {repr(line)}")
            print()
            
    except Exception as e:
        print(f"检查字节表示时出错: {e}")

def test_syntax():
    """测试语法"""
    print("\n=== 测试语法 ===")
    
    file_path = '/Users/<USER>/python/training/lotto/lottery_prediction_system/prediction/lottery_predictor.py'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试编译代码
        compile(content, file_path, 'exec')
        print("语法检查通过")
        
    except SyntaxError as e:
        print(f"语法错误: {e}")
        print(f"错误位置: 第{e.lineno}行, 第{e.offset}列")
        print(f"错误文本: {e.text}")
    except Exception as e:
        print(f"其他错误: {e}")

if __name__ == '__main__':
    check_file_encoding()
    test_syntax()