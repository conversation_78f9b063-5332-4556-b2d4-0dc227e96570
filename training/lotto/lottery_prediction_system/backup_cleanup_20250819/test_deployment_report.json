{"timestamp": "2025-07-20T07:07:09.023061", "total_tests": 0, "failures": 0, "errors": 7, "success_rate": 0, "details": {"failures": [], "errors": [{"test": "setUpClass (__main__.TestSystemMonitor)", "error": "Traceback (most recent call last):\n  File \"/Users/<USER>/python/training/lotto/lottery_prediction_system/test_production_deployment.py\", line 40, in setUpClass\n    cls.test_config = DeploymentConfig(\n                      ^^^^^^^^^^^^^^^^^\nTypeError: DeploymentConfig.__init__() missing 1 required positional argument: 'monitoring_enabled'\n"}, {"test": "setUpClass (__main__.TestHealthChecker)", "error": "Traceback (most recent call last):\n  File \"/Users/<USER>/python/training/lotto/lottery_prediction_system/test_production_deployment.py\", line 40, in setUpClass\n    cls.test_config = DeploymentConfig(\n                      ^^^^^^^^^^^^^^^^^\nTypeError: DeploymentConfig.__init__() missing 1 required positional argument: 'monitoring_enabled'\n"}, {"test": "setUpClass (__main__.TestLogManager)", "error": "Traceback (most recent call last):\n  File \"/Users/<USER>/python/training/lotto/lottery_prediction_system/test_production_deployment.py\", line 40, in setUpClass\n    cls.test_config = DeploymentConfig(\n                      ^^^^^^^^^^^^^^^^^\nTypeError: DeploymentConfig.__init__() missing 1 required positional argument: 'monitoring_enabled'\n"}, {"test": "setUpClass (__main__.TestBackupManager)", "error": "Traceback (most recent call last):\n  File \"/Users/<USER>/python/training/lotto/lottery_prediction_system/test_production_deployment.py\", line 40, in setUpClass\n    cls.test_config = DeploymentConfig(\n                      ^^^^^^^^^^^^^^^^^\nTypeError: DeploymentConfig.__init__() missing 1 required positional argument: 'monitoring_enabled'\n"}, {"test": "setUpClass (__main__.TestDeploymentManager)", "error": "Traceback (most recent call last):\n  File \"/Users/<USER>/python/training/lotto/lottery_prediction_system/test_production_deployment.py\", line 40, in setUpClass\n    cls.test_config = DeploymentConfig(\n                      ^^^^^^^^^^^^^^^^^\nTypeError: DeploymentConfig.__init__() missing 1 required positional argument: 'monitoring_enabled'\n"}, {"test": "setUpClass (__main__.TestDeploymentScript)", "error": "Traceback (most recent call last):\n  File \"/Users/<USER>/python/training/lotto/lottery_prediction_system/test_production_deployment.py\", line 40, in setUpClass\n    cls.test_config = DeploymentConfig(\n                      ^^^^^^^^^^^^^^^^^\nTypeError: DeploymentConfig.__init__() missing 1 required positional argument: 'monitoring_enabled'\n"}, {"test": "setUpClass (__main__.TestIntegration)", "error": "Traceback (most recent call last):\n  File \"/Users/<USER>/python/training/lotto/lottery_prediction_system/test_production_deployment.py\", line 40, in setUpClass\n    cls.test_config = DeploymentConfig(\n                      ^^^^^^^^^^^^^^^^^\nTypeError: DeploymentConfig.__init__() missing 1 required positional argument: 'monitoring_enabled'\n"}]}}