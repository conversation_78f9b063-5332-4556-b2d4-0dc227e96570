#!/usr/bin/env python3
"""
增強版多算法集成預測系統測試
Phase 2 完整版測試
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import DBManager
from prediction.enhanced_multi_algorithm_predictor import EnhancedMultiAlgorithmPredictor
import logging
import numpy as np
import time
from datetime import datetime

# 設置日誌
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

def test_enhanced_multi_algorithm():
    """測試增強版多算法集成預測器"""
    
    print("🚀 Phase 2 增強版多算法集成預測系統測試")
    print("=" * 80)
    
    # 初始化數據庫管理器
    db_manager = DBManager()
    
    # 載入威力彩歷史數據
    print("📊 載入威力彩歷史數據...")
    df = db_manager.load_lottery_data('powercolor')
    
    if df.empty:
        print("❌ 無法載入歷史數據")
        return False
    
    print(f"✅ 載入完成，共 {len(df)} 筆歷史記錄")
    
    # 使用最近100期數據進行測試
    test_data = df.tail(100)
    
    # 初始化增強版多算法預測器
    print("\n🧠 初始化增強版多算法集成預測器...")
    start_time = time.time()
    predictor = EnhancedMultiAlgorithmPredictor('powercolor', db_manager)
    init_time = time.time() - start_time
    print(f"✅ 初始化完成 (耗時: {init_time:.2f}秒)")
    
    # 測試各項功能
    print("\n📈 測試增強版核心功能...")
    
    # 1. 測試增強版頻率預測
    print("  1. 增強版頻率預測...")
    try:
        freq_result = predictor.predict_with_enhanced_frequency(test_data)
        if freq_result:
            print(f"     ✅ 預測結果: {freq_result['main_numbers']} (信心度: {freq_result['confidence']:.1f}%)")
        else:
            print("     ❌ 預測失敗")
    except Exception as e:
        print(f"     ❌ 預測失敗: {e}")
    
    # 2. 測試增強版模式預測
    print("  2. 增強版模式預測...")
    try:
        pattern_result = predictor.predict_with_enhanced_pattern(test_data)
        if pattern_result:
            print(f"     ✅ 預測結果: {pattern_result['main_numbers']} (信心度: {pattern_result['confidence']:.1f}%)")
        else:
            print("     ❌ 預測失敗")
    except Exception as e:
        print(f"     ❌ 預測失敗: {e}")
    
    # 3. 測試趨勢分析
    print("  3. 趨勢分析預測...")
    try:
        trend_result = predictor.predict_with_trend_analysis(test_data)
        if trend_result:
            print(f"     ✅ 預測結果: {trend_result['main_numbers']} (信心度: {trend_result['confidence']:.1f}%)")
        else:
            print("     ❌ 預測失敗")
    except Exception as e:
        print(f"     ❌ 預測失敗: {e}")
    
    # 4. 測試神經網絡預測
    print("  4. 神經網絡預測...")
    try:
        nn_result = predictor.predict_with_neural_network(test_data)
        if nn_result:
            print(f"     ✅ 預測結果: {nn_result['main_numbers']} (信心度: {nn_result['confidence']:.1f}%)")
        else:
            print("     ❌ 預測失敗")
    except Exception as e:
        print(f"     ❌ 預測失敗: {e}")
    
    # 5. 測試增強版集成預測 (核心功能)
    print("  5. 增強版集成預測...")
    try:
        start_time = time.time()
        ensemble_result = predictor.enhanced_ensemble_predict(test_data, ensemble_size=10)
        prediction_time = time.time() - start_time
        
        if ensemble_result:
            print(f"     ✅ 集成預測: {ensemble_result['main_numbers']} (信心度: {ensemble_result['confidence']:.1f}%)")
            print(f"     ✅ 參與算法數: {len(ensemble_result.get('individual_predictions', []))}")
            print(f"     ✅ 多樣性獎勵: +{ensemble_result.get('diversity_bonus', 0):.1f}%")
            print(f"     ✅ 預測耗時: {prediction_time:.2f}秒")
            
            # 顯示算法權重
            weights = ensemble_result.get('algorithm_weights', {})
            print("     算法權重:")
            for method, weight in weights.items():
                print(f"       {method}: {weight:.3f}")
        else:
            print("     ❌ 集成預測失敗")
    except Exception as e:
        print(f"     ❌ 集成預測失敗: {e}")
    
    return True

def test_enhanced_backtest():
    """增強版回測測試"""
    
    print("\n\n📈 增強版回測測試")
    print("=" * 80)
    
    # 初始化數據庫管理器
    db_manager = DBManager()
    
    # 載入威力彩歷史數據
    df = db_manager.load_lottery_data('powercolor')
    
    if df.empty:
        print("❌ 無法載入歷史數據")
        return False
    
    print(f"✅ 載入完成，共 {len(df)} 筆歷史記錄")
    
    # 初始化預測器
    predictor = EnhancedMultiAlgorithmPredictor('powercolor', db_manager)
    
    # 回測最近5期
    test_periods = 5
    print(f"\n🎯 開始增強版回測 (最近{test_periods}期)")
    print("-" * 60)
    
    results = []
    total_prediction_time = 0
    
    for i in range(test_periods):
        print(f"\n回測第 {i+1}/{test_periods} 期...")
        
        # 使用前N-i期數據進行預測
        train_data = df.iloc[:-i-1] if i > 0 else df.iloc[:-1]
        actual_data = df.iloc[-i-1]
        
        # 使用最近80期數據訓練
        train_data = train_data.tail(80)
        
        try:
            # 進行預測
            start_time = time.time()
            prediction = predictor.enhanced_ensemble_predict(train_data, ensemble_size=8)
            prediction_time = time.time() - start_time
            total_prediction_time += prediction_time
            
            if prediction:
                # 獲取實際結果
                actual_main = [
                    actual_data['Anumber1'], actual_data['Anumber2'], actual_data['Anumber3'],
                    actual_data['Anumber4'], actual_data['Anumber5'], actual_data['Anumber6']
                ]
                
                # 計算匹配數
                pred_main = prediction.get('main_numbers', [])
                match_count = len(set(pred_main).intersection(set(actual_main)))
                
                result = {
                    'period': actual_data['Period'],
                    'predicted_main': pred_main,
                    'actual_main': actual_main,
                    'match_count': match_count,
                    'confidence': prediction.get('confidence', 0),
                    'diversity_bonus': prediction.get('diversity_bonus', 0),
                    'algorithms_used': len(prediction.get('individual_predictions', [])),
                    'prediction_time': prediction_time
                }
                
                results.append(result)
                
                print(f"期數 {actual_data['Period']}: 預測 {pred_main} | 實際 {actual_main}")
                print(f"匹配 {match_count}個 | 信心度 {prediction.get('confidence', 0):.1f}% | 耗時 {prediction_time:.2f}s")
                
                # 更新算法性能（模擬）
                predictor.update_algorithm_performance(actual_main, prediction)
                
            else:
                print(f"期數 {actual_data['Period']}: 預測失敗")
                
        except Exception as e:
            print(f"❌ 回測期數 {actual_data['Period']} 失敗: {e}")
    
    # 分析回測結果
    if results:
        print("\n📊 增強版回測結果分析:")
        print("=" * 60)
        
        match_counts = [r['match_count'] for r in results]
        confidences = [r['confidence'] for r in results]
        diversity_bonuses = [r['diversity_bonus'] for r in results]
        prediction_times = [r['prediction_time'] for r in results]
        
        print(f"平均匹配數: {np.mean(match_counts):.2f}")
        print(f"平均信心度: {np.mean(confidences):.1f}%")
        print(f"平均多樣性獎勵: {np.mean(diversity_bonuses):.1f}%")
        print(f"最高匹配數: {max(match_counts)}")
        print(f"平均預測耗時: {np.mean(prediction_times):.2f}秒")
        print(f"總預測耗時: {total_prediction_time:.2f}秒")
        
        # 匹配分布
        print("\n匹配分布:")
        for i in range(max(match_counts) + 1):
            count = match_counts.count(i)
            percentage = count / len(match_counts) * 100
            print(f"  {i}個匹配: {count}次 ({percentage:.1f}%)")
        
        # 算法權重演變
        print(f"\n最終算法權重:")
        for method, weight in predictor.algorithm_weights.items():
            stability = predictor.algorithm_stability.get(method, 0.5)
            print(f"  {method}: {weight:.3f} (穩定性: {stability:.3f})")
        
        # 性能評估
        success_rate = sum(1 for r in results if r['match_count'] >= 2) / len(results) * 100
        print(f"\n成功率 (≥2個匹配): {success_rate:.1f}%")
        
        # 置信度vs準確率分析
        high_confidence_results = [r for r in results if r['confidence'] >= 80]
        if high_confidence_results:
            high_conf_avg_match = np.mean([r['match_count'] for r in high_confidence_results])
            print(f"高信心度預測(≥80%)平均匹配數: {high_conf_avg_match:.2f}")
    
    return True

def test_algorithm_stability():
    """測試算法穩定性"""
    
    print("\n\n🔬 算法穩定性測試")
    print("=" * 80)
    
    # 初始化數據庫管理器
    db_manager = DBManager()
    
    # 載入威力彩歷史數據
    df = db_manager.load_lottery_data('powercolor')
    
    if df.empty:
        print("❌ 無法載入歷史數據")
        return False
    
    # 使用最近120期數據
    test_data = df.tail(120)
    
    # 初始化預測器
    predictor = EnhancedMultiAlgorithmPredictor('powercolor', db_manager)
    
    print("\n📊 多次預測穩定性測試:")
    print("-" * 60)
    
    # 進行多次預測測試穩定性
    predictions = []
    for i in range(5):
        try:
            result = predictor.enhanced_ensemble_predict(test_data, ensemble_size=6)
            if result:
                predictions.append(result)
                print(f"預測 {i+1}: {result['main_numbers']} (信心度: {result['confidence']:.1f}%)")
        except Exception as e:
            print(f"預測 {i+1} 失敗: {e}")
    
    if len(predictions) >= 2:
        # 分析預測一致性
        all_numbers = []
        confidences = []
        
        for pred in predictions:
            all_numbers.extend(pred['main_numbers'])
            confidences.append(pred['confidence'])
        
        # 號碼重複率
        from collections import Counter
        number_counts = Counter(all_numbers)
        total_predictions = len(predictions) * 6
        consistency_rate = sum(count for count in number_counts.values() if count > 1) / total_predictions
        
        print(f"\n穩定性分析:")
        print(f"  號碼一致性: {consistency_rate:.2f}")
        print(f"  信心度標準差: {np.std(confidences):.2f}")
        print(f"  最常出現號碼: {[num for num, count in number_counts.most_common(6)]}")
    
    return True

def test_performance_optimization():
    """測試性能優化"""
    
    print("\n\n⚡ 性能優化測試")
    print("=" * 80)
    
    # 初始化數據庫管理器
    db_manager = DBManager()
    
    # 載入威力彩歷史數據
    df = db_manager.load_lottery_data('powercolor')
    
    if df.empty:
        print("❌ 無法載入歷史數據")
        return False
    
    # 初始化預測器
    predictor = EnhancedMultiAlgorithmPredictor('powercolor', db_manager)
    
    # 測試不同數據量的預測性能
    data_sizes = [50, 100, 200]
    
    print("\n📈 不同數據量預測性能:")
    print("-" * 60)
    
    for size in data_sizes:
        test_data = df.tail(size)
        
        start_time = time.time()
        result = predictor.enhanced_ensemble_predict(test_data, ensemble_size=5)
        end_time = time.time()
        
        if result:
            print(f"數據量 {size}: {result['main_numbers']} | "
                  f"信心度 {result['confidence']:.1f}% | "
                  f"耗時 {end_time - start_time:.2f}s")
        else:
            print(f"數據量 {size}: 預測失敗")
    
    return True

def main():
    """主測試函數"""
    
    print("🚀 Phase 2 增強版多算法集成預測系統完整測試")
    print("=" * 90)
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 90)
    
    # 測試序列
    test_results = []
    
    # 1. 增強版多算法測試
    print("\n1. 增強版多算法測試...")
    test_results.append(test_enhanced_multi_algorithm())
    
    # 2. 增強版回測
    print("\n2. 增強版回測測試...")
    test_results.append(test_enhanced_backtest())
    
    # 3. 算法穩定性測試
    print("\n3. 算法穩定性測試...")
    test_results.append(test_algorithm_stability())
    
    # 4. 性能優化測試
    print("\n4. 性能優化測試...")
    test_results.append(test_performance_optimization())
    
    # 總結
    print("\n\n🎯 Phase 2 增強版測試總結")
    print("=" * 90)
    
    test_names = [
        "增強版多算法測試",
        "增強版回測測試", 
        "算法穩定性測試",
        "性能優化測試"
    ]
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"總測試數: {total_tests}")
    print(f"通過測試: {passed_tests}")
    print(f"測試通過率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n各項測試結果:")
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {i+1}. {name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有測試通過！Phase 2 增強版多算法集成預測系統運行正常")
        print("📋 Phase 2 主要改進:")
        print("  • ✅ 修復頻率和模式預測器初始化問題")
        print("  • ✅ 添加趨勢分析算法")
        print("  • ✅ 實現神經網絡預測器")
        print("  • ✅ 智能權重動態調整機制")
        print("  • ✅ 增強版集成投票機制")
        print("  • ✅ 完善的性能監控和穩定性評估")
        print("  • ✅ 平衡的號碼組合選擇策略")
        print("  • ✅ 多樣性獎勵機制")
    else:
        print(f"\n⚠️  {total_tests - passed_tests} 項測試失敗，需要進一步優化")
    
    print("=" * 90)

if __name__ == "__main__":
    main()