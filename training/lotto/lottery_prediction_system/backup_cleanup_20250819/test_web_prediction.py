#!/usr/bin/env python3
"""
Web预测功能测试脚本
"""

import sys
import os
import json
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def convert_numpy_types(data):
    """递归转换numpy类型为Python原生类型"""
    if isinstance(data, dict):
        return {key: convert_numpy_types(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [convert_numpy_types(item) for item in data]
    elif isinstance(data, np.ndarray):
        return data.tolist()
    elif isinstance(data, np.integer):
        return int(data)
    elif isinstance(data, np.floating):
        return float(data)
    elif hasattr(data, 'dtype'):  # 其他numpy类型
        if hasattr(data, 'tolist'):
            return data.tolist()
        elif 'int' in str(data.dtype):
            return int(data)
        elif 'float' in str(data.dtype):
            return float(data)
        else:
            return str(data)
    else:
        return data

def create_mock_prediction():
    """创建模拟预测结果"""
    # 模拟AI生成的预测结果（包含numpy类型）
    prediction = {
        'lottery_type': 'powercolor',
        'lottery_name': '威力彩',
        'prediction_date': '2025-08-18',
        'main_numbers': np.array([8, 13, 16, 26, 35, 37], dtype=np.int64),
        'special_number': np.int64(5),
        'strategy': 'balanced',
        'confidence': np.float64(92.5),
        'expected_hit_rate': np.float64(0.35),
        'confidence_level': '高',
        'multi_groups': 5,
        'timestamp': '2025-08-18T15:07:25.685Z'
    }
    
    return prediction

def test_json_conversion():
    """测试JSON转换"""
    print("🧪 测试numpy类型转换...")
    
    # 创建包含numpy类型的数据
    prediction = create_mock_prediction()
    
    # 显示原始数据类型
    print(f"原始数据:")
    print(f"  main_numbers: {type(prediction['main_numbers'])} = {prediction['main_numbers']}")
    print(f"  special_number: {type(prediction['special_number'])} = {prediction['special_number']}")
    print(f"  confidence: {type(prediction['confidence'])} = {prediction['confidence']}")
    
    # 转换numpy类型
    clean_prediction = convert_numpy_types(prediction)
    
    # 显示转换后的数据类型
    print(f"\n转换后数据:")
    print(f"  main_numbers: {type(clean_prediction['main_numbers'])} = {clean_prediction['main_numbers']}")
    print(f"  special_number: {type(clean_prediction['special_number'])} = {clean_prediction['special_number']}")
    print(f"  confidence: {type(clean_prediction['confidence'])} = {clean_prediction['confidence']}")
    
    # 测试JSON序列化
    try:
        json_str = json.dumps(clean_prediction, ensure_ascii=False, indent=2)
        print(f"\n✅ JSON序列化成功！")
        print(f"JSON预览（前200字符）:")
        print(json_str[:200] + "...")
        
        # 测试反序列化
        parsed = json.loads(json_str)
        print(f"\n✅ JSON反序列化成功！")
        print(f"反序列化结果:")
        print(f"  威力彩号码: {parsed['main_numbers']}")
        print(f"  特别号: {parsed['special_number']}")
        print(f"  信心度: {parsed['confidence']}%")
        
        return True
        
    except Exception as e:
        print(f"\n❌ JSON序列化失败: {e}")
        return False

def test_web_response_format():
    """测试Web响应格式"""
    print(f"\n🌐 测试Web响应格式...")
    
    prediction = create_mock_prediction()
    clean_prediction = convert_numpy_types(prediction)
    
    # 构建Web API响应格式
    response = {
        'success': True,
        'lottery_name': clean_prediction['lottery_name'],
        'lottery_type': clean_prediction['lottery_type'],
        'main_numbers': clean_prediction['main_numbers'],
        'special_number': clean_prediction['special_number'],
        'confidence': round(float(clean_prediction['confidence']), 1),
        'expected_hit_rate': round(float(clean_prediction['expected_hit_rate']) * 100, 2),
        'confidence_level': clean_prediction['confidence_level'],
        'strategy': clean_prediction['strategy'],
        'prediction_date': clean_prediction['prediction_date'],
        'multi_groups': clean_prediction['multi_groups']
    }
    
    # 添加投注建议
    if clean_prediction['confidence_level'] == '高':
        response['suggestion'] = '信心度高，建议适度增加投注'
        response['suggestion_type'] = 'success'
    elif clean_prediction['confidence_level'] == '中等':
        response['suggestion'] = '信心度中等，建议正常投注'
        response['suggestion_type'] = 'warning'
    else:
        response['suggestion'] = '信心度较低，建议谨慎投注'
        response['suggestion_type'] = 'danger'
    
    # 测试完整响应的JSON序列化
    try:
        json_str = json.dumps(response, ensure_ascii=False, indent=2)
        print(f"✅ Web响应格式JSON序列化成功！")
        print(f"完整响应:")
        print(json_str)
        return True
        
    except Exception as e:
        print(f"❌ Web响应格式JSON序列化失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 Web预测功能JSON序列化测试")
    print("=" * 60)
    
    # 测试基本转换
    test1 = test_json_conversion()
    
    # 测试Web响应格式
    test2 = test_web_response_format()
    
    print(f"\n" + "=" * 60)
    print(f"🎉 测试总结")
    print(f"=" * 60)
    print(f"基本转换测试: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"Web响应测试: {'✅ 通过' if test2 else '❌ 失败'}")
    
    if test1 and test2:
        print(f"\n🎊 所有测试通过！Web预测功能的JSON序列化已完全修复。")
        print(f"💡 可以安全地在Web界面中显示预测结果。")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步调试。")

if __name__ == "__main__":
    main()