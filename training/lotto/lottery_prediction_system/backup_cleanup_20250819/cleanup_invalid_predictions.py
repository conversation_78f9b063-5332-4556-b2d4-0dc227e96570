"""
清理無效預測記錄
刪除第一區全為0、第二區為空或其他無效的預測記錄
"""

import sqlite3
import logging
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/cleanup_predictions_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def cleanup_invalid_predictions():
    """清理無效的預測記錄"""
    
    try:
        conn = sqlite3.connect('data/lottery_data.db')
        cursor = conn.cursor()
        
        print("=== 清理無效預測記錄 ===")
        
        # 1. 檢查威力彩無效預測
        print("\n1. 檢查威力彩無效預測...")
        
        # 查詢無效記錄
        cursor.execute('''
        SELECT COUNT(*) FROM PowerColorPredictions 
        WHERE (PredA1 = 0 OR PredA1 IS NULL) 
           AND (PredA2 = 0 OR PredA2 IS NULL) 
           AND (PredA3 = 0 OR PredA3 IS NULL) 
           AND (PredA4 = 0 OR PredA4 IS NULL) 
           AND (PredA5 = 0 OR PredA5 IS NULL) 
           AND (PredA6 = 0 OR PredA6 IS NULL)
        ''')
        
        invalid_count = cursor.fetchone()[0]
        print(f"找到 {invalid_count} 筆威力彩無效預測記錄（第一區全為0或空）")
        
        if invalid_count > 0:
            # 顯示一些無效記錄的詳情
            cursor.execute('''
            SELECT Period, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredS, 
                   PredictionDate, PredictionMethod
            FROM PowerColorPredictions 
            WHERE (PredA1 = 0 OR PredA1 IS NULL) 
               AND (PredA2 = 0 OR PredA2 IS NULL) 
               AND (PredA3 = 0 OR PredA3 IS NULL) 
               AND (PredA4 = 0 OR PredA4 IS NULL) 
               AND (PredA5 = 0 OR PredA5 IS NULL) 
               AND (PredA6 = 0 OR PredA6 IS NULL)
            ORDER BY PredictionDate DESC
            LIMIT 5
            ''')
            
            sample_records = cursor.fetchall()
            print("\n無效記錄樣本:")
            for record in sample_records:
                period, a1, a2, a3, a4, a5, a6, s, date, method = record
                print(f"  期數: {period}, 第一區: {a1}-{a2}-{a3}-{a4}-{a5}-{a6}, 第二區: {s}, 時間: {date}, 方法: {method}")
        
        # 2. 檢查大樂透無效預測
        print("\n2. 檢查大樂透無效預測...")
        
        cursor.execute('''
        SELECT COUNT(*) FROM Lotto649Predictions 
        WHERE (PredA1 = 0 OR PredA1 IS NULL) 
           AND (PredA2 = 0 OR PredA2 IS NULL) 
           AND (PredA3 = 0 OR PredA3 IS NULL) 
           AND (PredA4 = 0 OR PredA4 IS NULL) 
           AND (PredA5 = 0 OR PredA5 IS NULL) 
           AND (PredA6 = 0 OR PredA6 IS NULL)
        ''')
        
        invalid_lotto_count = cursor.fetchone()[0]
        print(f"找到 {invalid_lotto_count} 筆大樂透無效預測記錄")
        
        # 3. 檢查今彩539無效預測
        print("\n3. 檢查今彩539無效預測...")
        
        cursor.execute('''
        SELECT COUNT(*) FROM DailyCashPredictions 
        WHERE (PredA1 = 0 OR PredA1 IS NULL) 
           AND (PredA2 = 0 OR PredA2 IS NULL) 
           AND (PredA3 = 0 OR PredA3 IS NULL) 
           AND (PredA4 = 0 OR PredA4 IS NULL) 
           AND (PredA5 = 0 OR PredA5 IS NULL)
        ''')
        
        invalid_daily_count = cursor.fetchone()[0]
        print(f"找到 {invalid_daily_count} 筆今彩539無效預測記錄")
        
        # 4. 詢問是否要刪除
        total_invalid = invalid_count + invalid_lotto_count + invalid_daily_count
        print(f"\n總共找到 {total_invalid} 筆無效預測記錄")
        
        if total_invalid > 0:
            response = input("\n是否要刪除這些無效記錄？(y/N): ").strip().lower()
            
            if response == 'y':
                print("\n開始刪除無效記錄...")
                
                # 刪除威力彩無效記錄
                if invalid_count > 0:
                    cursor.execute('''
                    DELETE FROM PowerColorPredictions 
                    WHERE (PredA1 = 0 OR PredA1 IS NULL) 
                       AND (PredA2 = 0 OR PredA2 IS NULL) 
                       AND (PredA3 = 0 OR PredA3 IS NULL) 
                       AND (PredA4 = 0 OR PredA4 IS NULL) 
                       AND (PredA5 = 0 OR PredA5 IS NULL) 
                       AND (PredA6 = 0 OR PredA6 IS NULL)
                    ''')
                    print(f"已刪除 {cursor.rowcount} 筆威力彩無效記錄")
                
                # 刪除大樂透無效記錄
                if invalid_lotto_count > 0:
                    cursor.execute('''
                    DELETE FROM Lotto649Predictions 
                    WHERE (PredA1 = 0 OR PredA1 IS NULL) 
                       AND (PredA2 = 0 OR PredA2 IS NULL) 
                       AND (PredA3 = 0 OR PredA3 IS NULL) 
                       AND (PredA4 = 0 OR PredA4 IS NULL) 
                       AND (PredA5 = 0 OR PredA5 IS NULL) 
                       AND (PredA6 = 0 OR PredA6 IS NULL)
                    ''')
                    print(f"已刪除 {cursor.rowcount} 筆大樂透無效記錄")
                
                # 刪除今彩539無效記錄
                if invalid_daily_count > 0:
                    cursor.execute('''
                    DELETE FROM DailyCashPredictions 
                    WHERE (PredA1 = 0 OR PredA1 IS NULL) 
                       AND (PredA2 = 0 OR PredA2 IS NULL) 
                       AND (PredA3 = 0 OR PredA3 IS NULL) 
                       AND (PredA4 = 0 OR PredA4 IS NULL) 
                       AND (PredA5 = 0 OR PredA5 IS NULL)
                    ''')
                    print(f"已刪除 {cursor.rowcount} 筆今彩539無效記錄")
                
                # 提交更改
                conn.commit()
                print("\n✅ 無效記錄清理完成！")
                
                # 驗證清理結果
                print("\n驗證清理結果...")
                cursor.execute('''
                SELECT COUNT(*) FROM PowerColorPredictions 
                WHERE (PredA1 = 0 OR PredA1 IS NULL) 
                   AND (PredA2 = 0 OR PredA2 IS NULL) 
                   AND (PredA3 = 0 OR PredA3 IS NULL) 
                   AND (PredA4 = 0 OR PredA4 IS NULL) 
                   AND (PredA5 = 0 OR PredA5 IS NULL) 
                   AND (PredA6 = 0 OR PredA6 IS NULL)
                ''')
                remaining_invalid = cursor.fetchone()[0]
                print(f"威力彩剩餘無效記錄: {remaining_invalid}")
                
            else:
                print("取消刪除操作")
        else:
            print("沒有找到無效記錄")
        
        # 5. 檢查特定期數 114000050 的記錄
        print(f"\n5. 檢查期數 114000050 的記錄...")
        cursor.execute('''
        SELECT Period, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredS, 
               PredictionDate, PredictionMethod
        FROM PowerColorPredictions 
        WHERE Period = '114000050'
        ORDER BY PredictionDate DESC
        ''')
        
        period_records = cursor.fetchall()
        if period_records:
            print(f"期數 114000050 還有 {len(period_records)} 筆記錄:")
            for i, record in enumerate(period_records):
                period, a1, a2, a3, a4, a5, a6, s, date, method = record
                status = "有效" if not all(x in [0, None] for x in [a1, a2, a3, a4, a5, a6]) else "無效"
                print(f"  {i+1}. {a1}-{a2}-{a3}-{a4}-{a5}-{a6} (第二區:{s}) [{status}] - {method}")
        else:
            print("期數 114000050 沒有記錄")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"清理過程中出錯: {str(e)}")
        print(f"錯誤: {e}")

def cleanup_specific_period_invalid_records(period):
    """清理特定期數的無效記錄"""
    
    try:
        conn = sqlite3.connect('data/lottery_data.db')
        cursor = conn.cursor()
        
        print(f"\n=== 清理期數 {period} 的無效記錄 ===")
        
        # 查詢該期數的無效記錄
        cursor.execute('''
        SELECT ID, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredS, 
               PredictionDate, PredictionMethod
        FROM PowerColorPredictions 
        WHERE Period = ? 
           AND ((PredA1 = 0 OR PredA1 IS NULL) 
           AND (PredA2 = 0 OR PredA2 IS NULL) 
           AND (PredA3 = 0 OR PredA3 IS NULL) 
           AND (PredA4 = 0 OR PredA4 IS NULL) 
           AND (PredA5 = 0 OR PredA5 IS NULL) 
           AND (PredA6 = 0 OR PredA6 IS NULL))
        ''', (period,))
        
        invalid_records = cursor.fetchall()
        
        if invalid_records:
            print(f"找到 {len(invalid_records)} 筆期數 {period} 的無效記錄:")
            for record in invalid_records:
                id_val, a1, a2, a3, a4, a5, a6, s, date, method = record
                print(f"  ID: {id_val}, 號碼: {a1}-{a2}-{a3}-{a4}-{a5}-{a6}, 第二區: {s}, 方法: {method}")
            
            response = input(f"\n是否要刪除期數 {period} 的這些無效記錄？(y/N): ").strip().lower()
            
            if response == 'y':
                cursor.execute('''
                DELETE FROM PowerColorPredictions 
                WHERE Period = ? 
                   AND ((PredA1 = 0 OR PredA1 IS NULL) 
                   AND (PredA2 = 0 OR PredA2 IS NULL) 
                   AND (PredA3 = 0 OR PredA3 IS NULL) 
                   AND (PredA4 = 0 OR PredA4 IS NULL) 
                   AND (PredA5 = 0 OR PredA5 IS NULL) 
                   AND (PredA6 = 0 OR PredA6 IS NULL))
                ''', (period,))
                
                deleted_count = cursor.rowcount
                conn.commit()
                print(f"✅ 已刪除 {deleted_count} 筆期數 {period} 的無效記錄")
            else:
                print("取消刪除操作")
        else:
            print(f"期數 {period} 沒有無效記錄")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"清理期數 {period} 時出錯: {str(e)}")
        print(f"錯誤: {e}")

if __name__ == "__main__":
    # 清理所有無效記錄
    cleanup_invalid_predictions()
    
    # 特別清理期數 114000050
    cleanup_specific_period_invalid_records('114000050')
