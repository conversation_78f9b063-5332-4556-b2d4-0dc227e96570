#!/usr/bin/env python3
"""
修復日誌路徑問題
"""

import os
from pathlib import Path

def fix_log_paths():
    """修復日誌路徑問題"""
    
    # 確保目錄存在
    directories = [
        'logs',
        'phase3/logs', 
        'data',
        'backups',
        'reports',
        'exports'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ 創建目錄: {directory}")
    
    # 創建必要的日誌文件
    log_files = [
        'logs/db_manager.log',
        'phase3/logs/db_manager.log',
        'logs/api.log',
        'logs/web.log',
        'logs/system.log',
        'logs/production.log'
    ]
    
    for log_file in log_files:
        Path(log_file).touch()
        print(f"✅ 創建日誌文件: {log_file}")
    
    # 初始化數據庫
    try:
        from data.db_manager import DatabaseManager
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        print("✅ 數據庫初始化完成")
    except Exception as e:
        print(f"⚠️ 數據庫初始化警告: {e}")
    
    print("\n🎉 系統修復完成！現在可以啟動 Web 服務器了。")
    print("執行: python phase3/start_web_server.py")

if __name__ == "__main__":
    fix_log_paths()