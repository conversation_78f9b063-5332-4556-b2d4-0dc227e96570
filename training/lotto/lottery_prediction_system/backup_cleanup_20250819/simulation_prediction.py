#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模擬預測腳本
使用前一期的特徵對今年已開獎的期數進行模擬預測，並將結果寫入預測記錄中
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import json
import traceback

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import DBManager
from data.feature_engineering import FeatureEngineer
from prediction.lottery_predictor import LotteryPredictor
from config_manager import ConfigManager

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('simulation_prediction.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SimulationPredictor:
    """模擬預測器"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.db_manager = DBManager(config_manager=self.config_manager)
        self.feature_engineer = FeatureEngineer()
        self.predictor = LotteryPredictor()
        
    def get_2024_periods(self, lottery_type='powercolor'):
        """獲取2024年（民國113年）的所有期號"""
        try:
            # 獲取所有期號
            all_periods = self.db_manager.get_periods_list(lottery_type, limit=1000)
            
            # 篩選2024年的期號（民國113年）
            year_2024_periods = []
            for period in all_periods:
                period_str = str(period)
                if len(period_str) >= 7:
                    year_part = period_str[:3]
                    if year_part == '113':  # 民國113年 = 西元2024年
                        year_2024_periods.append(period)
                        
            logger.info(f"找到 {len(year_2024_periods)} 個2024年的{self._get_lottery_name(lottery_type)}期號")
            return sorted(year_2024_periods)
            
        except Exception as e:
            logger.error(f"獲取2024年期號時出錯: {str(e)}")
            return []
    
    def _get_lottery_name(self, lottery_type):
        """根據彩票類型獲取中文名稱"""
        name_map = {
            'powercolor': '威力彩',
            'lotto649': '大樂透',
            'dailycash': '今彩539'
        }
        return name_map.get(lottery_type.lower(), lottery_type)
    
    def simulate_prediction_for_period(self, target_period, lottery_type='powercolor'):
        """對指定期號進行模擬預測"""
        try:
            logger.info(f"開始對期號 {target_period} 進行模擬預測")
            
            # 獲取目標期號的實際開獎結果
            actual_result = self.db_manager.get_period_data(target_period, lottery_type)
            if not actual_result:
                logger.warning(f"期號 {target_period} 沒有開獎資料，跳過")
                return None
                
            # 獲取目標期號之前的歷史資料（用於特徵工程）
            # 使用目標期號前90天的資料作為訓練資料
            target_date = datetime.strptime(actual_result['date'], '%Y-%m-%d')
            start_date = target_date - timedelta(days=90)
            
            # 獲取所有歷史資料
            all_data = self.db_manager.load_lottery_data(lottery_type=lottery_type)
            
            # 篩選訓練資料（目標期號之前的90天）
            end_training_date = target_date - timedelta(days=1)  # 不包含目標期號當天
            training_data = all_data[
                (all_data['Sdate'] >= start_date) & 
                (all_data['Sdate'] <= end_training_date)
            ].copy()
            
            if training_data.empty:
                logger.warning(f"期號 {target_period} 沒有足夠的歷史資料進行預測")
                return None
                
            logger.info(f"使用 {len(training_data)} 筆歷史資料進行特徵工程")
            
            # 進行特徵工程
            features_df = self.feature_engineer.create_basic_features(
                training_data, 
                lottery_type=lottery_type
            )
            
            if features_df.empty:
                logger.warning(f"期號 {target_period} 特徵工程失敗")
                return None
                
            # 使用最後一期的特徵進行預測
            last_features = features_df.iloc[-1:].copy()
            
            # 移除目標變量（如果存在）
            target_columns = [col for col in last_features.columns if col.startswith('Target_')]
            for col in target_columns:
                if col in last_features.columns:
                    last_features = last_features.drop(columns=[col])
            
            logger.info(f"使用 {len(last_features.columns)} 個特徵進行預測")
            
            # 進行預測
            prediction_results = self.predictor.predict(
                df=training_data,
                features=last_features,
                lottery_type=lottery_type,
                prediction_methods=['ml']  # 使用機器學習方法
            )
            
            if not prediction_results:
                logger.warning(f"期號 {target_period} 預測失敗")
                return None
                
            # 處理預測結果
            predicted_numbers = []
            special_predicted = None
            
            if lottery_type.lower() == 'powercolor':
                predicted_numbers = prediction_results.get('第一區', [])
                special_predicted = prediction_results.get('第二區')
            elif lottery_type.lower() == 'lotto649':
                predicted_numbers = prediction_results.get('第一區', [])
                special_predicted = prediction_results.get('特別號')
            elif lottery_type.lower() == 'dailycash':
                predicted_numbers = prediction_results.get('號碼', [])
            
            # 準備預測結果
            prediction_data = {
                'period': target_period,
                'lottery_type': lottery_type,
                'prediction_date': (target_date - timedelta(days=1)).strftime('%Y-%m-%d'),
                'prediction_method': 'simulation_ml',
                'model_version': 'simulation_v1.0',
                'confidence': 0.5,  # 默認信心分數
                'predicted_numbers': predicted_numbers,
                'actual_numbers': actual_result['main_numbers'],
                'special_predicted': special_predicted,
                'special_actual': actual_result.get('special_number'),
                'training_data_size': len(training_data),
                'feature_count': len(last_features.columns)
            }
            
            # 計算準確度
            accuracy_info = self.calculate_accuracy(prediction_data, lottery_type)
            prediction_data.update(accuracy_info)
            
            # 詳細的預測結果日誌
            result_type = accuracy_info.get('prediction_result', '未知')
            matched = accuracy_info.get('matched_numbers', 0)
            total = accuracy_info.get('total_numbers', 0)
            
            logger.info(f"期號 {target_period} 模擬預測完成:")
            logger.info(f"  預測號碼: {predicted_numbers}")
            logger.info(f"  實際號碼: {actual_result['main_numbers']}")
            logger.info(f"  匹配結果: {matched}/{total} ({result_type})")
            
            if lottery_type.lower() in ['powercolor', 'lotto649'] and special_predicted is not None:
                special_actual = actual_result.get('special_number')
                special_match = accuracy_info.get('special_match', False)
                logger.info(f"  特別號: 預測={special_predicted}, 實際={special_actual}, 匹配={special_match}")
            
            return prediction_data
            
        except Exception as e:
            logger.error(f"模擬預測期號 {target_period} 時出錯: {str(e)}")
            logger.error(traceback.format_exc())
            return None
    
    def calculate_accuracy(self, prediction_data, lottery_type):
        """計算預測準確度"""
        try:
            predicted = prediction_data['predicted_numbers']
            actual = prediction_data['actual_numbers']
            
            if not predicted or not actual:
                return {'accuracy': 0, 'matched_numbers': 0, 'total_numbers': 0, 'prediction_result': '無效預測'}
            
            # 確保都是整數列表
            predicted = [int(x) for x in predicted if x is not None]
            actual = [int(x) for x in actual if x is not None]
            
            # 計算主號碼匹配數
            matched_main = len(set(predicted) & set(actual))
            total_main = len(actual)
            
            # 判斷預測結果類型
            prediction_result = self._classify_prediction_result(matched_main, lottery_type)
            
            # 成功預測的定義：完全預測正確（所有主號碼都中）
            is_successful = (matched_main == total_main)
            
            accuracy_info = {
                'matched_numbers': matched_main,
                'total_numbers': total_main,
                'accuracy': 1.0 if is_successful else 0.0,  # 只有完全正確才算成功
                'match_rate': matched_main / total_main if total_main > 0 else 0,  # 匹配率
                'prediction_result': prediction_result,
                'is_successful': is_successful
            }
            
            # 如果有特別號，也計算特別號準確度
            if lottery_type.lower() in ['powercolor', 'lotto649']:
                special_predicted = prediction_data.get('special_predicted')
                special_actual = prediction_data.get('special_actual')
                
                if special_predicted is not None and special_actual is not None:
                    special_match = int(special_predicted) == int(special_actual)
                    accuracy_info['special_match'] = special_match
                    
                    # 完全成功需要主號碼和特別號都正確
                    complete_success = is_successful and special_match
                    accuracy_info['complete_success'] = complete_success
                    accuracy_info['accuracy'] = 1.0 if complete_success else 0.0
            
            return accuracy_info
            
        except Exception as e:
            logger.error(f"計算準確度時出錯: {str(e)}")
            return {'accuracy': 0, 'matched_numbers': 0, 'total_numbers': 0, 'prediction_result': '計算錯誤'}
    
    def _classify_prediction_result(self, matched_numbers, lottery_type):
        """分類預測結果"""
        if lottery_type.lower() == 'powercolor':
            # 威力彩：6個主號碼
            if matched_numbers == 6:
                return '6中6（頭獎）'
            elif matched_numbers == 5:
                return '6中5'
            elif matched_numbers == 4:
                return '6中4'
            elif matched_numbers == 3:
                return '6中3'
            elif matched_numbers == 2:
                return '6中2'
            elif matched_numbers == 1:
                return '6中1'
            else:
                return '6不中（完全不中）'
        elif lottery_type.lower() == 'lotto649':
            # 大樂透：6個主號碼
            if matched_numbers == 6:
                return '6中6（頭獎）'
            elif matched_numbers == 5:
                return '6中5'
            elif matched_numbers == 4:
                return '6中4'
            elif matched_numbers == 3:
                return '6中3'
            elif matched_numbers == 2:
                return '6中2'
            elif matched_numbers == 1:
                return '6中1'
            else:
                return '6不中（完全不中）'
        elif lottery_type.lower() == 'dailycash':
            # 今彩539：5個號碼
            if matched_numbers == 5:
                return '5中5（頭獎）'
            elif matched_numbers == 4:
                return '5中4'
            elif matched_numbers == 3:
                return '5中3'
            elif matched_numbers == 2:
                return '5中2'
            elif matched_numbers == 1:
                return '5中1'
            else:
                return '5不中（完全不中）'
        else:
            return f'{matched_numbers}中'
    
    def save_simulation_result(self, prediction_data):
        """保存模擬預測結果到資料庫"""
        try:
            lottery_type = prediction_data['lottery_type']
            period = prediction_data['period']
            
            # 準備保存到資料庫的資料
            save_data = {
                'period': period,
                'prediction_date': prediction_data['prediction_date'],
                'prediction_method': prediction_data['prediction_method'],
                'model_version': prediction_data['model_version'],
                'confidence': prediction_data['confidence'],
                'predicted_numbers': prediction_data['predicted_numbers']
            }
            
            # 添加特別號（如果有）
            if 'special_predicted' in prediction_data and prediction_data['special_predicted'] is not None:
                save_data['special_number'] = prediction_data['special_predicted']
            
            # 添加元數據
            metadata = {
                'simulation': True,
                'actual_numbers': prediction_data['actual_numbers'],
                'accuracy': prediction_data.get('accuracy', 0),
                'matched_numbers': prediction_data.get('matched_numbers', 0),
                'training_data_size': prediction_data.get('training_data_size', 0),
                'feature_count': prediction_data.get('feature_count', 0)
            }
            
            if 'special_actual' in prediction_data:
                metadata['special_actual'] = prediction_data['special_actual']
            if 'special_match' in prediction_data:
                metadata['special_match'] = prediction_data['special_match']
            if 'total_accuracy' in prediction_data:
                metadata['total_accuracy'] = prediction_data['total_accuracy']
            
            save_data['metadata'] = metadata
            
            # 保存到資料庫
            success = self.db_manager.save_prediction(save_data, lottery_type)
            
            if success:
                logger.info(f"期號 {period} 的模擬預測結果已保存到資料庫")
            else:
                logger.error(f"期號 {period} 的模擬預測結果保存失敗")
                
            return success
            
        except Exception as e:
            logger.error(f"保存模擬預測結果時出錯: {str(e)}")
            return False
    
    def run_simulation(self, lottery_types=['powercolor', 'lotto649', 'dailycash']):
        """執行完整的模擬預測"""
        logger.info("開始執行模擬預測")
        
        total_predictions = 0
        successful_predictions = 0
        results_summary = {}
        
        for lottery_type in lottery_types:
            logger.info(f"\n開始處理 {self._get_lottery_name(lottery_type)}")
            
            # 獲取2024年的期號
            periods_2024 = self.get_2024_periods(lottery_type)
            
            if not periods_2024:
                logger.warning(f"{self._get_lottery_name(lottery_type)} 沒有2024年的期號資料")
                continue
            
            lottery_results = []
            
            for period in periods_2024:
                total_predictions += 1
                
                # 進行模擬預測
                prediction_result = self.simulate_prediction_for_period(period, lottery_type)
                
                if prediction_result:
                    # 保存結果
                    if self.save_simulation_result(prediction_result):
                        successful_predictions += 1
                        lottery_results.append(prediction_result)
                    
            # 統計該彩票類型的結果
            if lottery_results:
                accuracies = [r.get('accuracy', 0) for r in lottery_results]
                avg_accuracy = np.mean(accuracies)
                
                results_summary[lottery_type] = {
                    'total_periods': len(periods_2024),
                    'successful_predictions': len(lottery_results),
                    'average_accuracy': avg_accuracy,
                    'max_accuracy': max(accuracies),
                    'min_accuracy': min(accuracies)
                }
                
                logger.info(f"{self._get_lottery_name(lottery_type)} 模擬完成:")
                logger.info(f"  總期數: {len(periods_2024)}")
                logger.info(f"  成功預測: {len(lottery_results)}")
                logger.info(f"  平均準確度: {avg_accuracy:.2%}")
        
        # 統計6不中策略的總體表現
        total_six_no_match = 0
        all_results = []
        
        for lottery_type in lottery_types:
            periods_2024 = self.get_2024_periods(lottery_type)
            for period in periods_2024:
                # 獲取該期號的預測結果（從之前的處理中）
                prediction_result = self.simulate_prediction_for_period(period, lottery_type)
                if prediction_result:
                    all_results.append(prediction_result)
                    result_type = prediction_result.get('prediction_result', '')
                    if '6不中' in result_type or '完全不中' in result_type:
                        total_six_no_match += 1
        
        # 統計各種預測結果類型
        result_stats = {}
        for result in all_results:
            result_type = result.get('prediction_result', '未知')
            result_stats[result_type] = result_stats.get(result_type, 0) + 1
        
        # 更新結果摘要，增加6不中統計
        results_summary['overall_statistics'] = {
            'total_predictions': total_predictions,
            'successful_predictions': successful_predictions,
            'success_rate': successful_predictions/total_predictions if total_predictions > 0 else 0,
            'six_no_match_count': total_six_no_match,
            'six_no_match_rate': total_six_no_match / total_predictions if total_predictions > 0 else 0,
            'result_distribution': result_stats
        }
        
        # 輸出總結
        logger.info(f"\n模擬預測完成總結:")
        logger.info(f"總預測次數: {total_predictions}")
        logger.info(f"成功預測次數: {successful_predictions}")
        logger.info(f"成功率: {successful_predictions/total_predictions:.2%}" if total_predictions > 0 else "成功率: 0%")
        logger.info(f"\n【6不中策略統計】:")
        logger.info(f"6不中次數: {total_six_no_match}")
        logger.info(f"6不中比例: {total_six_no_match / total_predictions:.2%}" if total_predictions > 0 else "6不中比例: 0%")
        logger.info(f"\n預測結果分布:")
        for result_type, count in result_stats.items():
            percentage = count / total_predictions * 100 if total_predictions > 0 else 0
            logger.info(f"  {result_type}: {count}次 ({percentage:.1f}%)")
        
        # 保存結果摘要
        summary_file = f"simulation_results_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(results_summary, f, ensure_ascii=False, indent=2)
        
        logger.info(f"\n結果摘要已保存到: {summary_file}")
        
        return results_summary

def main():
    """主函數"""
    try:
        simulator = SimulationPredictor()
        results = simulator.run_simulation()
        
        print("\n=== 模擬預測結果摘要 ===")
        for lottery_type, stats in results.items():
            if lottery_type != 'overall_statistics':
                print(f"\n{simulator._get_lottery_name(lottery_type)}:")
                print(f"  總期數: {stats.get('total_periods', 0)}")
                print(f"  成功預測: {stats.get('successful_predictions', 0)}")
                print(f"  平均準確度: {stats.get('average_accuracy', 0):.2%}")
                print(f"  最高準確度: {stats.get('max_accuracy', 0):.2%}")
                print(f"  最低準確度: {stats.get('min_accuracy', 0):.2%}")
        
    except Exception as e:
        logger.error(f"執行模擬預測時出錯: {str(e)}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()