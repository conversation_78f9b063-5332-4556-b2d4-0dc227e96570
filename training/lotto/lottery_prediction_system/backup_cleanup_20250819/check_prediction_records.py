#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('.')

from data.db_manager import DBManager
from config.config_manager import ConfigManager

def main():
    try:
        config_manager = ConfigManager()
        db = DBManager(config_manager=config_manager)
        
        # 檢查威力彩預測記錄
        print("=== 威力彩預測記錄檢查 ===")
        records = db.load_prediction_records('powercolor', limit=None)
        print(f'威力彩預測記錄總數: {len(records)}')
        
        if len(records) > 0:
            print(f'預測記錄欄位: {list(records.columns)}')
            if 'Period' in records.columns:
                print(f'最新預測期號: {records.iloc[0]["Period"]}')
                print(f'最舊預測期號: {records.iloc[-1]["Period"]}')
            
            # 顯示前5筆記錄的期號
            print("\n前5筆預測記錄:")
            for i in range(min(5, len(records))):
                period = records.iloc[i].get('Period', '未知')
                method = records.iloc[i].get('PredictionMethod', '未知')
                date = records.iloc[i].get('PredictionDate', '未知')
                print(f"  {i+1}. 期號: {period}, 方法: {method}, 日期: {date}")
        
        # 檢查期號範圍
        print("\n=== 威力彩期號範圍檢查 ===")
        periods = db.get_periods_list('powercolor', 1000)
        print(f'威力彩總期數: {len(periods)}')
        if periods:
            print(f'最新期號: {periods[0]}')
            print(f'最舊期號: {periods[-1]}')
            
            # 找出2025年的期號
            periods_2025 = [p for p in periods if str(p).startswith('114')]
            print(f'2025年期號數量: {len(periods_2025)}')
            if periods_2025:
                print(f'2025年最新期號: {periods_2025[0]}')
                print(f'2025年最舊期號: {periods_2025[-1]}')
            
            # 找出2024年的期號
            periods_2024 = [p for p in periods if str(p).startswith('113')]
            print(f'2024年期號數量: {len(periods_2024)}')
            if periods_2024:
                print(f'2024年最新期號: {periods_2024[0]}')
                print(f'2024年最舊期號: {periods_2024[-1]}')
        
    except Exception as e:
        print(f"檢查時發生錯誤: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()