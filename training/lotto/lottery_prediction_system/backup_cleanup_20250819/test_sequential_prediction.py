#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一期一期預測測試腳本
從2025年開始進行連續預測並保存結果
"""

import os
import sys
import json
import sqlite3
from datetime import datetime, timedelta
import logging

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import DBManager
from core.enhanced_predictor import EnhancedPredictor
from analysis.prediction_success_analyzer import PredictionSuccessAnalyzer

class SequentialPredictionTester:
    """
    連續預測測試器
    """
    
    def __init__(self, lottery_type='powercolor'):
        self.lottery_type = lottery_type
        from config_manager import ConfigManager
        config_manager = ConfigManager()
        self.db_manager = DBManager(config_manager)
        self.predictor = EnhancedPredictor(lottery_type)
        self.analyzer = PredictionSuccessAnalyzer()
        
        # 設置日誌
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'sequential_prediction_{lottery_type}.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 創建結果保存目錄
        self.results_dir = f'sequential_predictions_{lottery_type}'
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 預測結果列表
        self.prediction_results = []
        
    def get_lottery_schedule(self, start_date, end_date):
        """
        獲取彩票開獎時間表
        """
        schedule = []
        current_date = start_date
        
        if self.lottery_type == 'powercolor':
            # 威力彩：每週一、四開獎
            while current_date <= end_date:
                if current_date.weekday() in [0, 3]:  # 週一=0, 週四=3
                    schedule.append(current_date)
                current_date += timedelta(days=1)
                
        elif self.lottery_type == 'lotto649':
            # 大樂透：每週二、五開獎
            while current_date <= end_date:
                if current_date.weekday() in [1, 4]:  # 週二=1, 週五=4
                    schedule.append(current_date)
                current_date += timedelta(days=1)
                
        elif self.lottery_type == 'dailycash':
            # 今彩539：每日開獎
            while current_date <= end_date:
                schedule.append(current_date)
                current_date += timedelta(days=1)
                
        return schedule
    
    def generate_period_number(self, date):
        """
        生成期號
        """
        year = date.year
        
        if self.lottery_type == 'powercolor':
            # 威力彩期號格式：年份 + 三位數期號
            # 計算該年度第幾期
            year_start = datetime(year, 1, 1)
            days_diff = (date - year_start).days
            # 每週兩期，大約每年104期
            period_in_year = (days_diff // 7) * 2 + (1 if date.weekday() == 0 else 2)
            return f"{year}{period_in_year:03d}"
            
        elif self.lottery_type == 'lotto649':
            # 大樂透期號格式類似
            year_start = datetime(year, 1, 1)
            days_diff = (date - year_start).days
            period_in_year = (days_diff // 7) * 2 + (1 if date.weekday() == 1 else 2)
            return f"{year}{period_in_year:03d}"
            
        elif self.lottery_type == 'dailycash':
            # 今彩539：每日一期
            year_start = datetime(year, 1, 1)
            days_diff = (date - year_start).days + 1
            return f"{year}{days_diff:03d}"
            
        return f"{year}001"
    
    def make_prediction(self, period, date):
        """
        進行單期預測
        """
        try:
            self.logger.info(f"開始預測第 {period} 期 ({date.strftime('%Y-%m-%d')})")
            
            # 載入歷史數據
            historical_data = self.db_manager.load_lottery_data(self.lottery_type)
            
            if not historical_data:
                self.logger.warning("沒有歷史數據可用於預測")
                return None
            
            # 進行預測
            prediction_result = self.predictor.predict()
            
            if not prediction_result:
                self.logger.error(f"預測失敗：第 {period} 期")
                return None
            
            # 構建預測記錄
            prediction_record = {
                'period': period,
                'date': date.strftime('%Y-%m-%d'),
                'lottery_type': self.lottery_type,
                'prediction_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'predicted_numbers': prediction_result.get('numbers', []),
                'special_number': prediction_result.get('special_number'),
                'confidence': prediction_result.get('confidence', 0),
                'method_used': prediction_result.get('method', 'unknown'),
                'analysis_data': prediction_result.get('analysis', {})
            }
            
            # 保存到數據庫
            self.save_prediction_to_db(prediction_record)
            
            # 添加到結果列表
            self.prediction_results.append(prediction_record)
            
            self.logger.info(f"預測完成：第 {period} 期 - 號碼: {prediction_record['predicted_numbers']}")
            
            return prediction_record
            
        except Exception as e:
            self.logger.error(f"預測過程發生錯誤：{str(e)}")
            return None
    
    def save_prediction_to_db(self, prediction_record):
        """
        保存預測結果到數據庫
        """
        try:
            # 構建預測數據
            numbers_str = ','.join(map(str, prediction_record['predicted_numbers']))
            special_str = str(prediction_record.get('special_number', ''))
            
            # 保存到數據庫
            self.db_manager.save_prediction(
                lottery_type=self.lottery_type,
                period=prediction_record['period'],
                numbers=numbers_str,
                special_number=special_str,
                confidence=prediction_record['confidence'],
                method=prediction_record['method_used']
            )
            
        except Exception as e:
            self.logger.error(f"保存預測到數據庫失敗：{str(e)}")
    
    def save_results_to_file(self):
        """
        保存預測結果到文件
        """
        try:
            # 保存為JSON格式
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            json_file = os.path.join(self.results_dir, f'predictions_{timestamp}.json')
            
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(self.prediction_results, f, ensure_ascii=False, indent=2)
            
            # 保存為CSV格式
            csv_file = os.path.join(self.results_dir, f'predictions_{timestamp}.csv')
            
            with open(csv_file, 'w', encoding='utf-8') as f:
                f.write('期號,日期,彩票類型,預測時間,預測號碼,特別號,信心度,使用方法\n')
                
                for record in self.prediction_results:
                    numbers_str = ' '.join(map(str, record['predicted_numbers']))
                    special = record.get('special_number', '')
                    
                    f.write(f"{record['period']},{record['date']},{record['lottery_type']},"
                           f"{record['prediction_time']},\"{numbers_str}\",{special},"
                           f"{record['confidence']},{record['method_used']}\n")
            
            self.logger.info(f"預測結果已保存到：{json_file} 和 {csv_file}")
            
        except Exception as e:
            self.logger.error(f"保存結果文件失敗：{str(e)}")
    
    def run_sequential_prediction(self, start_date=None, end_date=None, max_periods=None):
        """
        執行連續預測
        """
        if start_date is None:
            start_date = datetime(2025, 1, 1)
        if end_date is None:
            end_date = datetime(2025, 12, 31)
        
        self.logger.info(f"開始連續預測測試 - 彩票類型: {self.lottery_type}")
        self.logger.info(f"預測期間: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 獲取開獎時間表
        schedule = self.get_lottery_schedule(start_date, end_date)
        
        if max_periods:
            schedule = schedule[:max_periods]
        
        self.logger.info(f"總共需要預測 {len(schedule)} 期")
        
        successful_predictions = 0
        failed_predictions = 0
        
        for i, date in enumerate(schedule, 1):
            period = self.generate_period_number(date)
            
            self.logger.info(f"\n=== 進行第 {i}/{len(schedule)} 次預測 ===")
            
            result = self.make_prediction(period, date)
            
            if result:
                successful_predictions += 1
            else:
                failed_predictions += 1
            
            # 每10期保存一次結果
            if i % 10 == 0:
                self.save_results_to_file()
                self.logger.info(f"已完成 {i} 期預測，成功 {successful_predictions} 期，失敗 {failed_predictions} 期")
        
        # 最終保存結果
        self.save_results_to_file()
        
        # 生成總結報告
        self.generate_summary_report(successful_predictions, failed_predictions)
        
        self.logger.info(f"\n=== 連續預測測試完成 ===")
        self.logger.info(f"總預測期數: {len(schedule)}")
        self.logger.info(f"成功預測: {successful_predictions}")
        self.logger.info(f"失敗預測: {failed_predictions}")
        self.logger.info(f"成功率: {successful_predictions/len(schedule)*100:.2f}%")
    
    def generate_summary_report(self, successful, failed):
        """
        生成總結報告
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = os.path.join(self.results_dir, f'summary_report_{timestamp}.txt')
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(f"=== {self.lottery_type.upper()} 連續預測測試報告 ===\n\n")
                f.write(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"彩票類型: {self.lottery_type}\n")
                f.write(f"總預測期數: {len(self.prediction_results)}\n")
                f.write(f"成功預測: {successful}\n")
                f.write(f"失敗預測: {failed}\n")
                f.write(f"成功率: {successful/(successful+failed)*100:.2f}%\n\n")
                
                # 方法統計
                method_stats = {}
                for record in self.prediction_results:
                    method = record['method_used']
                    method_stats[method] = method_stats.get(method, 0) + 1
                
                f.write("使用方法統計:\n")
                for method, count in method_stats.items():
                    f.write(f"  {method}: {count} 次\n")
                
                # 信心度統計
                confidences = [r['confidence'] for r in self.prediction_results if r['confidence']]
                if confidences:
                    avg_confidence = sum(confidences) / len(confidences)
                    f.write(f"\n平均信心度: {avg_confidence:.2f}\n")
                
                f.write("\n=== 詳細預測記錄 ===\n")
                for record in self.prediction_results:
                    f.write(f"期號: {record['period']} | 日期: {record['date']} | ")
                    f.write(f"號碼: {record['predicted_numbers']} | ")
                    f.write(f"方法: {record['method_used']} | ")
                    f.write(f"信心度: {record['confidence']}\n")
            
            self.logger.info(f"總結報告已保存到：{report_file}")
            
        except Exception as e:
            self.logger.error(f"生成總結報告失敗：{str(e)}")

def main():
    """
    主函數
    """
    print("=== 連續預測測試系統 ===")
    print("1. 威力彩 (powercolor)")
    print("2. 大樂透 (lotto649)")
    print("3. 今彩539 (dailycash)")
    
    choice = input("請選擇彩票類型 (1-3): ").strip()
    
    lottery_types = {
        '1': 'powercolor',
        '2': 'lotto649', 
        '3': 'dailycash'
    }
    
    lottery_type = lottery_types.get(choice, 'powercolor')
    
    # 設置預測參數
    start_date_str = input("請輸入開始日期 (YYYY-MM-DD，默認2025-01-01): ").strip()
    if not start_date_str:
        start_date = datetime(2025, 1, 1)
    else:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
    
    max_periods_str = input("請輸入最大預測期數 (默認50期): ").strip()
    max_periods = int(max_periods_str) if max_periods_str else 50
    
    # 創建測試器並執行
    tester = SequentialPredictionTester(lottery_type)
    
    # 計算結束日期
    if lottery_type == 'powercolor':
        # 威力彩每週2期，50期約25週
        end_date = start_date + timedelta(weeks=max_periods//2 + 5)
    elif lottery_type == 'lotto649':
        # 大樂透每週2期
        end_date = start_date + timedelta(weeks=max_periods//2 + 5)
    else:
        # 今彩539每日1期
        end_date = start_date + timedelta(days=max_periods + 5)
    
    tester.run_sequential_prediction(start_date, end_date, max_periods)
    
    print(f"\n預測結果已保存在 {tester.results_dir} 目錄中")

if __name__ == "__main__":
    main()