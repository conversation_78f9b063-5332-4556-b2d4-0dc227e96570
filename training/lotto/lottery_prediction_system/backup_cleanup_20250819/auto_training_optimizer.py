#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自動化訓練優化器
定期測試不同訓練天數，找出最佳訓練期間
"""

import os
import sys
import json
import sqlite3
import logging
import struct
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import pandas as pd
import numpy as np

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from prediction.lottery_predictor import LotteryPredictor
from data.feature_engineering import FeatureEngineer

class AutoTrainingOptimizer:
    """自動化訓練優化器"""
    
    def __init__(self, db_path: str = "lottery_data.db"):
        self.db_path = db_path
        self.setup_logging()
        
        # 測試配置
        self.lottery_types = ['powercolor', 'lotto649', 'dailycash']
        self.training_days_options = [30, 60, 90, 120, 150, 180, 210, 240, 270, 300]
        self.test_periods = 30  # 測試過去30天的預測效果
        self.target_date = '2025-06-18'  # 測試目標日期
        
        # 結果存儲
        self.optimization_results = {}
        
    def setup_logging(self):
        """設置日誌"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('auto_training_optimizer.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def decode_binary_number(self, binary_data) -> int:
        """解碼二進制數據為整數"""
        if binary_data is None:
            return 0
        if isinstance(binary_data, (int, float)):
            return int(binary_data)
        if isinstance(binary_data, str):
            try:
                return int(binary_data)
            except ValueError:
                return 0
        if isinstance(binary_data, bytes):
            try:
                # 嘗試解析為小端序整數
                if len(binary_data) >= 4:
                    return struct.unpack('<I', binary_data[:4])[0]
                elif len(binary_data) >= 2:
                    return struct.unpack('<H', binary_data[:2])[0]
                else:
                    return int.from_bytes(binary_data, byteorder='little')
            except:
                return 0
        return 0
        
    def get_training_data(self, lottery_type: str, training_days: int, end_date: str) -> pd.DataFrame:
        """獲取訓練數據"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 計算開始日期
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            start_dt = end_dt - timedelta(days=training_days)
            start_date = start_dt.strftime('%Y-%m-%d')
            
            if lottery_type == 'powercolor':
                query = """
                SELECT Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, SpecialNumber
                FROM powercolor 
                WHERE Sdate >= ? AND Sdate < ?
                ORDER BY Sdate
                """
            elif lottery_type == 'lotto649':
                query = """
                SELECT Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, SpecialNumber
                FROM lotto649 
                WHERE Sdate >= ? AND Sdate < ?
                ORDER BY Sdate
                """
            elif lottery_type == 'dailycash':
                query = """
                SELECT Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5
                FROM dailycash 
                WHERE Sdate >= ? AND Sdate < ?
                ORDER BY Sdate
                """
            else:
                raise ValueError(f"不支持的彩票類型: {lottery_type}")
                
            df = pd.read_sql_query(query, conn, params=[start_date, end_date])
            conn.close()
            
            if df.empty:
                return df
                
            # 處理二進制數據
            number_columns = ['Anumber1', 'Anumber2', 'Anumber3', 'Anumber4', 'Anumber5']
            if lottery_type in ['powercolor', 'lotto649']:
                number_columns.extend(['Anumber6', 'SpecialNumber'])
                
            for col in number_columns:
                if col in df.columns:
                    df[col] = df[col].apply(self.decode_binary_number)
                    
            # 添加Period欄位並轉換日期
            df['Period'] = pd.to_datetime(df['Sdate']).dt.strftime('%Y%m%d')
            df['Sdate'] = pd.to_datetime(df['Sdate'])
            
            # 移除無效數據
            df = df.dropna()
            
            return df
            
        except Exception as e:
            self.logger.error(f"獲取 {lottery_type} 訓練數據時發生錯誤: {e}")
            return pd.DataFrame()
            
    def get_actual_result(self, lottery_type: str, target_date: str) -> Optional[Dict]:
        """獲取實際開獎結果"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            if lottery_type == 'powercolor':
                query = """
                SELECT Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, SpecialNumber
                FROM powercolor WHERE Sdate = ?
                """
            elif lottery_type == 'lotto649':
                query = """
                SELECT Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, SpecialNumber
                FROM lotto649 WHERE Sdate = ?
                """
            elif lottery_type == 'dailycash':
                query = """
                SELECT Anumber1, Anumber2, Anumber3, Anumber4, Anumber5
                FROM dailycash WHERE Sdate = ?
                """
            else:
                return None
                
            cursor = conn.execute(query, [target_date])
            row = cursor.fetchone()
            conn.close()
            
            if not row:
                return None
                
            # 解碼二進制數據
            numbers = [self.decode_binary_number(num) for num in row]
            
            if lottery_type in ['powercolor', 'lotto649']:
                return {
                    'main_numbers': numbers[:6],
                    'special_number': numbers[6] if len(numbers) > 6 else None
                }
            else:  # dailycash
                return {
                    'main_numbers': numbers[:5],
                    'special_number': None
                }
                
        except Exception as e:
            self.logger.error(f"獲取 {lottery_type} 實際結果時發生錯誤: {e}")
            return None
            
    def calculate_accuracy(self, prediction: Dict, actual: Dict) -> Dict:
        """計算預測準確度"""
        if not prediction or not actual:
            return {'accuracy': 0.0, 'main_matches': 0, 'special_matches': 0}
            
        pred_main = prediction.get('numbers', [])
        actual_main = actual.get('main_numbers', [])
        
        # 計算主號碼匹配數
        main_matches = len(set(pred_main) & set(actual_main))
        
        # 計算特別號匹配數
        special_matches = 0
        if 'special_number' in prediction and actual.get('special_number'):
            if prediction['special_number'] == actual['special_number']:
                special_matches = 1
                
        # 計算總準確度
        total_numbers = len(actual_main)
        if actual.get('special_number') is not None:
            total_numbers += 1
            
        accuracy = (main_matches + special_matches) / total_numbers if total_numbers > 0 else 0.0
        
        return {
            'accuracy': accuracy,
            'main_matches': main_matches,
            'special_matches': special_matches
        }
        
    def test_training_period(self, lottery_type: str, training_days: int, target_date: str) -> Dict:
        """測試特定訓練期間的效果
        
        Args:
            lottery_type: 彩票類型
            training_days: 訓練天數
            target_date: 目標日期
            
        Returns:
            dict: 測試結果
        """
        try:
            self.logger.info(f"測試 {lottery_type} 使用 {training_days} 天訓練數據")
            
            # 獲取訓練數據
            train_data = self.get_training_data(lottery_type, training_days, target_date)
            if train_data.empty or len(train_data) < 10:
                self.logger.warning(f"訓練數據不足: {len(train_data) if not train_data.empty else 0} 筆")
                return {
                    'lottery_type': lottery_type,
                    'training_days': training_days,
                    'avg_accuracy': 0.0,
                    'valid_tests': 0,
                    'total_tests': 1,
                    'error': '訓練數據不足'
                }
            
            # 創建特徵
            feature_engineer = FeatureEngineer()
            features = feature_engineer.create_basic_features(train_data, lottery_type=lottery_type)
            
            if features is None or len(features) == 0:
                self.logger.warning("特徵創建失敗")
                return {
                    'lottery_type': lottery_type,
                    'training_days': training_days,
                    'avg_accuracy': 0.0,
                    'valid_tests': 0,
                    'total_tests': 1,
                    'error': '特徵創建失敗'
                }
            
            # 使用預測器進行預測
            predictor = LotteryPredictor()
            
            # 加載模型
            if not predictor.load_models(lottery_type):
                self.logger.warning(f"無法加載 {lottery_type} 模型")
                return {
                    'lottery_type': lottery_type,
                    'training_days': training_days,
                    'avg_accuracy': 0.0,
                    'valid_tests': 0,
                    'total_tests': 1,
                    'error': '模型加載失敗'
                }
            
            # 進行預測
            prediction = predictor.predict(train_data, features, lottery_type)
            
            if prediction is None:
                self.logger.warning("預測失敗")
                return {
                    'lottery_type': lottery_type,
                    'training_days': training_days,
                    'avg_accuracy': 0.0,
                    'valid_tests': 0,
                    'total_tests': 1,
                    'error': '預測失敗'
                }
            
            # 獲取實際開獎結果
            actual_result = self.get_actual_result(lottery_type, target_date)
            if actual_result is None:
                self.logger.warning(f"無法獲取 {target_date} 的實際開獎結果")
                return {
                    'lottery_type': lottery_type,
                    'training_days': training_days,
                    'avg_accuracy': 0.0,
                    'valid_tests': 0,
                    'total_tests': 1,
                    'error': '無法獲取實際開獎結果'
                }
            
            # 計算準確度
            accuracy_result = self.calculate_accuracy(prediction, actual_result)
            accuracy = accuracy_result['accuracy']
            
            self.logger.info(f"{lottery_type} {training_days}天訓練 - 準確度: {accuracy:.3f}")
            
            return {
                'lottery_type': lottery_type,
                'training_days': training_days,
                'avg_accuracy': accuracy,
                'valid_tests': 1,
                'total_tests': 1,
                'detailed_results': [{
                    'date': target_date,
                    'accuracy': accuracy,
                    'main_matches': accuracy_result['main_matches'],
                    'special_matches': accuracy_result['special_matches'],
                    'training_records': len(train_data),
                    'prediction': prediction,
                    'actual_result': actual_result
                }]
            }
            
        except Exception as e:
            self.logger.error(f"測試訓練期間時發生錯誤: {str(e)}")
            return {
                'lottery_type': lottery_type,
                'training_days': training_days,
                'avg_accuracy': 0.0,
                'valid_tests': 0,
                'total_tests': 1,
                'error': str(e)
            }
        
    def optimize_single_lottery(self, lottery_type: str) -> Dict:
        """優化單一彩票類型的訓練期間"""
        self.logger.info(f"開始優化 {lottery_type} 的訓練期間")
        
        best_result = None
        best_accuracy = 0.0
        all_results = []
        
        for training_days in self.training_days_options:
            result = self.test_training_period(lottery_type, training_days, self.target_date)
            all_results.append(result)
            
            if result['avg_accuracy'] > best_accuracy:
                best_accuracy = result['avg_accuracy']
                best_result = result
                
            self.logger.info(
                f"{lottery_type} - {training_days}天: 平均準確度 {result['avg_accuracy']:.3f} "
                f"({result['valid_tests']}/{result['total_tests']} 有效測試)"
            )
            
        return {
            'lottery_type': lottery_type,
            'best_training_days': best_result['training_days'] if best_result else None,
            'best_accuracy': best_accuracy,
            'all_results': all_results,
            'optimization_date': datetime.now().isoformat()
        }
        
    def run_optimization(self) -> Dict:
        """運行完整的優化流程"""
        self.logger.info("開始自動化訓練優化")
        
        optimization_results = {}
        
        for lottery_type in self.lottery_types:
            try:
                result = self.optimize_single_lottery(lottery_type)
                optimization_results[lottery_type] = result
                
                self.logger.info(
                    f"{lottery_type} 優化完成 - 最佳訓練期間: {result['best_training_days']}天, "
                    f"最佳準確度: {result['best_accuracy']:.3f}"
                )
                
            except Exception as e:
                self.logger.error(f"優化 {lottery_type} 時發生錯誤: {e}")
                optimization_results[lottery_type] = {
                    'error': str(e),
                    'optimization_date': datetime.now().isoformat()
                }
                
        # 保存結果
        self.save_optimization_results(optimization_results)
        
        return optimization_results
        
    def save_optimization_results(self, results: Dict):
        """保存優化結果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"reports/training_optimization_{timestamp}.json"
        
        os.makedirs('reports', exist_ok=True)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
            
        self.logger.info(f"優化結果已保存到: {filename}")
        
    def get_current_best_settings(self) -> Dict:
        """獲取當前最佳設置"""
        try:
            # 查找最新的優化結果文件
            reports_dir = 'reports'
            if not os.path.exists(reports_dir):
                return {}
                
            optimization_files = [
                f for f in os.listdir(reports_dir) 
                if f.startswith('training_optimization_') and f.endswith('.json')
            ]
            
            if not optimization_files:
                return {}
                
            # 獲取最新文件
            latest_file = max(optimization_files)
            filepath = os.path.join(reports_dir, latest_file)
            
            with open(filepath, 'r', encoding='utf-8') as f:
                results = json.load(f)
                
            # 提取最佳設置
            best_settings = {}
            for lottery_type, result in results.items():
                if 'best_training_days' in result and result['best_training_days']:
                    best_settings[lottery_type] = {
                        'training_days': result['best_training_days'],
                        'accuracy': result['best_accuracy'],
                        'optimization_date': result['optimization_date']
                    }
                    
            return best_settings
            
        except Exception as e:
            self.logger.error(f"獲取最佳設置時發生錯誤: {e}")
            return {}
            
    def schedule_optimization(self, interval_days: int = 7):
        """定期執行優化（可以配合cron使用）"""
        self.logger.info(f"定期優化任務啟動，間隔 {interval_days} 天")
        
        # 檢查上次優化時間
        best_settings = self.get_current_best_settings()
        
        should_optimize = True
        if best_settings:
            # 檢查最近的優化日期
            latest_date = None
            for lottery_type, settings in best_settings.items():
                opt_date = datetime.fromisoformat(settings['optimization_date'])
                if latest_date is None or opt_date > latest_date:
                    latest_date = opt_date
                    
            if latest_date:
                days_since_last = (datetime.now() - latest_date).days
                if days_since_last < interval_days:
                    should_optimize = False
                    self.logger.info(f"距離上次優化僅 {days_since_last} 天，跳過本次優化")
                    
        if should_optimize:
            return self.run_optimization()
        else:
            return best_settings

def main():
    """主函數"""
    optimizer = AutoTrainingOptimizer()
    
    # 運行優化
    results = optimizer.run_optimization()
    
    # 顯示結果摘要
    print("\n=== 訓練期間優化結果 ===")
    for lottery_type, result in results.items():
        if 'error' in result:
            print(f"{lottery_type}: 優化失敗 - {result['error']}")
        else:
            print(f"{lottery_type}: 最佳訓練期間 {result['best_training_days']}天, "
                  f"準確度 {result['best_accuracy']:.3f}")
            
if __name__ == "__main__":
    main()