#!/usr/bin/env python3
"""
Phase 3.4 Web API 測試
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
import time
import json
import requests
import threading
from datetime import datetime
from typing import Dict, Any
import logging

# 配置日誌
logging.basicConfig(level=logging.WARNING)

# API 基礎 URL
API_BASE = "http://localhost:8000"

def test_api_connection():
    """測試 API 連接"""
    print("🔌 測試 API 連接")
    print("-" * 50)
    
    try:
        response = requests.get(f"{API_BASE}/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ API 連接成功")
            print(f"  版本: {data.get('version', 'Unknown')}")
            print(f"  狀態: {data.get('status', 'Unknown')}")
            return True
        else:
            print(f"  ❌ API 響應錯誤: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("  ❌ 無法連接到 API 服務器")
        print("  請確保 Web API 服務器正在運行 (python phase3/start_web_server.py)")
        return False
    except Exception as e:
        print(f"  ❌ 連接錯誤: {e}")
        return False

def test_health_endpoint():
    """測試健康檢查端點"""
    print("\n🏥 測試健康檢查")
    print("-" * 50)
    
    try:
        response = requests.get(f"{API_BASE}/health", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ 健康檢查成功")
            print(f"  系統狀態: {data.get('status', 'Unknown')}")
            print(f"  運行時間: {data.get('uptime', 'Unknown')}")
            print(f"  活躍預測: {data.get('active_predictions', 0)}")
            print(f"  總預測數: {data.get('total_predictions', 0)}")
            
            # 檢查系統組件
            system_health = data.get('system_health', {})
            print("  系統組件狀態:")
            for component, status in system_health.items():
                icon = "✅" if status == "正常" else "❌"
                print(f"    {icon} {component}: {status}")
            
            return True
        else:
            print(f"  ❌ 健康檢查失敗: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ 健康檢查錯誤: {e}")
        return False

def test_lottery_types():
    """測試彩票類型端點"""
    print("\n🎯 測試彩票類型")
    print("-" * 50)
    
    try:
        response = requests.get(f"{API_BASE}/lotteries", timeout=10)
        
        if response.status_code == 200:
            lotteries = response.json()
            print(f"  ✅ 獲取彩票類型成功")
            print(f"  支持的彩票類型: {len(lotteries)}")
            
            for lottery in lotteries:
                print(f"    • {lottery['name']} ({lottery['lottery_type']})")
                print(f"      號碼配置: {lottery['main_numbers_count']}+{lottery['special_numbers_count']}")
                print(f"      支持策略: {len(lottery['supported_strategies'])}個")
            
            return lotteries
        else:
            print(f"  ❌ 獲取彩票類型失敗: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"  ❌ 彩票類型錯誤: {e}")
        return None

def test_single_prediction():
    """測試單個預測"""
    print("\n🎲 測試單個預測")
    print("-" * 50)
    
    try:
        # 預測請求數據
        request_data = {
            "lottery_type": "powercolor",
            "strategy": "ensemble",
            "use_cross_learning": True,
            "ensemble_size": 5,
            "data_window": 50
        }
        
        print(f"  請求參數: {request_data}")
        
        # 發送預測請求
        start_time = time.time()
        response = requests.post(
            f"{API_BASE}/predict",
            json=request_data,
            timeout=30
        )
        elapsed_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ 預測成功 (耗時: {elapsed_time:.2f}秒)")
            print(f"  預測ID: {result['prediction_id'][:16]}...")
            print(f"  彩票類型: {result['lottery_type']}")
            print(f"  主號碼: {result['main_numbers']}")
            if result['special_numbers']:
                print(f"  特別號: {result['special_numbers']}")
            print(f"  信心度: {result['confidence']:.1f}%")
            print(f"  使用策略: {result['strategy_used']}")
            
            return result
        else:
            print(f"  ❌ 預測失敗: {response.status_code}")
            if response.content:
                error_data = response.json()
                print(f"  錯誤詳情: {error_data.get('detail', 'Unknown error')}")
            return None
            
    except requests.exceptions.Timeout:
        print("  ❌ 預測請求超時")
        return None
    except Exception as e:
        print(f"  ❌ 預測錯誤: {e}")
        return None

def test_batch_prediction():
    """測試批量預測"""
    print("\n📊 測試批量預測")
    print("-" * 50)
    
    try:
        # 批量預測請求數據
        request_data = {
            "lottery_types": ["powercolor", "lotto649"],
            "strategy": "ensemble",
            "use_cross_learning": False,
            "ensemble_size": 3
        }
        
        print(f"  批量預測類型: {request_data['lottery_types']}")
        
        # 發送批量預測請求
        start_time = time.time()
        response = requests.post(
            f"{API_BASE}/predict/batch",
            json=request_data,
            timeout=60
        )
        elapsed_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ 批量預測成功 (耗時: {elapsed_time:.2f}秒)")
            print(f"  批次ID: {result['batch_id'][:16]}...")
            print(f"  成功率: {result['successful_predictions']}/{result['total_predictions']}")
            
            print("  預測結果:")
            for lottery_type, prediction in result['results'].items():
                if prediction:
                    print(f"    ✅ {lottery_type}: {prediction['main_numbers']} "
                          f"(信心度: {prediction['confidence']:.1f}%)")
                else:
                    print(f"    ❌ {lottery_type}: 預測失敗")
            
            return result
        else:
            print(f"  ❌ 批量預測失敗: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"  ❌ 批量預測錯誤: {e}")
        return None

def test_recommendations():
    """測試預測建議"""
    print("\n💡 測試預測建議")
    print("-" * 50)
    
    try:
        lottery_type = "powercolor"
        response = requests.get(f"{API_BASE}/recommendations/{lottery_type}", timeout=15)
        
        if response.status_code == 200:
            recommendations = response.json()
            print(f"  ✅ 獲取建議成功")
            print(f"  彩票類型: {recommendations['lottery_type']}")
            
            # 推薦策略
            if recommendations['recommended_strategies']:
                print("  推薦策略:")
                for strategy in recommendations['recommended_strategies'][:3]:
                    print(f"    • {strategy['strategy']}: {strategy['confidence']:.1f}%")
            
            # 跨學習機會
            if recommendations['cross_learning_opportunities']:
                print("  跨學習機會:")
                for opp in recommendations['cross_learning_opportunities'][:2]:
                    status = "✅" if opp['recommended'] else "❌"
                    print(f"    {status} {opp['related_lottery']}: 相似度 {opp['similarity_score']:.3f}")
            
            # 信心度因子
            if recommendations['confidence_factors']:
                print("  信心度因子:")
                factors = recommendations['confidence_factors']
                for factor, value in factors.items():
                    print(f"    • {factor}: {value:.3f}")
            
            return recommendations
        else:
            print(f"  ❌ 獲取建議失敗: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"  ❌ 建議錯誤: {e}")
        return None

def test_analytics():
    """測試分析統計"""
    print("\n📈 測試分析統計")
    print("-" * 50)
    
    try:
        lottery_type = "powercolor"
        response = requests.get(f"{API_BASE}/analytics/{lottery_type}", timeout=15)
        
        if response.status_code == 200:
            analytics = response.json()
            print(f"  ✅ 獲取分析成功")
            print(f"  彩票類型: {analytics['lottery_type']}")
            print(f"  分析期間: {analytics['analysis_period_days']}天")
            
            # 準確度指標
            metrics = analytics.get('accuracy_metrics', {})
            if metrics:
                print("  準確度指標:")
                print(f"    平均匹配數: {metrics.get('average_match_count', 0):.2f}")
                print(f"    成功率(≥2): {metrics.get('success_rate_2plus', 0):.1%}")
                print(f"    總預測數: {metrics.get('total_predictions', 0)}")
            
            # 算法性能
            algorithm_perf = analytics.get('algorithm_performance', {})
            if algorithm_perf:
                print("  算法性能:")
                for algo, perf in list(algorithm_perf.items())[:3]:
                    print(f"    • {algo}: 成功率 {perf.get('success_rate', 0):.1%}")
            
            return analytics
        else:
            print(f"  ❌ 獲取分析失敗: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"  ❌ 分析錯誤: {e}")
        return None

def test_realtime_status():
    """測試實時數據狀態"""
    print("\n📡 測試實時數據狀態")
    print("-" * 50)
    
    try:
        response = requests.get(f"{API_BASE}/realtime/status", timeout=10)
        
        if response.status_code == 200:
            status = response.json()
            print(f"  ✅ 獲取實時狀態成功")
            print(f"  整體狀態: {status['overall_status']}")
            
            lottery_status = status.get('lottery_status', {})
            if lottery_status:
                print("  各彩票類型狀態:")
                for lottery_type, info in lottery_status.items():
                    last_update = info.get('last_update')
                    if last_update:
                        print(f"    ✅ {lottery_type}: {info['data_status']} "
                              f"({info['cache_status']})")
                    else:
                        print(f"    ❌ {lottery_type}: {info['data_status']}")
            
            return status
        else:
            print(f"  ❌ 獲取實時狀態失敗: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"  ❌ 實時狀態錯誤: {e}")
        return None

def test_prediction_evaluation():
    """測試預測評估"""
    print("\n📝 測試預測評估")
    print("-" * 50)
    
    try:
        # 首先創建一個預測
        prediction_result = test_single_prediction()
        if not prediction_result:
            print("  ❌ 無法創建測試預測")
            return None
        
        # 模擬評估數據
        evaluation_data = {
            "prediction_id": prediction_result['prediction_id'],
            "actual_numbers": [1, 15, 22, 28, 31, 38],
            "actual_special": 5
        }
        
        print(f"  評估預測: {prediction_result['prediction_id'][:16]}...")
        print(f"  實際號碼: {evaluation_data['actual_numbers']}")
        print(f"  實際特別號: {evaluation_data['actual_special']}")
        
        response = requests.post(
            f"{API_BASE}/evaluate",
            json=evaluation_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ 評估成功")
            print(f"  評估結果: {'成功' if result['evaluation_success'] else '失敗'}")
            
            return result
        else:
            print(f"  ❌ 評估失敗: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"  ❌ 評估錯誤: {e}")
        return None

def run_performance_test():
    """運行性能測試"""
    print("\n⚡ 性能測試")
    print("-" * 50)
    
    test_count = 5
    total_time = 0
    successful_requests = 0
    
    print(f"  執行 {test_count} 次預測請求...")
    
    for i in range(test_count):
        try:
            start_time = time.time()
            response = requests.post(
                f"{API_BASE}/predict",
                json={
                    "lottery_type": "powercolor",
                    "strategy": "frequency_based",
                    "use_cross_learning": False,
                    "ensemble_size": 3,
                    "data_window": 30
                },
                timeout=20
            )
            elapsed_time = time.time() - start_time
            
            if response.status_code == 200:
                successful_requests += 1
                total_time += elapsed_time
                print(f"    請求 {i+1}: ✅ {elapsed_time:.2f}秒")
            else:
                print(f"    請求 {i+1}: ❌ 失敗 ({response.status_code})")
                
        except Exception as e:
            print(f"    請求 {i+1}: ❌ 錯誤 ({e})")
    
    if successful_requests > 0:
        avg_time = total_time / successful_requests
        print(f"\n  性能統計:")
        print(f"    成功請求: {successful_requests}/{test_count}")
        print(f"    平均響應時間: {avg_time:.2f}秒")
        print(f"    總耗時: {total_time:.2f}秒")
    else:
        print("  ❌ 所有請求都失敗了")

async def main():
    """主測試函數"""
    print("🚀 Phase 3.4 Web API 測試")
    print("=" * 70)
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"API 基礎 URL: {API_BASE}")
    print("=" * 70)
    
    # 測試序列
    test_results = []
    
    # 1. API 連接測試
    test_results.append(test_api_connection())
    if not test_results[-1]:
        print("\n❌ API 連接失敗，無法繼續測試")
        return
    
    # 2. 健康檢查測試
    test_results.append(test_health_endpoint())
    
    # 3. 彩票類型測試
    lottery_types = test_lottery_types()
    test_results.append(lottery_types is not None)
    
    # 4. 單個預測測試
    test_results.append(test_single_prediction() is not None)
    
    # 5. 批量預測測試
    test_results.append(test_batch_prediction() is not None)
    
    # 6. 預測建議測試
    test_results.append(test_recommendations() is not None)
    
    # 7. 分析統計測試
    test_results.append(test_analytics() is not None)
    
    # 8. 實時狀態測試
    test_results.append(test_realtime_status() is not None)
    
    # 9. 預測評估測試 (可選)
    # test_results.append(test_prediction_evaluation() is not None)
    
    # 10. 性能測試
    run_performance_test()
    
    # 總結
    print("\n\n🎯 Web API 測試總結")
    print("=" * 70)
    
    test_names = [
        "API 連接測試",
        "健康檢查測試", 
        "彩票類型測試",
        "單個預測測試",
        "批量預測測試",
        "預測建議測試",
        "分析統計測試",
        "實時狀態測試"
    ]
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"總測試數: {total_tests}")
    print(f"通過測試: {passed_tests}")
    print(f"測試通過率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n各項測試結果:")
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {i+1}. {name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有測試通過！Web API 運行正常")
        print("📋 Phase 3.4 主要功能:")
        print("  ✅ FastAPI 後端服務")
        print("  ✅ RESTful API 接口")
        print("  ✅ 預測創建和管理")
        print("  ✅ 批量預測支持")
        print("  ✅ 實時狀態監控")
        print("  ✅ 預測建議系統")
        print("  ✅ 分析統計接口")
        print("  ✅ WebSocket 實時通信")
        
        print("\n💡 核心特性:")
        print("  • 完整的 RESTful API 設計")
        print("  • 異步請求處理和背景任務")
        print("  • CORS 支持和 WebSocket 通信")
        print("  • 自動 API 文檔生成 (/docs)")
        print("  • 系統健康監控和狀態管理")
        print("  • 批量操作和性能優化")
        
        print("\n🌐 Web 界面:")
        print("  • 響應式 Bootstrap 設計")
        print("  • 實時預測和結果顯示")
        print("  • 智能建議和分析圖表")
        print("  • WebSocket 實時更新")
        print("  • 多彩票類型支持")
    else:
        print(f"\n⚠️  {total_tests - passed_tests} 項測試失敗，需要進一步檢查")
        print("請確保:")
        print("  1. Web API 服務器正在運行")
        print("  2. 數據庫和預測器正確初始化")
        print("  3. 所有依賴項已安裝")
    
    print("=" * 70)

if __name__ == "__main__":
    asyncio.run(main())