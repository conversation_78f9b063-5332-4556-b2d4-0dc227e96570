#!/usr/bin/env python3
"""
測試 Streamlit 應用中的開獎結果顯示問題
"""

import requests
from bs4 import BeautifulSoup

def test_streamlit_app():
    """測試 Streamlit 應用"""
    print("=== 測試 Streamlit 應用 ===")
    
    try:
        # 測試 Streamlit 應用訪問
        streamlit_url = "http://localhost:8501"
        print(f"訪問 Streamlit 應用: {streamlit_url}")
        
        response = requests.get(streamlit_url, timeout=10)
        
        if response.status_code == 200:
            print("✅ Streamlit 應用訪問成功")
            
            # 檢查頁面是否包含"計算中..."
            if '計算中' in response.text:
                print("⚠️  發現'計算中...'文字在Streamlit應用中")
            else:
                print("✅ Streamlit應用中沒有發現'計算中...'文字")
                
        else:
            print(f"❌ Streamlit 應用訪問失敗: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 無法連接到 Streamlit 應用 - 可能未運行或端口不正確")
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

def analyze_streamlit_source():
    """分析 Streamlit 源代碼中的問題"""
    print("\n=== 分析 Streamlit 源代碼 ===")
    
    try:
        # 檢查 integrated_web_system.py 中的問題
        with open('integrated_web_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 尋找 "計算中..." 的使用
        lines = content.split('\n')
        calculating_lines = []
        
        for i, line in enumerate(lines, 1):
            if '計算中' in line:
                calculating_lines.append((i, line.strip()))
        
        if calculating_lines:
            print(f"✅ 在 integrated_web_system.py 中發現 {len(calculating_lines)} 處'計算中...'")
            for line_num, line_content in calculating_lines:
                print(f"  第 {line_num} 行: {line_content}")
        else:
            print("❌ 在 integrated_web_system.py 中未發現'計算中...'")
            
    except FileNotFoundError:
        print("❌ integrated_web_system.py 文件不存在")
    except Exception as e:
        print(f"❌ 分析源代碼失敗: {e}")

def check_data_loading_logic():
    """檢查數據載入邏輯"""
    print("\n=== 檢查數據載入邏輯 ===")
    
    try:
        # 模擬 Streamlit 應用中的數據載入邏輯
        from data.db_manager import DBManager
        import pandas as pd
        
        db_manager = DBManager()
        
        lottery_types = ['powercolor', 'lotto649', 'dailycash']
        
        for lottery_type in lottery_types:
            print(f"\n--- 測試 {lottery_type} 數據載入 ---")
            
            try:
                df = db_manager.load_lottery_data(lottery_type=lottery_type)
                
                if df.empty:
                    print(f"❌ {lottery_type}: 數據為空 - 會顯示'計算中...'")
                else:
                    print(f"✅ {lottery_type}: 成功載入 {len(df)} 筆數據")
                    
                    # 檢查最新數據
                    if len(df) > 0:
                        latest = df.iloc[-1]  # 最新一筆
                        print(f"  最新期號: {latest['Period']}")
                        print(f"  最新日期: {latest['Sdate']}")
                        
                        # 檢查號碼是否完整
                        if lottery_type == 'powercolor':
                            numbers = [latest[f'Anumber{i}'] for i in range(1, 7)]
                            special = latest['Second_district']
                            print(f"  第一區: {numbers}, 第二區: {special}")
                            
                            if pd.isna(numbers).any() or pd.isna(special):
                                print("  ⚠️  發現空值，可能導致顯示問題")
                                
                        elif lottery_type == 'lotto649':
                            numbers = [latest[f'Anumber{i}'] for i in range(1, 7)]
                            special = latest['SpecialNumber']
                            print(f"  一般號碼: {numbers}, 特別號: {special}")
                            
                            if pd.isna(numbers).any() or pd.isna(special):
                                print("  ⚠️  發現空值，可能導致顯示問題")
                                
                        elif lottery_type == 'dailycash':
                            numbers = [latest[f'Anumber{i}'] for i in range(1, 6)]
                            print(f"  開獎號碼: {numbers}")
                            
                            if pd.isna(numbers).any():
                                print("  ⚠️  發現空值，可能導致顯示問題")
                    
            except Exception as e:
                print(f"❌ {lottery_type}: 載入失敗 - {e}")
                
    except Exception as e:
        print(f"❌ 數據載入檢查失敗: {e}")

if __name__ == "__main__":
    test_streamlit_app()
    analyze_streamlit_source()
    check_data_loading_logic()