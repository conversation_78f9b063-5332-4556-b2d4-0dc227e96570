#!/usr/bin/env python3
"""
測試 Web API 端點訪問
"""

import requests
import json

def test_api_results():
    """測試 /api/results 端點"""
    base_url = "http://localhost:7890"  # 彩票系統運行在7890端口
    
    test_cases = [
        {
            'name': '大樂透默認查詢',
            'params': {'lottery_type': 'lotto649', 'limit': 5}
        },
        {
            'name': '今彩539默認查詢', 
            'params': {'lottery_type': 'dailycash', 'limit': 5}
        },
        {
            'name': '威力彩默認查詢',
            'params': {'lottery_type': 'powercolor', 'limit': 5}
        }
    ]
    
    for test_case in test_cases:
        print(f"\n=== {test_case['name']} ===")
        
        try:
            url = f"{base_url}/api/results"
            response = requests.get(url, params=test_case['params'], timeout=10)
            
            print(f"請求URL: {response.url}")
            print(f"響應狀態: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('success'):
                    results = data.get('data', {}).get('results', [])
                    print(f"✅ 成功獲取 {len(results)} 筆結果")
                    
                    # 顯示前2筆結果
                    for i, result in enumerate(results[:2]):
                        print(f"  {i+1}. 期號: {result['period']}, 日期: {result['date']}")
                        
                        numbers = result['numbers']
                        if 'main_numbers' in numbers:
                            print(f"     一般號碼: {numbers['main_numbers']}, 特別號: {numbers['special_number']}")
                        elif 'first_area' in numbers:
                            print(f"     第一區: {numbers['first_area']}, 第二區: {numbers['second_area']}")
                        elif 'numbers' in numbers:
                            print(f"     開獎號碼: {numbers['numbers']}")
                else:
                    print(f"❌ API返回錯誤: {data.get('error', '未知錯誤')}")
            else:
                print(f"❌ HTTP錯誤: {response.status_code}")
                print(f"響應內容: {response.text[:200]}")
                
        except requests.exceptions.ConnectionError:
            print("❌ 連接失敗 - Web服務器可能未運行")
            print("請啟動web應用: python web/app.py 或 python web/enhanced_app.py")
        except requests.exceptions.Timeout:
            print("❌ 請求超時")
        except Exception as e:
            print(f"❌ 測試失敗: {e}")

def test_results_page():
    """測試 /results 頁面"""
    print("\n=== 測試 /results 頁面 ===")
    
    try:
        base_url = "http://localhost:7890"
        url = f"{base_url}/results"
        
        response = requests.get(url, timeout=10)
        print(f"請求URL: {url}")
        print(f"響應狀態: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 頁面訪問成功")
            # 檢查頁面是否包含關鍵元素
            content = response.text
            if 'loadResults()' in content:
                print("✅ 頁面包含loadResults函數")
            if '/api/results' in content:
                print("✅ 頁面包含API調用")
            if '計算中' in content:
                print("⚠️  頁面可能包含'計算中'文字")
        else:
            print(f"❌ 頁面訪問失敗: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 連接失敗 - Web服務器可能未運行")
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

if __name__ == "__main__":
    test_api_results()
    test_results_page()