#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自動化預測測試腳本
從2025年開始自動進行預測測試並保存結果
"""

import os
import sys
import json
from datetime import datetime, timedelta
import logging

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from data.db_manager import DBManager
    from prediction.enhanced_lottery_predictor import EnhancedLotteryPredictor
except ImportError as e:
    print(f"導入模組失敗: {e}")
    print("嘗試使用基礎預測器...")
    try:
        from data.db_manager import DBManager
        from prediction.lottery_predictor import LotteryPredictor as EnhancedLotteryPredictor
    except ImportError as e2:
        print(f"基礎預測器也無法導入: {e2}")
        sys.exit(1)

class AutoPredictionTest:
    """
    自動化預測測試器
    """
    
    def __init__(self, lottery_type='powercolor'):
        self.lottery_type = lottery_type
        self.results = []
        
        # 設置日誌
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'auto_prediction_test_{lottery_type}.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        try:
            from config_manager import ConfigManager
            config_manager = ConfigManager()
            self.db_manager = DBManager(config_manager)
            self.predictor = EnhancedLotteryPredictor(lottery_type)
            self.logger.info(f"初始化完成 - 彩票類型: {lottery_type}")
        except Exception as e:
            self.logger.error(f"初始化失敗: {e}")
            # 如果初始化失敗，創建一個簡單的預測器
            self.predictor = None
            self.logger.warning("將使用簡單隨機預測")
    
    def generate_period_number(self, date):
        """
        生成期號
        """
        year = date.year
        
        if self.lottery_type == 'powercolor':
            # 威力彩：每週一、四開獎
            week_of_year = date.isocalendar()[1]
            # 計算該週是第幾期
            if date.weekday() == 0:  # 週一
                period_in_year = week_of_year * 2 - 1
            else:  # 週四或其他
                period_in_year = week_of_year * 2
            return f"{year}{period_in_year:03d}"
            
        elif self.lottery_type == 'lotto649':
            # 大樂透：每週二、五開獎
            week_of_year = date.isocalendar()[1]
            if date.weekday() == 1:  # 週二
                period_in_year = week_of_year * 2 - 1
            else:  # 週五或其他
                period_in_year = week_of_year * 2
            return f"{year}{period_in_year:03d}"
            
        elif self.lottery_type == 'dailycash':
            # 今彩539：每日開獎
            day_of_year = date.timetuple().tm_yday
            return f"{year}{day_of_year:03d}"
            
        return f"{year}001"
    
    def get_lottery_dates(self, start_date, num_periods):
        """
        獲取彩票開獎日期列表
        """
        dates = []
        current_date = start_date
        
        while len(dates) < num_periods:
            if self.lottery_type == 'powercolor':
                # 威力彩：每週一、四開獎
                if current_date.weekday() in [0, 3]:  # 週一=0, 週四=3
                    dates.append(current_date)
            elif self.lottery_type == 'lotto649':
                # 大樂透：每週二、五開獎
                if current_date.weekday() in [1, 4]:  # 週二=1, 週五=4
                    dates.append(current_date)
            elif self.lottery_type == 'dailycash':
                # 今彩539：每日開獎
                dates.append(current_date)
            
            current_date += timedelta(days=1)
        
        return dates
    
    def make_prediction(self, period, date):
        """
        進行預測
        """
        try:
            self.logger.info(f"開始預測第 {period} 期 ({date.strftime('%Y-%m-%d')})")
            
            prediction_result = None
            
            if self.predictor:
                try:
                    # 載入歷史數據
                    historical_data = self.db_manager.load_lottery_data(self.lottery_type)
                    
                    if historical_data:
                        # 使用預測器進行預測
                        prediction_result = self.predictor.predict()
                    else:
                        self.logger.warning("沒有歷史數據，使用隨機預測")
                except Exception as e:
                    self.logger.warning(f"預測器預測失敗: {e}，使用隨機預測")
            
            # 如果預測器失敗或沒有預測器，使用隨機預測
            if not prediction_result:
                import random
                if self.lottery_type == 'powercolor':
                    numbers = sorted(random.sample(range(1, 39), 6))
                    special = random.randint(1, 8)
                    prediction_result = {
                        'numbers': numbers,
                        'special_number': special,
                        'confidence': 0.3,
                        'method': 'random_fallback'
                    }
                elif self.lottery_type == 'lotto649':
                    numbers = sorted(random.sample(range(1, 50), 6))
                    special = random.randint(1, 8)
                    prediction_result = {
                        'numbers': numbers,
                        'special_number': special,
                        'confidence': 0.3,
                        'method': 'random_fallback'
                    }
                elif self.lottery_type == 'dailycash':
                    numbers = sorted(random.sample(range(1, 40), 5))
                    prediction_result = {
                        'numbers': numbers,
                        'confidence': 0.3,
                        'method': 'random_fallback'
                    }
            
            if not prediction_result:
                self.logger.error(f"預測失敗：第 {period} 期")
                return None
            
            # 構建預測記錄
            prediction_record = {
                'period': period,
                'date': date.strftime('%Y-%m-%d'),
                'prediction_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'lottery_type': self.lottery_type,
                'predicted_numbers': prediction_result.get('numbers', []),
                'special_number': prediction_result.get('special_number'),
                'confidence': prediction_result.get('confidence', 0),
                'method_used': prediction_result.get('method', 'unknown')
            }
            
            # 嘗試保存到數據庫
            try:
                numbers_str = ','.join(map(str, prediction_record['predicted_numbers']))
                special_str = str(prediction_record.get('special_number', ''))
                
                self.db_manager.save_prediction(
                    lottery_type=self.lottery_type,
                    period=period,
                    numbers=numbers_str,
                    special_number=special_str,
                    confidence=prediction_record['confidence'],
                    method=prediction_record['method_used']
                )
                self.logger.info("預測結果已保存到數據庫")
            except Exception as e:
                self.logger.warning(f"保存到數據庫失敗: {e}")
            
            # 添加到結果列表
            self.results.append(prediction_record)
            
            self.logger.info(f"預測完成：第 {period} 期")
            self.logger.info(f"預測號碼: {prediction_record['predicted_numbers']}")
            if prediction_record.get('special_number'):
                self.logger.info(f"特別號: {prediction_record['special_number']}")
            self.logger.info(f"信心度: {prediction_record['confidence']:.2f}")
            self.logger.info(f"使用方法: {prediction_record['method_used']}")
            
            return prediction_record
            
        except Exception as e:
            self.logger.error(f"預測過程發生錯誤：{str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return None
    
    def run_auto_test(self, start_date=None, num_periods=20):
        """
        執行自動化測試
        """
        if start_date is None:
            start_date = datetime(2025, 1, 6)  # 2025年第一個週一
        
        self.logger.info(f"開始自動化預測測試 - 彩票類型: {self.lottery_type}")
        self.logger.info(f"開始日期: {start_date.strftime('%Y-%m-%d')}")
        self.logger.info(f"預測期數: {num_periods}")
        
        # 獲取開獎日期
        dates = self.get_lottery_dates(start_date, num_periods)
        
        successful_predictions = 0
        failed_predictions = 0
        
        for i, date in enumerate(dates):
            period = self.generate_period_number(date)
            
            self.logger.info(f"\n=== 進行第 {i+1}/{len(dates)} 次預測 ===")
            
            result = self.make_prediction(period, date)
            
            if result:
                successful_predictions += 1
            else:
                failed_predictions += 1
            
            # 每5期保存一次結果
            if (i + 1) % 5 == 0:
                self.save_results()
                self.logger.info(f"已完成 {i+1} 期預測，成功 {successful_predictions} 期，失敗 {failed_predictions} 期")
        
        # 最終保存結果
        self.save_results()
        
        # 生成統計報告
        self.generate_report(successful_predictions, failed_predictions)
        
        self.logger.info(f"\n=== 自動化預測測試完成 ===")
        self.logger.info(f"總預測期數: {len(dates)}")
        self.logger.info(f"成功預測: {successful_predictions}")
        self.logger.info(f"失敗預測: {failed_predictions}")
        if len(dates) > 0:
            self.logger.info(f"成功率: {successful_predictions/len(dates)*100:.2f}%")
        
        return self.results
    
    def save_results(self):
        """
        保存預測結果
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 創建結果目錄
            results_dir = f'auto_prediction_results_{self.lottery_type}'
            os.makedirs(results_dir, exist_ok=True)
            
            # 保存JSON格式
            json_file = os.path.join(results_dir, f'predictions_{timestamp}.json')
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            
            # 保存CSV格式
            csv_file = os.path.join(results_dir, f'predictions_{timestamp}.csv')
            with open(csv_file, 'w', encoding='utf-8') as f:
                f.write('期號,日期,彩票類型,預測時間,預測號碼,特別號,信心度,使用方法\n')
                
                for record in self.results:
                    numbers_str = ' '.join(map(str, record['predicted_numbers']))
                    special = record.get('special_number', '')
                    
                    f.write(f"{record['period']},{record['date']},{record['lottery_type']},"
                           f"{record['prediction_time']},\"{numbers_str}\",{special},"
                           f"{record['confidence']},{record['method_used']}\n")
            
            self.logger.info(f"預測結果已保存到：{results_dir}")
            
        except Exception as e:
            self.logger.error(f"保存結果失敗：{str(e)}")
    
    def generate_report(self, successful, failed):
        """
        生成測試報告
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            results_dir = f'auto_prediction_results_{self.lottery_type}'
            report_file = os.path.join(results_dir, f'test_report_{timestamp}.txt')
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(f"=== {self.lottery_type.upper()} 自動化預測測試報告 ===\n\n")
                f.write(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"彩票類型: {self.lottery_type}\n")
                f.write(f"總預測期數: {len(self.results)}\n")
                f.write(f"成功預測: {successful}\n")
                f.write(f"失敗預測: {failed}\n")
                if (successful + failed) > 0:
                    f.write(f"成功率: {successful/(successful+failed)*100:.2f}%\n\n")
                
                # 方法統計
                method_stats = {}
                confidence_sum = 0
                confidence_count = 0
                
                for record in self.results:
                    method = record['method_used']
                    method_stats[method] = method_stats.get(method, 0) + 1
                    
                    if record['confidence']:
                        confidence_sum += record['confidence']
                        confidence_count += 1
                
                f.write("使用方法統計:\n")
                for method, count in method_stats.items():
                    f.write(f"  {method}: {count} 次\n")
                
                if confidence_count > 0:
                    avg_confidence = confidence_sum / confidence_count
                    f.write(f"\n平均信心度: {avg_confidence:.2f}\n")
                
                f.write("\n=== 詳細預測記錄 ===\n")
                for i, record in enumerate(self.results, 1):
                    f.write(f"第 {i} 期:\n")
                    f.write(f"  期號: {record['period']}\n")
                    f.write(f"  日期: {record['date']}\n")
                    f.write(f"  號碼: {record['predicted_numbers']}\n")
                    if record.get('special_number'):
                        f.write(f"  特別號: {record['special_number']}\n")
                    f.write(f"  方法: {record['method_used']}\n")
                    f.write(f"  信心度: {record['confidence']}\n\n")
            
            self.logger.info(f"測試報告已保存到：{report_file}")
            
        except Exception as e:
            self.logger.error(f"生成報告失敗：{str(e)}")

def main():
    """
    主函數 - 自動執行測試
    """
    print("=== 自動化預測測試系統 ===")
    print("開始執行威力彩預測測試...")
    
    # 測試威力彩
    try:
        tester = AutoPredictionTest('powercolor')
        results = tester.run_auto_test(num_periods=15)
        print(f"威力彩測試完成！共生成 {len(results)} 期預測結果")
    except Exception as e:
        print(f"威力彩測試失敗：{str(e)}")
    
    print("\n開始執行大樂透預測測試...")
    
    # 測試大樂透
    try:
        tester = AutoPredictionTest('lotto649')
        results = tester.run_auto_test(num_periods=10)
        print(f"大樂透測試完成！共生成 {len(results)} 期預測結果")
    except Exception as e:
        print(f"大樂透測試失敗：{str(e)}")
    
    print("\n開始執行今彩539預測測試...")
    
    # 測試今彩539
    try:
        tester = AutoPredictionTest('dailycash')
        results = tester.run_auto_test(num_periods=10)
        print(f"今彩539測試完成！共生成 {len(results)} 期預測結果")
    except Exception as e:
        print(f"今彩539測試失敗：{str(e)}")
    
    print("\n=== 所有測試完成 ===")
    print("結果已保存在各自的 auto_prediction_results_* 目錄中")

if __name__ == "__main__":
    main()