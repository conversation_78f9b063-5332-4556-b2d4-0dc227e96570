#!/usr/bin/env python3

import ast
import sys

def check_syntax(file_path):
    """检查Python文件的语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析AST
        ast.parse(content)
        print(f"✓ {file_path} 语法正确")
        return True
        
    except SyntaxError as e:
        print(f"✗ {file_path} 语法错误:")
        print(f"  行 {e.lineno}: {e.text.strip() if e.text else ''}")
        print(f"  错误: {e.msg}")
        return False
        
    except Exception as e:
        print(f"✗ 检查 {file_path} 时出错: {e}")
        return False

if __name__ == "__main__":
    file_path = "prediction/lottery_predictor.py"
    check_syntax(file_path)