#!/usr/bin/env python3
"""
Phase 3.6 生產部署系統測試
測試完整的部署流程和監控系統
"""

import os
import sys
import time
import json
import yaml
import requests
import subprocess
import tempfile
import unittest
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import patch, MagicMock

# 添加項目路徑
sys.path.append(str(Path(__file__).parent))

# 導入部署系統模塊
from phase3.production_deployment_system import (
    SystemMonitor, HealthChecker, LogManager, 
    BackupManager, DeploymentManager
)
from deploy import DeploymentScript

class TestProductionDeploymentSystem(unittest.TestCase):
    """生產部署系統測試"""
    
    @classmethod
    def setUpClass(cls):
        """測試類初始化"""
        cls.test_dir = Path(tempfile.mkdtemp())
        
        # 創建測試部署配置
        from phase3.production_deployment_system import DeploymentConfig, DeploymentMode
        cls.test_config = DeploymentConfig(
            mode=DeploymentMode.DEVELOPMENT,
            host="localhost",
            port=8000,
            workers=2,
            max_requests=1000,
            timeout=30,
            ssl_enabled=False,
            ssl_cert_path=None,
            ssl_key_path=None,
            database_url="sqlite:///test.db",
            redis_url="redis://localhost:6379",
            log_level="INFO",
            backup_enabled=True,
            monitoring_enabled=True
        )
        
        cls.deployment_manager = DeploymentManager(cls.test_config)
        cls.deployment_script = DeploymentScript()
        
    @classmethod
    def tearDownClass(cls):
        """測試類清理"""
        import shutil
        if cls.test_dir.exists():
            shutil.rmtree(cls.test_dir)
    
    def setUp(self):
        """測試方法初始化"""
        # 創建測試目錄結構
        test_dirs = [
            "logs", "data", "backups", "reports", 
            "docker", "phase3"
        ]
        for dir_name in test_dirs:
            (self.test_dir / dir_name).mkdir(exist_ok=True)
        
        # 創建測試配置文件
        self.create_test_config_files()
    
    def create_test_config_files(self):
        """創建測試配置文件"""
        # Docker Compose 配置
        docker_compose_config = {
            'version': '3.8',
            'services': {
                'lottery-api': {
                    'image': 'test-lottery-api:latest',
                    'ports': ['8000:8000']
                },
                'redis': {
                    'image': 'redis:7-alpine',
                    'ports': ['6379:6379']
                }
            }
        }
        
        with open(self.test_dir / "docker-compose.yml", 'w') as f:
            yaml.dump(docker_compose_config, f)
        
        # 部署配置
        deploy_config = {
            'deployment': {
                'environment': 'test',
                'domain': 'localhost'
            },
            'services': {
                'api': {
                    'port': 8000,
                    'workers': 2
                }
            }
        }
        
        with open(self.test_dir / "deploy_config.yaml", 'w') as f:
            yaml.dump(deploy_config, f)

class TestSystemMonitor(TestProductionDeploymentSystem):
    """系統監控測試"""
    
    def test_system_monitor_initialization(self):
        """測試系統監控初始化"""
        monitor = SystemMonitor(self.test_config)
        
        self.assertIsNotNone(monitor.config)
        self.assertEqual(monitor.monitoring_interval, 30)
        self.assertFalse(monitor.is_running)
    
    def test_collect_system_metrics(self):
        """測試系統指標收集"""
        monitor = SystemMonitor(self.test_config)
        metrics = monitor._collect_system_metrics()
        
        # 檢查指標完整性
        self.assertIsNotNone(metrics.timestamp)
        self.assertGreaterEqual(metrics.cpu_percent, 0)
        self.assertLessEqual(metrics.cpu_percent, 100)
        self.assertGreaterEqual(metrics.memory_percent, 0)
        self.assertLessEqual(metrics.memory_percent, 100)
        self.assertGreaterEqual(metrics.disk_percent, 0)
        self.assertLessEqual(metrics.disk_percent, 100)
    
    def test_performance_analysis(self):
        """測試性能分析"""
        monitor = SystemMonitor(self.test_config)
        
        # 模擬收集一些數據
        for i in range(5):
            monitor._collect_system_metrics()
            time.sleep(0.1)
        
        analysis = monitor.analyze_performance()
        
        # 檢查分析結果
        self.assertIn('cpu_trend', analysis)
        self.assertIn('memory_trend', analysis)
        self.assertIn('disk_trend', analysis)
        self.assertIn('alerts', analysis)
    
    @patch('requests.get')
    def test_service_health_monitoring(self, mock_get):
        """測試服務健康監控"""
        # 模擬健康的服務響應
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'status': 'healthy'}
        mock_get.return_value = mock_response
        
        monitor = SystemMonitor(self.test_config)
        health_status = monitor.check_service_health('http://localhost:8000/health')
        
        self.assertTrue(health_status['is_healthy'])
        self.assertEqual(health_status['status_code'], 200)

class TestHealthChecker(TestProductionDeploymentSystem):
    """健康檢查測試"""
    
    def test_health_checker_initialization(self):
        """測試健康檢查器初始化"""
        checker = HealthChecker(self.test_config)
        
        self.assertIsNotNone(checker.config)
        self.assertIsNotNone(checker.health_history)
    
    @patch('requests.get')
    def test_api_health_check(self, mock_get):
        """測試API健康檢查"""
        # 模擬健康的API響應
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'version': '3.6.0'
        }
        mock_get.return_value = mock_response
        
        checker = HealthChecker(self.test_config)
        result = checker.check_api_health()
        
        self.assertTrue(result['healthy'])
        self.assertIn('response_time', result)
        self.assertLess(result['response_time'], 1.0)  # 應該小於1秒
    
    @patch('subprocess.run')
    def test_database_health_check(self, mock_run):
        """測試數據庫健康檢查"""
        # 模擬成功的數據庫查詢
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_result.stdout = "1\n"
        mock_run.return_value = mock_result
        
        checker = HealthChecker(self.test_config)
        result = checker.check_database_health()
        
        self.assertTrue(result['healthy'])
        self.assertIn('response_time', result)
    
    def test_comprehensive_health_check(self):
        """測試綜合健康檢查"""
        checker = HealthChecker(self.test_config)
        
        with patch.object(checker, 'check_api_health') as mock_api, \
             patch.object(checker, 'check_database_health') as mock_db, \
             patch.object(checker, 'check_redis_health') as mock_redis:
            
            # 模擬各服務健康狀態
            mock_api.return_value = {'healthy': True, 'response_time': 0.1}
            mock_db.return_value = {'healthy': True, 'response_time': 0.05}
            mock_redis.return_value = {'healthy': True, 'response_time': 0.02}
            
            result = checker.run_health_checks()
            
            self.assertEqual(result['overall_status'], 'healthy')
            self.assertGreaterEqual(len(result['checks']), 1)

class TestLogManager(TestProductionDeploymentSystem):
    """日誌管理測試"""
    
    def test_log_manager_initialization(self):
        """測試日誌管理器初始化"""
        log_manager = LogManager(self.test_config)
        
        self.assertIsNotNone(log_manager.config)
        self.assertTrue(hasattr(log_manager, 'log_directory'))
    
    def test_log_rotation(self):
        """測試日誌輪轉"""
        log_dir = self.test_dir / "logs"
        log_file = log_dir / "test.log"
        
        # 創建測試日誌文件
        with open(log_file, 'w') as f:
            f.write("Test log content\n" * 1000)  # 創建大文件
        
        # 創建測試配置
        test_config = self.test_config
        test_config.log_directory = str(log_dir)
        log_manager = LogManager(test_config)
        
        # 執行日誌輪轉
        rotated_files = log_manager.rotate_logs()
        
        # 檢查是否創建了備份文件
        self.assertGreater(len(rotated_files), 0)
    
    def test_log_cleanup(self):
        """測試日誌清理"""
        log_dir = self.test_dir / "logs"
        
        # 創建一些舊日誌文件
        old_date = datetime.now() - timedelta(days=40)
        for i in range(5):
            log_file = log_dir / f"old_log_{i}.log"
            log_file.touch()
            # 設置文件修改時間為40天前
            os.utime(log_file, (old_date.timestamp(), old_date.timestamp()))
        
        # 創建測試配置
        test_config = self.test_config
        test_config.log_directory = str(log_dir)
        log_manager = LogManager(test_config)
        
        # 執行清理
        cleaned_files = log_manager.cleanup_old_logs()
        
        # 檢查是否清理了舊文件
        self.assertEqual(len(cleaned_files), 5)

class TestBackupManager(TestProductionDeploymentSystem):
    """備份管理測試"""
    
    def test_backup_manager_initialization(self):
        """測試備份管理器初始化"""
        backup_manager = BackupManager(self.test_config)
        
        self.assertIsNotNone(backup_manager.config)
        self.assertTrue(hasattr(backup_manager, 'data_directory'))
    
    def test_database_backup(self):
        """測試數據庫備份"""
        data_dir = self.test_dir / "data"
        backup_dir = self.test_dir / "backups"
        
        # 創建測試數據庫文件
        test_db = data_dir / "test.db"
        with open(test_db, 'w') as f:
            f.write("SQLite database content")
        
        # 創建測試配置
        test_config = self.test_config
        test_config.data_directory = str(data_dir)
        test_config.backup_directory = str(backup_dir)
        backup_manager = BackupManager(test_config)
        
        # 執行備份
        backup_file = backup_manager.backup_database("test.db")
        
        # 檢查備份文件是否存在
        self.assertTrue(backup_file.exists())
        self.assertIn("test.db", backup_file.name)
    
    def test_system_backup(self):
        """測試系統完整備份"""
        # 創建一些測試文件
        (self.test_dir / "data" / "config.json").write_text('{"test": "data"}')
        (self.test_dir / "reports" / "report.html").write_text('<html>Report</html>')
        
        # 創建測試配置
        test_config = self.test_config
        test_config.data_directory = str(self.test_dir / "data")
        test_config.backup_directory = str(self.test_dir / "backups")
        backup_manager = BackupManager(test_config)
        
        # 執行系統備份
        backup_info = backup_manager.create_system_backup()
        
        # 檢查備份信息
        self.assertIn('backup_file', backup_info)
        self.assertIn('file_count', backup_info)
        self.assertIn('total_size', backup_info)
        self.assertTrue(Path(backup_info['backup_file']).exists())
    
    def test_backup_cleanup(self):
        """測試備份清理"""
        backup_dir = self.test_dir / "backups"
        
        # 創建一些舊備份文件
        old_date = datetime.now() - timedelta(days=40)
        for i in range(3):
            backup_file = backup_dir / f"backup_{i}.tar.gz"
            backup_file.touch()
            # 設置文件修改時間為40天前
            os.utime(backup_file, (old_date.timestamp(), old_date.timestamp()))
        
        # 創建測試配置
        test_config = self.test_config
        test_config.data_directory = str(self.test_dir / "data")
        test_config.backup_directory = str(backup_dir)
        backup_manager = BackupManager(test_config)
        
        # 執行清理
        cleaned_backups = backup_manager.cleanup_old_backups()
        
        # 檢查是否清理了舊備份
        self.assertEqual(len(cleaned_backups), 3)

class TestDeploymentManager(TestProductionDeploymentSystem):
    """部署管理測試"""
    
    def test_deployment_manager_initialization(self):
        """測試部署管理器初始化"""
        deployment_manager = DeploymentManager(self.test_config)
        
        self.assertIsNotNone(deployment_manager.config)
        self.assertIsNotNone(deployment_manager.system_monitor)
        self.assertIsNotNone(deployment_manager.health_checker)
    
    @patch('subprocess.run')
    def test_docker_operations(self, mock_run):
        """測試Docker操作"""
        # 模擬成功的Docker命令
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_result.stdout = "Docker operation successful"
        mock_run.return_value = mock_result
        
        deployment_manager = DeploymentManager(self.test_config)
        
        # 測試Docker構建
        result = deployment_manager.build_docker_images()
        self.assertTrue(result)
        
        # 測試Docker啟動
        result = deployment_manager.start_services()
        self.assertTrue(result)
        
        # 測試Docker停止
        result = deployment_manager.stop_services()
        self.assertTrue(result)
    
    def test_deployment_validation(self):
        """測試部署驗證"""
        deployment_manager = DeploymentManager(self.test_config)
        
        with patch.object(deployment_manager.health_checker, 'run_health_checks') as mock_health:
            mock_health.return_value = {
                'overall_status': 'healthy',
                'checks': [
                    {'name': 'api', 'healthy': True},
                    {'name': 'database', 'healthy': True}
                ]
            }
            
            validation_result = deployment_manager.validate_deployment()
            
            self.assertTrue(validation_result['success'])
            self.assertEqual(validation_result['status'], 'healthy')

class TestDeploymentScript(TestProductionDeploymentSystem):
    """部署腳本測試"""
    
    def test_deployment_script_initialization(self):
        """測試部署腳本初始化"""
        # 修改部署腳本的項目根目錄為測試目錄
        script = DeploymentScript()
        script.project_root = self.test_dir
        script.config_file = self.test_dir / "deploy_config.yaml"
        
        self.assertEqual(script.project_root, self.test_dir)
    
    def test_config_creation_and_loading(self):
        """測試配置創建和載入"""
        script = DeploymentScript()
        script.project_root = self.test_dir
        script.config_file = self.test_dir / "test_deploy_config.yaml"
        
        # 測試創建默認配置
        config = script.create_default_config()
        self.assertIn('deployment', config)
        self.assertIn('services', config)
        
        # 測試載入配置
        loaded_config = script.load_config()
        self.assertEqual(config, loaded_config)
    
    @patch('shutil.disk_usage')
    @patch('subprocess.run')
    def test_prerequisites_check(self, mock_run, mock_disk_usage):
        """測試先決條件檢查"""
        # 模擬Docker命令成功
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_result.stdout = "Docker version 24.0.0"
        mock_run.return_value = mock_result
        
        # 模擬足夠的磁盤空間 (10GB)
        mock_disk_usage.return_value = (100 * 1024**3, 80 * 1024**3, 20 * 1024**3)
        
        script = DeploymentScript()
        result = script.check_prerequisites()
        
        self.assertTrue(result)
    
    def test_environment_preparation(self):
        """測試環境準備"""
        script = DeploymentScript()
        script.project_root = self.test_dir
        
        config = {
            'deployment': {'environment': 'test'},
            'services': {
                'api': {'host': '0.0.0.0', 'port': 8000, 'workers': 2, 'timeout': 30},
                'web': {'port': 8080},
                'redis': {'max_memory': '256mb'},
                'grafana': {'admin_password': 'admin123'}
            },
            'database': {'backup_schedule': '0 2 * * *', 'backup_retention': 30},
            'security': {'rate_limit_api': '10r/s', 'rate_limit_web': '5r/s'}
        }
        
        # 執行環境準備
        script.prepare_environment(config)
        
        # 檢查目錄是否創建
        expected_dirs = ['logs', 'data', 'backups', 'reports', 'exports']
        for dir_name in expected_dirs:
            self.assertTrue((self.test_dir / dir_name).exists())
        
        # 檢查環境變量文件是否創建
        env_file = self.test_dir / ".env"
        self.assertTrue(env_file.exists())
        
        # 檢查環境變量內容
        env_content = env_file.read_text()
        self.assertIn("LOTTERY_SYSTEM_MODE=test", env_content)
        self.assertIn("API_PORT=8000", env_content)

class TestIntegration(TestProductionDeploymentSystem):
    """集成測試"""
    
    def test_full_deployment_workflow(self):
        """測試完整部署工作流"""
        deployment_manager = DeploymentManager(self.test_config)
        
        # 模擬各個組件
        with patch.object(deployment_manager, 'build_docker_images', return_value=True), \
             patch.object(deployment_manager, 'start_services', return_value=True), \
             patch.object(deployment_manager.health_checker, 'run_health_checks') as mock_health:
            
            mock_health.return_value = {
                'overall_status': 'healthy',
                'checks': [
                    {'name': 'api', 'healthy': True, 'response_time': 0.1},
                    {'name': 'database', 'healthy': True, 'response_time': 0.05}
                ]
            }
            
            # 執行部署
            result = deployment_manager.deploy()
            
            self.assertTrue(result['success'])
            self.assertEqual(result['status'], 'deployed')
    
    def test_monitoring_and_alerting_integration(self):
        """測試監控和警報集成"""
        deployment_manager = DeploymentManager(self.test_config)
        
        # 模擬系統指標收集
        monitor = deployment_manager.system_monitor
        
        # 收集一些測試數據
        for _ in range(3):
            monitor._collect_system_metrics()
            time.sleep(0.1)
        
        # 分析性能
        analysis = monitor.analyze_performance()
        
        # 檢查分析結果包含必要信息
        self.assertIn('cpu_trend', analysis)
        self.assertIn('memory_trend', analysis)
        self.assertIn('alerts', analysis)
    
    def test_backup_and_recovery_integration(self):
        """測試備份和恢復集成"""
        # 創建測試數據
        data_dir = self.test_dir / "data"
        test_files = [
            "lottery_prediction.db",
            "config.json",
            "cache.db"
        ]
        
        for file_name in test_files:
            (data_dir / file_name).write_text(f"Test content for {file_name}")
        
        # 創建測試配置
        test_config = self.test_config
        test_config.data_directory = str(data_dir)
        test_config.backup_directory = str(self.test_dir / "backups")
        backup_manager = BackupManager(test_config)
        
        # 創建系統備份
        backup_info = backup_manager.create_system_backup()
        
        # 驗證備份
        self.assertTrue(Path(backup_info['backup_file']).exists())
        self.assertEqual(backup_info['file_count'], len(test_files))

def run_deployment_tests():
    """運行所有部署測試"""
    # 創建測試套件
    test_classes = [
        TestSystemMonitor,
        TestHealthChecker,
        TestLogManager,
        TestBackupManager,
        TestDeploymentManager,
        TestDeploymentScript,
        TestIntegration
    ]
    
    suite = unittest.TestSuite()
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # 運行測試
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 生成測試報告
    generate_test_report(result)
    
    return result.wasSuccessful()

def generate_test_report(test_result):
    """生成測試報告"""
    report = {
        'timestamp': datetime.now().isoformat(),
        'total_tests': test_result.testsRun,
        'failures': len(test_result.failures),
        'errors': len(test_result.errors),
        'success_rate': ((test_result.testsRun - len(test_result.failures) - len(test_result.errors)) / test_result.testsRun * 100) if test_result.testsRun > 0 else 0,
        'details': {
            'failures': [{'test': str(test), 'error': error} for test, error in test_result.failures],
            'errors': [{'test': str(test), 'error': error} for test, error in test_result.errors]
        }
    }
    
    # 保存測試報告
    report_file = Path("test_deployment_report.json")
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 測試報告:")
    print(f"   總測試數: {report['total_tests']}")
    print(f"   失敗數: {report['failures']}")
    print(f"   錯誤數: {report['errors']}")
    print(f"   成功率: {report['success_rate']:.1f}%")
    print(f"   報告文件: {report_file}")

if __name__ == "__main__":
    print("🧪 開始運行 Phase 3.6 生產部署系統測試...")
    print("=" * 60)
    
    success = run_deployment_tests()
    
    print("=" * 60)
    if success:
        print("✅ 所有測試通過！生產部署系統準備就緒。")
    else:
        print("❌ 部分測試失敗，請檢查問題後重新測試。")
    
    exit(0 if success else 1)