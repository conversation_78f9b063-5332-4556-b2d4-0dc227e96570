"""
清理重複預測記錄
刪除相同期數、相同號碼組合的重複預測記錄，只保留最新的一筆
"""

import sqlite3
import logging
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/cleanup_duplicates_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def cleanup_duplicate_predictions():
    """清理重複的預測記錄"""
    
    try:
        conn = sqlite3.connect('data/lottery_data.db')
        cursor = conn.cursor()
        
        print("=== 清理重複預測記錄 ===")
        
        # 1. 檢查威力彩重複預測
        print("\n1. 檢查威力彩重複預測...")
        
        # 查找重複記錄（相同期數、相同號碼組合）
        cursor.execute('''
        SELECT Period, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredS, 
               COUNT(*) as count
        FROM PowerColorPredictions 
        GROUP BY Period, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredS
        HAVING COUNT(*) > 1
        ORDER BY count DESC, Period DESC
        ''')
        
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"找到 {len(duplicates)} 組重複的威力彩預測記錄:")
            total_duplicates = 0
            
            for dup in duplicates[:10]:  # 只顯示前10組
                period, a1, a2, a3, a4, a5, a6, s, count = dup
                total_duplicates += (count - 1)  # 每組保留1筆，其餘為重複
                print(f"  期數: {period}, 號碼: {a1}-{a2}-{a3}-{a4}-{a5}-{a6}, 第二區: {s}, 重複: {count} 次")
            
            if len(duplicates) > 10:
                print(f"  ... 還有 {len(duplicates) - 10} 組重複記錄")
            
            print(f"\n總共有 {total_duplicates} 筆重複記錄需要清理")
            
            response = input("\n是否要清理威力彩重複記錄？(y/N): ").strip().lower()
            
            if response == 'y':
                print("\n開始清理威力彩重複記錄...")
                
                # 對每組重複記錄，只保留最新的一筆（ID最大的）
                for dup in duplicates:
                    period, a1, a2, a3, a4, a5, a6, s, count = dup
                    
                    # 找到這組重複記錄中的所有ID
                    cursor.execute('''
                    SELECT ID FROM PowerColorPredictions
                    WHERE Period = ? AND PredA1 = ? AND PredA2 = ? AND PredA3 = ?
                          AND PredA4 = ? AND PredA5 = ? AND PredA6 = ? AND PredS = ?
                    ORDER BY ID DESC
                    ''', (period, a1, a2, a3, a4, a5, a6, s))

                    all_ids = cursor.fetchall()
                    # 保留最新的一筆（第一個），刪除其餘的
                    old_ids = all_ids[1:] if len(all_ids) > 1 else []
                    
                    old_ids = cursor.fetchall()
                    
                    if old_ids:
                        # 刪除舊的重複記錄
                        old_id_list = [str(row[0]) for row in old_ids]
                        cursor.execute(f'''
                        DELETE FROM PowerColorPredictions 
                        WHERE ID IN ({','.join(old_id_list)})
                        ''')
                
                conn.commit()
                deleted_count = cursor.rowcount
                print(f"✅ 已清理 {deleted_count} 筆威力彩重複記錄")
            else:
                print("取消清理威力彩重複記錄")
        else:
            print("沒有找到威力彩重複記錄")
        
        # 2. 檢查大樂透重複預測
        print("\n2. 檢查大樂透重複預測...")
        
        cursor.execute('''
        SELECT Period, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredS, 
               COUNT(*) as count
        FROM Lotto649Predictions 
        GROUP BY Period, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredS
        HAVING COUNT(*) > 1
        ORDER BY count DESC, Period DESC
        ''')
        
        lotto_duplicates = cursor.fetchall()
        
        if lotto_duplicates:
            print(f"找到 {len(lotto_duplicates)} 組重複的大樂透預測記錄")
            
            response = input("是否要清理大樂透重複記錄？(y/N): ").strip().lower()
            
            if response == 'y':
                # 類似的清理邏輯...
                for dup in lotto_duplicates:
                    period, a1, a2, a3, a4, a5, a6, s, count = dup
                    
                    cursor.execute('''
                    SELECT ID FROM Lotto649Predictions
                    WHERE Period = ? AND PredA1 = ? AND PredA2 = ? AND PredA3 = ?
                          AND PredA4 = ? AND PredA5 = ? AND PredA6 = ? AND PredS = ?
                    ORDER BY ID DESC
                    ''', (period, a1, a2, a3, a4, a5, a6, s))

                    all_ids = cursor.fetchall()
                    old_ids = all_ids[1:] if len(all_ids) > 1 else []
                    
                    old_ids = cursor.fetchall()
                    
                    if old_ids:
                        old_id_list = [str(row[0]) for row in old_ids]
                        cursor.execute(f'''
                        DELETE FROM Lotto649Predictions 
                        WHERE ID IN ({','.join(old_id_list)})
                        ''')
                
                conn.commit()
                print(f"✅ 已清理大樂透重複記錄")
        else:
            print("沒有找到大樂透重複記錄")
        
        # 3. 檢查今彩539重複預測
        print("\n3. 檢查今彩539重複預測...")
        
        cursor.execute('''
        SELECT Period, PredA1, PredA2, PredA3, PredA4, PredA5, 
               COUNT(*) as count
        FROM DailyCashPredictions 
        GROUP BY Period, PredA1, PredA2, PredA3, PredA4, PredA5
        HAVING COUNT(*) > 1
        ORDER BY count DESC, Period DESC
        ''')
        
        daily_duplicates = cursor.fetchall()
        
        if daily_duplicates:
            print(f"找到 {len(daily_duplicates)} 組重複的今彩539預測記錄")
            
            response = input("是否要清理今彩539重複記錄？(y/N): ").strip().lower()
            
            if response == 'y':
                # 類似的清理邏輯...
                for dup in daily_duplicates:
                    period, a1, a2, a3, a4, a5, count = dup
                    
                    cursor.execute('''
                    SELECT ID FROM DailyCashPredictions
                    WHERE Period = ? AND PredA1 = ? AND PredA2 = ? AND PredA3 = ?
                          AND PredA4 = ? AND PredA5 = ?
                    ORDER BY ID DESC
                    ''', (period, a1, a2, a3, a4, a5))

                    all_ids = cursor.fetchall()
                    old_ids = all_ids[1:] if len(all_ids) > 1 else []
                    
                    old_ids = cursor.fetchall()
                    
                    if old_ids:
                        old_id_list = [str(row[0]) for row in old_ids]
                        cursor.execute(f'''
                        DELETE FROM DailyCashPredictions 
                        WHERE ID IN ({','.join(old_id_list)})
                        ''')
                
                conn.commit()
                print(f"✅ 已清理今彩539重複記錄")
        else:
            print("沒有找到今彩539重複記錄")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"清理重複記錄時出錯: {str(e)}")
        print(f"錯誤: {e}")

def cleanup_specific_period_duplicates(period):
    """清理特定期數的重複記錄"""
    
    try:
        conn = sqlite3.connect('data/lottery_data.db')
        cursor = conn.cursor()
        
        print(f"\n=== 清理期數 {period} 的重複記錄 ===")
        
        # 查詢該期數的重複記錄
        cursor.execute('''
        SELECT PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredS, 
               COUNT(*) as count
        FROM PowerColorPredictions 
        WHERE Period = ?
        GROUP BY PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredS
        HAVING COUNT(*) > 1
        ORDER BY count DESC
        ''', (period,))
        
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"期數 {period} 找到 {len(duplicates)} 組重複記錄:")
            total_to_delete = 0
            
            for dup in duplicates:
                a1, a2, a3, a4, a5, a6, s, count = dup
                total_to_delete += (count - 1)
                print(f"  號碼: {a1}-{a2}-{a3}-{a4}-{a5}-{a6}, 第二區: {s}, 重複: {count} 次")
            
            print(f"\n總共需要刪除 {total_to_delete} 筆重複記錄")
            
            response = input(f"\n是否要清理期數 {period} 的重複記錄？(y/N): ").strip().lower()
            
            if response == 'y':
                deleted_total = 0
                
                for dup in duplicates:
                    a1, a2, a3, a4, a5, a6, s, count = dup
                    
                    # 找到這組重複記錄中的所有ID
                    cursor.execute('''
                    SELECT ID FROM PowerColorPredictions
                    WHERE Period = ? AND PredA1 = ? AND PredA2 = ? AND PredA3 = ?
                          AND PredA4 = ? AND PredA5 = ? AND PredA6 = ? AND PredS = ?
                    ORDER BY ID DESC
                    ''', (period, a1, a2, a3, a4, a5, a6, s))

                    all_ids = cursor.fetchall()
                    # 保留最新的一筆（第一個），刪除其餘的
                    old_ids = all_ids[1:] if len(all_ids) > 1 else []
                    
                    old_ids = cursor.fetchall()
                    
                    if old_ids:
                        old_id_list = [str(row[0]) for row in old_ids]
                        cursor.execute(f'''
                        DELETE FROM PowerColorPredictions 
                        WHERE ID IN ({','.join(old_id_list)})
                        ''')
                        deleted_total += len(old_ids)
                
                conn.commit()
                print(f"✅ 已刪除期數 {period} 的 {deleted_total} 筆重複記錄")
                
                # 驗證結果
                cursor.execute('''
                SELECT COUNT(*) FROM PowerColorPredictions WHERE Period = ?
                ''', (period,))
                remaining_count = cursor.fetchone()[0]
                print(f"期數 {period} 剩餘記錄數: {remaining_count}")
                
            else:
                print("取消清理操作")
        else:
            print(f"期數 {period} 沒有重複記錄")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"清理期數 {period} 重複記錄時出錯: {str(e)}")
        print(f"錯誤: {e}")

if __name__ == "__main__":
    # 清理所有重複記錄
    cleanup_duplicate_predictions()
    
    # 特別清理期數 114000050
    cleanup_specific_period_duplicates('114000050')
