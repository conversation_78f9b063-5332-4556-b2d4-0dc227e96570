#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试类结构
"""

import sys
import os
import inspect

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def debug_class_structure():
    """调试类结构"""
    try:
        from prediction.lottery_predictor import LotteryPredictor
        
        print("=== 类信息 ===")
        print(f"类: {LotteryPredictor}")
        print(f"模块: {LotteryPredictor.__module__}")
        print(f"文件: {inspect.getfile(LotteryPredictor)}")
        
        print("\n=== 所有属性和方法 ===")
        all_attrs = dir(LotteryPredictor)
        for attr in sorted(all_attrs):
            if not attr.startswith('__'):
                attr_obj = getattr(LotteryPredictor, attr)
                attr_type = type(attr_obj).__name__
                print(f"{attr}: {attr_type}")
        
        print("\n=== 实例方法检查 ===")
        predictor = LotteryPredictor()
        
        # 检查特定方法
        methods_to_check = [
            'update_actual_numbers',
            'get_prediction_accuracy', 
            'predict_with_enhanced_analysis',
            '_merge_predictions',
            '_get_lottery_name'
        ]
        
        for method_name in methods_to_check:
            has_method = hasattr(predictor, method_name)
            print(f"{method_name}: {has_method}")
            
            if has_method:
                method = getattr(predictor, method_name)
                print(f"  类型: {type(method)}")
                print(f"  可调用: {callable(method)}")
                if hasattr(method, '__func__'):
                    print(f"  函数: {method.__func__}")
                    print(f"  代码对象: {method.__func__.__code__}")
                    print(f"  文件名: {method.__func__.__code__.co_filename}")
                    print(f"  行号: {method.__func__.__code__.co_firstlineno}")
            print()
        
        print("\n=== 类的MRO ===")
        print(LotteryPredictor.__mro__)
        
        print("\n=== 类字典中的方法 ===")
        for name, obj in LotteryPredictor.__dict__.items():
            if callable(obj) and not name.startswith('__'):
                print(f"{name}: {type(obj)} (行号: {getattr(obj, '__code__', {}).get('co_firstlineno', 'N/A')})")
        
    except Exception as e:
        print(f"调试时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    debug_class_structure()