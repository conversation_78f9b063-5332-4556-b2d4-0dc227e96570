#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化的訓練測試腳本
測試單一彩票類型在不同訓練天數下的效果
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
from prediction.lottery_predictor import LotteryPredictor
from data.feature_engineering import FeatureEngineer

def decode_binary_number(binary_data):
    """解碼二進制數據為整數"""
    if isinstance(binary_data, bytes):
        # 從二進制數據中提取整數（小端序）
        return int.from_bytes(binary_data[:4], byteorder='little')
    elif isinstance(binary_data, (int, float)):
        return int(binary_data)
    else:
        try:
            return int(binary_data)
        except:
            return 0

def get_training_data(lottery_type, start_date, end_date):
    """獲取訓練資料並處理二進制數據"""
    try:
        db_path = 'lottery_data.db'
        conn = sqlite3.connect(db_path)
        
        if lottery_type == 'powercolor':
            query = """
            SELECT Sdate, An<PERSON>ber1, An<PERSON>ber2, <PERSON><PERSON>ber3, <PERSON><PERSON>ber4, <PERSON><PERSON>ber5, <PERSON><PERSON><PERSON>6, Second_district
            FROM powercolor 
            WHERE Sdate >= ? AND Sdate <= ?
            ORDER BY Sdate
            """
        elif lottery_type == 'lotto649':
            query = """
            SELECT Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, SpecialNumber
            FROM lotto649 
            WHERE Sdate >= ? AND Sdate <= ?
            ORDER BY Sdate
            """
        elif lottery_type == 'dailycash':
            query = """
            SELECT Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5
            FROM dailycash 
            WHERE Sdate >= ? AND Sdate <= ?
            ORDER BY Sdate
            """
        else:
            return None
            
        training_data = pd.read_sql_query(query, conn, params=(start_date, end_date))
        conn.close()
        
        if training_data.empty:
            return None
            
        # 添加Period欄位
        training_data['Period'] = range(1, len(training_data) + 1)
        
        # 轉換Sdate為datetime格式
        training_data['Sdate'] = pd.to_datetime(training_data['Sdate'])
        
        # 處理二進制數據
        number_columns = ['Anumber1', 'Anumber2', 'Anumber3', 'Anumber4', 'Anumber5']
        if lottery_type in ['powercolor', 'lotto649']:
            number_columns.append('Anumber6')
        if lottery_type == 'powercolor':
            number_columns.append('Second_district')
        elif lottery_type == 'lotto649':
            number_columns.append('SpecialNumber')
            
        for col in number_columns:
            if col in training_data.columns:
                training_data[col] = training_data[col].apply(decode_binary_number)
                
        return training_data
        
    except Exception as e:
        print(f"獲取訓練資料時發生錯誤: {e}")
        return None

def test_single_lottery(lottery_type='powercolor', target_date='2025-06-20'):
    """測試單一彩票類型"""
    print(f"\n開始測試 {lottery_type}...")
    print(f"目標預測日期: {target_date}")
    
    training_periods = [30, 60, 90, 120]
    results = {}
    
    for days in training_periods:
        print(f"\n測試 {days} 天訓練資料...")
        
        try:
            # 計算訓練資料的開始日期
            target_dt = datetime.strptime(target_date, '%Y-%m-%d')
            start_date = (target_dt - timedelta(days=days)).strftime('%Y-%m-%d')
            end_date = (target_dt - timedelta(days=1)).strftime('%Y-%m-%d')
            
            print(f"訓練資料期間: {start_date} 到 {end_date}")
            
            # 獲取訓練資料
            training_data = get_training_data(lottery_type, start_date, end_date)
            
            if training_data is None or len(training_data) < 10:
                print(f"訓練資料不足 ({len(training_data) if training_data is not None else 0} 筆)")
                results[f"{days}_days"] = {
                    'status': 'failed',
                    'reason': f'訓練資料不足 ({len(training_data) if training_data is not None else 0} 筆)',
                    'training_records': len(training_data) if training_data is not None else 0
                }
                continue
                
            print(f"獲得 {len(training_data)} 筆訓練資料")
            print(f"資料範例: {training_data.head(2)}")
            
            # 特徵工程
            feature_eng = FeatureEngineer()
            features_df = feature_eng.create_basic_features(training_data, lottery_type)
            
            if features_df.empty:
                print("特徵工程失敗")
                results[f"{days}_days"] = {
                    'status': 'failed',
                    'reason': '特徵工程失敗',
                    'training_records': len(training_data)
                }
                continue
                
            print(f"特徵工程成功，創建 {len(features_df)} 筆特徵")
            
            # 獲取最新特徵
            latest_features = features_df.iloc[-1:]
            
            # 初始化預測器並進行預測
            predictor = LotteryPredictor()
            prediction = predictor.predict(
                df=training_data,
                features=latest_features,
                lottery_type=lottery_type,
                prediction_methods=['ml']
            )
            
            if prediction:
                print(f"預測成功: {prediction}")
                results[f"{days}_days"] = {
                    'status': 'success',
                    'prediction': str(prediction),
                    'training_records': len(training_data),
                    'features_count': len(features_df)
                }
            else:
                print("預測失敗")
                results[f"{days}_days"] = {
                    'status': 'failed',
                    'reason': '預測失敗',
                    'training_records': len(training_data)
                }
                
        except Exception as e:
            print(f"測試 {days} 天訓練時發生錯誤: {e}")
            results[f"{days}_days"] = {
                'status': 'failed',
                'reason': str(e),
                'training_records': 0
            }
    
    return results

if __name__ == "__main__":
    # 測試威力彩
    results = test_single_lottery('powercolor', '2025-06-20')
    
    print("\n" + "="*60)
    print("測試結果摘要")
    print("="*60)
    
    for period, result in results.items():
        print(f"{period}: {result['status']} - {result.get('reason', 'OK')}")
        if result['status'] == 'success':
            print(f"  預測結果: {result['prediction']}")
            print(f"  訓練資料: {result['training_records']} 筆")