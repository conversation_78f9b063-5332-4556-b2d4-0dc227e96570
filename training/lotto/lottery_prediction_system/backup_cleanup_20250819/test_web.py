#!/usr/bin/env python3
"""
彩票預測系統 - 簡化測試Web應用
"""

from flask import Flask, render_template, jsonify, request
import os
import sys

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

app = Flask(__name__, template_folder='web/templates')

@app.route('/')
def index():
    """首頁"""
    try:
        return render_template('index.html')
    except Exception as e:
        return f"""
        <html>
        <head><title>彩票預測系統</title></head>
        <body>
            <h1>🎯 彩票預測系統</h1>
            <h2>✅ Web應用已成功啟動！</h2>
            <p>系統正在運行中...</p>
            <p>錯誤信息: {str(e)}</p>
            <hr>
            <h3>🌟 主要功能</h3>
            <ul>
                <li>✅ 分離式預測結果展示</li>
                <li>✅ 預測成功原因分析</li>
                <li>✅ 號碼出現原因深度解釋</li>
                <li>✅ 板路分析數學關係擴展</li>
                <li>✅ 自動化日更新系統</li>
                <li>✅ Web可視化界面</li>
            </ul>
            <hr>
            <h3>🎯 核心價值</h3>
            <p><strong>正如您所說，號碼出現都有其原因！</strong></p>
            <p>這個系統不僅能預測號碼，還能解釋為什麼這些號碼會出現：</p>
            <ul>
                <li>🔍 連號 (1,2,3,4,5,6)：分析極罕見的完全連續號碼組合</li>
                <li>🔗 板路關聯：檢測號碼間的跟隨和跳躍模式</li>
                <li>🧮 數學模式：等差、倍數、平方、質數、費波那契等</li>
                <li>📊 統計回歸：冷號回歸、熱號降溫的分析</li>
            </ul>
        </body>
        </html>
        """

@app.route('/api/test')
def api_test():
    """測試API"""
    return jsonify({
        'status': 'success',
        'message': '彩票預測系統API正常運行',
        'features': [
            '分離式預測結果展示',
            '預測成功原因分析',
            '號碼出現原因深度解釋',
            '板路分析數學關係擴展',
            '自動化日更新系統',
            'Web可視化界面'
        ]
    })

@app.route('/api/latest_predictions/<lottery_type>')
def api_latest_predictions(lottery_type):
    """API: 獲取最新預測（模擬數據）"""
    # 模擬預測數據
    predictions = [
        {
            'Period': '113000123',
            'PredA1': 3, 'PredA2': 15, 'PredA3': 22, 'PredA4': 28, 'PredA5': 35, 'PredA6': 38,
            'PredSpecial': 8,
            'PredictionDate': '2025-06-21'
        },
        {
            'Period': '113000122',
            'PredA1': 7, 'PredA2': 14, 'PredA3': 21, 'PredA4': 28, 'PredA5': 35, 'PredA6': 42,
            'PredSpecial': 5,
            'PredictionDate': '2025-06-20'
        }
    ]

    return jsonify({'predictions': predictions})

@app.route('/api/comprehensive_analysis/<lottery_type>')
def api_comprehensive_analysis(lottery_type):
    """API: 綜合分析（模擬數據）"""
    # 模擬綜合分析數據
    analysis_result = {
        'lottery_type': lottery_type,
        'analysis_time': '2025-06-21 09:30:00',
        'summary': {
            'total_predictions': 156,
            'successful_predictions': 23,
            'success_rate': 0.147,
            'latest_period': '113000123'
        },
        'success_analysis': {
            'ml_success_factors': [
                '特徵重要性分析顯示強相關性',
                '歷史模式匹配度較高',
                '統計回歸模型表現優異',
                '時間序列特徵有效',
                '組合特徵工程成功'
            ],
            'board_path_success_factors': [
                '板路跟隨模式準確',
                '跳號間隔預測精準',
                '冷熱號轉換時機把握良好',
                '數字關聯性分析有效',
                '歷史走勢判斷正確'
            ],
            'common_success_patterns': [
                '中等和值範圍預測',
                '大小號平衡組合',
                '連號適度出現',
                '質數與合數搭配',
                '奇偶數均衡分布'
            ]
        },
        'pattern_analysis': {
            'numbers': [3, 15, 22, 28, 35, 38],
            'appearance_reasons': [
                '🔢 包含質數 [3] - 質數在數學中具有特殊地位',
                '📐 存在等差關係 - 可能反映數學規律性',
                '📊 統計分布: 中等和值 - 符合歷史分布特性',
                '🔥 包含中溫號碼 - 這些號碼近期出現頻率適中',
                '🧮 數字間距合理 - 避免過度集中或分散'
            ]
        },
        'enhanced_analysis': {
            'arithmetic_relationships': [
                {'type': 'addition', 'formula': '15 + 7 = 22', 'strength': 0.8},
                {'type': 'subtraction', 'formula': '35 - 7 = 28', 'strength': 0.7}
            ],
            'sequence_relationships': [
                {'type': 'arithmetic_sequence', 'formula': '等差數列: 15, 22, 29 (公差=7)', 'strength': 0.9}
            ],
            'modular_relationships': [
                {'type': 'modular_pattern', 'modulus': 7, 'remainders': [3, 1, 1, 0, 0, 3], 'strength': 0.6}
            ]
        }
    }

    return jsonify({'success': True, 'analysis': analysis_result})

@app.route('/separated_prediction')
def separated_prediction():
    """分離式預測頁面"""
    return """
    <html>
    <head>
        <title>分離式預測 - 彩票預測系統</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">🚀 分離式預測系統</a>
            </div>
        </nav>
        <div class="container mt-4">
            <h1>🚀 分離式預測系統</h1>
            <p class="text-muted">機器學習和板路分析獨立運行，提供詳細的預測理由和成功分析</p>

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5>🤖 機器學習預測</h5>
                        </div>
                        <div class="card-body">
                            <h6>候選 #1 (信心分數: 0.8542)</h6>
                            <p><strong>預測號碼:</strong> 3, 15, 22, 28, 35, 38 + 8</p>
                            <p><strong>預測依據:</strong></p>
                            <ul>
                                <li>特徵重要性分析顯示強相關性</li>
                                <li>歷史模式匹配度較高</li>
                                <li>統計回歸模型表現優異</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5>📊 板路分析預測</h5>
                        </div>
                        <div class="card-body">
                            <h6>候選 #1 (信心分數: 0.7834)</h6>
                            <p><strong>預測號碼:</strong> 7, 14, 21, 28, 35, 42 + 5</p>
                            <p><strong>板路分析依據:</strong></p>
                            <ul>
                                <li>板路跟隨模式準確</li>
                                <li>跳號間隔預測精準</li>
                                <li>等差數列關係 (公差=7)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """

@app.route('/comprehensive_analysis')
def comprehensive_analysis():
    """綜合分析頁面"""
    try:
        return render_template('comprehensive_analysis.html')
    except Exception as e:
        return f"""
        <html>
        <head><title>綜合分析 - 彩票預測系統</title></head>
        <body>
            <h1>🔬 綜合分析系統</h1>
            <p>模板載入錯誤: {str(e)}</p>
            <p>但系統核心功能已實現！</p>
        </body>
        </html>
        """

@app.route('/enhanced_prediction')
def enhanced_prediction():
    """智能預測頁面"""
    try:
        return render_template('enhanced_prediction.html')
    except Exception as e:
        return f"""
        <html>
        <head><title>智能預測 - 彩票預測系統</title></head>
        <body>
            <h1>🧠 智能預測系統</h1>
            <p>模板載入錯誤: {str(e)}</p>
            <p>但系統核心功能已實現！</p>
        </body>
        </html>
        """

@app.route('/predictions')
def predictions():
    """預測記錄頁面"""
    lottery_type = request.args.get('type', 'powercolor')

    # 模擬預測記錄數據
    records = [
        {
            'Period': '113000123',
            'PredA1': 3, 'PredA2': 15, 'PredA3': 22, 'PredA4': 28, 'PredA5': 35, 'PredA6': 38,
            'PredSpecial': 8,
            'PredictionDate': '2025-06-21',
            'Method': '機器學習',
            'Confidence': 0.854,
            'MatchCount': 4
        },
        {
            'Period': '113000122',
            'PredA1': 7, 'PredA2': 14, 'PredA3': 21, 'PredA4': 28, 'PredA5': 35, 'PredA6': 42,
            'PredSpecial': 5,
            'PredictionDate': '2025-06-20',
            'Method': '板路分析',
            'Confidence': 0.783,
            'MatchCount': 3
        },
        {
            'Period': '113000121',
            'PredA1': 1, 'PredA2': 8, 'PredA3': 15, 'PredA4': 22, 'PredA5': 29, 'PredA6': 36,
            'PredSpecial': 7,
            'PredictionDate': '2025-06-19',
            'Method': '綜合預測',
            'Confidence': 0.692,
            'MatchCount': 2
        }
    ]

    lottery_names = {
        'powercolor': '威力彩',
        'lotto649': '大樂透',
        'dailycash': '今彩539'
    }

    return f"""
    <html>
    <head>
        <title>預測記錄 - 彩票預測系統</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/"><i class="fas fa-chart-line"></i> 彩票預測系統</a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/">首頁</a>
                    <a class="nav-link active" href="/predictions">預測記錄</a>
                    <a class="nav-link" href="/analysis">分析報告</a>
                    <a class="nav-link" href="/comprehensive_analysis">綜合分析</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <h1><i class="fas fa-history"></i> {lottery_names.get(lottery_type, '彩票')}預測記錄</h1>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="btn-group" role="group">
                        <a href="/predictions?type=powercolor" class="btn {'btn-primary' if lottery_type == 'powercolor' else 'btn-outline-primary'}">威力彩</a>
                        <a href="/predictions?type=lotto649" class="btn {'btn-primary' if lottery_type == 'lotto649' else 'btn-outline-primary'}">大樂透</a>
                        <a href="/predictions?type=dailycash" class="btn {'btn-primary' if lottery_type == 'dailycash' else 'btn-outline-primary'}">今彩539</a>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-table"></i> 最近預測記錄</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>期數</th>
                                    <th>預測號碼</th>
                                    <th>預測方法</th>
                                    <th>信心分數</th>
                                    <th>匹配數</th>
                                    <th>預測日期</th>
                                </tr>
                            </thead>
                            <tbody>
    """

    table_rows = ""
    for record in records:
        numbers = f"{record['PredA1']}, {record['PredA2']}, {record['PredA3']}, {record['PredA4']}, {record['PredA5']}, {record['PredA6']}"
        if lottery_type in ['powercolor', 'lotto649']:
            numbers += f" + {record['PredSpecial']}"

        confidence_class = 'success' if record['Confidence'] > 0.8 else 'warning' if record['Confidence'] > 0.6 else 'danger'
        match_class = 'success' if record['MatchCount'] >= 4 else 'warning' if record['MatchCount'] >= 2 else 'secondary'

        table_rows += f"""
                                <tr>
                                    <td>{record['Period']}</td>
                                    <td><strong>{numbers}</strong></td>
                                    <td><span class="badge bg-info">{record['Method']}</span></td>
                                    <td><span class="badge bg-{confidence_class}">{record['Confidence']:.3f}</span></td>
                                    <td><span class="badge bg-{match_class}">{record['MatchCount']}</span></td>
                                    <td>{record['PredictionDate']}</td>
                                </tr>
        """

    return f"""
                            {table_rows}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary">總預測數</h5>
                            <h2>156</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">成功預測</h5>
                            <h2>23</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning">成功率</h5>
                            <h2>14.7%</h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """

@app.route('/analysis')
def analysis():
    """分析報告頁面"""
    lottery_type = request.args.get('type', 'powercolor')

    lottery_names = {
        'powercolor': '威力彩',
        'lotto649': '大樂透',
        'dailycash': '今彩539'
    }

    return f"""
    <html>
    <head>
        <title>分析報告 - 彩票預測系統</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/"><i class="fas fa-chart-line"></i> 彩票預測系統</a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/">首頁</a>
                    <a class="nav-link" href="/predictions">預測記錄</a>
                    <a class="nav-link active" href="/analysis">分析報告</a>
                    <a class="nav-link" href="/comprehensive_analysis">綜合分析</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <h1><i class="fas fa-chart-bar"></i> {lottery_names.get(lottery_type, '彩票')}分析報告</h1>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="btn-group" role="group">
                        <a href="/analysis?type=powercolor" class="btn {'btn-primary' if lottery_type == 'powercolor' else 'btn-outline-primary'}">威力彩</a>
                        <a href="/analysis?type=lotto649" class="btn {'btn-primary' if lottery_type == 'lotto649' else 'btn-outline-primary'}">大樂透</a>
                        <a href="/analysis?type=dailycash" class="btn {'btn-primary' if lottery_type == 'dailycash' else 'btn-outline-primary'}">今彩539</a>
                    </div>
                </div>
            </div>

            <!-- 預測準確度分析 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-target"></i> 預測準確度分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 text-center">
                                    <h4 class="text-primary">14.7%</h4>
                                    <small>總體成功率</small>
                                </div>
                                <div class="col-md-3 text-center">
                                    <h4 class="text-success">18.2%</h4>
                                    <small>機器學習成功率</small>
                                </div>
                                <div class="col-md-3 text-center">
                                    <h4 class="text-warning">12.8%</h4>
                                    <small>板路分析成功率</small>
                                </div>
                                <div class="col-md-3 text-center">
                                    <h4 class="text-info">21.5%</h4>
                                    <small>綜合預測成功率</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 號碼出現頻率分析 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-fire"></i> 熱門號碼 (最近30期)</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex flex-wrap">
                                <span class="badge bg-danger m-1 p-2">7 (12次)</span>
                                <span class="badge bg-danger m-1 p-2">14 (11次)</span>
                                <span class="badge bg-danger m-1 p-2">21 (10次)</span>
                                <span class="badge bg-warning m-1 p-2">28 (9次)</span>
                                <span class="badge bg-warning m-1 p-2">35 (8次)</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-snowflake"></i> 冷門號碼 (最近30期)</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex flex-wrap">
                                <span class="badge bg-primary m-1 p-2">2 (1次)</span>
                                <span class="badge bg-primary m-1 p-2">9 (1次)</span>
                                <span class="badge bg-info m-1 p-2">16 (2次)</span>
                                <span class="badge bg-info m-1 p-2">23 (2次)</span>
                                <span class="badge bg-info m-1 p-2">30 (3次)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 數學模式分析 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-calculator"></i> 數學模式分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6>等差數列出現</h6>
                                    <p>最近發現: 7, 14, 21, 28, 35 (公差=7)</p>
                                    <p class="text-muted">出現頻率: 15.3%</p>
                                </div>
                                <div class="col-md-4">
                                    <h6>連號組合</h6>
                                    <p>最近發現: 21, 22, 23</p>
                                    <p class="text-muted">出現頻率: 8.7%</p>
                                </div>
                                <div class="col-md-4">
                                    <h6>質數分布</h6>
                                    <p>質數號碼: 2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37</p>
                                    <p class="text-muted">平均每期: 2.3個質數</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 成功預測案例 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-trophy"></i> 成功預測案例分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>期數</th>
                                            <th>預測號碼</th>
                                            <th>實際號碼</th>
                                            <th>匹配數</th>
                                            <th>成功原因</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>113000120</td>
                                            <td>7, 14, 21, 28, 35, 42</td>
                                            <td>7, 14, 18, 28, 35, 39</td>
                                            <td><span class="badge bg-success">4</span></td>
                                            <td>等差數列模式識別準確</td>
                                        </tr>
                                        <tr>
                                            <td>113000118</td>
                                            <td>3, 15, 22, 28, 35, 38</td>
                                            <td>3, 12, 22, 28, 33, 38</td>
                                            <td><span class="badge bg-success">4</span></td>
                                            <td>質數與合數搭配預測成功</td>
                                        </tr>
                                        <tr>
                                            <td>113000115</td>
                                            <td>1, 8, 15, 22, 29, 36</td>
                                            <td>1, 8, 15, 25, 32, 36</td>
                                            <td><span class="badge bg-warning">3</span></td>
                                            <td>板路跟隨模式有效</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """

@app.route('/dashboard')
def dashboard():
    """系統儀表板"""
    return """
    <html>
    <head>
        <title>系統儀表板 - 彩票預測系統</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/"><i class="fas fa-tachometer-alt"></i> 系統儀表板</a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/">首頁</a>
                    <a class="nav-link" href="/predictions">預測記錄</a>
                    <a class="nav-link" href="/analysis">分析報告</a>
                    <a class="nav-link active" href="/dashboard">儀表板</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <h1><i class="fas fa-tachometer-alt"></i> 系統儀表板</h1>
            <p class="text-muted">實時監控系統狀態、預測統計和快速操作</p>

            <!-- 系統狀態 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center border-success">
                        <div class="card-body">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h5>系統狀態</h5>
                            <span class="badge bg-success">正常運行</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center border-info">
                        <div class="card-body">
                            <i class="fas fa-database fa-2x text-info mb-2"></i>
                            <h5>數據狀態</h5>
                            <span class="badge bg-info">已更新</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center border-warning">
                        <div class="card-body">
                            <i class="fas fa-brain fa-2x text-warning mb-2"></i>
                            <h5>AI模型</h5>
                            <span class="badge bg-warning">運行中</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center border-primary">
                        <div class="card-body">
                            <i class="fas fa-clock fa-2x text-primary mb-2"></i>
                            <h5>最後更新</h5>
                            <small>2025-06-21 09:30</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 統計概覽 -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-chart-pie"></i> 威力彩統計</h6>
                        </div>
                        <div class="card-body">
                            <p>總預測: <strong>156</strong></p>
                            <p>成功預測: <strong>23</strong></p>
                            <p>成功率: <strong>14.7%</strong></p>
                            <div class="progress">
                                <div class="progress-bar bg-success" style="width: 14.7%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-chart-pie"></i> 大樂透統計</h6>
                        </div>
                        <div class="card-body">
                            <p>總預測: <strong>142</strong></p>
                            <p>成功預測: <strong>19</strong></p>
                            <p>成功率: <strong>13.4%</strong></p>
                            <div class="progress">
                                <div class="progress-bar bg-warning" style="width: 13.4%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-chart-pie"></i> 今彩539統計</h6>
                        </div>
                        <div class="card-body">
                            <p>總預測: <strong>298</strong></p>
                            <p>成功預測: <strong>52</strong></p>
                            <p>成功率: <strong>17.4%</strong></p>
                            <div class="progress">
                                <div class="progress-bar bg-info" style="width: 17.4%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-bolt"></i> 快速操作</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <a href="/separated_prediction" class="btn btn-primary w-100">
                                        <i class="fas fa-magic"></i> 執行預測
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="/comprehensive_analysis" class="btn btn-success w-100">
                                        <i class="fas fa-microscope"></i> 綜合分析
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="/enhanced_prediction" class="btn btn-warning w-100">
                                        <i class="fas fa-brain"></i> 智能預測
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <button class="btn btn-info w-100" onclick="updateData()">
                                        <i class="fas fa-sync"></i> 更新數據
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            function updateData() {
                alert('數據更新功能已觸發！');
            }
        </script>
    </body>
    </html>
    """

if __name__ == '__main__':
    print("🚀 彩票預測系統測試版啟動中...")
    print("📍 訪問地址: http://localhost:5001")
    print("=" * 50)

    app.run(
        debug=True,
        host='0.0.0.0',
        port=5001,
        use_reloader=False
    )
