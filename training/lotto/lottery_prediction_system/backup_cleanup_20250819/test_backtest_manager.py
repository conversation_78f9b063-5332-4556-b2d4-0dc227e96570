#!/usr/bin/env python3
"""
測試回測管理器功能
驗證所有核心功能是否正常運作
"""

import os
import sys
import tempfile
import shutil
from backtest_manager import BacktestManager
from batch_backtest import GameType, PredictionMethod


def test_backtest_manager():
    """測試回測管理器的基本功能"""
    print("🧪 測試回測管理器功能")
    print("=" * 60)
    
    # 創建臨時測試目錄
    test_dir = tempfile.mkdtemp(prefix="backtest_test_")
    print(f"📁 測試目錄: {test_dir}")
    
    try:
        # 初始化管理器
        manager = BacktestManager(results_dir=test_dir)
        print("✅ 管理器初始化成功")
        
        # 測試自動回測功能
        print("\n🔄 測試自動回測...")
        
        # 模擬執行回測
        from batch_backtest import BatchBacktester, BacktestConfig
        
        config = BacktestConfig(
            game_type=GameType.POWERCOLOR,
            method=PredictionMethod.FREQUENCY_ANALYSIS,
            start_period="114000050",
            end_period="114000058",
            training_window=10
        )
        
        backtester = BatchBacktester(config)
        results = backtester.run_backtest()
        
        if "error" in results:
            print(f"⚠️ 回測執行警告: {results['error']}")
            return False
        
        # 保存結果
        filepath = backtester.save_results()
        manager._save_to_history(results, filepath, "自動化測試")
        
        print("✅ 回測執行和保存成功")
        
        # 測試歷史查詢
        print("\n📊 測試歷史查詢...")
        manager.view_history(limit=5)
        print("✅ 歷史查詢成功")
        
        # 測試詳細結果載入
        print("\n🔍 測試詳細結果載入...")
        detailed_result = manager.load_result_detail(1)
        
        if detailed_result:
            print("✅ 詳細結果載入成功")
        else:
            print("⚠️ 詳細結果載入失敗，但這可能是正常的")
        
        print("\n🎉 所有基本功能測試通過！")
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False
        
    finally:
        # 清理測試目錄
        try:
            shutil.rmtree(test_dir)
            print(f"🧹 已清理測試目錄: {test_dir}")
        except:
            pass


def test_method_comparison():
    """測試方法比較功能"""
    print("\n" + "=" * 60)
    print("🔍 測試方法比較功能")
    print("=" * 60)
    
    # 創建臨時測試目錄
    test_dir = tempfile.mkdtemp(prefix="comparison_test_")
    
    try:
        manager = BacktestManager(results_dir=test_dir)
        
        # 執行方法比較（使用較小的範圍以加快測試）
        print("🔄 執行方法比較測試...")
        
        comparison_results = manager.compare_methods(
            game_type=GameType.POWERCOLOR,
            start_period="114000055",  # 較小範圍
            end_period="114000058",
            training_window=5
        )
        
        if comparison_results:
            print("✅ 方法比較測試成功")
            print(f"📊 共比較了 {len(comparison_results)} 種方法")
            return True
        else:
            print("⚠️ 方法比較沒有返回結果")
            return False
            
    except Exception as e:
        print(f"❌ 方法比較測試失敗: {e}")
        return False
        
    finally:
        try:
            shutil.rmtree(test_dir)
        except:
            pass


def run_demo():
    """運行演示功能"""
    print("\n" + "=" * 60)
    print("🎯 回測管理器演示")
    print("=" * 60)
    
    print("這個系統提供以下功能:")
    print("\n1. 🔄 互動式回測執行")
    print("   - 選擇遊戲類型和預測方法")
    print("   - 設定測試期間和參數")
    print("   - 自動執行回測並保存結果")
    
    print("\n2. 📊 歷史結果管理")
    print("   - 查看所有歷史回測記錄")
    print("   - 載入詳細結果進行分析")
    print("   - 支援備註和分類管理")
    
    print("\n3. 🔍 方法比較分析")
    print("   - 同時測試多種預測方法")
    print("   - 自動比較各方法表現")
    print("   - 推薦最佳方法")
    
    print("\n4. 📈 結果可視化")
    print("   - Web界面提供圖表分析")
    print("   - 命中趨勢和分佈統計")
    print("   - 互動式數據探索")
    
    print("\n使用方式:")
    print("📱 命令行版本: python backtest_manager.py")
    print("🌐 Web版本: streamlit run backtest_web_manager.py")
    
    print("\n結果儲存:")
    print("📁 JSON格式詳細結果 -> backtest_results/")
    print("🗃️ SQLite歷史記錄 -> backtest_results/backtest_history.db")


def main():
    """主測試函數"""
    print("🧪 回測管理器測試套件")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 2
    
    # 測試基本功能
    if test_backtest_manager():
        tests_passed += 1
    
    # 測試方法比較
    if test_method_comparison():
        tests_passed += 1
    
    # 顯示演示
    run_demo()
    
    # 總結
    print("\n" + "=" * 60)
    print("📋 測試結果總結")
    print("=" * 60)
    print(f"通過測試: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 所有測試通過！系統可以正常使用。")
        print("\n🚀 快速開始:")
        print("   python backtest_manager.py")
        print("   streamlit run backtest_web_manager.py")
        return True
    else:
        print("⚠️ 部分測試失敗，請檢查系統配置。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)