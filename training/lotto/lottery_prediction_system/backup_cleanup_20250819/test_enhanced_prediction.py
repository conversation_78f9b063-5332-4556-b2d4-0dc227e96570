#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試增強預測功能
驗證大小號分析、穩定號碼識別和特定模式分析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime
import logging

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加項目路徑到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_enhanced_analysis():
    """測試增強分析功能"""
    try:
        from data.db_manager import DBManager
        from prediction.lottery_predictor import LotteryPredictor
        from data.feature_engineering import FeatureEngineer
        from config_manager import ConfigManager
        
        logger.info("開始測試增強預測功能...")
        
        # 初始化組件
        config_manager = ConfigManager()
        db = DBManager(config_manager)
        predictor = LotteryPredictor()
        fe = FeatureEngineer()
        
        # 測試威力彩增強分析
        logger.info("\n=== 測試威力彩增強分析 ===")
        powercolor_df = db.load_lottery_data('powercolor')
        
        if powercolor_df is not None and len(powercolor_df) > 0:
            # 準備特徵
            features = fe.prepare_next_period_features(powercolor_df, 'powercolor')
            
            # 執行增強分析預測
            enhanced_result = predictor.predict_with_enhanced_analysis(
                powercolor_df, features, 'powercolor'
            )
            
            if enhanced_result:
                logger.info("威力彩增強分析結果:")
                
                # 顯示推薦號碼
                recommended = enhanced_result.get('推薦號碼', {})
                logger.info(f"推薦號碼: {recommended}")
                
                # 顯示分析摘要
                summary = enhanced_result.get('分析摘要', {})
                logger.info(f"大小號分析: {summary.get('大小號分析', {})}")
                logger.info(f"穩定號碼: {summary.get('穩定號碼', [])}")
                logger.info(f"模式分析: {summary.get('模式分析', [])}")
                
                # 顯示融合預測（如果有）
                merged = enhanced_result.get('融合預測', {})
                if merged:
                    logger.info(f"融合預測: {merged}")
            else:
                logger.warning("威力彩增強分析失敗")
        else:
            logger.warning("無法載入威力彩數據")
        
        # 測試大樂透增強分析
        logger.info("\n=== 測試大樂透增強分析 ===")
        lotto649_df = db.load_lottery_data('lotto649')
        
        if lotto649_df is not None and len(lotto649_df) > 0:
            # 準備特徵
            features = fe.prepare_next_period_features(lotto649_df, 'lotto649')
            
            # 執行增強分析預測
            enhanced_result = predictor.predict_with_enhanced_analysis(
                lotto649_df, features, 'lotto649'
            )
            
            if enhanced_result:
                logger.info("大樂透增強分析結果:")
                
                # 顯示推薦號碼
                recommended = enhanced_result.get('推薦號碼', {})
                logger.info(f"推薦號碼: {recommended}")
                
                # 顯示分析摘要
                summary = enhanced_result.get('分析摘要', {})
                logger.info(f"大小號分析: {summary.get('大小號分析', {})}")
                logger.info(f"穩定號碼: {summary.get('穩定號碼', [])}")
                logger.info(f"模式分析: {summary.get('模式分析', [])}")
                
                # 顯示融合預測（如果有）
                merged = enhanced_result.get('融合預測', {})
                if merged:
                    logger.info(f"融合預測: {merged}")
            else:
                logger.warning("大樂透增強分析失敗")
        else:
            logger.warning("無法載入大樂透數據")
        
        # 測試今彩539增強分析
        logger.info("\n=== 測試今彩539增強分析 ===")
        dailycash_df = db.load_lottery_data('dailycash')
        
        if dailycash_df is not None and len(dailycash_df) > 0:
            # 準備特徵
            features = fe.prepare_next_period_features(dailycash_df, 'dailycash')
            
            # 執行增強分析預測
            enhanced_result = predictor.predict_with_enhanced_analysis(
                dailycash_df, features, 'dailycash'
            )
            
            if enhanced_result:
                logger.info("今彩539增強分析結果:")
                
                # 顯示推薦號碼
                recommended = enhanced_result.get('推薦號碼', {})
                logger.info(f"推薦號碼: {recommended}")
                
                # 顯示分析摘要
                summary = enhanced_result.get('分析摘要', {})
                logger.info(f"大小號分析: {summary.get('大小號分析', {})}")
                logger.info(f"穩定號碼: {summary.get('穩定號碼', [])}")
                logger.info(f"模式分析: {summary.get('模式分析', [])}")
                
                # 顯示融合預測（如果有）
                merged = enhanced_result.get('融合預測', {})
                if merged:
                    logger.info(f"融合預測: {merged}")
            else:
                logger.warning("今彩539增強分析失敗")
        else:
            logger.warning("無法載入今彩539數據")
        
        logger.info("\n增強預測功能測試完成")
        
    except Exception as e:
        logger.error(f"測試增強分析功能時出錯: {str(e)}")
        import traceback
        traceback.print_exc()

def test_actual_numbers_update():
    """測試實際號碼更新功能"""
    try:
        from prediction.lottery_predictor import LotteryPredictor
        
        logger.info("\n=== 測試實際號碼更新功能 ===")
        
        predictor = LotteryPredictor()
        
        # 測試威力彩實際號碼更新
        test_powercolor_numbers = {
            '第一區': [5, 12, 18, 25, 31, 36],
            '第二區': 7
        }
        
        predictor.update_actual_numbers('powercolor', '113000123', test_powercolor_numbers)
        
        # 測試大樂透實際號碼更新
        test_lotto649_numbers = {
            '第一區': [8, 15, 22, 29, 35, 42],
            '特別號': 16
        }
        
        predictor.update_actual_numbers('lotto649', '113000456', test_lotto649_numbers)
        
        # 測試今彩539實際號碼更新
        test_dailycash_numbers = {
            '號碼': [3, 11, 19, 27, 33]
        }
        
        predictor.update_actual_numbers('dailycash', '113000789', test_dailycash_numbers)
        
        # 測試準確度計算
        accuracy_powercolor = predictor.get_prediction_accuracy('powercolor', '113000123')
        accuracy_lotto649 = predictor.get_prediction_accuracy('lotto649', '113000456')
        accuracy_dailycash = predictor.get_prediction_accuracy('dailycash', '113000789')
        
        logger.info(f"威力彩準確度分析: {accuracy_powercolor}")
        logger.info(f"大樂透準確度分析: {accuracy_lotto649}")
        logger.info(f"今彩539準確度分析: {accuracy_dailycash}")
        
        logger.info("實際號碼更新功能測試完成")
        
    except Exception as e:
        logger.error(f"測試實際號碼更新功能時出錯: {str(e)}")
        import traceback
        traceback.print_exc()

def test_enhanced_analyzer_directly():
    """直接測試增強分析器"""
    try:
        from prediction.enhanced_number_analyzer import EnhancedNumberAnalyzer
        from data.db_manager import DBManager
        from config_manager import ConfigManager
        
        logger.info("\n=== 直接測試增強分析器 ===")
        
        analyzer = EnhancedNumberAnalyzer()
        config_manager = ConfigManager()
        db = DBManager(config_manager)
        
        # 測試威力彩分析
        powercolor_df = db.load_lottery_data('powercolor')
        
        if powercolor_df is not None and len(powercolor_df) > 0:
            logger.info("\n--- 威力彩大小號分析 ---")
            big_small_analysis = analyzer.analyze_big_small_pattern(powercolor_df, 'powercolor')
            logger.info(f"大小號分析結果: {big_small_analysis}")
            
            logger.info("\n--- 威力彩穩定號碼分析 ---")
            stable_analysis = analyzer.identify_stable_numbers(powercolor_df, 'powercolor')
            logger.info(f"穩定號碼分析結果: {stable_analysis}")
            
            logger.info("\n--- 威力彩特定模式分析 ---")
            pattern_analysis = analyzer.analyze_specific_number_patterns(powercolor_df, 'powercolor')
            logger.info(f"特定模式分析結果: {pattern_analysis}")
        
        logger.info("增強分析器直接測試完成")
        
    except Exception as e:
        logger.error(f"直接測試增強分析器時出錯: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("開始測試增強預測系統...")
    
    # 測試增強分析器
    test_enhanced_analyzer_directly()
    
    # 測試增強分析功能
    test_enhanced_analysis()
    
    # 測試實際號碼更新功能
    test_actual_numbers_update()
    
    print("\n所有測試完成！")