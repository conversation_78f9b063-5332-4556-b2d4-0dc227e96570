#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试LotteryPredictor类
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("开始调试LotteryPredictor...")

try:
    from prediction.lottery_predictor import LotteryPredictor
    print("✓ 成功导入LotteryPredictor")
    
    # 创建实例
    predictor = LotteryPredictor()
    print("✓ 成功创建LotteryPredictor实例")
    
    # 检查方法是否存在
    methods_to_check = [
        'update_actual_numbers',
        'get_prediction_accuracy', 
        'predict_with_enhanced_analysis',
        '_merge_predictions'
    ]
    
    for method_name in methods_to_check:
        if hasattr(predictor, method_name):
            print(f"✓ 方法 {method_name} 存在")
        else:
            print(f"✗ 方法 {method_name} 不存在")
    
    # 检查属性
    attributes_to_check = [
        'enhanced_analyzer',
        'actual_numbers_cache'
    ]
    
    for attr_name in attributes_to_check:
        if hasattr(predictor, attr_name):
            print(f"✓ 属性 {attr_name} 存在")
        else:
            print(f"✗ 属性 {attr_name} 不存在")
    
    # 显示所有方法
    print("\n所有可用方法:")
    all_methods = [method for method in dir(predictor) if not method.startswith('_') or method.startswith('_merge')]
    for method in sorted(all_methods):
        print(f"  - {method}")
        
except Exception as e:
    print(f"✗ 导入或实例化失败: {str(e)}")
    import traceback
    traceback.print_exc()

print("\n调试完成")