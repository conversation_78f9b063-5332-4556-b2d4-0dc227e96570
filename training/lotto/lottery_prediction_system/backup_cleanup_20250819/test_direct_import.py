#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接导入测试脚本
"""

import sys
import os
import importlib

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 强制重新加载模块
if 'prediction.lottery_predictor' in sys.modules:
    importlib.reload(sys.modules['prediction.lottery_predictor'])

try:
    # 直接导入
    from prediction.lottery_predictor import LotteryPredictor
    
    print("=== 直接导入测试 ===")
    print(f"成功导入 LotteryPredictor: {LotteryPredictor}")
    
    # 创建实例
    predictor = LotteryPredictor()
    print(f"成功创建实例: {predictor}")
    
    # 检查属性
    print(f"\n=== 检查属性 ===")
    print(f"enhanced_analyzer 存在: {hasattr(predictor, 'enhanced_analyzer')}")
    print(f"actual_numbers_cache 存在: {hasattr(predictor, 'actual_numbers_cache')}")
    
    # 检查方法
    print(f"\n=== 检查方法 ===")
    methods_to_check = [
        'update_actual_numbers',
        'get_prediction_accuracy', 
        'predict_with_enhanced_analysis',
        '_merge_predictions',
        '_get_lottery_name'
    ]
    
    for method_name in methods_to_check:
        exists = hasattr(predictor, method_name)
        print(f"{method_name}: {exists}")
        if exists:
            method = getattr(predictor, method_name)
            print(f"  类型: {type(method)}")
            print(f"  可调用: {callable(method)}")
    
    # 尝试调用一个简单的方法
    print(f"\n=== 测试方法调用 ===")
    try:
        lottery_name = predictor._get_lottery_name('powercolor')
        print(f"_get_lottery_name('powercolor') = {lottery_name}")
    except Exception as e:
        print(f"调用 _get_lottery_name 时出错: {e}")
    
    # 尝试调用 update_actual_numbers
    try:
        predictor.update_actual_numbers('powercolor', '001', {'第一區': [1, 2, 3, 4, 5, 6], '第二區': 8})
        print("update_actual_numbers 调用成功")
    except Exception as e:
        print(f"调用 update_actual_numbers 时出错: {e}")
    
    print("\n=== 测试完成 ===")
    
except ImportError as e:
    print(f"导入错误: {e}")
except Exception as e:
    print(f"其他错误: {e}")
    import traceback
    traceback.print_exc()