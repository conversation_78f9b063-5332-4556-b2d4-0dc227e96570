#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速更新演示工具
演示如何使用手動更新器
"""

from manual_lottery_updater import ManualLotteryUpdater

def demo_update():
    """演示更新功能"""
    print("🎯 彩票資料更新演示")
    print("=" * 40)
    
    updater = ManualLotteryUpdater()
    
    # 顯示當前狀態
    print("📊 當前各彩票最新狀態:")
    print("-" * 30)
    
    for lottery_type in ['powercolor', 'lotto649', 'dailycash']:
        info = updater.get_latest_period_info(lottery_type)
        if info:
            print(f"{info['lottery_name']}:")
            print(f"  最新期號: {info['latest_period']}")
            print(f"  最新日期: {info['latest_date']}")
            print(f"  建議下期: {info['next_period']}")
            print(f"  建議日期: {info['next_date']}")
            print()
    
    print("💡 使用說明:")
    print("=" * 40)
    print("要手動更新彩票資料，請執行以下步驟:")
    print()
    print("1. 直接運行手動更新工具:")
    print("   python3 manual_lottery_updater.py")
    print()
    print("2. 或者使用程式碼方式更新:")
    print("   updater = ManualLotteryUpdater()")
    print("   # 威力彩範例")
    print("   updater.add_draw_result(")
    print("       'powercolor',")
    print("       114000073,  # 期號")
    print("       '2025-09-12',  # 日期")
    print("       [1, 12, 14, 21, 36, 37],  # 主要號碼")
    print("       3  # 特別號")
    print("   )")
    print()
    print("3. 驗證更新結果:")
    print("   檢查資料庫是否正確更新")
    
    # 演示格式化輸入說明
    print("\n📋 各彩票輸入格式說明:")
    print("-" * 30)
    
    configs = updater.lottery_configs
    for lottery_type, config in configs.items():
        print(f"📌 {config['name']}:")
        print(f"   主要號碼: {config['main_numbers']}個")
        print(f"   號碼範圍: {config['number_range'][0]}-{config['number_range'][1]}")
        if config['special_range']:
            print(f"   特別號範圍: {config['special_range'][0]}-{config['special_range'][1]}")
        else:
            print("   無特別號")
        print(f"   開獎日: 每週{config['draw_days']}")
        print()

if __name__ == "__main__":
    demo_update()