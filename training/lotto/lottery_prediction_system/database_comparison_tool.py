#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
資料庫比對工具 - 全面比較lottery.db和lottery_data.db的差異
"""

import sqlite3
import pandas as pd
from datetime import datetime
import os

class DatabaseComparator:
    def __init__(self):
        self.db1_path = 'data/lottery.db'
        self.db2_path = 'data/lottery_data.db'
        self.db1_name = 'lottery.db'
        self.db2_name = 'lottery_data.db'
        
    def compare_all(self):
        """執行全面比對"""
        print("🔍 資料庫比對工具")
        print("=" * 70)
        print(f"資料庫1: {self.db1_name} ({self._get_file_size(self.db1_path)})")
        print(f"資料庫2: {self.db2_name} ({self._get_file_size(self.db2_path)})")
        print("=" * 70)
        
        self.compare_tables()
        self.compare_table_structures()
        self.compare_data_counts()
        self.compare_lottery_data()
        self.find_unique_data()
        
    def _get_file_size(self, path):
        """獲取檔案大小"""
        size = os.path.getsize(path)
        if size < 1024:
            return f"{size} bytes"
        elif size < 1024 * 1024:
            return f"{size/1024:.1f} KB"
        else:
            return f"{size/(1024*1024):.1f} MB"
    
    def compare_tables(self):
        """比對資料表清單"""
        print("\n📊 資料表比對")
        print("-" * 50)
        
        conn1 = sqlite3.connect(self.db1_path)
        conn2 = sqlite3.connect(self.db2_path)
        
        # 獲取表清單
        cursor1 = conn1.cursor()
        cursor1.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables1 = set([row[0] for row in cursor1.fetchall()])
        
        cursor2 = conn2.cursor()
        cursor2.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables2 = set([row[0] for row in cursor2.fetchall()])
        
        # 共同的表
        common_tables = tables1.intersection(tables2)
        print(f"✅ 共同的表 ({len(common_tables)}):")
        for table in sorted(common_tables):
            print(f"   - {table}")
        
        # 只在db1的表
        only_db1 = tables1 - tables2
        if only_db1:
            print(f"\n📍 只在 {self.db1_name} 的表 ({len(only_db1)}):")
            for table in sorted(only_db1):
                print(f"   - {table}")
        
        # 只在db2的表
        only_db2 = tables2 - tables1
        if only_db2:
            print(f"\n📍 只在 {self.db2_name} 的表 ({len(only_db2)}):")
            for table in sorted(only_db2):
                print(f"   - {table}")
        
        conn1.close()
        conn2.close()
        
        return common_tables
    
    def compare_table_structures(self):
        """比對共同表的結構"""
        print("\n🏗️ 表結構比對 (共同表)")
        print("-" * 50)
        
        conn1 = sqlite3.connect(self.db1_path)
        conn2 = sqlite3.connect(self.db2_path)
        
        # 獲取共同表
        cursor1 = conn1.cursor()
        cursor2 = conn2.cursor()
        
        cursor1.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables1 = set([row[0] for row in cursor1.fetchall()])
        
        cursor2.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables2 = set([row[0] for row in cursor2.fetchall()])
        
        common_tables = tables1.intersection(tables2)
        
        for table in sorted(common_tables):
            if table in ['Powercolor', 'Lotto649', 'DailyCash']:  # 重點關注彩票表
                print(f"\n📋 表: {table}")
                
                # 獲取結構
                cursor1.execute(f"PRAGMA table_info({table})")
                columns1 = cursor1.fetchall()
                
                cursor2.execute(f"PRAGMA table_info({table})")
                columns2 = cursor2.fetchall()
                
                # 比對欄位
                cols1_dict = {col[1]: col[2] for col in columns1}
                cols2_dict = {col[1]: col[2] for col in columns2}
                
                # 檢查差異
                differences = []
                for col_name in cols1_dict:
                    if col_name in cols2_dict:
                        if cols1_dict[col_name] != cols2_dict[col_name]:
                            differences.append(f"   {col_name}: {self.db1_name}={cols1_dict[col_name]}, {self.db2_name}={cols2_dict[col_name]}")
                
                if differences:
                    print("   ⚠️ 資料類型差異:")
                    for diff in differences:
                        print(f"     {diff}")
                else:
                    print("   ✅ 結構完全相同")
        
        conn1.close()
        conn2.close()
    
    def compare_data_counts(self):
        """比對資料數量"""
        print("\n📈 資料數量比對")
        print("-" * 50)
        
        conn1 = sqlite3.connect(self.db1_path)
        conn2 = sqlite3.connect(self.db2_path)
        
        cursor1 = conn1.cursor()
        cursor2 = conn2.cursor()
        
        tables_to_compare = ['Powercolor', 'Lotto649', 'DailyCash']
        
        for table in tables_to_compare:
            try:
                # lottery.db
                cursor1.execute(f"SELECT COUNT(*) FROM {table}")
                count1 = cursor1.fetchone()[0]
                
                cursor1.execute(f"SELECT MAX(Period), MIN(Period) FROM {table}")
                range1 = cursor1.fetchone()
            except:
                count1 = 0
                range1 = (None, None)
            
            try:
                # lottery_data.db
                cursor2.execute(f"SELECT COUNT(*) FROM {table}")
                count2 = cursor2.fetchone()[0]
                
                cursor2.execute(f"SELECT MAX(Period), MIN(Period) FROM {table}")
                range2 = cursor2.fetchone()
            except:
                count2 = 0
                range2 = (None, None)
            
            print(f"\n📊 {table}:")
            print(f"   {self.db1_name}: {count1} 筆 (期數範圍: {range1[1]} ~ {range1[0]})")
            print(f"   {self.db2_name}: {count2} 筆 (期數範圍: {range2[1]} ~ {range2[0]})")
            
            if count1 != count2:
                diff = abs(count1 - count2)
                if count1 > count2:
                    print(f"   ⚠️ {self.db1_name} 多 {diff} 筆")
                else:
                    print(f"   ⚠️ {self.db2_name} 多 {diff} 筆")
            else:
                print(f"   ✅ 資料數量相同")
        
        conn1.close()
        conn2.close()
    
    def compare_lottery_data(self):
        """比對最新的彩票資料"""
        print("\n🎯 最新資料比對")
        print("-" * 50)
        
        conn1 = sqlite3.connect(self.db1_path)
        conn2 = sqlite3.connect(self.db2_path)
        
        tables = ['Powercolor', 'Lotto649', 'DailyCash']
        
        for table in tables:
            print(f"\n📋 {table} 最新5期:")
            
            try:
                # lottery.db的最新資料
                query1 = f"SELECT Period, Sdate FROM {table} ORDER BY Period DESC LIMIT 5"
                df1 = pd.read_sql_query(query1, conn1)
                
                # lottery_data.db的最新資料
                query2 = f"SELECT Period, Sdate FROM {table} ORDER BY Period DESC LIMIT 5"
                df2 = pd.read_sql_query(query2, conn2)
                
                print(f"   {self.db1_name}:")
                for _, row in df1.iterrows():
                    print(f"     期數: {row['Period']}, 日期: {row['Sdate']}")
                
                print(f"   {self.db2_name}:")
                for _, row in df2.iterrows():
                    print(f"     期數: {row['Period']}, 日期: {row['Sdate']}")
                    
                # 檢查最新期數
                if not df1.empty and not df2.empty:
                    max1 = df1['Period'].max()
                    max2 = df2['Period'].max()
                    
                    if str(max1) != str(max2):
                        print(f"   ⚠️ 最新期數不同: {self.db1_name}={max1}, {self.db2_name}={max2}")
                    else:
                        print(f"   ✅ 最新期數相同: {max1}")
                        
            except Exception as e:
                print(f"   ❌ 無法比對: {e}")
        
        conn1.close()
        conn2.close()
    
    def find_unique_data(self):
        """找出獨特的資料"""
        print("\n🔎 獨特資料分析")
        print("-" * 50)
        
        conn1 = sqlite3.connect(self.db1_path)
        conn2 = sqlite3.connect(self.db2_path)
        
        # 檢查威力彩期數114000068的詳細資料
        print("\n🎯 威力彩期數 114000068 詳細比對:")
        
        try:
            # lottery.db
            cursor1 = conn1.cursor()
            cursor1.execute("SELECT * FROM Powercolor WHERE Period = '114000068' OR Period = 114000068")
            record1 = cursor1.fetchone()
            
            if record1:
                print(f"   {self.db1_name}: ✅ 存在")
                print(f"     資料: {record1}")
            else:
                print(f"   {self.db1_name}: ❌ 不存在")
                
            # lottery_data.db
            cursor2 = conn2.cursor()
            cursor2.execute("SELECT * FROM Powercolor WHERE Period = 114000068")
            record2 = cursor2.fetchone()
            
            if record2:
                print(f"   {self.db2_name}: ✅ 存在")
                print(f"     資料: {record2}")
            else:
                print(f"   {self.db2_name}: ❌ 不存在")
                
        except Exception as e:
            print(f"   ❌ 查詢錯誤: {e}")
        
        conn1.close()
        conn2.close()

def main():
    print("🔍 彩票資料庫比對工具")
    print("=" * 70)
    
    comparator = DatabaseComparator()
    comparator.compare_all()
    
    print("\n" + "=" * 70)
    print("📊 比對完成！")

if __name__ == "__main__":
    main()