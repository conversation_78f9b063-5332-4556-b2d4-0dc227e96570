#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
預測準確度追蹤系統
自動比對預測結果與實際開獎，計算各方法準確率
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import sqlite3
import json
from collections import defaultdict

logger = logging.getLogger('prediction_accuracy_tracker')

class PredictionAccuracyTracker:
    """預測準確度追蹤器"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
    
    def update_prediction_accuracy(self, lottery_type: str = 'powercolor') -> Dict:
        """更新預測準確度
        
        將預測結果與實際開獎結果比對，更新準確度統計
        """
        try:
            results = {
                'updated_predictions': 0,
                'accuracy_stats': {},
                'method_performance': {}
            }
            
            # 獲取未驗證的預測
            unverified_predictions = self._get_unverified_predictions(lottery_type)
            
            for prediction in unverified_predictions:
                # 查找對應的實際開獎結果
                actual_result = self._get_actual_result(prediction['Period'], lottery_type)
                
                if actual_result:
                    # 計算匹配度
                    match_result = self._calculate_match(prediction, actual_result, lottery_type)
                    
                    # 更新資料庫
                    self._update_prediction_record(prediction['ID'], match_result, lottery_type)
                    
                    results['updated_predictions'] += 1
                    
                    # 累計統計
                    method = prediction.get('PredictionMethod', 'unknown')
                    if method not in results['method_performance']:
                        results['method_performance'][method] = []
                    results['method_performance'][method].append(match_result)
            
            # 計算整體統計
            results['accuracy_stats'] = self._calculate_overall_stats(lottery_type)
            
            logger.info(f"更新了 {results['updated_predictions']} 個預測的準確度")
            return results
            
        except Exception as e:
            logger.error(f"更新預測準確度失敗: {str(e)}")
            return {'error': str(e)}
    
    def _get_unverified_predictions(self, lottery_type: str) -> List[Dict]:
        """獲取未驗證的預測"""
        table_map = {
            'powercolor': 'PowercolorPredictions',
            'lotto649': 'Lotto649Predictions',
            'dailycash': 'DailyCashPredictions'
        }
        
        table_name = table_map.get(lottery_type)
        conn = self.db_manager.create_connection()
        
        # 查詢沒有匹配結果的預測
        sql = f"""
        SELECT * FROM {table_name}
        WHERE MatchCount IS NULL 
        AND Period IS NOT NULL
        ORDER BY PredictionDate DESC
        LIMIT 100
        """
        
        df = pd.read_sql_query(sql, conn)
        conn.close()
        
        return df.to_dict('records')
    
    def _get_actual_result(self, period: str, lottery_type: str) -> Optional[Dict]:
        """獲取實際開獎結果"""
        table_map = {
            'powercolor': 'Powercolor',
            'lotto649': 'Lotto649',
            'dailycash': 'DailyCash'
        }
        
        table_name = table_map.get(lottery_type)
        conn = self.db_manager.create_connection()
        
        sql = f"SELECT * FROM {table_name} WHERE Period = ?"
        df = pd.read_sql_query(sql, conn, params=(period,))
        conn.close()
        
        if not df.empty:
            return df.iloc[0].to_dict()
        return None
    
    def _calculate_match(self, prediction: Dict, actual: Dict, lottery_type: str) -> Dict:
        """計算預測與實際結果的匹配度"""
        match_result = {
            'match_count': 0,
            'special_match': 0,
            'accuracy_score': 0.0
        }
        
        # 提取預測號碼
        if lottery_type == 'dailycash':
            pred_numbers = [prediction[f'PredA{i}'] for i in range(1, 6)]
            actual_numbers = [actual[f'Anumber{i}'] for i in range(1, 6)]
        else:
            pred_numbers = [prediction[f'PredA{i}'] for i in range(1, 7)]
            actual_numbers = [actual[f'Anumber{i}'] for i in range(1, 7)]
            
            # 特別號匹配
            pred_special = prediction.get('PredS') or prediction.get('PredSpecial')
            actual_special = actual.get('Second_district') or actual.get('SpecialNumber')
            
            if pred_special and actual_special and pred_special == actual_special:
                match_result['special_match'] = 1
        
        # 計算主號碼匹配
        matches = len(set(pred_numbers) & set(actual_numbers))
        match_result['match_count'] = matches
        
        # 計算準確度分數 (0-100)
        base_score = (matches / len(pred_numbers)) * 80  # 主號碼佔80%
        special_score = match_result['special_match'] * 20  # 特別號佔20%
        match_result['accuracy_score'] = base_score + special_score
        
        return match_result
    
    def _update_prediction_record(self, prediction_id: int, match_result: Dict, lottery_type: str):
        """更新預測記錄的準確度信息"""
        table_map = {
            'powercolor': 'PowercolorPredictions',
            'lotto649': 'Lotto649Predictions',
            'dailycash': 'DailyCashPredictions'
        }
        
        table_name = table_map.get(lottery_type)
        conn = self.db_manager.create_connection()
        cursor = conn.cursor()
        
        if lottery_type == 'dailycash':
            sql = f"""
            UPDATE {table_name}
            SET MatchCount = ?, AccuracyScore = ?
            WHERE ID = ?
            """
            values = (match_result['match_count'], match_result['accuracy_score'], prediction_id)
        else:
            sql = f"""
            UPDATE {table_name}
            SET MatchCount = ?, SecondMatch = ?, AccuracyScore = ?
            WHERE ID = ?
            """
            values = (match_result['match_count'], match_result['special_match'], 
                     match_result['accuracy_score'], prediction_id)
        
        cursor.execute(sql, values)
        conn.commit()
        conn.close()
    
    def _calculate_overall_stats(self, lottery_type: str) -> Dict:
        """計算整體準確度統計"""
        table_map = {
            'powercolor': 'PowercolorPredictions',
            'lotto649': 'Lotto649Predictions',
            'dailycash': 'DailyCashPredictions'
        }
        
        table_name = table_map.get(lottery_type)
        conn = self.db_manager.create_connection()
        
        sql = f"""
        SELECT PredictionMethod, 
               AVG(MatchCount) as avg_match,
               AVG(AccuracyScore) as avg_accuracy,
               COUNT(*) as total_predictions,
               AVG(Confidence) as avg_confidence
        FROM {table_name}
        WHERE MatchCount IS NOT NULL
        GROUP BY PredictionMethod
        """
        
        df = pd.read_sql_query(sql, conn)
        conn.close()
        
        return df.to_dict('records')
    
    def generate_accuracy_report(self, lottery_type: str, days_back: int = 30) -> Dict:
        """生成準確度報告"""
        try:
            # 先更新準確度
            update_result = self.update_prediction_accuracy(lottery_type)
            
            # 獲取統計數據
            stats = self._get_accuracy_statistics(lottery_type, days_back)
            
            # 獲取最佳和最差表現方法
            best_worst = self._get_best_worst_methods(lottery_type, days_back)
            
            # 獲取趨勢數據
            trends = self._get_accuracy_trends(lottery_type, days_back)
            
            report = {
                'lottery_type': lottery_type,
                'period': f'{days_back}天',
                'update_summary': update_result,
                'accuracy_statistics': stats,
                'best_worst_methods': best_worst,
                'accuracy_trends': trends,
                'generated_at': datetime.now().isoformat()
            }
            
            return report
            
        except Exception as e:
            logger.error(f"生成準確度報告失敗: {str(e)}")
            return {'error': str(e)}
    
    def _get_accuracy_statistics(self, lottery_type: str, days_back: int) -> Dict:
        """獲取準確度統計"""
        table_map = {
            'powercolor': 'PowercolorPredictions',
            'lotto649': 'Lotto649Predictions',
            'dailycash': 'DailyCashPredictions'
        }
        
        table_name = table_map.get(lottery_type)
        conn = self.db_manager.create_connection()
        
        cutoff_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
        
        # 基本統計
        sql = f"""
        SELECT 
            COUNT(*) as total_predictions,
            COUNT(CASE WHEN MatchCount IS NOT NULL THEN 1 END) as verified_predictions,
            AVG(CASE WHEN AccuracyScore IS NOT NULL THEN AccuracyScore ELSE 0 END) as avg_accuracy,
            AVG(Confidence) as avg_confidence,
            MAX(AccuracyScore) as max_accuracy,
            MIN(AccuracyScore) as min_accuracy,
            AVG(MatchCount) as avg_match_count
        FROM {table_name}
        WHERE PredictionDate >= ?
        """
        
        basic_stats = pd.read_sql_query(sql, conn, params=(cutoff_date,))
        
        # 準確度分布
        sql = f"""
        SELECT 
            CASE 
                WHEN AccuracyScore >= 80 THEN 'high'
                WHEN AccuracyScore >= 60 THEN 'medium'
                WHEN AccuracyScore >= 40 THEN 'low'
                ELSE 'very_low'
            END as accuracy_level,
            COUNT(*) as count
        FROM {table_name}
        WHERE PredictionDate >= ? AND AccuracyScore IS NOT NULL
        GROUP BY accuracy_level
        """
        
        accuracy_distribution = pd.read_sql_query(sql, conn, params=(cutoff_date,))
        conn.close()
        
        # 整理統計結果
        stats = basic_stats.iloc[0].to_dict() if not basic_stats.empty else {}
        stats['accuracy_distribution'] = accuracy_distribution.to_dict('records')
        stats['verification_rate'] = (stats.get('verified_predictions', 0) / max(stats.get('total_predictions', 1), 1)) * 100
        
        return stats
    
    def _get_best_worst_methods(self, lottery_type: str, days_back: int) -> Dict:
        """獲取表現最佳和最差的方法"""
        table_map = {
            'powercolor': 'PowercolorPredictions',
            'lotto649': 'Lotto649Predictions',
            'dailycash': 'DailyCashPredictions'
        }
        
        table_name = table_map.get(lottery_type)
        conn = self.db_manager.create_connection()
        
        cutoff_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
        
        sql = f"""
        SELECT 
            PredictionMethod,
            AVG(AccuracyScore) as avg_accuracy,
            AVG(Confidence) as avg_confidence,
            COUNT(*) as total_predictions,
            COUNT(CASE WHEN MatchCount IS NOT NULL THEN 1 END) as verified_predictions,
            AVG(MatchCount) as avg_match_count
        FROM {table_name}
        WHERE PredictionDate >= ? AND AccuracyScore IS NOT NULL
        GROUP BY PredictionMethod
        HAVING COUNT(*) >= 3
        ORDER BY avg_accuracy DESC
        """
        
        df = pd.read_sql_query(sql, conn, params=(cutoff_date,))
        conn.close()
        
        if df.empty:
            return {'best_methods': [], 'worst_methods': []}
        
        # 前3名和後3名
        best_methods = df.head(3).to_dict('records')
        worst_methods = df.tail(3).to_dict('records')
        
        return {
            'best_methods': best_methods,
            'worst_methods': worst_methods,
            'total_methods_analyzed': len(df)
        }
    
    def _get_accuracy_trends(self, lottery_type: str, days_back: int) -> Dict:
        """獲取準確度趨勢"""
        table_map = {
            'powercolor': 'PowercolorPredictions',
            'lotto649': 'Lotto649Predictions',
            'dailycash': 'DailyCashPredictions'
        }
        
        table_name = table_map.get(lottery_type)
        conn = self.db_manager.create_connection()
        
        cutoff_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
        
        # 每日趨勢
        sql = f"""
        SELECT 
            DATE(PredictionDate) as prediction_date,
            AVG(AccuracyScore) as daily_avg_accuracy,
            AVG(Confidence) as daily_avg_confidence,
            COUNT(*) as daily_predictions,
            COUNT(CASE WHEN AccuracyScore IS NOT NULL THEN 1 END) as daily_verified
        FROM {table_name}
        WHERE PredictionDate >= ?
        GROUP BY DATE(PredictionDate)
        ORDER BY prediction_date ASC
        """
        
        daily_trends = pd.read_sql_query(sql, conn, params=(cutoff_date,))
        
        # 方法趨勢
        sql = f"""
        SELECT 
            PredictionMethod,
            DATE(PredictionDate) as prediction_date,
            AVG(AccuracyScore) as method_accuracy
        FROM {table_name}
        WHERE PredictionDate >= ? AND AccuracyScore IS NOT NULL
        GROUP BY PredictionMethod, DATE(PredictionDate)
        ORDER BY prediction_date ASC
        """
        
        method_trends = pd.read_sql_query(sql, conn, params=(cutoff_date,))
        conn.close()
        
        # 計算趨勢方向
        trend_direction = 'stable'
        if not daily_trends.empty and len(daily_trends) >= 2:
            first_week_avg = daily_trends.head(7)['daily_avg_accuracy'].mean()
            last_week_avg = daily_trends.tail(7)['daily_avg_accuracy'].mean()
            
            if last_week_avg > first_week_avg * 1.05:
                trend_direction = 'improving'
            elif last_week_avg < first_week_avg * 0.95:
                trend_direction = 'declining'
        
        return {
            'daily_trends': daily_trends.to_dict('records'),
            'method_trends': method_trends.to_dict('records'),
            'trend_direction': trend_direction,
            'trend_analysis_period': f'{days_back}天'
        }

if __name__ == "__main__":
    # 測試追蹤器
    try:
        from data.db_manager import DBManager
        db_manager = DBManager()
        tracker = PredictionAccuracyTracker(db_manager)
        
        # 更新準確度
        result = tracker.update_prediction_accuracy('powercolor')
        print("準確度更新結果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
    except Exception as e:
        print(f"測試失敗: {e}")