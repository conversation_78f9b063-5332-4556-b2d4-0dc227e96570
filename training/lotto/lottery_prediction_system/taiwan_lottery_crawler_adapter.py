# -*- coding: utf-8 -*-
"""
TaiwanLotteryCrawler 適配器
整合第三方 TaiwanLotteryCrawler 套件到現有彩票預測系統
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from TaiwanLottery import TaiwanLotteryCrawler

class TaiwanLotteryCrawlerAdapter:
    """TaiwanLotteryCrawler 適配器類別"""
    
    def __init__(self):
        self.crawler = TaiwanLotteryCrawler()
        self.logger = logging.getLogger(__name__)
        
        # 彩票類型對應
        self.lottery_type_mapping = {
            'powercolor': {
                'method': 'super_lotto',
                'name': '威力彩',
                'main_numbers': 6,
                'has_special': True,
                'special_range': (1, 8)
            },
            'lotto649': {
                'method': 'lotto649',
                'name': '大樂透',
                'main_numbers': 6,
                'has_special': True,
                'special_range': (1, 49)
            },
            'dailycash': {
                'method': 'daily_cash',
                'name': '今彩539',
                'main_numbers': 5,
                'has_special': False,
                'special_range': None
            }
        }
    
    def get_current_month_data(self, lottery_type: str) -> List[Dict]:
        """
        獲取當前月份的彩票資料
        
        Args:
            lottery_type: 彩票類型 ('powercolor', 'lotto649', 'dailycash')
            
        Returns:
            List[Dict]: 彩票資料列表
        """
        now = datetime.now()
        return self.get_month_data(lottery_type, now.year, now.month)
    
    def get_month_data(self, lottery_type: str, year: int, month: int) -> List[Dict]:
        """
        獲取指定月份的彩票資料
        
        Args:
            lottery_type: 彩票類型
            year: 年份
            month: 月份
            
        Returns:
            List[Dict]: 彩票資料列表
        """
        if lottery_type not in self.lottery_type_mapping:
            raise ValueError(f"不支援的彩票類型: {lottery_type}")
        
        config = self.lottery_type_mapping[lottery_type]
        method_name = config['method']
        
        try:
            # 調用對應的爬蟲方法
            crawler_method = getattr(self.crawler, method_name)
            raw_data = crawler_method([year, month])
            
            if not raw_data:
                self.logger.warning(f"未獲取到 {config['name']} {year}年{month}月 的資料")
                return []
            
            # 轉換資料格式
            converted_data = self._convert_data_format(raw_data, lottery_type)
            
            self.logger.info(f"成功獲取 {config['name']} {year}年{month}月 資料: {len(converted_data)} 筆")
            return converted_data
            
        except Exception as e:
            self.logger.error(f"獲取 {config['name']} {year}年{month}月 資料失敗: {e}")
            raise
    
    def get_recent_data(self, lottery_type: str, months: int = 3) -> List[Dict]:
        """
        獲取最近幾個月的彩票資料
        
        Args:
            lottery_type: 彩票類型
            months: 月份數量
            
        Returns:
            List[Dict]: 彩票資料列表
        """
        all_data = []
        current_date = datetime.now()
        
        for i in range(months):
            # 計算目標月份
            target_date = current_date - timedelta(days=i * 30)
            year = target_date.year
            month = target_date.month
            
            try:
                month_data = self.get_month_data(lottery_type, year, month)
                all_data.extend(month_data)
            except Exception as e:
                self.logger.warning(f"獲取 {year}年{month}月 資料時發生錯誤: {e}")
                continue
        
        # 按期號排序 (最新的在前)
        all_data.sort(key=lambda x: x['period'], reverse=True)
        return all_data
    
    def _convert_data_format(self, raw_data: List[Dict], lottery_type: str) -> List[Dict]:
        """
        轉換資料格式以符合現有系統結構
        
        Args:
            raw_data: 原始資料
            lottery_type: 彩票類型
            
        Returns:
            List[Dict]: 轉換後的資料
        """
        config = self.lottery_type_mapping[lottery_type]
        converted_data = []
        
        for item in raw_data:
            # 提取基本資訊
            period = str(item.get('期別', ''))
            draw_date = item.get('開獎日期', '')
            
            # 處理開獎日期格式
            if 'T' in draw_date:
                draw_date = draw_date.split('T')[0]  # 移除時間部分
            
            # 提取號碼
            if lottery_type == 'powercolor':
                main_numbers = item.get('第一區', [])
                special_number = item.get('第二區')
            elif lottery_type == 'lotto649':
                main_numbers = item.get('獎號', [])
                special_number = item.get('特別號')
            elif lottery_type == 'dailycash':
                main_numbers = item.get('獎號', [])
                special_number = None
            else:
                continue
            
            # 驗證號碼
            if not main_numbers or len(main_numbers) != config['main_numbers']:
                self.logger.warning(f"期號 {period} 的主號碼數量不正確: {main_numbers}")
                continue
            
            # 構建轉換後的資料結構
            converted_item = {
                'period': period,
                'draw_date': draw_date,
                'lottery_type': lottery_type,
                'main_numbers': main_numbers,
                'special_number': special_number,
                'raw_data': item  # 保留原始資料以供參考
            }
            
            converted_data.append(converted_item)
        
        return converted_data
    
    def validate_data(self, data: Dict, lottery_type: str) -> Tuple[bool, str]:
        """
        驗證資料完整性
        
        Args:
            data: 資料項目
            lottery_type: 彩票類型
            
        Returns:
            Tuple[bool, str]: (是否有效, 錯誤訊息)
        """
        config = self.lottery_type_mapping.get(lottery_type)
        if not config:
            return False, f"不支援的彩票類型: {lottery_type}"
        
        # 檢查必要欄位
        required_fields = ['period', 'draw_date', 'main_numbers']
        for field in required_fields:
            if field not in data or not data[field]:
                return False, f"缺少必要欄位: {field}"
        
        # 檢查主號碼數量
        main_numbers = data['main_numbers']
        if len(main_numbers) != config['main_numbers']:
            return False, f"主號碼數量錯誤: 預期 {config['main_numbers']}，實際 {len(main_numbers)}"
        
        # 檢查特別號
        if config['has_special']:
            special_number = data.get('special_number')
            if special_number is None:
                return False, "缺少特別號"
            
            # 檢查特別號範圍
            special_range = config['special_range']
            if special_range and (special_number < special_range[0] or special_number > special_range[1]):
                return False, f"特別號超出範圍: {special_number} (範圍: {special_range})"
        
        return True, ""
    
    def get_latest_period(self, lottery_type: str) -> Optional[str]:
        """
        獲取最新期號
        
        Args:
            lottery_type: 彩票類型
            
        Returns:
            Optional[str]: 最新期號，失敗時返回 None
        """
        try:
            # 嘗試最近6個月的資料，找到最新的
            current_date = datetime.now()
            
            for i in range(6):  # 最近6個月
                # 計算目標月份
                target_date = current_date - timedelta(days=i * 30)
                year = target_date.year
                month = target_date.month
                
                try:
                    data = self.get_month_data(lottery_type, year, month)
                    if data:
                        return data[0]['period']  # 返回最新期號
                except Exception:
                    continue  # 該月沒有資料，嘗試下一個月
                
        except Exception as e:
            self.logger.error(f"獲取最新期號失敗: {e}")
        
        return None
    
    def test_connection(self) -> Dict[str, bool]:
        """
        測試與各彩票類型的連接狀態
        
        Returns:
            Dict[str, bool]: 各彩票類型的連接狀態
        """
        results = {}
        
        for lottery_type in self.lottery_type_mapping.keys():
            try:
                # 嘗試獲取最新資料
                latest_period = self.get_latest_period(lottery_type)
                results[lottery_type] = latest_period is not None
                
                if latest_period:
                    self.logger.info(f"{lottery_type} 連接正常，最新期號: {latest_period}")
                else:
                    self.logger.warning(f"{lottery_type} 無法獲取最新資料")
                    
            except Exception as e:
                self.logger.error(f"{lottery_type} 連接測試失敗: {e}")
                results[lottery_type] = False
        
        return results

# 使用範例和測試函數
def test_adapter():
    """測試適配器功能"""
    logging.basicConfig(level=logging.INFO)
    
    adapter = TaiwanLotteryCrawlerAdapter()
    
    print("🎯 測試 TaiwanLotteryCrawlerAdapter")
    print()
    
    # 測試連接
    print("1️⃣ 測試連接狀態...")
    connection_status = adapter.test_connection()
    for lottery_type, status in connection_status.items():
        status_text = "✅ 正常" if status else "❌ 失敗"
        print(f"   {lottery_type}: {status_text}")
    
    print()
    
    # 測試獲取資料
    print("2️⃣ 測試獲取威力彩資料...")
    try:
        powercolor_data = adapter.get_month_data('powercolor', 2024, 12)
        if powercolor_data:
            latest = powercolor_data[0]
            print(f"   ✅ 獲取成功，最新期號: {latest['period']}")
            print(f"   📊 開獎日期: {latest['draw_date']}")
            print(f"   🎲 主號碼: {latest['main_numbers']}")
            print(f"   ⭐ 特別號: {latest['special_number']}")
        else:
            print("   ⚠️  無資料")
    except Exception as e:
        print(f"   ❌ 失敗: {e}")

if __name__ == "__main__":
    test_adapter()