#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
台灣彩券官方網站真實數據爬蟲 (Real Taiwan Lottery Web Scraper) - 更新版本
基於實際網站結構的精確解析版本

核心功能：
1. 真實網站結構解析 (Real Website Structure Parsing)
2. 智能數據檢查 (Intelligent Data Checking)
3. 自動更新機制 (Auto Update Mechanism)
4. 數據完整性驗證 (Data Integrity Verification)
5. 多彩票類型支持 (Multi-Lottery Support)
"""

import requests
import time
import re
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from bs4 import BeautifulSoup
import urllib3
from urllib.parse import urljoin

# 導入我們的數據完整性框架
try:
    from data_integrity_framework import DataIntegrityAPI, get_integrity_manager
    from real_data_integration import get_real_data_manager
    DATA_INTEGRITY_AVAILABLE = True
except ImportError:
    DATA_INTEGRITY_AVAILABLE = False

# 抑制SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 設置專門的爬蟲日誌
scraper_logger = logging.getLogger('real_web_scraper_updated')
handler = logging.FileHandler('data/web_scraper_updated.log', encoding='utf-8')
handler.setFormatter(logging.Formatter(
    '%(asctime)s - WEB_SCRAPER_UPDATED - %(levelname)s - %(message)s'
))
scraper_logger.addHandler(handler)
scraper_logger.setLevel(logging.INFO)

@dataclass
class LotteryResult:
    """開獎結果數據結構"""
    lottery_type: str           # 彩票類型
    lottery_name: str           # 彩票名稱
    period: int                 # 期號
    draw_date: str             # 開獎日期
    main_numbers: List[int]    # 主號碼
    special_number: Optional[int] = None  # 特別號
    additional_info: Optional[Dict] = None
    scraped_at: Optional[str] = None  # 爬取時間

class UpdatedRealWebScraper:
    """基於實際網站結構的真實台灣彩券官方網站爬蟲"""
    
    def __init__(self):
        # 官方網站URL
        self.base_url = "https://www.taiwanlottery.com"
        self.latest_results_url = "https://www.taiwanlottery.com/lotto/lotto_lastest_result"
        
        # HTTP會話設置
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # 彩票類型映射
        self.lottery_mapping = {
            'powercolor': {
                'name': '威力彩',
                'main_count': 6,
                'main_range': (1, 38),
                'special_range': (1, 8),
                'has_special': True
            },
            'lotto649': {
                'name': '大樂透',
                'main_count': 6,
                'main_range': (1, 49),
                'special_range': (1, 49),
                'has_special': True
            },
            'dailycash': {
                'name': '今彩539',
                'main_count': 5,
                'main_range': (1, 39),
                'has_special': False
            }
        }
        
        # 數據管理器
        if DATA_INTEGRITY_AVAILABLE:
            self.integrity_api = DataIntegrityAPI()
            self.data_manager = get_real_data_manager()
        
        self.request_delay = 2  # 請求間隔
        scraper_logger.info("基於實際結構的真實網站爬蟲初始化完成")
    
    def make_request(self, url: str, retries: int = 3) -> Optional[BeautifulSoup]:
        """安全地發送請求並解析HTML"""
        for attempt in range(retries):
            try:
                time.sleep(self.request_delay)  # 避免請求過於頻繁
                
                scraper_logger.info(f"正在請求: {url}")
                response = self.session.get(url, timeout=30, verify=False)
                response.raise_for_status()
                
                # 檢查是否為HTML內容
                if 'text/html' not in response.headers.get('content-type', ''):
                    scraper_logger.warning(f"響應不是HTML內容: {response.headers.get('content-type')}")
                    continue
                
                # 解析HTML
                soup = BeautifulSoup(response.text, 'html.parser')
                scraper_logger.info(f"成功獲取並解析頁面: {url}")
                return soup
                
            except requests.exceptions.RequestException as e:
                scraper_logger.error(f"請求失敗 (嘗試 {attempt + 1}/{retries}): {str(e)}")
                if attempt < retries - 1:
                    time.sleep(5 * (attempt + 1))  # 指數退避
                
        scraper_logger.error(f"所有請求嘗試都失敗: {url}")
        return None
    
    def parse_taiwan_date(self, date_str: str) -> str:
        """解析台灣日期格式 (114/08/21) 轉換為西元年格式 (2025-08-21)"""
        try:
            date_match = re.search(r'(\d{3})/(\d{1,2})/(\d{1,2})', date_str)
            if date_match:
                tw_year, month, day = date_match.groups()
                western_year = int(tw_year) + 1911  # 台灣年 + 1911 = 西元年
                return f"{western_year}-{month.zfill(2)}-{day.zfill(2)}"
        except Exception as e:
            scraper_logger.warning(f"日期解析失敗: {date_str}, 錯誤: {str(e)}")
        
        return datetime.now().strftime('%Y-%m-%d')
    
    def extract_numbers_from_context(self, text: str, period: int, lottery_type: str) -> Tuple[List[int], Optional[int]]:
        """從期號上下文中提取開獎號碼"""
        config = self.lottery_mapping[lottery_type]
        main_range = config['main_range']
        special_range = config['special_range'] if config['has_special'] else None
        main_count = config['main_count']
        
        numbers = []
        special_number = None
        
        # 尋找期號位置
        period_text = f'第{period}期'
        period_pos = text.find(period_text)
        
        if period_pos != -1:
            # 從期號後取一段文字分析
            context_text = text[period_pos:period_pos + 800]
            scraper_logger.info(f"期號上下文: {context_text[:200]}...")
            
            # 提取所有數字
            all_numbers = re.findall(r'\b(\d{1,2})\b', context_text)
            scraper_logger.info(f"找到的數字: {all_numbers}")
            
            # 按順序收集符合範圍的號碼
            for num_str in all_numbers:
                num = int(num_str)
                
                # 收集主號碼
                if main_range[0] <= num <= main_range[1] and len(numbers) < main_count:
                    if num not in numbers:  # 避免重複
                        numbers.append(num)
                
                # 收集特別號（如果適用）
                elif (special_range and special_range[0] <= num <= special_range[1] and 
                      special_number is None and num not in numbers):
                    special_number = num
                    
                # 如果已經收集足夠的號碼，可以停止
                if len(numbers) == main_count and (not config['has_special'] or special_number is not None):
                    break
        
        scraper_logger.info(f"{config['name']} 解析結果: 主號{numbers}, 特號{special_number}")
        return numbers, special_number
    
    def scrape_lottery_result(self, lottery_type: str, soup: BeautifulSoup) -> Optional[LotteryResult]:
        """解析指定彩票類型的開獎結果"""
        config = self.lottery_mapping[lottery_type]
        lottery_name = config['name']
        
        try:
            scraper_logger.info(f"開始解析{lottery_name}開獎結果")
            
            # 獲取完整頁面文本
            page_text = soup.get_text()
            
            # 尋找最新期號 - 台灣彩券的期號格式：第114000067期
            period_pattern = r'第(\d{9})期'
            all_periods = re.findall(period_pattern, page_text)
            
            if not all_periods:
                scraper_logger.warning(f"未找到{lottery_name}期號")
                return None
                
            # 選擇最大的期號（最新期）
            latest_period = max([int(p) for p in all_periods])
            scraper_logger.info(f"{lottery_name} 找到期號: {latest_period}")
            
            # 解析開獎日期
            draw_date = self.parse_taiwan_date(page_text)
            
            # 從上下文中提取號碼
            main_numbers, special_number = self.extract_numbers_from_context(
                page_text, latest_period, lottery_type
            )
            
            # 驗證號碼數量
            if len(main_numbers) != config['main_count']:
                scraper_logger.error(f"{lottery_name}主號碼數量不正確: 期望{config['main_count']}個，實際{len(main_numbers)}個")
                return None
            
            # 創建結果對象
            result = LotteryResult(
                lottery_type=lottery_type,
                lottery_name=lottery_name,
                period=latest_period,
                draw_date=draw_date,
                main_numbers=sorted(main_numbers),
                special_number=special_number,
                scraped_at=datetime.now().isoformat()
            )
            
            scraper_logger.info(f"{lottery_name}解析成功: 第{latest_period}期 {main_numbers} + {special_number} ({draw_date})")
            return result
            
        except Exception as e:
            scraper_logger.error(f"{lottery_name}解析失敗: {str(e)}")
            return None
    
    def scrape_latest_results(self) -> Dict[str, Optional[LotteryResult]]:
        """爬取官方網站最新開獎結果"""
        scraper_logger.info("開始爬取官方網站最新開獎結果")
        
        results = {
            'powercolor': None,
            'lotto649': None,
            'dailycash': None
        }
        
        # 爬取最新開獎結果頁面
        soup = self.make_request(self.latest_results_url)
        if not soup:
            scraper_logger.error("無法獲取最新開獎結果頁面")
            return results
        
        # 解析各彩票類型的最新結果
        for lottery_type in results.keys():
            results[lottery_type] = self.scrape_lottery_result(lottery_type, soup)
        
        return results
    
    def check_for_new_data(self) -> Dict[str, bool]:
        """檢查是否有新的開獎數據"""
        scraper_logger.info("開始檢查是否有新的開獎數據")
        
        new_data_status = {
            'powercolor': False,
            'lotto649': False,
            'dailycash': False
        }
        
        # 獲取當前數據庫中的最新期號
        if DATA_INTEGRITY_AVAILABLE:
            current_periods = self._get_current_latest_periods()
        else:
            current_periods = {}
        
        # 爬取官方網站最新數據
        latest_results = self.scrape_latest_results()
        
        # 比較期號
        for lottery_type, result in latest_results.items():
            if result and lottery_type in current_periods:
                current_period = current_periods.get(lottery_type, 0)
                if result.period > current_period:
                    new_data_status[lottery_type] = True
                    scraper_logger.info(f"{result.lottery_name}發現新數據: 期號 {result.period} > {current_period}")
                else:
                    scraper_logger.info(f"{result.lottery_name}無新數據: 期號 {result.period} <= {current_period}")
        
        return new_data_status
    
    def _get_current_latest_periods(self) -> Dict[str, int]:
        """獲取數據庫中最新的期號"""
        periods = {}
        
        try:
            import sqlite3
            conn = sqlite3.connect('data/lottery_data.db')
            
            table_mapping = {
                'powercolor': 'Powercolor',
                'lotto649': 'Lotto649', 
                'dailycash': 'DailyCash'
            }
            
            for lottery_type, table_name in table_mapping.items():
                try:
                    cursor = conn.execute(f'SELECT MAX(Period) FROM {table_name}')
                    max_period = cursor.fetchone()[0]
                    periods[lottery_type] = max_period if max_period else 0
                except:
                    periods[lottery_type] = 0
            
            conn.close()
            
        except Exception as e:
            scraper_logger.error(f"獲取最新期號失敗: {str(e)}")
            
        return periods
    
    def sync_new_data(self) -> Dict[str, Any]:
        """同步新數據到數據庫"""
        sync_report = {
            'timestamp': datetime.now().isoformat(),
            'total_synced': 0,
            'sync_results': {},
            'errors': []
        }
        
        if not DATA_INTEGRITY_AVAILABLE:
            error_msg = "數據完整性框架不可用"
            sync_report['errors'].append(error_msg)
            scraper_logger.error(error_msg)
            return sync_report
        
        # 檢查新數據
        new_data_status = self.check_for_new_data()
        
        if not any(new_data_status.values()):
            scraper_logger.info("沒有發現新的開獎數據")
            return sync_report
        
        # 爬取並同步新數據
        latest_results = self.scrape_latest_results()
        
        for lottery_type, has_new_data in new_data_status.items():
            if has_new_data and latest_results[lottery_type]:
                result = latest_results[lottery_type]
                
                try:
                    success, message = self.data_manager.insert_lottery_result(
                        lottery_type=result.lottery_type,
                        period=result.period,
                        main_numbers=result.main_numbers,
                        special_number=result.special_number,
                        date=result.draw_date,
                        source_info=f"taiwan_lottery_official_updated_scraped_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    )
                    
                    sync_report['sync_results'][lottery_type] = {
                        'success': success,
                        'message': message,
                        'period': result.period,
                        'numbers': result.main_numbers,
                        'special': result.special_number
                    }
                    
                    if success:
                        sync_report['total_synced'] += 1
                        scraper_logger.info(f"成功同步{result.lottery_name}第{result.period}期數據")
                    else:
                        sync_report['errors'].append(f"{result.lottery_name}: {message}")
                        scraper_logger.error(f"{result.lottery_name}同步失敗: {message}")
                        
                except Exception as e:
                    error_msg = f"{result.lottery_name}同步異常: {str(e)}"
                    sync_report['errors'].append(error_msg)
                    scraper_logger.error(error_msg)
        
        return sync_report
    
    def auto_sync_with_check(self) -> Dict[str, Any]:
        """自動檢查並同步新數據（二小時一次執行）"""
        scraper_logger.info("開始執行自動檢查並同步")
        
        sync_report = {
            'start_time': datetime.now().isoformat(),
            'check_results': {},
            'sync_performed': False,
            'sync_results': {},
            'end_time': None
        }
        
        try:
            # 檢查是否有新數據
            new_data_status = self.check_for_new_data()
            sync_report['check_results'] = new_data_status
            
            # 如果有新數據，執行同步
            if any(new_data_status.values()):
                scraper_logger.info("發現新數據，開始同步")
                sync_report['sync_performed'] = True
                sync_results = self.sync_new_data()
                sync_report['sync_results'] = sync_results
            else:
                scraper_logger.info("沒有新數據，跳過同步")
                
        except Exception as e:
            error_msg = f"自動同步過程發生錯誤: {str(e)}"
            sync_report['error'] = error_msg
            scraper_logger.error(error_msg)
        
        sync_report['end_time'] = datetime.now().isoformat()
        scraper_logger.info("自動檢查並同步完成")
        
        return sync_report

# 全局爬蟲實例
_updated_scraper = None

def get_updated_scraper() -> UpdatedRealWebScraper:
    """獲取全局更新版真實網站爬蟲實例"""
    global _updated_scraper
    if _updated_scraper is None:
        _updated_scraper = UpdatedRealWebScraper()
    return _updated_scraper

# 便利函數
def check_for_new_lottery_data_updated() -> Dict[str, bool]:
    """檢查是否有新的彩票數據（更新版）"""
    scraper = get_updated_scraper()
    return scraper.check_for_new_data()

def auto_sync_lottery_data_updated() -> Dict[str, Any]:
    """自動同步彩票數據（更新版）"""
    scraper = get_updated_scraper()
    return scraper.auto_sync_with_check()

def scrape_latest_lottery_results_updated() -> Dict[str, Optional[LotteryResult]]:
    """爬取最新彩票開獎結果（更新版）"""
    scraper = get_updated_scraper()
    return scraper.scrape_latest_results()

if __name__ == "__main__":
    # 測試更新版真實網站爬蟲
    print("="*80)
    print("台灣彩券官方網站真實數據爬蟲 測試（更新版）")
    print("="*80)
    
    scraper = get_updated_scraper()
    
    # 測試爬取最新結果
    print("\n爬取最新開獎結果...")
    latest_results = scraper.scrape_latest_results()
    for lottery_type, result in latest_results.items():
        if result:
            special_str = f" + {result.special_number}" if result.special_number else ""
            print(f"  {result.lottery_name} 第{result.period}期: {result.main_numbers}{special_str} ({result.draw_date})")
        else:
            lottery_name = scraper.lottery_mapping[lottery_type]['name']
            print(f"  {lottery_name}: 解析失敗")
    
    # 測試檢查新數據
    print("\n檢查是否有新數據...")
    new_data_status = scraper.check_for_new_data()
    for lottery_type, has_new in new_data_status.items():
        lottery_name = scraper.lottery_mapping[lottery_type]['name']
        status = "有新數據" if has_new else "無新數據"
        print(f"  {lottery_name}: {status}")
    
    # 測試自動同步
    print("\n執行自動同步...")
    sync_report = scraper.auto_sync_with_check()
    print(f"  檢查結果: {sync_report['check_results']}")
    print(f"  是否執行同步: {sync_report['sync_performed']}")
    if sync_report['sync_performed']:
        sync_results = sync_report.get('sync_results', {})
        print(f"  同步結果: 成功 {sync_results.get('total_synced', 0)} 筆")
    
    print("\n更新版真實網站爬蟲測試完成")