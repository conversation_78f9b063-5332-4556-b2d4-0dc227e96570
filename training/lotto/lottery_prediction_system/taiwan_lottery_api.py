#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
台灣彩券官方數據源接口 (Taiwan Lottery Official Data Source API)
確保所有彩票數據都來自官方權威來源，進一步強化數據真實性保證

核心功能：
1. 官方數據源連接 (Official Data Source Connection)
2. 數據驗證與清理 (Data Validation & Cleaning)
3. 自動數據同步 (Automatic Data Synchronization)
4. 數據完整性追蹤 (Data Integrity Tracking)
5. 失敗恢復機制 (Failure Recovery Mechanism)
"""

import requests
import sqlite3
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import re
from urllib.parse import urljoin
import hashlib

# 導入數據完整性框架
from data_integrity_framework import (
    DataIntegrityAPI, 
    DataSource, 
    get_integrity_manager
)
from real_data_integration import get_real_data_manager

# 配置專門的台灣彩券API日誌
taiwan_lottery_logger = logging.getLogger('taiwan_lottery_api')
handler = logging.FileHandler('data/taiwan_lottery_api.log')
handler.setFormatter(logging.Formatter(
    '%(asctime)s - TAIWAN_LOTTERY - %(levelname)s - %(message)s'
))
taiwan_lottery_logger.addHandler(handler)
taiwan_lottery_logger.setLevel(logging.INFO)

@dataclass
class LotteryDrawResult:
    """彩票開獎結果標準化數據結構"""
    lottery_type: str           # 彩票類型
    period: int                # 期號
    draw_date: str             # 開獎日期
    main_numbers: List[int]    # 主號碼
    special_number: Optional[int] = None  # 特別號碼
    additional_info: Optional[Dict[str, Any]] = None  # 額外信息
    source_url: Optional[str] = None  # 數據來源URL
    verification_hash: Optional[str] = None  # 驗證雜湊

    def __post_init__(self):
        # 自動計算驗證雜湊
        data_for_hash = {
            'lottery_type': self.lottery_type,
            'period': self.period,
            'main_numbers': sorted(self.main_numbers),
            'special_number': self.special_number
        }
        hash_str = json.dumps(data_for_hash, sort_keys=True)
        self.verification_hash = hashlib.md5(hash_str.encode()).hexdigest()

class TaiwanLotteryAPI:
    """台灣彩券官方API連接器"""
    
    def __init__(self):
        # 台灣彩券官方網站基礎URL - 修正為正確網址
        self.base_urls = {
            'official': 'https://www.taiwanlottery.com',
            'backup': 'https://m.taiwanlottery.com'  # 手機版作為備用
        }
        
        # HTTP會話配置
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # API端點配置
        self.api_endpoints = {
            'powercolor': '/dl.aspx?cat=PowerBall38',  # 威力彩
            'lotto649': '/dl.aspx?cat=SuperLotto638',  # 大樂透 
            'dailycash': '/dl.aspx?cat=Dailycash'      # 今彩539
        }
        
        # 數據完整性工具
        self.integrity_api = DataIntegrityAPI()
        self.data_manager = get_real_data_manager()
        
        # 請求限制配置（避免被封IP）
        self.request_delay = 2  # 秒
        self.max_retries = 3
        self.timeout = 30
        
        taiwan_lottery_logger.info("台灣彩券官方API連接器初始化完成")
    
    def fetch_latest_draw_results(self, lottery_type: str, limit: int = 10) -> List[LotteryDrawResult]:
        """
        從官方網站獲取最新開獎結果
        
        Args:
            lottery_type: 彩票類型 ('powercolor', 'lotto649', 'dailycash')
            limit: 獲取筆數限制
            
        Returns:
            開獎結果列表
        """
        if lottery_type not in self.api_endpoints:
            taiwan_lottery_logger.error(f"不支援的彩票類型: {lottery_type}")
            return []
        
        results = []
        
        for base_url_key in ['official', 'backup']:
            try:
                base_url = self.base_urls[base_url_key]
                endpoint = self.api_endpoints[lottery_type]
                full_url = urljoin(base_url, endpoint)
                
                taiwan_lottery_logger.info(f"正在從 {base_url_key} 獲取 {lottery_type} 開獎資料...")
                
                # 發送請求
                response = self._make_request(full_url)
                if response:
                    results = self._parse_draw_results(response.text, lottery_type, full_url)
                    if results:
                        taiwan_lottery_logger.info(f"成功從 {base_url_key} 獲取 {len(results)} 筆 {lottery_type} 資料")
                        break
                    
            except Exception as e:
                taiwan_lottery_logger.error(f"從 {base_url_key} 獲取資料失敗: {str(e)}")
                continue
        
        # 限制結果數量
        if limit and len(results) > limit:
            results = results[:limit]
        
        # 驗證每筆數據的完整性
        verified_results = []
        for result in results:
            if self._verify_draw_result(result):
                verified_results.append(result)
            else:
                taiwan_lottery_logger.warning(f"數據驗證失敗，跳過: {result.period}")
        
        taiwan_lottery_logger.info(f"官方數據獲取完成: {len(verified_results)}/{len(results)} 筆通過驗證")
        return verified_results
    
    def _make_request(self, url: str) -> Optional[requests.Response]:
        """安全地發送HTTP請求"""
        for attempt in range(self.max_retries):
            try:
                # 延遲請求避免被封
                if attempt > 0:
                    time.sleep(self.request_delay * (attempt + 1))
                
                response = self.session.get(
                    url, 
                    timeout=self.timeout,
                    allow_redirects=True
                )
                
                if response.status_code == 200:
                    return response
                else:
                    taiwan_lottery_logger.warning(f"HTTP {response.status_code}: {url}")
                    
            except requests.exceptions.RequestException as e:
                taiwan_lottery_logger.error(f"請求失敗 (嘗試 {attempt + 1}/{self.max_retries}): {str(e)}")
                
        return None
    
    def _parse_draw_results(self, html_content: str, lottery_type: str, source_url: str) -> List[LotteryDrawResult]:
        """
        解析HTML內容，提取開獎結果
        注意：這是台灣彩券官方網站的HTML解析，需要根據實際網站結構調整
        """
        results = []
        
        try:
            # 移除HTML標籤，提取純文本數據
            import re
            
            # 依照不同彩票類型使用不同的解析模式
            if lottery_type == 'powercolor':
                # 威力彩：6個主號碼 + 1個第二區號碼
                results = self._parse_powercolor_data(html_content, source_url)
            elif lottery_type == 'lotto649':
                # 大樂透：6個主號碼 + 1個特別號
                results = self._parse_lotto649_data(html_content, source_url)
            elif lottery_type == 'dailycash':
                # 今彩539：5個主號碼
                results = self._parse_dailycash_data(html_content, source_url)
            
        except Exception as e:
            taiwan_lottery_logger.error(f"解析 {lottery_type} 數據時發生錯誤: {str(e)}")
            
        return results
    
    def _parse_powercolor_data(self, html_content: str, source_url: str) -> List[LotteryDrawResult]:
        """解析威力彩數據"""
        results = []
        
        # 威力彩數據模式：期號,日期,號碼1,號碼2,號碼3,號碼4,號碼5,號碼6,第二區號碼
        pattern = r'(\d{9}),(\d{4}/\d{2}/\d{2}),(\d+),(\d+),(\d+),(\d+),(\d+),(\d+),(\d+)'
        matches = re.findall(pattern, html_content)
        
        for match in matches:
            try:
                period = int(match[0])
                draw_date = match[1]
                main_numbers = [int(match[i]) for i in range(2, 8)]
                special_number = int(match[8])
                
                result = LotteryDrawResult(
                    lottery_type='powercolor',
                    period=period,
                    draw_date=draw_date,
                    main_numbers=sorted(main_numbers),
                    special_number=special_number,
                    source_url=source_url
                )
                
                results.append(result)
                
            except (ValueError, IndexError) as e:
                taiwan_lottery_logger.warning(f"威力彩數據解析錯誤: {match} - {str(e)}")
                continue
        
        return results
    
    def _parse_lotto649_data(self, html_content: str, source_url: str) -> List[LotteryDrawResult]:
        """解析大樂透數據"""
        results = []
        
        # 大樂透數據模式：期號,日期,號碼1,號碼2,號碼3,號碼4,號碼5,號碼6,特別號
        pattern = r'(\d{9}),(\d{4}/\d{2}/\d{2}),(\d+),(\d+),(\d+),(\d+),(\d+),(\d+),(\d+)'
        matches = re.findall(pattern, html_content)
        
        for match in matches:
            try:
                period = int(match[0])
                draw_date = match[1]
                main_numbers = [int(match[i]) for i in range(2, 8)]
                special_number = int(match[8])
                
                result = LotteryDrawResult(
                    lottery_type='lotto649',
                    period=period,
                    draw_date=draw_date,
                    main_numbers=sorted(main_numbers),
                    special_number=special_number,
                    source_url=source_url
                )
                
                results.append(result)
                
            except (ValueError, IndexError) as e:
                taiwan_lottery_logger.warning(f"大樂透數據解析錯誤: {match} - {str(e)}")
                continue
        
        return results
    
    def _parse_dailycash_data(self, html_content: str, source_url: str) -> List[LotteryDrawResult]:
        """解析今彩539數據"""
        results = []
        
        # 今彩539數據模式：期號,日期,號碼1,號碼2,號碼3,號碼4,號碼5
        pattern = r'(\d{9}),(\d{4}/\d{2}/\d{2}),(\d+),(\d+),(\d+),(\d+),(\d+)'
        matches = re.findall(pattern, html_content)
        
        for match in matches:
            try:
                period = int(match[0])
                draw_date = match[1]
                main_numbers = [int(match[i]) for i in range(2, 7)]
                
                result = LotteryDrawResult(
                    lottery_type='dailycash',
                    period=period,
                    draw_date=draw_date,
                    main_numbers=sorted(main_numbers),
                    source_url=source_url
                )
                
                results.append(result)
                
            except (ValueError, IndexError) as e:
                taiwan_lottery_logger.warning(f"今彩539數據解析錯誤: {match} - {str(e)}")
                continue
        
        return results
    
    def _verify_draw_result(self, result: LotteryDrawResult) -> bool:
        """驗證開獎結果的數據完整性"""
        try:
            # 使用數據完整性框架進行驗證
            data_for_validation = {
                'main_numbers': result.main_numbers,
                'period': result.period,
                'date': result.draw_date
            }
            
            if result.special_number is not None:
                data_for_validation['special_number'] = result.special_number
            
            is_valid, message = self.integrity_api.validate_lottery_data(
                data_for_validation, 
                result.lottery_type, 
                f'taiwan_lottery_official:{result.source_url}'
            )
            
            if not is_valid:
                taiwan_lottery_logger.error(f"官方數據驗證失敗: {result.period} - {message}")
            
            return is_valid
            
        except Exception as e:
            taiwan_lottery_logger.error(f"數據驗證過程發生錯誤: {str(e)}")
            return False
    
    def sync_to_database(self, results: List[LotteryDrawResult]) -> Tuple[int, int, List[str]]:
        """
        將官方數據同步到數據庫
        
        Returns:
            Tuple[成功數量, 失敗數量, 錯誤訊息列表]
        """
        success_count = 0
        failed_count = 0
        error_messages = []
        
        for result in results:
            try:
                # 使用真實數據管理器插入數據
                success, message = self.data_manager.insert_lottery_result(
                    lottery_type=result.lottery_type,
                    period=result.period,
                    main_numbers=result.main_numbers,
                    special_number=result.special_number,
                    date=result.draw_date,
                    source_info=f"taiwan_lottery_official:{result.verification_hash}"
                )
                
                if success:
                    success_count += 1
                    taiwan_lottery_logger.info(f"官方數據同步成功: {result.lottery_type} 第{result.period}期")
                else:
                    failed_count += 1
                    error_messages.append(f"第{result.period}期: {message}")
                    taiwan_lottery_logger.error(f"官方數據同步失敗: {result.lottery_type} 第{result.period}期 - {message}")
                    
            except Exception as e:
                failed_count += 1
                error_msg = f"第{result.period}期同步異常: {str(e)}"
                error_messages.append(error_msg)
                taiwan_lottery_logger.error(error_msg)
        
        taiwan_lottery_logger.info(f"官方數據同步完成: 成功 {success_count} 筆，失敗 {failed_count} 筆")
        return success_count, failed_count, error_messages

class AutoSyncManager:
    """自動同步管理器"""
    
    def __init__(self):
        self.api = TaiwanLotteryAPI()
        self.sync_intervals = {
            'powercolor': timedelta(hours=6),    # 威力彩每6小時檢查一次
            'lotto649': timedelta(hours=6),      # 大樂透每6小時檢查一次  
            'dailycash': timedelta(hours=2)      # 今彩539每2小時檢查一次（更頻繁）
        }
        
        # 同步狀態追蹤
        self.last_sync = {}
        self._load_sync_status()
    
    def _load_sync_status(self):
        """載入同步狀態"""
        try:
            with open('data/auto_sync_status.json', 'r', encoding='utf-8') as f:
                status_data = json.load(f)
                for lottery_type, timestamp_str in status_data.items():
                    self.last_sync[lottery_type] = datetime.fromisoformat(timestamp_str)
        except (FileNotFoundError, json.JSONDecodeError, KeyError):
            # 初始化所有彩票類型的同步時間
            self.last_sync = {
                lottery_type: datetime.now() - timedelta(days=1)
                for lottery_type in self.sync_intervals.keys()
            }
    
    def _save_sync_status(self):
        """保存同步狀態"""
        status_data = {
            lottery_type: timestamp.isoformat()
            for lottery_type, timestamp in self.last_sync.items()
        }
        
        import os
        os.makedirs('data', exist_ok=True)
        with open('data/auto_sync_status.json', 'w', encoding='utf-8') as f:
            json.dump(status_data, f, ensure_ascii=False, indent=2)
    
    def should_sync(self, lottery_type: str) -> bool:
        """判斷是否需要同步"""
        if lottery_type not in self.sync_intervals:
            return False
        
        last_sync_time = self.last_sync.get(lottery_type, datetime.now() - timedelta(days=1))
        interval = self.sync_intervals[lottery_type]
        
        return datetime.now() - last_sync_time >= interval
    
    def perform_auto_sync(self) -> Dict[str, Any]:
        """執行自動同步"""
        sync_report = {
            'start_time': datetime.now().isoformat(),
            'results': {},
            'total_success': 0,
            'total_failed': 0,
            'errors': []
        }
        
        for lottery_type in self.sync_intervals.keys():
            if self.should_sync(lottery_type):
                taiwan_lottery_logger.info(f"開始自動同步 {lottery_type}")
                
                try:
                    # 獲取官方數據
                    results = self.api.fetch_latest_draw_results(lottery_type, limit=20)
                    
                    if results:
                        # 同步到數據庫
                        success, failed, errors = self.api.sync_to_database(results)
                        
                        sync_report['results'][lottery_type] = {
                            'fetched': len(results),
                            'success': success,
                            'failed': failed,
                            'errors': errors
                        }
                        
                        sync_report['total_success'] += success
                        sync_report['total_failed'] += failed
                        
                        # 更新同步時間
                        self.last_sync[lottery_type] = datetime.now()
                        
                    else:
                        sync_report['results'][lottery_type] = {
                            'fetched': 0,
                            'success': 0,
                            'failed': 0,
                            'errors': ['無法獲取官方數據']
                        }
                        
                except Exception as e:
                    error_msg = f"{lottery_type} 自動同步失敗: {str(e)}"
                    sync_report['errors'].append(error_msg)
                    taiwan_lottery_logger.error(error_msg)
        
        # 保存同步狀態
        self._save_sync_status()
        
        sync_report['end_time'] = datetime.now().isoformat()
        taiwan_lottery_logger.info(f"自動同步完成: 成功 {sync_report['total_success']} 筆，失敗 {sync_report['total_failed']} 筆")
        
        return sync_report

# 全局自動同步管理器實例
_auto_sync_manager = None

def get_auto_sync_manager() -> AutoSyncManager:
    """獲取全局自動同步管理器"""
    global _auto_sync_manager
    if _auto_sync_manager is None:
        _auto_sync_manager = AutoSyncManager()
    return _auto_sync_manager

# 便利函數
def fetch_official_data(lottery_type: str, limit: int = 10) -> List[Dict[str, Any]]:
    """獲取官方數據的便利函數"""
    api = TaiwanLotteryAPI()
    results = api.fetch_latest_draw_results(lottery_type, limit)
    return [asdict(result) for result in results]

def sync_all_official_data() -> Dict[str, Any]:
    """同步所有官方數據的便利函數"""
    sync_manager = get_auto_sync_manager()
    return sync_manager.perform_auto_sync()

if __name__ == "__main__":
    # 演示台灣彩券官方API
    print("="*80)
    print("台灣彩券官方數據源接口 演示")
    print("="*80)
    
    # 測試獲取威力彩官方數據
    print("\n正在獲取威力彩官方數據...")
    api = TaiwanLotteryAPI()
    
    # 注意：這是演示代碼，實際的官方API可能需要不同的解析方式
    powercolor_results = api.fetch_latest_draw_results('powercolor', limit=5)
    
    if powercolor_results:
        print(f"成功獲取 {len(powercolor_results)} 筆威力彩官方數據:")
        for result in powercolor_results[:3]:  # 顯示前3筆
            print(f"  第{result.period}期 ({result.draw_date}): {result.main_numbers} + {result.special_number}")
            print(f"  驗證雜湊: {result.verification_hash}")
            print(f"  數據來源: {result.source_url}")
            print()
    else:
        print("無法獲取官方數據 - 這可能是因為網路連接問題或需要調整解析邏輯")
    
    # 演示自動同步功能
    print("\n演示自動同步管理器...")
    sync_manager = get_auto_sync_manager()
    
    print(f"威力彩需要同步: {sync_manager.should_sync('powercolor')}")
    print(f"大樂透需要同步: {sync_manager.should_sync('lotto649')}")
    print(f"今彩539需要同步: {sync_manager.should_sync('dailycash')}")
    
    print("\n官方數據源接口演示完成")
    print("注意：實際部署時需要根據台灣彩券官方網站的具體HTML結構調整解析邏輯")