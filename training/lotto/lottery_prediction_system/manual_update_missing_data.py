#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手動更新缺失的彩券開獎數據
用於補充8月24日至8月28日的資料
"""

import sqlite3
import logging
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('manual_updater')

def update_missing_data():
    """手動更新缺失的今彩539開獎資料"""
    
    # 根據台灣彩券網站，這些是缺失的開獎資料
    # 注意：這些是範例資料，請根據實際開獎結果更新
    missing_data = [
        # 期號, 日期, 號碼1-5
        # 請訪問 https://www.taiwanlottery.com 查看實際開獎號碼
        # 並更新以下資料
        
        # 範例格式：
        # (114000205, '2025-08-24', 1, 2, 3, 4, 5),  # 請替換為實際號碼
        # (114000206, '2025-08-26', 6, 7, 8, 9, 10),  # 請替換為實際號碼
        # (114000207, '2025-08-27', 11, 12, 13, 14, 15),  # 請替換為實際號碼
        # (114000208, '2025-08-28', 16, 17, 18, 19, 20),  # 請替換為實際號碼
    ]
    
    if not missing_data:
        print("⚠️  請先填入實際的開獎資料！")
        print("訪問 https://www.taiwanlottery.com 查看最新開獎結果")
        print("然後更新本程式中的 missing_data 列表")
        return
    
    try:
        conn = sqlite3.connect('data/lottery_data.db')
        cursor = conn.cursor()
        
        inserted_count = 0
        
        for data in missing_data:
            period, date, n1, n2, n3, n4, n5 = data
            
            # 檢查是否已存在
            cursor.execute("SELECT Period FROM DailyCash WHERE Period = ?", (period,))
            if cursor.fetchone():
                logger.info(f"期號 {period} 已存在，跳過")
                continue
            
            # 插入新資料
            cursor.execute("""
                INSERT INTO DailyCash (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, ('今彩539', period, date, n1, n2, n3, n4, n5))
            
            inserted_count += 1
            logger.info(f"成功插入期號 {period} ({date}) 的開獎資料")
        
        conn.commit()
        conn.close()
        
        if inserted_count > 0:
            print(f"✅ 成功更新 {inserted_count} 筆今彩539開獎資料")
        else:
            print("⚠️  所有資料都已是最新的")
            
    except Exception as e:
        logger.error(f"更新失敗: {e}")
        print(f"❌ 更新失敗: {e}")

def check_current_status():
    """檢查目前資料庫狀態"""
    try:
        conn = sqlite3.connect('data/lottery_data.db')
        cursor = conn.cursor()
        
        # 查詢最新5筆資料
        cursor.execute("""
            SELECT Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5
            FROM DailyCash
            ORDER BY Period DESC
            LIMIT 5
        """)
        
        results = cursor.fetchall()
        
        print("\n📊 目前資料庫最新5筆今彩539開獎記錄：")
        print("-" * 60)
        
        for row in results:
            period, date, n1, n2, n3, n4, n5 = row
            print(f"期號: {period} | 日期: {date} | 號碼: {n1:02d}, {n2:02d}, {n3:02d}, {n4:02d}, {n5:02d}")
        
        print("-" * 60)
        
        # 計算缺失天數
        if results:
            latest_date_str = results[0][1]
            latest_date = datetime.strptime(latest_date_str, '%Y-%m-%d')
            today = datetime.now()
            days_diff = (today - latest_date).days
            
            if days_diff > 0:
                print(f"\n⚠️  最新資料日期為 {latest_date_str}，距今已 {days_diff} 天")
                print("請更新缺失的開獎資料")
            else:
                print("\n✅ 資料已是最新的")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")

def main():
    """主程式"""
    print("=" * 60)
    print("🎲 手動更新缺失的今彩539開獎資料")
    print("=" * 60)
    
    # 先檢查目前狀態
    check_current_status()
    
    print("\n" + "=" * 60)
    print("📝 更新說明：")
    print("1. 請訪問 https://www.taiwanlottery.com")
    print("2. 查看今彩539的最新開獎結果")
    print("3. 編輯本程式，填入 missing_data 列表中的實際開獎號碼")
    print("4. 重新執行本程式完成更新")
    print("=" * 60)
    
    # 執行更新（需要先填入資料）
    # update_missing_data()

if __name__ == "__main__":
    main()