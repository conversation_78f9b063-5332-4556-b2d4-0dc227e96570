# 彩票預測系統 - 統一依賴文件
# 整合所有模組的Python依賴包

# === 核心Web框架 ===
Flask==2.3.3
Flask-CORS==4.0.0
Werkzeug==2.3.6
Jinja2==3.1.2
MarkupSafe==2.1.3

# FastAPI (Phase 3)
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0

# === 數據處理 ===
numpy==1.24.3
pandas==2.0.3
scipy==1.11.1

# === 機器學習 ===
scikit-learn==1.3.0
xgboost==1.7.6
lightgbm==4.0.0

# === 數據可視化 ===
matplotlib==3.7.2
seaborn==0.12.2
plotly==5.15.0
streamlit==1.29.0  # 用於快速原型

# === 網絡請求 ===
requests==2.31.0
httpx==0.24.1
aiohttp==3.9.1  # 異步HTTP

# === 緩存與存儲 ===
redis==4.6.0
cachetools==5.3.1
SQLAlchemy==2.0.19

# === 調度與異步 ===
APScheduler==3.10.4
celery==5.3.4
asyncio==3.4.3

# === 日期時間處理 ===
python-dateutil==2.8.2
pytz==2023.3

# === 配置管理 ===
PyYAML==6.0.1
python-dotenv==1.0.0

# === 日誌與監控 ===
loguru==0.7.0
prometheus-client==0.17.1
psutil==5.9.5

# === 安全 ===
bcrypt==4.0.1
PyJWT==2.8.0

# === 並發處理 ===
gevent==23.7.0
gunicorn==21.2.0
joblib==1.3.1

# === 文件處理 ===
openpyxl==3.1.2
xlsxwriter==3.1.2
BeautifulSoup4==4.12.2

# === 開發工具 ===
ipython==8.14.0
tqdm==4.65.0
click==8.1.6

# === 數據驗證 ===
marshmallow==3.20.1
email-validator==2.0.0

# === 性能優化 ===
optuna==3.3.0
numba==0.58.1

# === 其他工具 ===
retrying==1.3.4
chardet==5.1.0
typing-extensions==4.7.1
watchdog==3.0.0

# === 表單處理 ===
WTForms==3.0.1
flask-wtf==1.1.1

# === API文檔 ===
flask-restx==1.1.0

# === 統計分析 ===
statsmodels==0.14.0

# === 進程管理 (Docker內部使用) ===
supervisor==4.2.5