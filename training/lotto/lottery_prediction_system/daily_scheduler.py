#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彩票數據每日自動更新調度器
提供多種定時更新選項和Web接口控制
"""

import schedule
import time
import threading
import logging
from datetime import datetime, timedelta
from automated_lottery_updater import AutomatedLotteryUpdater
import json
import os

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/daily_scheduler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('daily_scheduler')

class DailyScheduler:
    """每日定時任務調度器"""
    
    def __init__(self):
        self.updater = AutomatedLotteryUpdater()
        self.is_running = False
        self.scheduler_thread = None
        self.config_file = 'scheduler_config.json'
        self.load_config()
        
    def load_config(self):
        """加載調度器配置"""
        default_config = {
            'enabled': True,
            'update_times': ['09:30', '22:00'],  # 威力彩開獎後的時間
            'auto_retry': True,
            'retry_interval_minutes': 60,
            'max_retries': 3
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = default_config
                self.save_config()
                
        except Exception as e:
            logger.error(f"載入配置失敗，使用默認配置: {e}")
            self.config = default_config
    
    def save_config(self):
        """保存調度器配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存配置失敗: {e}")
    
    def setup_schedule(self):
        """設置定時任務"""
        schedule.clear()
        
        if not self.config.get('enabled', True):
            logger.info("調度器已禁用")
            return
        
        # 為每個指定時間設置任務
        for update_time in self.config.get('update_times', ['09:30', '22:00']):
            schedule.every().day.at(update_time).do(self.scheduled_update)
            logger.info(f"✅ 已設置每日 {update_time} 自動更新任務")
        
        # 設置每小時檢查一次是否有遺漏的期號
        schedule.every().hour.do(self.check_missing_periods)
        
    def scheduled_update(self):
        """執行定時更新任務"""
        logger.info("🕒 執行定時更新任務...")
        
        try:
            results = self.updater.update_all_lotteries()
            
            # 記錄更新結果
            self.log_update_results(results)
            
            # 如果有失敗且啟用自動重試
            if not all(results.values()) and self.config.get('auto_retry', True):
                self.schedule_retry()
                
        except Exception as e:
            logger.error(f"定時更新失敗: {e}")
            if self.config.get('auto_retry', True):
                self.schedule_retry()
    
    def schedule_retry(self):
        """安排重試任務"""
        retry_minutes = self.config.get('retry_interval_minutes', 60)
        logger.info(f"將在 {retry_minutes} 分鐘後重試更新")
        
        schedule.every(retry_minutes).minutes.do(self.retry_update).tag('retry')
    
    def retry_update(self):
        """重試更新任務"""
        logger.info("🔄 執行重試更新任務...")
        
        try:
            results = self.updater.update_all_lotteries()
            self.log_update_results(results)
            
            # 如果成功或達到最大重試次數，取消後續重試
            if all(results.values()):
                schedule.clear('retry')
                logger.info("✅ 重試成功，取消後續重試")
            
        except Exception as e:
            logger.error(f"重試更新失敗: {e}")
    
    def check_missing_periods(self):
        """檢查是否有遺漏的期號"""
        try:
            # 簡單檢查：比較當前日期和資料庫最新日期
            current_date = datetime.now()
            latest_period = self.updater.get_latest_period_from_db('Powercolor')
            
            # 威力彩每週一、四開獎，檢查是否遺漏
            weekday = current_date.weekday()
            if weekday in [0, 3]:  # 週一=0, 週四=3
                # 檢查是否需要更新（簡化版本）
                if latest_period < 114000070:  # 假設的期號範圍
                    logger.info("⚠️ 檢測到可能的遺漏期號，執行補充更新")
                    self.updater.update_powercolor()
                    
        except Exception as e:
            logger.error(f"檢查遺漏期號時出錯: {e}")
    
    def log_update_results(self, results: dict):
        """記錄更新結果"""
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        logger.info(f"📊 更新結果: {success_count}/{total_count} 成功")
        
        for lottery_type, success in results.items():
            status = "✅" if success else "❌"
            logger.info(f"  {lottery_type}: {status}")
    
    def start(self):
        """啟動調度器"""
        if self.is_running:
            logger.warning("調度器已在運行中")
            return False
        
        self.setup_schedule()
        self.is_running = True
        
        def run_scheduler():
            logger.info("🚀 彩票數據自動更新調度器已啟動")
            logger.info(f"📅 更新時間: {', '.join(self.config.get('update_times', []))}")
            
            while self.is_running:
                schedule.run_pending()
                time.sleep(60)  # 每分鐘檢查一次
                
        self.scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        return True
    
    def stop(self):
        """停止調度器"""
        if not self.is_running:
            return False
            
        self.is_running = False
        schedule.clear()
        logger.info("🛑 調度器已停止")
        
        return True
    
    def get_status(self) -> dict:
        """獲取調度器狀態"""
        next_runs = []
        for job in schedule.jobs:
            next_runs.append({
                'time': str(job.next_run),
                'function': job.job_func.__name__
            })
        
        return {
            'is_running': self.is_running,
            'config': self.config,
            'next_runs': next_runs
        }
    
    def manual_update_now(self) -> dict:
        """立即執行手動更新"""
        logger.info("🔄 執行手動更新...")
        return self.updater.update_all_lotteries()


def main():
    """主程式 - 命令行控制界面"""
    print("🕒 彩票數據每日自動更新調度器")
    print("=" * 50)
    
    scheduler = DailyScheduler()
    
    print("選擇操作:")
    print("1. 啟動調度器 (後台運行)")
    print("2. 立即執行更新")
    print("3. 查看調度器狀態") 
    print("4. 修改配置")
    print("5. 停止調度器")
    
    try:
        choice = input("請選擇 (1-5): ").strip()
        
        if choice == '1':
            if scheduler.start():
                print("✅ 調度器啟動成功，將在背景運行")
                print("按 Ctrl+C 停止")
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    scheduler.stop()
                    print("\n👋 調度器已停止")
            else:
                print("❌ 調度器啟動失敗")
                
        elif choice == '2':
            results = scheduler.manual_update_now()
            print("\n📊 更新結果:")
            for lottery, success in results.items():
                status = "✅" if success else "❌"
                print(f"  {lottery}: {status}")
                
        elif choice == '3':
            status = scheduler.get_status()
            print(f"\n調度器狀態: {'運行中' if status['is_running'] else '已停止'}")
            print(f"更新時間: {', '.join(status['config']['update_times'])}")
            if status['next_runs']:
                print("下次執行時間:")
                for run in status['next_runs']:
                    print(f"  {run['function']}: {run['time']}")
                    
        elif choice == '4':
            print("配置修改功能開發中...")
            
        elif choice == '5':
            if scheduler.stop():
                print("✅ 調度器已停止")
            else:
                print("❌ 調度器未在運行")
        else:
            print("❌ 無效選擇")
            
    except KeyboardInterrupt:
        print("\n👋 程式已退出")
    except Exception as e:
        print(f"❌ 出現錯誤: {e}")


if __name__ == "__main__":
    main()