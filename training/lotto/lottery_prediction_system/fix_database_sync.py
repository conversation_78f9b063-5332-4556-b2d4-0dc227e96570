#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
資料庫同步修復工具 - 解決更新與顯示不一致問題
"""

import sqlite3
import pandas as pd
import logging
from datetime import datetime
import os
import shutil

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('database_sync_fixer')

class DatabaseSyncFixer:
    def __init__(self):
        self.main_db = 'data/lottery_data.db'  # Web介面使用的主資料庫
        self.realtime_db = 'data/lottery.db'    # 更新功能使用的即時資料庫
        
    def diagnose_issue(self):
        """診斷資料庫同步問題"""
        print("🔍 診斷資料庫同步問題...")
        
        # 檢查主資料庫
        main_conn = sqlite3.connect(self.main_db)
        main_cursor = main_conn.cursor()
        
        # 檢查威力彩最新期數
        main_cursor.execute("SELECT MAX(Period) as max_period FROM Powercolor")
        main_max = main_cursor.fetchone()[0] or 0
        
        main_cursor.execute("SELECT COUNT(*) as count FROM Powercolor")
        main_count = main_cursor.fetchone()[0]
        
        print(f"📊 主資料庫 (lottery_data.db):")
        print(f"   威力彩最新期數: {main_max}")
        print(f"   威力彩總記錄數: {main_count}")
        
        # 檢查即時資料庫
        if os.path.exists(self.realtime_db):
            realtime_conn = sqlite3.connect(self.realtime_db)
            realtime_cursor = realtime_conn.cursor()
            
            # 檢查是否有realtime_data表
            realtime_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='realtime_data'")
            if realtime_cursor.fetchone():
                realtime_cursor.execute("SELECT period, lottery_type, numbers FROM realtime_data WHERE lottery_type='powercolor' ORDER BY period DESC LIMIT 5")
                realtime_data = realtime_cursor.fetchall()
                
                print(f"📊 即時資料庫 (lottery.db):")
                print(f"   威力彩最新記錄:")
                for record in realtime_data:
                    print(f"     期數: {record[0]}, 類型: {record[1]}, 號碼: {record[2]}")
            else:
                print(f"⚠️  即時資料庫中沒有realtime_data表")
                
            realtime_conn.close()
        else:
            print(f"⚠️  即時資料庫不存在: {self.realtime_db}")
            
        main_conn.close()
        
    def sync_databases(self):
        """同步資料庫數據"""
        print("\n🔧 開始資料庫同步...")
        
        # 備份主資料庫
        backup_path = f"{self.main_db}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(self.main_db, backup_path)
        print(f"✅ 主資料庫已備份到: {backup_path}")
        
        # 從即時資料庫讀取新數據
        if not os.path.exists(self.realtime_db):
            print("❌ 即時資料庫不存在，無法同步")
            return False
            
        realtime_conn = sqlite3.connect(self.realtime_db)
        main_conn = sqlite3.connect(self.main_db)
        
        try:
            # 檢查即時資料庫中的新數據
            realtime_df = pd.read_sql_query("""
                SELECT * FROM realtime_data 
                WHERE lottery_type = 'powercolor'
                ORDER BY period DESC
            """, realtime_conn)
            
            if realtime_df.empty:
                print("⚠️  即時資料庫中沒有威力彩數據")
                return False
                
            print(f"📊 找到 {len(realtime_df)} 條威力彩記錄")
            
            # 獲取主資料庫中的最新期數
            main_cursor = main_conn.cursor()
            main_cursor.execute("SELECT MAX(Period) as max_period FROM Powercolor")
            main_max_period = main_cursor.fetchone()[0] or 0
            
            # 找出需要同步的新記錄
            new_records = realtime_df[realtime_df['period'].astype(int) > main_max_period]
            
            if new_records.empty:
                print("✅ 沒有需要同步的新記錄")
                return True
                
            print(f"🔄 需要同步 {len(new_records)} 條新記錄")
            
            # 同步每條新記錄
            synced_count = 0
            for _, record in new_records.iterrows():
                try:
                    # 解析號碼
                    numbers = record['numbers'].split(',')
                    if len(numbers) >= 7:  # 威力彩需要7個號碼
                        # 插入到主資料庫
                        main_cursor.execute("""
                            INSERT OR REPLACE INTO Powercolor 
                            (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, Second_district)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            'powercolor',
                            int(record['period']),
                            record['draw_date'][:10],  # 只取日期部分
                            int(numbers[0]),
                            int(numbers[1]), 
                            int(numbers[2]),
                            int(numbers[3]),
                            int(numbers[4]),
                            int(numbers[5]),
                            int(numbers[6])
                        ))
                        synced_count += 1
                        print(f"✅ 同步期數 {record['period']}")
                    else:
                        print(f"⚠️  期數 {record['period']} 號碼格式錯誤: {record['numbers']}")
                        
                except Exception as e:
                    print(f"❌ 同步期數 {record['period']} 失敗: {e}")
                    
            # 提交變更
            main_conn.commit()
            print(f"🎉 成功同步 {synced_count} 條記錄")
            return True
            
        except Exception as e:
            print(f"❌ 同步過程發生錯誤: {e}")
            main_conn.rollback()
            return False
            
        finally:
            realtime_conn.close()
            main_conn.close()
    
    def verify_sync(self):
        """驗證同步結果"""
        print("\n🧪 驗證同步結果...")
        
        conn = sqlite3.connect(self.main_db)
        cursor = conn.cursor()
        
        # 檢查最新期數
        cursor.execute("SELECT MAX(Period) as max_period FROM Powercolor")
        max_period = cursor.fetchone()[0]
        
        # 檢查期數114000068是否存在
        cursor.execute("SELECT * FROM Powercolor WHERE Period = 114000068")
        record_68 = cursor.fetchone()
        
        print(f"📊 同步後狀態:")
        print(f"   威力彩最新期數: {max_period}")
        print(f"   期數114000068存在: {'✅ 是' if record_68 else '❌ 否'}")
        
        if record_68:
            print(f"   期數114000068詳情: {record_68}")
            
        conn.close()

def main():
    print("🎯 資料庫同步修復工具")
    print("=" * 50)
    
    fixer = DatabaseSyncFixer()
    
    # 診斷問題
    fixer.diagnose_issue()
    
    # 詢問是否執行同步
    response = input("\n是否執行資料庫同步？(y/N): ")
    if response.lower() == 'y':
        success = fixer.sync_databases()
        if success:
            fixer.verify_sync()
            print("\n🎉 資料庫同步完成！現在Web介面應該能看到最新的開獎結果了。")
        else:
            print("\n❌ 資料庫同步失敗，請檢查錯誤訊息。")
    else:
        print("\n👋 取消同步操作")

if __name__ == "__main__":
    main()