#!/usr/bin/env python3
"""
SSL環境修復 - 在系統啟動前執行
"""

import ssl
import urllib3
import os

# 抑制SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 設定環境變數
os.environ['PYTHONHTTPSVERIFY'] = '0'
os.environ['CURL_CA_BUNDLE'] = ''
os.environ['REQUESTS_CA_BUNDLE'] = ''

# 創建寬鬆的SSL上下文
def create_unverified_context():
    context = ssl.create_default_context()
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE
    return context

# 替換默認SSL上下文
ssl._create_default_https_context = create_unverified_context

print("🔧 SSL環境修復已應用")
