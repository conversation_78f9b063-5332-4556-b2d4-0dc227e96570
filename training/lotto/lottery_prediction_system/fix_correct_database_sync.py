#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正確的資料庫同步修復工具 - 從lottery.db同步到lottery_data.db
"""

import sqlite3
import pandas as pd
import logging
from datetime import datetime
import os
import shutil

class CorrectDatabaseSyncFixer:
    def __init__(self):
        self.main_db = 'data/lottery_data.db'  # Web介面使用的主資料庫
        self.source_db = 'data/lottery.db'     # 更新功能寫入的資料庫
        
    def sync_powercolor_data(self):
        """從lottery.db同步威力彩資料到lottery_data.db"""
        print("🔄 開始同步威力彩資料...")
        
        # 備份主資料庫
        backup_path = f"{self.main_db}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(self.main_db, backup_path)
        print(f"✅ 主資料庫已備份到: {backup_path}")
        
        source_conn = sqlite3.connect(self.source_db)
        main_conn = sqlite3.connect(self.main_db)
        
        try:
            # 獲取來源資料庫中的威力彩資料
            source_cursor = source_conn.cursor()
            source_cursor.execute("SELECT MAX(Period) FROM Powercolor")
            source_max = source_cursor.fetchone()[0] or 0
            
            # 獲取主資料庫中的威力彩資料  
            main_cursor = main_conn.cursor()
            main_cursor.execute("SELECT MAX(Period) FROM Powercolor")
            main_max = main_cursor.fetchone()[0] or 0
            
            print(f"📊 來源資料庫最新期數: {source_max}")
            print(f"📊 主資料庫最新期數: {main_max}")
            
            if source_max <= main_max:
                print("✅ 主資料庫已是最新，無需同步")
                return True
            
            # 獲取需要同步的記錄
            source_cursor.execute("""
                SELECT * FROM Powercolor 
                WHERE Period > ? 
                ORDER BY Period
            """, (main_max,))
            
            new_records = source_cursor.fetchall()
            
            if not new_records:
                print("✅ 沒有需要同步的新記錄")
                return True
                
            print(f"🔄 需要同步 {len(new_records)} 條新記錄")
            
            # 獲取來源表結構
            source_cursor.execute("PRAGMA table_info(Powercolor)")
            columns = [col[1] for col in source_cursor.fetchall()]
            
            # 同步記錄
            synced_count = 0
            for record in new_records:
                try:
                    # 構建插入語句
                    placeholders = ','.join(['?' for _ in columns])
                    insert_sql = f"INSERT OR REPLACE INTO Powercolor ({','.join(columns)}) VALUES ({placeholders})"
                    
                    main_cursor.execute(insert_sql, record)
                    synced_count += 1
                    print(f"✅ 同步期數 {record[1]} (第 {synced_count} 條)")  # Period通常在第2個位置
                    
                except Exception as e:
                    print(f"❌ 同步記錄失敗: {e}")
                    print(f"   記錄內容: {record}")
            
            # 提交變更
            main_conn.commit()
            print(f"🎉 成功同步 {synced_count} 條威力彩記錄")
            
            return True
            
        except Exception as e:
            print(f"❌ 同步過程發生錯誤: {e}")
            main_conn.rollback()
            return False
            
        finally:
            source_conn.close()
            main_conn.close()
    
    def sync_all_lottery_types(self):
        """同步所有彩票類型"""
        print("🎯 開始同步所有彩票類型...")
        
        lottery_tables = ['Powercolor', 'Lotto649', 'DailyCash']
        success_count = 0
        
        for table in lottery_tables:
            print(f"\\n🔄 同步 {table}...")
            try:
                if self.sync_table(table):
                    success_count += 1
                    print(f"✅ {table} 同步完成")
                else:
                    print(f"❌ {table} 同步失敗")
            except Exception as e:
                print(f"❌ {table} 同步異常: {e}")
        
        print(f"\\n📊 同步完成: {success_count}/{len(lottery_tables)} 個表")
        return success_count == len(lottery_tables)
    
    def sync_table(self, table_name):
        """同步指定表"""
        source_conn = sqlite3.connect(self.source_db)
        main_conn = sqlite3.connect(self.main_db)
        
        try:
            # 檢查表是否存在
            source_cursor = source_conn.cursor()
            source_cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            if not source_cursor.fetchone():
                print(f"⚠️  來源資料庫中不存在表: {table_name}")
                return True  # 不算錯誤
                
            main_cursor = main_conn.cursor()
            main_cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            if not main_cursor.fetchone():
                print(f"⚠️  主資料庫中不存在表: {table_name}")
                return True  # 不算錯誤
            
            # 獲取最新期數進行對比
            source_cursor.execute(f"SELECT MAX(Period) FROM {table_name}")
            source_max = source_cursor.fetchone()[0] or 0
            
            main_cursor.execute(f"SELECT MAX(Period) FROM {table_name}")
            main_max = main_cursor.fetchone()[0] or 0
            
            if source_max <= main_max:
                print(f"   {table_name}: 已是最新 (期數 {main_max})")
                return True
            
            # 同步新記錄
            source_cursor.execute(f"SELECT * FROM {table_name} WHERE Period > ? ORDER BY Period", (main_max,))
            new_records = source_cursor.fetchall()
            
            if new_records:
                # 獲取列名
                source_cursor.execute(f"PRAGMA table_info({table_name})")
                columns = [col[1] for col in source_cursor.fetchall()]
                
                # 插入新記錄
                placeholders = ','.join(['?' for _ in columns])
                insert_sql = f"INSERT OR REPLACE INTO {table_name} ({','.join(columns)}) VALUES ({placeholders})"
                
                for record in new_records:
                    main_cursor.execute(insert_sql, record)
                
                main_conn.commit()
                print(f"   {table_name}: 同步了 {len(new_records)} 條記錄 (最新期數: {source_max})")
            
            return True
            
        finally:
            source_conn.close()
            main_conn.close()
    
    def verify_sync(self):
        """驗證同步結果"""
        print("\\n🧪 驗證同步結果...")
        
        conn = sqlite3.connect(self.main_db)
        cursor = conn.cursor()
        
        # 檢查威力彩期數114000068
        cursor.execute("SELECT Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, Second_district FROM Powercolor WHERE Period = 114000068")
        record_68 = cursor.fetchone()
        
        cursor.execute("SELECT MAX(Period) as max_period FROM Powercolor")
        max_period = cursor.fetchone()[0]
        
        print(f"📊 驗證結果:")
        print(f"   威力彩最新期數: {max_period}")
        print(f"   期數114000068存在: {'✅ 是' if record_68 else '❌ 否'}")
        
        if record_68:
            numbers = f"{record_68[2]:02d}, {record_68[3]:02d}, {record_68[4]:02d}, {record_68[5]:02d}, {record_68[6]:02d}, {record_68[7]:02d}"
            print(f"   期數114000068詳情:")
            print(f"     開獎日期: {record_68[1]}")
            print(f"     第一區號碼: {numbers}")
            print(f"     第二區號碼: {record_68[8]:02d}")
        
        conn.close()
        return record_68 is not None

def main():
    print("🎯 正確的資料庫同步修復工具")
    print("解決 lottery.db → lottery_data.db 同步問題")
    print("=" * 60)
    
    fixer = CorrectDatabaseSyncFixer()
    
    # 執行同步
    print("🔄 執行威力彩資料同步...")
    success = fixer.sync_powercolor_data()
    
    if success:
        # 驗證結果
        if fixer.verify_sync():
            print("\\n🎉 資料庫同步完成！")
            print("現在 Web 介面 http://localhost:7891/results 應該能看到期數114000068了！")
        else:
            print("\\n⚠️  同步完成但驗證失敗，請檢查資料")
    else:
        print("\\n❌ 資料庫同步失敗")

if __name__ == "__main__":
    main()