#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可觀察預測追蹤器
自動寫入預測記錄，持續追蹤和分析預測表現
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from collections import Counter, defaultdict
import logging
import os

# 導入預測模組
from stable_confidence_predictor import StableConfidencePredictor
from confidence_prediction_recorder import ConfidencePredictionRecorder

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('observable_tracker')

class ObservablePredictionTracker:
    """可觀察預測追蹤器"""
    
    def __init__(self, db_path: str = None):
        if db_path is None:
            self.db_path = os.path.join(
                os.path.dirname(os.path.abspath(__file__)), 
                'data', 'lottery_data.db'
            )
        else:
            self.db_path = db_path
        
        self.predictor = StableConfidencePredictor(self.db_path)
        self.recorder = ConfidencePredictionRecorder(self.db_path)
        
    def start_tracking_session(self, lottery_type: str = 'powercolor') -> Dict:
        """
        開始追蹤會話 - 生成預測並寫入記錄
        """
        logger.info(f"開始 {lottery_type} 預測追蹤會話")
        
        try:
            # 1. 生成穩定預測並記錄
            prediction_result = self.recorder.generate_and_record_stable_prediction(lottery_type)
            
            if not prediction_result['success']:
                return {
                    'success': False,
                    'error': prediction_result.get('error', '預測生成失敗')
                }
            
            # 2. 創建追蹤記錄
            tracking_record = self._create_tracking_record(prediction_result, lottery_type)
            
            # 3. 初始化觀察儀表板
            dashboard_data = self._initialize_dashboard(prediction_result, lottery_type)
            
            result = {
                'success': True,
                'tracking_session': {
                    'session_id': f"{lottery_type}_{prediction_result['period']}",
                    'lottery_type': lottery_type,
                    'period': prediction_result['period'],
                    'prediction_date': prediction_result['prediction_details']['generation_time'],
                    'status': 'tracking_started'
                },
                'prediction_summary': {
                    'numbers': prediction_result['prediction']['numbers'],
                    'special': prediction_result['prediction']['special'],
                    'confidence': prediction_result['prediction']['confidence_score'],
                    'stability': prediction_result['prediction']['stability_score'],
                    'method': prediction_result['prediction']['method']
                },
                'tracking_details': tracking_record,
                'observation_dashboard': dashboard_data,
                'next_steps': {
                    'wait_for_draw': '等待開獎結果',
                    'auto_update': '系統將自動更新命中結果',
                    'view_progress': '可隨時查看追蹤進度'
                }
            }
            
            logger.info(f"追蹤會話已啟動 - 期號: {prediction_result['period']}")
            return result
            
        except Exception as e:
            logger.error(f"啟動追蹤會話失敗: {str(e)}")
            return {
                'success': False,
                'error': f'追蹤會話啟動失敗: {str(e)}'
            }
    
    def get_observation_dashboard(self, lottery_type: str = 'powercolor') -> Dict:
        """
        獲取可觀察儀表板數據
        """
        logger.info(f"獲取 {lottery_type} 觀察儀表板")
        
        try:
            # 1. 獲取當前預測狀態
            current_prediction = self._get_latest_prediction(lottery_type)
            
            # 2. 獲取歷史表現數據
            performance_data = self._get_performance_analysis(lottery_type)
            
            # 3. 獲取追蹤統計
            tracking_stats = self._get_tracking_statistics(lottery_type)
            
            # 4. 獲取趨勢分析
            trend_analysis = self._get_trend_analysis(lottery_type)
            
            # 5. 準備觀察數據
            dashboard = {
                'dashboard_type': '可觀察預測追蹤',
                'lottery_type': lottery_type,
                'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'current_prediction': current_prediction,
                'performance_overview': performance_data,
                'tracking_statistics': tracking_stats,
                'trend_analysis': trend_analysis,
                'observation_insights': self._generate_observation_insights(
                    performance_data, tracking_stats, trend_analysis
                ),
                'actionable_recommendations': self._generate_recommendations(
                    performance_data, trend_analysis
                )
            }
            
            return dashboard
            
        except Exception as e:
            logger.error(f"獲取觀察儀表板失敗: {str(e)}")
            return {
                'success': False,
                'error': f'儀表板獲取失敗: {str(e)}'
            }
    
    def update_prediction_results(self, lottery_type: str = 'powercolor') -> Dict:
        """
        更新預測結果 - 檢查開獎並更新命中情況
        """
        logger.info(f"更新 {lottery_type} 預測結果")
        
        try:
            # 1. 獲取最新開獎結果
            latest_draw = self._get_latest_draw_result(lottery_type)
            
            if not latest_draw:
                return {
                    'success': True,
                    'message': '暫無新開獎結果',
                    'status': 'waiting_for_draw'
                }
            
            # 2. 獲取對應的預測記錄
            predictions = self._get_predictions_for_period(lottery_type, latest_draw['period'])
            
            updated_count = 0
            results = []
            
            for prediction in predictions:
                # 3. 計算命中情況
                hit_analysis = self._calculate_hit_analysis(prediction, latest_draw)
                
                # 4. 更新預測記錄
                update_success = self._update_prediction_hit_result(
                    lottery_type, prediction['id'], hit_analysis, latest_draw
                )
                
                if update_success:
                    updated_count += 1
                    results.append({
                        'prediction_id': prediction['id'],
                        'period': prediction['period'],
                        'method': prediction['method'],
                        'predicted_numbers': prediction['numbers'],
                        'actual_numbers': latest_draw['numbers'],
                        'hit_analysis': hit_analysis
                    })
            
            # 5. 生成更新報告
            update_report = {
                'success': True,
                'updated_predictions': updated_count,
                'draw_result': latest_draw,
                'hit_results': results,
                'summary': self._generate_update_summary(results),
                'next_observation': '可查看更新後的追蹤數據'
            }
            
            logger.info(f"預測結果更新完成 - 更新了 {updated_count} 條記錄")
            return update_report
            
        except Exception as e:
            logger.error(f"更新預測結果失敗: {str(e)}")
            return {
                'success': False,
                'error': f'預測結果更新失敗: {str(e)}'
            }
    
    def generate_tracking_report(self, lottery_type: str = 'powercolor', 
                                periods: int = 20) -> Dict:
        """
        生成追蹤報告
        """
        logger.info(f"生成 {lottery_type} 追蹤報告 (最近 {periods} 期)")
        
        try:
            # 1. 獲取追蹤數據
            tracking_data = self._get_tracking_data(lottery_type, periods)
            
            # 2. 執行深度分析
            deep_analysis = self._perform_deep_analysis(tracking_data)
            
            # 3. 生成可視化數據
            visualization_data = self._prepare_visualization_data(tracking_data)
            
            # 4. 創建追蹤報告
            report = {
                'report_type': '可觀察預測追蹤報告',
                'lottery_type': lottery_type,
                'analysis_period': f'最近 {len(tracking_data)} 期',
                'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'executive_summary': deep_analysis['summary'],
                'detailed_analysis': deep_analysis,
                'visualization_data': visualization_data,
                'tracking_insights': self._extract_tracking_insights(tracking_data),
                'performance_metrics': self._calculate_performance_metrics(tracking_data),
                'strategic_recommendations': self._generate_strategic_recommendations(deep_analysis),
                'observation_conclusions': self._draw_observation_conclusions(deep_analysis)
            }
            
            logger.info(f"追蹤報告生成完成 - 分析了 {len(tracking_data)} 期數據")
            return report
            
        except Exception as e:
            logger.error(f"生成追蹤報告失敗: {str(e)}")
            return {
                'success': False,
                'error': f'追蹤報告生成失敗: {str(e)}'
            }
    
    def _create_tracking_record(self, prediction_result: Dict, lottery_type: str) -> Dict:
        """創建追蹤記錄"""
        return {
            'tracking_id': f"track_{lottery_type}_{prediction_result['period']}",
            'prediction_method': prediction_result['prediction']['method'],
            'numbers_tracked': prediction_result['prediction']['numbers'],
            'special_tracked': prediction_result['prediction']['special'],
            'confidence_level': prediction_result['prediction']['confidence_score'],
            'stability_level': prediction_result['prediction']['stability_score'],
            'tracking_reason': {
                'pattern_supported': prediction_result['analysis']['selection_details']['pattern_supported'],
                'data_supported': prediction_result['analysis']['selection_details']['data_supported'],
                'methodology': prediction_result['analysis']['methodology']
            },
            'expected_observation': '等待開獎結果以評估預測準確性',
            'tracking_status': 'active'
        }
    
    def _initialize_dashboard(self, prediction_result: Dict, lottery_type: str) -> Dict:
        """初始化觀察儀表板"""
        return {
            'current_tracking': {
                'period': prediction_result['period'],
                'numbers': prediction_result['prediction']['numbers'],
                'confidence': f"{prediction_result['prediction']['confidence_score']:.1%}",
                'status': '追蹤中'
            },
            'prediction_basis': {
                'pattern_numbers': prediction_result['analysis']['selection_details']['pattern_supported'],
                'data_numbers': prediction_result['analysis']['selection_details']['data_supported'],
                'methodology': '版路分析 + 確定性算法'
            },
            'observation_focus': [
                '號碼命中率',
                '版路預測準確性',
                '穩定性表現',
                '長期趨勢'
            ]
        }
    
    def _get_latest_prediction(self, lottery_type: str) -> Optional[Dict]:
        """獲取最新預測"""
        try:
            conn = sqlite3.connect(self.db_path)
            table_name = {
                'powercolor': 'PowercolorPredictions',
                'lotto649': 'Lotto649Predictions',
                'dailycash': 'DailyCashPredictions'
            }.get(lottery_type, 'PowercolorPredictions')
            
            query = f"""
            SELECT * FROM {table_name}
            WHERE PredictionMethod = 'stable_confidence_strategy'
            ORDER BY Period DESC, PredictionDate DESC
            LIMIT 1
            """
            
            cursor = conn.execute(query)
            result = cursor.fetchone()
            conn.close()
            
            if result:
                columns = [description[0] for description in cursor.description]
                prediction_dict = dict(zip(columns, result))
                
                # 構建預測數據
                if lottery_type == 'dailycash':
                    numbers = [prediction_dict['PredA1'], prediction_dict['PredA2'], 
                              prediction_dict['PredA3'], prediction_dict['PredA4'], 
                              prediction_dict['PredA5']]
                    special = None
                else:
                    numbers = [prediction_dict['PredA1'], prediction_dict['PredA2'], 
                              prediction_dict['PredA3'], prediction_dict['PredA4'], 
                              prediction_dict['PredA5'], prediction_dict['PredA6']]
                    special = prediction_dict.get('PredS') or prediction_dict.get('PredSpecial')
                
                return {
                    'period': prediction_dict['Period'],
                    'numbers': numbers,
                    'special': special,
                    'confidence': prediction_dict.get('Confidence', 0),
                    'prediction_date': prediction_dict['PredictionDate'],
                    'status': '已預測' if not prediction_dict.get('ActualA1') else '已開獎',
                    'match_count': prediction_dict.get('MatchCount'),
                    'hit_rate': f"{prediction_dict.get('MatchCount', 0)}/{len(numbers)}" if prediction_dict.get('MatchCount') else '待開獎'
                }
            
            return None
            
        except Exception as e:
            logger.error(f"獲取最新預測失敗: {str(e)}")
            return None
    
    def _get_performance_analysis(self, lottery_type: str) -> Dict:
        """獲取表現分析"""
        try:
            # 使用已有的分析方法
            analysis = self.recorder.analyze_confidence_strategy_performance(lottery_type, 30)
            
            if analysis.get('success'):
                return {
                    'total_predictions': analysis['strategy_performance']['total_predictions'],
                    'hit_rate_3plus': analysis['strategy_performance']['hit_rate_3plus'],
                    'average_confidence': analysis['strategy_performance']['average_confidence'],
                    'estimated_roi': analysis['strategy_performance']['estimated_roi'],
                    'best_performance': analysis['best_performance'],
                    'recent_trends': analysis['recent_trends']
                }
            else:
                return {
                    'status': '數據不足',
                    'message': analysis.get('message', '需要更多開獎數據')
                }
                
        except Exception as e:
            logger.error(f"獲取表現分析失敗: {str(e)}")
            return {'error': str(e)}
    
    def _get_tracking_statistics(self, lottery_type: str) -> Dict:
        """獲取追蹤統計"""
        try:
            conn = sqlite3.connect(self.db_path)
            table_name = {
                'powercolor': 'PowercolorPredictions',
                'lotto649': 'Lotto649Predictions',
                'dailycash': 'DailyCashPredictions'
            }.get(lottery_type, 'PowercolorPredictions')
            
            # 統計穩定策略的記錄
            query = f"""
            SELECT 
                COUNT(*) as total_predictions,
                COUNT(CASE WHEN ActualA1 IS NOT NULL THEN 1 END) as completed_predictions,
                COUNT(CASE WHEN MatchCount >= 3 THEN 1 END) as successful_predictions,
                AVG(Confidence) as avg_confidence,
                MAX(MatchCount) as best_match,
                MIN(PredictionDate) as first_prediction,
                MAX(PredictionDate) as latest_prediction
            FROM {table_name}
            WHERE PredictionMethod = 'stable_confidence_strategy'
            """
            
            cursor = conn.execute(query)
            result = cursor.fetchone()
            conn.close()
            
            if result:
                total, completed, successful, avg_conf, best_match, first, latest = result
                
                return {
                    'tracking_period': f"{first} 至 {latest}" if first and latest else '無數據',
                    'total_predictions': total or 0,
                    'completed_predictions': completed or 0,
                    'pending_predictions': (total or 0) - (completed or 0),
                    'success_rate': f"{(successful or 0) / max(completed or 1, 1) * 100:.1f}%" if completed else '0%',
                    'average_confidence': f"{(avg_conf or 0):.1%}",
                    'best_match_count': best_match or 0,
                    'tracking_status': 'active' if total and total > completed else 'waiting_for_data'
                }
            
            return {'status': 'no_data'}
            
        except Exception as e:
            logger.error(f"獲取追蹤統計失敗: {str(e)}")
            return {'error': str(e)}
    
    def _get_trend_analysis(self, lottery_type: str) -> Dict:
        """獲取趨勢分析"""
        try:
            conn = sqlite3.connect(self.db_path)
            table_name = {
                'powercolor': 'PowercolorPredictions',
                'lotto649': 'Lotto649Predictions',
                'dailycash': 'DailyCashPredictions'
            }.get(lottery_type, 'PowercolorPredictions')
            
            # 獲取最近20期的命中數據
            query = f"""
            SELECT Period, MatchCount, Confidence, PredictionDate
            FROM {table_name}
            WHERE PredictionMethod = 'stable_confidence_strategy'
              AND MatchCount IS NOT NULL
            ORDER BY Period DESC
            LIMIT 20
            """
            
            cursor = conn.execute(query)
            results = cursor.fetchall()
            conn.close()
            
            if not results:
                return {'status': 'insufficient_data'}
            
            # 分析趨勢
            match_counts = [r[1] for r in results]
            confidences = [r[2] for r in results]
            
            # 計算趨勢
            if len(match_counts) >= 10:
                recent_avg = sum(match_counts[:5]) / 5
                earlier_avg = sum(match_counts[5:10]) / 5
                
                if recent_avg > earlier_avg + 0.2:
                    trend = 'improving'
                elif recent_avg < earlier_avg - 0.2:
                    trend = 'declining'
                else:
                    trend = 'stable'
            else:
                trend = 'insufficient_data'
            
            return {
                'trend_direction': trend,
                'recent_performance': match_counts[:5] if len(match_counts) >= 5 else match_counts,
                'average_hit_rate': sum(match_counts) / len(match_counts),
                'confidence_trend': sum(confidences) / len(confidences) if confidences else 0,
                'data_points': len(results),
                'trend_reliability': 'high' if len(results) >= 15 else 'medium' if len(results) >= 8 else 'low'
            }
            
        except Exception as e:
            logger.error(f"獲取趨勢分析失敗: {str(e)}")
            return {'error': str(e)}
    
    def _generate_observation_insights(self, performance: Dict, stats: Dict, trends: Dict) -> List[str]:
        """生成觀察洞察"""
        insights = []
        
        # 基於表現數據的洞察
        if 'hit_rate_3plus' in performance:
            hit_rate = float(performance['hit_rate_3plus'].rstrip('%'))
            if hit_rate > 20:
                insights.append(f"✅ 命中率表現優秀 ({performance['hit_rate_3plus']})，超越隨機預期")
            elif hit_rate > 10:
                insights.append(f"📊 命中率表現正常 ({performance['hit_rate_3plus']})，符合預期範圍")
            else:
                insights.append(f"⚠️ 命中率偏低 ({performance['hit_rate_3plus']})，需要調整策略")
        
        # 基於統計數據的洞察
        if 'success_rate' in stats and stats['success_rate'] != '0%':
            insights.append(f"📈 成功預測率: {stats['success_rate']} (基於已開獎數據)")
        
        # 基於趨勢的洞察
        if 'trend_direction' in trends:
            if trends['trend_direction'] == 'improving':
                insights.append("📈 預測表現呈上升趨勢，策略效果正在改善")
            elif trends['trend_direction'] == 'declining':
                insights.append("📉 預測表現呈下降趨勢，建議關注策略調整")
            elif trends['trend_direction'] == 'stable':
                insights.append("📊 預測表現保持穩定，策略運行正常")
        
        # 穩定性洞察
        if 'average_confidence' in stats:
            insights.append(f"🎯 平均信心度: {stats['average_confidence']}，預測穩定性良好")
        
        if not insights:
            insights.append("📊 正在收集數據中，請等待更多開獎結果以生成洞察")
        
        return insights
    
    def _generate_recommendations(self, performance: Dict, trends: Dict) -> List[str]:
        """生成建議"""
        recommendations = []
        
        # 基於表現的建議
        if 'hit_rate_3plus' in performance:
            hit_rate = float(performance['hit_rate_3plus'].rstrip('%'))
            if hit_rate > 25:
                recommendations.append("🎯 命中率優秀，建議繼續使用當前策略")
            elif hit_rate < 10:
                recommendations.append("🔄 建議結合其他分析方法提高準確性")
        
        # 基於趨勢的建議
        if trends.get('trend_direction') == 'improving':
            recommendations.append("📈 趨勢向好，建議加大投注信心")
        elif trends.get('trend_direction') == 'declining':
            recommendations.append("⚠️ 建議謹慎投注，等待趨勢改善")
        
        # 數據收集建議
        if trends.get('data_points', 0) < 10:
            recommendations.append("📊 建議繼續追蹤至少10期以獲得更可靠的分析")
        
        if not recommendations:
            recommendations.append("📋 持續觀察預測表現，定期檢視策略效果")
        
        return recommendations
    
    def _get_latest_draw_result(self, lottery_type: str) -> Optional[Dict]:
        """獲取最新開獎結果"""
        try:
            conn = sqlite3.connect(self.db_path)
            table_name = {
                'powercolor': 'Powercolor',
                'lotto649': 'Lotto649', 
                'dailycash': 'DailyCash'
            }.get(lottery_type, 'Powercolor')
            
            if lottery_type == 'powercolor':
                query = f"""
                SELECT Period, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, 
                       Second_district, Sdate
                FROM {table_name}
                ORDER BY Period DESC
                LIMIT 1
                """
            elif lottery_type == 'lotto649':
                query = f"""
                SELECT Period, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6,
                       SpecialNumber, Sdate
                FROM {table_name}
                ORDER BY Period DESC
                LIMIT 1
                """
            else:  # dailycash
                query = f"""
                SELECT Period, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Sdate
                FROM {table_name}
                ORDER BY Period DESC
                LIMIT 1
                """
            
            cursor = conn.execute(query)
            result = cursor.fetchone()
            conn.close()
            
            if result:
                if lottery_type in ['powercolor', 'lotto649']:
                    return {
                        'period': result[0],
                        'numbers': [result[1], result[2], result[3], result[4], result[5], result[6]],
                        'special': result[7] if len(result) > 7 else None,
                        'date': result[8] if len(result) > 8 else None
                    }
                else:
                    return {
                        'period': result[0],
                        'numbers': [result[1], result[2], result[3], result[4], result[5]],
                        'special': None,
                        'date': result[6] if len(result) > 6 else None
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"獲取最新開獎結果失敗: {str(e)}")
            return None
    
    def _get_predictions_for_period(self, lottery_type: str, period: int) -> List[Dict]:
        """獲取指定期號的預測記錄"""
        try:
            conn = sqlite3.connect(self.db_path)
            table_name = {
                'powercolor': 'PowercolorPredictions',
                'lotto649': 'Lotto649Predictions',
                'dailycash': 'DailyCashPredictions'
            }.get(lottery_type, 'PowercolorPredictions')
            
            query = f"""
            SELECT ID, Period, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6,
                   PredS, PredSpecial, PredictionMethod, Confidence
            FROM {table_name}
            WHERE Period = ? AND PredictionMethod = 'stable_confidence_strategy'
            """
            
            cursor = conn.execute(query, (period,))
            results = cursor.fetchall()
            conn.close()
            
            predictions = []
            for result in results:
                if lottery_type == 'dailycash':
                    numbers = [result[2], result[3], result[4], result[5], result[6]]
                    special = None
                else:
                    numbers = [result[2], result[3], result[4], result[5], result[6], result[7]]
                    special = result[8] or result[9]  # PredS or PredSpecial
                
                predictions.append({
                    'id': result[0],
                    'period': result[1],
                    'numbers': numbers,
                    'special': special,
                    'method': result[10],
                    'confidence': result[11]
                })
            
            return predictions
            
        except Exception as e:
            logger.error(f"獲取期號預測記錄失敗: {str(e)}")
            return []
    
    def _calculate_hit_analysis(self, prediction: Dict, draw_result: Dict) -> Dict:
        """計算命中分析"""
        predicted_numbers = set(prediction['numbers'])
        actual_numbers = set(draw_result['numbers'])
        
        main_matches = len(predicted_numbers & actual_numbers)
        special_match = (prediction['special'] == draw_result['special']) if prediction['special'] and draw_result['special'] else False
        
        return {
            'main_matches': main_matches,
            'special_match': special_match,
            'matched_numbers': list(predicted_numbers & actual_numbers),
            'missed_numbers': list(predicted_numbers - actual_numbers),
            'unexpected_numbers': list(actual_numbers - predicted_numbers),
            'hit_rate': f"{main_matches}/{len(prediction['numbers'])}",
            'success_level': self._classify_success_level(main_matches, special_match)
        }
    
    def _classify_success_level(self, main_matches: int, special_match: bool) -> str:
        """分類成功等級"""
        if main_matches >= 5:
            return 'excellent'
        elif main_matches >= 3:
            return 'good'
        elif main_matches >= 2:
            return 'fair'
        else:
            return 'poor'
    
    def _update_prediction_hit_result(self, lottery_type: str, prediction_id: int, 
                                     hit_analysis: Dict, draw_result: Dict) -> bool:
        """更新預測命中結果"""
        try:
            conn = sqlite3.connect(self.db_path)
            table_name = {
                'powercolor': 'PowercolorPredictions',
                'lotto649': 'Lotto649Predictions',
                'dailycash': 'DailyCashPredictions'
            }.get(lottery_type, 'PowercolorPredictions')
            
            # 準備更新數據
            update_data = {
                'MatchCount': hit_analysis['main_matches'],
                'LastUpdated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 添加實際開獎號碼
            if lottery_type == 'dailycash':
                update_data.update({
                    'ActualA1': draw_result['numbers'][0],
                    'ActualA2': draw_result['numbers'][1],
                    'ActualA3': draw_result['numbers'][2],
                    'ActualA4': draw_result['numbers'][3],
                    'ActualA5': draw_result['numbers'][4]
                })
            else:
                update_data.update({
                    'ActualA1': draw_result['numbers'][0],
                    'ActualA2': draw_result['numbers'][1],
                    'ActualA3': draw_result['numbers'][2],
                    'ActualA4': draw_result['numbers'][3],
                    'ActualA5': draw_result['numbers'][4],
                    'ActualA6': draw_result['numbers'][5]
                })
                
                if lottery_type == 'powercolor':
                    update_data['ActualS'] = draw_result['special']
                    update_data['SecondMatch'] = 1 if hit_analysis['special_match'] else 0
                elif lottery_type == 'lotto649':
                    update_data['ActualSpecial'] = draw_result['special']
                    update_data['SpecialMatch'] = 1 if hit_analysis['special_match'] else 0
            
            # 執行更新
            columns = ', '.join([f"{k} = ?" for k in update_data.keys()])
            values = list(update_data.values()) + [prediction_id]
            
            query = f"UPDATE {table_name} SET {columns} WHERE ID = ?"
            conn.execute(query, values)
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            logger.error(f"更新預測命中結果失敗: {str(e)}")
            return False
    
    def _generate_update_summary(self, results: List[Dict]) -> Dict:
        """生成更新摘要"""
        if not results:
            return {'message': '無預測記錄更新'}
        
        total_updated = len(results)
        hit_counts = [r['hit_analysis']['main_matches'] for r in results]
        avg_hits = sum(hit_counts) / len(hit_counts)
        
        best_result = max(results, key=lambda x: x['hit_analysis']['main_matches'])
        
        return {
            'total_updated': total_updated,
            'average_hits': f"{avg_hits:.1f}",
            'best_performance': {
                'method': best_result['method'],
                'hits': best_result['hit_analysis']['main_matches'],
                'matched_numbers': best_result['hit_analysis']['matched_numbers']
            },
            'success_distribution': Counter([r['hit_analysis']['success_level'] for r in results])
        }


def demonstrate_observable_tracker():
    """演示可觀察預測追蹤器"""
    
    tracker = ObservablePredictionTracker()
    
    print("=" * 100)
    print("可觀察預測追蹤系統演示")
    print("=" * 100)
    
    # 1. 開始追蹤會話
    print("\n🚀 啟動追蹤會話...")
    session_result = tracker.start_tracking_session('powercolor')
    
    if session_result['success']:
        print("✅ 追蹤會話啟動成功")
        session = session_result['tracking_session']
        prediction = session_result['prediction_summary']
        
        print(f"會話ID: {session['session_id']}")
        print(f"預測期號: {session['period']}")
        print(f"預測號碼: {prediction['numbers']}")
        print(f"特別號: {prediction['special']}")
        print(f"信心度: {prediction['confidence']:.1%}")
        print(f"穩定性: {prediction['stability']:.1%}")
        
        # 2. 觀察儀表板
        print("\n📊 觀察儀表板...")
        dashboard = tracker.get_observation_dashboard('powercolor')
        
        if 'current_prediction' in dashboard:
            current = dashboard['current_prediction']
            print(f"當前追蹤: 期號 {current['period']} - {current['numbers']}")
            print(f"狀態: {current['status']} | 命中: {current['hit_rate']}")
        
        # 顯示觀察洞察
        if 'observation_insights' in dashboard:
            print("\n💡 觀察洞察:")
            for insight in dashboard['observation_insights']:
                print(f"  {insight}")
        
        # 顯示建議
        if 'actionable_recommendations' in dashboard:
            print("\n📋 建議:")
            for rec in dashboard['actionable_recommendations']:
                print(f"  {rec}")
        
        # 3. 檢查更新
        print("\n🔄 檢查預測結果更新...")
        update_result = tracker.update_prediction_results('powercolor')
        
        if update_result['success']:
            if update_result.get('updated_predictions', 0) > 0:
                print(f"✅ 更新了 {update_result['updated_predictions']} 條預測記錄")
                if 'hit_results' in update_result:
                    for result in update_result['hit_results'][:3]:  # 顯示前3個
                        print(f"  期號 {result['period']}: 命中 {result['hit_analysis']['hit_rate']}")
            else:
                print("📊 暫無新開獎結果需要更新")
        
    else:
        print(f"❌ 追蹤會話啟動失敗: {session_result.get('error', 'Unknown error')}")


if __name__ == "__main__":
    demonstrate_observable_tracker()