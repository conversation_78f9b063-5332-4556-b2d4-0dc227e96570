#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
台灣彩券官方網站真實數據爬蟲 (Real Taiwan Lottery Web Scraper)
直接從 https://www.taiwanlottery.com 爬取最新開獎數據

核心功能：
1. 真實網站結構解析 (Real Website Structure Parsing)
2. 智能數據檢查 (Intelligent Data Checking)
3. 自動更新機制 (Auto Update Mechanism)
4. 數據完整性驗證 (Data Integrity Verification)
5. 多彩票類型支持 (Multi-Lottery Support)
"""

import requests
import time
import re
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from bs4 import BeautifulSoup
import urllib3
from urllib.parse import urljoin

# 導入我們的數據完整性框架
try:
    from data_integrity_framework import DataIntegrityAPI, get_integrity_manager
    from real_data_integration import get_real_data_manager
    DATA_INTEGRITY_AVAILABLE = True
except ImportError:
    DATA_INTEGRITY_AVAILABLE = False

# 抑制SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 設置專門的爬蟲日誌
scraper_logger = logging.getLogger('real_web_scraper')
handler = logging.FileHandler('data/web_scraper.log', encoding='utf-8')
handler.setFormatter(logging.Formatter(
    '%(asctime)s - WEB_SCRAPER - %(levelname)s - %(message)s'
))
scraper_logger.addHandler(handler)
scraper_logger.setLevel(logging.INFO)

@dataclass
class LotteryResult:
    """開獎結果數據結構"""
    lottery_type: str           # 彩票類型
    lottery_name: str           # 彩票名稱
    period: int                 # 期號
    draw_date: str             # 開獎日期
    main_numbers: List[int]    # 主號碼
    special_number: Optional[int] = None  # 特別號
    additional_info: Optional[Dict] = None
    scraped_at: Optional[str] = None  # 爬取時間

class RealWebScraper:
    """真實台灣彩券官方網站爬蟲"""
    
    def __init__(self):
        # 官方網站URL
        self.base_url = "https://www.taiwanlottery.com"
        self.latest_results_url = "https://www.taiwanlottery.com/lotto/lotto_lastest_result"
        
        # HTTP會話設置
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # 彩票類型映射
        self.lottery_mapping = {
            'powercolor': {
                'name': '威力彩',
                'main_count': 6,
                'main_range': (1, 38),
                'special_range': (1, 8),
                'has_special': True
            },
            'lotto649': {
                'name': '大樂透',
                'main_count': 6,
                'main_range': (1, 49),
                'special_range': (1, 49),
                'has_special': True
            },
            'dailycash': {
                'name': '今彩539',
                'main_count': 5,
                'main_range': (1, 39),
                'has_special': False
            }
        }
        
        # 數據管理器
        if DATA_INTEGRITY_AVAILABLE:
            self.integrity_api = DataIntegrityAPI()
            self.data_manager = get_real_data_manager()
        
        self.request_delay = 2  # 請求間隔
        scraper_logger.info("真實網站爬蟲初始化完成")
    
    def make_request(self, url: str, retries: int = 3) -> Optional[BeautifulSoup]:
        """安全地發送請求並解析HTML"""
        for attempt in range(retries):
            try:
                time.sleep(self.request_delay)  # 避免請求過於頻繁
                
                scraper_logger.info(f"正在請求: {url}")
                response = self.session.get(url, timeout=30, verify=False)
                response.raise_for_status()
                
                # 檢查是否為HTML內容
                if 'text/html' not in response.headers.get('content-type', ''):
                    scraper_logger.warning(f"響應不是HTML內容: {response.headers.get('content-type')}")
                    continue
                
                # 解析HTML
                soup = BeautifulSoup(response.text, 'html.parser')
                scraper_logger.info(f"成功獲取並解析頁面: {url}")
                return soup
                
            except requests.exceptions.RequestException as e:
                scraper_logger.error(f"請求失敗 (嘗試 {attempt + 1}/{retries}): {str(e)}")
                if attempt < retries - 1:
                    time.sleep(5 * (attempt + 1))  # 指數退避
                
        scraper_logger.error(f"所有請求嘗試都失敗: {url}")
        return None
    
    def check_for_new_data(self) -> Dict[str, bool]:
        """檢查是否有新的開獎數據"""
        scraper_logger.info("開始檢查是否有新的開獎數據")
        
        new_data_status = {
            'powercolor': False,
            'lotto649': False,
            'dailycash': False
        }
        
        # 獲取當前數據庫中的最新期號
        if DATA_INTEGRITY_AVAILABLE:
            current_periods = self._get_current_latest_periods()
        else:
            current_periods = {}
        
        # 爬取官方網站最新數據
        latest_results = self.scrape_latest_results()
        
        # 比較期號
        for lottery_type, result in latest_results.items():
            if result and lottery_type in current_periods:
                current_period = current_periods.get(lottery_type, 0)
                if result.period > current_period:
                    new_data_status[lottery_type] = True
                    scraper_logger.info(f"{result.lottery_name}發現新數據: 期號 {result.period} > {current_period}")
                else:
                    scraper_logger.info(f"{result.lottery_name}無新數據: 期號 {result.period} <= {current_period}")
        
        return new_data_status
    
    def _get_current_latest_periods(self) -> Dict[str, int]:
        """獲取數據庫中最新的期號"""
        periods = {}
        
        try:
            import sqlite3
            conn = sqlite3.connect('data/lottery_data.db')
            
            table_mapping = {
                'powercolor': 'Powercolor',
                'lotto649': 'Lotto649', 
                'dailycash': 'DailyCash'
            }
            
            for lottery_type, table_name in table_mapping.items():
                try:
                    cursor = conn.execute(f'SELECT MAX(Period) FROM {table_name}')
                    max_period = cursor.fetchone()[0]
                    periods[lottery_type] = max_period if max_period else 0
                except:
                    periods[lottery_type] = 0
            
            conn.close()
            
        except Exception as e:
            scraper_logger.error(f"獲取最新期號失敗: {str(e)}")
            
        return periods
    
    def scrape_latest_results(self) -> Dict[str, Optional[LotteryResult]]:
        """爬取官方網站最新開獎結果"""
        scraper_logger.info("開始爬取官方網站最新開獎結果")
        
        results = {
            'powercolor': None,
            'lotto649': None,
            'dailycash': None
        }
        
        # 爬取最新開獎結果頁面
        soup = self.make_request(self.latest_results_url)
        if not soup:
            scraper_logger.error("無法獲取最新開獎結果頁面")
            return results
        
        # 解析各彩票類型的最新結果
        results['powercolor'] = self._parse_powercolor_result(soup)
        results['lotto649'] = self._parse_lotto649_result(soup)  
        results['dailycash'] = self._parse_dailycash_result(soup)
        
        return results
    
    def _parse_powercolor_result(self, soup: BeautifulSoup) -> Optional[LotteryResult]:
        """解析威力彩開獎結果"""
        try:
            scraper_logger.info("開始解析威力彩開獎結果")
            
            # 在頁面中尋找威力彩相關內容
            # 根據官方網站的實際HTML結構進行解析
            powercolor_section = soup.find('div', class_=re.compile(r'.*powercolor.*|.*威力彩.*', re.I))
            if not powercolor_section:
                # 嘗試通過文字內容查找
                powercolor_section = soup.find(text=re.compile(r'威力彩|powercolor', re.I))
                if powercolor_section:
                    powercolor_section = powercolor_section.parent
            
            if not powercolor_section:
                scraper_logger.warning("未找到威力彩相關區域")
                return None
            
            # 解析期號
            period_match = re.search(r'第?(\d{9})期', str(powercolor_section))
            if not period_match:
                scraper_logger.warning("威力彩期號解析失敗")
                return None
            
            period = int(period_match.group(1))
            
            # 解析號碼 - 需要根據實際HTML結構調整
            number_elements = powercolor_section.find_all(['span', 'div'], class_=re.compile(r'number|ball|num', re.I))
            
            numbers = []
            special_number = None
            
            # 提取數字
            for element in number_elements:
                text = element.get_text().strip()
                if text.isdigit():
                    num = int(text)
                    if 1 <= num <= 38:  # 威力彩主號碼範圍
                        numbers.append(num)
                    elif 1 <= num <= 8:  # 威力彩第二區號碼範圍
                        special_number = num
            
            # 如果沒有找到號碼，嘗試從文本中提取
            if not numbers:
                text_content = powercolor_section.get_text()
                number_matches = re.findall(r'\b(\d{1,2})\b', text_content)
                for match in number_matches:
                    num = int(match)
                    if 1 <= num <= 38 and len(numbers) < 6:
                        numbers.append(num)
                    elif 1 <= num <= 8 and special_number is None:
                        special_number = num
            
            if len(numbers) != 6:
                scraper_logger.warning(f"威力彩號碼數量不正確: {len(numbers)}")
                return None
            
            # 解析開獎日期
            date_match = re.search(r'(\d{4})[/-]?(\d{1,2})[/-]?(\d{1,2})', str(powercolor_section))
            draw_date = datetime.now().strftime('%Y-%m-%d')
            if date_match:
                year, month, day = date_match.groups()
                draw_date = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            
            result = LotteryResult(
                lottery_type='powercolor',
                lottery_name='威力彩',
                period=period,
                draw_date=draw_date,
                main_numbers=sorted(numbers),
                special_number=special_number,
                scraped_at=datetime.now().isoformat()
            )
            
            scraper_logger.info(f"威力彩解析成功: 第{period}期 {numbers} + {special_number}")
            return result
            
        except Exception as e:
            scraper_logger.error(f"威力彩解析失敗: {str(e)}")
            return None
    
    def _parse_lotto649_result(self, soup: BeautifulSoup) -> Optional[LotteryResult]:
        """解析大樂透開獎結果"""
        try:
            scraper_logger.info("開始解析大樂透開獎結果")
            
            # 尋找大樂透相關內容
            lotto_section = soup.find('div', class_=re.compile(r'.*lotto.*|.*大樂透.*', re.I))
            if not lotto_section:
                lotto_section = soup.find(text=re.compile(r'大樂透|lotto', re.I))
                if lotto_section:
                    lotto_section = lotto_section.parent
            
            if not lotto_section:
                scraper_logger.warning("未找到大樂透相關區域")
                return None
            
            # 解析期號
            period_match = re.search(r'第?(\d{9})期', str(lotto_section))
            if not period_match:
                scraper_logger.warning("大樂透期號解析失敗")
                return None
            
            period = int(period_match.group(1))
            
            # 解析號碼
            number_elements = lotto_section.find_all(['span', 'div'], class_=re.compile(r'number|ball|num', re.I))
            
            numbers = []
            special_number = None
            
            for element in number_elements:
                text = element.get_text().strip()
                if text.isdigit():
                    num = int(text)
                    if 1 <= num <= 49:
                        if len(numbers) < 6:
                            numbers.append(num)
                        elif special_number is None:
                            special_number = num
            
            # 如果沒有找到號碼，從文本中提取
            if not numbers:
                text_content = lotto_section.get_text()
                number_matches = re.findall(r'\b(\d{1,2})\b', text_content)
                for match in number_matches:
                    num = int(match)
                    if 1 <= num <= 49:
                        if len(numbers) < 6:
                            numbers.append(num)
                        elif special_number is None:
                            special_number = num
            
            if len(numbers) != 6:
                scraper_logger.warning(f"大樂透號碼數量不正確: {len(numbers)}")
                return None
            
            # 解析日期
            date_match = re.search(r'(\d{4})[/-]?(\d{1,2})[/-]?(\d{1,2})', str(lotto_section))
            draw_date = datetime.now().strftime('%Y-%m-%d')
            if date_match:
                year, month, day = date_match.groups()
                draw_date = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            
            result = LotteryResult(
                lottery_type='lotto649',
                lottery_name='大樂透',
                period=period,
                draw_date=draw_date,
                main_numbers=sorted(numbers),
                special_number=special_number,
                scraped_at=datetime.now().isoformat()
            )
            
            scraper_logger.info(f"大樂透解析成功: 第{period}期 {numbers} + {special_number}")
            return result
            
        except Exception as e:
            scraper_logger.error(f"大樂透解析失敗: {str(e)}")
            return None
    
    def _parse_dailycash_result(self, soup: BeautifulSoup) -> Optional[LotteryResult]:
        """解析今彩539開獎結果"""
        try:
            scraper_logger.info("開始解析今彩539開獎結果")
            
            # 尋找今彩539相關內容
            dailycash_section = soup.find('div', class_=re.compile(r'.*dailycash.*|.*今彩539.*', re.I))
            if not dailycash_section:
                dailycash_section = soup.find(text=re.compile(r'今彩539|dailycash', re.I))
                if dailycash_section:
                    dailycash_section = dailycash_section.parent
            
            if not dailycash_section:
                scraper_logger.warning("未找到今彩539相關區域")
                return None
            
            # 解析期號
            period_match = re.search(r'第?(\d{9})期', str(dailycash_section))
            if not period_match:
                scraper_logger.warning("今彩539期號解析失敗")
                return None
            
            period = int(period_match.group(1))
            
            # 解析號碼
            number_elements = dailycash_section.find_all(['span', 'div'], class_=re.compile(r'number|ball|num', re.I))
            
            numbers = []
            
            for element in number_elements:
                text = element.get_text().strip()
                if text.isdigit():
                    num = int(text)
                    if 1 <= num <= 39 and len(numbers) < 5:
                        numbers.append(num)
            
            # 如果沒有找到號碼，從文本中提取
            if not numbers:
                text_content = dailycash_section.get_text()
                number_matches = re.findall(r'\b(\d{1,2})\b', text_content)
                for match in number_matches:
                    num = int(match)
                    if 1 <= num <= 39 and len(numbers) < 5:
                        numbers.append(num)
            
            if len(numbers) != 5:
                scraper_logger.warning(f"今彩539號碼數量不正確: {len(numbers)}")
                return None
            
            # 解析日期
            date_match = re.search(r'(\d{4})[/-]?(\d{1,2})[/-]?(\d{1,2})', str(dailycash_section))
            draw_date = datetime.now().strftime('%Y-%m-%d')
            if date_match:
                year, month, day = date_match.groups()
                draw_date = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            
            result = LotteryResult(
                lottery_type='dailycash',
                lottery_name='今彩539',
                period=period,
                draw_date=draw_date,
                main_numbers=sorted(numbers),
                special_number=None,
                scraped_at=datetime.now().isoformat()
            )
            
            scraper_logger.info(f"今彩539解析成功: 第{period}期 {numbers}")
            return result
            
        except Exception as e:
            scraper_logger.error(f"今彩539解析失敗: {str(e)}")
            return None
    
    def sync_new_data(self) -> Dict[str, Any]:
        """同步新數據到數據庫"""
        sync_report = {
            'timestamp': datetime.now().isoformat(),
            'total_synced': 0,
            'sync_results': {},
            'errors': []
        }
        
        if not DATA_INTEGRITY_AVAILABLE:
            error_msg = "數據完整性框架不可用"
            sync_report['errors'].append(error_msg)
            scraper_logger.error(error_msg)
            return sync_report
        
        # 檢查新數據
        new_data_status = self.check_for_new_data()
        
        if not any(new_data_status.values()):
            scraper_logger.info("沒有發現新的開獎數據")
            return sync_report
        
        # 爬取並同步新數據
        latest_results = self.scrape_latest_results()
        
        for lottery_type, has_new_data in new_data_status.items():
            if has_new_data and latest_results[lottery_type]:
                result = latest_results[lottery_type]
                
                try:
                    success, message = self.data_manager.insert_lottery_result(
                        lottery_type=result.lottery_type,
                        period=result.period,
                        main_numbers=result.main_numbers,
                        special_number=result.special_number,
                        date=result.draw_date,
                        source_info=f"taiwan_lottery_official_scraped_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    )
                    
                    sync_report['sync_results'][lottery_type] = {
                        'success': success,
                        'message': message,
                        'period': result.period,
                        'numbers': result.main_numbers,
                        'special': result.special_number
                    }
                    
                    if success:
                        sync_report['total_synced'] += 1
                        scraper_logger.info(f"成功同步{result.lottery_name}第{result.period}期數據")
                    else:
                        sync_report['errors'].append(f"{result.lottery_name}: {message}")
                        scraper_logger.error(f"{result.lottery_name}同步失敗: {message}")
                        
                except Exception as e:
                    error_msg = f"{result.lottery_name}同步異常: {str(e)}"
                    sync_report['errors'].append(error_msg)
                    scraper_logger.error(error_msg)
        
        return sync_report
    
    def auto_sync_with_check(self) -> Dict[str, Any]:
        """自動檢查並同步新數據（二小時一次執行）"""
        scraper_logger.info("開始執行自動檢查並同步")
        
        sync_report = {
            'start_time': datetime.now().isoformat(),
            'check_results': {},
            'sync_performed': False,
            'sync_results': {},
            'end_time': None
        }
        
        try:
            # 檢查是否有新數據
            new_data_status = self.check_for_new_data()
            sync_report['check_results'] = new_data_status
            
            # 如果有新數據，執行同步
            if any(new_data_status.values()):
                scraper_logger.info("發現新數據，開始同步")
                sync_report['sync_performed'] = True
                sync_results = self.sync_new_data()
                sync_report['sync_results'] = sync_results
            else:
                scraper_logger.info("沒有新數據，跳過同步")
                
        except Exception as e:
            error_msg = f"自動同步過程發生錯誤: {str(e)}"
            sync_report['error'] = error_msg
            scraper_logger.error(error_msg)
        
        sync_report['end_time'] = datetime.now().isoformat()
        scraper_logger.info("自動檢查並同步完成")
        
        return sync_report

# 全局爬蟲實例
_real_scraper = None

def get_real_scraper() -> RealWebScraper:
    """獲取全局真實網站爬蟲實例"""
    global _real_scraper
    if _real_scraper is None:
        _real_scraper = RealWebScraper()
    return _real_scraper

# 便利函數
def check_for_new_lottery_data() -> Dict[str, bool]:
    """檢查是否有新的彩票數據"""
    scraper = get_real_scraper()
    return scraper.check_for_new_data()

def auto_sync_lottery_data() -> Dict[str, Any]:
    """自動同步彩票數據"""
    scraper = get_real_scraper()
    return scraper.auto_sync_with_check()

def scrape_latest_lottery_results() -> Dict[str, Optional[LotteryResult]]:
    """爬取最新彩票開獎結果"""
    scraper = get_real_scraper()
    return scraper.scrape_latest_results()

if __name__ == "__main__":
    # 測試真實網站爬蟲
    print("="*80)
    print("台灣彩券官方網站真實數據爬蟲 測試")
    print("="*80)
    
    scraper = get_real_scraper()
    
    # 測試檢查新數據
    print("\n檢查是否有新數據...")
    new_data_status = scraper.check_for_new_data()
    for lottery_type, has_new in new_data_status.items():
        lottery_name = scraper.lottery_mapping[lottery_type]['name']
        status = "有新數據" if has_new else "無新數據"
        print(f"  {lottery_name}: {status}")
    
    # 測試爬取最新結果
    print("\n爬取最新開獎結果...")
    latest_results = scraper.scrape_latest_results()
    for lottery_type, result in latest_results.items():
        if result:
            special_str = f" + {result.special_number}" if result.special_number else ""
            print(f"  {result.lottery_name} 第{result.period}期: {result.main_numbers}{special_str} ({result.draw_date})")
        else:
            lottery_name = scraper.lottery_mapping[lottery_type]['name']
            print(f"  {lottery_name}: 解析失敗")
    
    # 測試自動同步
    print("\n執行自動同步...")
    sync_report = scraper.auto_sync_with_check()
    print(f"  檢查結果: {sync_report['check_results']}")
    print(f"  是否執行同步: {sync_report['sync_performed']}")
    if sync_report['sync_performed']:
        sync_results = sync_report.get('sync_results', {})
        print(f"  同步結果: 成功 {sync_results.get('total_synced', 0)} 筆")
    
    print("\n真實網站爬蟲測試完成")