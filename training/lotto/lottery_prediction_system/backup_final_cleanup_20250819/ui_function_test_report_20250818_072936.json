{"test_time": "2025-08-18T07:29:36.619401", "base_url": "http://localhost:7890", "total_tests": 41, "successful_tests": 39, "success_rate": 95.1219512195122, "results": [{"path": "/", "description": "主页", "status_code": 200, "success": true, "response_time": 0.009604, "content_length": 20325, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": true, "has_error": true}, {"path": "/predictions", "description": "预测记录页面", "status_code": 200, "success": true, "response_time": 0.01096, "content_length": 11908, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": false, "has_error": false}, {"path": "/results", "description": "开奖结果页面", "status_code": 200, "success": true, "response_time": 0.005055, "content_length": 30679, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": true, "has_error": true}, {"path": "/analysis", "description": "分析报告页面", "status_code": 200, "success": true, "response_time": 0.005025, "content_length": 10530, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": false, "has_error": false}, {"path": "/dashboard", "description": "仪表板页面", "status_code": 200, "success": true, "response_time": 0.005055, "content_length": 20007, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": true, "has_error": true}, {"path": "/comprehensive_analysis", "description": "综合分析页面", "status_code": 200, "success": true, "response_time": 0.00318, "content_length": 22386, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": true, "has_error": true}, {"path": "/number_analysis", "description": "号码分析页面", "status_code": 200, "success": true, "response_time": 0.003534, "content_length": 27427, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": true, "has_error": true}, {"path": "/separated_prediction", "description": "分离式预测页面", "status_code": 200, "success": true, "response_time": 0.003664, "content_length": 29308, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": true, "has_error": true}, {"path": "/phase3_dashboard", "description": "Phase 3综合仪表板", "status_code": 200, "success": true, "response_time": 0.005792, "content_length": 17571, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": true, "has_error": true}, {"path": "/universal_prediction", "description": "通用预测界面", "status_code": 200, "success": true, "response_time": 0.003596, "content_length": 20583, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": true, "has_error": true}, {"path": "/tracking_analytics", "description": "追踪分析报表", "status_code": 200, "success": true, "response_time": 0.003295, "content_length": 18814, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": true, "has_error": false}, {"path": "/prediction_management", "description": "预测管理页面", "status_code": 200, "success": true, "response_time": 0.003693, "content_length": 30699, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": true, "has_error": true}, {"path": "/periods_management", "description": "期号管理页面", "status_code": 200, "success": true, "response_time": 0.003724, "content_length": 26558, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": true, "has_error": true}, {"path": "/backtest_analysis", "description": "回测分析页面", "status_code": 200, "success": true, "response_time": 0.006258, "content_length": 57511, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": true, "has_error": true}, {"path": "/prediction_method_analysis", "description": "预测方法分析", "status_code": 200, "success": true, "response_time": 0.003167, "content_length": 22573, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": true, "has_error": true}, {"path": "/prediction_performance_dashboard", "description": "预测性能仪表板", "status_code": 200, "success": true, "response_time": 0.003369, "content_length": 26196, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": true, "has_error": true}, {"path": "/enhanced_index", "description": "增强主页", "status_code": 404, "success": false, "response_time": 0.001202, "content_length": 113, "has_html": false}, {"path": "/enhanced_prediction", "description": "增强预测页面", "status_code": 200, "success": true, "response_time": 0.004483, "content_length": 38139, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": true, "has_error": true}, {"path": "/enhanced_analysis", "description": "增强分析页面", "status_code": 200, "success": true, "response_time": 0.004579, "content_length": 28474, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": true, "has_error": true}, {"path": "/enhanced_history", "description": "增强历史页面", "status_code": 200, "success": true, "response_time": 0.00459, "content_length": 33484, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": true, "has_error": true}, {"path": "/enhanced_performance", "description": "增强性能页面", "status_code": 200, "success": true, "response_time": 0.003993, "content_length": 31250, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": true, "has_error": true}, {"path": "/enhanced_predict", "description": "增强预测功能", "status_code": 404, "success": false, "response_time": 0.000998, "content_length": 113, "has_html": false}, {"path": "/simple_backtest_viewer", "description": "简单回测查看器", "status_code": 200, "success": true, "response_time": 0.002719, "content_length": 17213, "has_html": true, "has_title": true, "has_bootstrap": true, "has_jquery": true, "has_error": true}, {"path": "/api/system_status", "method": "GET", "description": "系统状态", "status_code": 200, "success": true, "response_time": 0.000984, "is_json": true, "api_success": true, "has_data": true, "has_error": false}, {"path": "/api/latest_predictions/powercolor", "method": "GET", "description": "威力彩最新预测", "status_code": 200, "success": true, "response_time": 0.002838, "is_json": true, "api_success": true, "has_data": true, "has_error": false}, {"path": "/api/latest_predictions/lotto649", "method": "GET", "description": "大乐透最新预测", "status_code": 200, "success": true, "response_time": 0.002567, "is_json": true, "api_success": true, "has_data": true, "has_error": false}, {"path": "/api/latest_predictions/dailycash", "method": "GET", "description": "今彩539最新预测", "status_code": 200, "success": true, "response_time": 0.002546, "is_json": true, "api_success": true, "has_data": true, "has_error": false}, {"path": "/api/accuracy/powercolor", "method": "GET", "description": "威力彩准确度", "status_code": 200, "success": true, "response_time": 0.001332, "is_json": true, "api_success": true, "has_data": true, "has_error": false}, {"path": "/api/accuracy/lotto649", "method": "GET", "description": "大乐透准确度", "status_code": 200, "success": true, "response_time": 0.001312, "is_json": true, "api_success": true, "has_data": true, "has_error": false}, {"path": "/api/accuracy/dailycash", "method": "GET", "description": "今彩539准确度", "status_code": 200, "success": true, "response_time": 0.000992, "is_json": true, "api_success": true, "has_data": true, "has_error": false}, {"path": "/api/comprehensive_analysis/powercolor", "method": "GET", "description": "威力彩综合分析", "status_code": 200, "success": true, "response_time": 0.001229, "is_json": true, "api_success": true, "has_data": true, "has_error": false}, {"path": "/api/comprehensive_analysis/lotto649", "method": "GET", "description": "大乐透综合分析", "status_code": 200, "success": true, "response_time": 0.001407, "is_json": true, "api_success": true, "has_data": true, "has_error": false}, {"path": "/api/comprehensive_analysis/dailycash", "method": "GET", "description": "今彩539综合分析", "status_code": 200, "success": true, "response_time": 0.011102, "is_json": true, "api_success": true, "has_data": true, "has_error": false}, {"path": "/api/results?lottery_type=powercolor&limit=5", "method": "GET", "description": "威力彩开奖结果", "status_code": 200, "success": true, "response_time": 0.089906, "is_json": true, "api_success": true, "has_data": true, "has_error": false}, {"path": "/api/results?lottery_type=lotto649&limit=5", "method": "GET", "description": "大乐透开奖结果", "status_code": 200, "success": true, "response_time": 0.048677, "is_json": true, "api_success": true, "has_data": true, "has_error": false}, {"path": "/api/results?lottery_type=dailycash&limit=5", "method": "GET", "description": "今彩539开奖结果", "status_code": 200, "success": true, "response_time": 0.108835, "is_json": true, "api_success": true, "has_data": true, "has_error": false}, {"path": "/api/predict", "method": "POST", "description": "生成威力彩预测", "status_code": 200, "success": true, "response_time": 0.001277, "is_json": true, "api_success": true, "has_data": true, "has_error": false}, {"path": "/api/predict", "method": "POST", "description": "生成大乐透预测", "status_code": 200, "success": true, "response_time": 0.001031, "is_json": true, "api_success": true, "has_data": true, "has_error": false}, {"path": "/api/predict", "method": "POST", "description": "生成今彩539预测", "status_code": 200, "success": true, "response_time": 0.000985, "is_json": true, "api_success": true, "has_data": true, "has_error": false}, {"path": "/nonexistent", "description": "不存在的页面", "status_code": 404, "success": true, "response_time": 0.00087, "content_length": 113, "has_html": false}, {"path": "/api/nonexistent", "description": "不存在的API", "status_code": 404, "success": true, "response_time": 0.000847, "content_length": 113, "has_html": false}]}