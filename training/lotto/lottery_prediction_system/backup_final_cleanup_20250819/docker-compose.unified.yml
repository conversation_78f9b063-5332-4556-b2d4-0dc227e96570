# 彩票預測系統 - 單一容器統一部署
# 簡化版本，只使用一個主容器

version: '3.8'

services:
  # 主應用服務 - All-in-One
  lottery-system:
    build:
      context: .
      dockerfile: Dockerfile.unified
    container_name: lottery-prediction-unified
    restart: unless-stopped
    ports:
      # Web界面端口
      - "7890:7890"
      # API服務端口  
      - "8000:8000"
      # Nginx代理端口
      - "80:80"
    volumes:
      # 數據持久化
      - ./data:/app/data
      - ./logs:/app/logs
      - ./backups:/app/backups
      - ./reports:/app/reports
      - ./models:/app/models
      - ./analysis_results:/app/analysis_results
    environment:
      # 從環境文件載入配置
      - SYSTEM_MODE=production
      - FLASK_PORT=7890
      - API_PORT=8000
      - REDIS_PORT=6379
      - DB_PATH=/app/data/lottery.db
      - LOG_LEVEL=INFO
      - AUTO_UPDATE=true
      - TIMEZONE=Asia/Taipei
      # 業務配置
      - ML_WEIGHT=0.4
      - BOARD_PATH_WEIGHT=0.3
      - STATISTICAL_WEIGHT=0.2
      - PATTERN_WEIGHT=0.1
      # 安全配置
      - SECRET_KEY=lottery_prediction_system_2025_unified
      - CORS_ORIGINS=*
    env_file:
      - .env.unified
    healthcheck:
      test: ["/healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - lottery-net
    # 資源限制
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G

networks:
  lottery-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 可選：外部數據卷
volumes:
  lottery_data:
    driver: local
  lottery_logs:
    driver: local
  lottery_models:
    driver: local