# 彩票預測系統 - 基礎版本快速部署

services:
  # 基礎應用服務
  lottery-system-basic:
    image: lottery-prediction:basic
    container_name: lottery-prediction-basic
    restart: unless-stopped
    ports:
      # Web界面端口
      - "7890:7890"
      # API服務端口  
      - "8000:8000"
    volumes:
      # 數據持久化
      - ./data:/app/data
      - ./logs:/app/logs
      - ./backups:/app/backups
      - ./reports:/app/reports
      - ./models:/app/models
    environment:
      # 基礎配置
      - SYSTEM_MODE=development
      - FLASK_PORT=7890
      - API_PORT=8000
      - REDIS_PORT=6379
      - DB_PATH=/app/data/lottery.db
      - LOG_LEVEL=INFO
      - TIMEZONE=Asia/Taipei
    healthcheck:
      test: ["CMD", "/healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - lottery-net

networks:
  lottery-net:
    driver: bridge