# Prometheus 配置文件 - 彩票預測系統監控

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'lottery-prediction-system'

# 規則文件
rule_files:
  # - "alert_rules.yml"

# 警報管理器配置
# alerting:
#   alertmanagers:
#     - static_configs:
#         - targets:
#           - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus 自身監控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: '/metrics'

  # 彩票預測系統 API 監控
  - job_name: 'lottery-api'
    static_configs:
      - targets: ['lottery-api:8000']
    scrape_interval: 15s
    metrics_path: '/metrics'
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # 系統節點監控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['lottery-api:9100']
    scrape_interval: 15s
    metrics_path: '/metrics'

  # Redis 監控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
    metrics_path: '/metrics'

  # Nginx 監控
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:9113']
    scrape_interval: 30s
    metrics_path: '/metrics'

  # 應用特定指標
  - job_name: 'lottery-prediction-metrics'
    static_configs:
      - targets: ['lottery-api:8000']
    scrape_interval: 30s
    metrics_path: '/tracking/metrics/prometheus'
    params:
      lottery_type: ['powercolor', 'lotto649', 'dailycash']

  # 數據庫監控
  - job_name: 'sqlite-exporter'
    static_configs:
      - targets: ['lottery-api:9199']
    scrape_interval: 60s
    metrics_path: '/metrics'

# 遠程寫入配置 (可選)
# remote_write:
#   - url: "https://your-remote-prometheus/api/v1/write"
#     basic_auth:
#       username: "user"
#       password: "password"

# 遠程讀取配置 (可選)
# remote_read:
#   - url: "https://your-remote-prometheus/api/v1/read"
#     basic_auth:
#       username: "user"
#       password: "password"