# Redis配置文件 - 彩票預測系統
# 輕量級內存數據庫配置

# 基本配置
port 6379
bind 127.0.0.1
protected-mode yes
timeout 0
tcp-keepalive 300

# 持久化配置
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /app/data/redis

# 內存管理
maxmemory 256mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# 日誌配置
loglevel notice
logfile /app/logs/redis.log

# 慢查詢日誌
slowlog-log-slower-than 10000
slowlog-max-len 128

# 客戶端配置
maxclients 100

# AOF持久化（可選）
appendonly no
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 其他配置
databases 16
always-show-logo no