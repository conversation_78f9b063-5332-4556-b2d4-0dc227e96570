#!/bin/bash
# 彩票預測系統 - Docker健康檢查腳本

# 顏色定義
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 配置變量
FLASK_PORT=${FLASK_PORT:-7890}
API_PORT=${API_PORT:-8000}
REDIS_PORT=${REDIS_PORT:-6379}
TIMEOUT=10

# 健康檢查結果
overall_status=0
services_status=()

echo "🏥 執行系統健康檢查..."

# 檢查Redis服務
check_redis() {
    echo -n "  檢查Redis服務..."
    if timeout $TIMEOUT redis-cli -p $REDIS_PORT ping > /dev/null 2>&1; then
        echo -e " ${GREEN}✅ 正常${NC}"
        services_status+=("redis:healthy")
        return 0
    else
        echo -e " ${RED}❌ 異常${NC}"
        services_status+=("redis:unhealthy")
        return 1
    fi
}

# 檢查Flask Web服務
check_flask() {
    echo -n "  檢查Flask Web服務..."
    if timeout $TIMEOUT curl -s http://localhost:$FLASK_PORT/health > /dev/null 2>&1; then
        echo -e " ${GREEN}✅ 正常${NC}"
        services_status+=("flask:healthy")
        return 0
    else
        echo -e " ${RED}❌ 異常${NC}"
        services_status+=("flask:unhealthy")
        return 1
    fi
}

# 檢查FastAPI服務
check_fastapi() {
    echo -n "  檢查FastAPI服務..."
    if timeout $TIMEOUT curl -s http://localhost:$API_PORT/health > /dev/null 2>&1; then
        echo -e " ${GREEN}✅ 正常${NC}"
        services_status+=("fastapi:healthy")
        return 0
    else
        echo -e " ${RED}❌ 異常${NC}"
        services_status+=("fastapi:unhealthy")
        return 1
    fi
}

# 檢查數據庫文件
check_database() {
    echo -n "  檢查數據庫..."
    if [ -f "/app/data/lottery.db" ] && [ -s "/app/data/lottery.db" ]; then
        echo -e " ${GREEN}✅ 正常${NC}"
        services_status+=("database:healthy")
        return 0
    else
        echo -e " ${YELLOW}⚠️  數據庫文件不存在或為空${NC}"
        services_status+=("database:warning")
        return 0  # 不算作失敗，因為可能是首次啟動
    fi
}

# 檢查磁盤空間
check_disk_space() {
    echo -n "  檢查磁盤空間..."
    usage=$(df /app | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$usage" -lt 90 ]; then
        echo -e " ${GREEN}✅ 正常 (${usage}%)${NC}"
        services_status+=("disk:healthy")
        return 0
    else
        echo -e " ${RED}❌ 磁盤空間不足 (${usage}%)${NC}"
        services_status+=("disk:critical")
        return 1
    fi
}

# 檢查內存使用
check_memory() {
    echo -n "  檢查內存使用..."
    if command -v free > /dev/null; then
        mem_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
        if [ "$mem_usage" -lt 90 ]; then
            echo -e " ${GREEN}✅ 正常 (${mem_usage}%)${NC}"
            services_status+=("memory:healthy")
            return 0
        else
            echo -e " ${YELLOW}⚠️  內存使用高 (${mem_usage}%)${NC}"
            services_status+=("memory:warning")
            return 0
        fi
    else
        echo -e " ${YELLOW}⚠️  無法檢查${NC}"
        services_status+=("memory:unknown")
        return 0
    fi
}

# 執行所有檢查
main() {
    echo "========================================="
    
    # 執行各項檢查
    check_redis || overall_status=1
    check_flask || overall_status=1
    check_fastapi || overall_status=1
    check_database
    check_disk_space || overall_status=1
    check_memory
    
    echo "========================================="
    
    # 生成健康檢查報告JSON
    timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    cat > /tmp/health_status.json << EOF
{
    "timestamp": "$timestamp",
    "status": "$([ $overall_status -eq 0 ] && echo "healthy" || echo "unhealthy")",
    "services": {
EOF

    for i in "${!services_status[@]}"; do
        service_info=(${services_status[$i]//:/ })
        service_name=${service_info[0]}
        service_status=${service_info[1]}
        
        echo "        \"$service_name\": \"$service_status\"$([ $i -lt $((${#services_status[@]}-1)) ] && echo ",")" >> /tmp/health_status.json
    done

    cat >> /tmp/health_status.json << EOF
    },
    "uptime": "$(uptime -p 2>/dev/null || echo 'unknown')",
    "version": "${VERSION:-3.0.0}"
}
EOF
    
    # 顯示總體狀態
    if [ $overall_status -eq 0 ]; then
        echo -e "${GREEN}🎉 系統健康狀態: 正常${NC}"
        echo "📄 詳細報告: /tmp/health_status.json"
    else
        echo -e "${RED}🚨 系統健康狀態: 異常${NC}"
        echo "📄 詳細報告: /tmp/health_status.json"
        echo "💡 請檢查上述失敗的服務"
    fi
    
    return $overall_status
}

# 執行主函數
main