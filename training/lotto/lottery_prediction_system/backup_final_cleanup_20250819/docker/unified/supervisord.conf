[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
childlogdir=/var/log/supervisor

[unix_http_server]
file=/var/run/supervisor.sock

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

# Redis服務
[program:redis]
command=redis-server /etc/redis/redis.conf
priority=1
autostart=true
autorestart=true
stdout_logfile=/app/logs/redis.log
stderr_logfile=/app/logs/redis.error.log
stdout_logfile_maxbytes=10MB
stderr_logfile_maxbytes=10MB
stdout_logfile_backups=2
stderr_logfile_backups=2

# Flask Web界面 (主要服務)
[program:flask-web]
command=python /docker/unified/start_flask.py
directory=/app
priority=10
autostart=true
autorestart=true
startsecs=10
startretries=3
stdout_logfile=/app/logs/flask.log
stderr_logfile=/app/logs/flask.error.log
stdout_logfile_maxbytes=10MB
stderr_logfile_maxbytes=10MB
stdout_logfile_backups=3
stderr_logfile_backups=3
environment=PYTHONPATH="/app",FLASK_PORT="7890",FLASK_ENV="production"

# FastAPI服務 (Phase 3 API)
[program:fastapi]
command=uvicorn phase3.web_api:app --host 0.0.0.0 --port 8000 --workers 2
directory=/app
priority=10
autostart=true
autorestart=true
startsecs=10
startretries=3
stdout_logfile=/app/logs/fastapi.log
stderr_logfile=/app/logs/fastapi.error.log
stdout_logfile_maxbytes=10MB
stderr_logfile_maxbytes=10MB
stdout_logfile_backups=3
stderr_logfile_backups=3
environment=PYTHONPATH="/app"

# 每日自動化調度器
[program:scheduler]
command=python daily_automation.py --daemon
directory=/app
priority=20
autostart=true
autorestart=true
startsecs=30
startretries=3
stdout_logfile=/app/logs/scheduler.log
stderr_logfile=/app/logs/scheduler.error.log
stdout_logfile_maxbytes=10MB
stderr_logfile_maxbytes=10MB
stdout_logfile_backups=2
stderr_logfile_backups=2
environment=PYTHONPATH="/app"

# Nginx反向代理
[program:nginx]
command=/usr/sbin/nginx -g "daemon off;"
priority=30
autostart=true
autorestart=true
stdout_logfile=/app/logs/nginx.log
stderr_logfile=/app/logs/nginx.error.log
stdout_logfile_maxbytes=10MB
stderr_logfile_maxbytes=10MB
stdout_logfile_backups=2
stderr_logfile_backups=2

# 程序組管理
[group:lottery-system]
programs=redis,flask-web,fastapi,scheduler,nginx
priority=999