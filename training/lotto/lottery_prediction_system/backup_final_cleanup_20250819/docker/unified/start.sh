#!/bin/bash
# 彩票預測系統 - Docker容器啟動腳本

echo "🎯 彩票預測系統啟動中..."
echo "=================================="

# 設置環境變量默認值
export FLASK_PORT=${FLASK_PORT:-7890}
export API_PORT=${API_PORT:-8000}
export REDIS_PORT=${REDIS_PORT:-6379}
export DB_PATH=${DB_PATH:-/app/data/lottery.db}
export LOG_LEVEL=${LOG_LEVEL:-INFO}
export AUTO_UPDATE=${AUTO_UPDATE:-true}
export TIMEZONE=${TIMEZONE:-Asia/Taipei}

# 顯示配置信息
echo "📋 系統配置:"
echo "  Flask端口: $FLASK_PORT"
echo "  API端口: $API_PORT"
echo "  Redis端口: $REDIS_PORT"
echo "  數據庫路徑: $DB_PATH"
echo "  日誌級別: $LOG_LEVEL"
echo "  自動更新: $AUTO_UPDATE"
echo "  時區: $TIMEZONE"
echo "=================================="

# 初始化函數
initialize_system() {
    echo "🔧 初始化系統..."
    
    # 確保目錄存在
    mkdir -p /app/logs /app/data /app/models /app/backups /app/reports /app/analysis_results
    
    # 初始化數據庫
    if [ ! -f "$DB_PATH" ]; then
        echo "📊 創建數據庫..."
        python -c "
from data.db_manager import DBManager
db = DBManager()
print('數據庫初始化完成')
"
    fi
    
    # 檢查模型文件
    if [ ! -d "/app/models" ] || [ -z "$(ls -A /app/models 2>/dev/null)" ]; then
        echo "🤖 下載預訓練模型..."
        # 這裡可以添加模型下載邏輯
    fi
    
    # 設置日誌輪換
    echo "📝 配置日誌輪換..."
    cat > /etc/logrotate.d/lottery << EOF
/app/logs/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
EOF
}

# 檢查服務健康狀態
check_services() {
    echo "🏥 檢查服務狀態..."
    
    # 檢查Redis
    redis-cli -p $REDIS_PORT ping > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ Redis運行正常"
    else
        echo "❌ Redis未響應"
        return 1
    fi
    
    # 檢查Flask
    curl -s http://localhost:$FLASK_PORT/health > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ Flask運行正常"
    else
        echo "⏳ Flask啟動中..."
    fi
    
    # 檢查FastAPI
    curl -s http://localhost:$API_PORT/health > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ FastAPI運行正常"
    else
        echo "⏳ FastAPI啟動中..."
    fi
    
    return 0
}

# 清理函數
cleanup() {
    echo "🧹 清理臨時文件..."
    find /app/logs -name "*.log" -mtime +30 -delete
    find /app/backups -name "*.bak" -mtime +30 -delete
}

# 主啟動流程
main() {
    # 初始化系統
    initialize_system
    
    # 清理舊文件
    cleanup
    
    # 啟動supervisor
    echo "🚀 啟動Supervisor管理器..."
    exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
}

# 捕獲信號以優雅關閉
trap 'echo "🛑 收到停止信號，正在優雅關閉..."; supervisorctl stop all; exit 0' SIGTERM SIGINT

# 執行主函數
main