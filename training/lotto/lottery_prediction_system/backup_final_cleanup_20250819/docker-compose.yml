# Phase 3.6 Docker Compose 配置
# 彩票預測系統簡化部署

services:
  # 主應用服務
  lottery-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: lottery-prediction-api
    restart: unless-stopped
    ports:
      - "8001:8000"  # 避免端口衝突
      - "8081:8080"  # 避免端口衝突
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./backups:/app/backups
      - ./reports:/app/reports
      - ./exports:/app/exports
    environment:
      - LOTTERY_SYSTEM_MODE=production
      - LOTTERY_SYSTEM_LOG_LEVEL=INFO
      - DATABASE_URL=sqlite:///app/data/lottery_prediction.db
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - WEB_PORT=8080
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - lottery-network

# 網絡配置
networks:
  lottery-network:
    driver: bridge