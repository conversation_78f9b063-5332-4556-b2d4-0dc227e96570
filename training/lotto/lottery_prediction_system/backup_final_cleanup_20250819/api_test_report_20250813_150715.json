{"test_time": "2025-08-13T15:07:15.980602", "base_url": "http://localhost:7890", "total_endpoints": 26, "success_count": 24, "success_rate": 0.9230769230769231, "results": [{"endpoint": "/api/system_status", "method": "GET", "status_code": 200, "success": true, "description": "系统状态", "response_size": 640, "has_data": true, "api_success": true}, {"endpoint": "/api/dashboard_data", "method": "GET", "status_code": 500, "success": false, "description": "仪表板数据", "response_size": 137, "has_data": false, "api_success": false}, {"endpoint": "/api/latest_predictions/powercolor", "method": "GET", "status_code": 200, "success": true, "description": "威力彩最新预测", "response_size": 512, "has_data": true, "api_success": true}, {"endpoint": "/api/accuracy/powercolor", "method": "GET", "status_code": 200, "success": true, "description": "威力彩准确度统计", "response_size": 1300, "has_data": true, "api_success": true}, {"endpoint": "/api/comprehensive_analysis/powercolor", "method": "GET", "status_code": 200, "success": true, "description": "威力彩综合分析", "response_size": 4302, "has_data": true, "api_success": true}, {"endpoint": "/api/number_combination_analysis/powercolor", "method": "GET", "status_code": 200, "success": true, "description": "威力彩号码组合分析", "response_size": 5065, "has_data": true, "api_success": true}, {"endpoint": "/api/latest_predictions/lotto649", "method": "GET", "status_code": 200, "success": true, "description": "大乐透最新预测", "response_size": 180, "has_data": true, "api_success": true}, {"endpoint": "/api/accuracy/lotto649", "method": "GET", "status_code": 200, "success": true, "description": "大乐透准确度统计", "response_size": 1300, "has_data": true, "api_success": true}, {"endpoint": "/api/comprehensive_analysis/lotto649", "method": "GET", "status_code": 200, "success": true, "description": "大乐透综合分析", "response_size": 4304, "has_data": true, "api_success": true}, {"endpoint": "/api/number_combination_analysis/lotto649", "method": "GET", "status_code": 200, "success": true, "description": "大乐透号码组合分析", "response_size": 5070, "has_data": true, "api_success": true}, {"endpoint": "/api/latest_predictions/dailycash", "method": "GET", "status_code": 200, "success": true, "description": "今彩539最新预测", "response_size": 180, "has_data": true, "api_success": true}, {"endpoint": "/api/accuracy/dailycash", "method": "GET", "status_code": 200, "success": true, "description": "今彩539准确度统计", "response_size": 1300, "has_data": true, "api_success": true}, {"endpoint": "/api/comprehensive_analysis/dailycash", "method": "GET", "status_code": 200, "success": true, "description": "今彩539综合分析", "response_size": 4300, "has_data": true, "api_success": true}, {"endpoint": "/api/number_combination_analysis/dailycash", "method": "GET", "status_code": 200, "success": true, "description": "今彩539号码组合分析", "response_size": 4887, "has_data": true, "api_success": true}, {"endpoint": "/api/results?lottery_type=powercolor&limit=5", "method": "GET", "status_code": 200, "success": true, "description": "威力彩开奖结果", "response_size": 1943, "has_data": true, "api_success": true}, {"endpoint": "/api/results?lottery_type=lotto649&limit=5", "method": "GET", "status_code": 200, "success": true, "description": "大乐透开奖结果", "response_size": 1977, "has_data": true, "api_success": true}, {"endpoint": "/api/results?lottery_type=dailycash&limit=5", "method": "GET", "status_code": 200, "success": true, "description": "今彩539开奖结果", "response_size": 1711, "has_data": true, "api_success": true}, {"endpoint": "/api/predict", "method": "POST", "status_code": 200, "success": true, "description": "生成威力彩预测", "response_size": 333, "has_data": true, "api_success": true}, {"endpoint": "/api/predict", "method": "POST", "status_code": 200, "success": true, "description": "生成大乐透预测", "response_size": 330, "has_data": true, "api_success": true}, {"endpoint": "/api/predict", "method": "POST", "status_code": 200, "success": true, "description": "生成今彩539预测", "response_size": 324, "has_data": true, "api_success": true}, {"endpoint": "/api/verify_prediction/1", "method": "GET", "status_code": 405, "success": false, "description": "验证预测结果", "response_size": 153, "has_data": false, "api_success": false}, {"endpoint": "/", "method": "GET", "status_code": 200, "success": true, "description": "主页", "response_size": 18902, "has_data": false, "api_success": false}, {"endpoint": "/predictions", "method": "GET", "status_code": 200, "success": true, "description": "预测页面", "response_size": 11528, "has_data": false, "api_success": false}, {"endpoint": "/results", "method": "GET", "status_code": 200, "success": true, "description": "开奖结果页面", "response_size": 29655, "has_data": false, "api_success": false}, {"endpoint": "/analysis", "method": "GET", "status_code": 200, "success": true, "description": "分析页面", "response_size": 10028, "has_data": false, "api_success": false}, {"endpoint": "/dashboard", "method": "GET", "status_code": 200, "success": true, "description": "仪表板页面", "response_size": 19183, "has_data": false, "api_success": false}]}