{"lottery_type": "powercolor", "backtest_period": {"total_periods": 80, "start_date": "Unknown", "end_date": "Unknown"}, "method_performance": {"頻率分析": {"avg_accuracy": 14.533333333333333, "total_predictions": 50, "hit_rate_3plus": 0.0, "performance_score": 30.99293224352072}, "趨勢分析": {"avg_accuracy": 14.933333333333332, "total_predictions": 50, "hit_rate_3plus": 0.02, "performance_score": 32.78103704861201}, "板路分析": {"avg_accuracy": 12.0, "total_predictions": 50, "hit_rate_3plus": 0.02, "performance_score": 31.100129200907446}, "綜合分析": {"avg_accuracy": 16.133333333333336, "total_predictions": 50, "hit_rate_3plus": 0.0, "performance_score": 31.28000000000001}, "機器學習": {"avg_accuracy": 12.266666666666667, "total_predictions": 50, "hit_rate_3plus": 0.06, "performance_score": 32.07679265830172}, "增強分析": {"avg_accuracy": 14.533333333333333, "total_predictions": 50, "hit_rate_3plus": 0.0, "performance_score": 30.99293224352072}, "統計模型": {"avg_accuracy": 12.266666666666666, "total_predictions": 50, "hit_rate_3plus": 0.0, "performance_score": 30.797576472691578}, "模式識別": {"avg_accuracy": 14.533333333333333, "total_predictions": 50, "hit_rate_3plus": 0.0, "performance_score": 30.99293224352072}}, "overall_analysis": {"best_performing_method": {"method": "趨勢分析", "score": 32.78103704861201, "accuracy": 14.933333333333332}, "worst_performing_method": {"method": "統計模型", "score": 30.797576472691578, "accuracy": 12.266666666666666}, "average_accuracy": 13.899999999999999, "accuracy_variance": 2.025555555555557}, "recommendations": ["建議優先使用以下方法：趨勢分析, 機器學習, 綜合分析", "準確度較高的方法：綜合分析", "建議增加更多歷史數據以提升回測可靠性"], "generated_at": "2025-07-23T10:44:23.321047"}