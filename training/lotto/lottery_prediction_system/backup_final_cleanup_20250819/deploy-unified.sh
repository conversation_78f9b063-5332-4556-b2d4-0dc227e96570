#!/bin/bash
# 彩票預測系統 - 統一Docker部署腳本

# 設置顏色
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 設置變量
CONTAINER_NAME="lottery-prediction-unified"
IMAGE_NAME="lottery-prediction:unified"
COMPOSE_FILE="docker-compose.unified.yml"

echo -e "${BLUE}🎯 彩票預測系統 - 統一Docker部署${NC}"
echo "=============================================="

# 檢查Docker是否安裝
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker未安裝，請先安裝Docker${NC}"
        exit 1
    fi
    
    if ! docker compose version &> /dev/null; then
        echo -e "${RED}❌ Docker Compose未安裝，請先安裝Docker Compose${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Docker環境檢查通過${NC}"
}

# 創建必要的目錄
create_directories() {
    echo "📁 創建必要目錄..."
    mkdir -p data logs backups reports models analysis_results
    chmod 777 data logs backups reports models analysis_results
    echo -e "${GREEN}✅ 目錄創建完成${NC}"
}

# 檢查端口是否被佔用
check_ports() {
    echo "🔌 檢查端口佔用..."
    
    ports=(7890 8000 80)
    occupied_ports=()
    
    for port in "${ports[@]}"; do
        if netstat -tuln | grep ":$port " > /dev/null 2>&1; then
            occupied_ports+=($port)
        fi
    done
    
    if [ ${#occupied_ports[@]} -ne 0 ]; then
        echo -e "${YELLOW}⚠️  以下端口被佔用: ${occupied_ports[*]}${NC}"
        echo "是否繼續部署？(y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            echo "部署已取消"
            exit 1
        fi
    else
        echo -e "${GREEN}✅ 端口檢查通過${NC}"
    fi
}

# 停止現有容器
stop_existing() {
    echo "🛑 停止現有容器..."
    if docker ps -q --filter "name=$CONTAINER_NAME" | grep -q .; then
        docker stop $CONTAINER_NAME
        docker rm $CONTAINER_NAME
        echo -e "${GREEN}✅ 現有容器已停止${NC}"
    else
        echo "ℹ️  沒有運行中的容器"
    fi
}

# 構建Docker鏡像
build_image() {
    echo "🔨 構建Docker鏡像..."
    if docker build -f Dockerfile.unified -t $IMAGE_NAME .; then
        echo -e "${GREEN}✅ 鏡像構建完成${NC}"
    else
        echo -e "${RED}❌ 鏡像構建失敗${NC}"
        exit 1
    fi
}

# 啟動容器
start_container() {
    echo "🚀 啟動容器..."
    if docker compose -f $COMPOSE_FILE up -d; then
        echo -e "${GREEN}✅ 容器啟動完成${NC}"
    else
        echo -e "${RED}❌ 容器啟動失敗${NC}"
        exit 1
    fi
}

# 等待服務就緒
wait_for_services() {
    echo "⏳ 等待服務就緒..."
    sleep 30
    
    # 檢查健康狀態
    max_attempts=12  # 2分鐘
    attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if docker exec $CONTAINER_NAME /healthcheck.sh > /dev/null 2>&1; then
            echo -e "${GREEN}✅ 服務已就緒${NC}"
            return 0
        fi
        
        attempt=$((attempt + 1))
        echo -n "."
        sleep 10
    done
    
    echo -e "${YELLOW}⚠️  服務啟動時間較長，但容器已運行${NC}"
    return 0
}

# 顯示訪問信息
show_access_info() {
    echo ""
    echo "🎉 部署完成！"
    echo "=============================================="
    echo -e "${BLUE}📱 訪問地址:${NC}"
    echo "  🌐 主要Web界面: http://localhost:7890"
    echo "  🔧 API服務:     http://localhost:8000"
    echo "  📊 健康檢查:    http://localhost/health"
    echo ""
    echo -e "${BLUE}🛠️  管理命令:${NC}"
    echo "  查看日誌:   docker logs $CONTAINER_NAME"
    echo "  進入容器:   docker exec -it $CONTAINER_NAME bash"
    echo "  停止服務:   docker stop $CONTAINER_NAME"
    echo "  重啟服務:   docker restart $CONTAINER_NAME"
    echo ""
    echo -e "${BLUE}📁 數據目錄:${NC}"
    echo "  數據庫:     ./data/"
    echo "  日誌:       ./logs/"
    echo "  備份:       ./backups/"
    echo "  報告:       ./reports/"
    echo "=============================================="
}

# 顯示幫助信息
show_help() {
    echo "用法: $0 [選項]"
    echo ""
    echo "選項:"
    echo "  -h, --help     顯示幫助信息"
    echo "  -f, --force    強制重新構建鏡像"
    echo "  -s, --stop     停止服務"
    echo "  -r, --restart  重啟服務"
    echo "  --logs         查看服務日誌"
    echo "  --status       查看服務狀態"
}

# 檢查服務狀態
check_status() {
    echo "📋 服務狀態:"
    echo "=============================================="
    
    if docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep $CONTAINER_NAME; then
        echo ""
        echo "🏥 健康檢查:"
        docker exec $CONTAINER_NAME /healthcheck.sh || echo "健康檢查失敗"
    else
        echo "❌ 服務未運行"
    fi
}

# 主函數
main() {
    case "$1" in
        -h|--help)
            show_help
            exit 0
            ;;
        -s|--stop)
            echo "🛑 停止服務..."
            docker compose -f $COMPOSE_FILE down
            echo -e "${GREEN}✅ 服務已停止${NC}"
            exit 0
            ;;
        -r|--restart)
            echo "🔄 重啟服務..."
            docker compose -f $COMPOSE_FILE restart
            echo -e "${GREEN}✅ 服務已重啟${NC}"
            exit 0
            ;;
        --logs)
            docker logs -f $CONTAINER_NAME
            exit 0
            ;;
        --status)
            check_status
            exit 0
            ;;
    esac
    
    # 執行部署流程
    check_docker
    create_directories
    check_ports
    stop_existing
    
    if [ "$1" = "-f" ] || [ "$1" = "--force" ]; then
        echo "🔨 強制重新構建..."
        docker rmi $IMAGE_NAME 2>/dev/null || true
    fi
    
    build_image
    start_container
    wait_for_services
    show_access_info
}

# 執行主函數
main "$@"