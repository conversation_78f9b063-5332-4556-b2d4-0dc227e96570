lottery_prediction_system/
├── core/                       # 核心組件
│   ├── __init__.py
│   ├── version_manager.py      # 版本管理
│   ├── db_connector.py         # 資料庫連接抽象層
│   ├── logger.py               # 增強日誌系統
│   └── config.py               # 系統配置
├── data/                       # 資料處理
│   ├── __init__.py
│   ├── db_manager.py           # 增強的資料庫管理
│   ├── feature_engineering.py  # 特徵工程
│   └── lottery_data.db         # 資料庫
├── prediction/                 # 預測引擎
│   ├── __init__.py
│   ├── base_predictor.py       # 預測器基礎類別
│   ├── board_path_analyzer.py  # 版路分析
│   ├── board_path_engine.py    # 整合的板路分析引擎
│   ├── prediction_result.py
│   ├── result_narrower.py
│   └── lottery_predictor.py  <-- 原 predictor.py
├── analysis/                   # 分析與評估
│   ├── __init__.py
│   ├── result_analyzer.py      # 結果分析器
│   ├── backtest_engine.py      # 回測引擎
│   └── performance_evaluator.py # 性能評估工具
├── model/                     # 模型訓練與管理
│   ├── __init__.py
│   ├── model_trainer.py        # 模型訓練器
│   ├── model_selector.py       # 模型選擇器
│   └── feature_selector.py     # 特徵選擇器
├── utils/                      # 實用工具
│   ├── __init__.py
│   ├── result_narrower.py      # 結果縮小器
│   ├── visualization.py        # 視覺化工具
│   └── helpers.py              # 輔助函數
│
├── models/   #模型json
│   ├── 
├── 
├── lottery_daily_updater.py    #開獎更新
└── main.py #主程式