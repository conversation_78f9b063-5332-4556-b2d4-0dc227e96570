高優先項目實施計畫
一、資料庫結構升級
具體實施步驟
1. 前期準備（1週）

資料庫完整備份：建立現有系統的完整備份
建立測試環境：複製當前生產環境到測試伺服器
制定回滾計畫：準備資料庫回滾腳本及程序

2. 資料表結構修改（2週）
預測結果表擴展
sqlCopy-- 為威力彩預測表增加新欄位
ALTER TABLE PowercolorPredictions 
ADD CandidateIndex INT NULL,
    Confidence FLOAT NULL,
    GenerationMethod VARCHAR(50) NULL,
    ConfigParams NVARCHAR(MAX) NULL;

-- 為大樂透預測表增加新欄位
ALTER TABLE Lotto649Predictions 
ADD CandidateIndex INT NULL,
    Confidence FLOAT NULL,
    GenerationMethod VARCHAR(50) NULL,
    ConfigParams NVARCHAR(MAX) NULL;

-- 為今彩539預測表增加新欄位
ALTER TABLE DailyCashPredictions 
ADD CandidateIndex INT NULL,
    Confidence FLOAT NULL,
    GenerationMethod VARCHAR(50) NULL,
    ConfigParams NVARCHAR(MAX) NULL;
新增元數據表
sqlCopy-- 建立預測元數據表
CREATE TABLE PredictionMetadata (
    ID INT IDENTITY(1,1) PRIMARY KEY,
    PredictionID VARCHAR(50) NOT NULL,
    MetadataKey VARCHAR(255) NOT NULL,
    MetadataValue NVARCHAR(MAX) NULL,
    CreatedAt DATETIME DEFAULT GETDATE()
);

-- 建立索引提升查詢效能
CREATE INDEX idx_metadata_predictionid ON PredictionMetadata(PredictionID);
索引優化
sqlCopy-- 為各表建立索引
CREATE INDEX idx_powercolor_predictions_period ON PowercolorPredictions(Period);
CREATE INDEX idx_powercolor_predictions_preddate ON PowercolorPredictions(PredictionDate);
CREATE INDEX idx_powercolor_predictions_predid ON PowercolorPredictions(PredictionID);

CREATE INDEX idx_lotto649_predictions_period ON Lotto649Predictions(Period);
CREATE INDEX idx_lotto649_predictions_preddate ON Lotto649Predictions(PredictionDate);
CREATE INDEX idx_lotto649_predictions_predid ON Lotto649Predictions(PredictionID);

CREATE INDEX idx_dailycash_predictions_period ON DailyCashPredictions(Period);
CREATE INDEX idx_dailycash_predictions_preddate ON DailyCashPredictions(PredictionDate);
CREATE INDEX idx_dailycash_predictions_predid ON DailyCashPredictions(PredictionID);
3. 資料存取層程式修改（2週）

更新 db_manager.py 中的 save_prediction 方法，支援多候選結果存儲
實現 _save_candidates_predictions 和 _save_prediction_metadata 方法
優化 load_prediction_records 方法，支援元數據關聯查詢
新增 get_prediction_metadata 方法，支援元數據檢索

4. 遷移現有資料（1週）

編寫資料遷移腳本，將現有預測結果轉換為新結構
進行增量測試，確認遷移結果正確性
編寫資料驗證腳本，確保資料一致性

5. 測試與部署（1週）

在測試環境中進行全面測試
安排非高峰時段進行生產環境升級
實施升級後驗證流程
監控系統運行狀況

風險與應對措施

資料遺失風險：進行多重備份，準備回滾方案
效能問題：進行索引優化，監控查詢效能
程式相容性：逐步測試每個功能，確保相容性

二、多候選結果生成與管理
具體實施步驟
1. 設計與準備（1週）

設計 PredictionResult 類完整結構
設計 ResultNarrower 類介面及功能
準備測試資料集和測試用例

2. PredictionResult 類實現（1週）
pythonCopy# 完整實現預測結果類，支援多候選管理
class PredictionResult:
    def __init__(self, lottery_type, method, version):
        self.lottery_type = lottery_type
        self.method = method
        self.version = version
        self.timestamp = datetime.now().isoformat()
        self.candidates = []
        self.metadata = {}
    
    def add_candidate(self, main_numbers, special_number=None, confidence=1.0, explanation=None, metadata=None):
        """
        添加候選號碼
        
        Args:
            main_numbers: 主區號碼列表
            special_number: 特別號/第二區號碼
            confidence: 信心分數 (0-1)
            explanation: 預測解釋
            metadata: 其他元數據
        """
        candidate = {
            'main_numbers': sorted(main_numbers),
            'confidence': confidence,
            'creation_time': datetime.now().isoformat()
        }
        
        if special_number is not None:
            candidate['special_number'] = special_number
            
        if explanation:
            candidate['explanation'] = explanation
            
        if metadata:
            candidate['metadata'] = metadata
            
        self.candidates.append(candidate)
        
        # 根據信心分數排序
        self.candidates = sorted(self.candidates, key=lambda x: x['confidence'], reverse=True)
    
    def get_top_candidate(self):
        """獲取最高信心分數的候選號碼"""
        if self.candidates:
            return self.candidates[0]
        return None
    
    def to_dict(self):
        """轉換為字典格式"""
        return {
            'lottery_type': self.lottery_type,
            'method': self.method,
            'version': self.version,
            'timestamp': self.timestamp,
            'candidates': self.candidates,
            'metadata': self.metadata
        }
    
    def to_json(self, indent=2):
        """轉換為JSON格式"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=indent)
    
    @classmethod
    def from_dict(cls, data):
        """從字典創建結果對象"""
        result = cls(
            lottery_type=data['lottery_type'],
            method=data['method'],
            version=data['version']
        )
        
        result.timestamp = data['timestamp']
        result.candidates = data.get('candidates', [])
        result.metadata = data.get('metadata', {})
        
        return result
3. ResultNarrower 類實現（2週）
pythonCopyclass ResultNarrower:
    """預測結果縮小器，處理多組候選號碼的篩選"""
    
    def __init__(self, db_manager=None):
        """
        初始化結果縮小器
        
        Args:
            db_manager: 可選的資料庫管理器，用於查詢歷史數據
        """
        self.db_manager = db_manager
    
    def narrow_by_confidence(self, prediction_result, threshold=0.7):
        """
        根據信心分數篩選候選號碼
        
        Args:
            prediction_result: 預測結果物件
            threshold: 信心分數閾值
            
        Returns:
            PredictionResult: 篩選後的結果
        """
        filtered_candidates = []
        
        for candidate in prediction_result.candidates:
            if candidate['confidence'] >= threshold:
                filtered_candidates.append(candidate)
                
        # 創建新的預測結果
        filtered_result = PredictionResult(
            lottery_type=prediction_result.lottery_type,
            method=prediction_result.method,
            version=prediction_result.version
        )
        
        # 添加篩選後的候選
        for candidate in filtered_candidates:
            filtered_result.add_candidate(
                main_numbers=candidate['main_numbers'],
                special_number=candidate.get('special_number'),
                confidence=candidate['confidence'],
                explanation=candidate.get('explanation', [])
            )
            
        return filtered_result
    
    def narrow_by_ensemble(self, prediction_results, weights=None):
        """
        合併多個預測結果
        
        Args:
            prediction_results: 預測結果列表
            weights: 各預測方法的權重
            
        Returns:
            PredictionResult: 合併後的結果
        """
        # 實現方法詳細邏輯...
        # 包含多種預測結果整合策略
        pass
4. 預測器擴展（2週）

修改 BasePredictor 類，支援多候選結果生成
實現 generate_candidates 方法，提供標準介面
更新 predict 方法，支援候選生成參數設定
擴展 evaluate 方法，支援多候選結果評估

5. 板路分析引擎擴展（1週）

更新 BoardPathEngine 中的 predict 方法，支援多候選生成
實現更多樣化的候選生成策略
優化信心分數計算機制

6. 系統整合與測試（1週）

在 main.py 中整合多候選結果展示
更新用戶介面，支援多候選結果顯示
全面測試系統各功能模組

風險與應對措施

效能風險：多候選生成可能增加計算負擔，需進行優化
用戶體驗：確保多候選結果呈現方式直觀易懂
系統複雜度：做好文檔及註釋工作，確保維護性

三、時間規劃與資源需求
總體時間表（預計10週）

第1-4週：資料庫結構升級
第5-10週：多候選結果生成與管理

資源需求

人力資源：

1名資料庫專家（全職，4週）
2名後端開發工程師（全職，10週）
1名測試工程師（兼職，10週）


硬體資源：

測試環境伺服器配置
額外的資料庫儲存空間


軟體資源：

資料庫管理工具
源碼版本控制系統
自動化測試工具



四、預期成果與效益
資料庫升級效益

支援更豐富的預測結果儲存
提升查詢效能，優化系統響應速度
實現更細粒度的預測結果分析

多候選結果管理效益

提供多樣化的預測選擇，提高用戶滿意度
增強系統靈活性，支援不同風險偏好
提高預測成功率，改善整體預測效果

透過這兩項高優先級改進，系統將能夠更好地支援多樣化的預測需求，提供更靈活、更精確的彩票預測服務。這將顯著提升系統價值，並為後續功能擴展奠定堅實基礎。