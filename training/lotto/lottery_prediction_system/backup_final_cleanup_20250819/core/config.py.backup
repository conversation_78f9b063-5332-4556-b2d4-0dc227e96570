"""
系統配置模塊
集中管理所有配置項
"""
import os
import json
import logging
from pathlib import Path

logger = logging.getLogger('config')

class Config:
    """配置管理類"""
    
    # 默認配置
    _defaults = {
        # 系統路徑
        'paths': {
            'models_dir': 'models',
            'data_dir': 'data',
            'logs_dir': 'logs',
            'exports_dir': 'exports',
            'analysis_dir': 'analysis_results',
            'version_history_dir': 'version_history'
        },
        
        # 資料庫設置
        'database': {
            'path': 'data/lottery_data.db',
            'backup_dir': 'data/backups',
            'max_backups': 5
        },
        
        # 預測設置
        'prediction': {
            'default_methods': ['ml', 'board_path'],
            'default_ensemble': True,
            'default_candidates_count': 5,
            'min_confidence': 0.5,
            'stabilize_results': True,
            'use_enhanced_analysis': True
        },
        
        # 彩票類型配置
        'lottery_types': {
            'powercolor': {
                'main_numbers': 38,
                'main_count': 6,
                'special_numbers': 8,
                'special_count': 1,
                'table_name': 'Powercolor'
            },
            'lotto649': {
                'main_numbers': 49,
                'main_count': 6,
                'special_numbers': 49,
                'special_count': 1,
                'table_name': 'Lotto649'
            },
            'dailycash': {
                'main_numbers': 39,
                'main_count': 5,
                'special_numbers': 0,
                'special_count': 0,
                'table_name': 'DailyCash'
            }
        },
        
        # 日誌設置
        'logging': {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file_level': 'DEBUG',
            'console_level': 'INFO',
            'max_file_size': 10485760,  # 10MB
            'backup_count': 5
        },
        
        # 版本設置
        'versions': {
            'predictor': 'v2.0.0',
            'model': 'v1.0.0',
            'analyzer': 'v1.0.0'
        },
        
        # 下載設置
        'download': {
            'retry_count': 3,
            'timeout': 30,
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
    }
    
    # 實例配置
    _config = {}
    
    @classmethod
    def initialize(cls, config_file=None):
        """初始化配置
        
        Args:
            config_file (str, optional): 配置文件路徑
            
        Returns:
            bool: 是否成功初始化
        """
        # 加載默認配置
        cls._config = cls._defaults.copy()
        
        # 如果提供了配置文件，則加載
        if config_file and os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                
                # 合併配置
                cls._merge_config(cls._config, user_config)
                logger.info(f"已從 {config_file} 加載配置")
                return True
            except Exception as e:
                logger.error(f"加載配置文件時出錯: {str(e)}")
                logger.warning("使用默認配置")
                return False
        
        # 確保所有必要的目錄存在
        cls._ensure_directories()
        return True
    
    @classmethod
    def _merge_config(cls, target, source):
        """遞歸合併配置字典
        
        Args:
            target (dict): 目標字典
            source (dict): 源字典
        """
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                cls._merge_config(target[key], value)
            else:
                target[key] = value
    
    @classmethod
    def _ensure_directories(cls):
        """確保所有必要的目錄存在"""
        for _, path in cls._config['paths'].items():
            os.makedirs(path, exist_ok=True)
    
    @classmethod
    def get(cls, key, default=None):
        """獲取配置項
        
        Args:
            key (str): 配置項鍵，支持點號分隔的路徑
            default: 默認值
            
        Returns:
            任意類型: 配置值或默認值
        """
        if not cls._config:
            cls.initialize()
            
        # 處理點號分隔的路徑
        parts = key.split('.')
        config = cls._config
        
        for part in parts:
            if isinstance(config, dict) and part in config:
                config = config[part]
            else:
                return default
                
        return config
    
    @classmethod
    def set(cls, key, value):
        """設置配置項
        
        Args:
            key (str): 配置項鍵，支持點號分隔的路徑
            value: 配置值
            
        Returns:
            bool: 是否成功設置
        """
        if not cls._config:
            cls.initialize()
            
        # 處理點號分隔的路徑
        parts = key.split('.')
        config = cls._config
        
        # 遍歷路徑
        for i, part in enumerate(parts[:-1]):
            if part not in config:
                config[part] = {}
            elif not isinstance(config[part], dict):
                config[part] = {}
            config = config[part]