# core/version_manager.py

import json
import os
from datetime import datetime

class VersionManager:
    """版本管理器，處理所有元件的版本控制"""
    
    def __init__(self, component_type, base_version="1.0.0", storage_path=None):
        """
        初始化版本管理器
        
        Args:
            component_type: 元件類型 (模型、預測器、分析器等)
            base_version: 基礎版本號
            storage_path: 版本歷史儲存路徑
        """
        self.component_type = component_type
        self.version_parts = self._parse_version(base_version)
        self.version_history = []
        self.current_metadata = {}  # 確保初始化這個屬性
        self.storage_path = storage_path or os.path.join("version_history", f"{component_type}_versions.json")
        
        # 嘗試載入現有版本歷史
        self._load_history()
        
        # 如果沒有歷史，初始化版本記錄
        if not self.version_history:
            self._record_version("初始版本")
    
    def create_new_version(self, major=False, minor=False, description=""):
        """
        創建新版本
        
        Args:
            major: 是否為主要版本更新
            minor: 是否為次要版本更新
            description: 版本變更描述
            
        Returns:
            str: 新版本號
        """
        if major:
            self.version_parts[0] += 1
            self.version_parts[1] = 0
            self.version_parts[2] = 0
        elif minor:
            self.version_parts[1] += 1
            self.version_parts[2] = 0
        else:
            self.version_parts[2] += 1
            
        self._record_version(description)
        self._save_history()
        return self.get_current_version()
    
    def get_current_version(self):
        """獲取當前版本號，確保版本數組至少有3個元素"""
        # 確保 version_parts 至少有3個元素
        while len(self.version_parts) < 3:
            self.version_parts.append(0)
            
        return f"v{self.version_parts[0]}.{self.version_parts[1]}.{self.version_parts[2]}"
    
    def add_metadata(self, key, value):
        """
        添加版本元數據
        
        Args:
            key: 元數據鍵
            value: 元數據值
        """
        self.current_metadata[key] = value
    
    def _record_version(self, description):
        """記錄版本變更"""
        timestamp = datetime.now().isoformat()
        
        # 確保 current_metadata 已被初始化
        if not hasattr(self, 'current_metadata'):
            self.current_metadata = {}
            
        version_info = {
            'version': self.get_current_version(),
            'timestamp': timestamp,
            'description': description,
            'metadata': self.current_metadata.copy()
        }
        self.version_history.append(version_info)
        self.current_metadata = {}  # 重置元數據
    
    def _parse_version(self, version_str):
        """解析版本字串為數字列表，並確保輸出陣列長度為3"""
        # 移除可能的前綴v
        if isinstance(version_str, str) and version_str.startswith('v'):
            version_str = version_str[1:]
        
        try:
            # 拆分版本號
            parts = version_str.split('.')
            
            # 初始化結果陣列
            result = [0, 0, 0]
            
            # 填充可用的部分
            for i in range(min(len(parts), 3)):
                try:
                    result[i] = int(parts[i])
                except (ValueError, TypeError):
                    # 無法轉換為整數時使用默認值0
                    result[i] = 0
                    
            return result
        except Exception:
            # 任何解析錯誤時，返回默認版本
            return [1, 0, 0]
    
    def get_version_history(self):
        """獲取版本歷史"""
        return self.version_history
    
    def _save_history(self):
        """儲存版本歷史到檔案"""
        # 確保目錄存在
        os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)
        
        with open(self.storage_path, 'w', encoding='utf-8') as f:
            json.dump({
                'component_type': self.component_type,
                'current_version': self.get_current_version(),
                'history': self.version_history
            }, f, ensure_ascii=False, indent=2)
    
    def _load_history(self):
        """從檔案載入版本歷史"""
        if os.path.exists(self.storage_path):
            try:
                with open(self.storage_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if 'history' in data and isinstance(data['history'], list):
                        self.version_history = data['history']
                        
                        # 設置當前版本
                        if 'current_version' in data:
                            self.version_parts = self._parse_version(data['current_version'])
            except Exception as e:
                print(f"載入版本歷史出錯: {str(e)}")