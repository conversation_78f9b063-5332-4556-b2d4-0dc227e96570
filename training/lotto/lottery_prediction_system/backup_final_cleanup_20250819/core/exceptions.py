"""
自定義異常類別模塊
定義系統中使用的所有自定義異常
"""

class LotteryPredictionError(Exception):
    """彩票預測系統基礎異常類別"""
    def __init__(self, message, code=None):
        self.message = message
        self.code = code
        super().__init__(self.message)

# 資料庫相關異常
class DatabaseError(LotteryPredictionError):
    """資料庫操作異常"""
    pass

class DataNotFoundError(DatabaseError):
    """資料不存在異常"""
    pass

class DataFormatError(DatabaseError):
    """資料格式錯誤異常"""
    pass

# 預測相關異常
class PredictionError(LotteryPredictionError):
    """預測操作異常"""
    pass

class ModelNotFoundError(PredictionError):
    """模型不存在異常"""
    pass

class FeatureEngineeringError(PredictionError):
    """特徵工程異常"""
    pass

class InvalidPredictionMethodError(PredictionError):
    """無效的預測方法異常"""
    pass

# 分析相關異常
class AnalysisError(LotteryPredictionError):
    """分析操作異常"""
    pass

class InsufficientDataError(AnalysisError):
    """資料不足異常"""
    pass

# 配置相關異常
class ConfigurationError(LotteryPredictionError):
    """配置錯誤異常"""
    pass

class VersionError(LotteryPredictionError):
    """版本錯誤異常"""
    pass

# 輸入輸出相關異常
class IOError(LotteryPredictionError):
    """輸入輸出異常"""
    pass

class FileNotFoundError(IOError):
    """文件不存在異常"""
    pass

class InvalidInputError(IOError):
    """無效輸入異常"""
    pass

# 輔助函數
def handle_exception(exception, logger=None, raise_error=False, default_return=None):
    """統一處理異常
    
    Args:
        exception: 異常對象
        logger: 日誌記錄器
        raise_error: 是否重新拋出異常
        default_return: 默認返回值
        
    Returns:
        任意類型: 默認返回值
        
    Raises:
        LotteryPredictionError: 如果raise_error為True
    """
    # 記錄異常
    if logger:
        if isinstance(exception, LotteryPredictionError):
            logger.error(f"{exception.__class__.__name__}: {exception.message}")
            if hasattr(exception, 'code') and exception.code:
                logger.error(f"錯誤代碼: {exception.code}")
        else:
            logger.error(f"未處理的異常: {str(exception)}")
            import traceback
            logger.error(traceback.format_exc())
    
    # 重新拋出異常
    if raise_error:
        if isinstance(exception, LotteryPredictionError):
            raise exception
        else:
            raise LotteryPredictionError(str(exception))
    
    # 返回默認值
    return default_return