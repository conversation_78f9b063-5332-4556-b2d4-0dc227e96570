#!/usr/bin/env python3
"""
台灣彩券 Playwright 爬蟲
解決JavaScript動態渲染問題的現代化爬蟲
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import re

try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, Page
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    print("⚠️  Playwright未安裝。請執行：pip install playwright && playwright install")

logger = logging.getLogger('playwright_scraper')
handler = logging.FileHandler('logs/playwright_scraper.log', encoding='utf-8')
handler.setFormatter(logging.Formatter('%(asctime)s - PLAYWRIGHT - %(levelname)s - %(message)s'))
logger.addHandler(handler)
logger.setLevel(logging.INFO)

@dataclass
class LotteryResult:
    """彩票開獎結果數據結構"""
    lottery_type: str
    period: int
    draw_date: str
    main_numbers: List[int]
    special_number: Optional[int] = None
    scraped_at: str = ""

class PlaywrightTaiwanLotteryScraper:
    """使用Playwright的台灣彩券爬蟲"""
    
    def __init__(self):
        self.base_url = "https://www.taiwanlottery.com/lotto/lotto_lastest_result/"
        self.browser = None
        self.page = None
        
    async def initialize(self):
        """初始化Playwright瀏覽器"""
        if not PLAYWRIGHT_AVAILABLE:
            raise ImportError("Playwright未安裝")
            
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=True,  # 無頭模式
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        self.page = await self.browser.new_page()
        
        # 設置用戶代理
        await self.page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        })
        
        logger.info("Playwright瀏覽器初始化完成")
    
    async def close(self):
        """關閉瀏覽器"""
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()
        logger.info("Playwright瀏覽器已關閉")
    
    async def scrape_latest_results(self) -> List[LotteryResult]:
        """爬取最新開獎結果"""
        if not self.browser:
            await self.initialize()
        
        results = []
        
        try:
            logger.info(f"正在訪問: {self.base_url}")
            
            # 訪問頁面
            await self.page.goto(self.base_url, wait_until='networkidle')
            
            # 等待JavaScript載入內容
            await self.page.wait_for_timeout(3000)  # 等待3秒讓JS執行
            
            # 等待彩票結果容器載入
            try:
                await self.page.wait_for_selector('.lastest-result-container', timeout=10000)
                logger.info("頁面載入完成，開始解析數據")
            except Exception as e:
                logger.warning(f"等待頁面元素超時: {e}")
            
            # 爬取威力彩
            powercolor_result = await self._scrape_powercolor()
            if powercolor_result:
                results.append(powercolor_result)
            
            # 爬取大樂透
            lotto649_result = await self._scrape_lotto649()
            if lotto649_result:
                results.append(lotto649_result)
            
            # 爬取今彩539
            dailycash_result = await self._scrape_dailycash()
            if dailycash_result:
                results.append(dailycash_result)
            
            logger.info(f"成功爬取 {len(results)} 個彩票結果")
            
        except Exception as e:
            logger.error(f"爬取過程中發生錯誤: {e}")
            
        return results
    
    async def _scrape_powercolor(self) -> Optional[LotteryResult]:
        """爬取威力彩數據"""
        try:
            # 點擊威力彩標籤（如果存在）
            powercolor_tab = await self.page.query_selector('img[alt*="威力彩"], .select-item[data-lottery="powercolor"]')
            if powercolor_tab:
                await powercolor_tab.click()
                await self.page.wait_for_timeout(2000)
            
            # 查找威力彩容器
            container = await self.page.query_selector('#powercolor, .lastest-result-container:has-text("威力彩")')
            if not container:
                logger.warning("未找到威力彩容器")
                return None
            
            # 提取期號
            period_element = await container.query_selector('.period-date-item .content, [class*="period"]')
            period = None
            if period_element:
                period_text = await period_element.text_content()
                period_match = re.search(r'(\d+)', period_text or '')
                if period_match:
                    period = int(period_match.group(1))
            
            # 提取開獎日期
            date_element = await container.query_selector('.period-date-item:has-text("開獎日期") .content, [class*="date"]')
            draw_date = None
            if date_element:
                draw_date = await date_element.text_content()
            
            # 提取號碼
            number_elements = await container.query_selector_all('.ball:not(.color-super)')
            main_numbers = []
            for element in number_elements:
                number_text = await element.text_content()
                if number_text and number_text.isdigit():
                    main_numbers.append(int(number_text))
            
            # 提取第二區號碼
            special_element = await container.query_selector('.ball.color-super, .ball.secondary-area')
            special_number = None
            if special_element:
                special_text = await special_element.text_content()
                if special_text and special_text.isdigit():
                    special_number = int(special_text)
            
            if period and main_numbers:
                logger.info(f"成功解析威力彩: 期號{period}, 號碼{main_numbers}, 第二區{special_number}")
                return LotteryResult(
                    lottery_type="powercolor",
                    period=period,
                    draw_date=draw_date or "",
                    main_numbers=main_numbers,
                    special_number=special_number,
                    scraped_at=datetime.now().isoformat()
                )
            
        except Exception as e:
            logger.error(f"威力彩爬取失敗: {e}")
        
        return None
    
    async def _scrape_lotto649(self) -> Optional[LotteryResult]:
        """爬取大樂透數據"""
        try:
            # 點擊大樂透標籤
            lotto_tab = await self.page.query_selector('img[alt*="大樂透"], .select-item[data-lottery="lotto649"]')
            if lotto_tab:
                await lotto_tab.click()
                await self.page.wait_for_timeout(2000)
            
            # 查找大樂透容器
            container = await self.page.query_selector('#super_lotto638, .lastest-result-container:has-text("大樂透")')
            if not container:
                logger.warning("未找到大樂透容器")
                return None
            
            # 提取數據的邏輯與威力彩類似
            period_element = await container.query_selector('.period-date-item .content')
            period = None
            if period_element:
                period_text = await period_element.text_content()
                period_match = re.search(r'(\d+)', period_text or '')
                if period_match:
                    period = int(period_match.group(1))
            
            # 提取號碼
            number_elements = await container.query_selector_all('.ball:not(.special-number)')
            main_numbers = []
            for element in number_elements:
                number_text = await element.text_content()
                if number_text and number_text.isdigit():
                    main_numbers.append(int(number_text))
            
            # 提取特別號
            special_element = await container.query_selector('.ball.special-number')
            special_number = None
            if special_element:
                special_text = await special_element.text_content()
                if special_text and special_text.isdigit():
                    special_number = int(special_text)
            
            if period and main_numbers:
                logger.info(f"成功解析大樂透: 期號{period}, 號碼{main_numbers[:6]}, 特別號{special_number}")
                return LotteryResult(
                    lottery_type="lotto649",
                    period=period,
                    draw_date="",
                    main_numbers=main_numbers[:6],  # 只取前6個
                    special_number=special_number,
                    scraped_at=datetime.now().isoformat()
                )
            
        except Exception as e:
            logger.error(f"大樂透爬取失敗: {e}")
        
        return None
    
    async def _scrape_dailycash(self) -> Optional[LotteryResult]:
        """爬取今彩539數據"""
        try:
            # 點擊今彩539標籤
            dailycash_tab = await self.page.query_selector('img[alt*="今彩539"], .select-item[data-lottery="dailycash"]')
            if dailycash_tab:
                await dailycash_tab.click()
                await self.page.wait_for_timeout(2000)
            
            container = await self.page.query_selector('#daily_cash, .lastest-result-container:has-text("今彩539")')
            if not container:
                logger.warning("未找到今彩539容器")
                return None
            
            # 提取期號和號碼
            period_element = await container.query_selector('.period-date-item .content')
            period = None
            if period_element:
                period_text = await period_element.text_content()
                period_match = re.search(r'(\d+)', period_text or '')
                if period_match:
                    period = int(period_match.group(1))
            
            number_elements = await container.query_selector_all('.ball')
            main_numbers = []
            for element in number_elements:
                number_text = await element.text_content()
                if number_text and number_text.isdigit():
                    main_numbers.append(int(number_text))
            
            if period and main_numbers:
                logger.info(f"成功解析今彩539: 期號{period}, 號碼{main_numbers[:5]}")
                return LotteryResult(
                    lottery_type="dailycash",
                    period=period,
                    draw_date="",
                    main_numbers=main_numbers[:5],  # 只取前5個
                    scraped_at=datetime.now().isoformat()
                )
            
        except Exception as e:
            logger.error(f"今彩539爬取失敗: {e}")
        
        return None

# 測試函數
async def test_scraper():
    """測試爬蟲功能"""
    if not PLAYWRIGHT_AVAILABLE:
        print("❌ Playwright未安裝，無法測試")
        return
    
    scraper = PlaywrightTaiwanLotteryScraper()
    
    try:
        print("🚀 開始測試Playwright爬蟲...")
        results = await scraper.scrape_latest_results()
        
        print(f"\n✅ 測試完成！共爬取到 {len(results)} 個結果：")
        for result in results:
            print(f"  📊 {result.lottery_type}: 期號 {result.period}, 號碼 {result.main_numbers}")
            if result.special_number:
                print(f"      特別號/第二區: {result.special_number}")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
    finally:
        await scraper.close()

if __name__ == "__main__":
    # 運行測試
    if PLAYWRIGHT_AVAILABLE:
        asyncio.run(test_scraper())
    else:
        print("請先安裝Playwright: pip install playwright && playwright install")