#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
預測記錄管理器
負責管理預測記錄的清空、重置和維護
"""

import logging
import sqlite3
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import os

logger = logging.getLogger('prediction_manager')

class PredictionManager:
    """預測記錄管理器"""
    
    def __init__(self, db_manager=None):
        self.db_manager = db_manager
        
        # 預測表配置
        self.prediction_tables = {
            'powercolor': 'PowercolorPredictions',
            'lotto649': 'Lotto649Predictions', 
            'dailycash': 'DailyCashPredictions'
        }
    
    def clear_all_predictions(self) -> Dict[str, bool]:
        """清空所有彩票類型的預測記錄
        
        Returns:
            Dict: 每種彩票類型的清空結果
        """
        logger.info("開始清空所有預測記錄...")
        
        results = {}
        
        for lottery_type, table_name in self.prediction_tables.items():
            try:
                success = self.clear_predictions_by_type(lottery_type)
                results[lottery_type] = success
                
                if success:
                    logger.info(f"成功清空 {lottery_type} 預測記錄")
                else:
                    logger.error(f"清空 {lottery_type} 預測記錄失敗")
                    
            except Exception as e:
                logger.error(f"清空 {lottery_type} 預測記錄時發生錯誤: {str(e)}")
                results[lottery_type] = False
        
        # 記錄清空操作
        self._log_clear_operation(results)
        
        logger.info("預測記錄清空操作完成")
        return results
    
    def clear_predictions_by_type(self, lottery_type: str) -> bool:
        """清空指定彩票類型的預測記錄
        
        Args:
            lottery_type: 彩票類型
            
        Returns:
            bool: 是否清空成功
        """
        if lottery_type not in self.prediction_tables:
            logger.error(f"不支援的彩票類型: {lottery_type}")
            return False
            
        if not self.db_manager:
            logger.error("資料庫管理器未初始化")
            return False
            
        table_name = self.prediction_tables[lottery_type]
        
        try:
            conn = self.db_manager.create_connection()
            cursor = conn.cursor()
            
            # 備份現有記錄數量
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            record_count = cursor.fetchone()[0]
            
            if record_count > 0:
                logger.info(f"準備清空 {lottery_type} 的 {record_count} 條預測記錄")
                
                # 執行清空操作
                cursor.execute(f"DELETE FROM {table_name}")
                
                # 重置自增ID
                cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{table_name}'")
                
                conn.commit()
                
                logger.info(f"成功清空 {lottery_type} 的 {record_count} 條記錄")
            else:
                logger.info(f"{lottery_type} 預測表已為空，無需清空")
            
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"清空 {lottery_type} 預測記錄時發生錯誤: {str(e)}")
            return False
    
    def backup_predictions_before_clear(self) -> bool:
        """在清空前備份預測記錄
        
        Returns:
            bool: 是否備份成功
        """
        logger.info("開始備份預測記錄...")
        
        try:
            if not self.db_manager:
                logger.error("資料庫管理器未初始化")
                return False
                
            backup_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_dir = os.path.join(os.path.dirname(self.db_manager.db_path), 'backups')
            
            # 確保備份目錄存在
            os.makedirs(backup_dir, exist_ok=True)
            
            backup_path = os.path.join(backup_dir, f'predictions_backup_{backup_timestamp}.db')
            
            conn = self.db_manager.create_connection()
            
            # 創建備份數據庫連接
            backup_conn = sqlite3.connect(backup_path)
            
            # 備份整個數據庫
            conn.backup(backup_conn)
            
            backup_conn.close()
            conn.close()
            
            logger.info(f"預測記錄備份完成: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"備份預測記錄時發生錯誤: {str(e)}")
            return False
    
    def get_prediction_statistics(self) -> Dict[str, Dict]:
        """獲取預測記錄統計信息
        
        Returns:
            Dict: 各彩票類型的預測統計
        """
        statistics = {}
        
        for lottery_type, table_name in self.prediction_tables.items():
            try:
                stats = self._get_table_statistics(table_name, lottery_type)
                statistics[lottery_type] = stats
                
            except Exception as e:
                logger.error(f"獲取 {lottery_type} 統計信息時發生錯誤: {str(e)}")
                statistics[lottery_type] = {
                    'total_records': 0,
                    'error': str(e)
                }
        
        return statistics
    
    def _get_table_statistics(self, table_name: str, lottery_type: str) -> Dict:
        """獲取單個表的統計信息
        
        Args:
            table_name: 表名
            lottery_type: 彩票類型
            
        Returns:
            Dict: 統計信息
        """
        if not self.db_manager:
            return {'total_records': 0, 'error': '資料庫管理器未初始化'}
            
        try:
            conn = self.db_manager.create_connection()
            cursor = conn.cursor()
            
            # 總記錄數
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            total_records = cursor.fetchone()[0]
            
            # 最早和最晚的預測日期
            cursor.execute(f"SELECT MIN(PredictionDate), MAX(PredictionDate) FROM {table_name}")
            date_range = cursor.fetchone()
            
            # 平均信心度
            cursor.execute(f"SELECT AVG(Confidence) FROM {table_name} WHERE Confidence IS NOT NULL")
            avg_confidence = cursor.fetchone()[0]
            
            # 匹配統計
            cursor.execute(f"""
                SELECT MatchCount, COUNT(*) 
                FROM {table_name} 
                WHERE MatchCount IS NOT NULL 
                GROUP BY MatchCount 
                ORDER BY MatchCount
            """)
            match_distribution = dict(cursor.fetchall())
            
            conn.close()
            
            return {
                'total_records': total_records,
                'date_range': {
                    'earliest': date_range[0],
                    'latest': date_range[1]
                },
                'average_confidence': round(avg_confidence, 3) if avg_confidence else None,
                'match_distribution': match_distribution
            }
            
        except Exception as e:
            logger.error(f"獲取表 {table_name} 統計信息時發生錯誤: {str(e)}")
            return {'total_records': 0, 'error': str(e)}
    
    def _log_clear_operation(self, results: Dict[str, bool]):
        """記錄清空操作到日誌
        
        Args:
            results: 清空操作結果
        """
        try:
            if not self.db_manager:
                return
                
            conn = self.db_manager.create_connection()
            cursor = conn.cursor()
            
            # 確保操作日誌表存在
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS PredictionOperationLog (
                    ID INTEGER PRIMARY KEY AUTOINCREMENT,
                    Operation TEXT NOT NULL,
                    OperationTime TEXT NOT NULL,
                    Details TEXT,
                    Success INTEGER NOT NULL
                )
            """)
            
            # 記錄清空操作
            operation_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            details = f"清空結果: {results}"
            success = 1 if all(results.values()) else 0
            
            cursor.execute("""
                INSERT INTO PredictionOperationLog 
                (Operation, OperationTime, Details, Success)
                VALUES (?, ?, ?, ?)
            """, ('clear_predictions', operation_time, details, success))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"記錄清空操作時發生錯誤: {str(e)}")
    
    def reset_prediction_system(self) -> Dict[str, any]:
        """重置整個預測系統
        包括清空預測記錄、重置序號等
        
        Returns:
            Dict: 重置操作結果
        """
        logger.info("開始重置預測系統...")
        
        result = {
            'backup_success': False,
            'clear_success': {},
            'reset_success': True,
            'message': '',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        try:
            # 1. 備份現有數據
            backup_success = self.backup_predictions_before_clear()
            result['backup_success'] = backup_success
            
            if backup_success:
                logger.info("數據備份完成")
            else:
                logger.warning("數據備份失敗，但繼續執行清空操作")
            
            # 2. 清空預測記錄
            clear_results = self.clear_all_predictions()
            result['clear_success'] = clear_results
            
            # 3. 檢查結果
            all_cleared = all(clear_results.values())
            
            if all_cleared:
                result['message'] = '預測系統重置成功'
                logger.info("預測系統重置成功")
            else:
                result['reset_success'] = False
                failed_types = [k for k, v in clear_results.items() if not v]
                result['message'] = f'部分類型清空失敗: {failed_types}'
                logger.warning(f"部分彩票類型清空失敗: {failed_types}")
            
            return result
            
        except Exception as e:
            logger.error(f"重置預測系統時發生錯誤: {str(e)}")
            result['reset_success'] = False
            result['message'] = f'重置失敗: {str(e)}'
            return result

if __name__ == "__main__":
    # 測試代碼
    from db_manager import DBManager
    
    db_manager = DBManager()
    pred_manager = PredictionManager(db_manager)
    
    # 顯示當前統計
    print("當前預測記錄統計:")
    stats = pred_manager.get_prediction_statistics()
    for lottery_type, stat in stats.items():
        print(f"{lottery_type}: {stat}")
    
    # 重置系統
    print("\n重置預測系統...")
    reset_result = pred_manager.reset_prediction_system()
    print(f"重置結果: {reset_result}")