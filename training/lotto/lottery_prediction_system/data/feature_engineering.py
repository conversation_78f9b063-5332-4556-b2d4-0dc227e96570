"""
彩票預測系統 - 特徵工程模組
負責從原始數據中提取和創建特徵，支援威力彩、大樂透和今彩539
"""

import pandas as pd
import numpy as np
from collections import Counter
import logging
import time


# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/feature_engineering.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('feature_engineering')

class FeatureEngineer:
    def __init__(self):
        """初始化特徵工程器"""
        # 各類彩票的號碼範圍
        self.number_ranges = {
            'powercolor': {
                'first_area': (1, 38),
                'second_area': (1, 8)
            },
            'lotto649': {
                'first_area': (1, 49),
                'special': (1, 49)
            },
            'dailycash': {
                'numbers': (1, 39)
            }
        }
    
    def create_basic_features(self, df, lottery_type='powercolor'):
        """從原始數據中創建基本特徵
        
        Args:
            df: 歷史數據DataFrame
            lottery_type: 彩票類型 ('powercolor'=威力彩, 'lotto649'=大樂透, 'dailycash'=今彩539)
            
        Returns:
            DataFrame: 包含特徵的DataFrame
        """
        logger_text = f"開始為{self._get_lottery_name(lottery_type)}創建基本特徵..."
        logger.info(logger_text)
        print(logger_text)  # 同時在控制台顯示
        start_time = time.time()
        
        features = []
        total_rows = len(df)
        
        # 決定歷史資料的回溯期數
        lookback = 10
        if total_rows < lookback + 1:
            logger.warning(f"歷史資料不足 {lookback} 期，將使用可用的最大期數")
            lookback = max(1, total_rows - 1)
        
        for idx, (i, row) in enumerate(df.iterrows()):
            # 每處理100行輸出一次進度
            if idx % 100 == 0:
                progress = idx / total_rows * 100
                elapsed = time.time() - start_time
                logger.info(f"特徵處理進度: {progress:.1f}% ({idx}/{total_rows}) - 耗時: {elapsed:.1f}秒")
                
            # 跳過前lookback行，因為我們需要歷史數據來創建特徵
            if idx < lookback:
                continue
                
            # 基本特徵 - 所有彩票類型通用
            feature = {
                'Period': row['Period'],
                'Year': row['Sdate'].year,
                'Month': row['Sdate'].month,
                'Day': row['Sdate'].day,
                'DayOfWeek': row['Sdate'].dayofweek,
                'DayOfYear': row['Sdate'].dayofyear
            }
            
            # 根據彩票類型添加特徵
            if lottery_type.lower() == 'powercolor':
                # 威力彩特徵
                self._add_powercolor_features(df, idx, lookback, row, feature)
            elif lottery_type.lower() == 'lotto649':
                # 大樂透特徵
                self._add_lotto649_features(df, idx, lookback, row, feature)
            elif lottery_type.lower() == 'dailycash':
                # 今彩539特徵
                self._add_dailycash_features(df, idx, lookback, row, feature)
            else:
                raise ValueError(f"不支援的彩票類型: {lottery_type}")
            
            features.append(feature)
            
        # 轉換為DataFrame
        features_df = pd.DataFrame(features)
        
        elapsed = time.time() - start_time
        logger.info(f"基本特徵創建完成! 總共創建 {len(features_df)} 筆特徵數據，共 {len(features_df.columns)} 個特徵。")
        logger.info(f"特徵處理總耗時: {elapsed:.1f}秒")
        
        return features_df
    
    def _add_powercolor_features(self, df, idx, lookback, row, feature):
        """添加威力彩特徵"""
        # 添加過去lookback期的號碼特徵
        for j in range(1, lookback + 1):
            if idx - j >= 0:  # 確保索引不會越界
                prev_row = df.iloc[idx-j]
                feature[f'Prev{j}_A1'] = prev_row['Anumber1']
                feature[f'Prev{j}_A2'] = prev_row['Anumber2']
                feature[f'Prev{j}_A3'] = prev_row['Anumber3']
                feature[f'Prev{j}_A4'] = prev_row['Anumber4']
                feature[f'Prev{j}_A5'] = prev_row['Anumber5']
                feature[f'Prev{j}_A6'] = prev_row['Anumber6']
                feature[f'Prev{j}_S'] = prev_row['Second_district']
        
        # 計算過去lookback期的統計特徵
        prev_numbers = []
        prev_second = []
        
        for j in range(1, lookback + 1):
            if idx - j >= 0:  # 確保索引不會越界
                prev_row = df.iloc[idx-j]
                prev_numbers.extend([
                    prev_row['Anumber1'], 
                    prev_row['Anumber2'], 
                    prev_row['Anumber3'], 
                    prev_row['Anumber4'], 
                    prev_row['Anumber5'], 
                    prev_row['Anumber6']
                ])
                prev_second.append(prev_row['Second_district'])
        
        # 計算各號碼出現頻率
        counter = Counter(prev_numbers)
        for num in range(1, 39):  # 第一區號碼範圍1-38
            feature[f'Freq_A{num}'] = counter[num]
            
        counter_second = Counter(prev_second)
        for num in range(1, 9):  # 第二區號碼範圍1-8
            feature[f'Freq_S{num}'] = counter_second[num]
            
        # 計算統計特徵
        feature['Mean_First'] = np.mean(prev_numbers)
        feature['Std_First'] = np.std(prev_numbers)
        feature['Mean_Second'] = np.mean(prev_second)
        feature['Std_Second'] = np.std(prev_second)
        
        # 添加目標變量（本期號碼）
        feature['Target_A1'] = row['Anumber1']
        feature['Target_A2'] = row['Anumber2']
        feature['Target_A3'] = row['Anumber3']
        feature['Target_A4'] = row['Anumber4']
        feature['Target_A5'] = row['Anumber5']
        feature['Target_A6'] = row['Anumber6']
        feature['Target_S'] = row['Second_district']
        
    def _add_lotto649_features(self, df, idx, lookback, row, feature):
        """添加大樂透特徵"""
        # 添加過去lookback期的號碼特徵
        for j in range(1, lookback + 1):
            if idx - j >= 0:  # 確保索引不會越界
                prev_row = df.iloc[idx-j]
                feature[f'Prev{j}_A1'] = prev_row['Anumber1']
                feature[f'Prev{j}_A2'] = prev_row['Anumber2']
                feature[f'Prev{j}_A3'] = prev_row['Anumber3']
                feature[f'Prev{j}_A4'] = prev_row['Anumber4']
                feature[f'Prev{j}_A5'] = prev_row['Anumber5']
                feature[f'Prev{j}_A6'] = prev_row['Anumber6']
                feature[f'Prev{j}_Special'] = prev_row['SpecialNumber']
        
        # 計算過去lookback期的統計特徵
        prev_numbers = []
        prev_special = []
        
        for j in range(1, lookback + 1):
            if idx - j >= 0:  # 確保索引不會越界
                prev_row = df.iloc[idx-j]
                prev_numbers.extend([
                    prev_row['Anumber1'], 
                    prev_row['Anumber2'], 
                    prev_row['Anumber3'], 
                    prev_row['Anumber4'], 
                    prev_row['Anumber5'], 
                    prev_row['Anumber6']
                ])
                prev_special.append(prev_row['SpecialNumber'])
        
        # 計算各號碼出現頻率
        counter = Counter(prev_numbers)
        for num in range(1, 50):  # 第一區號碼範圍1-49
            feature[f'Freq_A{num}'] = counter[num]
            
        counter_special = Counter(prev_special)
        for num in range(1, 50):  # 特別號範圍1-49
            feature[f'Freq_Special{num}'] = counter_special[num]
            
        # 計算統計特徵
        feature['Mean_First'] = np.mean(prev_numbers)
        feature['Std_First'] = np.std(prev_numbers)
        feature['Mean_Special'] = np.mean(prev_special)
        feature['Std_Special'] = np.std(prev_special)
        
        # 添加目標變量（本期號碼）
        feature['Target_A1'] = row['Anumber1']
        feature['Target_A2'] = row['Anumber2']
        feature['Target_A3'] = row['Anumber3']
        feature['Target_A4'] = row['Anumber4']
        feature['Target_A5'] = row['Anumber5']
        feature['Target_A6'] = row['Anumber6']
        feature['Target_Special'] = row['SpecialNumber']
    
    def _add_dailycash_features(self, df, idx, lookback, row, feature):
        """添加今彩539特徵"""
        # 添加過去lookback期的號碼特徵
        for j in range(1, lookback + 1):
            if idx - j >= 0:  # 確保索引不會越界
                prev_row = df.iloc[idx-j]
                feature[f'Prev{j}_A1'] = prev_row['Anumber1']
                feature[f'Prev{j}_A2'] = prev_row['Anumber2']
                feature[f'Prev{j}_A3'] = prev_row['Anumber3']
                feature[f'Prev{j}_A4'] = prev_row['Anumber4']
                feature[f'Prev{j}_A5'] = prev_row['Anumber5']
        
        # 計算過去lookback期的統計特徵
        prev_numbers = []
        
        for j in range(1, lookback + 1):
            if idx - j >= 0:  # 確保索引不會越界
                prev_row = df.iloc[idx-j]
                prev_numbers.extend([
                    prev_row['Anumber1'], 
                    prev_row['Anumber2'], 
                    prev_row['Anumber3'], 
                    prev_row['Anumber4'], 
                    prev_row['Anumber5']
                ])
        
        # 計算各號碼出現頻率
        counter = Counter(prev_numbers)
        for num in range(1, 40):  # 號碼範圍1-39
            feature[f'Freq_A{num}'] = counter[num]
            
        # 計算統計特徵
        feature['Mean_Numbers'] = np.mean(prev_numbers)
        feature['Std_Numbers'] = np.std(prev_numbers)
        
        # 添加目標變量（本期號碼）
        feature['Target_A1'] = row['Anumber1']
        feature['Target_A2'] = row['Anumber2']
        feature['Target_A3'] = row['Anumber3']
        feature['Target_A4'] = row['Anumber4']
        feature['Target_A5'] = row['Anumber5']
    
    def create_advanced_features(self, df, features_df, lottery_type='powercolor'):
        """創建進階特徵
        
        Args:
            df: 原始歷史數據DataFrame
            features_df: 基本特徵DataFrame
            lottery_type: 彩票類型 ('powercolor'=威力彩, 'lotto649'=大樂透, 'dailycash'=今彩539)
            
        Returns:
            DataFrame: 包含基本特徵和進階特徵的DataFrame
        """
        logger_text = f"開始為{self._get_lottery_name(lottery_type)}創建進階特徵..."
        logger.info(logger_text)
        print(logger_text)  # 同時在控制台顯示
        start_time = time.time()
        
        # 新創建的特徵將被存儲在這個DataFrame中
        advanced_features = pd.DataFrame(index=features_df.index)
        
        # 根據彩票類型創建特定的進階特徵
        if lottery_type.lower() == 'powercolor':
            self._create_powercolor_advanced_features(features_df, advanced_features)
        elif lottery_type.lower() == 'lotto649':
            self._create_lotto649_advanced_features(features_df, advanced_features)
        elif lottery_type.lower() == 'dailycash':
            self._create_dailycash_advanced_features(features_df, advanced_features)
        else:
            raise ValueError(f"不支援的彩票類型: {lottery_type}")
        
        # 將進階特徵與基本特徵合併
        combined_features = pd.concat([features_df, advanced_features], axis=1)
        
        elapsed = time.time() - start_time
        logger.info(f"進階特徵創建完成! 新增 {len(advanced_features.columns)} 個特徵。")
        logger.info(f"進階特徵處理總耗時: {elapsed:.1f}秒")
        
        return combined_features
    
    def _create_powercolor_advanced_features(self, features_df, advanced_features):
        """創建威力彩的進階特徵"""
        # 1. 奇偶比例特徵
        for i, row in features_df.iterrows():
            # 計算過去10期奇數與偶數的比例
            odd_count = 0
            even_count = 0
            
            for j in range(1, 11):
                for pos in range(1, 7):
                    col_name = f'Prev{j}_A{pos}'
                    if col_name in row:
                        num = row[col_name]
                        if num % 2 == 1:
                            odd_count += 1
                        else:
                            even_count += 1
            
            total = odd_count + even_count
            if total > 0:
                advanced_features.loc[i, 'OddEvenRatio'] = odd_count / total
            else:
                advanced_features.loc[i, 'OddEvenRatio'] = 0.5
                
        # 2. 大小數比例 (大於19的視為大數)
        for i, row in features_df.iterrows():
            big_count = 0
            small_count = 0
            
            for j in range(1, 11):
                for pos in range(1, 7):
                    col_name = f'Prev{j}_A{pos}'
                    if col_name in row:
                        num = row[col_name]
                        if num > 19:
                            big_count += 1
                        else:
                            small_count += 1
            
            total = big_count + small_count
            if total > 0:
                advanced_features.loc[i, 'BigSmallRatio'] = big_count / total
            else:
                advanced_features.loc[i, 'BigSmallRatio'] = 0.5
                
        # 3. 連續數特徵 (檢測每期是否有連續的號碼)
        for i, row in features_df.iterrows():
            for j in range(1, 11):
                nums = []
                for pos in range(1, 7):
                    col_name = f'Prev{j}_A{pos}'
                    if col_name in row:
                        nums.append(row[col_name])
                
                nums = [x for x in nums if not pd.isna(x)]
                if len(nums) >= 2:
                    nums.sort()
                    has_consecutive = False
                    
                    for k in range(len(nums)-1):
                        if nums[k+1] - nums[k] == 1:
                            has_consecutive = True
                            break
                    
                    advanced_features.loc[i, f'HasConsecutive_{j}'] = 1 if has_consecutive else 0
                else:
                    advanced_features.loc[i, f'HasConsecutive_{j}'] = 0
        
        # 4. 和值特徵 (每期號碼的總和)
        for i, row in features_df.iterrows():
            for j in range(1, 11):
                sum_val = 0
                count = 0
                for pos in range(1, 7):
                    col_name = f'Prev{j}_A{pos}'
                    if col_name in row and not pd.isna(row[col_name]):
                        sum_val += row[col_name]
                        count += 1
                
                if count > 0:
                    advanced_features.loc[i, f'Sum_{j}'] = sum_val
                else:
                    advanced_features.loc[i, f'Sum_{j}'] = 0
                    
            # 過去10期的平均和值
            sums = [advanced_features.loc[i, f'Sum_{j}'] for j in range(1, 11) if f'Sum_{j}' in advanced_features.columns]
            if sums:
                advanced_features.loc[i, 'AvgSum'] = sum(sums) / len(sums)
            else:
                advanced_features.loc[i, 'AvgSum'] = 0
                
        # 5. 區間分佈特徵 (將1-38分為幾個區間，計算每個區間的號碼數量)
        intervals = [(1, 10), (11, 20), (21, 30), (31, 38)]
        
        for i, row in features_df.iterrows():
            interval_counts = {f'Interval_{start}_{end}': 0 for start, end in intervals}
            total_count = 0
            
            for j in range(1, 11):
                for pos in range(1, 7):
                    col_name = f'Prev{j}_A{pos}'
                    if col_name in row and not pd.isna(row[col_name]):
                        num = row[col_name]
                        for start, end in intervals:
                            if start <= num <= end:
                                interval_counts[f'Interval_{start}_{end}'] += 1
                                total_count += 1
            
            # 將區間計數添加為特徵
            for key, value in interval_counts.items():
                if total_count > 0:
                    advanced_features.loc[i, key] = value / total_count  # 標準化
                else:
                    advanced_features.loc[i, key] = 0
                    
        # 6. 第二區特徵
        for i, row in features_df.iterrows():
            second_nums = []
            for j in range(1, 11):
                col_name = f'Prev{j}_S'
                if col_name in row and not pd.isna(row[col_name]):
                    second_nums.append(row[col_name])
            
            if second_nums:
                # 計算第二區的統計特徵
                advanced_features.loc[i, 'Second_Mean'] = np.mean(second_nums)
                advanced_features.loc[i, 'Second_Std'] = np.std(second_nums) if len(second_nums) > 1 else 0
                advanced_features.loc[i, 'Second_Min'] = min(second_nums)
                advanced_features.loc[i, 'Second_Max'] = max(second_nums)
                
                # 奇偶比例
                odd_count = sum(1 for num in second_nums if num % 2 == 1)
                advanced_features.loc[i, 'Second_OddEvenRatio'] = odd_count / len(second_nums)
                
                # 重複情況
                counter = Counter(second_nums)
                most_common = counter.most_common(1)
                if most_common:
                    advanced_features.loc[i, 'Second_MostCommon'] = most_common[0][0]
                    advanced_features.loc[i, 'Second_MostCommonCount'] = most_common[0][1]
                else:
                    advanced_features.loc[i, 'Second_MostCommon'] = 0
                    advanced_features.loc[i, 'Second_MostCommonCount'] = 0
    
    def _create_lotto649_advanced_features(self, features_df, advanced_features):
        """創建大樂透的進階特徵"""
        # 1. 奇偶比例特徵
        for i, row in features_df.iterrows():
            # 計算過去10期奇數與偶數的比例
            odd_count = 0
            even_count = 0
            
            for j in range(1, 11):
                for pos in range(1, 7):
                    col_name = f'Prev{j}_A{pos}'
                    if col_name in row and not pd.isna(row[col_name]):
                        num = row[col_name]
                        if num % 2 == 1:
                            odd_count += 1
                        else:
                            even_count += 1
            
            total = odd_count + even_count
            if total > 0:
                advanced_features.loc[i, 'OddEvenRatio'] = odd_count / total
            else:
                advanced_features.loc[i, 'OddEvenRatio'] = 0.5
                
        # 2. 大小數比例 (大於25的視為大數)
        for i, row in features_df.iterrows():
            big_count = 0
            small_count = 0
            
            for j in range(1, 11):
                for pos in range(1, 7):
                    col_name = f'Prev{j}_A{pos}'
                    if col_name in row and not pd.isna(row[col_name]):
                        num = row[col_name]
                        if num > 25:
                            big_count += 1
                        else:
                            small_count += 1
            
            total = big_count + small_count
            if total > 0:
                advanced_features.loc[i, 'BigSmallRatio'] = big_count / total
            else:
                advanced_features.loc[i, 'BigSmallRatio'] = 0.5
        
        # 3. 連續數特徵
        for i, row in features_df.iterrows():
            for j in range(1, 11):
                nums = []
                for pos in range(1, 7):
                    col_name = f'Prev{j}_A{pos}'
                    if col_name in row and not pd.isna(row[col_name]):
                        nums.append(row[col_name])
                
                if len(nums) >= 2:
                    nums.sort()
                    has_consecutive = False
                    
                    for k in range(len(nums)-1):
                        if nums[k+1] - nums[k] == 1:
                            has_consecutive = True
                            break
                    
                    advanced_features.loc[i, f'HasConsecutive_{j}'] = 1 if has_consecutive else 0
                else:
                    advanced_features.loc[i, f'HasConsecutive_{j}'] = 0
        
        # 4. 和值特徵
        for i, row in features_df.iterrows():
            for j in range(1, 11):
                sum_val = 0
                count = 0
                for pos in range(1, 7):
                    col_name = f'Prev{j}_A{pos}'
                    if col_name in row and not pd.isna(row[col_name]):
                        sum_val += row[col_name]
                        count += 1
                
                if count > 0:
                    advanced_features.loc[i, f'Sum_{j}'] = sum_val
                else:
                    advanced_features.loc[i, f'Sum_{j}'] = 0
                    
            # 過去10期的平均和值
            sums = [advanced_features.loc[i, f'Sum_{j}'] for j in range(1, 11) if f'Sum_{j}' in advanced_features.columns]
            if sums:
                advanced_features.loc[i, 'AvgSum'] = sum(sums) / len(sums)
            else:
                advanced_features.loc[i, 'AvgSum'] = 0
        
        # 5. 區間分佈特徵 (將1-49分為幾個區間)
        intervals = [(1, 10), (11, 20), (21, 30), (31, 40), (41, 49)]
        
        for i, row in features_df.iterrows():
            interval_counts = {f'Interval_{start}_{end}': 0 for start, end in intervals}
            total_count = 0
            
            for j in range(1, 11):
                for pos in range(1, 7):
                    col_name = f'Prev{j}_A{pos}'
                    if col_name in row and not pd.isna(row[col_name]):
                        num = row[col_name]
                        for start, end in intervals:
                            if start <= num <= end:
                                interval_counts[f'Interval_{start}_{end}'] += 1
                                total_count += 1
            
            # 將區間計數添加為特徵
            for key, value in interval_counts.items():
                if total_count > 0:
                    advanced_features.loc[i, key] = value / total_count  # 標準化
                else:
                    advanced_features.loc[i, key] = 0
        
        # 6. 特別號特徵
        for i, row in features_df.iterrows():
            special_nums = []
            for j in range(1, 11):
                col_name = f'Prev{j}_Special'
                if col_name in row and not pd.isna(row[col_name]):
                    special_nums.append(row[col_name])
            
            if special_nums:
                # 計算特別號的統計特徵
                advanced_features.loc[i, 'Special_Mean'] = np.mean(special_nums)
                advanced_features.loc[i, 'Special_Std'] = np.std(special_nums) if len(special_nums) > 1 else 0
                advanced_features.loc[i, 'Special_Min'] = min(special_nums)
                advanced_features.loc[i, 'Special_Max'] = max(special_nums)
                
                # 奇偶比例
                odd_count = sum(1 for num in special_nums if num % 2 == 1)
                advanced_features.loc[i, 'Special_OddEvenRatio'] = odd_count / len(special_nums)
                
                # 大小比例
                big_count = sum(1 for num in special_nums if num > 25)
                advanced_features.loc[i, 'Special_BigSmallRatio'] = big_count / len(special_nums)
                
                # 區間分佈
                for start, end in intervals:
                    count = sum(1 for num in special_nums if start <= num <= end)
                    advanced_features.loc[i, f'Special_Interval_{start}_{end}'] = count / len(special_nums)
    
    def _create_dailycash_advanced_features(self, features_df, advanced_features):
        """創建今彩539的進階特徵"""
        # 1. 奇偶比例特徵
        for i, row in features_df.iterrows():
            # 計算過去10期奇數與偶數的比例
            odd_count = 0
            even_count = 0
            
            for j in range(1, 11):
                for pos in range(1, 6):  # 今彩539為5個號碼
                    col_name = f'Prev{j}_A{pos}'
                    if col_name in row and not pd.isna(row[col_name]):
                        num = row[col_name]
                        if num % 2 == 1:
                            odd_count += 1
                        else:
                            even_count += 1
            
            total = odd_count + even_count
            if total > 0:
                advanced_features.loc[i, 'OddEvenRatio'] = odd_count / total
            else:
                advanced_features.loc[i, 'OddEvenRatio'] = 0.5
                
        # 2. 大小數比例 (大於20的視為大數)
        for i, row in features_df.iterrows():
            big_count = 0
            small_count = 0
            
            for j in range(1, 11):
                for pos in range(1, 6):
                    col_name = f'Prev{j}_A{pos}'
                    if col_name in row and not pd.isna(row[col_name]):
                        num = row[col_name]
                        if num > 20:
                            big_count += 1
                        else:
                            small_count += 1
            
            total = big_count + small_count
            if total > 0:
                advanced_features.loc[i, 'BigSmallRatio'] = big_count / total
            else:
                advanced_features.loc[i, 'BigSmallRatio'] = 0.5
                
        # 3. 連續數特徵
        for i, row in features_df.iterrows():
            for j in range(1, 11):
                nums = []
                for pos in range(1, 6):
                    col_name = f'Prev{j}_A{pos}'
                    if col_name in row and not pd.isna(row[col_name]):
                        nums.append(row[col_name])
                
                if len(nums) >= 2:
                    nums.sort()
                    has_consecutive = False
                    
                    for k in range(len(nums)-1):
                        if nums[k+1] - nums[k] == 1:
                            has_consecutive = True
                            break
                    
                    advanced_features.loc[i, f'HasConsecutive_{j}'] = 1 if has_consecutive else 0
                else:
                    advanced_features.loc[i, f'HasConsecutive_{j}'] = 0
        
        # 4. 和值特徵
        for i, row in features_df.iterrows():
            for j in range(1, 11):
                sum_val = 0
                count = 0
                for pos in range(1, 6):
                    col_name = f'Prev{j}_A{pos}'
                    if col_name in row and not pd.isna(row[col_name]):
                        sum_val += row[col_name]
                        count += 1
                
                if count > 0:
                    advanced_features.loc[i, f'Sum_{j}'] = sum_val
                else:
                    advanced_features.loc[i, f'Sum_{j}'] = 0
                    
            # 過去10期的平均和值
            sums = [advanced_features.loc[i, f'Sum_{j}'] for j in range(1, 11) if f'Sum_{j}' in advanced_features.columns]
            if sums:
                advanced_features.loc[i, 'AvgSum'] = sum(sums) / len(sums)
            else:
                advanced_features.loc[i, 'AvgSum'] = 0
                
        # 5. 區間分佈特徵 (將1-39分為幾個區間)
        intervals = [(1, 10), (11, 20), (21, 30), (31, 39)]
        
        for i, row in features_df.iterrows():
            interval_counts = {f'Interval_{start}_{end}': 0 for start, end in intervals}
            total_count = 0
            
            for j in range(1, 11):
                for pos in range(1, 6):
                    col_name = f'Prev{j}_A{pos}'
                    if col_name in row and not pd.isna(row[col_name]):
                        num = row[col_name]
                        for start, end in intervals:
                            if start <= num <= end:
                                interval_counts[f'Interval_{start}_{end}'] += 1
                                total_count += 1
            
            # 將區間計數添加為特徵
            for key, value in interval_counts.items():
                if total_count > 0:
                    advanced_features.loc[i, key] = value / total_count  # 標準化
                else:
                    advanced_features.loc[i, key] = 0
    
    def prepare_next_period_features(self, df, lottery_type='powercolor'):
        """準備下一期預測的特徵
        
        Args:
            df: 歷史數據DataFrame
            lottery_type: 彩票類型 ('powercolor'=威力彩, 'lotto649'=大樂透, 'dailycash'=今彩539)
            
        Returns:
            DataFrame: 下一期預測特徵
        """
        logger_text = f"正在準備下一期{self._get_lottery_name(lottery_type)}預測特徵..."
        logger.info(logger_text)
        print(logger_text)  # 同時在控制台顯示
        
        # 創建特徵
        basic_features = self.create_basic_features(df, lottery_type)
        all_features = self.create_advanced_features(df, basic_features, lottery_type)
        
        # 獲取最後一行數據，這將包含最近的歷史數據作為特徵
        next_features = all_features.iloc[-1:].copy()
        
        # 刪除目標列，因為這些是我們要預測的
        if lottery_type.lower() == 'powercolor':
            target_columns = ['Target_A1', 'Target_A2', 'Target_A3', 'Target_A4', 'Target_A5', 'Target_A6', 'Target_S', 'Period']
        elif lottery_type.lower() == 'lotto649':
            target_columns = ['Target_A1', 'Target_A2', 'Target_A3', 'Target_A4', 'Target_A5', 'Target_A6', 'Target_Special', 'Period']
        elif lottery_type.lower() == 'dailycash':
            target_columns = ['Target_A1', 'Target_A2', 'Target_A3', 'Target_A4', 'Target_A5', 'Period']
        else:
            raise ValueError(f"不支援的彩票類型: {lottery_type}")
        
        prediction_features = next_features.drop(target_columns, axis=1, errors='ignore')
        
        logger.info(f"已準備好下一期{self._get_lottery_name(lottery_type)}預測特徵，共 {prediction_features.shape[1]} 個特徵")
        return prediction_features
    
    def _get_lottery_name(self, lottery_type):
        """根據彩票類型獲取中文名稱"""
        name_map = {
            'powercolor': '威力彩',
            'lotto649': '大樂透',
            'dailycash': '今彩539'
        }
        return name_map.get(lottery_type.lower(), lottery_type)

# 測試代碼
if __name__ == "__main__":
    from db_manager import DBManager
    from config_manager import ConfigManager
    
    # 加載數據
    config_manager = ConfigManager()
    db = DBManager(config_manager)
    powercolor_df = db.load_lottery_data('powercolor')
    lotto649_df = db.load_lottery_data('lotto649')
    dailycash_df = db.load_lottery_data('dailycash')
    
    # 創建特徵
    fe = FeatureEngineer()
    
    # 威力彩特徵
    print("=== 威力彩特徵 ===")
    basic_features = fe.create_basic_features(powercolor_df, 'powercolor')
    all_features = fe.create_advanced_features(powercolor_df, basic_features, 'powercolor')
    print(f"基本特徵: {basic_features.shape}")
    print(f"所有特徵: {all_features.shape}")
    
    # 大樂透特徵
    print("\n=== 大樂透特徵 ===")
    basic_features = fe.create_basic_features(lotto649_df, 'lotto649')
    all_features = fe.create_advanced_features(lotto649_df, basic_features, 'lotto649')
    print(f"基本特徵: {basic_features.shape}")
    print(f"所有特徵: {all_features.shape}")
    
    # 今彩539特徵
    print("\n=== 今彩539特徵 ===")
    basic_features = fe.create_basic_features(dailycash_df, 'dailycash')
    all_features = fe.create_advanced_features(dailycash_df, basic_features, 'dailycash')
    print(f"基本特徵: {basic_features.shape}")
    print(f"所有特徵: {all_features.shape}")