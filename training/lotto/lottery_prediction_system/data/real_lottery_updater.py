#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真實彩票開獎數據自動更新器
負責從台灣彩券官方網站獲取最新開獎結果
"""

import logging
import requests
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import re
import sqlite3
import json
import time
from bs4 import BeautifulSoup
import os

# 只使用台灣彩券官方數據，不使用任何模擬數據
# 確保開獎結果的真實性和準確性

logger = logging.getLogger('real_lottery_updater')

class RealLotteryUpdater:
    """真實彩票開獎數據更新器"""
    
    def __init__(self, db_manager=None):
        self.db_manager = db_manager
        
        # 只使用台灣彩券官方數據源
        logger.info("初始化台灣彩券官方數據更新器 - 僅使用真實開獎數據")
        
        # 台灣彩券官方API端點 - 使用正確的官方網址
        self.apis = {
            'powercolor': {
                'urls': [
                    'https://www.taiwanlottery.com/api/lotto/powerball',
                    'https://www.taiwanlottery.com/result/powerball/list',
                    'https://www.taiwanlottery.com/lotto/powerball/history',
                    'https://www.taiwanlottery.com/api/lottery/results?game=powerball'
                ],
                'table_name': 'Powercolor',
                'expected_numbers': 6,
                'has_special': True
            },
            'lotto649': {
                'urls': [
                    'https://www.taiwanlottery.com/api/lotto/lotto649',
                    'https://www.taiwanlottery.com/result/lotto649/list',
                    'https://www.taiwanlottery.com/lotto/lotto649/history',
                    'https://www.taiwanlottery.com/api/lottery/results?game=lotto649'
                ],
                'table_name': 'Lotto649',
                'expected_numbers': 6,
                'has_special': True
            },
            'dailycash': {
                'urls': [
                    'https://www.taiwanlottery.com/api/lotto/dailycash',
                    'https://www.taiwanlottery.com/result/dailycash/list',
                    'https://www.taiwanlottery.com/lotto/dailycash/history',
                    'https://www.taiwanlottery.com/api/lottery/results?game=dailycash'
                ],
                'table_name': 'DailyCash',
                'expected_numbers': 5,
                'has_special': False
            }
        }
        
        # 創建session以提高連線穩定性
        self.session = requests.Session()
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.taiwanlottery.com/',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin'
        }
        
        # 設定session headers
        self.session.headers.update(self.headers)
        
        # 配置連線池和重試
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=3,  # 自動重試3次
            status_forcelist=[429, 500, 502, 503, 504],
            backoff_factor=2,  # 指數退避
            raise_on_status=False
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

    def fetch_latest_results(self, lottery_type: str) -> Optional[Dict]:
        """從台灣彩券官方網站獲取最新開獎結果
        
        Args:
            lottery_type: 彩票類型 ('powercolor', 'lotto649', 'dailycash')
            
        Returns:
            Dict: 最新開獎結果，如果獲取失敗返回None
        """
        if lottery_type not in self.apis:
            logger.error(f"不支援的彩票類型: {lottery_type}")
            return None
            
        api_config = self.apis[lottery_type]
        
        try:
            logger.info(f"正在獲取 {lottery_type} 最新開獎結果...")
            
            # 嘗試所有台灣彩券官方URL
            urls_to_try = api_config['urls']
            response = None
            last_error = None
            
            for url_index, url in enumerate(urls_to_try):
                logger.info(f"嘗試從台灣彩券官方來源 {url_index + 1}/{len(urls_to_try)} 獲取 {lottery_type} 數據...")
                
                # 每個URL重試5次，增加成功機率
                max_retries = 5
                for attempt in range(max_retries):
                    try:
                        # 使用session發送請求，使用較短的超時時間
                        response = self.session.get(
                            url,
                            timeout=(10, 20),  # 連線超時10秒，讀取超時20秒
                            allow_redirects=True,
                            verify=True
                        )
                        response.raise_for_status()
                        logger.info(f"✅ 成功從台灣彩券官方來源 {url_index + 1} 獲取 {lottery_type} 數據")
                        break  # 成功就跳出重試循環
                        
                    except Exception as e:
                        last_error = e
                        if attempt < max_retries - 1:
                            wait_time = (attempt + 1) * 3  # 等待3, 6, 9, 12, 15秒
                            logger.warning(f"⚠️ 第 {attempt + 1}/{max_retries} 次嘗試失敗，{wait_time}秒後重試: {str(e)[:100]}")
                            time.sleep(wait_time)
                            continue
                        else:
                            logger.error(f"❌ 來源 {url_index + 1} 所有嘗試失敗: {str(e)[:100]}")
                            break  # 嘗試下一個URL
                
                if response and response.status_code == 200:
                    break  # 成功獲取數據，跳出URL循環
                
            # 如果所有URL都失敗
            if not response or response.status_code != 200:
                logger.error(f"❌ 所有台灣彩券官方來源都無法獲取 {lottery_type} 數據")
                logger.error(f"最後錯誤: {str(last_error) if last_error else '未知錯誤'}")
                return None
            
            # 解析HTML回應
            content_type = response.headers.get('content-type', '').lower()
            
            if 'json' in content_type:
                # 如果是JSON格式
                try:
                    data = response.json()
                    if not data or 'content' not in data:
                        logger.warning(f"{lottery_type} API返回空JSON數據")
                        result = None
                    else:
                        latest = data['content'][0] if data['content'] else None
                        if not latest:
                            logger.warning(f"{lottery_type} JSON中沒有找到開獎數據")
                            result = None
                        else:
                            result = self._parse_lottery_result(latest, lottery_type, api_config)
                except Exception as e:
                    logger.warning(f"JSON解析失敗: {str(e)}")
                    result = None
            else:
                # 如果是HTML格式，解析HTML內容
                try:
                    result = self._parse_html_lottery_result(response.text, lottery_type, api_config)
                except Exception as e:
                    logger.warning(f"HTML解析失敗: {str(e)}")
                    result = None
            
            if result:
                logger.info(f"成功獲取 {lottery_type} 第 {result['period']} 期開獎結果")
                return result
            else:
                logger.error(f"解析 {lottery_type} 開獎結果失敗")
                return None
                
        except requests.RequestException as e:
            logger.error(f"❌ 獲取 {lottery_type} 開獎數據時網絡錯誤: {str(e)}")
            logger.error("❌ 無法從台灣彩券官方獲取數據")
            return None
        except Exception as e:
            logger.error(f"❌ 獲取 {lottery_type} 開獎數據時發生錯誤: {str(e)}")
            logger.error("❌ 只接受台灣彩券官方真實數據")
            return None
    
    def _parse_html_lottery_result(self, html_content: str, lottery_type: str, config: Dict) -> Optional[Dict]:
        """解析HTML格式的開獎結果
        
        Args:
            html_content: HTML內容
            lottery_type: 彩票類型
            config: API配置
            
        Returns:
            Dict: 解析後的開獎結果
        """
        try:
            from bs4 import BeautifulSoup
            import re
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 尋找包含開獎數據的元素
            # 常見的選擇器模式
            selectors_to_try = [
                # 尋找包含期號的元素
                '[class*="period"], [class*="draw"], [class*="term"]',
                # 尋找包含號碼的元素  
                '[class*="number"], [class*="ball"], [class*="result"]',
                # 尋找表格行
                'tr, .row, .item',
                # 尋找JSON數據
                'script[type="application/json"], script:contains("data"), script:contains("result")'
            ]
            
            # 嘗試從script標籤中提取JSON數據
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string:
                    # 尋找可能的JSON數據
                    json_match = re.search(r'(?:data|result|lottery)\s*[=:]\s*(\{.*?\}|\[.*?\])', script.string, re.DOTALL)
                    if json_match:
                        try:
                            import json
                            json_data = json.loads(json_match.group(1))
                            if isinstance(json_data, dict) and any(key in json_data for key in ['period', 'drawTerm', '期號']):
                                return self._parse_lottery_result(json_data, lottery_type, config)
                        except:
                            continue
            
            # 嘗試從HTML結構中提取數據
            # 尋找期號
            period_patterns = [
                r'(\d{9})', # 期號格式 114000058
                r'第\s*(\d+)\s*期',
                r'期數[：:]\s*(\d+)',
            ]
            
            period = None
            for pattern in period_patterns:
                match = re.search(pattern, html_content)
                if match:
                    period = match.group(1)
                    break
                    
            if not period:
                logger.warning(f"無法從HTML中提取期號")
                return None
                
            # 尋找號碼
            number_patterns = [
                r'(\d{1,2})[,，\s]+(\d{1,2})[,，\s]+(\d{1,2})[,，\s]+(\d{1,2})[,，\s]+(\d{1,2})[,，\s]+(\d{1,2})',
                r'號碼[：:]\s*(\d+(?:[,，\s]+\d+)*)',
            ]
            
            numbers = []
            for pattern in number_patterns:
                matches = re.findall(pattern, html_content)
                if matches:
                    if isinstance(matches[0], tuple):
                        numbers = [int(n) for n in matches[0] if n.isdigit()]
                    else:
                        numbers = [int(n.strip()) for n in re.split(r'[,，\s]+', matches[0]) if n.strip().isdigit()]
                    break
                    
            if len(numbers) != config['expected_numbers']:
                logger.warning(f"號碼數量不正確: 期望{config['expected_numbers']}個，找到{len(numbers)}個")
                return None
            
            # 尋找特別號（如果需要）
            special_number = None
            if config['has_special']:
                special_patterns = [
                    r'威力彩號碼[：:]\s*(\d+)',
                    r'特別號[：:]\s*(\d+)',
                    r'second[_-]?number[：:]\s*(\d+)',
                ]
                
                for pattern in special_patterns:
                    match = re.search(pattern, html_content, re.IGNORECASE)
                    if match:
                        special_number = int(match.group(1))
                        break
            
            # 構建結果
            result = {
                'period': str(period),
                'draw_date': datetime.now().strftime('%Y-%m-%d'),  # 使用當前日期作為備用
                'numbers': sorted(numbers),
                'lottery_type': lottery_type
            }
            
            if special_number is not None:
                result['special_number'] = special_number
                
            logger.info(f"從HTML成功解析 {lottery_type} 數據: 期號 {period}, 號碼 {numbers}")
            return result
            
        except ImportError:
            logger.error("缺少 BeautifulSoup4，無法解析HTML")
            return None
        except Exception as e:
            logger.error(f"HTML解析失敗: {str(e)}")
            return None

    def check_network_connectivity(self) -> bool:
        """檢查網路連接狀態
        
        Returns:
            bool: 網路是否可用
        """
        try:
            # 測試連接台灣彩券主網站 (正確的網址)
            response = self.session.get(
                'https://www.taiwanlottery.com/',
                timeout=10,
                allow_redirects=True
            )
            return response.status_code == 200
        except Exception as e:
            logger.warning(f"網路連接檢查失敗: {str(e)}")
            return False

    def _parse_lottery_result(self, data: Dict, lottery_type: str, config: Dict) -> Optional[Dict]:
        """解析開獎結果數據
        
        Args:
            data: 原始API數據
            lottery_type: 彩票類型
            config: API配置
            
        Returns:
            Dict: 解析後的開獎結果
        """
        try:
            # 提取期號
            period = data.get('drawTerm', data.get('期號', ''))
            if not period:
                logger.error("無法獲取期號")
                return None
                
            # 提取開獎日期
            draw_date = data.get('drawDate', data.get('開獎日期', ''))
            if not draw_date:
                draw_date = datetime.now().strftime('%Y-%m-%d')
                
            # 提取開獎號碼
            numbers = []
            special_number = None
            
            if lottery_type == 'powercolor':
                # 威力彩: 6個主要號碼 + 1個威力彩號碼
                main_numbers = data.get('drawNumber1', data.get('主號碼', [])) 
                numbers = list(map(int, main_numbers)) if main_numbers else []
                special_number = data.get('drawNumber2', data.get('威力彩號碼'))
                if special_number:
                    special_number = int(special_number) if isinstance(special_number, str) else special_number
                    
            elif lottery_type == 'lotto649':
                # 大樂透: 6個主要號碼 + 1個特別號
                main_numbers = data.get('drawNumber1', data.get('主號碼', []))
                numbers = list(map(int, main_numbers)) if main_numbers else []
                special_number = data.get('drawNumber2', data.get('特別號'))
                if special_number:
                    special_number = int(special_number) if isinstance(special_number, str) else special_number
                    
            elif lottery_type == 'dailycash':
                # 今彩539: 5個號碼
                main_numbers = data.get('drawNumber1', data.get('主號碼', []))
                numbers = list(map(int, main_numbers)) if main_numbers else []
                
            # 驗證號碼數量
            if len(numbers) != config['expected_numbers']:
                logger.error(f"{lottery_type} 號碼數量不正確: 期望{config['expected_numbers']}個，實際{len(numbers)}個")
                return None
                
            # 構建結果
            result = {
                'period': str(period),
                'draw_date': draw_date,
                'numbers': sorted(numbers),
                'lottery_type': lottery_type
            }
            
            if config['has_special'] and special_number is not None:
                result['special_number'] = special_number
                
            return result
            
        except Exception as e:
            logger.error(f"解析 {lottery_type} 開獎結果時發生錯誤: {str(e)}")
            return None

    def save_lottery_result(self, result: Dict) -> bool:
        """將開獎結果保存到數據庫
        
        Args:
            result: 開獎結果數據
            
        Returns:
            bool: 是否保存成功
        """
        if not self.db_manager:
            logger.error("資料庫管理器未初始化")
            return False
            
        lottery_type = result['lottery_type']
        config = self.apis[lottery_type]
        
        try:
            # 檢查是否已存在該期數據
            if self._result_exists(result['period'], lottery_type):
                logger.info(f"{lottery_type} 第 {result['period']} 期數據已存在，跳過保存")
                return True
                
            # 構建SQL插入語句
            conn = self.db_manager.create_connection()
            cursor = conn.cursor()
            
            if lottery_type == 'powercolor':
                sql = """
                INSERT INTO Powercolor (Period, Sdate, Anumber1, Anumber2, Anumber3, 
                                       Anumber4, Anumber5, Anumber6, Second_district)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                values = [
                    result['period'],
                    result['draw_date'],
                    *result['numbers'],
                    result.get('special_number', 0)
                ]
                
            elif lottery_type == 'lotto649':
                sql = """
                INSERT INTO Lotto649 (Period, Sdate, Anumber1, Anumber2, Anumber3,
                                     Anumber4, Anumber5, Anumber6, SpecialNumber)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                values = [
                    result['period'],
                    result['draw_date'],
                    *result['numbers'],
                    result.get('special_number', 0)
                ]
                
            elif lottery_type == 'dailycash':
                sql = """
                INSERT INTO DailyCash (Period, Sdate, Anumber1, Anumber2, Anumber3,
                                      Anumber4, Anumber5)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                values = [
                    result['period'], 
                    result['draw_date'],
                    *result['numbers']
                ]
                
            cursor.execute(sql, values)
            conn.commit()
            conn.close()
            
            logger.info(f"成功保存 {lottery_type} 第 {result['period']} 期開獎結果")
            return True
            
        except Exception as e:
            logger.error(f"保存 {lottery_type} 開獎結果時發生錯誤: {str(e)}")
            return False

    def _result_exists(self, period: str, lottery_type: str) -> bool:
        """檢查指定期數的開獎結果是否已存在
        
        Args:
            period: 期號
            lottery_type: 彩票類型
            
        Returns:
            bool: 是否已存在
        """
        try:
            conn = self.db_manager.create_connection()
            cursor = conn.cursor()
            
            table_name = self.apis[lottery_type]['table_name']
            sql = f"SELECT COUNT(*) FROM {table_name} WHERE Period = ?"
            
            cursor.execute(sql, (period,))
            count = cursor.fetchone()[0]
            conn.close()
            
            return count > 0
            
        except Exception as e:
            logger.error(f"檢查期數是否存在時發生錯誤: {str(e)}")
            return False

    def update_all_lottery_results(self) -> Dict[str, Dict]:
        """更新所有彩票類型的開獎結果
        
        Returns:
            Dict: 更新結果統計
        """
        logger.info("開始更新所有彩票開獎結果...")
        
        results = {}
        
        for lottery_type in self.apis.keys():
            try:
                logger.info(f"正在更新 {lottery_type}...")
                
                # 獲取最新開獎結果
                latest_result = self.fetch_latest_results(lottery_type)
                
                if latest_result:
                    # 保存台灣彩券官方數據到數據庫
                    success = self.save_lottery_result(latest_result)
                    
                    results[lottery_type] = {
                        'status': 'success' if success else 'failed',
                        'period': latest_result['period'],
                        'draw_date': latest_result['draw_date'],
                        'numbers': latest_result['numbers'],
                        'special_number': latest_result.get('special_number'),
                        'message': '✅ 台灣彩券官方數據更新成功' if success else '❌ 數據保存失敗',
                        'data_source': '台灣彩券官方'
                    }
                else:
                    results[lottery_type] = {
                        'status': 'failed',
                        'message': '❌ 無法從台灣彩券官方獲取開獎數據，網路可能不穩定',
                        'data_source': '台灣彩券官方 (連接失敗)'
                    }
                    
                # 避免對台灣彩券伺服器造成壓力
                time.sleep(5)  # 增加間隔時間
                
            except Exception as e:
                logger.error(f"更新 {lottery_type} 時發生錯誤: {str(e)}")
                results[lottery_type] = {
                    'status': 'error',
                    'message': str(e)
                }
        
        logger.info("所有彩票開獎結果更新完成")
        return results

    def get_latest_period(self, lottery_type: str) -> Optional[str]:
        """獲取數據庫中指定彩票類型的最新期號
        
        Args:
            lottery_type: 彩票類型
            
        Returns:
            str: 最新期號，如果沒有數據返回None
        """
        if not self.db_manager:
            return None
            
        try:
            conn = self.db_manager.create_connection()
            cursor = conn.cursor()
            
            table_name = self.apis[lottery_type]['table_name']
            sql = f"SELECT Period FROM {table_name} ORDER BY Period DESC LIMIT 1"
            
            cursor.execute(sql)
            result = cursor.fetchone()
            conn.close()
            
            return str(result[0]) if result else None
            
        except Exception as e:
            logger.error(f"獲取最新期號時發生錯誤: {str(e)}")
            return None

    def get_next_expected_period(self, lottery_type: str) -> str:
        """根據最新期號計算下一期預期期號
        
        Args:
            lottery_type: 彩票類型
            
        Returns:
            str: 下一期預期期號
        """
        latest_period = self.get_latest_period(lottery_type)
        
        if not latest_period:
            # 如果沒有數據，返回當年度第一期
            current_year = datetime.now().year
            year_suffix = str(current_year)[-3:]  # 取年份後三位
            return f"{year_suffix}001"
        
        try:
            # 解析期號 (格式: YYYXXX，如113001)
            period_num = int(latest_period[-3:])  # 取最後三位數字
            year_part = latest_period[:-3]  # 取年份部分
            
            next_period_num = period_num + 1
            next_period = f"{year_part}{next_period_num:03d}"
            
            return next_period
            
        except Exception as e:
            logger.error(f"計算下一期期號時發生錯誤: {str(e)}")
            # 返回當年度第一期作為備用
            current_year = datetime.now().year
            year_suffix = str(current_year)[-3:]
            return f"{year_suffix}001"
    
    def close_session(self):
        """關閉session以釋放資源"""
        if hasattr(self, 'session'):
            self.session.close()
    
    def __del__(self):
        """析構函數，自動清理資源"""
        self.close_session()

if __name__ == "__main__":
    # 測試代碼
    updater = RealLotteryUpdater()
    results = updater.update_all_lottery_results()
    
    print("更新結果:")
    for lottery_type, result in results.items():
        print(f"{lottery_type}: {result}")