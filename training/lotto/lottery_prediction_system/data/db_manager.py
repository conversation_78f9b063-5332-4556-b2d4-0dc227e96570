"""
彩票預測系統 - 資料庫管理模組
負責所有與資料庫相關的操作，支援威力彩、大樂透和今彩539
"""

import pandas as pd
import sqlite3
import logging
import json  # Add this line
from datetime import datetime
import traceback
import os
import sys
from pathlib import Path    

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/db_manager.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('db_manager')

class DBManager:
    def __init__(self, db_path=None, config_manager=None):
        """初始化資料庫管理器
        
        Args:
            db_path: 資料庫路徑（可選）
            config_manager: 配置管理器實例（可選）
        """
        if db_path is None:
            # 嘗試從配置管理器獲取路徑
            if config_manager:
                db_config = config_manager.get_database_config()
                self.db_path = db_config.get('path', 'data/lottery_data.db')
            else:
                # 回退到預設路徑
                self.db_path = 'data/lottery_data.db'
            
            # 如果是相對路徑，轉換為絕對路徑
            if not os.path.isabs(self.db_path):
                base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                self.db_path = os.path.join(base_dir, self.db_path)
            
            # 確保目錄存在
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        else:
            self.db_path = db_path
        
        self._ensure_db_exists()
    def _ensure_db_exists(self):
        """確保資料庫檔案和所需表格存在"""
        # 如果資料庫目錄不存在，創建它
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
            
        # 連接資料庫（如果不存在會創建）
        conn = self.create_connection()
        
        # 創建必要的表格
        cursor = conn.cursor()
        
        # 創建威力彩表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS Powercolor (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            Period TEXT NOT NULL,
            Sdate TEXT NOT NULL,
            Anumber1 INTEGER,
            Anumber2 INTEGER,
            Anumber3 INTEGER,
            Anumber4 INTEGER,
            Anumber5 INTEGER,
            Anumber6 INTEGER,
            Second_district INTEGER
        )
        """)
        
        # 創建大樂透表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS Lotto649 (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            Period TEXT NOT NULL,
            Sdate TEXT NOT NULL,
            Anumber1 INTEGER,
            Anumber2 INTEGER,
            Anumber3 INTEGER,
            Anumber4 INTEGER,
            Anumber5 INTEGER,
            Anumber6 INTEGER,
            SpecialNumber INTEGER
        )
        """)
        
        # 創建今彩539表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS DailyCash (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            Period TEXT NOT NULL,
            Sdate TEXT NOT NULL,
            Anumber1 INTEGER,
            Anumber2 INTEGER,
            Anumber3 INTEGER,
            Anumber4 INTEGER,
            Anumber5 INTEGER
        )
        """)
        
        # 創建預測表格
        self._ensure_prediction_tables_exist(cursor)
        
        # 創建Phase 3表格（如果需要）
        self._ensure_phase3_tables_exist(cursor)
        
        conn.commit()
        conn.close()
            
    def create_connection(self):
        """建立資料庫連接"""
        try:
            return sqlite3.connect(self.db_path)
        except sqlite3.Error as e:
            logger.error(f"資料庫連接錯誤: {e}")
            raise
    
    def load_lottery_data(self, lottery_type='powercolor'):
        """從資料庫加載彩票數據
        
        Args:
            lottery_type: 彩票類型 ('powercolor'=威力彩, 'lotto649'=大樂透, 'dailycash'=今彩539)
            
        Returns:
            DataFrame: 彩票歷史資料
        """
        try:
            table_map = {
                'powercolor': 'Powercolor',
                'lotto649': 'Lotto649',
                'dailycash': 'DailyCash'
            }
            
            table_name = table_map.get(lottery_type.lower())
            if not table_name:
                raise ValueError(f"不支援的彩票類型: {lottery_type}")
            
            logger.info(f"正在從資料庫讀取{self._get_lottery_name(lottery_type)}歷史資料...")
            conn = self.create_connection()
            
            if lottery_type.lower() == 'powercolor':
                query = """
                SELECT Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, Second_district
                FROM Powercolor
                ORDER BY Period
                """
            elif lottery_type.lower() == 'lotto649':
                query = """
                SELECT Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, SpecialNumber
                FROM Lotto649
                ORDER BY Period
                """
            elif lottery_type.lower() == 'dailycash':
                query = """
                SELECT Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5
                FROM DailyCash
                ORDER BY Period
                """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            # 轉換日期格式
            df['Sdate'] = pd.to_datetime(df['Sdate'])
            
            # 確保數值列為數值型別
            if lottery_type.lower() == 'powercolor':
                numeric_cols = ['Anumber1', 'Anumber2', 'Anumber3', 'Anumber4', 'Anumber5', 'Anumber6', 'Second_district']
            elif lottery_type.lower() == 'lotto649':
                numeric_cols = ['Anumber1', 'Anumber2', 'Anumber3', 'Anumber4', 'Anumber5', 'Anumber6', 'SpecialNumber']
            elif lottery_type.lower() == 'dailycash':
                numeric_cols = ['Anumber1', 'Anumber2', 'Anumber3', 'Anumber4', 'Anumber5']
                
            for col in numeric_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 轉換期數為數字
            df['Period'] = df['Period'].astype(int)
            
            logger.info(f"讀取完成! 總共 {len(df)} 筆歷史記錄")
            logger.info(f"資料涵蓋期間: {df['Sdate'].min().date()} 至 {df['Sdate'].max().date()}")
            
            return df
            
        except Exception as e:
            logger.error(f"加載資料時出錯: {str(e)}")
            raise
    
    def save_prediction(self, prediction, period, lottery_type='powercolor', ml_result=None, board_path_result=None, metadata=None):
        """將預測結果保存到資料庫 (擴展版本)
        
        Args:
            prediction: 預測結果字典 (整合後的結果) 或 PredictionResult 對象
            period: 期數
            lottery_type: 彩票類型 ('powercolor'=威力彩, 'lotto649'=大樂透, 'dailycash'=今彩539)
            ml_result: 機器學習預測結果 (可選)
            board_path_result: 板路分析預測結果 (可選)
            metadata: 額外元數據 (可選)
            
        Returns:
            bool: 是否成功保存
        """
        try:
            logger.info(f"正在將{self._get_lottery_name(lottery_type)}預測結果保存到資料庫... (期數: {period})")
            
            conn = self.create_connection()
            cursor = conn.cursor()
            
            # 檢查表是否存在，如果不存在則創建
            self._ensure_prediction_tables_exist(cursor)
            
            # 將ML和板路分析結果轉為JSON字符串
            ml_details = json.dumps(ml_result, ensure_ascii=False) if ml_result else None
            board_path_details = json.dumps(board_path_result, ensure_ascii=False) if board_path_result else None
            
            # 生成唯一識別碼 (使用時間戳記)
            prediction_id = datetime.now().strftime("%Y%m%d%H%M%S")
            
            # 處理 PredictionResult 對象或原始字典
            prediction_data = prediction
            if hasattr(prediction, 'to_dict'):
                prediction_data = prediction.to_dict()
                
            # 如果是新格式(有多個候選結果)則特殊處理
            if 'candidates' in prediction_data and prediction_data['candidates']:
                return self._save_candidates_predictions(
                    cursor, 
                    prediction_data, 
                    period, 
                    lottery_type, 
                    ml_result, 
                    board_path_result, 
                    prediction_id, 
                    conn,
                    metadata
                )
                
            # 使用原有的方式保存單一預測
            # 取得預測方法和版本
            prediction_method = prediction_data.get('預測方法', prediction_data.get('method', 'ensemble'))
            model_version = prediction_data.get('模型版本', prediction_data.get('version', 'V1.0'))
            details = prediction_data.get('詳細分析', prediction_data.get('explanation', None))
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')    
            if isinstance(details, list) or isinstance(details, dict):
                details = json.dumps(details, ensure_ascii=False)
                
            # 根據彩票類型準備插入資料
            if lottery_type.lower() == 'powercolor':
                # 處理新舊格式的欄位名稱
                first_area = prediction_data.get('第一區', prediction_data.get('main_numbers', []))
                second_area = prediction_data.get('第二區', prediction_data.get('special_number', 0))
                
                # 確保有足夠的號碼
                while len(first_area) < 6:
                    first_area.append(0)
                    
                # 始終插入新的預測記錄
                insert_query = """
                INSERT INTO PowercolorPredictions 
                (Period, PredictionDate, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredS, 
                ModelVersion, PredictionDetails, PredictionMethod, MLResult, BoardPathResult, PredictionID, Confidence)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                # 獲取信心分數(如果有)
                confidence = prediction_data.get('confidence', 1.0)
                
                cursor.execute(insert_query, (
                    period,
                    current_time,
                    first_area[0],
                    first_area[1],
                    first_area[2],
                    first_area[3],
                    first_area[4],
                    first_area[5],
                    second_area,
                    model_version,
                    details,
                    prediction_method,
                    ml_details,
                    board_path_details,
                    prediction_id,
                    confidence
                ))
            
            elif lottery_type.lower() == 'lotto649':
                # 處理新舊格式的欄位名稱
                first_area = prediction_data.get('第一區', prediction_data.get('main_numbers', []))
                special_number = prediction_data.get('特別號', prediction_data.get('special_number', 0))
                
                # 確保有足夠的號碼
                while len(first_area) < 6:
                    first_area.append(0)
                
                # 始終插入新的預測記錄
                insert_query = """
                INSERT INTO Lotto649Predictions 
                (Period, PredictionDate, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredSpecial, 
                ModelVersion, PredictionDetails, PredictionMethod, MLResult, BoardPathResult, PredictionID, Confidence)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                # 獲取信心分數(如果有)
                confidence = prediction_data.get('confidence', 1.0)
                
                cursor.execute(insert_query, (
                    period,
                    current_time,
                    first_area[0],
                    first_area[1],
                    first_area[2],
                    first_area[3],
                    first_area[4],
                    first_area[5],
                    special_number,
                    model_version,
                    details,
                    prediction_method,
                    ml_details,
                    board_path_details,
                    prediction_id,
                    confidence
                ))
            
            elif lottery_type.lower() == 'dailycash':
                # 處理新舊格式的欄位名稱
                numbers = prediction_data.get('號碼', prediction_data.get('main_numbers', []))
                
                # 確保有足夠的號碼
                while len(numbers) < 5:
                    numbers.append(0)
                    
                # 始終插入新的預測記錄
                insert_query = """
                INSERT INTO DailyCashPredictions 
                (Period, PredictionDate, PredA1, PredA2, PredA3, PredA4, PredA5, 
                ModelVersion, PredictionDetails, PredictionMethod, MLResult, BoardPathResult, PredictionID, Confidence)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                # 獲取信心分數(如果有)
                confidence = prediction_data.get('confidence', 1.0)
                
                cursor.execute(insert_query, (
                    period,
                    current_time,
                    numbers[0],
                    numbers[1],
                    numbers[2],
                    numbers[3],
                    numbers[4],
                    model_version,
                    details,
                    prediction_method,
                    ml_details,
                    board_path_details,
                    prediction_id,
                    confidence
                ))
            
            # 保存額外元數據(如果有)
            if metadata:
                self._save_prediction_metadata(cursor, prediction_id, metadata)
            
            conn.commit()
            logger.info(f"已將期數 {period} 的{self._get_lottery_name(lottery_type)}預測結果保存到資料庫 (預測ID: {prediction_id})")
            
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"保存預測結果時出錯: {str(e)}")
            logger.error(traceback.format_exc())
            if 'conn' in locals():
                conn.rollback()
                conn.close()
            return False

    def _save_candidates_predictions(self, cursor, prediction_data, period, lottery_type, ml_result, board_path_result, prediction_id, conn, metadata=None):
        """保存多個候選預測結果
        
        Args:
            cursor: 資料庫游標
            prediction_data: 預測資料字典
            period: 期數
            lottery_type: 彩票類型
            ml_result: 機器學習結果
            board_path_result: 板路分析結果
            prediction_id: 預測ID
            conn: 資料庫連接
            metadata: 額外元數據
            
        Returns:
            bool: 是否成功
        """
        try:
            # 取得預測方法和版本
            prediction_method = prediction_data.get('method', 'ensemble')
            model_version = prediction_data.get('version', 'V1.0')
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            # 將ML和板路分析結果轉為JSON字符串
            ml_details = json.dumps(ml_result, ensure_ascii=False) if ml_result else None
            board_path_details = json.dumps(board_path_result, ensure_ascii=False) if board_path_result else None
            
            # 保存每個候選結果
            for idx, candidate in enumerate(prediction_data['candidates']):
                main_numbers = candidate['main_numbers']
                special_number = candidate.get('special_number', 0)
                confidence = candidate.get('confidence', 1.0)
                explanation = candidate.get('explanation', [])
                
                if isinstance(explanation, list) or isinstance(explanation, dict):
                    explanation = json.dumps(explanation, ensure_ascii=False)
                
                # 根據彩票類型保存
                if lottery_type.lower() == 'powercolor':
                    # 確保有足夠的號碼
                    while len(main_numbers) < 6:
                        main_numbers.append(0)
                        
                    insert_query = """
                    INSERT INTO PowercolorPredictions 
                    (Period, PredictionDate, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredS, 
                    ModelVersion, PredictionDetails, PredictionMethod, MLResult, BoardPathResult, PredictionID, CandidateIndex, Confidence)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    
                    cursor.execute(insert_query, (
                        period,
                        current_time,
                        main_numbers[0],
                        main_numbers[1],
                        main_numbers[2],
                        main_numbers[3],
                        main_numbers[4],
                        main_numbers[5],
                        special_number,
                        model_version,
                        explanation,
                        prediction_method,
                        ml_details,
                        board_path_details,
                        prediction_id,
                        idx,
                        confidence
                    ))
                
                elif lottery_type.lower() == 'lotto649':
                    # 確保有足夠的號碼
                    while len(main_numbers) < 6:
                        main_numbers.append(0)
                        
                    insert_query = """
                    INSERT INTO Lotto649Predictions 
                    (Period, PredictionDate, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredSpecial, 
                    ModelVersion, PredictionDetails, PredictionMethod, MLResult, BoardPathResult, PredictionID, CandidateIndex, Confidence)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    
                    cursor.execute(insert_query, (
                        period,
                        current_time,
                        main_numbers[0],
                        main_numbers[1],
                        main_numbers[2],
                        main_numbers[3],
                        main_numbers[4],
                        main_numbers[5],
                        special_number,
                        model_version,
                        explanation,
                        prediction_method,
                        ml_details,
                        board_path_details,
                        prediction_id,
                        idx,
                        confidence
                    ))
                
                elif lottery_type.lower() == 'dailycash':
                    # 確保有足夠的號碼
                    while len(main_numbers) < 5:
                        main_numbers.append(0)
                        
                    insert_query = """
                    INSERT INTO DailyCashPredictions 
                    (Period, PredictionDate, PredA1, PredA2, PredA3, PredA4, PredA5, 
                    ModelVersion, PredictionDetails, PredictionMethod, MLResult, BoardPathResult, PredictionID, CandidateIndex, Confidence)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    
                    cursor.execute(insert_query, (
                        period,
                        current_time,
                        main_numbers[0],
                        main_numbers[1],
                        main_numbers[2],
                        main_numbers[3],
                        main_numbers[4],
                        model_version,
                        explanation,
                        prediction_method,
                        ml_details,
                        board_path_details,
                        prediction_id,
                        idx,
                        confidence
                    ))
            
            # 保存元數據
            if metadata:
                self._save_prediction_metadata(cursor, prediction_id, metadata)
            
            # 提交事務
            conn.commit()
            logger.info(f"已將期數 {period} 的{self._get_lottery_name(lottery_type)}預測結果保存到資料庫 (預測ID: {prediction_id}, 候選數: {len(prediction_data['candidates'])})")
            
            return True
        except Exception as e:
            logger.error(f"保存多候選預測結果時出錯: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def _save_prediction_metadata(self, cursor, prediction_id, metadata):
        """保存預測元數據
        
        Args:
            cursor: 資料庫游標
            prediction_id: 預測ID
            metadata: 元數據字典
        """
        try:
            # 確保表存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='PredictionMetadata'")
            table_exists = cursor.fetchone()

            if not table_exists:
                cursor.execute("""
                CREATE TABLE PredictionMetadata (
                    ID INTEGER PRIMARY KEY AUTOINCREMENT,
                    PredictionID TEXT,
                    MetadataKey TEXT,
                    MetadataValue TEXT,
                    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """)
                        
            # 保存每個元數據項
            for key, value in metadata.items():
                # 將值轉為字符串
                if isinstance(value, (dict, list)):
                    value = json.dumps(value, ensure_ascii=False)
                else:
                    value = str(value)
                    
                cursor.execute("""
                INSERT INTO PredictionMetadata (PredictionID, MetadataKey, MetadataValue)
                VALUES (?, ?, ?)
                """, (prediction_id, key, value))
                
        except Exception as e:
            logger.error(f"保存預測元數據時出錯: {str(e)}")
            logger.error(traceback.format_exc())

    def _ensure_prediction_tables_exist(self, cursor):
        """確保所有預測表存在，並添加新增的列"""
        # 威力彩預測表
        cursor.execute("""
          CREATE TABLE IF NOT EXISTS PowercolorPredictions (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            Period TEXT,
            PredictionDate TEXT,
            PredA1 INTEGER,
            PredA2 INTEGER,
            PredA3 INTEGER,
            PredA4 INTEGER,
            PredA5 INTEGER,
            PredA6 INTEGER,
            PredS INTEGER,
            ActualA1 INTEGER,
            ActualA2 INTEGER,
            ActualA3 INTEGER,
            ActualA4 INTEGER,
            ActualA5 INTEGER,
            ActualA6 INTEGER,
            ActualS INTEGER,
            MatchCount INTEGER,
            SecondMatch INTEGER,
            ModelVersion TEXT,
            PredictionDetails TEXT,
            PredictionMethod TEXT,
            MLResult TEXT,
            BoardPathResult TEXT,
            PredictionID TEXT,
            CandidateIndex INTEGER,
            Confidence REAL
        )
        """)
        
        # 檢查並新增欄位
        self._ensure_column_exists(cursor, 'PowercolorPredictions', 'PredictionMethod', 'TEXT')
        self._ensure_column_exists(cursor, 'PowercolorPredictions', 'MLResult', 'TEXT')
        self._ensure_column_exists(cursor, 'PowercolorPredictions', 'BoardPathResult', 'TEXT')
        self._ensure_column_exists(cursor, 'PowercolorPredictions', 'PredictionID', 'TEXT')
        self._ensure_column_exists(cursor, 'PowercolorPredictions', 'CandidateIndex', 'INTEGER')
        self._ensure_column_exists(cursor, 'PowercolorPredictions', 'Confidence', 'REAL')
        # 新增準確度追蹤欄位
        self._ensure_column_exists(cursor, 'PowercolorPredictions', 'AccuracyScore', 'REAL')
        self._ensure_column_exists(cursor, 'PowercolorPredictions', 'MethodCategory', 'TEXT')
        self._ensure_column_exists(cursor, 'PowercolorPredictions', 'ProcessingTime', 'REAL')
        self._ensure_column_exists(cursor, 'PowercolorPredictions', 'LastUpdated', 'TEXT')
        
        # 大樂透預測表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS Lotto649Predictions (
            ID INT IDENTITY(1,1) PRIMARY KEY,
            Period TEXT,
            PredictionDate TEXT,
            PredA1 INTEGER,
            PredA2 INTEGER,
            PredA3 INTEGER,
            PredA4 INTEGER,
            PredA5 INTEGER,
            PredA6 INTEGER,
            PredSpecial INTEGER,
            ActualA1 INTEGER,
            ActualA2 INTEGER,
            ActualA3 INTEGER,
            ActualA4 INTEGER,
            ActualA5 INTEGER,
            ActualA6 INTEGER,
            ActualSpecial INTEGER,
            MatchCount INTEGER,
            SpecialMatch INTEGER,
            ModelVersion TEXT,
            PredictionDetails TEXT,
            PredictionMethod TEXT,
            MLResult TEXT,
            BoardPathResult TEXT,
            PredictionID TEXT,
            CandidateIndex INTEGER,
            Confidence REAL
        )
        """)
        
        # 檢查並新增欄位
        self._ensure_column_exists(cursor, 'Lotto649Predictions', 'PredictionMethod', 'TEXT')
        self._ensure_column_exists(cursor, 'Lotto649Predictions', 'MLResult', 'TEXT')
        self._ensure_column_exists(cursor, 'Lotto649Predictions', 'BoardPathResult', 'TEXT')
        self._ensure_column_exists(cursor, 'Lotto649Predictions', 'PredictionID', 'TEXT')
        self._ensure_column_exists(cursor, 'Lotto649Predictions', 'CandidateIndex', 'INTEGER')
        self._ensure_column_exists(cursor, 'Lotto649Predictions', 'Confidence', 'REAL')
        
        # 今彩539預測表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS DailyCashPredictions (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            Period TEXT,
            PredictionDate TEXT,
            PredA1 INTEGER,
            PredA2 INTEGER,
            PredA3 INTEGER,
            PredA4 INTEGER,
            PredA5 INTEGER,
            ActualA1 INTEGER,
            ActualA2 INTEGER,
            ActualA3 INTEGER,
            ActualA4 INTEGER,
            ActualA5 INTEGER,
            MatchCount INTEGER,
            ModelVersion TEXT,
            PredictionDetails TEXT,
            PredictionMethod TEXT,
            MLResult TEXT,
            BoardPathResult TEXT,
            PredictionID TEXT,
            CandidateIndex INTEGER,
            Confidence REAL
        )
        """)
        
        # 檢查並新增欄位
        self._ensure_column_exists(cursor, 'DailyCashPredictions', 'PredictionMethod', 'TEXT')
        self._ensure_column_exists(cursor, 'DailyCashPredictions', 'MLResult', 'TEXT')
        self._ensure_column_exists(cursor, 'DailyCashPredictions', 'BoardPathResult', 'TEXT')
        self._ensure_column_exists(cursor, 'DailyCashPredictions', 'PredictionID', 'TEXT')
        self._ensure_column_exists(cursor, 'DailyCashPredictions', 'CandidateIndex', 'INTEGER')
        self._ensure_column_exists(cursor, 'DailyCashPredictions', 'Confidence', 'REAL')    
    def _ensure_prediction_tables_exist(self, cursor):
        """確保所有預測表存在，並添加新增的列"""
        # 威力彩預測表
        cursor.execute("""       
        CREATE TABLE IF NOT EXISTS PowercolorPredictions (
            ID INT IDENTITY(1,1) PRIMARY KEY,
            Period TEXT,
            PredictionDate TEXT,
            PredA1 INTEGER,
            PredA2 INTEGER,
            PredA3 INTEGER,
            PredA4 INTEGER,
            PredA5 INTEGER,
            PredA6 INTEGER,
            PredS INTEGER,
            ActualA1 INTEGER,
            ActualA2 INTEGER,
            ActualA3 INTEGER,
            ActualA4 INTEGER,
            ActualA5 INTEGER,
            ActualA6 INTEGER,
            ActualS INTEGER,
            MatchCount INTEGER,
            SecondMatch INTEGER,
            ModelVersion TEXT,
            PredictionDetails TEXT,
            PredictionMethod TEXT,
            MLResult TEXT,
            BoardPathResult TEXT,
            PredictionID TEXT
        )
        """)
        
        # 檢查並新增欄位
        self._ensure_column_exists(cursor, 'PowercolorPredictions', 'PredictionMethod', 'VARCHAR(50) NULL')
        self._ensure_column_exists(cursor, 'PowercolorPredictions', 'MLResult', 'NVARCHAR(MAX) NULL')
        self._ensure_column_exists(cursor, 'PowercolorPredictions', 'BoardPathResult', 'NVARCHAR(MAX) NULL')
        self._ensure_column_exists(cursor, 'PowercolorPredictions', 'PredictionID', 'VARCHAR(50) NULL')
        
        # 大樂透預測表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS Lotto649Predictions (
            ID INT IDENTITY(1,1) PRIMARY KEY,
            Period TEXT,
            PredictionDate TEXT,
            PredA1 INTEGER,
            PredA2 INTEGER,
            PredA3 INTEGER,
            PredA4 INTEGER,
            PredA5 INTEGER,
            PredA6 INTEGER,
            PredSpecial INTEGER,
            ActualA1 INTEGER,
            ActualA2 INTEGER,
            ActualA3 INTEGER,
            ActualA4 INTEGER,
            ActualA5 INTEGER,
            ActualA6 INTEGER,
            ActualSpecial INTEGER,
            MatchCount INTEGER,
            SpecialMatch INTEGER,
            ModelVersion TEXT,
            PredictionDetails TEXT,
            PredictionMethod TEXT,
            MLResult TEXT,
            BoardPathResult TEXT,
            PredictionID TEXT
        )
        """)
        
        # 檢查並新增欄位
        self._ensure_column_exists(cursor, 'Lotto649Predictions', 'PredictionMethod', 'VARCHAR(50) NULL')
        self._ensure_column_exists(cursor, 'Lotto649Predictions', 'MLResult', 'NVARCHAR(MAX) NULL')
        self._ensure_column_exists(cursor, 'Lotto649Predictions', 'BoardPathResult', 'NVARCHAR(MAX) NULL')
        self._ensure_column_exists(cursor, 'Lotto649Predictions', 'PredictionID', 'VARCHAR(50) NULL')
        
        # 新增準確度追蹤欄位
        self._ensure_column_exists(cursor, 'Lotto649Predictions', 'AccuracyScore', 'REAL')
        self._ensure_column_exists(cursor, 'Lotto649Predictions', 'MethodCategory', 'TEXT')
        self._ensure_column_exists(cursor, 'Lotto649Predictions', 'ProcessingTime', 'REAL')
        self._ensure_column_exists(cursor, 'Lotto649Predictions', 'LastUpdated', 'TEXT')
        
        # 今彩539預測表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS DailyCashPredictions (
            ID INT IDENTITY(1,1) PRIMARY KEY,
            Period TEXT,
            PredictionDate TEXT,
            PredA1 INTEGER,
            PredA2 INTEGER,
            PredA3 INTEGER,
            PredA4 INTEGER,
            PredA5 INTEGER,
            ActualA1 INTEGER,
            ActualA2 INTEGER,
            ActualA3 INTEGER,
            ActualA4 INTEGER,
            ActualA5 INTEGER,
            MatchCount INTEGER,
            ModelVersion TEXT,
            PredictionDetails TEXT,
            PredictionMethod TEXT,
            MLResult TEXT,
            BoardPathResult TEXT,
            PredictionID TEXT
        )
        """)
        
        # 檢查並新增欄位
        self._ensure_column_exists(cursor, 'DailyCashPredictions', 'PredictionMethod', 'VARCHAR(50) NULL')
        self._ensure_column_exists(cursor, 'DailyCashPredictions', 'MLResult', 'NVARCHAR(MAX) NULL')
        self._ensure_column_exists(cursor, 'DailyCashPredictions', 'BoardPathResult', 'NVARCHAR(MAX) NULL')
        self._ensure_column_exists(cursor, 'DailyCashPredictions', 'PredictionID', 'VARCHAR(50) NULL')
        
        # 新增準確度追蹤欄位
        self._ensure_column_exists(cursor, 'DailyCashPredictions', 'AccuracyScore', 'REAL')
        self._ensure_column_exists(cursor, 'DailyCashPredictions', 'MethodCategory', 'TEXT')
        self._ensure_column_exists(cursor, 'DailyCashPredictions', 'ProcessingTime', 'REAL')
        self._ensure_column_exists(cursor, 'DailyCashPredictions', 'LastUpdated', 'TEXT')
        
        # 創建性能優化索引
        self._create_performance_indexes(cursor)

    def _create_performance_indexes(self, cursor):
        """創建性能優化索引"""
        try:
            indexes = [
                # PowercolorPredictions 索引
                "CREATE INDEX IF NOT EXISTS idx_powercolor_method ON PowercolorPredictions(PredictionMethod)",
                "CREATE INDEX IF NOT EXISTS idx_powercolor_date ON PowercolorPredictions(PredictionDate)",
                "CREATE INDEX IF NOT EXISTS idx_powercolor_accuracy ON PowercolorPredictions(AccuracyScore)",
                "CREATE INDEX IF NOT EXISTS idx_powercolor_period ON PowercolorPredictions(Period)",
                "CREATE INDEX IF NOT EXISTS idx_powercolor_method_date ON PowercolorPredictions(PredictionMethod, PredictionDate)",
                
                # Lotto649Predictions 索引
                "CREATE INDEX IF NOT EXISTS idx_lotto649_method ON Lotto649Predictions(PredictionMethod)",
                "CREATE INDEX IF NOT EXISTS idx_lotto649_date ON Lotto649Predictions(PredictionDate)",
                "CREATE INDEX IF NOT EXISTS idx_lotto649_accuracy ON Lotto649Predictions(AccuracyScore)",
                "CREATE INDEX IF NOT EXISTS idx_lotto649_period ON Lotto649Predictions(Period)",
                "CREATE INDEX IF NOT EXISTS idx_lotto649_method_date ON Lotto649Predictions(PredictionMethod, PredictionDate)",
                
                # DailyCashPredictions 索引
                "CREATE INDEX IF NOT EXISTS idx_dailycash_method ON DailyCashPredictions(PredictionMethod)",
                "CREATE INDEX IF NOT EXISTS idx_dailycash_date ON DailyCashPredictions(PredictionDate)",
                "CREATE INDEX IF NOT EXISTS idx_dailycash_accuracy ON DailyCashPredictions(AccuracyScore)",
                "CREATE INDEX IF NOT EXISTS idx_dailycash_period ON DailyCashPredictions(Period)",
                "CREATE INDEX IF NOT EXISTS idx_dailycash_method_date ON DailyCashPredictions(PredictionMethod, PredictionDate)",
                
                # 開獎結果表索引
                "CREATE INDEX IF NOT EXISTS idx_powercolor_period ON Powercolor(Period)",
                "CREATE INDEX IF NOT EXISTS idx_lotto649_period ON Lotto649(Period)",
                "CREATE INDEX IF NOT EXISTS idx_dailycash_period ON DailyCash(Period)"
            ]
            
            for index_sql in indexes:
                try:
                    cursor.execute(index_sql)
                    logger.debug(f"索引創建成功: {index_sql}")
                except Exception as e:
                    # 如果索引已存在或創建失敗，記錄但不中斷
                    logger.debug(f"索引創建跳過: {e}")
                    
            logger.info("數據庫性能索引創建完成")
            
        except Exception as e:
            logger.error(f"創建性能索引失敗: {str(e)}")

    def _ensure_column_exists(self, cursor, table, column, column_type):
        """確保表中存在指定的列，如果不存在則添加"""
        try:
            # SQLite 版本
            cursor.execute(f"PRAGMA table_info({table})")
            columns = [row[1] for row in cursor.fetchall()]
            
            if column not in columns:
                cursor.execute(f"ALTER TABLE {table} ADD COLUMN {column} {column_type}")
                
        except Exception as e:
            logger.error(f"檢查並添加列時出錯: {str(e)}")
        
    def update_prediction_results(self, lottery_type='powercolor'):
        """更新預測結果與實際開獎結果的比對"""
        logger.info("正在運行已更新版本的 update_prediction_results 方法!")
        try:
            logger.info(f"正在更新{self._get_lottery_name(lottery_type)}歷史預測結果的比對...")
            
            conn = self.create_connection()
            cursor = conn.cursor()
            
            if lottery_type.lower() == 'powercolor':
                # 獲取尚未更新結果的預測記錄
                cursor.execute("""
                SELECT p.ID, p.Period, p.PredA1, p.PredA2, p.PredA3, p.PredA4, p.PredA5, p.PredA6, p.PredS
                FROM PowercolorPredictions p
                WHERE p.ActualA1 IS NULL
                """)
                
                predictions = cursor.fetchall()
                
                if len(predictions) == 0:
                    logger.info("沒有需要更新的預測記錄")
                    conn.close()
                    return 0
                    
                logger.info(f"找到 {len(predictions)} 筆需要更新的預測記錄")
                
                updated_count = 0
                
                for pred in predictions:
                    pred_id, period = pred[0], pred[1]
                    
                    # 查詢實際開獎結果
                    cursor.execute("""
                    SELECT Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, Second_district
                    FROM Powercolor
                    WHERE Period = ?
                    """, (period,))
                    
                    actual = cursor.fetchone()
                    
                    if actual:
                        logger.info(f"找到期數 {period} 的實際開獎結果: {actual}")
                        
                        try:
                            # 預測號碼轉為整數集合
                            pred_first = set()
                            for i in range(6):
                                try:
                                    pred_first.add(int(float(pred[i+2])))
                                except (ValueError, TypeError) as e:
                                    logger.warning(f"無法將預測值 {pred[i+2]} 轉換為整數: {e}")
                            
                            # 實際號碼轉為整數集合
                            actual_first = set()
                            for i in range(6):
                                try:
                                    if actual[i] is not None:
                                        actual_first.add(int(float(actual[i])))
                                except (ValueError, TypeError) as e:
                                    logger.warning(f"無法將實際值 {actual[i]} 轉換為整數: {e}")
                            
                            # 計算匹配數量
                            match_count = len(pred_first.intersection(actual_first))
                            logger.info(f"計算的匹配數量: {match_count}, 預測號碼: {pred_first}, 實際號碼: {actual_first}")
                            
                            # 第二區匹配 - 更加穩健的計算方式
                            second_match = 0
                            try:
                                if pred[8] is not None and actual[6] is not None:
                                    second_match = 1 if int(float(pred[8])) == int(float(actual[6])) else 0
                            except (ValueError, TypeError) as e:
                                logger.warning(f"無法比較第二區值: {e}")
                            
                            # 更新預測結果
                            cursor.execute("""
                            UPDATE PowercolorPredictions
                            SET ActualA1 = ?, ActualA2 = ?, ActualA3 = ?, ActualA4 = ?, ActualA5 = ?, ActualA6 = ?, 
                                ActualS = ?, MatchCount = ?, SecondMatch = ?
                            WHERE ID = ?
                            """, (
                                actual[0], actual[1], actual[2], actual[3], actual[4], actual[5],
                                actual[6], match_count, second_match, pred_id
                            ))
                            
                            updated_count += 1
                            logger.info(f"已更新期數 {period} 的預測結果比對: 第一區匹配 {match_count} 個, 第二區匹配: {'是' if second_match else '否'}")
                        except Exception as e:
                            logger.error(f"計算或更新期數 {period} 的匹配數量時出錯: {str(e)}")
                    else:
                        logger.info(f"找不到期數 {period} 的實際開獎結果")
                
                # 提交更新
                conn.commit()
                logger.info(f"總共更新了 {updated_count} 筆預測記錄的實際結果")
                
                conn.close()
                return updated_count
            
            elif lottery_type.lower() == 'lotto649':
                # 獲取尚未更新結果的預測記錄
                cursor.execute("""
                SELECT p.ID, p.Period, p.PredA1, p.PredA2, p.PredA3, p.PredA4, p.PredA5, p.PredA6, p.PredSpecial
                FROM Lotto649Predictions p
                WHERE p.ActualA1 IS NULL
                """)
                
                predictions = cursor.fetchall()
                
                if len(predictions) == 0:
                    logger.info("沒有需要更新的預測記錄")
                    conn.close()
                    return 0
                    
                logger.info(f"找到 {len(predictions)} 筆需要更新的預測記錄")
                
                updated_count = 0
                
                for pred in predictions:
                    pred_id, period = pred[0], pred[1]
                    
                    # 查詢實際開獎結果
                    cursor.execute("""
                    SELECT Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, SpecialNumber
                    FROM Lotto649
                    WHERE Period = ?
                    """, (period,))
                    
                    actual = cursor.fetchone()
                    
                    if actual:
                        logger.info(f"找到期數 {period} 的實際開獎結果: {actual}")
                        
                        try:
                            # 預測號碼轉為整數集合
                            pred_first = set()
                            for i in range(6):
                                try:
                                    pred_first.add(int(float(pred[i+2])))
                                except (ValueError, TypeError) as e:
                                    logger.warning(f"無法將預測值 {pred[i+2]} 轉換為整數: {e}")
                            
                            # 實際號碼轉為整數集合
                            actual_first = set()
                            for i in range(6):
                                try:
                                    if actual[i] is not None:
                                        actual_first.add(int(float(actual[i])))
                                except (ValueError, TypeError) as e:
                                    logger.warning(f"無法將實際值 {actual[i]} 轉換為整數: {e}")
                            
                            # 計算匹配數量
                            match_count = len(pred_first.intersection(actual_first))
                            logger.info(f"計算的匹配數量: {match_count}, 預測號碼: {pred_first}, 實際號碼: {actual_first}")
                            
                            # 特別號匹配 - 更加穩健的計算方式
                            special_match = 0
                            try:
                                if pred[8] is not None and actual[6] is not None:
                                    special_match = 1 if int(float(pred[8])) == int(float(actual[6])) else 0
                            except (ValueError, TypeError) as e:
                                logger.warning(f"無法比較特別號值: {e}")
                            
                            # 更新預測結果
                            cursor.execute("""
                            UPDATE Lotto649Predictions
                            SET ActualA1 = ?, ActualA2 = ?, ActualA3 = ?, ActualA4 = ?, ActualA5 = ?, ActualA6 = ?, 
                                ActualSpecial = ?, MatchCount = ?, SpecialMatch = ?
                            WHERE ID = ?
                            """, (
                                actual[0], actual[1], actual[2], actual[3], actual[4], actual[5],
                                actual[6], match_count, special_match, pred_id
                            ))
                            
                            updated_count += 1
                            logger.info(f"已更新期數 {period} 的預測結果比對: 第一區匹配 {match_count} 個, 特別號匹配: {'是' if special_match else '否'}")
                        except Exception as e:
                            logger.error(f"計算或更新期數 {period} 的匹配數量時出錯: {str(e)}")
                    else:
                        logger.info(f"找不到期數 {period} 的實際開獎結果")
            
            elif lottery_type.lower() == 'dailycash':
                # 獲取尚未更新結果的預測記錄
                cursor.execute("""
                SELECT p.ID, p.Period, p.PredA1, p.PredA2, p.PredA3, p.PredA4, p.PredA5
                FROM DailyCashPredictions p
                WHERE p.ActualA1 IS NULL
                """)
                
                predictions = cursor.fetchall()
                
                if len(predictions) == 0:
                    logger.info("沒有需要更新的預測記錄")
                    conn.close()
                    return 0
                    
                logger.info(f"找到 {len(predictions)} 筆需要更新的預測記錄")
                
                updated_count = 0
                
                for pred in predictions:
                    pred_id, period = pred[0], pred[1]
                    
                    # 查詢實際開獎結果
                    cursor.execute("""
                    SELECT Anumber1, Anumber2, Anumber3, Anumber4, Anumber5
                    FROM DailyCash
                    WHERE Period = ?
                    """, (period,))
                    
                    actual = cursor.fetchone()
                    
                    if actual:
                        logger.info(f"找到期數 {period} 的實際開獎結果: {actual}")
                        
                        try:
                            # 預測號碼轉為整數集合
                            pred_nums = set()
                            for i in range(5):
                                try:
                                    pred_nums.add(int(float(pred[i+2])))
                                except (ValueError, TypeError) as e:
                                    logger.warning(f"無法將預測值 {pred[i+2]} 轉換為整數: {e}")
                            
                            # 實際號碼轉為整數集合
                            actual_nums = set()
                            for i in range(5):
                                try:
                                    if actual[i] is not None:
                                        actual_nums.add(int(float(actual[i])))
                                except (ValueError, TypeError) as e:
                                    logger.warning(f"無法將實際值 {actual[i]} 轉換為整數: {e}")
                            
                            # 計算匹配數量
                            match_count = len(pred_nums.intersection(actual_nums))
                            logger.info(f"計算的匹配數量: {match_count}, 預測號碼: {pred_nums}, 實際號碼: {actual_nums}")
                            
                            # 更新預測結果
                            cursor.execute("""
                            UPDATE DailyCashPredictions
                            SET ActualA1 = ?, ActualA2 = ?, ActualA3 = ?, ActualA4 = ?, ActualA5 = ?, 
                                MatchCount = ?
                            WHERE ID = ?
                            """, (
                                actual[0], actual[1], actual[2], actual[3], actual[4],
                                match_count, pred_id
                            ))
                            
                            updated_count += 1
                            logger.info(f"已更新期數 {period} 的預測結果比對: 匹配 {match_count} 個號碼")
                        except Exception as e:
                            logger.error(f"計算或更新期數 {period} 的匹配數量時出錯: {str(e)}")
                    else:
                        logger.info(f"找不到期數 {period} 的實際開獎結果")
                
            # 提交更新
            conn.commit()
            logger.info(f"總共更新了 {updated_count} 筆預測記錄的實際結果")
            
            conn.close()
            return updated_count
            
        except Exception as e:
            logger.error(f"更新預測結果比對時出錯: {str(e)}")
            if 'conn' in locals():
                conn.rollback()
            if 'conn' in locals():
                conn.close()
            return 0
    
    def load_prediction_records(self, lottery_type='powercolor', limit=10, period=None, with_metadata=False, metadata_filter=None):
        """加載預測記錄，支援元數據關聯查詢
        
        Args:
            lottery_type: 彩票類型
            limit: 要加載的記錄數量，若為None則不限制數量
            period: 指定期數，若提供則只返回該期數的所有預測記錄
            with_metadata: 是否包含元數據
            metadata_filter: 元數據篩選條件，格式為 {key: value}
            
        Returns:
            DataFrame: 預測記錄
        """
        try:
            if period:
                logger.info(f"正在加載期數 {period} 的{self._get_lottery_name(lottery_type)}預測記錄...")
            else:
                logger.info(f"正在加載最近 {limit} 筆{self._get_lottery_name(lottery_type)}預測記錄...")
            
            conn = self.create_connection()
            
            # 根據彩票類型選擇表名
            if lottery_type.lower() == 'powercolor':
                table_name = 'PowercolorPredictions'
            elif lottery_type.lower() == 'lotto649':
                table_name = 'Lotto649Predictions'
            elif lottery_type.lower() == 'dailycash':
                table_name = 'DailyCashPredictions'
            else:
                raise ValueError(f"不支援的彩票類型: {lottery_type}")
            
            # 構建基本查詢
            query = f"SELECT * FROM {table_name} "
            
            # 添加條件
            params = []
            if period:
                query += "WHERE Period = ? "
                params.append(period)
            
            # 添加排序和限制
            query += "ORDER BY PredictionDate DESC "
            # 檢查資料庫類型
            if 'sqlite' in str(type(conn)).lower() or 'mysql' in str(type(conn)).lower():
                if limit is None:
                    query = f"SELECT * FROM {table_name} ORDER BY PredictionDate DESC"
                else:
                    query = f"SELECT * FROM {table_name} ORDER BY PredictionDate DESC LIMIT {limit}"
            else:  # 假設是 SQL Server
                if limit is None:
                    query = f"SELECT * FROM {table_name} ORDER BY PredictionDate DESC"
                else:
                    query = f"SELECT TOP {limit} * FROM {table_name} ORDER BY PredictionDate DESC"
            
            df = pd.read_sql(query, conn)
            
            # 如果需要元數據，進行關聯查詢
            if with_metadata and not df.empty:
                # 獲取所有預測ID
                prediction_ids = df['PredictionID'].dropna().unique().tolist()
                
                if prediction_ids:
                    # 構建元數據查詢
                    metadata_query = """
                    SELECT PredictionID, MetadataKey, MetadataValue
                    FROM PredictionMetadata
                    WHERE PredictionID IN ({})
                    """.format(','.join(['?'] * len(prediction_ids)))
                    
                    # 添加元數據篩選條件
                    if metadata_filter:
                        for key, value in metadata_filter.items():
                            metadata_query += f" AND MetadataKey = ? AND MetadataValue LIKE ?"
                            params.extend([key, f"%{value}%"])
                    
                    # 執行元數據查詢
                    metadata_df = pd.read_sql(metadata_query, conn, params=prediction_ids)
                    
                    # 如果有元數據篩選條件，只保留符合條件的預測記錄
                    if metadata_filter and not metadata_df.empty:
                        valid_prediction_ids = metadata_df['PredictionID'].unique()
                        df = df[df['PredictionID'].isin(valid_prediction_ids)]
                    
                    # 將元數據添加到主DataFrame
                    if not metadata_df.empty:
                        # 轉換成字典格式
                        metadata_dict = {}
                        for _, row in metadata_df.iterrows():
                            pred_id = row['PredictionID']
                            key = row['MetadataKey']
                            value = row['MetadataValue']
                            
                            if pred_id not in metadata_dict:
                                metadata_dict[pred_id] = {}
                            
                            try:
                                metadata_dict[pred_id][key] = json.loads(value)
                            except:
                                metadata_dict[pred_id][key] = value
                        
                        # 添加元數據列
                        df['Metadata'] = df['PredictionID'].map(lambda x: metadata_dict.get(x, {}))
            
            conn.close()
            
            # 轉換日期時間格式
            if 'PredictionDate' in df.columns:
                try:
                    df['PredictionDate'] = pd.to_datetime(df['PredictionDate'])
                except:
                    pass
            
            # 嘗試解析JSON字符串列
            for json_col in ['MLResult', 'BoardPathResult', 'PredictionDetails']:
                if json_col in df.columns:
                    def parse_json(x):
                        if x and isinstance(x, str):
                            try:
                                return json.loads(x)
                            except:
                                return None
                        return x
                    
                    df[json_col] = df[json_col].apply(parse_json)
            
            if period:
                logger.info(f"已加載期數 {period} 的 {len(df)} 筆{self._get_lottery_name(lottery_type)}預測記錄")
            else:
                logger.info(f"已加載 {len(df)} 筆{self._get_lottery_name(lottery_type)}預測記錄")
            return df
            
        except Exception as e:
            logger.error(f"加載預測記錄時出錯: {str(e)}")
            logger.error(traceback.format_exc())
            return pd.DataFrame()  # 返回空DataFrame
    
    def _get_lottery_name(self, lottery_type):
        """根據彩票類型獲取中文名稱"""
        name_map = {
            'powercolor': '威力彩',
            'lotto649': '大樂透',
            'dailycash': '今彩539'
        }
        return name_map.get(lottery_type.lower(), lottery_type)
    
    def get_periods_list(self, lottery_type='powercolor', limit=50):
        """獲取期號列表
        
        Args:
            lottery_type: 彩票類型
            limit: 返回期號數量限制
            
        Returns:
            list: 期號列表，按期號降序排列
        """
        try:
            conn = self.create_connection()
            
            # 根據彩票類型選擇表名
            table_map = {
                'powercolor': 'Powercolor',
                'lotto649': 'Lotto649',
                'dailycash': 'DailyCash'
            }
            
            table_name = table_map.get(lottery_type.lower())
            if not table_name:
                raise ValueError(f"不支援的彩票類型: {lottery_type}")
            
            query = f"""
            SELECT DISTINCT Period 
            FROM {table_name} 
            ORDER BY Period DESC 
            LIMIT ?
            """
            
            cursor = conn.cursor()
            cursor.execute(query, (limit,))
            periods = [row[0] for row in cursor.fetchall()]
            
            conn.close()
            logger.info(f"獲取到 {len(periods)} 個{self._get_lottery_name(lottery_type)}期號")
            return periods
            
        except Exception as e:
            logger.error(f"獲取期號列表時出錯: {str(e)}")
            return []
    
    def get_latest_period(self, lottery_type='powercolor'):
        """獲取最新期號
        
        Args:
            lottery_type: 彩票類型
            
        Returns:
            str: 最新期號
        """
        try:
            conn = self.create_connection()
            
            # 根據彩票類型選擇表名
            table_map = {
                'powercolor': 'Powercolor',
                'lotto649': 'Lotto649',
                'dailycash': 'DailyCash'
            }
            
            table_name = table_map.get(lottery_type.lower())
            if not table_name:
                raise ValueError(f"不支援的彩票類型: {lottery_type}")
            
            query = f"""
            SELECT Period 
            FROM {table_name} 
            ORDER BY Period DESC 
            LIMIT 1
            """
            
            cursor = conn.cursor()
            cursor.execute(query)
            result = cursor.fetchone()
            
            conn.close()
            
            if result:
                latest_period = result[0]
                logger.info(f"獲取到{self._get_lottery_name(lottery_type)}最新期號: {latest_period}")
                return latest_period
            else:
                logger.warning(f"未找到{self._get_lottery_name(lottery_type)}的期號資料")
                return None
                
        except Exception as e:
            logger.error(f"獲取最新期號時出錯: {str(e)}")
            return None
    
    def get_next_period(self, lottery_type='powercolor'):
        """獲取下一期號
        
        Args:
            lottery_type: 彩票類型
            
        Returns:
            str: 下一期號
        """
        try:
            latest_period = self.get_latest_period(lottery_type)
            if latest_period:
                # 台彩期數格式為：民國年(3位) + 期數(4位)，例如：114000053
                # 需要正確解析並計算下一期
                period_str = str(latest_period)
                
                if len(period_str) >= 7:  # 台彩官方格式
                    # 提取年份部分（前3位）和期數部分（後4位）
                    year_part = period_str[:3]
                    period_part = period_str[3:]
                    
                    # 期數加1
                    next_period_num = int(period_part) + 1
                    
                    # 檢查是否需要跨年
                    from datetime import datetime
                    current_year = datetime.now().year - 1911  # 轉換為民國年
                    
                    if int(year_part) < current_year:
                        # 跨年了，重置期數
                        next_period = f"{current_year:03d}0001"
                    else:
                        # 同年內，期數遞增
                        next_period = f"{year_part}{next_period_num:04d}"
                else:
                    # 舊格式或其他格式，直接加1
                    next_period = str(int(latest_period) + 1)
                
                logger.info(f"計算出{self._get_lottery_name(lottery_type)}下一期號: {next_period}")
                return next_period
            else:
                logger.warning(f"無法計算{self._get_lottery_name(lottery_type)}下一期號")
                return None
                
        except Exception as e:
            logger.error(f"計算下一期號時出錯: {str(e)}")
            return None
    
    def get_period_data(self, period, lottery_type='powercolor'):
        """獲取指定期號的開獎資料
        
        Args:
            period: 期號
            lottery_type: 彩票類型
            
        Returns:
            dict: 開獎資料，如果期號不存在則返回None
        """
        try:
            conn = self.create_connection()
            
            # 根據彩票類型選擇表名和查詢
            if lottery_type.lower() == 'powercolor':
                query = """
                SELECT Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, Second_district
                FROM Powercolor
                WHERE Period = ?
                """
            elif lottery_type.lower() == 'lotto649':
                query = """
                SELECT Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, SpecialNumber
                FROM Lotto649
                WHERE Period = ?
                """
            elif lottery_type.lower() == 'dailycash':
                query = """
                SELECT Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5
                FROM DailyCash
                WHERE Period = ?
                """
            else:
                raise ValueError(f"不支援的彩票類型: {lottery_type}")
            
            cursor = conn.cursor()
            cursor.execute(query, (period,))
            result = cursor.fetchone()
            
            conn.close()
            
            if result:
                # 根據彩票類型構建返回資料
                if lottery_type.lower() == 'powercolor':
                    return {
                        'period': result[0],
                        'date': result[1],
                        'main_numbers': [result[2], result[3], result[4], result[5], result[6], result[7]],
                        'special_number': result[8]
                    }
                elif lottery_type.lower() == 'lotto649':
                    return {
                        'period': result[0],
                        'date': result[1],
                        'main_numbers': [result[2], result[3], result[4], result[5], result[6], result[7]],
                        'special_number': result[8]
                    }
                elif lottery_type.lower() == 'dailycash':
                    return {
                        'period': result[0],
                        'date': result[1],
                        'main_numbers': [result[2], result[3], result[4], result[5], result[6]]
                    }
            else:
                logger.info(f"期號 {period} 的{self._get_lottery_name(lottery_type)}資料不存在")
                return None
                
        except Exception as e:
            logger.error(f"獲取期號 {period} 資料時出錯: {str(e)}")
            return None
    
    def get_prediction_by_period(self, period, lottery_type='powercolor'):
        """獲取指定期號的預測資料
        
        Args:
            period: 期號
            lottery_type: 彩票類型
            
        Returns:
            dict: 預測資料，如果期號不存在則返回None
        """
        try:
            conn = self.create_connection()
            
            # 根據彩票類型選擇表名和查詢
            if lottery_type.lower() == 'powercolor':
                query = """
                SELECT Period, PredictionDate, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredS,
                       ModelVersion, PredictionMethod, Confidence
                FROM PowercolorPredictions
                WHERE Period = ?
                ORDER BY PredictionDate DESC
                LIMIT 1
                """
            elif lottery_type.lower() == 'lotto649':
                query = """
                SELECT Period, PredictionDate, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, PredSpecial,
                       ModelVersion, PredictionMethod, Confidence
                FROM Lotto649Predictions
                WHERE Period = ?
                ORDER BY PredictionDate DESC
                LIMIT 1
                """
            elif lottery_type.lower() == 'dailycash':
                query = """
                SELECT Period, PredictionDate, PredA1, PredA2, PredA3, PredA4, PredA5,
                       ModelVersion, PredictionMethod, Confidence
                FROM DailyCashPredictions
                WHERE Period = ?
                ORDER BY PredictionDate DESC
                LIMIT 1
                """
            else:
                raise ValueError(f"不支援的彩票類型: {lottery_type}")
            
            cursor = conn.cursor()
            cursor.execute(query, (period,))
            result = cursor.fetchone()
            
            conn.close()
            
            if result:
                # 根據彩票類型構建返回資料
                if lottery_type.lower() == 'powercolor':
                    return {
                        'period': result[0],
                        'prediction_date': result[1],
                        'main_numbers': [result[2], result[3], result[4], result[5], result[6], result[7]],
                        'special_number': result[8],
                        'model_version': result[9],
                        'prediction_method': result[10],
                        'confidence': result[11]
                    }
                elif lottery_type.lower() == 'lotto649':
                    return {
                        'period': result[0],
                        'prediction_date': result[1],
                        'main_numbers': [result[2], result[3], result[4], result[5], result[6], result[7]],
                        'special_number': result[8],
                        'model_version': result[9],
                        'prediction_method': result[10],
                        'confidence': result[11]
                    }
                elif lottery_type.lower() == 'dailycash':
                    return {
                        'period': result[0],
                        'prediction_date': result[1],
                        'main_numbers': [result[2], result[3], result[4], result[5], result[6]],
                        'model_version': result[7],
                        'prediction_method': result[8],
                        'confidence': result[9]
                    }
            else:
                logger.info(f"期號 {period} 的{self._get_lottery_name(lottery_type)}預測資料不存在")
                return None
                
        except Exception as e:
            logger.error(f"獲取期號 {period} 預測資料時出錯: {str(e)}")
            return None
    def get_prediction_metadata(self, prediction_id=None, metadata_key=None, lottery_type=None, period=None, limit=None):
        """獲取預測元數據
        
        Args:
            prediction_id: 預測ID，可選
            metadata_key: 元數據鍵名，可選
            lottery_type: 彩票類型，可選
            period: 期數，可選
            limit: 返回結果數量限制，可選
            
        Returns:
            dict 或 list: 元數據資訊
        """
        try:
            conn = self.create_connection()
            cursor = conn.cursor()
            
            # 構建基本查詢
            query = """
            SELECT pm.PredictionID, pm.MetadataKey, pm.MetadataValue, pm.CreatedAt
            FROM PredictionMetadata pm
            """
            
            # 如果有預測ID，直接查詢
            if prediction_id:
                query += " WHERE pm.PredictionID = ?"
                params = [prediction_id]
            # 否則，通過其他條件查詢
            else:
                # 先找出符合條件的預測ID
                pred_id_query = "SELECT PredictionID FROM "
                
                # 根據彩票類型選擇表
                if lottery_type == 'powercolor':
                    pred_id_query += "PowercolorPredictions"
                elif lottery_type == 'lotto649':
                    pred_id_query += "Lotto649Predictions"
                elif lottery_type == 'dailycash':
                    pred_id_query += "DailyCashPredictions"
                else:
                    raise ValueError(f"不支援的彩票類型: {lottery_type}")
                    
                # 添加期數條件（如果有）
                if period:
                    pred_id_query += f" WHERE Period = '{period}'"
                    
                # 獲取最近的預測（如果有限制）
                if limit:
                    pred_id_query += f" ORDER BY PredictionDate DESC LIMIT {limit}"
                    
                # 取得符合條件的預測ID列表
                cursor.execute(pred_id_query)
                prediction_ids = [row[0] for row in cursor.fetchall() if row[0]]
                
                if not prediction_ids:
                    logger.warning(f"沒有找到符合條件的預測記錄")
                    return []
                    
                # 根據預測ID列表和元數據鍵名查詢
                placeholders = ', '.join(['?'] * len(prediction_ids))
                query += f" WHERE pm.PredictionID IN ({placeholders})"
                params = prediction_ids
                
                if metadata_key:
                    query += " AND pm.MetadataKey = ?"
                    params.append(metadata_key)
                    
            # 執行查詢
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 整理結果
            result = {}
            for row in rows:
                pred_id, key, value, created_at = row
                
                if pred_id not in result:
                    result[pred_id] = {}
                    
                # 嘗試解析JSON值
                try:
                    parsed_value = json.loads(value)
                    result[pred_id][key] = parsed_value
                except (json.JSONDecodeError, TypeError):
                    result[pred_id][key] = value
                    
            conn.close()
            
            # 如果只查詢一個預測ID，直接返回其元數據
            if prediction_id:
                return result.get(prediction_id, {})
            
            return result
        except Exception as e:
            logger.error(f"獲取預測元數據時出錯: {str(e)}")
            logger.error(traceback.format_exc())
            if 'conn' in locals():
                conn.close()
            return {}
    
    def get_database_config(self):
        """獲取數據庫配置信息
        
        Returns:
            dict: 數據庫配置信息
        """
        return {
            'path': self.db_path,
            'type': 'sqlite',
            'status': 'connected' if os.path.exists(self.db_path) else 'not_found'
        }
    
    def _ensure_phase3_tables_exist(self, cursor):
        """確保Phase 3所需的表格存在（基本版本）"""
        try:
            # 簡化版的預測追蹤表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS predictions_tracking (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                prediction_id TEXT NOT NULL UNIQUE,
                lottery_type TEXT NOT NULL,
                period TEXT NOT NULL,
                predicted_main_numbers TEXT NOT NULL,
                predicted_special_numbers TEXT,
                actual_main_numbers TEXT,
                actual_special_numbers TEXT,
                strategy_used TEXT NOT NULL,
                confidence REAL,
                match_count INTEGER DEFAULT 0,
                special_match INTEGER DEFAULT 0,
                prize_level TEXT,
                prediction_date TEXT NOT NULL,
                draw_date TEXT,
                verification_date TEXT,
                is_verified INTEGER DEFAULT 0,
                accuracy_score REAL,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
            """)
            
            # 統計報告表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS statistics_reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                report_id TEXT NOT NULL UNIQUE,
                lottery_type TEXT,
                timeframe TEXT NOT NULL,
                start_date TEXT NOT NULL,
                end_date TEXT NOT NULL,
                total_predictions INTEGER DEFAULT 0,
                successful_predictions INTEGER DEFAULT 0,
                accuracy_rate REAL DEFAULT 0.0,
                accuracy_metrics TEXT,
                strategy_performance TEXT,
                trend_analysis TEXT,
                recommendations TEXT,
                generated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
            """)
            
            # 實時數據狀態表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS realtime_data_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                data_source TEXT NOT NULL,
                lottery_type TEXT NOT NULL,
                last_update_time TEXT NOT NULL,
                sync_status TEXT DEFAULT 'pending',
                records_count INTEGER DEFAULT 0,
                error_message TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
            """)
            
            # 創建基本索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_predictions_tracking_lottery_period ON predictions_tracking(lottery_type, period)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_statistics_reports_timeframe ON statistics_reports(timeframe, start_date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_realtime_data_status_source ON realtime_data_status(data_source, lottery_type)")
            
            logger.info("Phase 3基礎表格檢查完成")
            
        except Exception as e:
            logger.warning(f"Phase 3表格創建失敗: {e}")
            # 不影響主程序運行

# 測試代碼
if __name__ == "__main__":
    from config_manager import ConfigManager
    config_manager = ConfigManager()
    db = DBManager(config_manager)
    # 測試加載數據
    powercolor_df = db.load_lottery_data('powercolor')
    print("威力彩資料:", powercolor_df.shape)
    
    lotto649_df = db.load_lottery_data('lotto649')
    print("大樂透資料:", lotto649_df.shape)
    
    dailycash_df = db.load_lottery_data('dailycash')
    print("今彩539資料:", dailycash_df.shape)