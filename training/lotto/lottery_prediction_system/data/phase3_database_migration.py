#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phase 3 數據庫遷移腳本
創建Phase 3所需的數據庫表
"""

import os
import sys
import sqlite3
import logging
from datetime import datetime

# 添加項目根目錄到Python路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.db_manager import DBManager

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('phase3_migration')

class Phase3DatabaseMigration:
    """Phase 3數據庫遷移類"""
    
    def __init__(self, db_manager: DBManager = None):
        if db_manager:
            self.db_manager = db_manager
        else:
            self.db_manager = DBManager()
        self.conn = None
    
    def migrate(self):
        """執行完整的Phase 3數據庫遷移"""
        try:
            logger.info("開始Phase 3數據庫遷移...")
            
            self.conn = self.db_manager.create_connection()
            cursor = self.conn.cursor()
            
            # 創建所有Phase 3表
            self._create_predictions_tracking_table(cursor)
            self._create_statistics_reports_table(cursor)
            self._create_accuracy_metrics_table(cursor)
            self._create_realtime_data_status_table(cursor)
            self._create_scheduler_jobs_table(cursor)
            self._create_visualization_reports_table(cursor)
            self._create_cross_lottery_learning_table(cursor)
            self._create_strategy_optimization_table(cursor)
            
            self.conn.commit()
            logger.info("✅ Phase 3數據庫遷移完成")
            
        except Exception as e:
            logger.error(f"❌ Phase 3數據庫遷移失敗: {e}")
            if self.conn:
                self.conn.rollback()
            raise
        finally:
            if self.conn:
                self.conn.close()
    
    def _create_predictions_tracking_table(self, cursor):
        """創建預測追蹤表 - 擴展版本"""
        logger.info("創建預測追蹤表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS predictions_tracking (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            prediction_id TEXT NOT NULL UNIQUE,
            lottery_type TEXT NOT NULL,
            period TEXT NOT NULL,
            predicted_main_numbers TEXT NOT NULL,  -- JSON格式存儲
            predicted_special_numbers TEXT,        -- JSON格式存儲
            actual_main_numbers TEXT,              -- JSON格式存儲
            actual_special_numbers TEXT,           -- JSON格式存儲
            strategy_used TEXT NOT NULL,
            confidence REAL,
            algorithm_weights TEXT,                -- JSON格式存儲算法權重
            match_count INTEGER DEFAULT 0,
            special_match INTEGER DEFAULT 0,
            prize_level TEXT,
            prediction_date TEXT NOT NULL,
            draw_date TEXT,
            verification_date TEXT,
            is_verified INTEGER DEFAULT 0,
            accuracy_score REAL,
            performance_metrics TEXT,              -- JSON格式存儲性能指標
            metadata TEXT,                         -- JSON格式存儲額外元數據
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 創建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_predictions_tracking_lottery_period ON predictions_tracking(lottery_type, period)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_predictions_tracking_prediction_date ON predictions_tracking(prediction_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_predictions_tracking_strategy ON predictions_tracking(strategy_used)")
    
    def _create_statistics_reports_table(self, cursor):
        """創建統計報告表"""
        logger.info("創建統計報告表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS statistics_reports (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            report_id TEXT NOT NULL UNIQUE,
            lottery_type TEXT,
            timeframe TEXT NOT NULL,              -- daily, weekly, monthly, quarterly, yearly
            start_date TEXT NOT NULL,
            end_date TEXT NOT NULL,
            total_predictions INTEGER DEFAULT 0,
            successful_predictions INTEGER DEFAULT 0,
            accuracy_rate REAL DEFAULT 0.0,
            accuracy_metrics TEXT,                -- JSON格式存儲詳細準確度指標
            strategy_performance TEXT,            -- JSON格式存儲策略性能比較
            trend_analysis TEXT,                  -- JSON格式存儲趨勢分析
            recommendations TEXT,                 -- JSON格式存儲改進建議
            report_data TEXT,                     -- JSON格式存儲完整報告數據
            generated_at TEXT DEFAULT CURRENT_TIMESTAMP,
            expires_at TEXT
        )
        """)
        
        # 創建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_statistics_reports_timeframe ON statistics_reports(timeframe, start_date, end_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_statistics_reports_lottery ON statistics_reports(lottery_type)")
    
    def _create_accuracy_metrics_table(self, cursor):
        """創建準確度指標表"""
        logger.info("創建準確度指標表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS accuracy_metrics (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            metric_id TEXT NOT NULL UNIQUE,
            lottery_type TEXT NOT NULL,
            strategy_name TEXT NOT NULL,
            metric_type TEXT NOT NULL,            -- overall, daily, weekly, monthly
            metric_period TEXT NOT NULL,          -- 指標計算期間
            total_predictions INTEGER DEFAULT 0,
            correct_predictions INTEGER DEFAULT 0,
            accuracy_rate REAL DEFAULT 0.0,
            precision_score REAL DEFAULT 0.0,
            recall_score REAL DEFAULT 0.0,
            f1_score REAL DEFAULT 0.0,
            confidence_correlation REAL DEFAULT 0.0,
            hit_rate_by_position TEXT,            -- JSON格式存儲按位置的命中率
            match_distribution TEXT,              -- JSON格式存儲匹配數分佈
            performance_trends TEXT,              -- JSON格式存儲性能趨勢
            calculated_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 創建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_accuracy_metrics_lottery_strategy ON accuracy_metrics(lottery_type, strategy_name)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_accuracy_metrics_type_period ON accuracy_metrics(metric_type, metric_period)")
    
    def _create_realtime_data_status_table(self, cursor):
        """創建實時數據狀態表"""
        logger.info("創建實時數據狀態表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS realtime_data_status (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            data_source TEXT NOT NULL,            -- lottery_data, predictions, analysis
            lottery_type TEXT NOT NULL,
            last_update_time TEXT NOT NULL,
            last_sync_time TEXT,
            sync_status TEXT DEFAULT 'pending',   -- pending, syncing, completed, failed
            records_count INTEGER DEFAULT 0,
            error_message TEXT,
            retry_count INTEGER DEFAULT 0,
            next_sync_time TEXT,
            sync_interval INTEGER DEFAULT 3600,   -- 同步間隔（秒）
            data_version TEXT,
            checksum TEXT,                        -- 數據完整性校驗
            metadata TEXT,                        -- JSON格式存儲額外信息
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 創建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_realtime_data_source_lottery ON realtime_data_status(data_source, lottery_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_realtime_data_sync_status ON realtime_data_status(sync_status)")
    
    def _create_scheduler_jobs_table(self, cursor):
        """創建調度任務表"""
        logger.info("創建調度任務表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS scheduler_jobs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            job_id TEXT NOT NULL UNIQUE,
            job_name TEXT NOT NULL,
            job_type TEXT NOT NULL,               -- prediction, data_update, report_generation, cleanup
            lottery_type TEXT,
            schedule_expression TEXT NOT NULL,    -- cron表達式或間隔描述
            is_active INTEGER DEFAULT 1,
            last_run_time TEXT,
            next_run_time TEXT,
            run_count INTEGER DEFAULT 0,
            success_count INTEGER DEFAULT 0,
            failure_count INTEGER DEFAULT 0,
            last_result TEXT,                     -- success, failed, partial
            last_error TEXT,
            execution_time REAL DEFAULT 0.0,     -- 執行時間（秒）
            job_config TEXT,                      -- JSON格式存儲任務配置
            created_by TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 創建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_scheduler_jobs_type_active ON scheduler_jobs(job_type, is_active)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_scheduler_jobs_next_run ON scheduler_jobs(next_run_time)")
    
    def _create_visualization_reports_table(self, cursor):
        """創建可視化報告表"""
        logger.info("創建可視化報告表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS visualization_reports (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            report_id TEXT NOT NULL UNIQUE,
            report_name TEXT NOT NULL,
            report_type TEXT NOT NULL,            -- accuracy_trend, strategy_comparison, number_heatmap, dashboard
            lottery_type TEXT,
            report_format TEXT DEFAULT 'json',    -- json, png, svg, html
            report_data TEXT,                     -- JSON格式存儲報告數據
            chart_config TEXT,                    -- JSON格式存儲圖表配置
            file_path TEXT,                       -- 如果是文件格式的報告
            file_size INTEGER DEFAULT 0,
            parameters TEXT,                      -- JSON格式存儲生成參數
            generated_by TEXT,
            generated_at TEXT DEFAULT CURRENT_TIMESTAMP,
            expires_at TEXT,
            is_cached INTEGER DEFAULT 1,
            cache_key TEXT,
            download_count INTEGER DEFAULT 0,
            last_accessed TEXT
        )
        """)
        
        # 創建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_visualization_reports_type ON visualization_reports(report_type, lottery_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_visualization_reports_generated ON visualization_reports(generated_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_visualization_reports_cache ON visualization_reports(cache_key)")
    
    def _create_cross_lottery_learning_table(self, cursor):
        """創建跨彩票學習表"""
        logger.info("創建跨彩票學習表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS cross_lottery_learning (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            learning_id TEXT NOT NULL UNIQUE,
            source_lottery_type TEXT NOT NULL,
            target_lottery_type TEXT NOT NULL,
            pattern_type TEXT NOT NULL,           -- frequency, sequence, correlation, timing
            pattern_data TEXT NOT NULL,           -- JSON格式存儲模式數據
            correlation_strength REAL DEFAULT 0.0,
            confidence_score REAL DEFAULT 0.0,
            validation_results TEXT,              -- JSON格式存儲驗證結果
            application_count INTEGER DEFAULT 0,
            success_rate REAL DEFAULT 0.0,
            last_validated TEXT,
            is_active INTEGER DEFAULT 1,
            learning_algorithm TEXT,
            feature_importance TEXT,              -- JSON格式存儲特徵重要性
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 創建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_cross_lottery_source_target ON cross_lottery_learning(source_lottery_type, target_lottery_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_cross_lottery_pattern_type ON cross_lottery_learning(pattern_type, is_active)")
    
    def _create_strategy_optimization_table(self, cursor):
        """創建策略優化表"""
        logger.info("創建策略優化表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS strategy_optimization (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            optimization_id TEXT NOT NULL UNIQUE,
            lottery_type TEXT NOT NULL,
            strategy_name TEXT NOT NULL,
            optimization_type TEXT NOT NULL,      -- weight_tuning, parameter_adjustment, ensemble_optimization
            baseline_performance REAL DEFAULT 0.0,
            optimized_performance REAL DEFAULT 0.0,
            improvement_percentage REAL DEFAULT 0.0,
            optimization_parameters TEXT,         -- JSON格式存儲優化參數
            optimization_results TEXT,            -- JSON格式存儲優化結果
            validation_scores TEXT,               -- JSON格式存儲驗證分數
            is_applied INTEGER DEFAULT 0,
            application_date TEXT,
            rollback_date TEXT,
            optimization_method TEXT,
            iterations INTEGER DEFAULT 0,
            convergence_criteria TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 創建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_strategy_optimization_lottery_strategy ON strategy_optimization(lottery_type, strategy_name)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_strategy_optimization_performance ON strategy_optimization(optimized_performance)")
    
    def check_migration_status(self) -> dict:
        """檢查遷移狀態"""
        try:
            conn = self.db_manager.create_connection()
            cursor = conn.cursor()
            
            phase3_tables = [
                'predictions_tracking',
                'statistics_reports',
                'accuracy_metrics',
                'realtime_data_status',
                'scheduler_jobs',
                'visualization_reports',
                'cross_lottery_learning',
                'strategy_optimization'
            ]
            
            existing_tables = []
            missing_tables = []
            
            for table in phase3_tables:
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
                if cursor.fetchone():
                    existing_tables.append(table)
                else:
                    missing_tables.append(table)
            
            conn.close()
            
            return {
                'total_tables': len(phase3_tables),
                'existing_tables': existing_tables,
                'missing_tables': missing_tables,
                'migration_complete': len(missing_tables) == 0,
                'migration_percentage': (len(existing_tables) / len(phase3_tables)) * 100
            }
            
        except Exception as e:
            logger.error(f"檢查遷移狀態失敗: {e}")
            return {
                'error': str(e),
                'migration_complete': False
            }


def main():
    """主函數"""
    try:
        logger.info("啟動Phase 3數據庫遷移工具...")
        
        # 檢查當前狀態
        migration = Phase3DatabaseMigration()
        status = migration.check_migration_status()
        
        print("\n" + "="*60)
        print("Phase 3 數據庫遷移狀態檢查")
        print("="*60)
        
        if 'error' in status:
            print(f"❌ 檢查失敗: {status['error']}")
            return
        
        print(f"總表數: {status['total_tables']}")
        print(f"已存在: {len(status['existing_tables'])} 個表")
        print(f"缺失: {len(status['missing_tables'])} 個表")
        print(f"完成度: {status['migration_percentage']:.1f}%")
        
        if status['missing_tables']:
            print(f"\n缺失的表: {', '.join(status['missing_tables'])}")
            
            # 執行遷移
            print("\n🚀 開始執行數據庫遷移...")
            migration.migrate()
            print("✅ 遷移完成!")
            
            # 再次檢查狀態
            final_status = migration.check_migration_status()
            print(f"\n最終完成度: {final_status['migration_percentage']:.1f}%")
        else:
            print("\n✅ 所有Phase 3表已存在，無需遷移")
            
    except Exception as e:
        logger.error(f"遷移過程失敗: {e}")
        print(f"❌ 遷移失敗: {e}")


if __name__ == "__main__":
    main()