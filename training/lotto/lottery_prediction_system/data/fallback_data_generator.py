#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
備用數據生成器
當官方API無法存取時，生成模擬的開獎數據供系統測試使用
"""

import random
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional

logger = logging.getLogger('fallback_data_generator')

class FallbackDataGenerator:
    """備用數據生成器"""
    
    def __init__(self):
        # 設定隨機種子確保一致性
        random.seed(datetime.now().date().toordinal())
        
        # 彩票配置
        self.lottery_configs = {
            'powercolor': {
                'number_range': (1, 38),
                'special_range': (1, 8),
                'number_count': 6,
                'has_special': True,
                'name': '威力彩'
            },
            'lotto649': {
                'number_range': (1, 49), 
                'special_range': (1, 49),
                'number_count': 6,
                'has_special': True,
                'name': '大樂透'
            },
            'dailycash': {
                'number_range': (1, 39),
                'special_range': None,
                'number_count': 5,
                'has_special': False,
                'name': '今彩539'
            }
        }
    
    def generate_latest_result(self, lottery_type: str) -> Optional[Dict]:
        """生成最新的開獎結果
        
        Args:
            lottery_type: 彩票類型
            
        Returns:
            Dict: 模擬的開獎結果
        """
        if lottery_type not in self.lottery_configs:
            logger.error(f"不支援的彩票類型: {lottery_type}")
            return None
            
        config = self.lottery_configs[lottery_type]
        logger.info(f"生成 {config['name']} 模擬開獎數據...")
        
        try:
            # 生成期號
            period = self._generate_current_period()
            
            # 生成開獎日期
            draw_date = self._get_latest_draw_date(lottery_type)
            
            # 生成主要號碼
            main_numbers = self._generate_main_numbers(config)
            
            # 構建結果
            result = {
                'Period': period,
                'Sdate': draw_date.strftime('%Y-%m-%d'),
                'lottery_type': lottery_type
            }
            
            # 添加主要號碼
            for i, number in enumerate(main_numbers, 1):
                result[f'Anumber{i}'] = number
            
            # 添加特別號
            if config['has_special']:
                special_number = self._generate_special_number(config, main_numbers)
                result['Second_district'] = special_number
            
            logger.info(f"生成 {config['name']} 數據完成: 期號 {period}")
            return result
            
        except Exception as e:
            logger.error(f"生成 {lottery_type} 模擬數據時發生錯誤: {str(e)}")
            return None
    
    def _generate_current_period(self) -> str:
        """生成當期期號"""
        current_date = datetime.now()
        year_suffix = str(current_date.year)[-3:]
        
        # 簡單計算：每週開獎兩次，一年約100期
        week_of_year = current_date.timetuple().tm_yday // 7
        draw_in_week = min(2, current_date.weekday() // 3)  # 週三、週六開獎
        period_number = min(week_of_year * 2 + draw_in_week, 999)
        
        return f"{year_suffix}{period_number:03d}"
    
    def _get_latest_draw_date(self, lottery_type: str) -> datetime:
        """獲取最新的開獎日期"""
        today = datetime.now()
        
        # 不同彩票有不同的開獎日
        if lottery_type == 'powercolor':
            # 威力彩：週一、週四
            days_back = 0
            while today.weekday() not in [0, 3]:  # 0=Monday, 3=Thursday
                today -= timedelta(days=1)
                days_back += 1
                if days_back > 7:
                    break
        elif lottery_type == 'lotto649':
            # 大樂透：週二、週五
            days_back = 0
            while today.weekday() not in [1, 4]:  # 1=Tuesday, 4=Friday
                today -= timedelta(days=1)
                days_back += 1
                if days_back > 7:
                    break
        else:
            # 今彩539：每日
            pass
        
        return today
    
    def _generate_main_numbers(self, config: Dict) -> List[int]:
        """生成主要號碼"""
        min_num, max_num = config['number_range']
        count = config['number_count']
        
        # 使用加權隨機確保號碼分布更真實
        numbers = []
        available_numbers = list(range(min_num, max_num + 1))
        
        for _ in range(count):
            # 給中間區間的號碼更高的權重
            weights = []
            for num in available_numbers:
                # 中間號碼權重較高
                mid_point = (min_num + max_num) / 2
                distance_from_mid = abs(num - mid_point)
                weight = max(1, 10 - int(distance_from_mid / 5))
                weights.append(weight)
            
            chosen = random.choices(available_numbers, weights=weights)[0]
            numbers.append(chosen)
            available_numbers.remove(chosen)
        
        return sorted(numbers)
    
    def _generate_special_number(self, config: Dict, main_numbers: List[int]) -> int:
        """生成特別號"""
        if not config['has_special']:
            return None
            
        min_num, max_num = config['special_range']
        
        # 特別號避免與主要號碼重複
        available_numbers = [n for n in range(min_num, max_num + 1) 
                           if n not in main_numbers]
        
        if not available_numbers:
            # 如果沒有可用號碼，直接從範圍中選擇
            available_numbers = list(range(min_num, max_num + 1))
        
        return random.choice(available_numbers)
    
    def generate_historical_data(self, lottery_type: str, periods: int = 10) -> List[Dict]:
        """生成歷史數據
        
        Args:
            lottery_type: 彩票類型
            periods: 生成的期數
            
        Returns:
            List[Dict]: 歷史開獎數據
        """
        if lottery_type not in self.lottery_configs:
            return []
            
        config = self.lottery_configs[lottery_type]
        historical_data = []
        
        base_date = datetime.now()
        
        for i in range(periods):
            # 計算歷史日期
            days_back = (i + 1) * 3  # 每3天一期
            draw_date = base_date - timedelta(days=days_back)
            
            # 生成歷史期號
            year_suffix = str(draw_date.year)[-3:]
            period_num = periods - i  # 遞減期號
            period = f"{year_suffix}{period_num:03d}"
            
            # 生成數據
            main_numbers = self._generate_main_numbers(config)
            
            result = {
                'Period': period,
                'Sdate': draw_date.strftime('%Y-%m-%d'),
                'lottery_type': lottery_type
            }
            
            # 添加號碼
            for j, number in enumerate(main_numbers, 1):
                result[f'Anumber{j}'] = number
            
            # 特別號
            if config['has_special']:
                result['Second_district'] = self._generate_special_number(config, main_numbers)
            
            historical_data.append(result)
        
        return historical_data
    
    def test_generation(self):
        """測試數據生成"""
        print("=== 備用數據生成器測試 ===")
        
        for lottery_type in self.lottery_configs:
            print(f"\n{self.lottery_configs[lottery_type]['name']}:")
            result = self.generate_latest_result(lottery_type)
            if result:
                print(f"  期號: {result['Period']}")
                print(f"  日期: {result['Sdate']}")
                numbers = [result.get(f'Anumber{i}') for i in range(1, 7) if result.get(f'Anumber{i}')]
                print(f"  號碼: {numbers}")
                if result.get('Second_district'):
                    print(f"  特別號: {result['Second_district']}")

if __name__ == "__main__":
    generator = FallbackDataGenerator()
    generator.test_generation()