#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安裝彩票預測系統缺失的依賴模組
"""

import subprocess
import sys
import importlib

def install_package(package_name, import_name=None):
    """安裝包並測試導入"""
    if import_name is None:
        import_name = package_name
    
    print(f"🔧 正在安裝 {package_name}...")
    
    try:
        # 嘗試先導入，看是否已安裝
        importlib.import_module(import_name)
        print(f"✅ {package_name} 已安裝")
        return True
    except ImportError:
        pass
    
    try:
        # 安裝包
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, check=True)
        
        print(f"✅ {package_name} 安裝成功")
        
        # 測試導入
        try:
            importlib.import_module(import_name)
            print(f"✅ {package_name} 導入測試成功")
            return True
        except ImportError as e:
            print(f"⚠️ {package_name} 導入測試失敗: {e}")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安裝失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ {package_name} 安裝過程發生錯誤: {e}")
        return False

def main():
    """主函數"""
    print("🚀 開始安裝彩票預測系統依賴模組")
    print("=" * 50)
    
    # 需要安裝的包
    packages = [
        ("redis", "redis"),
        ("python-dotenv", "dotenv"),
        ("TaiwanLottery", "TaiwanLottery"),
        ("playwright", "playwright"),
        ("beautifulsoup4", "bs4"),
        ("lxml", "lxml"),
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for package_name, import_name in packages:
        if install_package(package_name, import_name):
            success_count += 1
        print()
    
    print("=" * 50)
    print(f"📊 安裝結果: {success_count}/{total_count} 個包成功安裝")
    
    if success_count == total_count:
        print("🎉 所有依賴模組安裝完成！")
        print("💡 請重新啟動系統以使用完整功能")
    else:
        print("⚠️ 部分模組安裝失敗，系統仍可使用基礎功能")
    
    # 特殊處理 - 安裝 Playwright 瀏覽器
    print("\n🌐 安裝 Playwright 瀏覽器...")
    try:
        result = subprocess.run([
            sys.executable, "-m", "playwright", "install", "chromium"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Playwright Chromium 安裝成功")
        else:
            print("⚠️ Playwright Chromium 安裝失敗，但不影響基本功能")
    except Exception as e:
        print(f"⚠️ Playwright 瀏覽器安裝跳過: {e}")

if __name__ == "__main__":
    main()