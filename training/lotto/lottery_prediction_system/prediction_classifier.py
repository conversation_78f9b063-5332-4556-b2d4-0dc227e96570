#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
預測方法分類器
為不同的預測方法提供分類和標記功能，便於未來分析
"""

from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional
import logging

logger = logging.getLogger('prediction_classifier')

class PredictionType(Enum):
    """預測類型枚舉"""
    # 基礎統計方法
    FREQUENCY_ANALYSIS = "frequency_analysis"      # 頻率分析
    TREND_ANALYSIS = "trend_analysis"             # 趨勢分析
    PATTERN_ANALYSIS = "pattern_analysis"         # 模式分析
    
    # 機器學習方法
    MACHINE_LEARNING = "machine_learning"         # 機器學習
    NEURAL_NETWORK = "neural_network"             # 神經網路
    ENSEMBLE_MODEL = "ensemble_model"             # 集成模型
    
    # 傳統彩票分析
    BOARD_PATH_ANALYSIS = "board_path_analysis"   # 板路分析
    HOT_COLD_ANALYSIS = "hot_cold_analysis"       # 冷熱號分析
    SUM_ANALYSIS = "sum_analysis"                 # 和值分析
    
    # 綜合方法
    COMPREHENSIVE = "comprehensive"               # 綜合分析
    HYBRID = "hybrid"                            # 混合方法
    ENHANCED = "enhanced"                        # 增強方法
    
    # 其他
    RANDOM = "random"                            # 隨機生成
    MANUAL = "manual"                            # 手動輸入

class PredictionCategory(Enum):
    """預測大類別"""
    STATISTICAL = "statistical"                  # 統計分析類
    AI_ML = "ai_ml"                             # 人工智能/機器學習類
    TRADITIONAL = "traditional"                 # 傳統彩票分析類
    COMBINED = "combined"                       # 組合方法類
    OTHER = "other"                            # 其他類

@dataclass
class PredictionMethodInfo:
    """預測方法信息"""
    method_type: PredictionType
    category: PredictionCategory
    display_name: str
    description: str
    confidence_range: tuple  # (min, max) 信心度範圍
    complexity: str          # 'simple', 'medium', 'complex'
    requires_network: bool   # 是否需要網路連接
    processing_time: str     # 'fast', 'medium', 'slow'

class PredictionClassifier:
    """預測方法分類器"""
    
    def __init__(self):
        self.method_info = {
            PredictionType.FREQUENCY_ANALYSIS: PredictionMethodInfo(
                method_type=PredictionType.FREQUENCY_ANALYSIS,
                category=PredictionCategory.STATISTICAL,
                display_name="頻率分析",
                description="基於歷史開獎頻率進行預測，選擇中等頻率號碼",
                confidence_range=(0.4, 0.7),
                complexity="simple",
                requires_network=False,
                processing_time="fast"
            ),
            PredictionType.TREND_ANALYSIS: PredictionMethodInfo(
                method_type=PredictionType.TREND_ANALYSIS,
                category=PredictionCategory.STATISTICAL,
                display_name="趨勢分析",
                description="分析號碼近期出現趨勢，預測未來走向",
                confidence_range=(0.45, 0.75),
                complexity="medium",
                requires_network=False,
                processing_time="fast"
            ),
            PredictionType.COMPREHENSIVE: PredictionMethodInfo(
                method_type=PredictionType.COMPREHENSIVE,
                category=PredictionCategory.COMBINED,
                display_name="綜合分析",
                description="結合頻率、趨勢、奇偶等多種因素的綜合預測",
                confidence_range=(0.6, 0.85),
                complexity="complex",
                requires_network=False,
                processing_time="medium"
            ),
            PredictionType.MACHINE_LEARNING: PredictionMethodInfo(
                method_type=PredictionType.MACHINE_LEARNING,
                category=PredictionCategory.AI_ML,
                display_name="機器學習",
                description="使用機器學習算法進行智能預測",
                confidence_range=(0.65, 0.9),
                complexity="complex",
                requires_network=False,
                processing_time="medium"
            ),
            PredictionType.BOARD_PATH_ANALYSIS: PredictionMethodInfo(
                method_type=PredictionType.BOARD_PATH_ANALYSIS,
                category=PredictionCategory.TRADITIONAL,
                display_name="板路分析",
                description="傳統彩票板路分析方法",
                confidence_range=(0.5, 0.8),
                complexity="medium",
                requires_network=False,
                processing_time="fast"
            ),
            PredictionType.ENHANCED: PredictionMethodInfo(
                method_type=PredictionType.ENHANCED,
                category=PredictionCategory.COMBINED,
                display_name="增強分析",
                description="結合多種先進算法的增強預測方法",
                confidence_range=(0.7, 0.95),
                complexity="complex",
                requires_network=False,
                processing_time="medium"
            ),
            PredictionType.HYBRID: PredictionMethodInfo(
                method_type=PredictionType.HYBRID,
                category=PredictionCategory.COMBINED,
                display_name="混合方法",
                description="機器學習與傳統分析的混合方法",
                confidence_range=(0.65, 0.9),
                complexity="complex",
                requires_network=False,
                processing_time="medium"
            )
        }
    
    def classify_prediction_method(self, method_name: str) -> Optional[PredictionMethodInfo]:
        """根據方法名稱分類預測方法"""
        method_mapping = {
            '頻率分析': PredictionType.FREQUENCY_ANALYSIS,
            '趨勢分析': PredictionType.TREND_ANALYSIS,
            '綜合分析': PredictionType.COMPREHENSIVE,
            '機器學習': PredictionType.MACHINE_LEARNING,
            '板路分析': PredictionType.BOARD_PATH_ANALYSIS,
            '增強分析': PredictionType.ENHANCED,
            '混合方法': PredictionType.HYBRID,
            'frequency_analysis': PredictionType.FREQUENCY_ANALYSIS,
            'trend_analysis': PredictionType.TREND_ANALYSIS,
            'comprehensive': PredictionType.COMPREHENSIVE,
            'machine_learning': PredictionType.MACHINE_LEARNING,
            'board_path_analysis': PredictionType.BOARD_PATH_ANALYSIS,
            'enhanced': PredictionType.ENHANCED,
            'hybrid': PredictionType.HYBRID,
        }
        
        method_type = method_mapping.get(method_name)
        if method_type:
            return self.method_info.get(method_type)
        
        # 如果找不到精確匹配，嘗試模糊匹配
        method_name_lower = method_name.lower()
        if any(keyword in method_name_lower for keyword in ['頻率', 'frequency']):
            return self.method_info[PredictionType.FREQUENCY_ANALYSIS]
        elif any(keyword in method_name_lower for keyword in ['趨勢', 'trend']):
            return self.method_info[PredictionType.TREND_ANALYSIS]
        elif any(keyword in method_name_lower for keyword in ['機器', 'learning', 'ml', 'ai']):
            return self.method_info[PredictionType.MACHINE_LEARNING]
        elif any(keyword in method_name_lower for keyword in ['板路', 'board', 'path']):
            return self.method_info[PredictionType.BOARD_PATH_ANALYSIS]
        elif any(keyword in method_name_lower for keyword in ['綜合', 'comprehensive']):
            return self.method_info[PredictionType.COMPREHENSIVE]
        elif any(keyword in method_name_lower for keyword in ['增強', 'enhanced']):
            return self.method_info[PredictionType.ENHANCED]
        elif any(keyword in method_name_lower for keyword in ['混合', 'hybrid']):
            return self.method_info[PredictionType.HYBRID]
        
        return None
    
    def get_prediction_statistics(self, predictions: List[Dict]) -> Dict:
        """獲取預測方法統計信息"""
        stats = {
            'total_predictions': len(predictions),
            'by_category': {},
            'by_method': {},
            'average_confidence': {},
            'success_rate': {}
        }
        
        for prediction in predictions:
            method_name = prediction.get('method', 'unknown')
            method_info = self.classify_prediction_method(method_name)
            
            if method_info:
                category = method_info.category.value
                method_type = method_info.method_type.value
                
                # 按類別統計
                if category not in stats['by_category']:
                    stats['by_category'][category] = 0
                stats['by_category'][category] += 1
                
                # 按方法統計
                if method_type not in stats['by_method']:
                    stats['by_method'][method_type] = 0
                stats['by_method'][method_type] += 1
                
                # 信心度統計
                confidence = prediction.get('confidence', 0)
                if method_type not in stats['average_confidence']:
                    stats['average_confidence'][method_type] = []
                stats['average_confidence'][method_type].append(confidence)
        
        # 計算平均信心度
        for method_type, confidences in stats['average_confidence'].items():
            if confidences:
                stats['average_confidence'][method_type] = sum(confidences) / len(confidences)
            else:
                stats['average_confidence'][method_type] = 0
        
        return stats
    
    def recommend_prediction_method(self, lottery_type: str, 
                                   data_availability: str = "good",
                                   time_constraint: str = "medium") -> List[PredictionType]:
        """根據條件推薦預測方法"""
        recommendations = []
        
        if time_constraint == "fast":
            # 快速預測
            recommendations.extend([
                PredictionType.FREQUENCY_ANALYSIS,
                PredictionType.TREND_ANALYSIS
            ])
        elif time_constraint == "medium":
            # 中等時間
            recommendations.extend([
                PredictionType.COMPREHENSIVE,
                PredictionType.BOARD_PATH_ANALYSIS,
                PredictionType.MACHINE_LEARNING
            ])
        else:  # slow
            # 複雜預測
            recommendations.extend([
                PredictionType.ENHANCED,
                PredictionType.HYBRID,
                PredictionType.MACHINE_LEARNING
            ])
        
        return recommendations
    
    def create_prediction_tag(self, method_info: PredictionMethodInfo, 
                            lottery_type: str, confidence: float) -> Dict:
        """創建預測標籤"""
        return {
            'method_type': method_info.method_type.value,
            'category': method_info.category.value,
            'display_name': method_info.display_name,
            'lottery_type': lottery_type,
            'confidence': confidence,
            'complexity': method_info.complexity,
            'processing_time': method_info.processing_time,
            'classification_timestamp': logging.time.strftime('%Y-%m-%d %H:%M:%S')
        }

# 全局分類器實例
prediction_classifier = PredictionClassifier()

def classify_prediction(method_name: str) -> Optional[PredictionMethodInfo]:
    """分類預測方法的快捷函數"""
    return prediction_classifier.classify_prediction_method(method_name)

def get_method_category(method_name: str) -> str:
    """獲取方法類別的快捷函數"""
    info = classify_prediction(method_name)
    return info.category.value if info else "other"

def get_method_display_name(method_name: str) -> str:
    """獲取方法顯示名稱的快捷函數"""
    info = classify_prediction(method_name)
    return info.display_name if info else method_name

if __name__ == "__main__":
    # 測試分類器
    classifier = PredictionClassifier()
    
    test_methods = ['頻率分析', '機器學習', '綜合分析', '板路分析']
    for method in test_methods:
        info = classifier.classify_prediction_method(method)
        if info:
            print(f"{method}: {info.category.value} - {info.display_name}")
        else:
            print(f"{method}: 未分類")