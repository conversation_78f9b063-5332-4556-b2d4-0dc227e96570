#!/usr/bin/env python3
"""
Phase 3.5 可視化報告生成器
生成圖表、報告和可視化分析結果
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import json
import base64
from io import BytesIO
import logging
from pathlib import Path

# 設置中文字體支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 配置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 導入本地模組
from prediction_tracking_system import (
    PredictionTracker, StatisticsAnalyzer, IntegratedTrackingSystem,
    PredictionRecord, StatisticsReport, AnalysisTimeframe
)

class VisualizationGenerator:
    """可視化生成器"""
    
    def __init__(self, tracking_system: IntegratedTrackingSystem):
        self.tracking_system = tracking_system
        self.tracker = tracking_system.tracker
        self.analyzer = tracking_system.analyzer
        
        # 設置圖表風格
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
    def create_accuracy_trend_chart(self, 
                                   lottery_type: str,
                                   days: int = 30) -> str:
        """創建準確度趨勢圖表"""
        try:
            # 獲取數據
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            records = self.tracker.get_prediction_records(
                lottery_type=lottery_type,
                start_date=start_date,
                end_date=end_date
            )
            
            if not records:
                return self._create_no_data_chart("準確度趨勢", "暫無預測數據")
            
            # 按日分組計算準確度
            daily_data = {}
            for record in records:
                date = record.prediction_timestamp.date()
                if date not in daily_data:
                    daily_data[date] = {'total': 0, 'success': 0}
                
                daily_data[date]['total'] += 1
                if record.match_count and record.match_count >= 2:
                    daily_data[date]['success'] += 1
            
            # 準備數據
            dates = sorted(daily_data.keys())
            accuracies = []
            
            for date in dates:
                data = daily_data[date]
                accuracy = data['success'] / data['total'] if data['total'] > 0 else 0
                accuracies.append(accuracy * 100)  # 轉換為百分比
            
            # 創建圖表
            fig, ax = plt.subplots(figsize=(12, 6))
            
            ax.plot(dates, accuracies, marker='o', linewidth=2, markersize=6)
            ax.fill_between(dates, accuracies, alpha=0.3)
            
            ax.set_title(f'{lottery_type} 準確度趨勢 (最近{days}天)', fontsize=16, fontweight='bold')
            ax.set_xlabel('日期', fontsize=12)
            ax.set_ylabel('準確度 (%)', fontsize=12)
            ax.grid(True, alpha=0.3)
            
            # 設置Y軸範圍
            ax.set_ylim(0, max(100, max(accuracies) * 1.1) if accuracies else 100)
            
            # 旋轉x軸標籤
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            return self._save_chart_as_base64(fig)
            
        except Exception as e:
            logger.error(f"❌ 準確度趨勢圖表創建失敗: {e}")
            return self._create_error_chart("準確度趨勢圖表", str(e))
    
    def create_strategy_performance_chart(self, lottery_type: str) -> str:
        """創建策略性能對比圖表"""
        try:
            # 獲取最近30天的數據
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            records = self.tracker.get_prediction_records(
                lottery_type=lottery_type,
                start_date=start_date,
                end_date=end_date
            )
            
            if not records:
                return self._create_no_data_chart("策略性能對比", "暫無預測數據")
            
            # 統計策略性能
            strategy_stats = {}
            for record in records:
                strategy = record.strategy_used
                if strategy not in strategy_stats:
                    strategy_stats[strategy] = {
                        'total': 0, 
                        'success': 0, 
                        'avg_confidence': 0,
                        'total_confidence': 0
                    }
                
                stats = strategy_stats[strategy]
                stats['total'] += 1
                stats['total_confidence'] += record.confidence
                
                if record.match_count and record.match_count >= 2:
                    stats['success'] += 1
            
            # 計算成功率和平均信心度
            strategies = []
            success_rates = []
            avg_confidences = []
            
            for strategy, stats in strategy_stats.items():
                if stats['total'] >= 3:  # 至少3次預測才顯示
                    strategies.append(strategy)
                    success_rates.append(stats['success'] / stats['total'] * 100)
                    avg_confidences.append(stats['total_confidence'] / stats['total'])
            
            if not strategies:
                return self._create_no_data_chart("策略性能對比", "數據不足以生成圖表")
            
            # 創建雙Y軸圖表
            fig, ax1 = plt.subplots(figsize=(12, 8))
            
            # 成功率柱狀圖
            x_pos = np.arange(len(strategies))
            bars = ax1.bar(x_pos, success_rates, alpha=0.7, label='成功率', color='steelblue')
            ax1.set_xlabel('預測策略', fontsize=12)
            ax1.set_ylabel('成功率 (%)', fontsize=12, color='steelblue')
            ax1.tick_params(axis='y', labelcolor='steelblue')
            ax1.set_xticks(x_pos)
            ax1.set_xticklabels(strategies, rotation=45, ha='right')
            
            # 添加數值標籤
            for bar, rate in zip(bars, success_rates):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                        f'{rate:.1f}%', ha='center', va='bottom')
            
            # 平均信心度折線圖
            ax2 = ax1.twinx()
            line = ax2.plot(x_pos, avg_confidences, 'ro-', linewidth=2, 
                           markersize=8, label='平均信心度', color='red')
            ax2.set_ylabel('平均信心度 (%)', fontsize=12, color='red')
            ax2.tick_params(axis='y', labelcolor='red')
            
            # 添加圖例
            lines1, labels1 = ax1.get_legend_handles_labels()
            lines2, labels2 = ax2.get_legend_handles_labels()
            ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
            
            plt.title(f'{lottery_type} 策略性能對比 (最近30天)', fontsize=16, fontweight='bold')
            plt.tight_layout()
            
            return self._save_chart_as_base64(fig)
            
        except Exception as e:
            logger.error(f"❌ 策略性能圖表創建失敗: {e}")
            return self._create_error_chart("策略性能圖表", str(e))
    
    def create_confidence_accuracy_scatter(self, lottery_type: str) -> str:
        """創建信心度與準確度散點圖"""
        try:
            # 獲取最近60天的已驗證數據
            end_date = datetime.now()
            start_date = end_date - timedelta(days=60)
            
            records = self.tracker.get_prediction_records(
                lottery_type=lottery_type,
                start_date=start_date,
                end_date=end_date
            )
            
            verified_records = [r for r in records if r.status.value == 'verified']
            
            if len(verified_records) < 10:
                return self._create_no_data_chart("信心度與準確度關係", "驗證數據不足")
            
            # 準備數據
            confidences = [r.confidence for r in verified_records]
            match_counts = [r.match_count or 0 for r in verified_records]
            strategies = [r.strategy_used for r in verified_records]
            
            # 創建散點圖
            fig, ax = plt.subplots(figsize=(10, 8))
            
            # 按策略分組著色
            unique_strategies = list(set(strategies))
            colors = plt.cm.Set3(np.linspace(0, 1, len(unique_strategies)))
            
            for i, strategy in enumerate(unique_strategies):
                strategy_confidences = [c for c, s in zip(confidences, strategies) if s == strategy]
                strategy_matches = [m for m, s in zip(match_counts, strategies) if s == strategy]
                
                ax.scatter(strategy_confidences, strategy_matches, 
                          c=[colors[i]], label=strategy, alpha=0.7, s=60)
            
            # 添加趨勢線
            if len(confidences) > 1:
                z = np.polyfit(confidences, match_counts, 1)
                p = np.poly1d(z)
                x_trend = np.linspace(min(confidences), max(confidences), 100)
                ax.plot(x_trend, p(x_trend), "r--", alpha=0.8, linewidth=2)
            
            ax.set_xlabel('預測信心度 (%)', fontsize=12)
            ax.set_ylabel('匹配號碼數', fontsize=12)
            ax.set_title(f'{lottery_type} 信心度與準確度關係 (最近60天)', fontsize=16, fontweight='bold')
            ax.grid(True, alpha=0.3)
            ax.legend()
            
            plt.tight_layout()
            
            return self._save_chart_as_base64(fig)
            
        except Exception as e:
            logger.error(f"❌ 信心度散點圖創建失敗: {e}")
            return self._create_error_chart("信心度散點圖", str(e))
    
    def create_monthly_summary_chart(self, lottery_type: str) -> str:
        """創建月度總結圖表"""
        try:
            # 獲取最近12個月的數據
            end_date = datetime.now()
            start_date = end_date - timedelta(days=365)
            
            records = self.tracker.get_prediction_records(
                lottery_type=lottery_type,
                start_date=start_date,
                end_date=end_date
            )
            
            if not records:
                return self._create_no_data_chart("月度總結", "暫無年度數據")
            
            # 按月分組
            monthly_data = {}
            for record in records:
                month_key = record.prediction_timestamp.strftime('%Y-%m')
                if month_key not in monthly_data:
                    monthly_data[month_key] = {
                        'total': 0, 
                        'verified': 0, 
                        'success': 0,
                        'total_confidence': 0
                    }
                
                data = monthly_data[month_key]
                data['total'] += 1
                data['total_confidence'] += record.confidence
                
                if record.status.value == 'verified':
                    data['verified'] += 1
                    if record.match_count and record.match_count >= 2:
                        data['success'] += 1
            
            # 準備數據
            months = sorted(monthly_data.keys())[-12:]  # 最近12個月
            total_predictions = [monthly_data[m]['total'] for m in months]
            verified_predictions = [monthly_data[m]['verified'] for m in months]
            success_predictions = [monthly_data[m]['success'] for m in months]
            
            # 創建子圖
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
            
            # 上圖：預測數量統計
            x_pos = np.arange(len(months))
            width = 0.35
            
            ax1.bar(x_pos - width/2, total_predictions, width, label='總預測數', alpha=0.8)
            ax1.bar(x_pos + width/2, verified_predictions, width, label='已驗證數', alpha=0.8)
            
            ax1.set_xlabel('月份', fontsize=12)
            ax1.set_ylabel('預測數量', fontsize=12)
            ax1.set_title(f'{lottery_type} 月度預測統計', fontsize=14, fontweight='bold')
            ax1.set_xticks(x_pos)
            ax1.set_xticklabels(months, rotation=45)
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 下圖：成功率趨勢
            success_rates = []
            for m in months:
                data = monthly_data[m]
                if data['verified'] > 0:
                    rate = data['success'] / data['verified'] * 100
                else:
                    rate = 0
                success_rates.append(rate)
            
            ax2.plot(months, success_rates, marker='o', linewidth=2, markersize=6)
            ax2.fill_between(months, success_rates, alpha=0.3)
            ax2.set_xlabel('月份', fontsize=12)
            ax2.set_ylabel('成功率 (%)', fontsize=12)
            ax2.set_title('月度成功率趨勢', fontsize=14, fontweight='bold')
            ax2.grid(True, alpha=0.3)
            plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
            
            plt.tight_layout()
            
            return self._save_chart_as_base64(fig)
            
        except Exception as e:
            logger.error(f"❌ 月度總結圖表創建失敗: {e}")
            return self._create_error_chart("月度總結圖表", str(e))
    
    def create_prize_distribution_chart(self, lottery_type: str) -> str:
        """創建獎項分布圖表"""
        try:
            # 獲取最近90天的已驗證數據
            end_date = datetime.now()
            start_date = end_date - timedelta(days=90)
            
            records = self.tracker.get_prediction_records(
                lottery_type=lottery_type,
                start_date=start_date,
                end_date=end_date
            )
            
            verified_records = [r for r in records if r.status.value == 'verified']
            
            if not verified_records:
                return self._create_no_data_chart("獎項分布", "暫無驗證數據")
            
            # 統計獎項分布
            prize_counts = {}
            for record in verified_records:
                prize_level = record.prize_level or 0
                if prize_level == 0:
                    level_name = "未中獎"
                else:
                    level_name = f"{self._number_to_chinese(prize_level)}獎"
                
                prize_counts[level_name] = prize_counts.get(level_name, 0) + 1
            
            if not prize_counts:
                return self._create_no_data_chart("獎項分布", "暫無獎項數據")
            
            # 創建餅圖
            fig, ax = plt.subplots(figsize=(10, 8))
            
            labels = list(prize_counts.keys())
            sizes = list(prize_counts.values())
            colors = plt.cm.Set3(np.linspace(0, 1, len(labels)))
            
            # 計算百分比
            total = sum(sizes)
            percentages = [s/total*100 for s in sizes]
            
            # 創建餅圖
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, autopct='%1.1f%%',
                                             colors=colors, startangle=90)
            
            # 美化文字
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
            
            ax.set_title(f'{lottery_type} 獎項分布 (最近90天)', fontsize=16, fontweight='bold')
            
            # 添加統計信息
            textstr = f'總驗證預測: {total}次\n中獎預測: {total - prize_counts.get("未中獎", 0)}次'
            props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
            ax.text(0.02, 0.98, textstr, transform=ax.transAxes, fontsize=10,
                   verticalalignment='top', bbox=props)
            
            plt.tight_layout()
            
            return self._save_chart_as_base64(fig)
            
        except Exception as e:
            logger.error(f"❌ 獎項分布圖表創建失敗: {e}")
            return self._create_error_chart("獎項分布圖表", str(e))
    
    def _number_to_chinese(self, num: int) -> str:
        """數字轉中文"""
        chinese_nums = ['', '頭', '貳', '參', '肆', '伍', '陸', '柒', '捌', '玖']
        if 1 <= num <= 9:
            return chinese_nums[num]
        return str(num)
    
    def _save_chart_as_base64(self, fig) -> str:
        """將圖表保存為Base64字符串"""
        try:
            buffer = BytesIO()
            fig.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
            buffer.seek(0)
            
            image_png = buffer.getvalue()
            buffer.close()
            plt.close(fig)
            
            graphic = base64.b64encode(image_png)
            return graphic.decode('utf-8')
            
        except Exception as e:
            logger.error(f"❌ 圖表保存失敗: {e}")
            plt.close(fig)
            return ""
    
    def _create_no_data_chart(self, title: str, message: str) -> str:
        """創建無數據提示圖表"""
        fig, ax = plt.subplots(figsize=(8, 6))
        ax.text(0.5, 0.5, message, ha='center', va='center', 
                fontsize=16, transform=ax.transAxes)
        ax.set_title(title, fontsize=16, fontweight='bold')
        ax.axis('off')
        return self._save_chart_as_base64(fig)
    
    def _create_error_chart(self, title: str, error_message: str) -> str:
        """創建錯誤提示圖表"""
        fig, ax = plt.subplots(figsize=(8, 6))
        ax.text(0.5, 0.5, f'圖表生成失敗:\n{error_message}', 
                ha='center', va='center', fontsize=12, transform=ax.transAxes)
        ax.set_title(title, fontsize=16, fontweight='bold')
        ax.axis('off')
        return self._save_chart_as_base64(fig)

class ReportGenerator:
    """報告生成器"""
    
    def __init__(self, tracking_system: IntegratedTrackingSystem):
        self.tracking_system = tracking_system
        self.visualizer = VisualizationGenerator(tracking_system)
        
    def generate_comprehensive_html_report(self, lottery_type: str) -> str:
        """生成綜合HTML報告"""
        try:
            # 獲取基礎數據
            dashboard_data = self.tracking_system.get_dashboard_data(lottery_type)
            
            # 生成圖表
            accuracy_chart = self.visualizer.create_accuracy_trend_chart(lottery_type)
            strategy_chart = self.visualizer.create_strategy_performance_chart(lottery_type)
            confidence_chart = self.visualizer.create_confidence_accuracy_scatter(lottery_type)
            monthly_chart = self.visualizer.create_monthly_summary_chart(lottery_type)
            prize_chart = self.visualizer.create_prize_distribution_chart(lottery_type)
            
            # 生成統計報告
            weekly_report = self.tracking_system.analyzer.generate_statistics_report(
                lottery_type, AnalysisTimeframe.WEEKLY
            )
            monthly_report = self.tracking_system.analyzer.generate_statistics_report(
                lottery_type, AnalysisTimeframe.MONTHLY
            )
            
            # 生成HTML
            html_content = self._create_html_template(
                lottery_type=lottery_type,
                dashboard_data=dashboard_data,
                charts={
                    'accuracy': accuracy_chart,
                    'strategy': strategy_chart,
                    'confidence': confidence_chart,
                    'monthly': monthly_chart,
                    'prize': prize_chart
                },
                reports={
                    'weekly': weekly_report,
                    'monthly': monthly_report
                }
            )
            
            return html_content
            
        except Exception as e:
            logger.error(f"❌ HTML報告生成失敗: {e}")
            return f"<html><body><h1>報告生成失敗</h1><p>{str(e)}</p></body></html>"
    
    def _create_html_template(self, 
                             lottery_type: str,
                             dashboard_data: Dict[str, Any],
                             charts: Dict[str, str],
                             reports: Dict[str, Any]) -> str:
        """創建HTML模板"""
        
        html = f"""
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{lottery_type} 預測分析報告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
        }}
        
        .header p {{
            margin: 10px 0 0 0;
            opacity: 0.9;
        }}
        
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }}
        
        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }}
        
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }}
        
        .stat-label {{
            color: #666;
            margin-top: 5px;
        }}
        
        .content {{
            padding: 30px;
        }}
        
        .section {{
            margin-bottom: 40px;
        }}
        
        .section h2 {{
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }}
        
        .chart-container {{
            text-align: center;
            margin: 20px 0;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
        }}
        
        .chart-container img {{
            max-width: 100%;
            height: auto;
            border-radius: 4px;
        }}
        
        .recommendations {{
            background: #e7f3ff;
            border-left: 4px solid #2196F3;
            padding: 20px;
            margin: 20px 0;
        }}
        
        .recommendations h3 {{
            color: #1976D2;
            margin-top: 0;
        }}
        
        .recommendations ul {{
            margin: 10px 0;
            padding-left: 20px;
        }}
        
        .recommendations li {{
            margin: 8px 0;
        }}
        
        .footer {{
            background: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }}
        
        @media (max-width: 768px) {{
            .stats-grid {{
                grid-template-columns: repeat(2, 1fr);
            }}
            
            .header h1 {{
                font-size: 2em;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{lottery_type} 預測分析報告</h1>
            <p>生成時間: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{dashboard_data.get('total_predictions', 0)}</div>
                <div class="stat-label">總預測次數</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{dashboard_data.get('verified_predictions', 0)}</div>
                <div class="stat-label">已驗證預測</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{dashboard_data.get('accuracy_rate', 0):.1%}</div>
                <div class="stat-label">準確率</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{dashboard_data.get('average_confidence', 0):.1f}%</div>
                <div class="stat-label">平均信心度</div>
            </div>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📈 準確度趨勢分析</h2>
                <div class="chart-container">
                    <img src="data:image/png;base64,{charts.get('accuracy', '')}" alt="準確度趨勢圖">
                </div>
            </div>
            
            <div class="section">
                <h2>🎯 策略性能對比</h2>
                <div class="chart-container">
                    <img src="data:image/png;base64,{charts.get('strategy', '')}" alt="策略性能圖">
                </div>
            </div>
            
            <div class="section">
                <h2>💡 信心度與準確度關係</h2>
                <div class="chart-container">
                    <img src="data:image/png;base64,{charts.get('confidence', '')}" alt="信心度散點圖">
                </div>
            </div>
            
            <div class="section">
                <h2>📊 月度統計總結</h2>
                <div class="chart-container">
                    <img src="data:image/png;base64,{charts.get('monthly', '')}" alt="月度總結圖">
                </div>
            </div>
            
            <div class="section">
                <h2>🏆 獎項分布分析</h2>
                <div class="chart-container">
                    <img src="data:image/png;base64,{charts.get('prize', '')}" alt="獎項分布圖">
                </div>
            </div>
        """
        
        # 添加建議部分
        if reports.get('monthly') and reports['monthly'].recommendations:
            html += f"""
            <div class="section">
                <div class="recommendations">
                    <h3>💡 系統建議</h3>
                    <ul>
            """
            for recommendation in reports['monthly'].recommendations:
                html += f"<li>{recommendation}</li>"
            
            html += """
                    </ul>
                </div>
            </div>
            """
        
        html += """
        </div>
        
        <div class="footer">
            <p>© 2024 彩票預測系統 - Phase 3.5 預測結果追蹤和統計分析系統</p>
        </div>
    </div>
</body>
</html>
        """
        
        return html
    
    def save_report_to_file(self, lottery_type: str, report_content: str) -> str:
        """保存報告到文件"""
        try:
            # 確保報告目錄存在
            reports_dir = Path("reports")
            reports_dir.mkdir(exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{lottery_type}_report_{timestamp}.html"
            filepath = reports_dir / filename
            
            # 保存文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            logger.info(f"✅ 報告已保存: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"❌ 報告保存失敗: {e}")
            return ""
    
    def export_data_to_csv(self, lottery_type: str) -> str:
        """導出數據到CSV"""
        try:
            # 獲取預測記錄
            records = self.tracking_system.tracker.get_prediction_records(
                lottery_type=lottery_type,
                limit=1000
            )
            
            if not records:
                logger.warning("⚠️ 無數據可導出")
                return ""
            
            # 轉換為DataFrame
            data = []
            for record in records:
                data.append({
                    '預測ID': record.prediction_id,
                    '彩票類型': record.lottery_type,
                    '期號': record.period,
                    '預測號碼': str(record.predicted_main_numbers),
                    '預測特別號': str(record.predicted_special_numbers),
                    '實際號碼': str(record.actual_main_numbers),
                    '實際特別號': str(record.actual_special_numbers),
                    '使用策略': record.strategy_used,
                    '信心度': record.confidence,
                    '預測時間': record.prediction_timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                    '開獎時間': record.draw_timestamp.strftime('%Y-%m-%d %H:%M:%S') if record.draw_timestamp else '',
                    '狀態': record.status.value,
                    '匹配數': record.match_count,
                    '特別號匹配': record.special_match,
                    '獎級': record.prize_level
                })
            
            df = pd.DataFrame(data)
            
            # 確保導出目錄存在
            exports_dir = Path("exports")
            exports_dir.mkdir(exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{lottery_type}_data_{timestamp}.csv"
            filepath = exports_dir / filename
            
            # 保存CSV
            df.to_csv(filepath, index=False, encoding='utf-8-sig')
            
            logger.info(f"✅ 數據已導出: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"❌ 數據導出失敗: {e}")
            return ""

if __name__ == "__main__":
    # 測試示例
    async def test_visualization():
        from data.db_manager import DBManager
        
        db_manager = DBManager()
        tracking_system = IntegratedTrackingSystem(db_manager)
        report_generator = ReportGenerator(tracking_system)
        
        # 生成報告
        html_report = report_generator.generate_comprehensive_html_report("powercolor")
        filepath = report_generator.save_report_to_file("powercolor", html_report)
        
        print(f"✅ 測試報告已生成: {filepath}")
    
    import asyncio
    asyncio.run(test_visualization())