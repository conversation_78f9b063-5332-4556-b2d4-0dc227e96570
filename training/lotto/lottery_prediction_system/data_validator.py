# -*- coding: utf-8 -*-
"""
資料驗證器
防止異常或無效的彩票資料進入系統
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import re

class LotteryDataValidator:
    """彩票資料驗證器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 彩票類型配置
        self.lottery_configs = {
            'powercolor': {
                'name': '威力彩',
                'main_numbers_count': 6,
                'main_number_range': (1, 38),
                'special_number_range': (1, 8),
                'period_pattern': r'^1\d{8}$',  # 1開頭，共9位數字
                'draw_days': [0, 3],  # 週一、週四 (0=週一, 3=週四)
                'min_period': 103000001,  # 最小期號（2014年開始）
            },
            'lotto649': {
                'name': '大樂透',
                'main_numbers_count': 6,
                'main_number_range': (1, 49),
                'special_number_range': (1, 49),
                'period_pattern': r'^1\d{8}$',
                'draw_days': [1, 4],  # 週二、週五 (1=週二, 4=週五)
                'min_period': 103000001,
            },
            'dailycash': {
                'name': '今彩539',
                'main_numbers_count': 5,
                'main_number_range': (1, 39),
                'special_number_range': None,  # 無特別號
                'period_pattern': r'^1\d{8}$',
                'draw_days': [0, 1, 2, 3, 4, 5, 6],  # 每天 (0-6 週一到週日)
                'min_period': 103000001,
            }
        }
    
    def validate_lottery_data(self, lottery_type: str, data: Dict) -> Tuple[bool, List[str]]:
        """
        驗證單筆彩票資料
        
        Args:
            lottery_type: 彩票類型
            data: 資料項目
            
        Returns:
            Tuple[bool, List[str]]: (是否有效, 錯誤訊息列表)
        """
        if lottery_type not in self.lottery_configs:
            return False, [f'不支援的彩票類型: {lottery_type}']
        
        config = self.lottery_configs[lottery_type]
        errors = []
        
        # 1. 檢查必要欄位
        required_fields = ['period', 'draw_date', 'main_numbers']
        for field in required_fields:
            if field not in data or data[field] is None:
                errors.append(f'缺少必要欄位: {field}')
        
        if errors:  # 如果基本欄位都沒有，就不用繼續檢查了
            return False, errors
        
        # 2. 驗證期號
        period = str(data['period'])
        if not re.match(config['period_pattern'], period):
            errors.append(f'期號格式錯誤: {period}（應符合格式 {config["period_pattern"]}）')
        
        try:
            period_int = int(period)
            if period_int < config['min_period']:
                errors.append(f'期號過小: {period}（最小期號: {config["min_period"]}）')
        except ValueError:
            errors.append(f'期號不是有效數字: {period}')
        
        # 3. 驗證日期
        draw_date = data['draw_date']
        if isinstance(draw_date, str):
            try:
                # 嘗試解析日期
                if 'T' in draw_date:
                    date_obj = datetime.fromisoformat(draw_date.replace('T', ' ').replace('Z', ''))
                else:
                    date_obj = datetime.strptime(draw_date, '%Y-%m-%d')
                
                # 檢查日期是否為未來
                now = datetime.now()
                if date_obj > now:
                    errors.append(f'開獎日期不能是未來: {draw_date}')
                
                # 檢查日期是否過於久遠（2014年之前）
                min_date = datetime(2014, 1, 1)
                if date_obj < min_date:
                    errors.append(f'開獎日期過於久遠: {draw_date}（最早日期: 2014-01-01）')
                
                # 檢查是否為合理的開獎日（週幾）
                weekday = date_obj.weekday()  # 0=週一, 6=週日
                if weekday not in config['draw_days']:
                    expected_days = [['週一', '週二', '週三', '週四', '週五', '週六', '週日'][d] for d in config['draw_days']]
                    actual_day = ['週一', '週二', '週三', '週四', '週五', '週六', '週日'][weekday]
                    errors.append(f'開獎日期星期不正確: {actual_day}（{config["name"]}應在 {", ".join(expected_days)} 開獎）')
                    
            except (ValueError, TypeError) as e:
                errors.append(f'日期格式錯誤: {draw_date}')
        else:
            errors.append(f'日期必須是字串格式: {type(draw_date)}')
        
        # 4. 驗證主號碼
        main_numbers = data['main_numbers']
        if not isinstance(main_numbers, list):
            errors.append(f'主號碼必須是列表格式: {type(main_numbers)}')
        else:
            # 檢查數量
            if len(main_numbers) != config['main_numbers_count']:
                errors.append(f'主號碼數量錯誤: {len(main_numbers)}（應為 {config["main_numbers_count"]} 個）')
            
            # 檢查號碼範圍和重複
            min_num, max_num = config['main_number_range']
            for i, num in enumerate(main_numbers):
                try:
                    num_int = int(num)
                    if num_int < min_num or num_int > max_num:
                        errors.append(f'主號碼超出範圍: {num}（範圍: {min_num}-{max_num}）')
                except (ValueError, TypeError):
                    errors.append(f'主號碼不是有效數字: {num}')
            
            # 檢查重複號碼
            if len(set(main_numbers)) != len(main_numbers):
                errors.append(f'主號碼有重複: {main_numbers}')
        
        # 5. 驗證特別號（如果有）
        if config['special_number_range'] is not None:
            special_number = data.get('special_number')
            if special_number is not None:
                try:
                    special_int = int(special_number)
                    min_special, max_special = config['special_number_range']
                    if special_int < min_special or special_int > max_special:
                        errors.append(f'特別號超出範圍: {special_number}（範圍: {min_special}-{max_special}）')
                except (ValueError, TypeError):
                    errors.append(f'特別號不是有效數字: {special_number}')
        
        return len(errors) == 0, errors
    
    def validate_batch_data(self, lottery_type: str, data_list: List[Dict]) -> Dict:
        """
        批次驗證彩票資料
        
        Args:
            lottery_type: 彩票類型
            data_list: 資料列表
            
        Returns:
            Dict: 驗證結果統計
        """
        valid_data = []
        invalid_data = []
        error_summary = {}
        
        for i, data in enumerate(data_list):
            is_valid, errors = self.validate_lottery_data(lottery_type, data)
            
            if is_valid:
                valid_data.append(data)
            else:
                invalid_data.append({
                    'index': i,
                    'data': data,
                    'errors': errors
                })
                
                # 統計錯誤類型
                for error in errors:
                    error_type = error.split(':')[0] if ':' in error else error
                    error_summary[error_type] = error_summary.get(error_type, 0) + 1
        
        return {
            'total_count': len(data_list),
            'valid_count': len(valid_data),
            'invalid_count': len(invalid_data),
            'valid_data': valid_data,
            'invalid_data': invalid_data,
            'error_summary': error_summary,
            'validation_rate': f"{(len(valid_data) / len(data_list) * 100):.1f}%" if data_list else "0%"
        }
    
    def suggest_corrections(self, lottery_type: str, data: Dict, errors: List[str]) -> Dict:
        """
        建議資料修正方案
        
        Args:
            lottery_type: 彩票類型
            data: 原始資料
            errors: 錯誤列表
            
        Returns:
            Dict: 修正建議
        """
        suggestions = []
        
        for error in errors:
            if '期號格式錯誤' in error:
                period = str(data.get('period', ''))
                if period.isdigit() and len(period) != 9:
                    if len(period) < 9:
                        suggested_period = period.ljust(9, '0')
                    else:
                        suggested_period = period[:9]
                    suggestions.append(f'建議期號格式: {suggested_period}')
            
            elif '開獎日期不能是未來' in error:
                suggestions.append('請確認開獎日期是否正確，或等待實際開獎後再更新')
            
            elif '主號碼數量錯誤' in error:
                config = self.lottery_configs[lottery_type]
                suggestions.append(f'請確保主號碼有 {config["main_numbers_count"]} 個')
            
            elif '號碼超出範圍' in error:
                config = self.lottery_configs[lottery_type]
                min_num, max_num = config['main_number_range']
                suggestions.append(f'請確保號碼在 {min_num}-{max_num} 範圍內')
        
        return {
            'original_data': data,
            'errors': errors,
            'suggestions': suggestions,
            'action': 'manual_review' if suggestions else 'discard'
        }

# 測試函數
def test_validator():
    """測試資料驗證器"""
    print("🧪 測試資料驗證器")
    print()
    
    validator = LotteryDataValidator()
    
    # 測試有效資料
    valid_data = {
        'period': '113000105',
        'draw_date': '2024-12-30',
        'main_numbers': [6, 8, 12, 16, 23, 31],
        'special_number': 1
    }
    
    print("1️⃣ 測試有效資料...")
    is_valid, errors = validator.validate_lottery_data('powercolor', valid_data)
    print(f"   結果: {'✅ 有效' if is_valid else '❌ 無效'}")
    if errors:
        for error in errors:
            print(f"   錯誤: {error}")
    
    # 測試異常資料
    invalid_data = {
        'period': '114000999',  # 期號可能有問題
        'draw_date': '2025-12-25',  # 未來日期
        'main_numbers': [1, 5, 12, 18, 25, 32],
        'special_number': 7
    }
    
    print()
    print("2️⃣ 測試異常資料...")
    is_valid, errors = validator.validate_lottery_data('powercolor', invalid_data)
    print(f"   結果: {'✅ 有效' if is_valid else '❌ 無效'}")
    if errors:
        for error in errors:
            print(f"   錯誤: {error}")
    
    # 測試修正建議
    print()
    print("3️⃣ 測試修正建議...")
    suggestions = validator.suggest_corrections('powercolor', invalid_data, errors)
    print(f"   建議動作: {suggestions['action']}")
    for suggestion in suggestions['suggestions']:
        print(f"   建議: {suggestion}")

if __name__ == "__main__":
    test_validator()