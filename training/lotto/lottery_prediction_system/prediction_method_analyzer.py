#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
預測方法效果分析器
分析不同預測方法的準確性、信心度分布和效果對比
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import sqlite3
import json
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict

# 設置中文字體
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger('prediction_method_analyzer')

class PredictionMethodAnalyzer:
    """預測方法效果分析器"""
    
    def __init__(self, db_manager=None):
        self.db_manager = db_manager
        
        # 導入分類器
        try:
            from prediction_classifier import prediction_classifier, PredictionCategory
            self.classifier = prediction_classifier
            self.PredictionCategory = PredictionCategory
            self.classifier_available = True
        except ImportError:
            logger.warning("預測分類器不可用")
            self.classifier_available = False
    
    def load_prediction_history(self, lottery_type: str = 'powercolor', 
                               days_back: int = 30) -> pd.DataFrame:
        """載入預測歷史數據"""
        try:
            table_map = {
                'powercolor': 'PowercolorPredictions',
                'lotto649': 'Lotto649Predictions', 
                'dailycash': 'DailyCashPredictions'
            }
            
            table_name = table_map.get(lottery_type)
            if not table_name:
                raise ValueError(f"不支援的彩票類型: {lottery_type}")
            
            conn = self.db_manager.create_connection()
            
            # 載入預測數據
            cutoff_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
            
            sql = f"""
            SELECT * FROM {table_name}
            WHERE PredictionDate >= ?
            ORDER BY PredictionDate DESC
            """
            
            df = pd.read_sql_query(sql, conn, params=(cutoff_date,))
            conn.close()
            
            return df
            
        except Exception as e:
            logger.error(f"載入預測歷史失敗: {str(e)}")
            return pd.DataFrame()
    
    def calculate_accuracy_metrics(self, predictions_df: pd.DataFrame) -> Dict:
        """計算預測準確度指標"""
        if predictions_df.empty:
            return {}
        
        metrics = {}
        
        # 按預測方法分組
        if 'PredictionMethod' in predictions_df.columns:
            method_groups = predictions_df.groupby('PredictionMethod')
            
            for method, group in method_groups:
                # 處理NaN值的安全計算
                confidence_std = group['Confidence'].std() if 'Confidence' in group.columns else 0
                if pd.isna(confidence_std):
                    confidence_std = 0.0
                    
                method_metrics = {
                    'total_predictions': len(group),
                    'avg_confidence': group['Confidence'].mean() if 'Confidence' in group.columns else 0,
                    'confidence_std': confidence_std,
                    'confidence_range': [
                        group['Confidence'].min() if 'Confidence' in group.columns else 0,
                        group['Confidence'].max() if 'Confidence' in group.columns else 0
                    ]
                }
                
                # 計算命中率（如果有實際結果）
                if 'MatchCount' in group.columns:
                    match_counts = group['MatchCount'].dropna()
                    if not match_counts.empty:
                        method_metrics.update({
                            'avg_match_count': match_counts.mean(),
                            'max_match_count': match_counts.max(),
                            'hit_rate_3plus': (match_counts >= 3).mean(),
                            'hit_rate_4plus': (match_counts >= 4).mean(),
                            'hit_rate_5plus': (match_counts >= 5).mean(),
                        })
                
                metrics[method] = method_metrics
        
        return metrics
    
    def analyze_method_categories(self, predictions_df: pd.DataFrame) -> Dict:
        """分析預測方法類別分布"""
        if not self.classifier_available or predictions_df.empty:
            return {}
        
        category_stats = defaultdict(list)
        method_details = []
        
        for _, row in predictions_df.iterrows():
            method = row.get('PredictionMethod', 'unknown')
            confidence = row.get('Confidence', 0)
            
            # 嘗試從詳細信息中獲取分類
            method_classification = None
            if 'PredictionDetails' in row and pd.notna(row['PredictionDetails']):
                try:
                    details = json.loads(row['PredictionDetails'])
                    method_classification = details.get('method_classification')
                except:
                    pass
            
            # 如果沒有預存分類，進行實時分類
            if not method_classification:
                method_info = self.classifier.classify_prediction_method(method)
                if method_info:
                    method_classification = {
                        'category': method_info.category.value,
                        'method_type': method_info.method_type.value,
                        'display_name': method_info.display_name,
                        'complexity': method_info.complexity
                    }
            
            if method_classification:
                category = method_classification['category']
                category_stats[category].append({
                    'method': method,
                    'confidence': confidence,
                    'classification': method_classification
                })
                
                method_details.append({
                    'period': row.get('Period'),
                    'date': row.get('PredictionDate'),
                    'method': method,
                    'category': category,
                    'confidence': confidence,
                    'details': method_classification
                })
        
        # 統計各類別的表現
        category_summary = {}
        for category, predictions in category_stats.items():
            confidences = [p['confidence'] for p in predictions]
            category_summary[category] = {
                'count': len(predictions),
                'avg_confidence': np.mean(confidences) if confidences else 0,
                'std_confidence': np.std(confidences) if confidences else 0,
                'methods': list(set(p['method'] for p in predictions))
            }
        
        return {
            'category_summary': category_summary,
            'method_details': method_details,
            'total_predictions': len(method_details)
        }
    
    def generate_performance_report(self, lottery_type: str = 'powercolor', 
                                  days_back: int = 30) -> Dict:
        """生成預測方法效果報告"""
        # 載入數據
        predictions_df = self.load_prediction_history(lottery_type, days_back)
        
        if predictions_df.empty:
            return {
                'status': 'no_data',
                'message': f'沒有找到最近{days_back}天的預測數據'
            }
        
        # 計算各種指標
        accuracy_metrics = self.calculate_accuracy_metrics(predictions_df)
        category_analysis = self.analyze_method_categories(predictions_df)
        
        # 時間趨勢分析
        time_trends = self._analyze_time_trends(predictions_df)
        
        # 信心度分析
        confidence_analysis = self._analyze_confidence_distribution(predictions_df)
        
        report = {
            'status': 'success',
            'lottery_type': lottery_type,
            'analysis_period': f'{days_back}天',
            'total_predictions': len(predictions_df),
            'accuracy_metrics': accuracy_metrics,
            'category_analysis': category_analysis,
            'time_trends': time_trends,
            'confidence_analysis': confidence_analysis,
            'generated_at': datetime.now().isoformat()
        }
        
        return report
    
    def _analyze_time_trends(self, predictions_df: pd.DataFrame) -> Dict:
        """分析時間趨勢"""
        if 'PredictionDate' not in predictions_df.columns:
            return {}
        
        try:
            # 轉換日期
            predictions_df['Date'] = pd.to_datetime(predictions_df['PredictionDate'])
            
            # 按日期分組
            daily_stats = predictions_df.groupby(predictions_df['Date'].dt.date).agg({
                'Confidence': ['mean', 'count'],
                'PredictionMethod': lambda x: x.value_counts().to_dict()
            }).reset_index()
            
            return {
                'daily_prediction_count': daily_stats['Confidence']['count'].tolist(),
                'daily_avg_confidence': daily_stats['Confidence']['mean'].tolist(),
                'dates': [str(date) for date in daily_stats['Date']]
            }
            
        except Exception as e:
            logger.error(f"時間趨勢分析失敗: {str(e)}")
            return {}
    
    def _analyze_confidence_distribution(self, predictions_df: pd.DataFrame) -> Dict:
        """分析信心度分布"""
        if 'Confidence' not in predictions_df.columns:
            return {}
        
        confidences = predictions_df['Confidence'].dropna()
        
        if confidences.empty:
            return {}
        
        # 安全轉換，處理NaN值
        def safe_float(value):
            return 0.0 if pd.isna(value) else float(value)
            
        return {
            'mean': safe_float(confidences.mean()),
            'median': safe_float(confidences.median()),
            'std': safe_float(confidences.std()),
            'min': safe_float(confidences.min()),
            'max': safe_float(confidences.max()),
            'quartiles': [
                safe_float(confidences.quantile(0.25)),
                safe_float(confidences.quantile(0.5)),
                safe_float(confidences.quantile(0.75))
            ],
            'distribution_by_method': self._safe_describe_by_method(predictions_df) if 'PredictionMethod' in predictions_df.columns else {}
        }
    
    def _safe_describe_by_method(self, predictions_df: pd.DataFrame) -> Dict:
        """安全的方法描述統計，處理NaN值"""
        result = {}
        
        def safe_float(value):
            return 0.0 if pd.isna(value) else float(value)
        
        for method, group in predictions_df.groupby('PredictionMethod'):
            if 'Confidence' in group.columns:
                confidences = group['Confidence']
                result[method] = {
                    'count': safe_float(len(confidences)),
                    'mean': safe_float(confidences.mean()),
                    'std': safe_float(confidences.std()),
                    'min': safe_float(confidences.min()),
                    '25%': safe_float(confidences.quantile(0.25)),
                    '50%': safe_float(confidences.quantile(0.5)),
                    '75%': safe_float(confidences.quantile(0.75)),
                    'max': safe_float(confidences.max())
                }
        
        return result
    
    def create_visualization(self, report: Dict, save_path: str = None) -> str:
        """創建視覺化圖表"""
        if report.get('status') != 'success':
            return "無法創建圖表：數據不足"
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'{report["lottery_type"]} 預測方法分析報告', fontsize=16)
        
        # 1. 預測方法分布
        if report['category_analysis'].get('category_summary'):
            category_data = report['category_analysis']['category_summary']
            categories = list(category_data.keys())
            counts = [data['count'] for data in category_data.values()]
            
            axes[0, 0].pie(counts, labels=categories, autopct='%1.1f%%')
            axes[0, 0].set_title('預測方法類別分布')
        
        # 2. 信心度分布
        if report['confidence_analysis']:
            conf_data = report['confidence_analysis']
            if conf_data.get('distribution_by_method'):
                methods = list(conf_data['distribution_by_method'].keys())
                means = [conf_data['distribution_by_method'][method]['mean'] 
                        for method in methods]
                
                axes[0, 1].bar(range(len(methods)), means)
                axes[0, 1].set_xticks(range(len(methods)))
                axes[0, 1].set_xticklabels(methods, rotation=45)
                axes[0, 1].set_title('各方法平均信心度')
                axes[0, 1].set_ylabel('信心度')
        
        # 3. 時間趨勢
        if report['time_trends'].get('dates'):
            dates = report['time_trends']['dates']
            counts = report['time_trends']['daily_prediction_count']
            
            axes[1, 0].plot(range(len(dates)), counts, marker='o')
            axes[1, 0].set_title('每日預測數量趨勢')
            axes[1, 0].set_ylabel('預測數量')
            axes[1, 0].set_xlabel('日期')
        
        # 4. 準確度對比
        if report['accuracy_metrics']:
            methods = list(report['accuracy_metrics'].keys())
            avg_confidences = [report['accuracy_metrics'][method]['avg_confidence'] 
                             for method in methods]
            
            axes[1, 1].bar(range(len(methods)), avg_confidences)
            axes[1, 1].set_xticks(range(len(methods)))
            axes[1, 1].set_xticklabels(methods, rotation=45)
            axes[1, 1].set_title('各方法效果對比')
            axes[1, 1].set_ylabel('平均信心度')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            return f"圖表已保存至: {save_path}"
        else:
            plt.show()
            return "圖表已顯示"
    
    def export_analysis_report(self, report: Dict, export_path: str) -> bool:
        """導出分析報告"""
        try:
            # 導出為JSON格式
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"分析報告已導出至: {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"導出分析報告失敗: {str(e)}")
            return False

if __name__ == "__main__":
    # 測試分析器
    try:
        from data.db_manager import DBManager
        db_manager = DBManager()
        analyzer = PredictionMethodAnalyzer(db_manager)
        
        # 生成報告
        report = analyzer.generate_performance_report('powercolor', 7)
        print("預測方法分析報告:")
        print(json.dumps(report, ensure_ascii=False, indent=2))
        
    except Exception as e:
        print(f"測試失敗: {e}")