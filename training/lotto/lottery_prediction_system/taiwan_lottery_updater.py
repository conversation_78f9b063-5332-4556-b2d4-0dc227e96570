# -*- coding: utf-8 -*-
"""
Taiwan Lottery 更新器
使用 TaiwanLotteryCrawler 替代原有的 Web 爬蟲系統
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import json
from taiwan_lottery_crawler_adapter import TaiwanLotteryCrawlerAdapter
from data.db_manager import DBManager
from data_validator import LotteryDataValidator

class TaiwanLotteryUpdater:
    """基於 TaiwanLotteryCrawler 的彩票資料更新器"""
    
    def __init__(self, db_manager: DBManager = None):
        self.adapter = TaiwanLotteryCrawlerAdapter()
        self.db_manager = db_manager or DBManager()
        self.validator = LotteryDataValidator()
        self.logger = logging.getLogger(__name__)
        
        # 支援的彩票類型
        self.supported_types = ['powercolor', 'lotto649', 'dailycash']
    
    def update_lottery_data(self, lottery_type: str, months: int = 2) -> Dict:
        """
        更新指定彩票類型的資料
        
        Args:
            lottery_type: 彩票類型
            months: 要更新的月份數量
            
        Returns:
            Dict: 更新結果
        """
        if lottery_type not in self.supported_types:
            return {
                'success': False,
                'error': f'不支援的彩票類型: {lottery_type}',
                'data': None
            }
        
        try:
            self.logger.info(f"開始更新 {lottery_type} 資料，範圍：最近 {months} 個月")
            
            # 獲取資料
            all_data = self.adapter.get_recent_data(lottery_type, months)
            
            if not all_data:
                return {
                    'success': False,
                    'error': f'未獲取到 {lottery_type} 的任何資料',
                    'data': None
                }
            
            # 儲存到資料庫
            saved_count = 0
            updated_count = 0
            error_count = 0
            validation_failed_count = 0
            errors = []
            
            for data_item in all_data:
                try:
                    # 先進行資料驗證
                    is_valid, validation_errors = self.validator.validate_lottery_data(lottery_type, data_item)
                    
                    if not is_valid:
                        validation_failed_count += 1
                        error_msg = f"期號 {data_item.get('period', '未知')} 資料驗證失敗: {'; '.join(validation_errors[:3])}"
                        errors.append(error_msg)
                        self.logger.warning(error_msg)
                        continue
                    
                    # 驗證通過，儲存到資料庫
                    result = self._save_to_database(data_item)
                    if result['success']:
                        if result['action'] == 'insert':
                            saved_count += 1
                        elif result['action'] == 'update':
                            updated_count += 1
                    else:
                        error_count += 1
                        errors.append(f"期號 {data_item['period']}: {result['error']}")
                        
                except Exception as e:
                    error_count += 1
                    errors.append(f"期號 {data_item.get('period', '未知')}: {str(e)}")
            
            # 統計結果
            total_processed = len(all_data)
            success_count = saved_count + updated_count
            
            result = {
                'success': success_count > 0,
                'data': {
                    'lottery_type': lottery_type,
                    'total_processed': total_processed,
                    'new_records': saved_count,
                    'updated_records': updated_count,
                    'error_count': error_count,
                    'validation_failed_count': validation_failed_count,
                    'errors': errors[:10],  # 只保留前10個錯誤
                    'success_rate': f"{(success_count / total_processed * 100):.1f}%" if total_processed > 0 else "0%"
                }
            }
            
            if success_count > 0:
                self.logger.info(f"{lottery_type} 更新完成: 新增 {saved_count} 筆，更新 {updated_count} 筆，驗證失敗 {validation_failed_count} 筆，錯誤 {error_count} 筆")
            else:
                result['error'] = f'所有資料都處理失敗: {errors[:3]}'
                
            return result
            
        except Exception as e:
            self.logger.error(f"更新 {lottery_type} 時發生異常: {e}")
            return {
                'success': False,
                'error': f'更新過程中發生異常: {str(e)}',
                'data': None
            }
    
    def update_all_lotteries(self, months: int = 2) -> Dict:
        """
        更新所有支援的彩票類型
        
        Args:
            months: 要更新的月份數量
            
        Returns:
            Dict: 總體更新結果
        """
        self.logger.info(f"開始批量更新所有彩票類型，範圍：最近 {months} 個月")
        
        results = {}
        total_new = 0
        total_updated = 0
        total_errors = 0
        all_errors = []
        
        for lottery_type in self.supported_types:
            try:
                result = self.update_lottery_data(lottery_type, months)
                results[lottery_type] = result
                
                if result['success'] and result['data']:
                    data = result['data']
                    total_new += data['new_records']
                    total_updated += data['updated_records']
                    total_errors += data['error_count']
                    all_errors.extend(data['errors'])
                    
            except Exception as e:
                self.logger.error(f"批量更新中 {lottery_type} 發生異常: {e}")
                results[lottery_type] = {
                    'success': False,
                    'error': f'更新異常: {str(e)}',
                    'data': None
                }
                total_errors += 1
                all_errors.append(f"{lottery_type}: {str(e)}")
        
        # 計算總體結果
        successful_types = sum(1 for r in results.values() if r['success'])
        
        return {
            'success': successful_types > 0,
            'data': {
                'results_by_type': results,
                'summary': {
                    'successful_types': successful_types,
                    'total_types': len(self.supported_types),
                    'total_new_records': total_new,
                    'total_updated_records': total_updated,
                    'total_errors': total_errors,
                    'all_errors': all_errors[:20]  # 只保留前20個錯誤
                }
            }
        }
    
    def _save_to_database(self, data_item: Dict) -> Dict:
        """
        將資料項目儲存到資料庫（使用現有表格結構）
        
        Args:
            data_item: 資料項目
            
        Returns:
            Dict: 儲存結果
        """
        try:
            lottery_type = data_item['lottery_type']
            period = str(data_item['period'])
            draw_date = data_item['draw_date']
            main_numbers = data_item['main_numbers']
            special_number = data_item['special_number']
            
            # 建立資料庫連接
            conn = self.db_manager.create_connection()
            if not conn:
                return {'success': False, 'error': '無法連接資料庫', 'action': None}
            
            cursor = conn.cursor()
            
            try:
                # 根據彩票類型選擇對應的表格
                if lottery_type == 'powercolor':
                    table_name = 'Powercolor'
                    # 檢查記錄是否已存在
                    check_query = f"SELECT COUNT(*) FROM {table_name} WHERE Period = ?"
                    cursor.execute(check_query, (period,))
                    exists = cursor.fetchone()[0] > 0
                    
                    if exists:
                        # 更新現有記錄
                        update_query = f"""
                        UPDATE {table_name} 
                        SET Sdate = ?, Anumber1 = ?, Anumber2 = ?, Anumber3 = ?, 
                            Anumber4 = ?, Anumber5 = ?, Anumber6 = ?, Second_district = ?
                        WHERE Period = ?
                        """
                        cursor.execute(update_query, (
                            draw_date, main_numbers[0], main_numbers[1], main_numbers[2],
                            main_numbers[3], main_numbers[4], main_numbers[5], 
                            special_number, period
                        ))
                        action = 'update'
                    else:
                        # 插入新記錄
                        insert_query = f"""
                        INSERT INTO {table_name} 
                        (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3, 
                         Anumber4, Anumber5, Anumber6, Second_district)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """
                        cursor.execute(insert_query, (
                            '威力彩', period, draw_date, main_numbers[0], main_numbers[1], 
                            main_numbers[2], main_numbers[3], main_numbers[4], 
                            main_numbers[5], special_number
                        ))
                        action = 'insert'
                        
                elif lottery_type == 'lotto649':
                    table_name = 'Lotto649'
                    # 檢查記錄是否已存在
                    check_query = f"SELECT COUNT(*) FROM {table_name} WHERE Period = ?"
                    cursor.execute(check_query, (period,))
                    exists = cursor.fetchone()[0] > 0
                    
                    if exists:
                        # 更新現有記錄
                        update_query = f"""
                        UPDATE {table_name} 
                        SET Sdate = ?, Anumber1 = ?, Anumber2 = ?, Anumber3 = ?, 
                            Anumber4 = ?, Anumber5 = ?, Anumber6 = ?, SpecialNumber = ?
                        WHERE Period = ?
                        """
                        cursor.execute(update_query, (
                            draw_date, main_numbers[0], main_numbers[1], main_numbers[2],
                            main_numbers[3], main_numbers[4], main_numbers[5], 
                            special_number, period
                        ))
                        action = 'update'
                    else:
                        # 插入新記錄
                        insert_query = f"""
                        INSERT INTO {table_name} 
                        (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3, 
                         Anumber4, Anumber5, Anumber6, SpecialNumber)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """
                        cursor.execute(insert_query, (
                            '大樂透', period, draw_date, main_numbers[0], main_numbers[1], 
                            main_numbers[2], main_numbers[3], main_numbers[4], 
                            main_numbers[5], special_number
                        ))
                        action = 'insert'
                        
                elif lottery_type == 'dailycash':
                    table_name = 'DailyCash'
                    # 檢查記錄是否已存在
                    check_query = f"SELECT COUNT(*) FROM {table_name} WHERE Period = ?"
                    cursor.execute(check_query, (period,))
                    exists = cursor.fetchone()[0] > 0
                    
                    if exists:
                        # 更新現有記錄
                        update_query = f"""
                        UPDATE {table_name} 
                        SET Sdate = ?, Anumber1 = ?, Anumber2 = ?, Anumber3 = ?, Anumber4 = ?, Anumber5 = ?
                        WHERE Period = ?
                        """
                        cursor.execute(update_query, (
                            draw_date, main_numbers[0], main_numbers[1], main_numbers[2],
                            main_numbers[3], main_numbers[4], period
                        ))
                        action = 'update'
                    else:
                        # 插入新記錄
                        insert_query = f"""
                        INSERT INTO {table_name} 
                        (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        """
                        cursor.execute(insert_query, (
                            '今彩539', period, draw_date, main_numbers[0], main_numbers[1], 
                            main_numbers[2], main_numbers[3], main_numbers[4]
                        ))
                        action = 'insert'
                else:
                    return {'success': False, 'error': f'不支援的彩票類型: {lottery_type}', 'action': None}
                    
                conn.commit()
                return {'success': True, 'action': action, 'error': None}
                    
            except Exception as e:
                conn.rollback()
                return {'success': False, 'error': f'資料庫操作失敗: {str(e)}', 'action': None}
                
            finally:
                cursor.close()
                conn.close()
                
        except Exception as e:
            return {'success': False, 'error': f'儲存過程異常: {str(e)}', 'action': None}
    
    def get_latest_periods(self) -> Dict[str, Optional[str]]:
        """
        獲取各彩票類型的最新期號
        
        Returns:
            Dict[str, Optional[str]]: 各彩票類型的最新期號
        """
        periods = {}
        
        for lottery_type in self.supported_types:
            try:
                period = self.adapter.get_latest_period(lottery_type)
                periods[lottery_type] = period
                
                if period:
                    self.logger.info(f"{lottery_type} 最新期號: {period}")
                else:
                    self.logger.warning(f"{lottery_type} 無法獲取最新期號")
                    
            except Exception as e:
                self.logger.error(f"獲取 {lottery_type} 最新期號時發生錯誤: {e}")
                periods[lottery_type] = None
        
        return periods
    
    def validate_existing_data(self, lottery_type: str, limit: int = 100) -> Dict:
        """
        驗證資料庫中現有的資料
        
        Args:
            lottery_type: 彩票類型
            limit: 檢查的記錄數量限制
            
        Returns:
            Dict: 驗證結果
        """
        try:
            conn = self.db_manager.create_connection()
            if not conn:
                return {'success': False, 'error': '無法連接資料庫', 'data': None}
            
            cursor = conn.cursor()
            
            # 根據彩票類型選擇表格和查詢
            table_mapping = {
                'powercolor': ('Powercolor', 'Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, Second_district'),
                'lotto649': ('Lotto649', 'Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, SpecialNumber'),
                'dailycash': ('DailyCash', 'Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5')
            }
            
            if lottery_type not in table_mapping:
                return {'success': False, 'error': f'不支援的彩票類型: {lottery_type}', 'data': None}
            
            table_name, columns = table_mapping[lottery_type]
            
            # 獲取最近的記錄進行驗證
            query = f"SELECT {columns} FROM {table_name} ORDER BY Period DESC LIMIT ?"
            cursor.execute(query, (limit,))
            records = cursor.fetchall()
            
            if not records:
                return {'success': True, 'data': {'message': f'表格 {table_name} 中沒有資料', 'valid_count': 0, 'invalid_count': 0}}
            
            # 轉換資料格式進行驗證
            validation_data = []
            column_list = columns.split(', ')
            
            for record in records:
                data_item = {}
                
                # 基本欄位轉換
                data_item['period'] = record[0]
                data_item['draw_date'] = record[1]
                
                # 主號碼
                if lottery_type == 'dailycash':
                    data_item['main_numbers'] = [record[2], record[3], record[4], record[5], record[6]]
                    data_item['special_number'] = None
                else:
                    data_item['main_numbers'] = [record[2], record[3], record[4], record[5], record[6], record[7]]
                    data_item['special_number'] = record[8]
                
                validation_data.append(data_item)
            
            # 批次驗證
            validation_result = self.validator.validate_batch_data(lottery_type, validation_data)
            
            cursor.close()
            conn.close()
            
            return {
                'success': True,
                'data': {
                    'table_name': table_name,
                    'records_checked': len(records),
                    'validation_result': validation_result,
                    'invalid_records': validation_result['invalid_data'][:10] if validation_result['invalid_data'] else []
                }
            }
            
        except Exception as e:
            self.logger.error(f"驗證現有資料時發生異常: {e}")
            return {
                'success': False,
                'error': f'驗證過程異常: {str(e)}',
                'data': None
            }

    def test_connection(self) -> Dict:
        """
        測試與資料來源的連接
        
        Returns:
            Dict: 連接測試結果
        """
        self.logger.info("開始測試 TaiwanLotteryCrawler 連接")
        
        try:
            # 測試適配器連接
            connection_results = {}
            
            for lottery_type in self.supported_types:
                try:
                    # 嘗試獲取最近的資料
                    test_data = self.adapter.get_month_data(lottery_type, 2024, 12)
                    connection_results[lottery_type] = {
                        'connected': len(test_data) > 0 if test_data else False,
                        'latest_period': test_data[0]['period'] if test_data else None,
                        'data_count': len(test_data) if test_data else 0,
                        'error': None
                    }
                    
                except Exception as e:
                    connection_results[lottery_type] = {
                        'connected': False,
                        'latest_period': None,
                        'data_count': 0,
                        'error': str(e)
                    }
            
            # 計算總體狀態
            successful_connections = sum(1 for r in connection_results.values() if r['connected'])
            
            return {
                'success': successful_connections > 0,
                'data': {
                    'total_types': len(self.supported_types),
                    'successful_connections': successful_connections,
                    'connection_results': connection_results,
                    'overall_status': 'healthy' if successful_connections == len(self.supported_types) else 'partial'
                }
            }
            
        except Exception as e:
            self.logger.error(f"連接測試發生異常: {e}")
            return {
                'success': False,
                'error': f'連接測試異常: {str(e)}',
                'data': None
            }

# 測試函數
def test_taiwan_lottery_updater():
    """測試 TaiwanLotteryUpdater"""
    logging.basicConfig(level=logging.INFO)
    
    updater = TaiwanLotteryUpdater()
    
    print("🎯 測試 TaiwanLotteryUpdater")
    print()
    
    # 測試連接
    print("1️⃣ 測試連接...")
    connection_result = updater.test_connection()
    if connection_result['success']:
        print("   ✅ 連接測試通過")
        data = connection_result['data']
        print(f"   📊 成功連接: {data['successful_connections']}/{data['total_types']} 個類型")
    else:
        print(f"   ❌ 連接測試失敗: {connection_result.get('error', '未知錯誤')}")
    
    print()
    
    # 測試單一更新 (由於當前時間2025年9月資料不存在，直接使用2024年12月的測試)
    print("2️⃣ 測試威力彩資料處理...")
    try:
        # 直接使用適配器獲取已知存在的2024年12月資料進行測試
        test_data = updater.adapter.get_month_data('powercolor', 2024, 12)
        if test_data:
            # 測試儲存功能
            first_item = test_data[0]
            save_result = updater._save_to_database(first_item)
            
            if save_result['success']:
                print(f"   ✅ 資料儲存測試成功 (動作: {save_result['action']})")
                print(f"   📊 測試資料: 期號 {first_item['period']}, 日期 {first_item['draw_date']}")
            else:
                print(f"   ❌ 資料儲存測試失敗: {save_result['error']}")
        else:
            print("   ⚠️  無法獲取測試資料")
        
        # 由於時間問題，這個測試暫時不執行完整的更新流程
            
    except Exception as e:
        print(f"   ❌ 測試異常: {e}")

if __name__ == "__main__":
    test_taiwan_lottery_updater()