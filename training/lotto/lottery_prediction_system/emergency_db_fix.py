#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
緊急資料庫修復工具
強制修正威力彩資料問題
"""

import sqlite3
import shutil
import os
import time
from datetime import datetime

def emergency_fix():
    """緊急修復資料庫"""
    
    # 備份原始資料庫
    backup_name = f"data/lottery_data.db.emergency_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2("data/lottery_data.db", backup_name)
    print(f"✅ 資料庫已備份至: {backup_name}")
    
    # 強制刪除可能的鎖定檔案
    lock_files = [
        "data/lottery_data.db-journal",
        "data/lottery_data.db-wal",
        "data/lottery_data.db-shm"
    ]
    
    for lock_file in lock_files:
        if os.path.exists(lock_file):
            try:
                os.remove(lock_file)
                print(f"🗑️ 已刪除鎖定檔案: {lock_file}")
            except:
                print(f"⚠️ 無法刪除: {lock_file}")
    
    # 等待系統釋放資源
    time.sleep(3)
    
    try:
        # 使用 WAL 模式避免鎖定
        conn = sqlite3.connect("data/lottery_data.db", timeout=60)
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=30000")
        conn.execute("PRAGMA synchronous=NORMAL")
        
        cursor = conn.cursor()
        
        print("🔧 開始修正資料...")
        
        # 修正已知錯誤資料
        fixes = [
            {
                'period': 114000068,
                'date': '2025-08-26',
                'numbers': [1, 12, 14, 21, 36, 37],
                'special': 3,
                'action': 'update'
            },
            {
                'period': 114000069,
                'date': '2025-08-29', 
                'numbers': [9, 13, 14, 17, 23, 30],
                'special': 5,
                'action': 'insert'
            }
        ]
        
        for fix in fixes:
            period = fix['period']
            
            if fix['action'] == 'update':
                cursor.execute("""
                    UPDATE Powercolor 
                    SET Sdate = ?, Anumber1 = ?, Anumber2 = ?, Anumber3 = ?,
                        Anumber4 = ?, Anumber5 = ?, Anumber6 = ?, Second_district = ?
                    WHERE Period = ?
                """, (fix['date'], *fix['numbers'], fix['special'], period))
                
                if cursor.rowcount > 0:
                    print(f"✅ 已更新期號 {period}")
                else:
                    print(f"⚠️ 期號 {period} 更新失敗，嘗試插入")
                    fix['action'] = 'insert'
            
            if fix['action'] == 'insert':
                cursor.execute("""
                    INSERT OR REPLACE INTO Powercolor 
                    (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3,
                     Anumber4, Anumber5, Anumber6, Second_district)
                    VALUES ('威力彩', ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (period, fix['date'], *fix['numbers'], fix['special']))
                
                print(f"✅ 已插入/替換期號 {period}")
        
        # 刪除有重複號碼的異常資料
        print("🗑️ 清理異常資料...")
        
        # 找出有重複號碼的記錄
        cursor.execute("""
            SELECT Period, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6
            FROM Powercolor
            WHERE Period >= 114000060
        """)
        
        all_records = cursor.fetchall()
        to_delete = []
        
        for record in all_records:
            period = record[0]
            numbers = list(record[1:7])
            
            # 檢查重複號碼
            if len(set(numbers)) != len(numbers):
                to_delete.append(period)
                print(f"⚠️ 發現重複號碼 期號 {period}: {numbers}")
        
        # 刪除異常記錄
        for period in to_delete:
            cursor.execute("DELETE FROM Powercolor WHERE Period = ?", (period,))
            print(f"🗑️ 已刪除異常期號: {period}")
        
        conn.commit()
        print("✅ 所有修正已提交")
        
        # 驗證結果
        print("\n🔍 驗證修正結果:")
        cursor.execute("""
            SELECT Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, Second_district 
            FROM Powercolor 
            WHERE Period >= 114000065 
            ORDER BY Period DESC
        """)
        
        results = cursor.fetchall()
        
        for row in results:
            period, date, n1, n2, n3, n4, n5, n6, sp = row
            numbers = [n1, n2, n3, n4, n5, n6]
            numbers_str = ','.join(f"{n:02d}" for n in numbers)
            
            # 檢查資料有效性
            is_valid = len(set(numbers)) == 6 and all(1 <= n <= 38 for n in numbers) and 1 <= sp <= 8
            status = "✅" if is_valid else "❌"
            
            print(f"{status} 期數: {period}, 日期: {date}, 號碼: {numbers_str}+{sp:02d}")
        
        conn.close()
        print("\n🎉 資料庫修復完成!")
        
        return True
        
    except Exception as e:
        print(f"❌ 修復失敗: {e}")
        return False

if __name__ == "__main__":
    print("🚨 開始緊急修復資料庫...")
    emergency_fix()