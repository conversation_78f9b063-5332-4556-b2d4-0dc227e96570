#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最高信心度預測器
從六次機會策略中提取最可能的單組號碼組合
基於號碼重疊頻率和策略共識度分析
"""

import numpy as np
from collections import Counter
from typing import List, Dict, Tuple
import logging
import sys
import os

# 導入六次機會策略
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from six_chances_strategy import SixChancesStrategy

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('highest_confidence')

class HighestConfidencePredictor:
    """最高信心度預測系統"""
    
    def __init__(self):
        self.strategy_system = SixChancesStrategy()
        
    def extract_highest_confidence_numbers(self, lottery_type: str = 'powercolor') -> Dict:
        """
        從所有策略中提取最高信心度的號碼組合
        
        Args:
            lottery_type: 彩票類型
            
        Returns:
            包含最高信心度號碼組合和分析數據的字典
        """
        
        # 生成所有6種基礎策略
        strategies = [
            'wheel',    # 輪盤覆蓋
            'zone',     # 區間平衡  
            'hotcold',  # 冷熱混合
            'math',     # 數學優化
            'pattern',  # 模式識別
            'ai'        # AI集成
        ]
        
        all_numbers = []
        all_specials = []
        strategy_results = {}
        
        logger.info(f"開始分析 {lottery_type} 的所有策略...")
        
        # 收集所有策略的號碼
        for strategy in strategies:
            try:
                result = self.strategy_system.generate_six_chances(strategy)
                strategy_results[strategy] = result
                
                # 收集主號碼
                for combo in result['combinations']:
                    all_numbers.extend(combo['numbers'])
                    if combo.get('special'):
                        all_specials.append(combo['special'])
                        
                logger.info(f"策略 {strategy} 分析完成")
                
            except Exception as e:
                logger.error(f"策略 {strategy} 分析失敗: {str(e)}")
                continue
        
        # 分析號碼出現頻率
        number_freq = Counter(all_numbers)
        special_freq = Counter(all_specials) if all_specials else Counter()
        
        # 獲取最高頻率的號碼
        most_common_numbers = number_freq.most_common()
        
        logger.info(f"號碼頻率統計: {most_common_numbers[:10]}")
        
        # 提取最高信心度的6個號碼
        confidence_numbers = self._select_optimal_combination(
            most_common_numbers, lottery_type
        )
        
        # 選擇最高信心度的特別號
        confidence_special = special_freq.most_common(1)[0][0] if special_freq else 1
        
        # 計算信心度分數
        confidence_score = self._calculate_confidence_score(
            confidence_numbers, number_freq, len(strategies)
        )
        
        # 分析尾數分布
        tail_analysis = self._analyze_tail_distribution(confidence_numbers)
        
        # 分析區間分布  
        zone_analysis = self._analyze_zone_distribution(confidence_numbers)
        
        result = {
            'lottery_type': lottery_type,
            'highest_confidence_numbers': sorted(confidence_numbers),
            'highest_confidence_special': confidence_special,
            'confidence_score': confidence_score,
            'analysis': {
                'number_frequency': dict(most_common_numbers[:15]),
                'special_frequency': dict(special_freq.most_common(5)) if special_freq else {},
                'tail_distribution': tail_analysis,
                'zone_distribution': zone_analysis,
                'strategy_consensus': self._analyze_strategy_consensus(strategy_results),
                'risk_assessment': self._assess_risk_level(confidence_score)
            },
            'comparison': {
                'vs_six_chances_cost': {
                    'six_chances': 300,
                    'single_bet': 50,
                    'cost_reduction': '83.3%'
                },
                'vs_random_selection': {
                    'random_confidence': 0.1,
                    'this_confidence': confidence_score,
                    'improvement': f'{confidence_score/0.1:.1f}x'
                }
            },
            'backtest_recommendation': self._generate_backtest_recommendation(
                confidence_numbers, confidence_special, confidence_score
            )
        }
        
        return result
    
    def _select_optimal_combination(self, number_freq: List[Tuple], lottery_type: str) -> List[int]:
        """選擇最優的6個號碼組合"""
        
        # 根據頻率和分布平衡選擇
        selected = []
        candidates = [num for num, freq in number_freq]
        
        # 首先選擇前6個最高頻率的號碼
        base_selection = candidates[:8]  # 取前8個作為候選
        
        # 應用平衡策略
        if lottery_type in ['powercolor', 'lotto649']:
            # 威力彩和大樂透：確保尾數分布均勻
            selected = self._balance_tail_distribution(base_selection, 6)
        else:
            # 今彩539：5個號碼
            selected = self._balance_tail_distribution(base_selection, 5)
            
        return selected[:6] if lottery_type in ['powercolor', 'lotto649'] else selected[:5]
    
    def _balance_tail_distribution(self, candidates: List[int], target_count: int) -> List[int]:
        """平衡尾數分布"""
        selected = []
        tail_count = {}
        
        for num in candidates:
            if len(selected) >= target_count:
                break
                
            tail = num % 10
            current_tail_count = tail_count.get(tail, 0)
            
            # 限制同一尾數不超過2個
            if current_tail_count < 2:
                selected.append(num)
                tail_count[tail] = current_tail_count + 1
        
        # 如果還不夠，從剩餘的候選中選擇
        if len(selected) < target_count:
            remaining = [num for num in candidates if num not in selected]
            selected.extend(remaining[:target_count - len(selected)])
            
        return selected
    
    def _calculate_confidence_score(self, numbers: List[int], freq: Counter, strategy_count: int) -> float:
        """計算信心度分數"""
        
        # 基於號碼出現頻率計算
        total_appearances = sum(freq[num] for num in numbers)
        max_possible = strategy_count * 6 * 6  # 最大可能出現次數
        
        frequency_score = min(total_appearances / max_possible, 1.0)
        
        # 平衡性加分
        tail_diversity = len(set(num % 10 for num in numbers)) / 10.0
        
        # 區間分布加分
        zone_count = 0
        for zone_start in range(1, 39, 6):
            if any(zone_start <= num < zone_start + 6 for num in numbers):
                zone_count += 1
        zone_diversity = zone_count / 6.0
        
        # 綜合信心度
        confidence = (frequency_score * 0.7 + tail_diversity * 0.2 + zone_diversity * 0.1)
        
        return min(confidence, 0.95)  # 最高95%
    
    def _analyze_tail_distribution(self, numbers: List[int]) -> Dict:
        """分析尾數分布"""
        tails = [num % 10 for num in numbers]
        tail_count = Counter(tails)
        
        return {
            'tail_frequencies': dict(tail_count),
            'unique_tails': len(set(tails)),
            'most_common_tail': tail_count.most_common(1)[0] if tail_count else (0, 0),
            'balance_score': len(set(tails)) / len(numbers)  # 1.0 = 完全不重複
        }
    
    def _analyze_zone_distribution(self, numbers: List[int]) -> Dict:
        """分析區間分布（1-6, 7-12, 13-18, 19-24, 25-30, 31-38）"""
        zones = {}
        for i in range(6):
            zone_start = i * 6 + 1
            zone_end = zone_start + 6 if i < 5 else 39
            zone_numbers = [num for num in numbers if zone_start <= num < zone_end]
            zones[f'zone_{i+1}'] = {
                'range': f'{zone_start}-{zone_end-1}',
                'numbers': zone_numbers,
                'count': len(zone_numbers)
            }
        
        return zones
    
    def _analyze_strategy_consensus(self, strategy_results: Dict) -> Dict:
        """分析策略共識度"""
        consensus = {}
        
        for strategy_name, result in strategy_results.items():
            numbers = []
            for combo in result['combinations']:
                numbers.extend(combo['numbers'])
            
            consensus[strategy_name] = {
                'unique_numbers': len(set(numbers)),
                'most_frequent': Counter(numbers).most_common(5)
            }
        
        return consensus
    
    def _assess_risk_level(self, confidence_score: float) -> str:
        """評估風險等級"""
        if confidence_score >= 0.8:
            return "低風險 - 高度共識"
        elif confidence_score >= 0.6:
            return "中等風險 - 良好共識"
        elif confidence_score >= 0.4:
            return "中高風險 - 部分共識"
        else:
            return "高風險 - 共識較低"
    
    def _generate_backtest_recommendation(self, numbers: List[int], special: int, confidence: float) -> Dict:
        """生成回測建議"""
        return {
            'recommended_periods': 50,  # 建議回測50期
            'test_strategy': '單組最高信心度投注',
            'expected_performance': {
                'hit_rate_estimate': f'{confidence * 15:.1f}%',  # 估算命中率
                'cost_per_test': 50,
                'total_test_cost': 2500,  # 50期 × 50元
                'break_even_hits': 2  # 需要中獎2次才能保本
            },
            'comparison_baseline': '與六次機會策略(300元/期)相比成本降低83.3%'
        }

def demonstrate_highest_confidence():
    """演示最高信心度預測"""
    predictor = HighestConfidencePredictor()
    
    print("=" * 60)
    print("最高信心度預測系統")
    print("=" * 60)
    
    # 威力彩分析
    print("\n【威力彩最高信心度分析】")
    result = predictor.extract_highest_confidence_numbers('powercolor')
    
    print(f"\n🎯 最高信心度號碼: {result['highest_confidence_numbers']}")
    print(f"🎯 最高信心度特別號: {result['highest_confidence_special']}")
    print(f"📊 整體信心度: {result['confidence_score']:.1%}")
    print(f"⚠️ 風險評估: {result['analysis']['risk_assessment']}")
    
    print(f"\n💰 成本對比:")
    print(f"  六次機會策略: {result['comparison']['vs_six_chances_cost']['six_chances']}元")
    print(f"  單組精準投注: {result['comparison']['vs_six_chances_cost']['single_bet']}元")
    print(f"  成本降低: {result['comparison']['vs_six_chances_cost']['cost_reduction']}")
    
    print(f"\n📈 回測建議:")
    backtest = result['backtest_recommendation']
    print(f"  建議回測期數: {backtest['recommended_periods']}期")
    print(f"  預估命中率: {backtest['expected_performance']['hit_rate_estimate']}")
    print(f"  回測成本: {backtest['expected_performance']['total_test_cost']}元")
    print(f"  保本需求: 中獎{backtest['expected_performance']['break_even_hits']}次")
    
    print(f"\n🔍 尾數分析:")
    tail_analysis = result['analysis']['tail_distribution']
    print(f"  尾數分布: {tail_analysis['tail_frequencies']}")
    print(f"  最常見尾數: {tail_analysis['most_common_tail'][0]} (出現{tail_analysis['most_common_tail'][1]}次)")
    print(f"  平衡性分數: {tail_analysis['balance_score']:.2f}")
    
    print(f"\n📊 號碼頻率統計 (前10名):")
    for num, freq in list(result['analysis']['number_frequency'].items())[:10]:
        print(f"  號碼 {num}: 出現 {freq} 次")

if __name__ == "__main__":
    demonstrate_highest_confidence()