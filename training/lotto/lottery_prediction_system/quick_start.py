#!/usr/bin/env python3
"""
快速啟動腳本 - 一鍵解決所有問題
整合了 start_app.py 的所有功能並修復了截圖中的問題
"""

import os
import sys
import subprocess
import time
import webbrowser
import threading


def show_welcome():
    """顯示歡迎信息"""
    print("=" * 70)
    print("🏆 台灣彩券官方數據專用系統".center(70))
    print("=" * 70)
    print("✅ 修復了你截圖中的所有問題:")
    print("   - 🏆 開獎結果查詢功能 (不再顯示'比對失敗')")
    print("   - 📊 回測分析系統 (完整實現，不再顯示'開發中')")
    print("   - 🔍 預測方法分析 (修復請求失敗問題)")
    print("   - 🔄 自動更新開獎數據")
    print("=" * 70)
    print("🛡️ 重要保證：")
    print("   - ✅ 100% 使用台灣彩券官方開獎數據")
    print("   - ✅ 絕不生成或使用任何模擬數據")
    print("   - ✅ 網路失敗時寧可停止也不用假數據")
    print("=" * 70)


def quick_install_dependencies():
    """快速安裝依賴"""
    print("🔧 快速檢查依賴...")
    
    required = ['streamlit', 'pandas', 'plotly', 'numpy', 'flask', 'requests', 'beautifulsoup4']
    missing = []
    
    for pkg in required:
        try:
            if pkg == 'beautifulsoup4':
                import bs4
            else:
                __import__(pkg)
        except ImportError:
            missing.append(pkg)
    
    if missing:
        print(f"📦 安裝缺少的包: {', '.join(missing)}")
        subprocess.run([sys.executable, "-m", "pip", "install"] + missing, check=True)
        print("✅ 依賴安裝完成")
    else:
        print("✅ 所有依賴已就緒")


def show_options():
    """顯示啟動選項"""
    print("\n🚀 選擇啟動方式:")
    print("1. 🌟 啟動增強版系統 (推薦 - 包含所有修復)")
    print("2. 🏆 啟動原版系統 (包含開獎查詢)")
    print("3. 📊 啟動回測專業版")
    print("4. 🔧 啟動完整功能選單")
    

def start_enhanced_system():
    """啟動增強版系統"""
    print("\n🌟 啟動增強版系統...")
    print("📋 這個版本修復了你截圖中的所有問題！")
    print("🏆 保證：只使用台灣彩券官方數據，絕不生成模擬數據")
    
    def open_browser():
        time.sleep(3)
        webbrowser.open('http://localhost:8501')
        print("🌐 瀏覽器已開啟: http://localhost:8501")
    
    threading.Thread(target=open_browser, daemon=True).start()
    
    subprocess.run([
        sys.executable, "-m", "streamlit", "run", 
        "enhanced_integrated_web_system.py", 
        "--server.port=8501"
    ])


def start_original_system():
    """啟動原版系統"""
    print("\n🏆 啟動原版系統...")
    print("📋 包含完整的開獎結果查詢和自動更新功能")
    
    subprocess.run([sys.executable, "start_app.py"])


def start_backtest_system():
    """啟動回測專業版"""
    print("\n📊 啟動回測專業版...")
    
    def open_browser():
        time.sleep(3)
        webbrowser.open('http://localhost:8502')
        print("🌐 瀏覽器已開啟: http://localhost:8502")
    
    threading.Thread(target=open_browser, daemon=True).start()
    
    subprocess.run([
        sys.executable, "-m", "streamlit", "run",
        "backtest_web_manager.py",
        "--server.port=8502"
    ])


def start_full_menu():
    """啟動完整功能選單"""
    print("\n🔧 啟動完整功能選單...")
    subprocess.run([sys.executable, "enhanced_start_system.py"])


def main():
    """主函數"""
    show_welcome()
    
    # 快速安裝依賴
    try:
        quick_install_dependencies()
    except Exception as e:
        print(f"⚠️ 依賴安裝警告: {e}")
        print("系統將嘗試繼續運行...")
    
    # 顯示選項
    show_options()
    
    try:
        choice = input("\n請選擇 (1-4): ").strip()
        
        if choice == "1":
            start_enhanced_system()
        elif choice == "2":
            start_original_system()
        elif choice == "3":
            start_backtest_system()
        elif choice == "4":
            start_full_menu()
        else:
            print("❌ 無效選項")
            print("🔄 預設啟動增強版系統...")
            time.sleep(2)
            start_enhanced_system()
            
    except KeyboardInterrupt:
        print("\n👋 程式已中斷")
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")
        print("\n🔄 嘗試啟動增強版系統...")
        start_enhanced_system()


if __name__ == "__main__":
    main()