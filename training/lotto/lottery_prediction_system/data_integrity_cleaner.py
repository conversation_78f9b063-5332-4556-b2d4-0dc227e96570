#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
資料完整性清理工具
清理虛假和錯誤的開獎資料
"""

import sqlite3
from datetime import datetime, timedelta
from typing import List, Tuple, Dict

class DataIntegrityCleaner:
    """資料完整性清理器"""
    
    def __init__(self, db_path: str = "data/lottery_data.db"):
        self.db_path = db_path
        
        # 彩票開獎規則
        self.draw_rules = {
            'Powercolor': {
                'name': '威力彩',
                'draw_days': [1, 4],  # 週一、週四
                'description': '週一和週四開獎'
            },
            'Lotto649': {
                'name': '大樂透', 
                'draw_days': [2, 5],  # 週二、週五
                'description': '週二和週五開獎'
            },
            'DailyCash': {
                'name': '今彩539',
                'draw_days': [1, 2, 3, 4, 5, 6, 7],  # 每日
                'description': '每日開獎'
            }
        }
    
    def analyze_suspicious_data(self) -> Dict:
        """分析可疑資料"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        analysis = {}
        today = datetime.now()
        
        try:
            for table, rules in self.draw_rules.items():
                print(f"\n🔍 分析 {rules['name']} ({table}) 資料...")
                
                cursor.execute(f"SELECT Period, Sdate FROM {table} ORDER BY Period DESC LIMIT 50")
                records = cursor.fetchall()
                
                suspicious = []
                future_dates = []
                wrong_days = []
                
                for period, date_str in records:
                    try:
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                        weekday = date_obj.weekday() + 1  # 1=週一
                        
                        issues = []
                        
                        # 檢查未來日期
                        if date_obj > today:
                            issues.append("未來日期")
                            future_dates.append((period, date_str))
                        
                        # 檢查開獎日
                        if weekday not in rules['draw_days']:
                            day_names = ['', '一', '二', '三', '四', '五', '六', '日']
                            issues.append(f"錯誤開獎日(週{day_names[weekday]})")
                            wrong_days.append((period, date_str, weekday))
                        
                        if issues:
                            suspicious.append((period, date_str, issues))
                            
                    except ValueError:
                        suspicious.append((period, date_str, ["日期格式錯誤"]))
                
                analysis[table] = {
                    'name': rules['name'],
                    'suspicious': suspicious,
                    'future_dates': future_dates,
                    'wrong_days': wrong_days,
                    'rules': rules['description']
                }
                
                print(f"   可疑記錄: {len(suspicious)} 筆")
                print(f"   未來日期: {len(future_dates)} 筆") 
                print(f"   錯誤開獎日: {len(wrong_days)} 筆")
                
        finally:
            conn.close()
        
        return analysis
    
    def show_detailed_analysis(self, analysis: Dict):
        """顯示詳細分析結果"""
        print("\n" + "=" * 60)
        print("🚨 資料完整性分析報告")
        print("=" * 60)
        
        total_suspicious = 0
        
        for table, data in analysis.items():
            if data['suspicious']:
                print(f"\n📊 {data['name']} 可疑資料詳情:")
                print(f"   開獎規則: {data['rules']}")
                print("   可疑記錄:")
                
                for period, date, issues in data['suspicious'][:10]:  # 顯示前10筆
                    issues_str = ", ".join(issues)
                    print(f"     {period} ({date}): {issues_str}")
                
                if len(data['suspicious']) > 10:
                    print(f"     ... 還有 {len(data['suspicious']) - 10} 筆可疑記錄")
                
                total_suspicious += len(data['suspicious'])
        
        print(f"\n📈 總計可疑記錄: {total_suspicious} 筆")
        
        if total_suspicious > 0:
            print(f"\n⚠️  這些資料可能是:")
            print(f"   1. 測試資料或模擬資料")
            print(f"   2. 錯誤輸入的資料")
            print(f"   3. 系統bug產生的虛假資料")
            print(f"   4. 惡意或不當的資料插入")
    
    def backup_database(self, backup_suffix: str = None) -> str:
        """備份資料庫"""
        if not backup_suffix:
            backup_suffix = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        backup_path = f"{self.db_path}.backup_{backup_suffix}"
        
        import shutil
        shutil.copy2(self.db_path, backup_path)
        
        print(f"✅ 資料庫已備份至: {backup_path}")
        return backup_path
    
    def clean_suspicious_data(self, analysis: Dict, confirm: bool = True) -> int:
        """清理可疑資料"""
        total_deleted = 0
        
        if confirm:
            print(f"\n⚠️  準備清理可疑資料，此操作不可逆！")
            print(f"請確認是否繼續 (輸入 'YES' 確認): ", end="")
            user_input = input().strip()
            
            if user_input != "YES":
                print("取消清理操作")
                return 0
        
        # 先備份
        backup_path = self.backup_database("before_cleanup")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            for table, data in analysis.items():
                if data['suspicious']:
                    print(f"\n🧹 清理 {data['name']} 可疑資料...")
                    
                    deleted_count = 0
                    for period, date, issues in data['suspicious']:
                        cursor.execute(f"DELETE FROM {table} WHERE Period = ?", (period,))
                        if cursor.rowcount > 0:
                            deleted_count += 1
                            print(f"   刪除: {period} ({date}) - {', '.join(issues)}")
                    
                    total_deleted += deleted_count
                    print(f"   共刪除 {deleted_count} 筆記錄")
            
            conn.commit()
            print(f"\n✅ 清理完成，總共刪除 {total_deleted} 筆可疑記錄")
            
        except Exception as e:
            conn.rollback()
            print(f"❌ 清理失敗: {e}")
            total_deleted = 0
        finally:
            conn.close()
        
        return total_deleted
    
    def find_missing_periods(self, table: str, start_period: int = None) -> List[int]:
        """找出缺失的期號"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 取得期號範圍
            if not start_period:
                cursor.execute(f"SELECT MIN(Period) FROM {table} WHERE Period >= 114000000")
                start_period = cursor.fetchone()[0] or 114000001
            
            cursor.execute(f"SELECT MAX(Period) FROM {table}")
            max_period = cursor.fetchone()[0] or start_period
            
            # 取得所有現有期號
            cursor.execute(f"SELECT Period FROM {table} WHERE Period BETWEEN ? AND ? ORDER BY Period", 
                         (start_period, max_period))
            existing_periods = set(row[0] for row in cursor.fetchall())
            
            # 找出缺失期號
            missing = []
            for period in range(start_period, max_period + 1):
                if period not in existing_periods:
                    missing.append(period)
            
            return missing
            
        finally:
            conn.close()

def main():
    """主函數"""
    cleaner = DataIntegrityCleaner()
    
    print("🔍 台灣彩券資料完整性檢查工具")
    print("=" * 40)
    
    # 分析可疑資料
    analysis = cleaner.analyze_suspicious_data()
    
    # 顯示詳細分析
    cleaner.show_detailed_analysis(analysis)
    
    # 檢查缺失期號
    print(f"\n🔍 檢查缺失期號...")
    for table, rules in cleaner.draw_rules.items():
        missing = cleaner.find_missing_periods(table)
        if missing:
            print(f"   {rules['name']}: 缺失 {len(missing)} 個期號")
            if len(missing) <= 10:
                print(f"      缺失期號: {missing}")
            else:
                print(f"      部分缺失: {missing[:5]} ... {missing[-5:]}")
        else:
            print(f"   {rules['name']}: 無缺失期號")
    
    # 詢問是否清理
    total_suspicious = sum(len(data['suspicious']) for data in analysis.values())
    if total_suspicious > 0:
        print(f"\n💡 建議:")
        print(f"   1. 先備份資料庫")
        print(f"   2. 清理可疑資料")
        print(f"   3. 使用手動更新工具補正確資料")
        print(f"\n是否執行清理? (y/n): ", end="")
        
        try:
            if input().lower() == 'y':
                deleted = cleaner.clean_suspicious_data(analysis, confirm=True)
                if deleted > 0:
                    print(f"\n🎉 資料清理完成！建議接下來:")
                    print(f"   1. 檢查系統是否正常運作")
                    print(f"   2. 使用手動更新工具補充正確的開獎資料")
            else:
                print("清理操作已取消")
        except EOFError:
            print("\n清理操作已跳過（非互動環境）")

if __name__ == "__main__":
    main()