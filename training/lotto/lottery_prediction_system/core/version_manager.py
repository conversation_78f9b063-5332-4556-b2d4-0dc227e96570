#!/usr/bin/env python3
"""
版本管理模組
Version Management Module
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict

logger = logging.getLogger('core.version_manager')

@dataclass
class VersionInfo:
    """版本信息數據類"""
    version: str
    release_date: str
    features: List[str]
    fixes: List[str]
    changes: List[str]
    compatibility: Dict[str, str]

class VersionManager:
    """版本管理器"""
    
    CURRENT_VERSION = "3.0.0"
    
    def __init__(self):
        """初始化版本管理器"""
        self.version_history = self._load_version_history()
        self.current_info = self._get_current_version_info()
    
    def _load_version_history(self) -> Dict[str, VersionInfo]:
        """載入版本歷史記錄"""
        return {
            "3.0.0": VersionInfo(
                version="3.0.0",
                release_date="2025-08-26",
                features=[
                    "數據真實性保證框架",
                    "智能同步系統",
                    "核心模組系統",
                    "進階預測算法",
                    "Web界面優化",
                    "多彩票類型支持"
                ],
                fixes=[
                    "修復台灣彩券網站結構變更問題",
                    "優化數據庫連接穩定性",
                    "改進錯誤處理機制",
                    "修復模組導入問題"
                ],
                changes=[
                    "重構核心架構",
                    "改進用戶界面",
                    "增強數據分析功能",
                    "優化預測準確性"
                ],
                compatibility={
                    "python": ">=3.9",
                    "numpy": ">=1.21.0",
                    "pandas": ">=1.3.0",
                    "flask": ">=2.0.0",
                    "requests": ">=2.25.0"
                }
            ),
            "2.5.0": VersionInfo(
                version="2.5.0",
                release_date="2025-07-15",
                features=[
                    "多算法預測引擎",
                    "歷史數據分析",
                    "基礎Web界面"
                ],
                fixes=[
                    "數據同步問題",
                    "預測準確性改進"
                ],
                changes=[
                    "算法優化",
                    "界面改進"
                ],
                compatibility={
                    "python": ">=3.8",
                    "numpy": ">=1.20.0",
                    "pandas": ">=1.2.0"
                }
            )
        }
    
    def _get_current_version_info(self) -> VersionInfo:
        """獲取當前版本信息"""
        return self.version_history.get(self.CURRENT_VERSION)
    
    def get_version(self) -> str:
        """獲取當前版本號"""
        return self.CURRENT_VERSION
    
    def get_version_info(self, version: Optional[str] = None) -> Optional[VersionInfo]:
        """
        獲取版本信息
        
        Args:
            version: 版本號，如果為None則返回當前版本信息
            
        Returns:
            版本信息對象
        """
        version = version or self.CURRENT_VERSION
        return self.version_history.get(version)
    
    def list_versions(self) -> List[str]:
        """獲取所有版本列表"""
        return list(self.version_history.keys())
    
    def is_compatible(self, required_version: str) -> bool:
        """
        檢查版本兼容性
        
        Args:
            required_version: 需要的版本號
            
        Returns:
            是否兼容
        """
        try:
            current_parts = [int(x) for x in self.CURRENT_VERSION.split('.')]
            required_parts = [int(x) for x in required_version.split('.')]
            
            # 主版本號必須匹配
            if current_parts[0] != required_parts[0]:
                return False
            
            # 次版本號必須大於等於要求
            if len(current_parts) > 1 and len(required_parts) > 1:
                if current_parts[1] < required_parts[1]:
                    return False
            
            return True
        except Exception as e:
            logger.error(f"版本兼容性檢查失敗: {e}")
            return False
    
    def get_changelog(self, from_version: Optional[str] = None) -> Dict[str, Any]:
        """
        獲取變更日誌
        
        Args:
            from_version: 起始版本號，如果為None則返回當前版本的變更
            
        Returns:
            變更日誌
        """
        if from_version:
            # 返回從指定版本到當前版本的所有變更
            changelog = {}
            for version, info in self.version_history.items():
                if version > from_version:
                    changelog[version] = asdict(info)
            return changelog
        else:
            # 只返回當前版本的變更
            return {self.CURRENT_VERSION: asdict(self.current_info)} if self.current_info else {}
    
    def check_updates(self) -> Dict[str, Any]:
        """
        檢查更新（模擬功能）
        
        Returns:
            更新檢查結果
        """
        return {
            "current_version": self.CURRENT_VERSION,
            "latest_version": self.CURRENT_VERSION,
            "update_available": False,
            "check_time": datetime.now().isoformat(),
            "message": "您正在使用最新版本"
        }
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        獲取系統信息
        
        Returns:
            系統信息
        """
        import platform
        import sys
        
        return {
            "version": self.CURRENT_VERSION,
            "python_version": sys.version,
            "platform": platform.platform(),
            "architecture": platform.architecture()[0],
            "release_date": self.current_info.release_date if self.current_info else "Unknown",
            "core_module_available": True
        }

# 全局版本管理器實例
version_manager = VersionManager()