#!/usr/bin/env python3
"""
系統配置管理模組
Configuration Management Module
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger('core.config')

class Config:
    """系統配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路徑，如果為None則使用默認配置
        """
        self.config_path = config_path or 'config.json'
        self._config = {}
        self._load_config()
    
    def _load_config(self):
        """載入配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
                logger.info(f"配置文件載入成功: {self.config_path}")
            else:
                self._config = self._get_default_config()
                logger.warning(f"配置文件不存在，使用默認配置: {self.config_path}")
        except Exception as e:
            logger.error(f"載入配置文件失敗: {e}")
            self._config = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """獲取默認配置"""
        return {
            "system": {
                "name": "彩票預測系統",
                "version": "3.0.0",
                "debug": True,
                "log_level": "INFO"
            },
            "database": {
                "type": "sqlite",
                "path": "data/lottery_data.db",
                "backup_enabled": True,
                "backup_interval": 24
            },
            "prediction": {
                "default_method": "enhanced_multi_algorithm",
                "confidence_threshold": 0.7,
                "max_predictions": 10,
                "enable_core_refiner": True
            },
            "web": {
                "host": "127.0.0.1",
                "port": 5000,
                "debug": True,
                "secret_key": "lottery_prediction_system_2025"
            },
            "scraper": {
                "base_url": "https://www.taiwanlottery.com",
                "timeout": 30,
                "retry_count": 3,
                "delay_between_requests": 2
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        獲取配置值
        
        Args:
            key: 配置鍵，支持點記法 (如 'database.type')
            default: 默認值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """
        設置配置值
        
        Args:
            key: 配置鍵，支持點記法
            value: 配置值
        """
        keys = key.split('.')
        config = self._config
        
        # 遍歷到最後一個鍵之前
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 設置最後一個鍵的值
        config[keys[-1]] = value
    
    def save(self):
        """保存配置到文件"""
        try:
            # 確保目錄存在
            Path(self.config_path).parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, ensure_ascii=False, indent=2)
            
            logger.info(f"配置文件保存成功: {self.config_path}")
        except Exception as e:
            logger.error(f"保存配置文件失敗: {e}")
    
    def reload(self):
        """重新載入配置文件"""
        self._load_config()
    
    @property
    def config(self) -> Dict[str, Any]:
        """獲取完整配置"""
        return self._config.copy()
    
    # 便捷屬性
    @property
    def debug(self) -> bool:
        return self.get('system.debug', False)
    
    @property
    def database_path(self) -> str:
        return self.get('database.path', 'data/lottery_data.db')
    
    @property
    def web_host(self) -> str:
        return self.get('web.host', '127.0.0.1')
    
    @property
    def web_port(self) -> int:
        return self.get('web.port', 5000)

# 全局配置實例
config = Config()