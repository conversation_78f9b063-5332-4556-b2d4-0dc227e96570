"""
彩票預測系統 - 自動化日更新系統
每日自動更新彩票結果並進行分析，在預測下一期之前完成數據更新和分析
"""

import os
import sys
import schedule
import time
import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import json
import traceback

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.db_manager import DBManager
from data.lottery_daily_updater import LotteryDailyUpdater
from analysis import PredictionAnalyzer
from analysis.number_pattern_analyzer import NumberPatternAnalyzer
from analysis.prediction_success_analyzer import PredictionSuccessAnalyzer
from prediction.integrated_predictor import IntegratedPredictor
from display import PredictionDisplay
from config_manager import ConfigManager

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/daily_update_system.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('daily_update_system')

class DailyUpdateSystem:
    """自動化日更新系統"""
    
    def __init__(self, config_manager=None):
        self.config_manager = config_manager or ConfigManager()
        self.db_manager = DBManager(config_manager=self.config_manager)
        self.daily_updater = LotteryDailyUpdater()
        self.prediction_analyzer = PredictionAnalyzer(self.db_manager)
        self.number_analyzer = NumberPatternAnalyzer(self.db_manager)
        self.success_analyzer = PredictionSuccessAnalyzer(self.db_manager)
        self.integrated_predictor = IntegratedPredictor(self.db_manager)
        self.display = PredictionDisplay()
        
        self.lottery_types = ['powercolor', 'lotto649', 'dailycash']
        self.update_schedule = {
            'powercolor': {'days': ['monday', 'thursday'], 'time': '21:30'},
            'lotto649': {'days': ['tuesday', 'friday'], 'time': '21:30'},
            'dailycash': {'days': ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'], 'time': '20:30'}
        }
        
    def setup_schedule(self):
        """設置自動化排程"""
        logger.info("設置自動化排程...")
        
        # 每日數據更新檢查（每天早上8點）
        schedule.every().day.at("08:00").do(self.daily_data_update)
        
        # 每日分析更新（每天晚上11點）
        schedule.every().day.at("23:00").do(self.daily_analysis_update)
        
        # 預測前的準備工作
        for lottery_type, config in self.update_schedule.items():
            for day in config['days']:
                # 在開獎前1小時進行最終檢查和預測
                pre_draw_time = self._calculate_pre_draw_time(config['time'])
                getattr(schedule.every(), day).at(pre_draw_time).do(
                    self.pre_draw_preparation, lottery_type
                )
        
        logger.info("自動化排程設置完成")
    
    def daily_data_update(self):
        """每日數據更新"""
        logger.info("開始每日數據更新...")
        
        try:
            update_results = {}
            
            for lottery_type in self.lottery_types:
                logger.info(f"更新 {lottery_type} 數據...")
                
                # 檢查是否有新的開獎結果
                result = self.daily_updater.update_lottery_data(lottery_type)
                update_results[lottery_type] = result
                
                if result.get('new_records', 0) > 0:
                    logger.info(f"{lottery_type} 發現 {result['new_records']} 筆新記錄")
                    
                    # 更新預測結果的匹配狀況
                    self._update_prediction_matches(lottery_type)
                else:
                    logger.info(f"{lottery_type} 無新數據")
            
            # 保存更新報告
            self._save_daily_update_report(update_results)
            
            logger.info("每日數據更新完成")
            return update_results
            
        except Exception as e:
            logger.error(f"每日數據更新失敗: {str(e)}")
            logger.error(traceback.format_exc())
            return None
    
    def daily_analysis_update(self):
        """每日分析更新"""
        logger.info("開始每日分析更新...")
        
        try:
            analysis_results = {}
            
            for lottery_type in self.lottery_types:
                logger.info(f"分析 {lottery_type} 數據...")
                
                # 1. 預測準確性分析
                accuracy_analysis = self.prediction_analyzer.analyze_prediction_accuracy(lottery_type)
                
                # 2. 號碼模式分析
                pattern_analysis = self._analyze_recent_patterns(lottery_type)
                
                # 3. 成功預測分析
                success_analysis = self.success_analyzer.analyze_successful_predictions(lottery_type)
                
                analysis_results[lottery_type] = {
                    'accuracy_analysis': accuracy_analysis,
                    'pattern_analysis': pattern_analysis,
                    'success_analysis': success_analysis,
                    'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            
            # 保存分析報告
            self._save_daily_analysis_report(analysis_results)
            
            logger.info("每日分析更新完成")
            return analysis_results
            
        except Exception as e:
            logger.error(f"每日分析更新失敗: {str(e)}")
            logger.error(traceback.format_exc())
            return None
    
    def pre_draw_preparation(self, lottery_type: str):
        """開獎前準備工作"""
        logger.info(f"開始 {lottery_type} 開獎前準備...")
        
        try:
            # 1. 最終數據檢查
            self._final_data_check(lottery_type)
            
            # 2. 生成預測
            prediction_results = self.integrated_predictor.predict_separated(
                lottery_type=lottery_type,
                candidates_count=5,
                min_confidence=0.4
            )
            
            if prediction_results:
                # 3. 保存預測結果
                self._save_prediction_results(lottery_type, prediction_results)
                
                # 4. 生成預測報告
                report_path = self.display.save_prediction_report(prediction_results)
                logger.info(f"預測報告已保存: {report_path}")
                
                # 5. 發送通知（如果配置了）
                self._send_prediction_notification(lottery_type, prediction_results)
            
            logger.info(f"{lottery_type} 開獎前準備完成")
            return prediction_results
            
        except Exception as e:
            logger.error(f"{lottery_type} 開獎前準備失敗: {str(e)}")
            logger.error(traceback.format_exc())
            return None
    
    def _update_prediction_matches(self, lottery_type: str):
        """更新預測匹配結果"""
        try:
            # 載入最新的開獎結果
            df = self.db_manager.load_lottery_data(lottery_type)
            if df.empty:
                return
            
            latest_result = df.iloc[-1]
            period = latest_result['Period']
            
            # 檢查是否有該期的預測記錄
            prediction_records = self.db_manager.load_prediction_records(lottery_type)
            if prediction_records.empty:
                return
            
            # 查找對應期數的預測
            period_predictions = prediction_records[prediction_records['Period'] == period]
            
            for _, pred_record in period_predictions.iterrows():
                # 計算匹配數
                match_count = self._calculate_match_count(pred_record, latest_result, lottery_type)
                
                # 更新數據庫
                self.db_manager.update_prediction_match(
                    pred_record['ID'], 
                    match_count,
                    self._extract_actual_numbers(latest_result, lottery_type)
                )
            
            logger.info(f"{lottery_type} 期數 {period} 的預測匹配結果已更新")
            
        except Exception as e:
            logger.error(f"更新預測匹配結果時出錯: {str(e)}")
    
    def _analyze_recent_patterns(self, lottery_type: str) -> Dict:
        """分析最近的號碼模式"""
        try:
            df = self.db_manager.load_lottery_data(lottery_type)
            if df.empty or len(df) < 5:
                return {}
            
            # 分析最近5期的號碼模式
            recent_patterns = {}
            for i in range(min(5, len(df))):
                row = df.iloc[-(i+1)]
                numbers = self._extract_numbers_from_row(row, lottery_type)
                
                if numbers:
                    pattern_analysis = self.number_analyzer.analyze_number_appearance_reasons(
                        lottery_type, numbers, str(row['Period'])
                    )
                    recent_patterns[f"period_{row['Period']}"] = pattern_analysis
            
            return recent_patterns
            
        except Exception as e:
            logger.error(f"分析最近模式時出錯: {str(e)}")
            return {}
    
    def _final_data_check(self, lottery_type: str):
        """最終數據檢查"""
        try:
            # 檢查數據完整性
            df = self.db_manager.load_lottery_data(lottery_type)
            if df.empty:
                logger.warning(f"{lottery_type} 無歷史數據")
                return False
            
            # 檢查最新數據的時間
            latest_date = df.iloc[-1].get('Date', '')
            if latest_date:
                latest_datetime = datetime.strptime(latest_date, '%Y-%m-%d')
                days_ago = (datetime.now() - latest_datetime).days
                
                if days_ago > 7:
                    logger.warning(f"{lottery_type} 最新數據已過期 {days_ago} 天")
            
            logger.info(f"{lottery_type} 數據檢查完成，共 {len(df)} 筆記錄")
            return True
            
        except Exception as e:
            logger.error(f"數據檢查時出錯: {str(e)}")
            return False
    
    def _save_prediction_results(self, lottery_type: str, results: Dict):
        """保存預測結果到數據庫"""
        try:
            period = results.get('period', 'unknown')
            
            # 保存機器學習預測
            ml_prediction = results.get('ml_prediction')
            if ml_prediction and ml_prediction.candidates:
                for i, candidate in enumerate(ml_prediction.candidates):
                    self.db_manager.save_prediction(
                        prediction_data={
                            'lottery_type': lottery_type,
                            'period': period,
                            'method': f"ml_candidate_{i+1}",
                            'predicted_numbers': candidate.get('main_numbers', []),
                            'special_number': candidate.get('special_number'),
                            'confidence': candidate.get('confidence', 0),
                            'explanation': candidate.get('explanation', [])
                        },
                        period=period,
                        lottery_type=lottery_type
                    )
            
            # 保存板路分析預測
            bp_prediction = results.get('board_path_prediction')
            if bp_prediction and bp_prediction.candidates:
                for i, candidate in enumerate(bp_prediction.candidates):
                    self.db_manager.save_prediction(
                        prediction_data={
                            'lottery_type': lottery_type,
                            'period': period,
                            'method': f"board_path_candidate_{i+1}",
                            'predicted_numbers': candidate.get('main_numbers', []),
                            'special_number': candidate.get('special_number'),
                            'confidence': candidate.get('confidence', 0),
                            'explanation': candidate.get('explanation', [])
                        },
                        period=period,
                        lottery_type=lottery_type
                    )
            
            logger.info(f"{lottery_type} 期數 {period} 的預測結果已保存")
            
        except Exception as e:
            logger.error(f"保存預測結果時出錯: {str(e)}")
    
    def _save_daily_update_report(self, update_results: Dict):
        """保存每日更新報告"""
        try:
            report_date = datetime.now().strftime('%Y%m%d')
            report_path = f"analysis_results/daily_update_report_{report_date}.json"
            
            os.makedirs(os.path.dirname(report_path), exist_ok=True)
            
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(update_results, f, ensure_ascii=False, indent=2)
            
            logger.info(f"每日更新報告已保存: {report_path}")
            
        except Exception as e:
            logger.error(f"保存更新報告時出錯: {str(e)}")
    
    def _save_daily_analysis_report(self, analysis_results: Dict):
        """保存每日分析報告"""
        try:
            report_date = datetime.now().strftime('%Y%m%d')
            report_path = f"analysis_results/daily_analysis_{report_date}.json"
            
            os.makedirs(os.path.dirname(report_path), exist_ok=True)
            
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(analysis_results, f, ensure_ascii=False, indent=2)
            
            logger.info(f"每日分析報告已保存: {report_path}")
            
        except Exception as e:
            logger.error(f"保存分析報告時出錯: {str(e)}")
    
    def _send_prediction_notification(self, lottery_type: str, results: Dict):
        """發送預測通知（可擴展為郵件、微信等）"""
        try:
            # 這裡可以實現各種通知方式
            # 例如：郵件、微信、Telegram等
            
            # 目前只記錄到日誌
            ml_count = len(results.get('ml_prediction', {}).get('candidates', []))
            bp_count = len(results.get('board_path_prediction', {}).get('candidates', []))
            
            logger.info(f"📢 {lottery_type} 預測完成通知:")
            logger.info(f"   機器學習候選: {ml_count} 個")
            logger.info(f"   板路分析候選: {bp_count} 個")
            logger.info(f"   預測期數: {results.get('period', 'unknown')}")
            
        except Exception as e:
            logger.error(f"發送通知時出錯: {str(e)}")
    
    def _calculate_match_count(self, pred_record, actual_result, lottery_type: str) -> int:
        """計算預測匹配數量"""
        try:
            # 提取預測號碼
            predicted_numbers = self._extract_predicted_numbers(pred_record, lottery_type)

            # 提取實際號碼
            actual_numbers = self._extract_actual_numbers(actual_result, lottery_type)

            if not predicted_numbers or not actual_numbers:
                return 0

            # 計算匹配數
            predicted_set = set(predicted_numbers)
            actual_set = set(actual_numbers)
            match_count = len(predicted_set & actual_set)

            return match_count

        except Exception as e:
            logger.error(f"計算匹配數時出錯: {str(e)}")
            return 0

    def _extract_predicted_numbers(self, pred_record, lottery_type: str) -> List[int]:
        """提取預測號碼"""
        try:
            numbers = []

            if lottery_type == 'powercolor':
                for i in range(1, 7):
                    col = f'PredA{i}'
                    if col in pred_record and pd.notna(pred_record[col]):
                        numbers.append(int(pred_record[col]))
            elif lottery_type == 'lotto649':
                for i in range(1, 7):
                    col = f'PredA{i}'
                    if col in pred_record and pd.notna(pred_record[col]):
                        numbers.append(int(pred_record[col]))
            elif lottery_type == 'dailycash':
                for i in range(1, 6):
                    col = f'PredA{i}'
                    if col in pred_record and pd.notna(pred_record[col]):
                        numbers.append(int(pred_record[col]))

            return numbers

        except Exception as e:
            logger.error(f"提取預測號碼時出錯: {str(e)}")
            return []

    def _extract_actual_numbers(self, actual_result, lottery_type: str) -> List[int]:
        """提取實際開獎號碼"""
        try:
            numbers = []

            if lottery_type == 'powercolor':
                for i in range(1, 7):
                    col = f'Anumber{i}'
                    if col in actual_result and pd.notna(actual_result[col]):
                        numbers.append(int(actual_result[col]))
            elif lottery_type == 'lotto649':
                for i in range(1, 7):
                    col = f'Anumber{i}'
                    if col in actual_result and pd.notna(actual_result[col]):
                        numbers.append(int(actual_result[col]))
            elif lottery_type == 'dailycash':
                for i in range(1, 6):
                    col = f'Anumber{i}'
                    if col in actual_result and pd.notna(actual_result[col]):
                        numbers.append(int(actual_result[col]))

            return numbers

        except Exception as e:
            logger.error(f"提取實際號碼時出錯: {str(e)}")
            return []

    def _extract_numbers_from_row(self, row, lottery_type: str) -> List[int]:
        """從數據行提取號碼"""
        try:
            numbers = []

            if lottery_type == 'powercolor':
                for i in range(1, 7):
                    col = f'Anumber{i}'
                    if col in row and pd.notna(row[col]):
                        numbers.append(int(row[col]))
            elif lottery_type == 'lotto649':
                for i in range(1, 7):
                    col = f'Anumber{i}'
                    if col in row and pd.notna(row[col]):
                        numbers.append(int(row[col]))
            elif lottery_type == 'dailycash':
                for i in range(1, 6):
                    col = f'Anumber{i}'
                    if col in row and pd.notna(row[col]):
                        numbers.append(int(row[col]))

            return numbers

        except Exception as e:
            logger.error(f"提取號碼時出錯: {str(e)}")
            return []

    def _calculate_pre_draw_time(self, draw_time: str) -> str:
        """計算開獎前1小時的時間"""
        try:
            hour, minute = map(int, draw_time.split(':'))
            pre_hour = hour - 1
            if pre_hour < 0:
                pre_hour = 23
            return f"{pre_hour:02d}:{minute:02d}"
        except:
            return "20:00"  # 默認時間
    
    def run_scheduler(self):
        """運行排程器"""
        logger.info("自動化日更新系統啟動...")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分鐘檢查一次
                
        except KeyboardInterrupt:
            logger.info("自動化系統已停止")
        except Exception as e:
            logger.error(f"排程器運行錯誤: {str(e)}")
    
    def manual_update(self, lottery_type: str = None):
        """手動觸發更新"""
        if lottery_type:
            logger.info(f"手動更新 {lottery_type}...")
            self.daily_data_update()
            self.daily_analysis_update()
            self.pre_draw_preparation(lottery_type)
        else:
            logger.info("手動更新所有彩票類型...")
            self.daily_data_update()
            self.daily_analysis_update()
            for lt in self.lottery_types:
                self.pre_draw_preparation(lt)

def main():
    """主函數"""
    system = DailyUpdateSystem()
    
    # 檢查命令行參數
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "setup":
            system.setup_schedule()
            system.run_scheduler()
        elif command == "update":
            lottery_type = sys.argv[2] if len(sys.argv) > 2 else None
            system.manual_update(lottery_type)
        elif command == "test":
            # 測試模式
            system.daily_data_update()
            system.daily_analysis_update()
        else:
            print("用法: python daily_update_system.py [setup|update|test] [lottery_type]")
    else:
        # 默認運行排程器
        system.setup_schedule()
        system.run_scheduler()

if __name__ == "__main__":
    main()
