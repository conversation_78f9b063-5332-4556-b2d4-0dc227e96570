#!/usr/bin/env python3
"""
Web API 集成模組
將增強型更新器集成到現有的Web系統中
"""

from flask import Blueprint, jsonify, request
import logging
from updated_lottery_updater import UpdatedLotteryUpdater
from threading import Thread
import time
from datetime import datetime

# 創建藍圖
api_bp = Blueprint('enhanced_api', __name__)

# 全局更新器實例
updater = UpdatedLotteryUpdater()

# 更新狀態追蹤
update_status = {
    'is_updating': False,
    'last_update_time': None,
    'last_result': None
}

logger = logging.getLogger('web_api_integration')

@api_bp.route('/api/enhanced_update_results', methods=['POST'])
def enhanced_update_results():
    """增強型彩票數據更新API"""
    global update_status
    
    try:
        # 檢查是否正在更新
        if update_status['is_updating']:
            return jsonify({
                'success': False,
                'message': '更新正在進行中，請稍後再試',
                'is_updating': True
            }), 409
        
        # 開始後台更新
        def run_update():
            global update_status
            update_status['is_updating'] = True
            
            try:
                logger.info("開始增強型彩票數據更新")
                result = updater.update_lottery_data()
                
                update_status['last_result'] = result
                update_status['last_update_time'] = datetime.now().isoformat()
                
                logger.info(f"更新完成: 新增 {result['total_new_records']} 筆記錄")
                
            except Exception as e:
                logger.error(f"更新過程中發生錯誤: {e}")
                update_status['last_result'] = {
                    'success': False,
                    'error_message': str(e),
                    'total_new_records': 0
                }
            finally:
                update_status['is_updating'] = False
        
        # 啟動後台線程
        update_thread = Thread(target=run_update)
        update_thread.daemon = True
        update_thread.start()
        
        return jsonify({
            'success': True,
            'message': '更新已開始，請稍後查看結果',
            'is_updating': True
        })
        
    except Exception as e:
        logger.error(f"API調用失敗: {e}")
        return jsonify({
            'success': False,
            'message': f'系統錯誤: {str(e)}',
            'is_updating': False
        }), 500

@api_bp.route('/api/update_status', methods=['GET'])
def get_update_status():
    """獲取更新狀態"""
    try:
        # 獲取數據狀態
        data_status = updater.get_data_status()
        
        return jsonify({
            'success': True,
            'is_updating': update_status['is_updating'],
            'last_update_time': update_status['last_update_time'],
            'last_result': update_status['last_result'],
            'data_status': data_status,
            'system_info': {
                'core_module_available': True,
                'enhanced_scraper_enabled': True,
                'update_method': 'multi_strategy_scraper',
                'version': '2.0'
            }
        })
        
    except Exception as e:
        logger.error(f"獲取狀態失敗: {e}")
        return jsonify({
            'success': False,
            'message': f'獲取狀態失敗: {str(e)}'
        }), 500

@api_bp.route('/api/test_scraper', methods=['GET'])
def test_scraper():
    """測試爬蟲功能"""
    try:
        logger.info("開始測試增強型爬蟲")
        
        # 測試爬蟲
        results = updater.scraper.scrape_latest_results()
        
        test_result = {
            'success': True,
            'message': f'測試完成，獲取到 {len(results)} 個結果',
            'results_count': len(results),
            'results': []
        }
        
        # 格式化結果
        for result in results:
            lottery_name = updater.lottery_mapping.get(result.lottery_type, {}).get('name', result.lottery_type)
            
            test_result['results'].append({
                'lottery_type': result.lottery_type,
                'lottery_name': lottery_name,
                'period': result.period,
                'draw_date': result.draw_date,
                'main_numbers': result.main_numbers,
                'special_number': result.special_number,
                'scraped_at': result.scraped_at
            })
        
        return jsonify(test_result)
        
    except Exception as e:
        logger.error(f"測試爬蟲失敗: {e}")
        return jsonify({
            'success': False,
            'message': f'測試失敗: {str(e)}',
            'results_count': 0
        }), 500

# 修改現有的更新API以使用新的更新器
def patch_existing_api(app):
    """修補現有的API以使用增強型更新器"""
    
    @app.route('/api/auto_update_results', methods=['POST'])
    def auto_update_results_enhanced():
        """覆蓋原有的自動更新API"""
        try:
            logger.info("使用增強型更新器進行自動更新")
            
            # 獲取數據狀態
            data_status = updater.get_data_status()
            
            # 檢查是否需要更新
            needs_update = False
            status_info = []
            
            for lottery, info in data_status.items():
                days_old = info.get('days_old', 0)
                status = "✅ 數據最新" if days_old is None or days_old <= 1 else "⚠️ 數據稍舊，建議更新"
                
                status_info.append({
                    'lottery': lottery,
                    'latest_period': info.get('latest_period', 'N/A'),
                    'latest_date': info.get('latest_date', 'N/A'),
                    'total_records': info.get('total_records', 0),
                    'days_old': days_old,
                    'status': status
                })
                
                if days_old is None or days_old > 1:
                    needs_update = True
            
            # 返回狀態信息（不自動執行更新，讓用戶手動觸發）
            response = {
                'success': True,
                'system_status': '良好',
                'core_module_available': True,
                'enhanced_scraper_enabled': True,
                'data_status': status_info,
                'needs_update': needs_update,
                'message': '💡 提示：使用增強型更新器解決了台灣彩券網站結構變更問題' if needs_update else '✅ 所有數據都是最新的',
                'update_method': 'enhanced_multi_strategy_scraper',
                'version': '2.0'
            }
            
            return jsonify(response)
            
        except Exception as e:
            logger.error(f"增強型自動更新API錯誤: {e}")
            return jsonify({
                'success': False,
                'message': f'系統錯誤: {str(e)}',
                'core_module_available': True,
                'enhanced_scraper_enabled': False
            }), 500

# 應用修補的輔助函數
def integrate_enhanced_api(app):
    """將增強型API集成到Flask應用中"""
    
    # 註冊新的API藍圖
    app.register_blueprint(api_bp)
    
    # 修補現有API
    patch_existing_api(app)
    
    logger.info("增強型API集成完成")
    
    return app