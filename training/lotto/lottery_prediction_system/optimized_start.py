#!/usr/bin/env python3
"""
優化的系統啟動包裝器
"""

print("🚀 彩票預測系統 - 優化啟動器")
print("=" * 50)

# 1. 應用SSL修復
try:
    exec(open('ssl_env_fix.py').read())
except FileNotFoundError:
    print("⚠️ SSL修復腳本不存在，跳過")
except Exception as e:
    print(f"⚠️ SSL修復應用失敗: {e}")

# 2. 檢查關鍵依賴
missing_deps = []

try:
    import redis
    print("✅ redis 可用")
except ImportError:
    missing_deps.append("redis")

try:
    import dotenv
    print("✅ python-dotenv 可用")  
except ImportError:
    missing_deps.append("python-dotenv")

if missing_deps:
    print(f"⚠️ 缺少依賴: {', '.join(missing_deps)}")
    print("💡 系統將使用替代方案運行")
else:
    print("✅ 所有關鍵依賴都可用")

# 3. 啟動主系統
print("\n🌐 啟動主系統...")

try:
    # 導入並啟動主應用
    import app
    if hasattr(app, 'app'):
        print("✅ Flask應用載入成功")
        print("🎯 系統運行在: http://localhost:7891")
        app.app.run(host='0.0.0.0', port=7891, debug=True)
    else:
        print("❌ Flask應用載入失敗")
except Exception as e:
    print(f"❌ 系統啟動失敗: {e}")
    print("💡 請檢查系統配置或使用原始啟動腳本")
