#!/usr/bin/env python3
"""
Phase 3 實時數據管理系統
實現自動數據更新、驗證和緩存管理
"""

import asyncio
import aiohttp
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging
import json
import hashlib
from dataclasses import dataclass, asdict
from enum import Enum
import time
from pathlib import Path

logger = logging.getLogger('realtime_data_manager')

class DataSource(Enum):
    """數據源類型"""
    OFFICIAL_API = "official_api"
    BACKUP_API = "backup_api"
    MANUAL_IMPORT = "manual_import"

class DataStatus(Enum):
    """數據狀態"""
    PENDING = "pending"
    PROCESSING = "processing"
    VALIDATED = "validated"
    ERROR = "error"
    CACHED = "cached"

@dataclass
class LotteryData:
    """彩票數據結構"""
    lottery_type: str
    period: str
    draw_date: datetime
    numbers: List[int]
    special_number: Optional[int]
    source: DataSource
    status: DataStatus
    created_at: datetime
    validated_at: Optional[datetime] = None
    error_message: Optional[str] = None
    checksum: Optional[str] = None

    def __post_init__(self):
        """計算數據校驗和"""
        if not self.checksum:
            data_str = f"{self.lottery_type}_{self.period}_{'_'.join(map(str, self.numbers))}"
            if self.special_number:
                data_str += f"_{self.special_number}"
            self.checksum = hashlib.md5(data_str.encode()).hexdigest()

class DataValidator:
    """數據驗證器"""
    
    def __init__(self):
        self.validation_rules = {
            'powercolor': {
                'main_numbers': {'count': 6, 'range': (1, 38)},
                'special_number': {'count': 1, 'range': (1, 8)}
            },
            'lotto649': {
                'main_numbers': {'count': 6, 'range': (1, 49)},
                'special_number': {'count': 1, 'range': (1, 10)}
            },
            'dailycash': {
                'main_numbers': {'count': 5, 'range': (1, 39)},
                'special_number': None
            }
        }
    
    def validate_lottery_data(self, data: LotteryData) -> tuple[bool, Optional[str]]:
        """驗證彩票數據"""
        try:
            # 檢查彩票類型
            if data.lottery_type not in self.validation_rules:
                return False, f"不支持的彩票類型: {data.lottery_type}"
            
            rules = self.validation_rules[data.lottery_type]
            
            # 驗證主號碼
            main_rule = rules['main_numbers']
            if len(data.numbers) != main_rule['count']:
                return False, f"主號碼數量錯誤: 期望{main_rule['count']}個，實際{len(data.numbers)}個"
            
            min_num, max_num = main_rule['range']
            for num in data.numbers:
                if not (min_num <= num <= max_num):
                    return False, f"主號碼超出範圍: {num} 不在 [{min_num}, {max_num}] 範圍內"
            
            # 檢查號碼重複
            if len(set(data.numbers)) != len(data.numbers):
                return False, "主號碼存在重複"
            
            # 驗證特別號
            if rules.get('special_number'):
                if data.special_number is None:
                    return False, "缺少特別號碼"
                
                special_rule = rules['special_number']
                min_special, max_special = special_rule['range']
                if not (min_special <= data.special_number <= max_special):
                    return False, f"特別號碼超出範圍: {data.special_number} 不在 [{min_special}, {max_special}] 範圍內"
            
            # 驗證期號格式
            if not data.period or len(data.period) < 8:
                return False, f"期號格式錯誤: {data.period}"
            
            # 驗證開獎日期
            if data.draw_date > datetime.now():
                return False, f"開獎日期不能是未來時間: {data.draw_date}"
            
            return True, None
            
        except Exception as e:
            return False, f"驗證過程中發生錯誤: {str(e)}"
    
    def validate_data_consistency(self, new_data: LotteryData, existing_data: List[LotteryData]) -> tuple[bool, Optional[str]]:
        """驗證數據一致性"""
        try:
            # 檢查重複期號
            for existing in existing_data:
                if (existing.lottery_type == new_data.lottery_type and 
                    existing.period == new_data.period):
                    if existing.checksum != new_data.checksum:
                        return False, f"期號 {new_data.period} 數據不一致"
                    return False, f"期號 {new_data.period} 已存在"
            
            # 檢查期號連續性
            same_type_data = [d for d in existing_data if d.lottery_type == new_data.lottery_type]
            if same_type_data:
                latest_period = max(d.period for d in same_type_data)
                if new_data.period < latest_period:
                    return False, f"期號順序錯誤: {new_data.period} < {latest_period}"
            
            return True, None
            
        except Exception as e:
            return False, f"一致性驗證錯誤: {str(e)}"

class CacheManager:
    """智能緩存管理器"""
    
    def __init__(self, cache_dir: str = "cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.cache_expiry = {
            'recent_data': timedelta(minutes=5),
            'prediction_results': timedelta(hours=1),
            'analysis_cache': timedelta(hours=6)
        }
        self.memory_cache = {}
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0
        }
    
    def _get_cache_path(self, cache_type: str, key: str) -> Path:
        """獲取緩存文件路徑"""
        return self.cache_dir / cache_type / f"{key}.json"
    
    def _is_cache_valid(self, cache_path: Path, cache_type: str) -> bool:
        """檢查緩存是否有效"""
        if not cache_path.exists():
            return False
        
        try:
            cache_time = datetime.fromtimestamp(cache_path.stat().st_mtime)
            expiry_time = self.cache_expiry.get(cache_type, timedelta(hours=1))
            return datetime.now() - cache_time < expiry_time
        except:
            return False
    
    def get_cache(self, cache_type: str, key: str) -> Optional[Any]:
        """獲取緩存數據"""
        # 首先檢查內存緩存
        memory_key = f"{cache_type}:{key}"
        if memory_key in self.memory_cache:
            self.cache_stats['hits'] += 1
            return self.memory_cache[memory_key]
        
        # 檢查文件緩存
        cache_path = self._get_cache_path(cache_type, key)
        if self._is_cache_valid(cache_path, cache_type):
            try:
                with open(cache_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                # 存入內存緩存
                self.memory_cache[memory_key] = data
                self.cache_stats['hits'] += 1
                return data
            except Exception as e:
                logger.warning(f"讀取緩存失敗: {e}")
        
        self.cache_stats['misses'] += 1
        return None
    
    def set_cache(self, cache_type: str, key: str, data: Any) -> bool:
        """設置緩存數據"""
        try:
            # 存入內存緩存
            memory_key = f"{cache_type}:{key}"
            self.memory_cache[memory_key] = data
            
            # 存入文件緩存
            cache_path = self._get_cache_path(cache_type, key)
            cache_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(cache_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            
            return True
        except Exception as e:
            logger.error(f"設置緩存失敗: {e}")
            return False
    
    def clear_cache(self, cache_type: Optional[str] = None) -> bool:
        """清理緩存"""
        try:
            if cache_type:
                # 清理特定類型的緩存
                cache_prefix = f"{cache_type}:"
                keys_to_remove = [k for k in self.memory_cache.keys() if k.startswith(cache_prefix)]
                for key in keys_to_remove:
                    del self.memory_cache[key]
                    self.cache_stats['evictions'] += 1
                
                cache_type_dir = self.cache_dir / cache_type
                if cache_type_dir.exists():
                    import shutil
                    shutil.rmtree(cache_type_dir)
            else:
                # 清理所有緩存
                self.memory_cache.clear()
                if self.cache_dir.exists():
                    import shutil
                    shutil.rmtree(self.cache_dir)
                    self.cache_dir.mkdir(exist_ok=True)
                self.cache_stats['evictions'] += len(self.memory_cache)
            
            return True
        except Exception as e:
            logger.error(f"清理緩存失敗: {e}")
            return False
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """獲取緩存統計"""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = self.cache_stats['hits'] / total_requests if total_requests > 0 else 0
        
        return {
            'hits': self.cache_stats['hits'],
            'misses': self.cache_stats['misses'],
            'evictions': self.cache_stats['evictions'],
            'hit_rate': hit_rate,
            'memory_cache_size': len(self.memory_cache),
            'disk_cache_size': sum(1 for _ in self.cache_dir.rglob('*.json'))
        }

class DataSourceManager:
    """多數據源管理器"""
    
    def __init__(self):
        self.data_sources = {
            'powercolor': [
                "https://api.taiwanlottery.com/powercolor",
                "https://backup-api.lottery.gov.tw/powercolor"
            ],
            'lotto649': [
                "https://api.taiwanlottery.com/lotto649", 
                "https://backup-api.lottery.gov.tw/lotto649"
            ],
            'dailycash': [
                "https://api.taiwanlottery.com/dailycash",
                "https://backup-api.lottery.gov.tw/dailycash"
            ]
        }
        self.source_status = {}
        self.retry_config = {
            'max_retries': 3,
            'retry_delay': 5,
            'timeout': 30
        }
    
    async def fetch_latest_data(self, lottery_type: str) -> Optional[Dict[str, Any]]:
        """從多個數據源獲取最新數據"""
        if lottery_type not in self.data_sources:
            logger.error(f"不支持的彩票類型: {lottery_type}")
            return None
        
        sources = self.data_sources[lottery_type]
        
        for source_url in sources:
            try:
                logger.info(f"嘗試從數據源獲取數據: {source_url}")
                data = await self._fetch_from_source(source_url, lottery_type)
                if data:
                    self._update_source_status(source_url, True)
                    return data
            except Exception as e:
                logger.warning(f"數據源 {source_url} 獲取失敗: {e}")
                self._update_source_status(source_url, False)
                continue
        
        logger.error(f"所有數據源都無法獲取 {lottery_type} 數據")
        return None
    
    async def _fetch_from_source(self, source_url: str, lottery_type: str) -> Optional[Dict[str, Any]]:
        """從指定數據源獲取數據"""
        async with aiohttp.ClientSession() as session:
            for attempt in range(self.retry_config['max_retries']):
                try:
                    timeout = aiohttp.ClientTimeout(total=self.retry_config['timeout'])
                    async with session.get(source_url, timeout=timeout) as response:
                        if response.status == 200:
                            data = await response.json()
                            return self._parse_api_data(data, lottery_type)
                        else:
                            logger.warning(f"API返回錯誤狀態碼: {response.status}")
                
                except asyncio.TimeoutError:
                    logger.warning(f"請求超時 (嘗試 {attempt + 1}/{self.retry_config['max_retries']})")
                except Exception as e:
                    logger.warning(f"請求失敗 (嘗試 {attempt + 1}/{self.retry_config['max_retries']}): {e}")
                
                if attempt < self.retry_config['max_retries'] - 1:
                    await asyncio.sleep(self.retry_config['retry_delay'])
        
        return None
    
    def _parse_api_data(self, api_data: Dict[str, Any], lottery_type: str) -> Optional[Dict[str, Any]]:
        """解析API數據"""
        try:
            # 這裡需要根據實際API格式進行調整
            # 目前使用模擬數據格式
            if not api_data or 'results' not in api_data:
                return None
            
            latest_result = api_data['results'][0] if api_data['results'] else None
            if not latest_result:
                return None
            
            # 解析數據格式
            parsed_data = {
                'lottery_type': lottery_type,
                'period': latest_result.get('period', ''),
                'draw_date': datetime.fromisoformat(latest_result.get('draw_date', '')),
                'numbers': latest_result.get('numbers', []),
                'special_number': latest_result.get('special_number'),
                'source': DataSource.OFFICIAL_API,
                'created_at': datetime.now()
            }
            
            return parsed_data
            
        except Exception as e:
            logger.error(f"解析API數據失敗: {e}")
            return None
    
    def _update_source_status(self, source_url: str, success: bool):
        """更新數據源狀態"""
        if source_url not in self.source_status:
            self.source_status[source_url] = {
                'success_count': 0,
                'failure_count': 0,
                'last_success': None,
                'last_failure': None
            }
        
        status = self.source_status[source_url]
        if success:
            status['success_count'] += 1
            status['last_success'] = datetime.now()
        else:
            status['failure_count'] += 1
            status['last_failure'] = datetime.now()
    
    def get_source_status(self) -> Dict[str, Any]:
        """獲取數據源狀態"""
        return self.source_status.copy()

class RealTimeDataManager:
    """實時數據管理器主類"""
    
    def __init__(self, db_path: str = "data/lottery_data.db", cache_dir: str = "cache"):
        self.db_path = db_path
        self.validator = DataValidator()
        self.cache_manager = CacheManager(cache_dir)
        self.source_manager = DataSourceManager()
        self.update_status = {
            'last_update': None,
            'update_count': 0,
            'error_count': 0
        }
        
        # 確保數據庫目錄存在
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        self._init_database()
    
    def _init_database(self):
        """初始化數據庫表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 創建實時數據表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS realtime_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        lottery_type TEXT NOT NULL,
                        period TEXT NOT NULL,
                        draw_date DATETIME NOT NULL,
                        numbers TEXT NOT NULL,
                        special_number INTEGER,
                        source TEXT NOT NULL,
                        status TEXT NOT NULL,
                        created_at DATETIME NOT NULL,
                        validated_at DATETIME,
                        error_message TEXT,
                        checksum TEXT NOT NULL,
                        UNIQUE(lottery_type, period)
                    )
                ''')
                
                # 創建更新日誌表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS update_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        lottery_type TEXT NOT NULL,
                        action TEXT NOT NULL,
                        status TEXT NOT NULL,
                        message TEXT,
                        created_at DATETIME NOT NULL
                    )
                ''')
                
                conn.commit()
                logger.info("數據庫初始化完成")
                
        except Exception as e:
            logger.error(f"數據庫初始化失敗: {e}")
            raise
    
    async def update_lottery_data(self, lottery_type: str) -> bool:
        """更新指定彩票類型的數據"""
        try:
            logger.info(f"開始更新 {lottery_type} 數據")
            
            # 檢查緩存
            cache_key = f"latest_{lottery_type}"
            cached_data = self.cache_manager.get_cache('recent_data', cache_key)
            
            if cached_data:
                logger.info(f"使用緩存數據: {lottery_type}")
                return True
            
            # 從數據源獲取最新數據
            api_data = await self.source_manager.fetch_latest_data(lottery_type)
            if not api_data:
                self._log_update_action(lottery_type, "fetch", "failed", "無法從任何數據源獲取數據")
                return False
            
            # 創建LotteryData對象
            lottery_data = LotteryData(
                lottery_type=api_data['lottery_type'],
                period=api_data['period'],
                draw_date=api_data['draw_date'],
                numbers=api_data['numbers'],
                special_number=api_data.get('special_number'),
                source=api_data['source'],
                status=DataStatus.PENDING,
                created_at=api_data['created_at']
            )
            
            # 驗證數據
            is_valid, error_msg = self.validator.validate_lottery_data(lottery_data)
            if not is_valid:
                lottery_data.status = DataStatus.ERROR
                lottery_data.error_message = error_msg
                self._log_update_action(lottery_type, "validate", "failed", error_msg)
                return False
            
            # 檢查數據一致性
            existing_data = self._load_existing_data(lottery_type)
            is_consistent, consistency_error = self.validator.validate_data_consistency(lottery_data, existing_data)
            
            if not is_consistent:
                lottery_data.status = DataStatus.ERROR
                lottery_data.error_message = consistency_error
                self._log_update_action(lottery_type, "consistency", "failed", consistency_error)
                return False
            
            # 數據驗證成功
            lottery_data.status = DataStatus.VALIDATED
            lottery_data.validated_at = datetime.now()
            
            # 存儲到數據庫
            if self._save_lottery_data(lottery_data):
                # 更新緩存
                self.cache_manager.set_cache('recent_data', cache_key, asdict(lottery_data))
                
                # 記錄成功
                self.update_status['last_update'] = datetime.now()
                self.update_status['update_count'] += 1
                self._log_update_action(lottery_type, "update", "success", f"成功更新期號 {lottery_data.period}")
                
                logger.info(f"{lottery_type} 數據更新成功: 期號 {lottery_data.period}")
                return True
            else:
                self.update_status['error_count'] += 1
                return False
                
        except Exception as e:
            self.update_status['error_count'] += 1
            self._log_update_action(lottery_type, "update", "error", str(e))
            logger.error(f"更新 {lottery_type} 數據時發生錯誤: {e}")
            return False
    
    def _load_existing_data(self, lottery_type: str) -> List[LotteryData]:
        """載入現有數據"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT * FROM realtime_data WHERE lottery_type = ? ORDER BY period DESC LIMIT 100",
                    (lottery_type,)
                )
                
                existing_data = []
                for row in cursor.fetchall():
                    data = LotteryData(
                        lottery_type=row[1],
                        period=row[2],
                        draw_date=datetime.fromisoformat(row[3]),
                        numbers=json.loads(row[4]),
                        special_number=row[5],
                        source=DataSource(row[6]),
                        status=DataStatus(row[7]),
                        created_at=datetime.fromisoformat(row[8]),
                        validated_at=datetime.fromisoformat(row[9]) if row[9] else None,
                        error_message=row[10],
                        checksum=row[11]
                    )
                    existing_data.append(data)
                
                return existing_data
                
        except Exception as e:
            logger.error(f"載入現有數據失敗: {e}")
            return []
    
    def _save_lottery_data(self, data: LotteryData) -> bool:
        """保存彩票數據到數據庫"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO realtime_data 
                    (lottery_type, period, draw_date, numbers, special_number, 
                     source, status, created_at, validated_at, error_message, checksum)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    data.lottery_type,
                    data.period,
                    data.draw_date.isoformat(),
                    json.dumps(data.numbers),
                    data.special_number,
                    data.source.value,
                    data.status.value,
                    data.created_at.isoformat(),
                    data.validated_at.isoformat() if data.validated_at else None,
                    data.error_message,
                    data.checksum
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"保存數據失敗: {e}")
            return False
    
    def _log_update_action(self, lottery_type: str, action: str, status: str, message: str):
        """記錄更新操作"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO update_logs (lottery_type, action, status, message, created_at)
                    VALUES (?, ?, ?, ?, ?)
                ''', (lottery_type, action, status, message, datetime.now().isoformat()))
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"記錄更新日誌失敗: {e}")
    
    async def update_all_lottery_types(self) -> Dict[str, bool]:
        """更新所有彩票類型的數據"""
        lottery_types = ['powercolor', 'lotto649', 'dailycash']
        results = {}
        
        # 並行更新所有彩票類型
        tasks = [self.update_lottery_data(lottery_type) for lottery_type in lottery_types]
        update_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for lottery_type, result in zip(lottery_types, update_results):
            if isinstance(result, Exception):
                logger.error(f"更新 {lottery_type} 時發生異常: {result}")
                results[lottery_type] = False
            else:
                results[lottery_type] = result
        
        return results
    
    def get_system_status(self) -> Dict[str, Any]:
        """獲取系統狀態"""
        cache_stats = self.cache_manager.get_cache_stats()
        source_status = self.source_manager.get_source_status()
        
        return {
            'update_status': self.update_status.copy(),
            'cache_stats': cache_stats,
            'source_status': source_status,
            'database_status': self._get_database_status()
        }
    
    def _get_database_status(self) -> Dict[str, Any]:
        """獲取數據庫狀態"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 統計各彩票類型的數據量
                cursor.execute('''
                    SELECT lottery_type, COUNT(*) as count, MAX(created_at) as latest
                    FROM realtime_data 
                    GROUP BY lottery_type
                ''')
                
                data_stats = {}
                for row in cursor.fetchall():
                    data_stats[row[0]] = {
                        'count': row[1],
                        'latest': row[2]
                    }
                
                # 統計錯誤數據
                cursor.execute('''
                    SELECT COUNT(*) FROM realtime_data WHERE status = 'error'
                ''')
                error_count = cursor.fetchone()[0]
                
                return {
                    'data_stats': data_stats,
                    'error_count': error_count,
                    'database_path': self.db_path,
                    'database_size': Path(self.db_path).stat().st_size if Path(self.db_path).exists() else 0
                }
                
        except Exception as e:
            logger.error(f"獲取數據庫狀態失敗: {e}")
            return {'error': str(e)}

# 使用示例
async def main():
    """測試實時數據管理器"""
    # 初始化實時數據管理器
    data_manager = RealTimeDataManager()
    
    print("🚀 Phase 3 實時數據管理系統測試")
    print("=" * 50)
    
    # 測試單個彩票類型更新
    print("\n📊 測試威力彩數據更新...")
    result = await data_manager.update_lottery_data('powercolor')
    print(f"更新結果: {'成功' if result else '失敗'}")
    
    # 測試所有彩票類型更新
    print("\n📊 測試所有彩票類型更新...")
    results = await data_manager.update_all_lottery_types()
    for lottery_type, success in results.items():
        print(f"{lottery_type}: {'成功' if success else '失敗'}")
    
    # 獲取系統狀態
    print("\n📈 系統狀態:")
    status = data_manager.get_system_status()
    print(json.dumps(status, indent=2, ensure_ascii=False, default=str))

if __name__ == "__main__":
    asyncio.run(main())