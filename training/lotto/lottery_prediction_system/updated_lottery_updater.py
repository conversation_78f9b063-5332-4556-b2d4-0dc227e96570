#!/usr/bin/env python3
"""
更新後的彩票數據更新器
集成增強型爬蟲解決JavaScript渲染問題
"""

import sqlite3
import logging
from datetime import datetime, timedelta
import time
import traceback
import os
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

# 導入增強型爬蟲
from enhanced_scraper import EnhancedTaiwanLotteryScraper, LotteryResult

# 設置日誌記錄
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='logs/updated_lottery_updater.log',
    encoding='utf-8'
)
logger = logging.getLogger('updated_lottery_updater')

class UpdatedLotteryUpdater:
    """更新後的彩票數據更新器"""
    
    def __init__(self):
        """初始化更新器"""
        self.db_path = 'data/lottery_data.db'
        self.scraper = EnhancedTaiwanLotteryScraper()
        
        # 確保日誌目錄存在
        os.makedirs('logs', exist_ok=True)
        
        # 彩票類型映射
        self.lottery_mapping = {
            'powercolor': {
                'table': 'Powercolor',
                'name': '威力彩',
                'columns': ['Period', 'Sdate', 'Anumber1', 'Anumber2', 'Anumber3', 
                           'Anumber4', 'Anumber5', 'Anumber6', 'Second_district']
            },
            'lotto649': {
                'table': 'Lotto649', 
                'name': '大樂透',
                'columns': ['Period', 'Sdate', 'Anumber1', 'Anumber2', 'Anumber3',
                           'Anumber4', 'Anumber5', 'Anumber6', 'SpecialNumber']
            },
            'dailycash': {
                'table': 'DailyCash',
                'name': '今彩539', 
                'columns': ['Period', 'Sdate', 'Anumber1', 'Anumber2', 'Anumber3',
                           'Anumber4', 'Anumber5']
            }
        }
    
    def create_connection(self):
        """建立資料庫連接"""
        try:
            return sqlite3.connect(self.db_path)
        except sqlite3.Error as e:
            logger.error(f"資料庫連接錯誤: {e}")
            raise
    
    def ensure_tables_exist(self):
        """確保所有需要的資料表都存在"""
        try:
            conn = self.create_connection()
            cursor = conn.cursor()
            
            # 創建威力彩表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS Powercolor (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                gameKind TEXT,
                Period TEXT,
                Sdate DATE,
                Anumber1 TEXT,
                Anumber2 TEXT,
                Anumber3 TEXT,
                Anumber4 TEXT,
                Anumber5 TEXT,
                Anumber6 TEXT,
                Second_district TEXT
            )
            """)
            
            # 創建大樂透表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS Lotto649 (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                gameKind TEXT,
                Period TEXT,
                Sdate DATE,
                Anumber1 TEXT,
                Anumber2 TEXT,
                Anumber3 TEXT,
                Anumber4 TEXT,
                Anumber5 TEXT,
                Anumber6 TEXT,
                SpecialNumber TEXT
            )
            """)
            
            # 創建今彩539表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS DailyCash (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                gameKind TEXT,
                Period TEXT,
                Sdate DATE,
                Anumber1 TEXT,
                Anumber2 TEXT,
                Anumber3 TEXT,
                Anumber4 TEXT,
                Anumber5 TEXT
            )
            """)
            
            # 創建更新日誌表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS LotteryUpdateLog (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                LotteryType TEXT,
                LastUpdateTime DATETIME,
                Status TEXT,
                ResultCount INTEGER
            )
            """)
            
            conn.commit()
            logger.info("資料表檢查完成")
            
        except sqlite3.Error as e:
            logger.error(f"創建資料表失敗: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def period_exists(self, lottery_type: str, period: int) -> bool:
        """檢查期號是否已存在"""
        conn = None
        try:
            conn = self.create_connection()
            cursor = conn.cursor()
            
            table = self.lottery_mapping[lottery_type]['table']
            cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE Period = ?", (str(period),))
            count = cursor.fetchone()[0]
            
            return count > 0
            
        except sqlite3.Error as e:
            logger.error(f"檢查期號存在性失敗: {e}")
            return False
        finally:
            if conn:
                conn.close()
    
    def insert_lottery_result(self, result: LotteryResult) -> bool:
        """插入彩票結果到資料庫"""
        conn = None
        try:
            if result.lottery_type not in self.lottery_mapping:
                logger.warning(f"未知的彩票類型: {result.lottery_type}")
                return False
            
            # 檢查期號是否已存在
            if self.period_exists(result.lottery_type, result.period):
                logger.info(f"{self.lottery_mapping[result.lottery_type]['name']}期數 {result.period} 已存在，跳過")
                return False
            
            conn = self.create_connection()
            cursor = conn.cursor()
            
            table = self.lottery_mapping[result.lottery_type]['table']
            lottery_name = self.lottery_mapping[result.lottery_type]['name']
            
            if result.lottery_type == 'powercolor':
                # 威力彩
                cursor.execute(f"""
                INSERT INTO {table} (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3, 
                                   Anumber4, Anumber5, Anumber6, Second_district)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    lottery_name,
                    str(result.period),
                    result.draw_date,
                    str(result.main_numbers[0]) if len(result.main_numbers) > 0 else None,
                    str(result.main_numbers[1]) if len(result.main_numbers) > 1 else None,
                    str(result.main_numbers[2]) if len(result.main_numbers) > 2 else None,
                    str(result.main_numbers[3]) if len(result.main_numbers) > 3 else None,
                    str(result.main_numbers[4]) if len(result.main_numbers) > 4 else None,
                    str(result.main_numbers[5]) if len(result.main_numbers) > 5 else None,
                    str(result.special_number) if result.special_number else None
                ))
                
            elif result.lottery_type == 'lotto649':
                # 大樂透
                cursor.execute(f"""
                INSERT INTO {table} (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3,
                                   Anumber4, Anumber5, Anumber6, SpecialNumber)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    lottery_name,
                    str(result.period),
                    result.draw_date,
                    str(result.main_numbers[0]) if len(result.main_numbers) > 0 else None,
                    str(result.main_numbers[1]) if len(result.main_numbers) > 1 else None,
                    str(result.main_numbers[2]) if len(result.main_numbers) > 2 else None,
                    str(result.main_numbers[3]) if len(result.main_numbers) > 3 else None,
                    str(result.main_numbers[4]) if len(result.main_numbers) > 4 else None,
                    str(result.main_numbers[5]) if len(result.main_numbers) > 5 else None,
                    str(result.special_number) if result.special_number else None
                ))
                
            elif result.lottery_type == 'dailycash':
                # 今彩539
                cursor.execute(f"""
                INSERT INTO {table} (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3,
                                   Anumber4, Anumber5)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    lottery_name,
                    str(result.period),
                    result.draw_date,
                    str(result.main_numbers[0]) if len(result.main_numbers) > 0 else None,
                    str(result.main_numbers[1]) if len(result.main_numbers) > 1 else None,
                    str(result.main_numbers[2]) if len(result.main_numbers) > 2 else None,
                    str(result.main_numbers[3]) if len(result.main_numbers) > 3 else None,
                    str(result.main_numbers[4]) if len(result.main_numbers) > 4 else None
                ))
            
            conn.commit()
            logger.info(f"成功插入{lottery_name}期數 {result.period} 的資料")
            return True
            
        except sqlite3.Error as e:
            logger.error(f"插入資料失敗: {e}")
            return False
        finally:
            if conn:
                conn.close()
    
    def update_lottery_data(self) -> Dict[str, Any]:
        """更新彩票數據"""
        start_time = time.time()
        results_summary = {
            'success': False,
            'total_new_records': 0,
            'lottery_results': {},
            'error_message': None,
            'execution_time': 0
        }
        
        try:
            logger.info("🚀 開始更新彩票數據...")
            
            # 確保資料表存在
            self.ensure_tables_exist()
            
            # 使用增強型爬蟲獲取最新數據
            logger.info("📥 使用增強型爬蟲獲取最新開獎結果...")
            lottery_results = self.scraper.scrape_latest_results()
            
            if not lottery_results:
                results_summary['error_message'] = "未能獲取到任何開獎數據"
                logger.warning("未能獲取到任何開獎數據")
                return results_summary
            
            # 處理獲取到的結果
            for result in lottery_results:
                lottery_name = self.lottery_mapping.get(result.lottery_type, {}).get('name', result.lottery_type)
                
                if result.lottery_type not in results_summary['lottery_results']:
                    results_summary['lottery_results'][lottery_name] = {
                        'new_records': 0,
                        'skipped_records': 0
                    }
                
                # 嘗試插入資料
                if self.insert_lottery_result(result):
                    results_summary['lottery_results'][lottery_name]['new_records'] += 1
                    results_summary['total_new_records'] += 1
                else:
                    results_summary['lottery_results'][lottery_name]['skipped_records'] += 1
            
            # 記錄更新狀態
            self.log_update_status(len(lottery_results), results_summary['total_new_records'])
            
            results_summary['success'] = True
            results_summary['execution_time'] = time.time() - start_time
            
            logger.info(f"✅ 更新完成！新增 {results_summary['total_new_records']} 筆記錄，耗時 {results_summary['execution_time']:.2f} 秒")
            
        except Exception as e:
            error_msg = f"更新過程中發生錯誤: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            results_summary['error_message'] = error_msg
            results_summary['execution_time'] = time.time() - start_time
        
        return results_summary
    
    def log_update_status(self, total_scraped: int, new_records: int):
        """記錄更新狀態"""
        conn = None
        try:
            conn = self.create_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
            INSERT INTO LotteryUpdateLog (LotteryType, LastUpdateTime, Status, ResultCount)
            VALUES (?, ?, ?, ?)
            """, (
                'ALL',
                datetime.now(),
                'SUCCESS' if new_records > 0 else 'NO_NEW_DATA',
                new_records
            ))
            
            conn.commit()
            
        except sqlite3.Error as e:
            logger.error(f"記錄更新狀態失敗: {e}")
        finally:
            if conn:
                conn.close()
    
    def get_data_status(self) -> Dict[str, Any]:
        """獲取數據狀態"""
        status = {}
        conn = None
        
        try:
            conn = self.create_connection()
            cursor = conn.cursor()
            
            for lottery_type, mapping in self.lottery_mapping.items():
                table = mapping['table']
                name = mapping['name']
                
                # 獲取最新期號和日期
                cursor.execute(f"""
                SELECT Period, Sdate, COUNT(*) as total_count 
                FROM {table} 
                ORDER BY Period DESC 
                LIMIT 1
                """)
                
                result = cursor.fetchone()
                if result:
                    latest_period, latest_date, total_count = result
                    
                    # 計算數據年齡
                    if latest_date:
                        try:
                            data_date = datetime.strptime(latest_date, '%Y-%m-%d')
                            days_old = (datetime.now() - data_date).days
                        except:
                            days_old = None
                    else:
                        days_old = None
                    
                    status[name] = {
                        'latest_period': latest_period,
                        'latest_date': latest_date,
                        'total_records': total_count,
                        'days_old': days_old,
                        'status': '良好' if days_old is None or days_old <= 3 else '需要更新'
                    }
                else:
                    status[name] = {
                        'latest_period': None,
                        'latest_date': None,
                        'total_records': 0,
                        'days_old': None,
                        'status': '無數據'
                    }
            
        except sqlite3.Error as e:
            logger.error(f"獲取數據狀態失敗: {e}")
        finally:
            if conn:
                conn.close()
        
        return status

def main():
    """主函數 - 執行更新"""
    updater = UpdatedLotteryUpdater()
    
    print("🎯 彩票數據更新器 v2.0 - 增強版")
    print("=" * 50)
    
    # 顯示當前數據狀態
    print("📊 當前數據狀態:")
    status = updater.get_data_status()
    for lottery, info in status.items():
        print(f"   {lottery}: 期號 {info['latest_period']}, 日期 {info['latest_date']}, 狀態 {info['status']}")
    
    print("\n🚀 開始更新...")
    results = updater.update_lottery_data()
    
    print("\n" + "=" * 50)
    if results['success']:
        print("✅ 更新成功！")
        print(f"⏱️  執行時間: {results['execution_time']:.2f} 秒")
        print(f"📈 新增記錄: {results['total_new_records']} 筆")
        
        if results['lottery_results']:
            print("\n詳細結果:")
            for lottery, stats in results['lottery_results'].items():
                print(f"   {lottery}: 新增 {stats['new_records']} 筆, 跳過 {stats['skipped_records']} 筆")
    else:
        print("❌ 更新失敗!")
        print(f"錯誤信息: {results['error_message']}")

if __name__ == "__main__":
    main()