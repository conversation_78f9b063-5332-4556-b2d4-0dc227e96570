#!/usr/bin/env python3
"""
整合式彩票預測系統 - Web界面
結合開獎查詢、回測分析、預測方法分析的完整系統
"""

import streamlit as st
import pandas as pd
import json
import os
import sqlite3
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from batch_backtest import BatchBacktester, BacktestConfig, GameType, PredictionMethod
from backtest_manager import BacktestManager


def init_session_state():
    """初始化session state"""
    if 'backtest_manager' not in st.session_state:
        st.session_state.backtest_manager = BacktestManager()
    if 'current_results' not in st.session_state:
        st.session_state.current_results = None


def main():
    st.set_page_config(
        page_title="彩票預測系統",
        page_icon="🎯",
        layout="wide"
    )
    
    init_session_state()
    
    # 頂部標題和導航
    st.markdown("""
        <div style='background: linear-gradient(90deg, #4e79a7 0%, #6b4c93 100%); padding: 1rem; border-radius: 10px; margin-bottom: 2rem;'>
            <h1 style='color: white; text-align: center; margin: 0;'>🎯 彩票預測系統</h1>
            <p style='color: white; text-align: center; margin: 0; opacity: 0.9;'>完整的開獎查詢、回測分析、預測方法評估系統</p>
        </div>
    """, unsafe_allow_html=True)
    
    # 主導航選單
    tab1, tab2, tab3, tab4 = st.tabs([
        "🏆 開獎結果查詢", 
        "📊 回測分析系統", 
        "🔍 預測方法分析", 
        "📈 系統管理"
    ])
    
    with tab1:
        show_lottery_results_page()
    
    with tab2:
        show_backtest_analysis_page()
    
    with tab3:
        show_prediction_analysis_page()
    
    with tab4:
        show_system_management_page()


def show_lottery_results_page():
    """開獎結果查詢頁面"""
    st.header("🏆 開獎結果查詢")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # 遊戲類型選擇
        game_options = ["威力彩", "大樂透", "今彩539"]
        selected_game = st.selectbox("選擇彩票類型", game_options)
    
    with col2:
        # 期數搜尋
        period_input = st.text_input(
            "輸入期號", 
            placeholder="例如: 114000058",
            help="格式: 114000### (民國114年第###期)"
        )
    
    with col3:
        # 顯示筆數
        display_count = st.selectbox("顯示筆數", [10, 20, 50], index=1)
    
    # 查詢和重置按鈕
    col_btn1, col_btn2 = st.columns(2)
    with col_btn1:
        search_btn = st.button("🔍 查詢", type="primary", use_container_width=True)
    with col_btn2:
        reset_btn = st.button("🔄 重置", use_container_width=True)
    
    if search_btn or not period_input:
        show_lottery_results(selected_game, period_input, display_count)


def show_lottery_results(game_type, period_filter, limit):
    """顯示開獎結果"""
    try:
        # 連接資料庫
        db_path = "data/lottery_data.db"
        if not os.path.exists(db_path):
            st.error("❌ 資料庫文件不存在，請檢查數據文件路徑")
            return
        
        conn = sqlite3.connect(db_path)
        
        # 根據遊戲類型選擇表格
        table_map = {
            "威力彩": "Powercolor",
            "大樂透": "Lotto649", 
            "今彩539": "DailyCash"
        }
        
        table_name = table_map.get(game_type, "Powercolor")
        
        # 構建查詢
        query = f"SELECT * FROM {table_name}"
        params = []
        
        if period_filter:
            query += " WHERE Period LIKE ?"
            params.append(f"%{period_filter}%")
        
        query += " ORDER BY CAST(Period AS INTEGER) DESC LIMIT ?"
        params.append(limit)
        
        df = pd.read_sql_query(query, conn, params=params)
        conn.close()
        
        if df.empty:
            st.warning("📝 未找到符合條件的開獎記錄")
            return
        
        # 顯示統計摘要
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("總開獎次數", len(df))
        with col2:
            latest_period = df.iloc[0]['Period'] if not df.empty else "無"
            st.metric("最新期數", latest_period)
        with col3:
            # 計算最常出現號碼
            numbers = []
            if game_type == "威力彩":
                for _, row in df.iterrows():
                    numbers.extend([
                        row.get('Anumber1', 0), row.get('Anumber2', 0), 
                        row.get('Anumber3', 0), row.get('Anumber4', 0),
                        row.get('Anumber5', 0), row.get('Anumber6', 0)
                    ])
            elif game_type == "大樂透":
                for _, row in df.iterrows():
                    numbers.extend([
                        row.get('Anumber1', 0), row.get('Anumber2', 0), 
                        row.get('Anumber3', 0), row.get('Anumber4', 0),
                        row.get('Anumber5', 0), row.get('Anumber6', 0)
                    ])
            elif game_type == "今彩539":
                for _, row in df.iterrows():
                    numbers.extend([
                        row.get('Anumber1', 0), row.get('Anumber2', 0), 
                        row.get('Anumber3', 0), row.get('Anumber4', 0),
                        row.get('Anumber5', 0)
                    ])
            
            numbers = [n for n in numbers if n > 0]
            most_common = max(set(numbers), key=numbers.count) if numbers else "無"
            st.metric("最常出現號碼", most_common)
        
        with col4:
            date_range = f"{df.iloc[-1]['Sdate']} ~ {df.iloc[0]['Sdate']}" if len(df) > 1 else df.iloc[0]['Sdate']
            st.metric("日期範圍", date_range[:10])  # 只顯示日期部分
        
        st.markdown("---")
        
        # 開獎結果表格
        st.subheader("📋 開獎結果")
        
        # 格式化顯示
        display_df = df.copy()
        
        if game_type == "威力彩":
            # 威力彩格式化
            display_df['開獎號碼'] = display_df.apply(lambda row: 
                f"{row.get('Anumber1', '?'):02d} {row.get('Anumber2', '?'):02d} {row.get('Anumber3', '?'):02d} "
                f"{row.get('Anumber4', '?'):02d} {row.get('Anumber5', '?'):02d} {row.get('Anumber6', '?'):02d} "
                f"+ {row.get('Second_district', '?'):02d}", axis=1
            )
        
        # 選擇要顯示的欄位
        columns_to_show = ['Period', 'Sdate', '開獎號碼'] if '開獎號碼' in display_df.columns else ['Period', 'Sdate']
        display_df = display_df[columns_to_show]
        display_df.columns = ['期數', '開獎日期', '開獎號碼'] if '開獎號碼' in display_df.columns else ['期數', '開獎日期']
        
        st.dataframe(display_df, use_container_width=True)
        
        # 下載功能
        csv = display_df.to_csv(index=False, encoding='utf-8-sig')
        st.download_button(
            label="💾 下載開獎結果",
            data=csv,
            file_name=f"{game_type}_results_{datetime.now().strftime('%Y%m%d')}.csv",
            mime="text/csv"
        )
        
    except Exception as e:
        st.error(f"❌ 查詢失敗: {str(e)}")


def show_backtest_analysis_page():
    """回測分析系統頁面"""
    st.header("📊 回測分析系統")
    
    # 子頁面選單
    subtab1, subtab2, subtab3 = st.tabs([
        "🔄 執行回測", 
        "📚 歷史記錄", 
        "📈 結果分析"
    ])
    
    with subtab1:
        show_backtest_execution()
    
    with subtab2:
        show_backtest_history()
    
    with subtab3:
        show_backtest_results_analysis()


def show_backtest_execution():
    """回測執行界面"""
    st.subheader("🔄 執行新回測")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### ⚙️ 基本設定")
        
        # 遊戲類型選擇
        game_options = {
            "威力彩": GameType.POWERCOLOR,
            "大樂透": GameType.LOTTO649,
            "今彩539": GameType.DAILYCASH
        }
        game_choice = st.selectbox("🎮 選擇遊戲類型", list(game_options.keys()))
        game_type = game_options[game_choice]
        
        # 預測方法選擇
        method_options = {
            "頻率分析": PredictionMethod.FREQUENCY_ANALYSIS,
            "模式分析": PredictionMethod.PATTERN_ANALYSIS,
            "連號分析": PredictionMethod.CONSECUTIVE_ANALYSIS,
            "隨機基準": PredictionMethod.RANDOM_BASELINE
        }
        method_choice = st.selectbox("🔍 選擇預測方法", list(method_options.keys()))
        method = method_options[method_choice]
        
    with col2:
        st.markdown("### 📅 期間設定")
        
        # 期間選擇
        start_period = st.text_input(
            "開始期數", 
            value="114000030",
            help="例如: 114000030 表示民國114年第30期"
        )
        end_period = st.text_input(
            "結束期數", 
            value="114000058",
            help="例如: 114000058 表示民國114年第58期"
        )
        
        # 訓練窗口
        training_window = st.slider(
            "🔧 訓練窗口大小", 
            min_value=5, 
            max_value=50, 
            value=20,
            help="用於預測的歷史期數"
        )
    
    # 備註
    notes = st.text_area("📝 備註 (可選)", height=80)
    
    # 執行按鈕
    if st.button("🚀 開始回測", type="primary", use_container_width=True):
        execute_backtest_analysis(game_type, method, start_period, end_period, training_window, notes)


def execute_backtest_analysis(game_type, method, start_period, end_period, training_window, notes):
    """執行回測分析"""
    with st.spinner("🔄 執行回測中..."):
        try:
            # 創建配置
            config = BacktestConfig(
                game_type=game_type,
                method=method,
                start_period=start_period,
                end_period=end_period,
                training_window=training_window
            )
            
            # 執行回測
            backtester = BatchBacktester(config)
            results = backtester.run_backtest()
            
            if "error" in results:
                st.error(f"❌ 回測失敗: {results['error']}")
                return
            
            # 保存結果
            filepath = backtester.save_results()
            
            # 記錄到歷史
            st.session_state.backtest_manager._save_to_history(results, filepath, notes)
            
            # 儲存到session state
            st.session_state.current_results = results
            
            st.success("✅ 回測執行完成！")
            
            # 顯示結果摘要
            display_backtest_summary(results)
            
        except Exception as e:
            st.error(f"❌ 執行過程中發生錯誤: {str(e)}")


def display_backtest_summary(results):
    """顯示回測結果摘要"""
    st.subheader("📊 回測結果摘要")
    
    config = results['config']
    stats = results['statistics']
    
    # 基本統計指標
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("總預測數", stats['total_periods'])
    with col2:
        st.metric("整體準確度", f"{stats['accuracy']:.2f}%")
    with col3:
        st.metric("最佳預測", f"{stats['total_matches']} 個")
    with col4:
        st.metric("中獎率", f"{stats['winning_rate']:.2f}%")
    
    # 命中分佈圖
    match_dist = results.get('match_distribution', {})
    if match_dist:
        st.subheader("🎯 命中分佈")
        
        matches = []
        counts = []
        for i in range(7):
            key = f'matches_{i}'
            if key in match_dist and match_dist[key] > 0:
                matches.append(f"{i}個命中")
                counts.append(match_dist[key])
        
        if matches:
            fig = px.pie(values=counts, names=matches, title="命中分佈統計")
            st.plotly_chart(fig, use_container_width=True)


def show_backtest_history():
    """顯示回測歷史"""
    st.subheader("📚 回測歷史記錄")
    
    # 載入歷史數據
    try:
        conn = sqlite3.connect(st.session_state.backtest_manager.history_db)
        df = pd.read_sql_query(
            "SELECT * FROM backtest_history ORDER BY created_at DESC LIMIT 50",
            conn
        )
        conn.close()
        
        if df.empty:
            st.info("📝 暫無回測歷史記錄")
            return
        
        # 統計卡片
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("總記錄數", len(df))
        with col2:
            st.metric("平均準確率", f"{df['accuracy'].mean():.2f}%")
        with col3:
            st.metric("平均中獎率", f"{df['winning_rate'].mean():.2f}%")
        with col4:
            st.metric("最高準確率", f"{df['accuracy'].max():.2f}%")
        
        # 歷史記錄表格
        display_df = df[[
            'id', 'timestamp', 'game_type', 'method', 
            'total_periods', 'accuracy', 'winning_rate', 'notes'
        ]].copy()
        
        display_df.columns = [
            'ID', '時間戳', '遊戲類型', '預測方法',
            '總期數', '準確率(%)', '中獎率(%)', '備註'
        ]
        
        st.dataframe(display_df, use_container_width=True)
        
        # 載入詳細結果功能
        selected_id = st.number_input("選擇記錄ID查看詳細結果", min_value=1, max_value=int(df['id'].max()) if not df.empty else 1)
        
        if st.button("📊 載入詳細結果"):
            detailed_result = st.session_state.backtest_manager.load_result_detail(selected_id)
            if detailed_result:
                st.session_state.current_results = detailed_result
                st.success(f"✅ 已載入記錄 #{selected_id} 的詳細結果")
                st.rerun()
        
    except Exception as e:
        st.error(f"❌ 載入歷史記錄失敗: {str(e)}")


def show_backtest_results_analysis():
    """顯示回測結果分析"""
    st.subheader("📈 結果分析")
    
    if st.session_state.current_results is None:
        st.info("📊 請先執行回測或載入歷史結果")
        return
    
    results = st.session_state.current_results
    config = results['config']
    stats = results['statistics']
    
    # 基本資訊
    st.markdown("### 📋 回測基本資訊")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.info(f"**遊戲類型**\n{config['game_type']}")
    with col2:
        st.info(f"**預測方法**\n{config['method']}")
    with col3:
        st.info(f"**測試期間**\n{config['test_period']}")
    with col4:
        st.info(f"**訓練窗口**\n{config['training_window']}期")
    
    # 詳細分析圖表
    detailed_results = results.get('results', [])
    if detailed_results:
        col1, col2 = st.columns(2)
        
        with col1:
            # 命中趨勢圖
            periods = [r['period'] for r in detailed_results]
            matches = [r['matches'] for r in detailed_results]
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=periods,
                y=matches,
                mode='lines+markers',
                name='命中數',
                line=dict(color='blue')
            ))
            
            fig.update_layout(
                title="命中數趨勢",
                xaxis_title="期數",
                yaxis_title="命中數"
            )
            
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # 信心度分析
            confidence = [r['confidence'] for r in detailed_results]
            fig = px.histogram(
                x=confidence,
                nbins=10,
                title="信心度分佈",
                labels={'x': '信心度(%)', 'y': '頻次'}
            )
            st.plotly_chart(fig, use_container_width=True)


def show_prediction_analysis_page():
    """預測方法分析頁面"""
    st.header("🔍 預測方法分析")
    
    st.markdown("### 📊 方法比較分析")
    st.info("💡 這個功能將同時測試多種預測方法，並比較它們的表現")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("⚙️ 分析設置")
        
        # 遊戲類型
        game_options = {
            "威力彩": GameType.POWERCOLOR,
            "大樂透": GameType.LOTTO649,
            "今彩539": GameType.DAILYCASH
        }
        game_choice = st.selectbox("彩票類型", list(game_options.keys()))
        game_type = game_options[game_choice]
        
    with col2:
        st.subheader("📅 分析時間範圍")
        
        start_period = st.text_input("開始期數", value="114000030")
        end_period = st.text_input("結束期數", value="114000058")
        training_window = st.slider("訓練窗口", 5, 50, 20)
    
    if st.button("📊 開始分析", type="primary", use_container_width=True):
        run_prediction_method_analysis(game_type, start_period, end_period, training_window)


def run_prediction_method_analysis(game_type, start_period, end_period, training_window):
    """執行預測方法分析"""
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    methods = [
        ("頻率分析", PredictionMethod.FREQUENCY_ANALYSIS),
        ("模式分析", PredictionMethod.PATTERN_ANALYSIS),
        ("連號分析", PredictionMethod.CONSECUTIVE_ANALYSIS),
        ("隨機基準", PredictionMethod.RANDOM_BASELINE)
    ]
    
    analysis_results = []
    
    status_text.text("🔄 正在分析預測方法效果...")
    
    for i, (method_name, method) in enumerate(methods):
        status_text.text(f"🔄 測試 {method_name}...")
        progress_bar.progress((i + 1) / len(methods))
        
        try:
            config = BacktestConfig(
                game_type=game_type,
                method=method,
                start_period=start_period,
                end_period=end_period,
                training_window=training_window
            )
            
            backtester = BatchBacktester(config)
            results = backtester.run_backtest()
            
            if "error" not in results:
                stats = results['statistics']
                analysis_results.append({
                    '方法': method_name,
                    '總期數': stats['total_periods'],
                    '準確率(%)': stats['accuracy'],
                    '中獎率(%)': stats['winning_rate'],
                    '平均命中數': stats['average_matches_per_period'],
                    '平均信心度(%)': stats['average_confidence']
                })
                
        except Exception as e:
            st.warning(f"⚠️ {method_name} 分析失敗: {str(e)}")
    
    status_text.text("✅ 分析完成！")
    
    if analysis_results:
        # 顯示分析結果
        st.subheader("📊 方法分析結果")
        df = pd.DataFrame(analysis_results)
        st.dataframe(df, use_container_width=True)
        
        # 比較圖表
        col1, col2 = st.columns(2)
        
        with col1:
            fig = px.bar(
                df, 
                x='方法', 
                y='準確率(%)',
                title="準確率比較",
                color='準確率(%)',
                color_continuous_scale='viridis'
            )
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            fig = px.bar(
                df, 
                x='方法', 
                y='中獎率(%)',
                title="中獎率比較", 
                color='中獎率(%)',
                color_continuous_scale='plasma'
            )
            st.plotly_chart(fig, use_container_width=True)
        
        # 推薦結果
        best_accuracy = df.loc[df['準確率(%)'].idxmax()]
        best_winning = df.loc[df['中獎率(%)'].idxmax()]
        
        st.subheader("🏅 分析結論")
        col1, col2 = st.columns(2)
        
        with col1:
            st.success(f"**最佳準確率方法**\n{best_accuracy['方法']} ({best_accuracy['準確率(%)']:.2f}%)")
        
        with col2:
            st.success(f"**最佳中獎率方法**\n{best_winning['方法']} ({best_winning['中獎率(%)']:.2f}%)")
    else:
        st.error("❌ 分析失敗，請檢查參數設定")


def show_system_management_page():
    """系統管理頁面"""
    st.header("📈 系統管理")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📊 系統統計")
        
        try:
            conn = sqlite3.connect(st.session_state.backtest_manager.history_db)
            stats_df = pd.read_sql_query(
                "SELECT COUNT(*) as total, game_type FROM backtest_history GROUP BY game_type",
                conn
            )
            conn.close()
            
            if not stats_df.empty:
                for _, row in stats_df.iterrows():
                    st.metric(f"{row['game_type']} 回測次數", row['total'])
            else:
                st.info("暫無統計數據")
        except:
            st.info("統計數據載入中...")
    
    with col2:
        st.subheader("🗂️ 檔案管理")
        
        results_dir = st.session_state.backtest_manager.results_dir
        if os.path.exists(results_dir):
            json_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
            st.metric("結果檔案數量", len(json_files))
            
            total_size = sum(
                os.path.getsize(os.path.join(results_dir, f)) 
                for f in json_files
            )
            st.metric("總佔用空間", f"{total_size / 1024 / 1024:.2f} MB")
    
    # 系統維護
    st.markdown("---")
    st.subheader("🧹 系統維護")
    
    days = st.slider("保留最近幾天的檔案", 1, 30, 7)
    
    if st.button("🗑️ 清理舊檔案", type="secondary"):
        try:
            if os.path.exists(results_dir):
                result_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
                cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)
                
                deleted_count = 0
                for filename in result_files:
                    filepath = os.path.join(results_dir, filename)
                    if os.path.getmtime(filepath) < cutoff_time:
                        os.remove(filepath)
                        deleted_count += 1
                
                st.success(f"✅ 已清理 {deleted_count} 個舊檔案")
            else:
                st.info("📁 結果目錄不存在")
        except Exception as e:
            st.error(f"❌ 清理失敗: {str(e)}")


if __name__ == "__main__":
    main()