import sqlite3
import logging
from datetime import datetime, timedelta
import time
import traceback
import os
import json
from typing import Dict, List, Any, Tuple

# 嘗試導入 TaiwanLottery 模組
try:
    from TaiwanLottery import TaiwanLotteryCrawler
except ImportError:
    # 如果導入失敗，創建一個模擬的爬蟲類，以便程式還能部分運行
    class TaiwanLotteryCrawler:
        def __init__(self):
            logging.warning("TaiwanLotteryCrawler 模組無法導入，使用模擬資料")
        
        def super_lotto(self):
            return []
            
        def lotto649(self):
            return []
            
        def daily_cash(self):
            return []

# 設置日誌記錄
if not os.path.exists('logs'):
    os.makedirs('logs')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/lottery_daily_updater_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('lottery_daily_updater')

def create_connection():
    """建立 SQLite 資料庫連接"""
    # 指定固定的絕對路徑，放在data子目錄下
    base_dir = "/Users/<USER>/python/training/lotto/lottery_prediction_system/"
    db_path = os.path.join(base_dir, "data", "lottery_data.db")
    
    try:
        # 確保資料庫目錄存在
        db_dir = os.path.dirname(db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
            
        return sqlite3.connect(db_path)
    except sqlite3.Error as e:
        logger.error(f"資料庫連接錯誤: {e}")
        raise
    
def ensure_tables_exist():
    """確保所有需要的資料表與欄位都存在"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 檢查表格是否存在，如不存在則創建
        for table_name, columns in {
            'Powercolor': [
                ('ID', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                ('gameKind', 'TEXT'),
                ('Period', 'TEXT'),
                ('Sdate', 'TEXT'),
                ('Anumber1', 'TEXT'),
                ('Anumber2', 'TEXT'),
                ('Anumber3', 'TEXT'),
                ('Anumber4', 'TEXT'),
                ('Anumber5', 'TEXT'),
                ('Anumber6', 'TEXT'),
                ('Second_district', 'TEXT')
            ],
            'Lotto649': [
                ('ID', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                ('gameKind', 'TEXT'),
                ('Period', 'TEXT'),
                ('Sdate', 'TEXT'),
                ('Anumber1', 'TEXT'),
                ('Anumber2', 'TEXT'),
                ('Anumber3', 'TEXT'),
                ('Anumber4', 'TEXT'),
                ('Anumber5', 'TEXT'),
                ('Anumber6', 'TEXT'),
                ('SpecialNumber', 'TEXT')
            ],
            'DailyCash': [
                ('ID', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                ('gameKind', 'TEXT'),
                ('Period', 'TEXT'),
                ('Sdate', 'TEXT'),
                ('Anumber1', 'TEXT'),
                ('Anumber2', 'TEXT'),
                ('Anumber3', 'TEXT'),
                ('Anumber4', 'TEXT'),
                ('Anumber5', 'TEXT')
            ],
            'LotteryUpdateLog': [
                ('ID', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                ('LotteryType', 'TEXT'),
                ('LastUpdateTime', 'TEXT')
            ]
        }.items():
            # 檢查表格是否存在
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            table_exists = cursor.fetchone() is not None
            
            if not table_exists:
                # 創建表格
                columns_sql = ', '.join([f'{col[0]} {col[1]}' for col in columns])
                cursor.execute(f"CREATE TABLE {table_name} ({columns_sql})")
                logger.info(f"創建 {table_name} 表")
            else:
                # 檢查現有表的欄位並添加缺少的欄位
                cursor.execute(f"PRAGMA table_info({table_name})")
                existing_columns = [row[1] for row in cursor.fetchall()]
                
                for col_name, col_type in columns:
                    if col_name not in existing_columns:
                        try:
                            cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {col_name} {col_type}")
                            logger.info(f"在 {table_name} 表中添加 {col_name} 欄位")
                        except Exception as e:
                            logger.warning(f"添加欄位時出錯: {str(e)}")
        
        conn.commit()
        logger.info("已確認所有必要的資料表與欄位存在")
    except Exception as e:
        logger.error(f"確認資料表時出錯: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'conn' in locals():
            conn.close()

def get_last_update_date(lottery_type):
    """獲取特定彩券類型的最後更新日期"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 檢查是否有更新記錄
        cursor.execute("SELECT LastUpdateTime FROM LotteryUpdateLog WHERE LotteryType = ?", (lottery_type,))
        result = cursor.fetchone()
        
        if result:
            # 確保返回日期對象
            last_update = result[0]
            if isinstance(last_update, str):
                # 如果是字符串，解析為日期
                try:
                    return datetime.strptime(last_update.split()[0], '%Y-%m-%d').date()
                except ValueError:
                    logger.error(f"無法解析日期字符串: {last_update}")
                    return (datetime.now() - timedelta(days=30)).date()
            else:
                # 如果已經是datetime對象，只取日期部分
                try:
                    return last_update.date()
                except AttributeError:
                    # 如果沒有.date()方法，可能已經是日期對象
                    if isinstance(last_update, datetime.date):
                        return last_update
                    else:
                        logger.error(f"無法從 {type(last_update)} 轉換為日期")
                        return (datetime.now() - timedelta(days=30)).date()
        else:
            # 如果沒有記錄，嘗試從彩券資料表中找到最新的日期
            table_map = {
                '威力彩': 'Powercolor',
                '大樂透': 'Lotto649',
                '今彩539': 'DailyCash'
            }
            
            table_name = table_map.get(lottery_type)
            if table_name:
                cursor.execute(f"SELECT MAX(Sdate) FROM {table_name}")
                max_date = cursor.fetchone()[0]
                
                if max_date:
                    # 如果有資料，返回最後日期
                    if isinstance(max_date, str):
                        return datetime.strptime(max_date.split()[0], '%Y-%m-%d').date()
                    else:
                        try:
                            return max_date.date()
                        except AttributeError:
                            if isinstance(max_date, datetime.date):
                                return max_date
            
            # 如果沒有任何記錄，返回一個月前的日期作為預設值
            return (datetime.now() - timedelta(days=30)).date()
    except Exception as e:
        logger.error(f"獲取 {lottery_type} 最後更新日期時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        # 如果出錯，返回一個月前的日期
        return (datetime.now() - timedelta(days=30)).date()
    finally:
        if 'conn' in locals():
            conn.close()

def update_last_update_date(lottery_type):
    """更新特定彩券類型的最後更新時間"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 檢查是否有更新記錄
        cursor.execute("SELECT COUNT(*) FROM LotteryUpdateLog WHERE LotteryType = ?", (lottery_type,))
        count = cursor.fetchone()[0]
        
        if count > 0:
            # 更新現有記錄
            cursor.execute("UPDATE LotteryUpdateLog SET LastUpdateTime = ? WHERE LotteryType = ?", 
                         (now, lottery_type))
        else:
            # 插入新記錄
            cursor.execute("INSERT INTO LotteryUpdateLog (LotteryType, LastUpdateTime) VALUES (?, ?)", 
                          (lottery_type, now))
        
        conn.commit()
        logger.info(f"已更新 {lottery_type} 的最後更新時間為 {now}")
    except Exception as e:
        logger.error(f"更新 {lottery_type} 最後更新時間時出錯: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'conn' in locals():
            conn.close()

def period_exists(cursor, table, period):
    """檢查期數是否已存在於資料庫中"""
    query = f'SELECT COUNT(*) FROM {table} WHERE Period = ?'
    cursor.execute(query, (period,))
    count = cursor.fetchone()[0]
    return count > 0

def save_powercolor_data(cursor, data):
    """儲存威力彩資料到資料庫"""
    inserted = 0
    skipped = 0
    
    for draw in data:
        try:
            period = draw['期別']
            
            # 跳過已存在的期數
            if period_exists(cursor, 'Powercolor', period):
                logger.info(f"威力彩期數 {period} 已存在，跳過")
                skipped += 1
                continue
                
            # 準備插入資料
            date = draw['開獎日期'].split('T')[0]  # 僅保留日期部分
            numbers = draw['第一區']
            second_district = draw['第二區']
            
            # 插入資料
            insert_query = '''
            INSERT INTO Powercolor (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, Second_district)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            
            cursor.execute(insert_query, (
                '威力彩',
                str(period),
                date,
                str(numbers[0]),
                str(numbers[1]),
                str(numbers[2]),
                str(numbers[3]),
                str(numbers[4]),
                str(numbers[5]),
                str(second_district)
            ))
            
            inserted += 1
            logger.info(f"成功插入威力彩期數 {period} 的資料")
        except Exception as e:
            logger.error(f"處理威力彩資料時出錯: {str(e)}")
    
    return inserted, skipped

def save_lotto649_data(cursor, data):
    """儲存大樂透資料到資料庫"""
    inserted = 0
    skipped = 0
    
    for draw in data:
        try:
            period = draw['期別']
            
            # 跳過已存在的期數
            if period_exists(cursor, 'Lotto649', period):
                logger.info(f"大樂透期數 {period} 已存在，跳過")
                skipped += 1
                continue
                
            # 準備插入資料
            date = draw['開獎日期'].split('T')[0]  # 僅保留日期部分
            numbers = draw['獎號']
            special_number = draw.get('特別號', '')
            
            # 插入資料
            insert_query = '''
            INSERT INTO Lotto649 (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, SpecialNumber)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            
            cursor.execute(insert_query, (
                '大樂透',
                str(period),
                date,
                str(numbers[0]),
                str(numbers[1]),
                str(numbers[2]),
                str(numbers[3]),
                str(numbers[4]),
                str(numbers[5]),
                str(special_number)
            ))
            
            inserted += 1
            logger.info(f"成功插入大樂透期數 {period} 的資料")
        except Exception as e:
            logger.error(f"處理大樂透資料時出錯: {str(e)}")
    
    return inserted, skipped

def save_daily_cash_data(cursor, data):
    """儲存今彩539資料到資料庫"""
    inserted = 0
    skipped = 0
    
    for draw in data:
        try:
            period = draw['期別']
            
            # 跳過已存在的期數
            if period_exists(cursor, 'DailyCash', period):
                logger.info(f"今彩539期數 {period} 已存在，跳過")
                skipped += 1
                continue
                
            # 準備插入資料
            date = draw['開獎日期'].split('T')[0]  # 僅保留日期部分
            numbers = draw['獎號']
            
            # 插入資料
            insert_query = '''
            INSERT INTO DailyCash (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            '''
            
            cursor.execute(insert_query, (
                '今彩539',
                str(period),
                date,
                str(numbers[0]),
                str(numbers[1]),
                str(numbers[2]),
                str(numbers[3]),
                str(numbers[4])
            ))
            
            inserted += 1
            logger.info(f"成功插入今彩539期數 {period} 的資料")
        except Exception as e:
            logger.error(f"處理今彩539資料時出錯: {str(e)}")
    
    return inserted, skipped

def download_latest_data(days_lookback=7):
    """下載最近幾天的彩券資料"""
    # 初始化彩券爬蟲
    try:
        lottery = TaiwanLotteryCrawler()
    except Exception as e:
        logger.error(f"初始化彩券爬蟲時出錯: {str(e)}")
        # 返回空結果
        return {'powercolor': 0, 'lotto649': 0, 'daily_cash': 0}
    
    # 獲取當前日期
    current_date = datetime.now()
    
    # 確保資料表存在
    ensure_tables_exist()
    
    # 獲取每種彩券的最後更新日期
    powercolor_last_update = get_last_update_date('威力彩')
    lotto649_last_update = get_last_update_date('大樂透')
    daily_cash_last_update = get_last_update_date('今彩539')
    
    logger.info(f"威力彩最後更新日期: {powercolor_last_update}")
    logger.info(f"大樂透最後更新日期: {lotto649_last_update}")
    logger.info(f"今彩539最後更新日期: {daily_cash_last_update}")
    
    # 決定查詢的起始日期（最後更新日期或指定天數之前）
    lookback_date = (current_date - timedelta(days=days_lookback)).date()
    
    # 初始化總計
    total_inserted = {
        'powercolor': 0,
        'lotto649': 0,
        'daily_cash': 0
    }
    
    # 建立資料庫連接
    conn = create_connection()
    cursor = conn.cursor()
    
    try:
        # 下載並儲存威力彩資料
        try:
            logger.info(f"正在下載最新的威力彩資料...")
            # 使用空參數獲取最新資料
            powercolor_data = lottery.super_lotto()
            inserted, skipped = save_powercolor_data(cursor, powercolor_data)
            total_inserted['powercolor'] += inserted
            logger.info(f"威力彩資料處理完成: 新增 {inserted} 筆, 跳過 {skipped} 筆")
            if inserted > 0:
                update_last_update_date('威力彩')
            time.sleep(1)  # 短暫休息避免過於頻繁請求
        except Exception as e:
            logger.error(f"下載或儲存威力彩資料時出錯: {str(e)}")
            logger.error(traceback.format_exc())
        
        # 下載並儲存大樂透資料
        try:
            logger.info(f"正在下載最新的大樂透資料...")
            lotto649_data = lottery.lotto649()
            inserted, skipped = save_lotto649_data(cursor, lotto649_data)
            total_inserted['lotto649'] += inserted
            logger.info(f"大樂透資料處理完成: 新增 {inserted} 筆, 跳過 {skipped} 筆")
            if inserted > 0:
                update_last_update_date('大樂透')
            time.sleep(1)  # 短暫休息
        except Exception as e:
            logger.error(f"下載或儲存大樂透資料時出錯: {str(e)}")
            logger.error(traceback.format_exc())
        
        # 下載並儲存今彩539資料
        try:
            logger.info(f"正在下載最新的今彩539資料...")
            daily_cash_data = lottery.daily_cash()
            inserted, skipped = save_daily_cash_data(cursor, daily_cash_data)
            total_inserted['daily_cash'] += inserted
            logger.info(f"今彩539資料處理完成: 新增 {inserted} 筆, 跳過 {skipped} 筆")
            if inserted > 0:
                update_last_update_date('今彩539')
            time.sleep(1)  # 短暫休息
        except Exception as e:
            logger.error(f"下載或儲存今彩539資料時出錯: {str(e)}")
            logger.error(traceback.format_exc())
        
        # 提交所有更改
        conn.commit()
        logger.info(f"已提交所有彩券資料更新")
        
        return total_inserted
        
    except Exception as e:
        logger.error(f"下載彩券資料時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        conn.rollback()
        return total_inserted
    finally:
        conn.close()

def main():
    """主函數"""
    start_time = datetime.now()
    logger.info("開始每日彩券資料更新程序...")
    
    try:
        # 默認回溯7天，確保不會漏掉資料
        total_records = download_latest_data(days_lookback=7)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        summary = f"""
        更新完成! 總共新增:
        威力彩: {total_records['powercolor']} 筆記錄
        大樂透: {total_records['lotto649']} 筆記錄
        今彩539: {total_records['daily_cash']} 筆記錄
        總耗時: {duration}
        """
        
        logger.info(summary)
        print(summary)
        
    except Exception as e:
        logger.error(f"主程序執行錯誤: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"更新過程中出現錯誤，請查閱日誌檔案以獲取詳細信息。")

if __name__ == "__main__":
    main()