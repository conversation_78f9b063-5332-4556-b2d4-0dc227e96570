#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import DBManager
import pandas as pd
from collections import Counter
import json
from datetime import datetime, <PERSON><PERSON><PERSON>

def analyze_real_data():
    """分析真實彩票數據"""
    print("=" * 60)
    print("彩票真實數據分析報告")
    print("=" * 60)
    
    db_manager = DBManager()
    
    # 分析各種彩票類型
    lottery_types = {
        'powercolor': '威力彩',
        'lotto649': '大樂透', 
        'dailycash': '今彩539'
    }
    
    analysis_results = {}
    
    for lottery_type, display_name in lottery_types.items():
        print(f"\n📊 {display_name} 數據分析")
        print("-" * 40)
        
        try:
            # 加載數據
            data = db_manager.load_lottery_data(lottery_type)
            if data is None or len(data) == 0:
                print(f"❌ 沒有 {display_name} 數據")
                continue
                
            # 基本統計
            total_draws = len(data)
            date_start = data['Sdate'].min()
            date_end = data['Sdate'].max()
            
            print(f"📈 總開獎次數: {total_draws:,} 次")
            print(f"📅 數據期間: {date_start} 至 {date_end}")
            
            # 最新開獎
            latest = data.iloc[-1]
            print(f"🎯 最新開獎 (期號 {latest['Period']}):")
            
            if lottery_type in ['powercolor', 'lotto649']:
                numbers = [latest[f'Anumber{i}'] for i in range(1, 7)]
                special = latest['Snumber']
                print(f"   號碼: {'-'.join(map(str, numbers))} + 特別號: {special}")
                
                # 號碼頻率分析
                all_numbers = []
                for i in range(1, 7):
                    all_numbers.extend(data[f'Anumber{i}'].tolist())
                    
                freq = Counter(all_numbers)
                hot_numbers = freq.most_common(10)
                cold_numbers = freq.most_common()[-10:]
                
                print(f"🔥 熱門號碼 (前10): {', '.join([f'{num}({count}次)' for num, count in hot_numbers])}")
                print(f"❄️  冷門號碼 (後10): {', '.join([f'{num}({count}次)' for num, count in reversed(cold_numbers)])}")
                
                # 特別號分析
                special_freq = Counter(data['Snumber'].tolist())
                hot_special = special_freq.most_common(5)
                print(f"⭐ 熱門特別號 (前5): {', '.join([f'{num}({count}次)' for num, count in hot_special])}")
                
            elif lottery_type == 'dailycash':
                numbers = [latest[f'Anumber{i}'] for i in range(1, 6)]
                print(f"   號碼: {'-'.join(map(str, numbers))}")
                
                # 號碼頻率分析
                all_numbers = []
                for i in range(1, 6):
                    all_numbers.extend(data[f'Anumber{i}'].tolist())
                    
                freq = Counter(all_numbers)
                hot_numbers = freq.most_common(10)
                cold_numbers = freq.most_common()[-10:]
                
                print(f"🔥 熱門號碼 (前10): {', '.join([f'{num}({count}次)' for num, count in hot_numbers])}")
                print(f"❄️  冷門號碼 (後10): {', '.join([f'{num}({count}次)' for num, count in reversed(cold_numbers)])}")
            
            # 近期趨勢分析 (最近30期)
            recent_data = data.tail(30)
            print(f"\n📊 近期趨勢分析 (最近30期):")
            
            if lottery_type in ['powercolor', 'lotto649']:
                recent_numbers = []
                for i in range(1, 7):
                    recent_numbers.extend(recent_data[f'Anumber{i}'].tolist())
                recent_freq = Counter(recent_numbers)
                recent_hot = recent_freq.most_common(5)
                print(f"   近期熱門: {', '.join([f'{num}({count}次)' for num, count in recent_hot])}")
                
            elif lottery_type == 'dailycash':
                recent_numbers = []
                for i in range(1, 6):
                    recent_numbers.extend(recent_data[f'Anumber{i}'].tolist())
                recent_freq = Counter(recent_numbers)
                recent_hot = recent_freq.most_common(5)
                print(f"   近期熱門: {', '.join([f'{num}({count}次)' for num, count in recent_hot])}")
            
            # 保存分析結果
            analysis_results[lottery_type] = {
                'display_name': display_name,
                'total_draws': total_draws,
                'date_range': f"{date_start} 至 {date_end}",
                'latest_result': {
                    'period': latest['Period'],
                    'date': str(latest['Sdate']),
                    'numbers': numbers if lottery_type == 'dailycash' else numbers + [special]
                },
                'hot_numbers': hot_numbers[:5],
                'cold_numbers': cold_numbers[-5:],
                'recent_hot': recent_hot
            }
            
        except Exception as e:
            print(f"❌ 分析 {display_name} 時發生錯誤: {str(e)}")
    
    # 生成投資建議
    print("\n" + "=" * 60)
    print("💡 基於真實數據的投資分析建議")
    print("=" * 60)
    
    print("\n✅ 數據可信度評估:")
    print("   • 威力彩: 1,203筆歷史記錄 (2014-2025) - 數據充足")
    print("   • 大樂透: 1,299筆歷史記錄 (2014-2025) - 數據充足")
    print("   • 今彩539: 3,613筆歷史記錄 (2014-2025) - 數據豐富")
    
    print("\n📈 統計學觀點:")
    print("   • 熱門號碼確實存在統計差異，但不代表未來趨勢")
    print("   • 冷門號碼理論上有'回歸平均'的可能性")
    print("   • 近期趨勢可能反映機器或球的微小偏差")
    
    print("\n⚠️  投資風險提醒:")
    print("   • 彩票本質仍是隨機事件，過往數據不保證未來結果")
    print("   • 任何'預測'都只是統計推測，不應作為投資決策依據")
    print("   • 建議將彩票視為娛樂，而非投資工具")
    
    print("\n🎯 如果要參考數據:")
    print("   • 可考慮熱門號碼的組合策略")
    print("   • 注意冷門號碼的潛在機會")
    print("   • 分散投注，避免單一策略")
    print("   • 設定預算上限，理性參與")
    
    # 保存分析報告
    report_file = 'real_data_analysis_report.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n📄 詳細分析報告已保存至: {report_file}")
    
    return analysis_results

if __name__ == "__main__":
    try:
        results = analyze_real_data()
        print("\n🎉 真實數據分析完成！")
    except Exception as e:
        print(f"\n❌ 分析過程發生錯誤: {str(e)}")
        import traceback
        traceback.print_exc()