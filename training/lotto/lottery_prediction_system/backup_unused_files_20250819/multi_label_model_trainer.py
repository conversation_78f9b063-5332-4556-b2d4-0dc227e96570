"""
改進的彩票預測系統 - 多標籤模型訓練模組
使用多標籤分類方法，同時考慮所有可能的號碼，支援威力彩、大樂透和今彩539
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.multioutput import MultiOutputClassifier
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, f1_score
import matplotlib.pyplot as plt
import os
import pickle
import logging
import time
import json

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/multi_label_model.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('multi_label_model')

class MultiLabelModelTrainer:
    def __init__(self, model_dir='models'):
        """初始化多標籤模型訓練器"""
        self.model_dir = model_dir
        self.models = {
            'powercolor': None,  # 威力彩模型
            'lotto649': None,    # 大樂透模型
            'dailycash': None    # 今彩539模型
        }
        self.scalers = {}
        self.model_info = {}
        
        # 確保模型目錄存在
        if not os.path.exists(model_dir):
            os.makedirs(model_dir)
    
    def prepare_target_data(self, df, lottery_type):
        """準備多標籤目標數據
        
        Args:
            df: 特徵DataFrame
            lottery_type: 彩票類型
        
        Returns:
            目標矩陣 (samples x total_numbers)
        """
        # 設定各彩種的號碼範圍
        number_ranges = {
            'powercolor': {'first_area': 38, 'second_area': 8},
            'lotto649': {'first_area': 49, 'special': 49},
            'dailycash': {'numbers': 39}
        }
        
        # 提取目標列
        if lottery_type == 'powercolor':
            # 威力彩: 一區(6個號碼) + 二區(1個號碼)
            first_area_cols = ['Target_A1', 'Target_A2', 'Target_A3', 'Target_A4', 'Target_A5', 'Target_A6']
            second_area_col = 'Target_S'
            
            # 創建多標籤目標矩陣 (samples x 38) 用於第一區
            first_area_target = np.zeros((len(df), number_ranges[lottery_type]['first_area']))
            
            # 填充目標矩陣
            for idx, row in df.iterrows():
                for col in first_area_cols:
                    # 將每個號碼標記為1 (注意索引從0開始，號碼從1開始)
                    number = int(row[col])
                    if 1 <= number <= number_ranges[lottery_type]['first_area']:
                        first_area_target[idx, number-1] = 1
            
            # 創建多標籤目標矩陣 (samples x 8) 用於第二區
            second_area_target = np.zeros((len(df), number_ranges[lottery_type]['second_area']))
            
            # 填充第二區目標矩陣
            for idx, row in df.iterrows():
                number = int(row[second_area_col])
                if 1 <= number <= number_ranges[lottery_type]['second_area']:
                    second_area_target[idx, number-1] = 1
            
            return {
                'first_area': first_area_target,
                'second_area': second_area_target
            }
            
        elif lottery_type == 'lotto649':
            # 大樂透: 第一區(6個號碼) + 特別號(1個號碼)
            first_area_cols = ['Target_A1', 'Target_A2', 'Target_A3', 'Target_A4', 'Target_A5', 'Target_A6']
            special_col = 'Target_Special'
            
            # 創建多標籤目標矩陣 (samples x 49) 用於第一區
            first_area_target = np.zeros((len(df), number_ranges[lottery_type]['first_area']))
            
            # 填充目標矩陣
            for idx, row in df.iterrows():
                for col in first_area_cols:
                    number = int(row[col])
                    if 1 <= number <= number_ranges[lottery_type]['first_area']:
                        first_area_target[idx, number-1] = 1
            
            # 創建多標籤目標矩陣 (samples x 49) 用於特別號
            special_target = np.zeros((len(df), number_ranges[lottery_type]['special']))
            
            # 填充特別號目標矩陣
            for idx, row in df.iterrows():
                number = int(row[special_col])
                if 1 <= number <= number_ranges[lottery_type]['special']:
                    special_target[idx, number-1] = 1
            
            return {
                'first_area': first_area_target,
                'special': special_target
            }
            
        elif lottery_type == 'dailycash':
            # 今彩539: 5個號碼
            number_cols = ['Target_A1', 'Target_A2', 'Target_A3', 'Target_A4', 'Target_A5']
            
            # 創建多標籤目標矩陣 (samples x 39)
            target = np.zeros((len(df), number_ranges[lottery_type]['numbers']))
            
            # 填充目標矩陣
            for idx, row in df.iterrows():
                for col in number_cols:
                    number = int(row[col])
                    if 1 <= number <= number_ranges[lottery_type]['numbers']:
                        target[idx, number-1] = 1
                        
            return {'numbers': target}
    
    def train_models(self, features_df, lottery_type='powercolor', model_type='random_forest', test_size=0.2):
        """訓練多標籤模型
        
        Args:
            features_df: 包含特徵的DataFrame
            lottery_type: 彩票類型
            model_type: 模型類型
            test_size: 測試集比例
            
        Returns:
            字典，包含模型評估結果
        """
        logger.info(f"開始訓練{self._get_lottery_name(lottery_type)}多標籤模型 (類型: {model_type})...")
        start_time = time.time()
        
        # 刪除目標列
        target_cols = []
        if lottery_type == 'powercolor':
            target_cols = ['Target_A1', 'Target_A2', 'Target_A3', 'Target_A4', 'Target_A5', 'Target_A6', 'Target_S']
        elif lottery_type == 'lotto649':
            target_cols = ['Target_A1', 'Target_A2', 'Target_A3', 'Target_A4', 'Target_A5', 'Target_A6', 'Target_Special']
        elif lottery_type == 'dailycash':
            target_cols = ['Target_A1', 'Target_A2', 'Target_A3', 'Target_A4', 'Target_A5']
        
        if 'Period' in features_df.columns:
            target_cols.append('Period')
        
        X = features_df.drop(target_cols, axis=1, errors='ignore')
        
        # 準備多標籤目標數據
        y_dict = self.prepare_target_data(features_df, lottery_type)
        
        # 標準化特徵
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        self.scalers[lottery_type] = scaler
        
        # 創建模型
        if model_type == 'random_forest':
            base_model = RandomForestClassifier(n_estimators=100, random_state=42)
        elif model_type == 'gradient_boosting':
            base_model = GradientBoostingClassifier(n_estimators=100, random_state=42)
        elif model_type == 'svm':
            base_model = SVC(probability=True, random_state=42)
        elif model_type == 'mlp':
            base_model = MLPClassifier(hidden_layer_sizes=(100, 50), max_iter=500, random_state=42)
        else:
            raise ValueError(f"不支援的模型類型: {model_type}")
        
        # 訓練模型並計算性能
        performance = {}
        
        if lottery_type == 'powercolor':
            # 訓練第一區模型
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y_dict['first_area'], test_size=test_size, random_state=42
            )
            
            logger.info("訓練威力彩第一區模型...")
            first_area_model = MultiOutputClassifier(base_model)
            first_area_model.fit(X_train, y_train)
            
            # 預測測試集
            y_pred = first_area_model.predict(X_test)
            
            # 計算性能指標
            accuracy = np.mean([accuracy_score(y_test[:, i], y_pred[:, i]) for i in range(y_test.shape[1])])
            f1 = np.mean([f1_score(y_test[:, i], y_pred[:, i], average='weighted') for i in range(y_test.shape[1])])
            
            performance['first_area'] = {
                'accuracy': accuracy,
                'f1_score': f1
            }
            
            logger.info(f"第一區模型性能 - 準確率: {accuracy:.4f}, F1分數: {f1:.4f}")
            
            # 訓練第二區模型
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y_dict['second_area'], test_size=test_size, random_state=42
            )
            
            logger.info("訓練威力彩第二區模型...")
            second_area_model = MultiOutputClassifier(base_model)
            second_area_model.fit(X_train, y_train)
            
            # 預測測試集
            y_pred = second_area_model.predict(X_test)
            
            # 計算性能指標
            accuracy = np.mean([accuracy_score(y_test[:, i], y_pred[:, i]) for i in range(y_test.shape[1])])
            f1 = np.mean([f1_score(y_test[:, i], y_pred[:, i], average='weighted') for i in range(y_test.shape[1])])
            
            performance['second_area'] = {
                'accuracy': accuracy,
                'f1_score': f1
            }
            
            logger.info(f"第二區模型性能 - 準確率: {accuracy:.4f}, F1分數: {f1:.4f}")
            
            # 保存模型
            self.models[lottery_type] = {
                'first_area': first_area_model,
                'second_area': second_area_model
            }
            
        elif lottery_type == 'lotto649':
            # 訓練第一區模型
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y_dict['first_area'], test_size=test_size, random_state=42
            )
            
            logger.info("訓練大樂透第一區模型...")
            first_area_model = MultiOutputClassifier(base_model)
            first_area_model.fit(X_train, y_train)
            
            # 預測測試集
            y_pred = first_area_model.predict(X_test)
            
            # 計算性能指標
            accuracy = np.mean([accuracy_score(y_test[:, i], y_pred[:, i]) for i in range(y_test.shape[1])])
            f1 = np.mean([f1_score(y_test[:, i], y_pred[:, i], average='weighted') for i in range(y_test.shape[1])])
            
            performance['first_area'] = {
                'accuracy': accuracy,
                'f1_score': f1
            }
            
            logger.info(f"第一區模型性能 - 準確率: {accuracy:.4f}, F1分數: {f1:.4f}")
            
            # 訓練特別號模型
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y_dict['special'], test_size=test_size, random_state=42
            )
            
            logger.info("訓練大樂透特別號模型...")
            special_model = MultiOutputClassifier(base_model)
            special_model.fit(X_train, y_train)
            
            # 預測測試集
            y_pred = special_model.predict(X_test)
            
            # 計算性能指標
            accuracy = np.mean([accuracy_score(y_test[:, i], y_pred[:, i]) for i in range(y_test.shape[1])])
            f1 = np.mean([f1_score(y_test[:, i], y_pred[:, i], average='weighted') for i in range(y_test.shape[1])])
            
            performance['special'] = {
                'accuracy': accuracy,
                'f1_score': f1
            }
            
            logger.info(f"特別號模型性能 - 準確率: {accuracy:.4f}, F1分數: {f1:.4f}")
            
            # 保存模型
            self.models[lottery_type] = {
                'first_area': first_area_model,
                'special': special_model
            }
            
        elif lottery_type == 'dailycash':
            # 訓練今彩539模型
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y_dict['numbers'], test_size=test_size, random_state=42
            )
            
            logger.info("訓練今彩539模型...")
            numbers_model = MultiOutputClassifier(base_model)
            numbers_model.fit(X_train, y_train)
            
            # 預測測試集
            y_pred = numbers_model.predict(X_test)
            
            # 計算性能指標
            accuracy = np.mean([accuracy_score(y_test[:, i], y_pred[:, i]) for i in range(y_test.shape[1])])
            f1 = np.mean([f1_score(y_test[:, i], y_pred[:, i], average='weighted') for i in range(y_test.shape[1])])
            
            performance['numbers'] = {
                'accuracy': accuracy,
                'f1_score': f1
            }
            
            logger.info(f"今彩539模型性能 - 準確率: {accuracy:.4f}, F1分數: {f1:.4f}")
            
            # 保存模型
            self.models[lottery_type] = {
                'numbers': numbers_model
            }
        
        # 記錄模型資訊
        self.model_info[lottery_type] = {
            'model_type': model_type,
            'feature_count': X.shape[1],
            'performance': performance,
            'training_time': time.time() - start_time
        }
        
        logger.info(f"{self._get_lottery_name(lottery_type)}多標籤模型訓練完成！總耗時: {time.time() - start_time:.1f}秒")
        
        return self.model_info[lottery_type]
    
    def predict(self, features, lottery_type, board_path_scores=None):
        """使用多標籤模型進行預測
        
        Args:
            features: 特徵數據
            lottery_type: 彩票類型
            board_path_scores: 板路分析得出的分數(可選)
            
        Returns:
            dict: 預測結果
        """
        if not self.models[lottery_type]:
            logger.error(f"未找到已訓練的{self._get_lottery_name(lottery_type)}模型")
            return None
        
        # 標準化特徵
        X_scaled = self.scalers[lottery_type].transform(features)
        
        # 設定各彩種的號碼範圍和選擇數量
        number_config = {
            'powercolor': {'first_area': 38, 'first_count': 6, 'second_area': 8},
            'lotto649': {'first_area': 49, 'first_count': 6, 'special': 49},
            'dailycash': {'numbers': 39, 'count': 5}
        }
        
        if lottery_type == 'powercolor':
            # 預測第一區
            first_area_probs = self.models[lottery_type]['first_area'].predict_proba(X_scaled)
            
            # 轉換為每個號碼的概率值 (1-38)
            first_area_scores = {}
            for num in range(1, number_config[lottery_type]['first_area'] + 1):
                # 取得模型對號碼num的預測概率
                prob = first_area_probs[num-1][0][1]  # 正類別的概率
                first_area_scores[num] = prob
            
            # 如果有板路分析分數，進行整合
            if board_path_scores and '主區' in board_path_scores:
                # 結合機器學習和板路分析的分數
                combined_scores = {}
                for num in range(1, number_config[lottery_type]['first_area'] + 1):
                    ml_score = first_area_scores.get(num, 0)
                    bp_score = board_path_scores['主區'].get(num, 0) / 10.0  # 將板路分數標準化到0-1範圍
                    # 使用加權平均
                    combined_scores[num] = 0.6 * ml_score + 0.4 * bp_score
                first_area_scores = combined_scores
            
            # 選擇分數最高的6個號碼
            selected_first_area = sorted(
                first_area_scores.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:number_config[lottery_type]['first_count']]
            selected_first_area = [num for num, _ in selected_first_area]
            selected_first_area.sort()  # 按號碼順序排列
            
            # 預測第二區
            second_area_probs = self.models[lottery_type]['second_area'].predict_proba(X_scaled)
            
            # 轉換為每個號碼的概率值 (1-8)
            second_area_scores = {}
            for num in range(1, number_config[lottery_type]['second_area'] + 1):
                prob = second_area_probs[num-1][0][1]  # 正類別的概率
                second_area_scores[num] = prob
            
            # 如果有板路分析分數，進行整合
            if board_path_scores and '特區' in board_path_scores:
                # 結合機器學習和板路分析的分數
                combined_scores = {}
                for num in range(1, number_config[lottery_type]['second_area'] + 1):
                    ml_score = second_area_scores.get(num, 0)
                    bp_score = board_path_scores['特區'].get(num, 0) / 10.0  # 將板路分數標準化到0-1範圍
                    # 使用加權平均
                    combined_scores[num] = 0.6 * ml_score + 0.4 * bp_score
                second_area_scores = combined_scores
            
            # 選擇分數最高的第二區號碼
            selected_second_area = max(second_area_scores.items(), key=lambda x: x[1])[0]
            
            return {
                '第一區': selected_first_area,
                '第二區': selected_second_area,
                '分數': {
                    '第一區': {num: score for num, score in first_area_scores.items()},
                    '第二區': second_area_scores
                }
            }
            
        elif lottery_type == 'lotto649':
            # 預測第一區
            first_area_probs = self.models[lottery_type]['first_area'].predict_proba(X_scaled)
            
            # 轉換為每個號碼的概率值 (1-49)
            first_area_scores = {}
            for num in range(1, number_config[lottery_type]['first_area'] + 1):
                prob = first_area_probs[num-1][0][1]  # 正類別的概率
                first_area_scores[num] = prob
            
            # 如果有板路分析分數，進行整合
            if board_path_scores and '主區' in board_path_scores:
                combined_scores = {}
                for num in range(1, number_config[lottery_type]['first_area'] + 1):
                    ml_score = first_area_scores.get(num, 0)
                    bp_score = board_path_scores['主區'].get(num, 0) / 10.0
                    combined_scores[num] = 0.6 * ml_score + 0.4 * bp_score
                first_area_scores = combined_scores
            
            # 選擇分數最高的6個號碼
            selected_first_area = sorted(
                first_area_scores.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:number_config[lottery_type]['first_count']]
            selected_first_area = [num for num, _ in selected_first_area]
            selected_first_area.sort()  # 按號碼順序排列
            
            # 預測特別號
            special_probs = self.models[lottery_type]['special'].predict_proba(X_scaled)
            
            # 轉換為每個號碼的概率值 (1-49)
            special_scores = {}
            for num in range(1, number_config[lottery_type]['special'] + 1):
                prob = special_probs[num-1][0][1]  # 正類別的概率
                special_scores[num] = prob
            
            # 排除已經在第一區的號碼
            for num in selected_first_area:
                special_scores[num] = 0
            
            # 如果有板路分析分數，進行整合
            if board_path_scores and '特區' in board_path_scores:
                combined_scores = {}
                for num in range(1, number_config[lottery_type]['special'] + 1):
                    if num not in selected_first_area:  # 排除第一區已選號碼
                        ml_score = special_scores.get(num, 0)
                        bp_score = board_path_scores['特區'].get(num, 0) / 10.0
                        combined_scores[num] = 0.6 * ml_score + 0.4 * bp_score
                    else:
                        combined_scores[num] = 0
                special_scores = combined_scores
            
            # 選擇分數最高的特別號
            selected_special = max(special_scores.items(), key=lambda x: x[1])[0]
            
            return {
                '第一區': selected_first_area,
                '特別號': selected_special,
                '分數': {
                    '第一區': {num: score for num, score in first_area_scores.items()},
                    '特別號': special_scores
                }
            }
            
        elif lottery_type == 'dailycash':
            # 預測今彩539號碼
            numbers_probs = self.models[lottery_type]['numbers'].predict_proba(X_scaled)
            
            # 轉換為每個號碼的概率值 (1-39)
            numbers_scores = {}
            for num in range(1, number_config[lottery_type]['numbers'] + 1):
                prob = numbers_probs[num-1][0][1]  # 正類別的概率
                numbers_scores[num] = prob
            
            # 如果有板路分析分數，進行整合
            if board_path_scores and '主區' in board_path_scores:
                combined_scores = {}
                for num in range(1, number_config[lottery_type]['numbers'] + 1):
                    ml_score = numbers_scores.get(num, 0)
                    bp_score = board_path_scores['主區'].get(num, 0) / 10.0
                    combined_scores[num] = 0.6 * ml_score + 0.4 * bp_score
                numbers_scores = combined_scores
            
            # 選擇分數最高的5個號碼
            selected_numbers = sorted(
                numbers_scores.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:number_config[lottery_type]['count']]
            selected_numbers = [num for num, _ in selected_numbers]
            selected_numbers.sort()  # 按號碼順序排列
            
            return {
                '號碼': selected_numbers,
                '分數': {
                    '號碼': {num: score for num, score in numbers_scores.items()}
                }
            }
    
    def save_models(self, lottery_type='powercolor', version="v1.0"):
        """保存訓練好的模型
        
        Args:
            lottery_type: 彩票類型
            version: 模型版本
            
        Returns:
            bool: 是否成功保存模型
        """
        if lottery_type.lower() not in self.models or not self.models[lottery_type]:
            logger.error(f"沒有找到{self._get_lottery_name(lottery_type)}模型")
            return False
        
        try:
            # 確保模型目錄存在
            if not os.path.exists(self.model_dir):
                os.makedirs(self.model_dir)
                
            # 保存模型
            if lottery_type == 'powercolor':
                with open(os.path.join(self.model_dir, f'multi_powercolor_first_area_{version}.pkl'), 'wb') as f:
                    pickle.dump(self.models[lottery_type]['first_area'], f)
                    
                with open(os.path.join(self.model_dir, f'multi_powercolor_second_area_{version}.pkl'), 'wb') as f:
                    pickle.dump(self.models[lottery_type]['second_area'], f)
                    
            elif lottery_type == 'lotto649':
                with open(os.path.join(self.model_dir, f'multi_lotto649_first_area_{version}.pkl'), 'wb') as f:
                    pickle.dump(self.models[lottery_type]['first_area'], f)
                    
                with open(os.path.join(self.model_dir, f'multi_lotto649_special_{version}.pkl'), 'wb') as f:
                    pickle.dump(self.models[lottery_type]['special'], f)
                    
            elif lottery_type == 'dailycash':
                with open(os.path.join(self.model_dir, f'multi_dailycash_numbers_{version}.pkl'), 'wb') as f:
                    pickle.dump(self.models[lottery_type]['numbers'], f)
            
            # 保存標準化器
            with open(os.path.join(self.model_dir, f'multi_{lottery_type}_scaler_{version}.pkl'), 'wb') as f:
                pickle.dump(self.scalers[lottery_type], f)
            
            # 保存模型信息
            with open(os.path.join(self.model_dir, f'multi_{lottery_type}_info_{version}.json'), 'w', encoding='utf-8') as f:
                # 將numpy類型轉換為標準Python類型
                info_serializable = json.loads(
                    json.dumps(self.model_info[lottery_type], 
                              default=lambda obj: float(obj) if isinstance(obj, (np.float32, np.float64)) else obj)
                )
                json.dump(info_serializable, f, ensure_ascii=False, indent=4)
            
            logger.info(f"{self._get_lottery_name(lottery_type)}多標籤模型已保存到 {self.model_dir} (版本: {version})")
            return True
            
        except Exception as e:
            logger.error(f"保存{self._get_lottery_name(lottery_type)}模型時出錯: {str(e)}")
            return False
    
    def load_models(self, lottery_type='powercolor', version="v1.0"):
        """加載已訓練的模型
        
        Args:
            lottery_type: 彩票類型
            version: 模型版本
            
        Returns:
            bool: 是否成功加載模型
        """
        try:
            # 加載模型
            if lottery_type == 'powercolor':
                with open(os.path.join(self.model_dir, f'multi_powercolor_first_area_{version}.pkl'), 'rb') as f:
                    first_area_model = pickle.load(f)
                    
                with open(os.path.join(self.model_dir, f'multi_powercolor_second_area_{version}.pkl'), 'rb') as f:
                    second_area_model = pickle.load(f)
                    
                self.models[lottery_type] = {
                    'first_area': first_area_model,
                    'second_area': second_area_model
                }
                    
            elif lottery_type == 'lotto649':
                with open(os.path.join(self.model_dir, f'multi_lotto649_first_area_{version}.pkl'), 'rb') as f:
                    first_area_model = pickle.load(f)
                    
                with open(os.path.join(self.model_dir, f'multi_lotto649_special_{version}.pkl'), 'rb') as f:
                    special_model = pickle.load(f)
                    
                self.models[lottery_type] = {
                    'first_area': first_area_model,
                    'special': special_model
                }
                    
            elif lottery_type == 'dailycash':
                with open(os.path.join(self.model_dir, f'multi_dailycash_numbers_{version}.pkl'), 'rb') as f:
                    numbers_model = pickle.load(f)
                    
                self.models[lottery_type] = {
                    'numbers': numbers_model
                }
            
            # 加載標準化器
            with open(os.path.join(self.model_dir, f'multi_{lottery_type}_scaler_{version}.pkl'), 'rb') as f:
                self.scalers[lottery_type] = pickle.load(f)
            
            # 加載模型信息
            with open(os.path.join(self.model_dir, f'multi_{lottery_type}_info_{version}.json'), 'r', encoding='utf-8') as f:
                self.model_info[lottery_type] = json.load(f)
            
            logger.info(f"已加載{self._get_lottery_name(lottery_type)}多標籤模型 (版本: {version})")
            return True
            
        except FileNotFoundError:
            logger.warning(f"找不到{self._get_lottery_name(lottery_type)}多標籤模型文件 (版本: {version})")
            return False
            
        except Exception as e:
            logger.error(f"加載{self._get_lottery_name(lottery_type)}多標籤模型時出錯: {str(e)}")
            return False
    
    def _get_lottery_name(self, lottery_type):
        """根據彩票類型獲取中文名稱"""
        name_map = {
            'powercolor': '威力彩',
            'lotto649': '大樂透',
            'dailycash': '今彩539'
        }
        return name_map.get(lottery_type.lower(), lottery_type)


# 測試代碼
if __name__ == "__main__":
    from db_manager import DBManager
    from feature_engineering import FeatureEngineer
    from config_manager import ConfigManager
    
    # 加載數據
    config_manager = ConfigManager()
    db = DBManager(config_manager)
    
    # 測試威力彩模型
    print("\n=== 測試威力彩多標籤模型 ===")
    powercolor_df = db.load_lottery_data('powercolor')
    
    # 創建特徵
    fe = FeatureEngineer()
    features_df = fe.create_basic_features(powercolor_df, 'powercolor')
    features_df = fe.create_advanced_features(powercolor_df, features_df, 'powercolor')
    
    # 訓練模型
    trainer = MultiLabelModelTrainer()
    trainer.train_models(features_df, lottery_type='powercolor', model_type='random_forest')
    
    # 保存模型
    trainer.save_models(lottery_type='powercolor', version="v2.0")
    
    # 測試模型
    next_features = fe.prepare_next_period_features(powercolor_df, 'powercolor')
    prediction = trainer.predict(next_features, 'powercolor')
    
    print(f"威力彩預測結果:")
    print(f"第一區: {prediction['第一區']}")
    print(f"第二區: {prediction['第二區']}")
    
    # 測試板路分析整合
    from board_path_analyzer_integrated import BoardPathAnalyzer
    analyzer = BoardPathAnalyzer('powerball')
    analyzer.run_full_analysis(powercolor_df)
    board_path_result = analyzer.predict_next_numbers(powercolor_df)
    
    # 將板路分析結果轉換為分數格式
    board_path_scores = {}
    if '候選號碼分數' in board_path_result:
        board_path_scores = board_path_result['候選號碼分數']
    
    # 結合板路分析進行預測
    prediction = trainer.predict(next_features, 'powercolor', board_path_scores)
    
    print(f"\n威力彩結合板路分析的預測結果:")
    print(f"第一區: {prediction['第一區']}")
    print(f"第二區: {prediction['第二區']}")
