#!/usr/bin/env python3
"""
Phase 3 自動預測調度系統
實現智能預測時機判斷、任務管理和結果處理
"""

import asyncio
import schedule
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
import logging
import json
from dataclasses import dataclass, asdict
from enum import Enum
import sqlite3
from pathlib import Path
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import uuid
import sys
import os

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from prediction.enhanced_multi_algorithm_predictor import EnhancedMultiAlgorithmPredictor
from data.db_manager import DBManager
from phase3.realtime_data_manager import RealTimeDataManager

logger = logging.getLogger('auto_prediction_scheduler')

class TaskStatus(Enum):
    """任務狀態"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class TaskPriority(Enum):
    """任務優先級"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4

class TriggerType(Enum):
    """觸發類型"""
    SCHEDULED = "scheduled"
    DATA_UPDATE = "data_update"
    MANUAL = "manual"
    OPTIMIZATION = "optimization"

@dataclass
class PredictionTask:
    """預測任務結構"""
    task_id: str
    lottery_type: str
    trigger_type: TriggerType
    priority: TaskPriority
    status: TaskStatus
    created_at: datetime
    scheduled_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    execution_time: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 3

    def __post_init__(self):
        """初始化後處理"""
        if not self.task_id:
            self.task_id = str(uuid.uuid4())

class SchedulingStrategy:
    """調度策略"""
    
    def __init__(self):
        # 各彩票類型的開獎時間配置
        self.draw_schedules = {
            'powercolor': {
                'days': ['monday', 'thursday'],
                'time': '20:30',
                'prediction_advance': timedelta(hours=2)  # 提前2小時預測
            },
            'lotto649': {
                'days': ['tuesday', 'friday'],
                'time': '20:30',
                'prediction_advance': timedelta(hours=2)
            },
            'dailycash': {
                'days': ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
                'time': '20:30',
                'prediction_advance': timedelta(hours=1)  # 提前1小時預測
            }
        }
        
        # 自動預測配置
        self.auto_prediction_config = {
            'enabled': True,
            'prediction_window': timedelta(hours=6),  # 預測窗口期
            'min_data_age': timedelta(hours=1),       # 最小數據年齡
            'max_prediction_age': timedelta(hours=12) # 最大預測有效期
        }
    
    def should_predict(self, lottery_type: str, last_prediction: Optional[datetime] = None, 
                      last_data_update: Optional[datetime] = None) -> tuple[bool, str]:
        """判斷是否應該進行預測"""
        now = datetime.now()
        
        # 檢查是否啟用自動預測
        if not self.auto_prediction_config['enabled']:
            return False, "自動預測已禁用"
        
        # 檢查彩票類型配置
        if lottery_type not in self.draw_schedules:
            return False, f"不支持的彩票類型: {lottery_type}"
        
        schedule_config = self.draw_schedules[lottery_type]
        
        # 計算下次開獎時間
        next_draw_time = self._calculate_next_draw_time(lottery_type)
        if not next_draw_time:
            return False, "無法計算下次開獎時間"
        
        # 檢查是否在預測窗口內
        prediction_time = next_draw_time - schedule_config['prediction_advance']
        window_start = prediction_time - self.auto_prediction_config['prediction_window'] / 2
        window_end = prediction_time + self.auto_prediction_config['prediction_window'] / 2
        
        if not (window_start <= now <= window_end):
            return False, f"不在預測窗口內 ({window_start} - {window_end})"
        
        # 檢查上次預測時間
        if last_prediction:
            time_since_prediction = now - last_prediction
            if time_since_prediction < self.auto_prediction_config['max_prediction_age']:
                return False, f"距離上次預測時間過短: {time_since_prediction}"
        
        # 檢查數據更新時間
        if last_data_update:
            data_age = now - last_data_update
            if data_age < self.auto_prediction_config['min_data_age']:
                return False, f"數據過新，等待穩定: {data_age}"
        
        return True, "滿足預測條件"
    
    def _calculate_next_draw_time(self, lottery_type: str) -> Optional[datetime]:
        """計算下次開獎時間"""
        try:
            schedule_config = self.draw_schedules[lottery_type]
            now = datetime.now()
            
            # 解析開獎時間
            draw_time = datetime.strptime(schedule_config['time'], '%H:%M').time()
            
            # 查找下次開獎日期
            for days_ahead in range(7):
                check_date = now.date() + timedelta(days=days_ahead)
                weekday = check_date.strftime('%A').lower()
                
                if weekday in schedule_config['days']:
                    draw_datetime = datetime.combine(check_date, draw_time)
                    
                    # 如果是今天但已過開獎時間，跳過
                    if days_ahead == 0 and draw_datetime <= now:
                        continue
                    
                    return draw_datetime
            
            return None
            
        except Exception as e:
            logger.error(f"計算下次開獎時間失敗: {e}")
            return None
    
    def get_task_priority(self, lottery_type: str, trigger_type: TriggerType) -> TaskPriority:
        """獲取任務優先級"""
        # 根據觸發類型和彩票類型確定優先級
        if trigger_type == TriggerType.URGENT:
            return TaskPriority.URGENT
        elif trigger_type == TriggerType.DATA_UPDATE:
            return TaskPriority.HIGH
        elif trigger_type == TriggerType.SCHEDULED:
            return TaskPriority.MEDIUM
        else:
            return TaskPriority.LOW

class TaskManager:
    """任務管理器"""
    
    def __init__(self, db_path: str = "data/scheduler.db"):
        self.db_path = db_path
        self.active_tasks: Dict[str, PredictionTask] = {}
        self.task_queue: List[PredictionTask] = []
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.running = False
        
        # 確保數據庫目錄存在
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        self._init_database()
    
    def _init_database(self):
        """初始化任務數據庫"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 創建任務表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS prediction_tasks (
                        task_id TEXT PRIMARY KEY,
                        lottery_type TEXT NOT NULL,
                        trigger_type TEXT NOT NULL,
                        priority INTEGER NOT NULL,
                        status TEXT NOT NULL,
                        created_at DATETIME NOT NULL,
                        scheduled_at DATETIME,
                        started_at DATETIME,
                        completed_at DATETIME,
                        result TEXT,
                        error_message TEXT,
                        execution_time REAL,
                        retry_count INTEGER DEFAULT 0,
                        max_retries INTEGER DEFAULT 3
                    )
                ''')
                
                # 創建任務執行日誌表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS task_execution_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        task_id TEXT NOT NULL,
                        action TEXT NOT NULL,
                        status TEXT NOT NULL,
                        message TEXT,
                        timestamp DATETIME NOT NULL,
                        FOREIGN KEY (task_id) REFERENCES prediction_tasks (task_id)
                    )
                ''')
                
                conn.commit()
                logger.info("任務數據庫初始化完成")
                
        except Exception as e:
            logger.error(f"任務數據庫初始化失敗: {e}")
            raise
    
    def submit_task(self, task: PredictionTask) -> bool:
        """提交預測任務"""
        try:
            # 檢查重複任務
            if self._has_duplicate_task(task):
                logger.warning(f"重複任務，跳過: {task.lottery_type}")
                return False
            
            # 保存任務到數據庫
            if self._save_task(task):
                # 添加到任務隊列
                self.task_queue.append(task)
                self.task_queue.sort(key=lambda t: (t.priority.value, t.created_at), reverse=True)
                
                self._log_task_action(task.task_id, "submit", "success", f"任務已提交: {task.lottery_type}")
                logger.info(f"任務已提交: {task.task_id} ({task.lottery_type})")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"提交任務失敗: {e}")
            return False
    
    def _has_duplicate_task(self, new_task: PredictionTask) -> bool:
        """檢查是否有重複任務"""
        # 檢查活動任務
        for task in self.active_tasks.values():
            if (task.lottery_type == new_task.lottery_type and 
                task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]):
                return True
        
        # 檢查隊列中的任務
        for task in self.task_queue:
            if (task.lottery_type == new_task.lottery_type and
                task.status == TaskStatus.PENDING):
                return True
        
        return False
    
    def get_next_task(self) -> Optional[PredictionTask]:
        """獲取下一個待執行任務"""
        while self.task_queue:
            task = self.task_queue.pop(0)
            if task.status == TaskStatus.PENDING:
                return task
        
        return None
    
    def update_task_status(self, task_id: str, status: TaskStatus, 
                          result: Optional[Dict[str, Any]] = None,
                          error_message: Optional[str] = None):
        """更新任務狀態"""
        try:
            task = self.active_tasks.get(task_id)
            if not task:
                logger.warning(f"任務不存在: {task_id}")
                return
            
            # 更新任務信息
            task.status = status
            if result:
                task.result = result
            if error_message:
                task.error_message = error_message
            
            # 設置時間戳
            now = datetime.now()
            if status == TaskStatus.RUNNING and not task.started_at:
                task.started_at = now
            elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                task.completed_at = now
                if task.started_at:
                    task.execution_time = (now - task.started_at).total_seconds()
            
            # 更新數據庫
            self._update_task_in_db(task)
            
            # 記錄日誌
            self._log_task_action(task_id, "status_update", status.value, 
                                f"任務狀態更新為: {status.value}")
            
            # 如果任務完成，從活動任務中移除
            if status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                del self.active_tasks[task_id]
            
        except Exception as e:
            logger.error(f"更新任務狀態失敗: {e}")
    
    def _save_task(self, task: PredictionTask) -> bool:
        """保存任務到數據庫"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO prediction_tasks 
                    (task_id, lottery_type, trigger_type, priority, status, created_at,
                     scheduled_at, retry_count, max_retries)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    task.task_id,
                    task.lottery_type,
                    task.trigger_type.value,
                    task.priority.value,
                    task.status.value,
                    task.created_at.isoformat(),
                    task.scheduled_at.isoformat() if task.scheduled_at else None,
                    task.retry_count,
                    task.max_retries
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"保存任務失敗: {e}")
            return False
    
    def _update_task_in_db(self, task: PredictionTask):
        """更新數據庫中的任務"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE prediction_tasks SET
                        status = ?, started_at = ?, completed_at = ?,
                        result = ?, error_message = ?, execution_time = ?,
                        retry_count = ?
                    WHERE task_id = ?
                ''', (
                    task.status.value,
                    task.started_at.isoformat() if task.started_at else None,
                    task.completed_at.isoformat() if task.completed_at else None,
                    json.dumps(task.result, default=str) if task.result else None,
                    task.error_message,
                    task.execution_time,
                    task.retry_count,
                    task.task_id
                ))
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"更新任務數據庫失敗: {e}")
    
    def _log_task_action(self, task_id: str, action: str, status: str, message: str):
        """記錄任務操作日誌"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO task_execution_logs 
                    (task_id, action, status, message, timestamp)
                    VALUES (?, ?, ?, ?, ?)
                ''', (task_id, action, status, message, datetime.now().isoformat()))
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"記錄任務日誌失敗: {e}")
    
    def get_task_stats(self) -> Dict[str, Any]:
        """獲取任務統計"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 統計任務狀態
                cursor.execute('''
                    SELECT status, COUNT(*) as count
                    FROM prediction_tasks 
                    WHERE created_at >= datetime('now', '-24 hours')
                    GROUP BY status
                ''')
                
                status_stats = {}
                for row in cursor.fetchall():
                    status_stats[row[0]] = row[1]
                
                # 統計平均執行時間
                cursor.execute('''
                    SELECT AVG(execution_time) as avg_time,
                           MIN(execution_time) as min_time,
                           MAX(execution_time) as max_time
                    FROM prediction_tasks 
                    WHERE status = 'completed' AND execution_time IS NOT NULL
                    AND created_at >= datetime('now', '-24 hours')
                ''')
                
                time_stats = cursor.fetchone()
                
                return {
                    'active_tasks': len(self.active_tasks),
                    'queued_tasks': len(self.task_queue),
                    'status_stats': status_stats,
                    'execution_time_stats': {
                        'average': time_stats[0] if time_stats[0] else 0,
                        'minimum': time_stats[1] if time_stats[1] else 0,
                        'maximum': time_stats[2] if time_stats[2] else 0
                    }
                }
                
        except Exception as e:
            logger.error(f"獲取任務統計失敗: {e}")
            return {}

class AutoPredictionScheduler:
    """自動預測調度器主類"""
    
    def __init__(self, db_manager: DBManager, realtime_manager: RealTimeDataManager):
        self.db_manager = db_manager
        self.realtime_manager = realtime_manager
        self.strategy = SchedulingStrategy()
        self.task_manager = TaskManager()
        
        # 預測器實例緩存
        self.predictors: Dict[str, EnhancedMultiAlgorithmPredictor] = {}
        
        # 調度器狀態
        self.scheduler_status = {
            'running': False,
            'last_check': None,
            'predictions_made': 0,
            'errors': 0
        }
        
        # 初始化預測器
        self._init_predictors()
        
        # 設置定期檢查
        self._setup_scheduled_checks()
    
    def _init_predictors(self):
        """初始化預測器"""
        lottery_types = ['powercolor', 'lotto649', 'dailycash']
        
        for lottery_type in lottery_types:
            try:
                predictor = EnhancedMultiAlgorithmPredictor(lottery_type, self.db_manager)
                self.predictors[lottery_type] = predictor
                logger.info(f"預測器初始化完成: {lottery_type}")
            except Exception as e:
                logger.error(f"預測器初始化失敗 {lottery_type}: {e}")
    
    def _setup_scheduled_checks(self):
        """設置定期檢查"""
        # 每小時檢查一次是否需要預測
        schedule.every().hour.do(self._check_prediction_needs)
        
        # 每30分鐘處理任務隊列
        schedule.every(30).minutes.do(self._process_task_queue)
        
        # 每天清理舊任務
        schedule.every().day.at("02:00").do(self._cleanup_old_tasks)
    
    def start(self):
        """啟動調度器"""
        if self.scheduler_status['running']:
            logger.warning("調度器已在運行")
            return
        
        self.scheduler_status['running'] = True
        logger.info("自動預測調度器已啟動")
        
        # 啟動調度線程
        def run_scheduler():
            while self.scheduler_status['running']:
                try:
                    schedule.run_pending()
                    time.sleep(60)  # 每分鐘檢查一次
                except Exception as e:
                    logger.error(f"調度器運行錯誤: {e}")
                    time.sleep(5)
        
        scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()
        
        # 啟動任務處理線程
        task_thread = threading.Thread(target=self._task_processor, daemon=True)
        task_thread.start()
    
    def stop(self):
        """停止調度器"""
        self.scheduler_status['running'] = False
        logger.info("自動預測調度器已停止")
    
    def _check_prediction_needs(self):
        """檢查預測需求"""
        try:
            self.scheduler_status['last_check'] = datetime.now()
            
            for lottery_type in self.predictors.keys():
                # 獲取最後預測和數據更新時間
                last_prediction = self._get_last_prediction_time(lottery_type)
                last_data_update = self._get_last_data_update_time(lottery_type)
                
                # 判斷是否需要預測
                should_predict, reason = self.strategy.should_predict(
                    lottery_type, last_prediction, last_data_update
                )
                
                if should_predict:
                    # 創建預測任務
                    task = PredictionTask(
                        task_id="",
                        lottery_type=lottery_type,
                        trigger_type=TriggerType.SCHEDULED,
                        priority=self.strategy.get_task_priority(lottery_type, TriggerType.SCHEDULED),
                        status=TaskStatus.PENDING,
                        created_at=datetime.now()
                    )
                    
                    if self.task_manager.submit_task(task):
                        logger.info(f"已創建自動預測任務: {lottery_type}")
                else:
                    logger.debug(f"暫不需要預測 {lottery_type}: {reason}")
        
        except Exception as e:
            logger.error(f"檢查預測需求失敗: {e}")
            self.scheduler_status['errors'] += 1
    
    def _process_task_queue(self):
        """處理任務隊列"""
        try:
            # 獲取下一個任務
            task = self.task_manager.get_next_task()
            if not task:
                return
            
            # 將任務添加到活動任務
            self.task_manager.active_tasks[task.task_id] = task
            
            # 更新任務狀態為運行中
            self.task_manager.update_task_status(task.task_id, TaskStatus.RUNNING)
            
            # 提交到線程池執行
            future = self.task_manager.executor.submit(self._execute_prediction_task, task)
            
            # 異步處理結果
            def handle_result(fut):
                try:
                    result = fut.result()
                    if result:
                        self.task_manager.update_task_status(
                            task.task_id, TaskStatus.COMPLETED, result=result
                        )
                        self.scheduler_status['predictions_made'] += 1
                    else:
                        self.task_manager.update_task_status(
                            task.task_id, TaskStatus.FAILED, 
                            error_message="預測執行失敗"
                        )
                except Exception as e:
                    self.task_manager.update_task_status(
                        task.task_id, TaskStatus.FAILED,
                        error_message=str(e)
                    )
                    self.scheduler_status['errors'] += 1
            
            future.add_done_callback(handle_result)
            
        except Exception as e:
            logger.error(f"處理任務隊列失敗: {e}")
    
    def _execute_prediction_task(self, task: PredictionTask) -> Optional[Dict[str, Any]]:
        """執行預測任務"""
        try:
            logger.info(f"開始執行預測任務: {task.task_id} ({task.lottery_type})")
            
            # 獲取預測器
            predictor = self.predictors.get(task.lottery_type)
            if not predictor:
                raise Exception(f"找不到預測器: {task.lottery_type}")
            
            # 載入最新數據
            df = self.db_manager.load_lottery_data(task.lottery_type)
            if df.empty:
                raise Exception(f"無法載入 {task.lottery_type} 數據")
            
            # 執行預測
            start_time = time.time()
            result = predictor.enhanced_ensemble_predict(df.tail(100), ensemble_size=10)
            end_time = time.time()
            
            if not result:
                raise Exception("預測返回空結果")
            
            # 添加執行信息
            result['task_id'] = task.task_id
            result['lottery_type'] = task.lottery_type
            result['execution_time'] = end_time - start_time
            result['data_size'] = len(df)
            result['prediction_timestamp'] = datetime.now().isoformat()
            
            # 保存預測結果
            self._save_prediction_result(result)
            
            logger.info(f"預測任務完成: {task.task_id}, 執行時間: {end_time - start_time:.2f}秒")
            
            return result
            
        except Exception as e:
            logger.error(f"執行預測任務失敗 {task.task_id}: {e}")
            raise
    
    def _save_prediction_result(self, result: Dict[str, Any]):
        """保存預測結果"""
        try:
            # 這裡可以保存到數據庫或文件
            # 目前使用簡單的文件保存
            results_dir = Path("prediction_results")
            results_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{result['lottery_type']}_{timestamp}.json"
            
            with open(results_dir / filename, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"預測結果已保存: {filename}")
            
        except Exception as e:
            logger.error(f"保存預測結果失敗: {e}")
    
    def _get_last_prediction_time(self, lottery_type: str) -> Optional[datetime]:
        """獲取最後預測時間"""
        try:
            results_dir = Path("prediction_results")
            if not results_dir.exists():
                return None
            
            # 查找最新的預測文件
            pattern = f"{lottery_type}_*.json"
            files = list(results_dir.glob(pattern))
            
            if files:
                latest_file = max(files, key=lambda f: f.stat().st_mtime)
                return datetime.fromtimestamp(latest_file.stat().st_mtime)
            
            return None
            
        except Exception as e:
            logger.error(f"獲取最後預測時間失敗: {e}")
            return None
    
    def _get_last_data_update_time(self, lottery_type: str) -> Optional[datetime]:
        """獲取最後數據更新時間"""
        try:
            # 這裡應該從實時數據管理器獲取
            # 目前使用簡化實現
            df = self.db_manager.load_lottery_data(lottery_type)
            if not df.empty:
                return datetime.now() - timedelta(hours=2)  # 模擬數據
            
            return None
            
        except Exception as e:
            logger.error(f"獲取最後數據更新時間失敗: {e}")
            return None
    
    def _cleanup_old_tasks(self):
        """清理舊任務"""
        try:
            with sqlite3.connect(self.task_manager.db_path) as conn:
                cursor = conn.cursor()
                
                # 刪除7天前的已完成任務
                cursor.execute('''
                    DELETE FROM prediction_tasks 
                    WHERE status IN ('completed', 'failed', 'cancelled')
                    AND created_at < datetime('now', '-7 days')
                ''')
                
                # 刪除對應的日誌
                cursor.execute('''
                    DELETE FROM task_execution_logs 
                    WHERE task_id NOT IN (SELECT task_id FROM prediction_tasks)
                ''')
                
                conn.commit()
                logger.info("舊任務清理完成")
                
        except Exception as e:
            logger.error(f"清理舊任務失敗: {e}")
    
    def _task_processor(self):
        """任務處理器線程"""
        while self.scheduler_status['running']:
            try:
                self._process_task_queue()
                time.sleep(30)  # 每30秒處理一次
            except Exception as e:
                logger.error(f"任務處理器錯誤: {e}")
                time.sleep(5)
    
    def manual_prediction(self, lottery_type: str, priority: TaskPriority = TaskPriority.HIGH) -> str:
        """手動觸發預測"""
        try:
            task = PredictionTask(
                task_id="",
                lottery_type=lottery_type,
                trigger_type=TriggerType.MANUAL,
                priority=priority,
                status=TaskStatus.PENDING,
                created_at=datetime.now()
            )
            
            if self.task_manager.submit_task(task):
                logger.info(f"手動預測任務已創建: {task.task_id}")
                return task.task_id
            else:
                raise Exception("創建任務失敗")
                
        except Exception as e:
            logger.error(f"手動預測失敗: {e}")
            raise
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """獲取調度器狀態"""
        task_stats = self.task_manager.get_task_stats()
        
        return {
            'scheduler_status': self.scheduler_status.copy(),
            'task_stats': task_stats,
            'predictors_loaded': len(self.predictors),
            'next_scheduled_check': self.strategy.draw_schedules
        }

# 使用示例
async def main():
    """測試自動預測調度器"""
    # 初始化組件
    db_manager = DBManager()
    realtime_manager = RealTimeDataManager()
    
    # 創建調度器
    scheduler = AutoPredictionScheduler(db_manager, realtime_manager)
    
    print("🚀 Phase 3 自動預測調度系統測試")
    print("=" * 50)
    
    # 啟動調度器
    print("\n📅 啟動調度器...")
    scheduler.start()
    
    # 手動觸發預測測試
    print("\n🎯 測試手動預測...")
    task_id = scheduler.manual_prediction('powercolor')
    print(f"手動預測任務ID: {task_id}")
    
    # 等待一段時間觀察結果
    print("\n⏳ 等待任務執行...")
    await asyncio.sleep(10)
    
    # 獲取調度器狀態
    print("\n📊 調度器狀態:")
    status = scheduler.get_scheduler_status()
    print(json.dumps(status, indent=2, ensure_ascii=False, default=str))
    
    # 停止調度器
    print("\n🛑 停止調度器...")
    scheduler.stop()

if __name__ == "__main__":
    asyncio.run(main())