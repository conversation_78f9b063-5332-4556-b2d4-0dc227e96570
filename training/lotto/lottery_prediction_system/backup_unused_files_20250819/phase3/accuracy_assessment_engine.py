#!/usr/bin/env python3
"""
Phase 3 準確度評估和優化引擎
實現實時準確度追蹤、自動模型優化和適應性權重管理
"""

import asyncio
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import logging
import json
from dataclasses import dataclass, asdict
from enum import Enum
import statistics
from pathlib import Path
import threading
import time
from concurrent.futures import ThreadPoolExecutor
import sys
import os

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from prediction.enhanced_multi_algorithm_predictor import EnhancedMultiAlgorithmPredictor
from data.db_manager import DBManager

logger = logging.getLogger('accuracy_assessment_engine')

class MetricType(Enum):
    """指標類型"""
    ACCURACY = "accuracy"  # 準確度
    PRECISION = "precision"  # 精確度
    RECALL = "recall"  # 召回率
    F1_SCORE = "f1_score"  # F1分數
    MATCH_RATE = "match_rate"  # 匹配率
    CONFIDENCE_CORRELATION = "confidence_correlation"  # 信心度相關性

class OptimizationStrategy(Enum):
    """優化策略"""
    CONSERVATIVE = "conservative"  # 保守策略
    AGGRESSIVE = "aggressive"  # 激進策略
    BALANCED = "balanced"  # 平衡策略
    ADAPTIVE = "adaptive"  # 自適應策略

@dataclass
class PredictionResult:
    """預測結果結構"""
    prediction_id: str
    lottery_type: str
    period: str
    predicted_numbers: List[int]
    predicted_special: Optional[int]
    confidence: float
    algorithm_weights: Dict[str, float]
    prediction_timestamp: datetime
    actual_numbers: Optional[List[int]] = None
    actual_special: Optional[int] = None
    match_count: Optional[int] = None
    accuracy_score: Optional[float] = None
    evaluated_at: Optional[datetime] = None

@dataclass
class AlgorithmPerformance:
    """算法性能結構"""
    algorithm_name: str
    total_predictions: int
    total_matches: int
    average_confidence: float
    accuracy_trend: List[float]
    last_updated: datetime
    stability_score: float
    success_rate: float

@dataclass
class OptimizationResult:
    """優化結果結構"""
    optimization_id: str
    strategy: OptimizationStrategy
    old_weights: Dict[str, float]
    new_weights: Dict[str, float]
    performance_improvement: float
    confidence_delta: float
    optimization_timestamp: datetime
    validation_score: float

class AccuracyTracker:
    """準確度追蹤器"""
    
    def __init__(self, db_path: str = "data/accuracy.db"):
        self.db_path = db_path
        self.tracking_window = timedelta(days=30)  # 追蹤窗口期
        self.min_samples = 10  # 最小樣本數
        
        # 確保數據庫目錄存在
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        self._init_database()
    
    def _init_database(self):
        """初始化準確度數據庫"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 創建預測結果表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS prediction_results (
                        prediction_id TEXT PRIMARY KEY,
                        lottery_type TEXT NOT NULL,
                        period TEXT NOT NULL,
                        predicted_numbers TEXT NOT NULL,
                        predicted_special INTEGER,
                        confidence REAL NOT NULL,
                        algorithm_weights TEXT NOT NULL,
                        prediction_timestamp DATETIME NOT NULL,
                        actual_numbers TEXT,
                        actual_special INTEGER,
                        match_count INTEGER,
                        accuracy_score REAL,
                        evaluated_at DATETIME
                    )
                ''')
                
                # 創建算法性能表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS algorithm_performance (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        algorithm_name TEXT NOT NULL,
                        lottery_type TEXT NOT NULL,
                        total_predictions INTEGER DEFAULT 0,
                        total_matches INTEGER DEFAULT 0,
                        average_confidence REAL DEFAULT 0,
                        accuracy_trend TEXT,
                        last_updated DATETIME NOT NULL,
                        stability_score REAL DEFAULT 0.5,
                        success_rate REAL DEFAULT 0,
                        UNIQUE(algorithm_name, lottery_type)
                    )
                ''')
                
                # 創建準確度指標表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS accuracy_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        lottery_type TEXT NOT NULL,
                        metric_type TEXT NOT NULL,
                        metric_value REAL NOT NULL,
                        calculation_timestamp DATETIME NOT NULL,
                        time_window_days INTEGER NOT NULL
                    )
                ''')
                
                conn.commit()
                logger.info("準確度數據庫初始化完成")
                
        except Exception as e:
            logger.error(f"準確度數據庫初始化失敗: {e}")
            raise
    
    def record_prediction(self, result: PredictionResult) -> bool:
        """記錄預測結果"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO prediction_results 
                    (prediction_id, lottery_type, period, predicted_numbers, predicted_special,
                     confidence, algorithm_weights, prediction_timestamp, actual_numbers,
                     actual_special, match_count, accuracy_score, evaluated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    result.prediction_id,
                    result.lottery_type,
                    result.period,
                    json.dumps(result.predicted_numbers),
                    result.predicted_special,
                    result.confidence,
                    json.dumps(result.algorithm_weights, default=str),
                    result.prediction_timestamp.isoformat(),
                    json.dumps(result.actual_numbers) if result.actual_numbers else None,
                    result.actual_special,
                    result.match_count,
                    result.accuracy_score,
                    result.evaluated_at.isoformat() if result.evaluated_at else None
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"記錄預測結果失敗: {e}")
            return False
    
    def evaluate_prediction(self, prediction_id: str, actual_numbers: List[int], 
                          actual_special: Optional[int] = None) -> bool:
        """評估預測結果"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 獲取預測記錄
                cursor.execute(
                    "SELECT predicted_numbers, predicted_special FROM prediction_results WHERE prediction_id = ?",
                    (prediction_id,)
                )
                
                row = cursor.fetchone()
                if not row:
                    logger.warning(f"找不到預測記錄: {prediction_id}")
                    return False
                
                predicted_numbers = json.loads(row[0])
                predicted_special = row[1]
                
                # 計算匹配數
                match_count = len(set(predicted_numbers).intersection(set(actual_numbers)))
                
                # 計算準確度分數 (主號碼匹配數 + 特別號匹配獎勵)
                accuracy_score = match_count
                if predicted_special and actual_special and predicted_special == actual_special:
                    accuracy_score += 0.5  # 特別號匹配獎勵
                
                # 更新記錄
                cursor.execute('''
                    UPDATE prediction_results SET
                        actual_numbers = ?, actual_special = ?, match_count = ?,
                        accuracy_score = ?, evaluated_at = ?
                    WHERE prediction_id = ?
                ''', (
                    json.dumps(actual_numbers),
                    actual_special,
                    match_count,
                    accuracy_score,
                    datetime.now().isoformat(),
                    prediction_id
                ))
                
                conn.commit()
                
                logger.info(f"預測 {prediction_id} 評估完成: 匹配 {match_count}個, 分數 {accuracy_score:.1f}")
                return True
                
        except Exception as e:
            logger.error(f"評估預測結果失敗: {e}")
            return False
    
    def calculate_accuracy_metrics(self, lottery_type: str, 
                                 time_window: timedelta = None) -> Dict[str, float]:
        """計算準確度指標"""
        if time_window is None:
            time_window = self.tracking_window
        
        try:
            cutoff_time = datetime.now() - time_window
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 獲取時間窗口內的評估結果
                cursor.execute('''
                    SELECT match_count, accuracy_score, confidence 
                    FROM prediction_results 
                    WHERE lottery_type = ? AND evaluated_at IS NOT NULL 
                    AND evaluated_at >= ? 
                    ORDER BY evaluated_at DESC
                ''', (lottery_type, cutoff_time.isoformat()))
                
                results = cursor.fetchall()
                
                if not results or len(results) < self.min_samples:
                    logger.warning(f"樣本數不足 ({len(results) if results else 0} < {self.min_samples})")
                    return {}
                
                # 提取數據
                match_counts = [r[0] for r in results]
                accuracy_scores = [r[1] for r in results]
                confidences = [r[2] for r in results]
                
                # 計算各項指標
                metrics = {
                    'average_match_count': np.mean(match_counts),
                    'match_count_std': np.std(match_counts),
                    'max_match_count': max(match_counts),
                    'average_accuracy_score': np.mean(accuracy_scores),
                    'accuracy_consistency': 1.0 / (1.0 + np.std(accuracy_scores)),  # 一致性評分
                    'average_confidence': np.mean(confidences),
                    'confidence_accuracy_correlation': np.corrcoef(confidences, accuracy_scores)[0, 1] if len(confidences) > 1 else 0,
                    'success_rate_2plus': sum(1 for m in match_counts if m >= 2) / len(match_counts),
                    'success_rate_3plus': sum(1 for m in match_counts if m >= 3) / len(match_counts),
                    'total_predictions': len(results)
                }
                
                # 儲存指標到數據庫
                timestamp = datetime.now()
                for metric_name, metric_value in metrics.items():
                    if not np.isnan(metric_value):  # 排除NaN值
                        cursor.execute('''
                            INSERT INTO accuracy_metrics 
                            (lottery_type, metric_type, metric_value, calculation_timestamp, time_window_days)
                            VALUES (?, ?, ?, ?, ?)
                        ''', (lottery_type, metric_name, metric_value, timestamp.isoformat(), time_window.days))
                
                conn.commit()
                
                return metrics
                
        except Exception as e:
            logger.error(f"計算準確度指標失敗: {e}")
            return {}
    
    def get_algorithm_performance(self, lottery_type: str) -> Dict[str, AlgorithmPerformance]:
        """獲取算法性能統計"""
        try:
            cutoff_time = datetime.now() - self.tracking_window
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 獲取所有預測結果中的算法權重
                cursor.execute('''
                    SELECT algorithm_weights, match_count, confidence 
                    FROM prediction_results 
                    WHERE lottery_type = ? AND evaluated_at IS NOT NULL 
                    AND evaluated_at >= ?
                ''', (lottery_type, cutoff_time.isoformat()))
                
                results = cursor.fetchall()
                
                if not results:
                    return {}
                
                # 解析算法性能
                algorithm_stats = {}
                
                for row in results:
                    weights = json.loads(row[0])
                    match_count = row[1]
                    confidence = row[2]
                    
                    for algo_name, weight in weights.items():
                        if algo_name not in algorithm_stats:
                            algorithm_stats[algo_name] = {
                                'total_predictions': 0,
                                'weighted_matches': 0,
                                'weighted_confidence': 0,
                                'match_history': []
                            }
                        
                        algorithm_stats[algo_name]['total_predictions'] += 1
                        algorithm_stats[algo_name]['weighted_matches'] += match_count * weight
                        algorithm_stats[algo_name]['weighted_confidence'] += confidence * weight
                        algorithm_stats[algo_name]['match_history'].append(match_count * weight)
                
                # 轉換為AlgorithmPerformance對象
                performance_dict = {}
                
                for algo_name, stats in algorithm_stats.items():
                    if stats['total_predictions'] > 0:
                        avg_confidence = stats['weighted_confidence'] / stats['total_predictions']
                        total_matches = stats['weighted_matches']
                        match_history = stats['match_history']
                        
                        # 計算穩定性分數 (基於匹配數的一致性)
                        stability_score = 1.0 / (1.0 + np.std(match_history)) if len(match_history) > 1 else 0.5
                        
                        # 計算成功率 (匹配數 >= 2的比例)
                        success_rate = sum(1 for m in match_history if m >= 2) / len(match_history)
                        
                        performance_dict[algo_name] = AlgorithmPerformance(
                            algorithm_name=algo_name,
                            total_predictions=stats['total_predictions'],
                            total_matches=int(total_matches),
                            average_confidence=avg_confidence,
                            accuracy_trend=match_history[-10:],  # 最近10次的趨勢
                            last_updated=datetime.now(),
                            stability_score=stability_score,
                            success_rate=success_rate
                        )
                
                return performance_dict
                
        except Exception as e:
            logger.error(f"獲取算法性能失敗: {e}")
            return {}

class ModelOptimizer:
    """模型自動優化器"""
    
    def __init__(self, tracker: AccuracyTracker):
        self.tracker = tracker
        self.optimization_threshold = 0.1  # 優化閾值
        self.learning_rate = 0.05  # 學習率
        self.momentum = 0.9  # 動量
        self.weight_history = {}  # 權重歷史
    
    def analyze_performance_trends(self, lottery_type: str) -> Dict[str, Any]:
        """分析性能趨勢"""
        try:
            # 獲取算法性能
            performance_data = self.tracker.get_algorithm_performance(lottery_type)
            
            if not performance_data:
                return {}
            
            analysis = {
                'algorithm_ranking': [],
                'underperforming_algorithms': [],
                'stable_algorithms': [],
                'trending_up': [],
                'trending_down': []
            }
            
            # 按成功率排序算法
            sorted_algos = sorted(
                performance_data.items(), 
                key=lambda x: x[1].success_rate, 
                reverse=True
            )
            
            analysis['algorithm_ranking'] = [(name, perf.success_rate) for name, perf in sorted_algos]
            
            # 識別表現不佳的算法
            avg_success_rate = np.mean([perf.success_rate for perf in performance_data.values()])
            for name, perf in performance_data.items():
                if perf.success_rate < avg_success_rate * 0.8:  # 低於平均80%
                    analysis['underperforming_algorithms'].append(name)
                
                if perf.stability_score > 0.7:  # 穩定性高
                    analysis['stable_algorithms'].append(name)
                
                # 分析趨勢
                if len(perf.accuracy_trend) >= 5:
                    recent_trend = np.polyfit(range(len(perf.accuracy_trend)), perf.accuracy_trend, 1)[0]
                    if recent_trend > 0.1:
                        analysis['trending_up'].append(name)
                    elif recent_trend < -0.1:
                        analysis['trending_down'].append(name)
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析性能趨勢失敗: {e}")
            return {}
    
    def calculate_optimal_weights(self, lottery_type: str, 
                                strategy: OptimizationStrategy = OptimizationStrategy.BALANCED) -> Dict[str, float]:
        """計算最優權重"""
        try:
            performance_data = self.tracker.get_algorithm_performance(lottery_type)
            
            if not performance_data:
                return {}
            
            # 基礎權重計算
            weights = {}
            total_score = 0
            
            for algo_name, perf in performance_data.items():
                # 綜合評分 = 成功率 * 穩定性權重
                stability_weight = 0.7 if strategy == OptimizationStrategy.CONSERVATIVE else 0.3
                performance_weight = 1.0 - stability_weight
                
                score = (perf.success_rate * performance_weight + 
                        perf.stability_score * stability_weight)
                
                # 策略調整
                if strategy == OptimizationStrategy.AGGRESSIVE:
                    # 激進策略：更偏重近期表現
                    if len(perf.accuracy_trend) >= 3:
                        recent_performance = np.mean(perf.accuracy_trend[-3:])
                        score = score * 0.7 + recent_performance * 0.3
                
                elif strategy == OptimizationStrategy.ADAPTIVE:
                    # 自適應策略：根據信心度調整
                    confidence_factor = min(perf.average_confidence / 100, 1.0)
                    score *= confidence_factor
                
                weights[algo_name] = max(score, 0.01)  # 最小權重
                total_score += weights[algo_name]
            
            # 標準化權重
            if total_score > 0:
                for algo_name in weights:
                    weights[algo_name] /= total_score
            
            return weights
            
        except Exception as e:
            logger.error(f"計算最優權重失敗: {e}")
            return {}
    
    def optimize_weights(self, lottery_type: str, current_weights: Dict[str, float],
                        strategy: OptimizationStrategy = OptimizationStrategy.BALANCED) -> OptimizationResult:
        """優化權重"""
        try:
            # 計算最優權重
            optimal_weights = self.calculate_optimal_weights(lottery_type, strategy)
            
            if not optimal_weights:
                raise Exception("無法計算最優權重")
            
            # 平滑權重變化 (避免劇烈調整)
            smoothed_weights = {}
            for algo_name in current_weights:
                if algo_name in optimal_weights:
                    current_weight = current_weights[algo_name]
                    optimal_weight = optimal_weights[algo_name]
                    
                    # 使用動量進行平滑
                    if algo_name in self.weight_history:
                        momentum_term = self.momentum * self.weight_history[algo_name].get('momentum', 0)
                        weight_change = self.learning_rate * (optimal_weight - current_weight) + momentum_term
                        new_weight = current_weight + weight_change
                        
                        # 記錄動量
                        self.weight_history[algo_name] = {'momentum': weight_change}
                    else:
                        new_weight = current_weight + self.learning_rate * (optimal_weight - current_weight)
                        self.weight_history[algo_name] = {'momentum': 0}
                    
                    smoothed_weights[algo_name] = max(min(new_weight, 1.0), 0.001)  # 限制範圍
                else:
                    smoothed_weights[algo_name] = current_weights[algo_name]
            
            # 重新標準化
            total_weight = sum(smoothed_weights.values())
            if total_weight > 0:
                for algo_name in smoothed_weights:
                    smoothed_weights[algo_name] /= total_weight
            
            # 計算性能改進預期
            performance_improvement = self._estimate_performance_improvement(
                lottery_type, current_weights, smoothed_weights
            )
            
            # 計算信心度變化
            confidence_delta = self._estimate_confidence_change(
                lottery_type, current_weights, smoothed_weights
            )
            
            # 創建優化結果
            result = OptimizationResult(
                optimization_id=f"opt_{lottery_type}_{int(time.time())}",
                strategy=strategy,
                old_weights=current_weights.copy(),
                new_weights=smoothed_weights,
                performance_improvement=performance_improvement,
                confidence_delta=confidence_delta,
                optimization_timestamp=datetime.now(),
                validation_score=self._validate_weights(smoothed_weights)
            )
            
            return result
            
        except Exception as e:
            logger.error(f"優化權重失敗: {e}")
            raise
    
    def _estimate_performance_improvement(self, lottery_type: str, 
                                        old_weights: Dict[str, float], 
                                        new_weights: Dict[str, float]) -> float:
        """估計性能改進"""
        try:
            performance_data = self.tracker.get_algorithm_performance(lottery_type)
            
            if not performance_data:
                return 0.0
            
            # 計算加權性能分數
            old_score = 0
            new_score = 0
            
            for algo_name, perf in performance_data.items():
                if algo_name in old_weights and algo_name in new_weights:
                    algo_score = perf.success_rate * 0.7 + perf.stability_score * 0.3
                    old_score += algo_score * old_weights[algo_name]
                    new_score += algo_score * new_weights[algo_name]
            
            return new_score - old_score
            
        except Exception as e:
            logger.error(f"估計性能改進失敗: {e}")
            return 0.0
    
    def _estimate_confidence_change(self, lottery_type: str,
                                  old_weights: Dict[str, float], 
                                  new_weights: Dict[str, float]) -> float:
        """估計信心度變化"""
        try:
            performance_data = self.tracker.get_algorithm_performance(lottery_type)
            
            if not performance_data:
                return 0.0
            
            # 計算加權信心度
            old_confidence = 0
            new_confidence = 0
            
            for algo_name, perf in performance_data.items():
                if algo_name in old_weights and algo_name in new_weights:
                    old_confidence += perf.average_confidence * old_weights[algo_name]
                    new_confidence += perf.average_confidence * new_weights[algo_name]
            
            return new_confidence - old_confidence
            
        except Exception as e:
            logger.error(f"估計信心度變化失敗: {e}")
            return 0.0
    
    def _validate_weights(self, weights: Dict[str, float]) -> float:
        """驗證權重有效性"""
        try:
            # 檢查權重總和
            total_weight = sum(weights.values())
            if abs(total_weight - 1.0) > 0.01:
                return 0.0
            
            # 檢查權重分布 (避免過度集中)
            max_weight = max(weights.values())
            min_weight = min(weights.values())
            
            if max_weight > 0.8:  # 單個算法權重過高
                return 0.5
            
            if max_weight / min_weight > 20:  # 權重差異過大
                return 0.6
            
            # 檢查權重數量
            active_weights = sum(1 for w in weights.values() if w > 0.05)
            if active_weights < 2:  # 活躍算法過少
                return 0.7
            
            return 1.0  # 權重分布良好
            
        except Exception as e:
            logger.error(f"驗證權重失敗: {e}")
            return 0.0

class AdaptiveWeightManager:
    """自適應權重管理器"""
    
    def __init__(self, optimizer: ModelOptimizer, tracker: AccuracyTracker):
        self.optimizer = optimizer
        self.tracker = tracker
        self.adjustment_threshold = 0.05  # 調整閾值
        self.monitoring_window = timedelta(hours=6)  # 監控窗口
        self.last_optimization = {}  # 最後優化時間
        
        # 權重管理策略
        self.strategies = {
            'performance_driven': OptimizationStrategy.AGGRESSIVE,
            'stability_focused': OptimizationStrategy.CONSERVATIVE,
            'balanced_approach': OptimizationStrategy.BALANCED,
            'auto_adaptive': OptimizationStrategy.ADAPTIVE
        }
    
    def should_optimize(self, lottery_type: str, current_performance: Dict[str, float]) -> Tuple[bool, str]:
        """判斷是否需要優化"""
        try:
            # 檢查最後優化時間
            last_opt_time = self.last_optimization.get(lottery_type)
            if last_opt_time and datetime.now() - last_opt_time < self.monitoring_window:
                return False, "優化間隔未到"
            
            # 檢查性能指標
            if not current_performance:
                return False, "無性能數據"
            
            # 檢查準確度下降
            recent_accuracy = current_performance.get('average_accuracy_score', 0)
            if recent_accuracy < 1.5:  # 平均匹配數低於1.5
                return True, "準確度下降"
            
            # 檢查一致性
            consistency = current_performance.get('accuracy_consistency', 1.0)
            if consistency < 0.7:  # 一致性過低
                return True, "預測一致性低"
            
            # 檢查信心度準確率相關性
            correlation = current_performance.get('confidence_accuracy_correlation', 0)
            if abs(correlation) < 0.3:  # 相關性過低
                return True, "信心度與準確率相關性低"
            
            return False, "性能正常"
            
        except Exception as e:
            logger.error(f"判斷優化需求失敗: {e}")
            return False, f"判斷失敗: {e}"
    
    def auto_adjust_weights(self, lottery_type: str, predictor: EnhancedMultiAlgorithmPredictor) -> bool:
        """自動調整權重"""
        try:
            # 獲取當前性能
            current_performance = self.tracker.calculate_accuracy_metrics(lottery_type)
            
            # 判斷是否需要優化
            should_opt, reason = self.should_optimize(lottery_type, current_performance)
            
            if not should_opt:
                logger.info(f"{lottery_type} 權重調整跳過: {reason}")
                return False
            
            logger.info(f"{lottery_type} 開始權重調整: {reason}")
            
            # 選擇優化策略
            strategy = self._select_optimization_strategy(lottery_type, current_performance)
            
            # 執行優化
            current_weights = predictor.algorithm_weights.copy()
            optimization_result = self.optimizer.optimize_weights(
                lottery_type, current_weights, strategy
            )
            
            # 驗證優化結果
            if optimization_result.validation_score < 0.7:
                logger.warning(f"權重優化結果不佳: {optimization_result.validation_score:.3f}")
                return False
            
            # 應用新權重
            predictor.algorithm_weights = optimization_result.new_weights
            
            # 記錄優化
            self.last_optimization[lottery_type] = datetime.now()
            
            logger.info(f"{lottery_type} 權重調整完成:")
            logger.info(f"  性能改進預期: {optimization_result.performance_improvement:.3f}")
            logger.info(f"  信心度變化: {optimization_result.confidence_delta:.1f}%")
            logger.info(f"  驗證分數: {optimization_result.validation_score:.3f}")
            
            # 顯示權重變化
            for algo_name, new_weight in optimization_result.new_weights.items():
                old_weight = optimization_result.old_weights.get(algo_name, 0)
                change = new_weight - old_weight
                if abs(change) > 0.01:
                    logger.info(f"  {algo_name}: {old_weight:.3f} → {new_weight:.3f} ({change:+.3f})")
            
            return True
            
        except Exception as e:
            logger.error(f"自動調整權重失敗: {e}")
            return False
    
    def _select_optimization_strategy(self, lottery_type: str, 
                                    performance: Dict[str, float]) -> OptimizationStrategy:
        """選擇優化策略"""
        try:
            # 基於當前性能選擇策略
            accuracy = performance.get('average_accuracy_score', 0)
            consistency = performance.get('accuracy_consistency', 1.0)
            correlation = performance.get('confidence_accuracy_correlation', 0)
            
            # 性能很差 → 激進策略
            if accuracy < 1.0 or consistency < 0.5:
                return OptimizationStrategy.AGGRESSIVE
            
            # 性能不穩定 → 保守策略
            if consistency < 0.7 or abs(correlation) < 0.2:
                return OptimizationStrategy.CONSERVATIVE
            
            # 性能良好但可優化 → 平衡策略
            if accuracy < 2.0 or abs(correlation) < 0.5:
                return OptimizationStrategy.BALANCED
            
            # 性能優秀 → 自適應策略
            return OptimizationStrategy.ADAPTIVE
            
        except Exception as e:
            logger.error(f"選擇優化策略失敗: {e}")
            return OptimizationStrategy.BALANCED

class AccuracyAssessmentEngine:
    """準確度評估引擎主類"""
    
    def __init__(self, db_manager: DBManager):
        self.db_manager = db_manager
        self.tracker = AccuracyTracker()
        self.optimizer = ModelOptimizer(self.tracker)
        self.weight_manager = AdaptiveWeightManager(self.optimizer, self.tracker)
        
        # 評估狀態
        self.assessment_status = {
            'running': False,
            'last_assessment': None,
            'assessments_completed': 0,
            'optimizations_performed': 0
        }
        
        # 預測器緩存
        self.predictors = {}
    
    def start_continuous_assessment(self):
        """啟動持續評估"""
        if self.assessment_status['running']:
            logger.warning("評估引擎已在運行")
            return
        
        self.assessment_status['running'] = True
        logger.info("準確度評估引擎已啟動")
        
        # 啟動評估線程
        def assessment_worker():
            while self.assessment_status['running']:
                try:
                    self._perform_assessment_cycle()
                    time.sleep(3600)  # 每小時評估一次
                except Exception as e:
                    logger.error(f"評估循環錯誤: {e}")
                    time.sleep(300)  # 錯誤時等待5分鐘
        
        assessment_thread = threading.Thread(target=assessment_worker, daemon=True)
        assessment_thread.start()
    
    def stop_continuous_assessment(self):
        """停止持續評估"""
        self.assessment_status['running'] = False
        logger.info("準確度評估引擎已停止")
    
    def _perform_assessment_cycle(self):
        """執行評估循環"""
        try:
            lottery_types = ['powercolor', 'lotto649', 'dailycash']
            
            for lottery_type in lottery_types:
                logger.info(f"開始評估 {lottery_type}")
                
                # 計算準確度指標
                metrics = self.tracker.calculate_accuracy_metrics(lottery_type)
                
                if metrics:
                    logger.info(f"{lottery_type} 準確度指標:")
                    logger.info(f"  平均匹配數: {metrics.get('average_match_count', 0):.2f}")
                    logger.info(f"  成功率(≥2): {metrics.get('success_rate_2plus', 0):.1%}")
                    logger.info(f"  一致性: {metrics.get('accuracy_consistency', 0):.3f}")
                    
                    # 檢查是否需要權重優化
                    if lottery_type in self.predictors:
                        predictor = self.predictors[lottery_type]
                        optimization_performed = self.weight_manager.auto_adjust_weights(
                            lottery_type, predictor
                        )
                        
                        if optimization_performed:
                            self.assessment_status['optimizations_performed'] += 1
                
                self.assessment_status['assessments_completed'] += 1
            
            self.assessment_status['last_assessment'] = datetime.now()
            
        except Exception as e:
            logger.error(f"評估循環失敗: {e}")
    
    def register_predictor(self, lottery_type: str, predictor: EnhancedMultiAlgorithmPredictor):
        """註冊預測器"""
        self.predictors[lottery_type] = predictor
        logger.info(f"已註冊 {lottery_type} 預測器")
    
    def record_prediction_result(self, lottery_type: str, prediction_result: Dict[str, Any],
                               prediction_id: Optional[str] = None) -> str:
        """記錄預測結果"""
        try:
            if not prediction_id:
                prediction_id = f"pred_{lottery_type}_{int(time.time())}"
            
            result = PredictionResult(
                prediction_id=prediction_id,
                lottery_type=lottery_type,
                period=prediction_result.get('period', ''),
                predicted_numbers=prediction_result.get('main_numbers', []),
                predicted_special=prediction_result.get('special_number'),
                confidence=prediction_result.get('confidence', 0),
                algorithm_weights=prediction_result.get('algorithm_weights', {}),
                prediction_timestamp=datetime.now()
            )
            
            if self.tracker.record_prediction(result):
                logger.info(f"預測結果已記錄: {prediction_id}")
                return prediction_id
            else:
                raise Exception("記錄預測結果失敗")
                
        except Exception as e:
            logger.error(f"記錄預測結果失敗: {e}")
            raise
    
    def evaluate_prediction_accuracy(self, prediction_id: str, actual_result: Dict[str, Any]) -> bool:
        """評估預測準確度"""
        try:
            actual_numbers = [
                actual_result.get('Anumber1'), actual_result.get('Anumber2'),
                actual_result.get('Anumber3'), actual_result.get('Anumber4'),
                actual_result.get('Anumber5'), actual_result.get('Anumber6')
            ]
            actual_special = actual_result.get('Bnumber')
            
            # 過濾None值
            actual_numbers = [n for n in actual_numbers if n is not None]
            
            if len(actual_numbers) < 6:
                logger.warning(f"實際結果不完整: {actual_numbers}")
                return False
            
            return self.tracker.evaluate_prediction(prediction_id, actual_numbers, actual_special)
            
        except Exception as e:
            logger.error(f"評估預測準確度失敗: {e}")
            return False
    
    def get_comprehensive_report(self, lottery_type: str) -> Dict[str, Any]:
        """獲取綜合評估報告"""
        try:
            # 基礎指標
            metrics = self.tracker.calculate_accuracy_metrics(lottery_type)
            
            # 算法性能
            algorithm_performance = self.tracker.get_algorithm_performance(lottery_type)
            
            # 性能趨勢分析
            trend_analysis = self.optimizer.analyze_performance_trends(lottery_type)
            
            # 系統狀態
            status = self.assessment_status.copy()
            
            report = {
                'lottery_type': lottery_type,
                'generation_time': datetime.now().isoformat(),
                'accuracy_metrics': metrics,
                'algorithm_performance': {
                    name: asdict(perf) for name, perf in algorithm_performance.items()
                },
                'trend_analysis': trend_analysis,
                'system_status': status,
                'recommendations': self._generate_recommendations(metrics, trend_analysis)
            }
            
            return report
            
        except Exception as e:
            logger.error(f"生成綜合報告失敗: {e}")
            return {'error': str(e)}
    
    def _generate_recommendations(self, metrics: Dict[str, float], 
                                trend_analysis: Dict[str, Any]) -> List[str]:
        """生成優化建議"""
        recommendations = []
        
        try:
            # 基於準確度指標的建議
            avg_match = metrics.get('average_match_count', 0)
            if avg_match < 1.5:
                recommendations.append("準確度偏低，建議調整算法權重或增加訓練數據")
            
            consistency = metrics.get('accuracy_consistency', 1.0)
            if consistency < 0.7:
                recommendations.append("預測一致性不足，建議使用更穩定的算法組合")
            
            correlation = metrics.get('confidence_accuracy_correlation', 0)
            if abs(correlation) < 0.3:
                recommendations.append("信心度與準確率相關性低，建議重新校準信心度計算")
            
            # 基於趨勢分析的建議
            if trend_analysis.get('underperforming_algorithms'):
                algorithms = ', '.join(trend_analysis['underperforming_algorithms'])
                recommendations.append(f"以下算法表現不佳，建議降低權重: {algorithms}")
            
            if trend_analysis.get('trending_down'):
                algorithms = ', '.join(trend_analysis['trending_down'])
                recommendations.append(f"以下算法呈下降趨勢，需要關注: {algorithms}")
            
            if not recommendations:
                recommendations.append("系統運行正常，建議維持當前配置")
                
        except Exception as e:
            logger.error(f"生成建議失敗: {e}")
            recommendations.append("建議生成失敗，請檢查系統狀態")
        
        return recommendations

# 使用示例
async def main():
    """測試準確度評估引擎"""
    # 初始化組件
    db_manager = DBManager()
    assessment_engine = AccuracyAssessmentEngine(db_manager)
    
    print("🚀 Phase 3 準確度評估和優化引擎測試")
    print("=" * 60)
    
    # 初始化預測器
    print("\n🧠 初始化預測器...")
    predictor = EnhancedMultiAlgorithmPredictor('powercolor', db_manager)
    assessment_engine.register_predictor('powercolor', predictor)
    
    # 載入測試數據
    print("\n📊 載入測試數據...")
    df = db_manager.load_lottery_data('powercolor')
    if df.empty:
        print("❌ 無法載入歷史數據")
        return
    
    test_data = df.tail(100)
    
    # 模擬預測和評估
    print("\n🎯 模擬預測和評估...")
    
    for i in range(3):
        try:
            # 生成預測
            prediction = predictor.enhanced_ensemble_predict(test_data, ensemble_size=5)
            if prediction:
                # 記錄預測
                prediction_id = assessment_engine.record_prediction_result('powercolor', prediction)
                
                # 模擬實際結果 (使用歷史數據)
                actual_data = df.iloc[-i-1]
                actual_result = {
                    'Anumber1': actual_data['Anumber1'],
                    'Anumber2': actual_data['Anumber2'], 
                    'Anumber3': actual_data['Anumber3'],
                    'Anumber4': actual_data['Anumber4'],
                    'Anumber5': actual_data['Anumber5'],
                    'Anumber6': actual_data['Anumber6'],
                    'Bnumber': actual_data['Bnumber']
                }
                
                # 評估預測
                assessment_engine.evaluate_prediction_accuracy(prediction_id, actual_result)
                
                print(f"預測 {i+1}: {prediction['main_numbers']} | 實際: {[actual_data[f'Anumber{j}'] for j in range(1, 7)]}")
        except Exception as e:
            print(f"模擬預測 {i+1} 失敗: {e}")
    
    # 等待一下讓數據處理完成
    await asyncio.sleep(1)
    
    # 獲取綜合報告
    print("\n📈 生成綜合評估報告...")
    report = assessment_engine.get_comprehensive_report('powercolor')
    
    if 'error' not in report:
        metrics = report.get('accuracy_metrics', {})
        print(f"平均匹配數: {metrics.get('average_match_count', 0):.2f}")
        print(f"成功率(≥2): {metrics.get('success_rate_2plus', 0):.1%}")
        print(f"一致性分數: {metrics.get('accuracy_consistency', 0):.3f}")
        
        print("\n💡 優化建議:")
        for recommendation in report.get('recommendations', []):
            print(f"  • {recommendation}")
    else:
        print(f"❌ 報告生成失敗: {report['error']}")
    
    print("\n✅ 準確度評估引擎測試完成")

if __name__ == "__main__":
    asyncio.run(main())