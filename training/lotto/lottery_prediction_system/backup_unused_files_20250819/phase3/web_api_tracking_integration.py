#!/usr/bin/env python3
"""
Phase 3.5 Web API 與追蹤系統集成模組
將預測追蹤和統計分析功能集成到 Web API 中
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Query, status
from fastapi.responses import HTMLResponse, FileResponse
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import asyncio
import json
import tempfile
import os
from datetime import datetime, timedelta
from pathlib import Path
import logging

# 導入追蹤系統組件
from prediction_tracking_system import (
    IntegratedTrackingSystem, PredictionTracker, StatisticsAnalyzer,
    AnalysisTimeframe, PredictionStatus
)
from visualization_reports import VisualizationGenerator, ReportGenerator

# 配置日誌
logger = logging.getLogger(__name__)

# === API 數據模型 ===

class PredictionVerificationRequest(BaseModel):
    """預測驗證請求模型"""
    prediction_id: str = Field(..., description="預測ID")
    actual_main_numbers: List[int] = Field(..., description="實際主號碼")
    actual_special_numbers: Optional[List[int]] = Field(None, description="實際特別號碼")
    draw_timestamp: Optional[datetime] = Field(None, description="開獎時間")

class TrackingStatsResponse(BaseModel):
    """追蹤統計響應模型"""
    lottery_type: str
    total_predictions: int
    verified_predictions: int
    pending_predictions: int
    accuracy_rate: float
    average_confidence: float
    best_strategy: Optional[str]
    recent_trend: str

class PerformanceMetricsResponse(BaseModel):
    """性能指標響應模型"""
    lottery_type: str
    timeframe: str
    accuracy_rate: float
    average_confidence: float
    confidence_accuracy_correlation: float
    strategy_success_rates: Dict[str, float]
    temporal_consistency: float
    improvement_trend: float

class StatisticsReportResponse(BaseModel):
    """統計報告響應模型"""
    report_id: str
    lottery_type: str
    timeframe: str
    start_date: datetime
    end_date: datetime
    total_predictions: int
    verified_predictions: int
    accuracy_metrics: Dict[str, float]
    strategy_performance: Dict[str, Dict[str, float]]
    recommendations: List[str]
    generated_timestamp: datetime

class ChartResponse(BaseModel):
    """圖表響應模型"""
    chart_type: str
    lottery_type: str
    chart_data: str  # Base64 編碼的圖片
    generated_timestamp: datetime

# === Web API 追蹤集成類 ===

class WebAPITrackingIntegration:
    """Web API 追蹤系統集成"""
    
    def __init__(self):
        self.tracking_system: Optional[IntegratedTrackingSystem] = None
        self.visualizer: Optional[VisualizationGenerator] = None
        self.report_generator: Optional[ReportGenerator] = None
        self._initialized = False
    
    async def initialize(self, db_manager):
        """初始化追蹤系統"""
        if self._initialized:
            return
        
        try:
            logger.info("🔄 初始化追蹤系統...")
            
            # 創建追蹤系統實例
            self.tracking_system = IntegratedTrackingSystem(db_manager)
            self.visualizer = VisualizationGenerator(self.tracking_system)
            self.report_generator = ReportGenerator(self.tracking_system)
            
            # 啟動追蹤服務
            await self.tracking_system.start_tracking_service()
            
            self._initialized = True
            logger.info("✅ 追蹤系統初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 追蹤系統初始化失敗: {e}")
            raise
    
    def get_tracking_system(self) -> IntegratedTrackingSystem:
        """獲取追蹤系統實例"""
        if not self._initialized or not self.tracking_system:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="追蹤系統尚未初始化"
            )
        return self.tracking_system
    
    def get_visualizer(self) -> VisualizationGenerator:
        """獲取可視化生成器實例"""
        if not self._initialized or not self.visualizer:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="可視化生成器尚未初始化"
            )
        return self.visualizer
    
    def get_report_generator(self) -> ReportGenerator:
        """獲取報告生成器實例"""
        if not self._initialized or not self.report_generator:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="報告生成器尚未初始化"
            )
        return self.report_generator

# 全局實例
tracking_integration = WebAPITrackingIntegration()

# === API 路由器 ===

tracking_router = APIRouter(prefix="/tracking", tags=["預測追蹤"])

@tracking_router.post("/verify", summary="驗證預測結果")
async def verify_prediction(
    request: PredictionVerificationRequest,
    background_tasks: BackgroundTasks
):
    """驗證預測結果並更新追蹤記錄"""
    try:
        tracking_system = tracking_integration.get_tracking_system()
        
        # 執行預測驗證
        success = tracking_system.tracker.verify_prediction(
            prediction_id=request.prediction_id,
            actual_main=request.actual_main_numbers,
            actual_special=request.actual_special_numbers,
            draw_timestamp=request.draw_timestamp
        )
        
        if success:
            # 背景任務：更新統計分析
            background_tasks.add_task(
                update_analytics_background,
                tracking_system
            )
            
            return {
                "success": True,
                "message": "預測驗證成功",
                "prediction_id": request.prediction_id,
                "verification_timestamp": datetime.now()
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="預測驗證失敗"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"預測驗證失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"驗證處理失敗: {str(e)}"
        )

@tracking_router.get("/stats/{lottery_type}", response_model=TrackingStatsResponse)
async def get_tracking_stats(lottery_type: str):
    """獲取預測追蹤統計"""
    try:
        tracking_system = tracking_integration.get_tracking_system()
        
        # 獲取儀表板數據
        dashboard_data = tracking_system.get_dashboard_data(lottery_type)
        
        if not dashboard_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到彩票類型 {lottery_type} 的追蹤數據"
            )
        
        # 確定最佳策略
        strategy_performance = dashboard_data.get('strategy_performance', {})
        best_strategy = None
        if strategy_performance:
            best_strategy = max(strategy_performance.items(), key=lambda x: x[1])[0]
        
        # 計算趨勢
        metrics = tracking_system.analyzer.calculate_performance_metrics(lottery_type)
        trend_direction = "上升" if metrics.improvement_trend > 0.01 else "下降" if metrics.improvement_trend < -0.01 else "穩定"
        
        return TrackingStatsResponse(
            lottery_type=lottery_type,
            total_predictions=dashboard_data.get('total_predictions', 0),
            verified_predictions=dashboard_data.get('verified_predictions', 0),
            pending_predictions=dashboard_data.get('pending_predictions', 0),
            accuracy_rate=dashboard_data.get('accuracy_rate', 0),
            average_confidence=dashboard_data.get('average_confidence', 0),
            best_strategy=best_strategy,
            recent_trend=trend_direction
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取追蹤統計失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"統計數據獲取失敗: {str(e)}"
        )

@tracking_router.get("/metrics/{lottery_type}", response_model=PerformanceMetricsResponse)
async def get_performance_metrics(
    lottery_type: str,
    timeframe: AnalysisTimeframe = Query(AnalysisTimeframe.MONTHLY, description="分析時間框架")
):
    """獲取性能指標"""
    try:
        tracking_system = tracking_integration.get_tracking_system()
        
        # 計算性能指標
        metrics = tracking_system.analyzer.calculate_performance_metrics(
            lottery_type, timeframe
        )
        
        return PerformanceMetricsResponse(
            lottery_type=lottery_type,
            timeframe=timeframe.value,
            accuracy_rate=metrics.accuracy_rate,
            average_confidence=metrics.average_confidence,
            confidence_accuracy_correlation=metrics.confidence_accuracy_correlation,
            strategy_success_rates=metrics.strategy_success_rates,
            temporal_consistency=metrics.temporal_consistency,
            improvement_trend=metrics.improvement_trend
        )
        
    except Exception as e:
        logger.error(f"獲取性能指標失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"性能指標計算失敗: {str(e)}"
        )

@tracking_router.get("/reports/{lottery_type}", response_model=StatisticsReportResponse)
async def get_statistics_report(
    lottery_type: str,
    timeframe: AnalysisTimeframe = Query(AnalysisTimeframe.MONTHLY, description="報告時間框架")
):
    """獲取統計報告"""
    try:
        tracking_system = tracking_integration.get_tracking_system()
        
        # 生成統計報告
        report = tracking_system.analyzer.generate_statistics_report(
            lottery_type, timeframe
        )
        
        return StatisticsReportResponse(
            report_id=report.report_id,
            lottery_type=report.lottery_type,
            timeframe=report.timeframe.value,
            start_date=report.start_date,
            end_date=report.end_date,
            total_predictions=report.total_predictions,
            verified_predictions=report.verified_predictions,
            accuracy_metrics=report.accuracy_metrics,
            strategy_performance=report.strategy_performance,
            recommendations=report.recommendations,
            generated_timestamp=report.generated_timestamp
        )
        
    except Exception as e:
        logger.error(f"獲取統計報告失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"統計報告生成失敗: {str(e)}"
        )

@tracking_router.get("/charts/{chart_type}/{lottery_type}", response_model=ChartResponse)
async def get_chart(
    chart_type: str,
    lottery_type: str,
    days: int = Query(30, ge=7, le=365, description="數據天數")
):
    """獲取圖表"""
    try:
        visualizer = tracking_integration.get_visualizer()
        
        # 根據圖表類型生成圖表
        chart_generators = {
            "accuracy_trend": lambda: visualizer.create_accuracy_trend_chart(lottery_type, days),
            "strategy_performance": lambda: visualizer.create_strategy_performance_chart(lottery_type),
            "confidence_scatter": lambda: visualizer.create_confidence_accuracy_scatter(lottery_type),
            "monthly_summary": lambda: visualizer.create_monthly_summary_chart(lottery_type),
            "prize_distribution": lambda: visualizer.create_prize_distribution_chart(lottery_type)
        }
        
        if chart_type not in chart_generators:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的圖表類型: {chart_type}"
            )
        
        # 生成圖表
        chart_data = chart_generators[chart_type]()
        
        if not chart_data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="圖表生成失敗"
            )
        
        return ChartResponse(
            chart_type=chart_type,
            lottery_type=lottery_type,
            chart_data=chart_data,
            generated_timestamp=datetime.now()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"圖表生成失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"圖表生成失敗: {str(e)}"
        )

@tracking_router.get("/reports/{lottery_type}/html", response_class=HTMLResponse)
async def get_html_report(lottery_type: str):
    """獲取HTML格式的綜合報告"""
    try:
        report_generator = tracking_integration.get_report_generator()
        
        # 生成HTML報告
        html_report = report_generator.generate_comprehensive_html_report(lottery_type)
        
        if not html_report:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="HTML報告生成失敗"
            )
        
        return HTMLResponse(content=html_report)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"HTML報告生成失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"HTML報告生成失敗: {str(e)}"
        )

@tracking_router.get("/exports/{lottery_type}/csv")
async def export_csv_data(lottery_type: str):
    """導出CSV格式的預測數據"""
    try:
        report_generator = tracking_integration.get_report_generator()
        
        # 生成CSV文件
        csv_filepath = report_generator.export_data_to_csv(lottery_type)
        
        if not csv_filepath or not os.path.exists(csv_filepath):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="CSV文件生成失敗或無數據"
            )
        
        # 返回文件
        filename = f"{lottery_type}_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        return FileResponse(
            path=csv_filepath,
            filename=filename,
            media_type='text/csv'
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"CSV導出失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"CSV導出失敗: {str(e)}"
        )

@tracking_router.get("/records/{lottery_type}")
async def get_prediction_records(
    lottery_type: str,
    status_filter: Optional[PredictionStatus] = Query(None, description="狀態篩選"),
    limit: int = Query(50, ge=1, le=500, description="記錄數量限制"),
    start_date: Optional[datetime] = Query(None, description="開始日期"),
    end_date: Optional[datetime] = Query(None, description="結束日期")
):
    """獲取預測記錄"""
    try:
        tracking_system = tracking_integration.get_tracking_system()
        
        # 獲取預測記錄
        records = tracking_system.tracker.get_prediction_records(
            lottery_type=lottery_type,
            status=status_filter,
            start_date=start_date,
            end_date=end_date,
            limit=limit
        )
        
        # 轉換為API響應格式
        record_data = []
        for record in records:
            record_data.append({
                "prediction_id": record.prediction_id,
                "lottery_type": record.lottery_type,
                "period": record.period,
                "predicted_main_numbers": record.predicted_main_numbers,
                "predicted_special_numbers": record.predicted_special_numbers,
                "actual_main_numbers": record.actual_main_numbers,
                "actual_special_numbers": record.actual_special_numbers,
                "strategy_used": record.strategy_used,
                "confidence": record.confidence,
                "prediction_timestamp": record.prediction_timestamp,
                "draw_timestamp": record.draw_timestamp,
                "verification_timestamp": record.verification_timestamp,
                "status": record.status.value,
                "match_count": record.match_count,
                "special_match": record.special_match,
                "prize_level": record.prize_level
            })
        
        return {
            "lottery_type": lottery_type,
            "total_records": len(record_data),
            "records": record_data,
            "query_timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"獲取預測記錄失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"預測記錄查詢失敗: {str(e)}"
        )

@tracking_router.delete("/records/{prediction_id}")
async def delete_prediction_record(prediction_id: str):
    """刪除預測記錄"""
    try:
        tracking_system = tracking_integration.get_tracking_system()
        
        # 實現記錄刪除邏輯
        # 注意：這裡需要添加實際的刪除實現
        
        return {
            "success": True,
            "message": f"預測記錄 {prediction_id} 已刪除",
            "deleted_timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"刪除預測記錄失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"記錄刪除失敗: {str(e)}"
        )

# === 背景任務 ===

async def update_analytics_background(tracking_system: IntegratedTrackingSystem):
    """背景任務：更新分析統計"""
    try:
        # 標記過期預測
        tracking_system.tracker.mark_expired_predictions()
        
        # 可以添加其他後台分析任務
        logger.info("✅ 背景分析任務完成")
        
    except Exception as e:
        logger.error(f"❌ 背景分析任務失敗: {e}")

async def record_prediction_to_tracking(
    tracking_system: IntegratedTrackingSystem,
    prediction_result,
    metadata: Optional[Dict[str, Any]] = None
):
    """背景任務：記錄預測到追蹤系統"""
    try:
        success = tracking_system.tracker.record_prediction(
            prediction_id=prediction_result.prediction_id,
            lottery_type=prediction_result.lottery_type,
            predicted_main=prediction_result.main_numbers,
            predicted_special=prediction_result.special_numbers,
            strategy=prediction_result.strategy_used,
            confidence=prediction_result.confidence,
            algorithm_weights={},  # 可以從 metadata 中提取
            metadata=metadata or {}
        )
        
        if success:
            logger.info(f"✅ 預測已記錄到追蹤系統: {prediction_result.prediction_id}")
        else:
            logger.error(f"❌ 預測記錄失敗: {prediction_result.prediction_id}")
            
    except Exception as e:
        logger.error(f"❌ 預測記錄到追蹤系統失敗: {e}")

# === 初始化函數 ===

async def initialize_tracking_integration(db_manager):
    """初始化追蹤系統集成"""
    await tracking_integration.initialize(db_manager)

def get_tracking_router() -> APIRouter:
    """獲取追蹤系統路由器"""
    return tracking_router

def get_tracking_integration() -> WebAPITrackingIntegration:
    """獲取追蹤系統集成實例"""
    return tracking_integration