#!/usr/bin/env python3
"""
Phase 3.6 生產環境部署和監控系統
提供完整的生產環境部署、監控、日誌管理和運維功能
"""

import os
import sys
import json
import yaml
import psutil
import asyncio
import logging
import threading
import subprocess
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import socket
import time
import shutil
import sqlite3
from concurrent.futures import ThreadPoolExecutor
import requests

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/production.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# === 數據模型定義 ===

class ServiceStatus(Enum):
    """服務狀態枚舉"""
    RUNNING = "running"
    STOPPED = "stopped"
    ERROR = "error"
    STARTING = "starting"
    STOPPING = "stopping"

class AlertLevel(Enum):
    """警報級別枚舉"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class DeploymentMode(Enum):
    """部署模式枚舉"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"

@dataclass
class SystemMetrics:
    """系統指標數據結構"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_io: Dict[str, int]
    process_count: int
    load_average: List[float]
    temperature: Optional[float] = None

@dataclass
class ServiceHealth:
    """服務健康狀態"""
    service_name: str
    status: ServiceStatus
    response_time: Optional[float]
    last_check: datetime
    error_message: Optional[str]
    uptime: Optional[timedelta]
    memory_usage: Optional[float]
    cpu_usage: Optional[float]

@dataclass
class Alert:
    """警報數據結構"""
    alert_id: str
    alert_type: str
    level: AlertLevel
    message: str
    timestamp: datetime
    service_name: Optional[str]
    resolved: bool
    resolution_time: Optional[datetime]
    metadata: Dict[str, Any]

@dataclass
class DeploymentConfig:
    """部署配置"""
    mode: DeploymentMode
    host: str
    port: int
    workers: int
    max_requests: int
    timeout: int
    ssl_enabled: bool
    ssl_cert_path: Optional[str]
    ssl_key_path: Optional[str]
    database_url: str
    redis_url: Optional[str]
    log_level: str
    backup_enabled: bool
    monitoring_enabled: bool

# === 系統監控器 ===

class SystemMonitor:
    """系統監控器"""
    
    def __init__(self, config: DeploymentConfig):
        self.config = config
        self.metrics_history: List[SystemMetrics] = []
        self.max_history = 1440  # 24小時（每分鐘一條記錄）
        self.monitoring_active = False
        self.alert_thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_percent': 90.0,
            'response_time': 5.0
        }
        
    def start_monitoring(self):
        """開始監控"""
        self.monitoring_active = True
        logger.info("🔍 系統監控已啟動")
        
        # 啟動監控線程
        monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        monitor_thread.start()
        
    def stop_monitoring(self):
        """停止監控"""
        self.monitoring_active = False
        logger.info("🛑 系統監控已停止")
        
    def _monitoring_loop(self):
        """監控循環"""
        while self.monitoring_active:
            try:
                metrics = self._collect_system_metrics()
                self.metrics_history.append(metrics)
                
                # 保持歷史記錄數量
                if len(self.metrics_history) > self.max_history:
                    self.metrics_history.pop(0)
                
                # 檢查警報條件
                self._check_alert_conditions(metrics)
                
                # 每分鐘檢查一次
                time.sleep(60)
                
            except Exception as e:
                logger.error(f"❌ 監控循環錯誤: {e}")
                time.sleep(30)  # 錯誤時減少頻率
    
    def _collect_system_metrics(self) -> SystemMetrics:
        """收集系統指標"""
        try:
            # CPU 使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 內存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁盤使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # 網絡IO
            network = psutil.net_io_counters()
            network_io = {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            }
            
            # 進程數量
            process_count = len(psutil.pids())
            
            # 負載平均值
            try:
                load_average = list(psutil.getloadavg())
            except AttributeError:
                # Windows 不支持 getloadavg
                load_average = [0.0, 0.0, 0.0]
            
            # 溫度（如果可用）
            temperature = None
            try:
                temps = psutil.sensors_temperatures()
                if temps:
                    # 取第一個傳感器的溫度
                    first_sensor = next(iter(temps.values()))
                    if first_sensor:
                        temperature = first_sensor[0].current
            except:
                pass
            
            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                disk_percent=disk_percent,
                network_io=network_io,
                process_count=process_count,
                load_average=load_average,
                temperature=temperature
            )
            
        except Exception as e:
            logger.error(f"❌ 系統指標收集失敗: {e}")
            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=0.0,
                memory_percent=0.0,
                disk_percent=0.0,
                network_io={},
                process_count=0,
                load_average=[0.0, 0.0, 0.0]
            )
    
    def _check_alert_conditions(self, metrics: SystemMetrics):
        """檢查警報條件"""
        alerts = []
        
        # CPU 使用率警報
        if metrics.cpu_percent > self.alert_thresholds['cpu_percent']:
            alerts.append(Alert(
                alert_id=f"cpu_{int(time.time())}",
                alert_type="high_cpu_usage",
                level=AlertLevel.WARNING if metrics.cpu_percent < 95 else AlertLevel.CRITICAL,
                message=f"CPU 使用率過高: {metrics.cpu_percent:.1f}%",
                timestamp=metrics.timestamp,
                service_name="system",
                resolved=False,
                resolution_time=None,
                metadata={"cpu_percent": metrics.cpu_percent}
            ))
        
        # 內存使用率警報
        if metrics.memory_percent > self.alert_thresholds['memory_percent']:
            alerts.append(Alert(
                alert_id=f"memory_{int(time.time())}",
                alert_type="high_memory_usage",
                level=AlertLevel.WARNING if metrics.memory_percent < 95 else AlertLevel.CRITICAL,
                message=f"內存使用率過高: {metrics.memory_percent:.1f}%",
                timestamp=metrics.timestamp,
                service_name="system",
                resolved=False,
                resolution_time=None,
                metadata={"memory_percent": metrics.memory_percent}
            ))
        
        # 磁盤使用率警報
        if metrics.disk_percent > self.alert_thresholds['disk_percent']:
            alerts.append(Alert(
                alert_id=f"disk_{int(time.time())}",
                alert_type="high_disk_usage",
                level=AlertLevel.CRITICAL,
                message=f"磁盤使用率過高: {metrics.disk_percent:.1f}%",
                timestamp=metrics.timestamp,
                service_name="system",
                resolved=False,
                resolution_time=None,
                metadata={"disk_percent": metrics.disk_percent}
            ))
        
        # 處理警報
        for alert in alerts:
            self._handle_alert(alert)
    
    def _handle_alert(self, alert: Alert):
        """處理警報"""
        logger.warning(f"🚨 {alert.level.value.upper()}: {alert.message}")
        
        # 這裡可以添加其他警報處理邏輯
        # 例如：發送郵件、短信、推送通知等
    
    def get_current_metrics(self) -> Optional[SystemMetrics]:
        """獲取當前系統指標"""
        if self.metrics_history:
            return self.metrics_history[-1]
        return None
    
    def get_metrics_summary(self, hours: int = 24) -> Dict[str, Any]:
        """獲取指標摘要"""
        if not self.metrics_history:
            return {}
        
        # 獲取指定時間範圍內的指標
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_metrics = [m for m in self.metrics_history if m.timestamp >= cutoff_time]
        
        if not recent_metrics:
            return {}
        
        # 計算統計信息
        cpu_values = [m.cpu_percent for m in recent_metrics]
        memory_values = [m.memory_percent for m in recent_metrics]
        disk_values = [m.disk_percent for m in recent_metrics]
        
        return {
            'period_hours': hours,
            'data_points': len(recent_metrics),
            'cpu': {
                'current': cpu_values[-1],
                'average': sum(cpu_values) / len(cpu_values),
                'max': max(cpu_values),
                'min': min(cpu_values)
            },
            'memory': {
                'current': memory_values[-1],
                'average': sum(memory_values) / len(memory_values),
                'max': max(memory_values),
                'min': min(memory_values)
            },
            'disk': {
                'current': disk_values[-1],
                'average': sum(disk_values) / len(disk_values),
                'max': max(disk_values),
                'min': min(disk_values)
            }
        }

# === 服務健康檢查器 ===

class HealthChecker:
    """服務健康檢查器"""
    
    def __init__(self, config: DeploymentConfig):
        self.config = config
        self.services: Dict[str, ServiceHealth] = {}
        self.check_interval = 30  # 30秒檢查一次
        self.checking_active = False
        
        # 定義需要檢查的服務
        self.service_endpoints = {
            'web_api': f"http://{config.host}:{config.port}/health",
            'database': None,  # 數據庫連接檢查
            'file_system': None,  # 文件系統檢查
        }
        
    def start_health_checks(self):
        """開始健康檢查"""
        self.checking_active = True
        logger.info("🏥 健康檢查已啟動")
        
        # 啟動檢查線程
        check_thread = threading.Thread(target=self._health_check_loop, daemon=True)
        check_thread.start()
        
    def stop_health_checks(self):
        """停止健康檢查"""
        self.checking_active = False
        logger.info("🛑 健康檢查已停止")
        
    def _health_check_loop(self):
        """健康檢查循環"""
        while self.checking_active:
            try:
                for service_name, endpoint in self.service_endpoints.items():
                    health = self._check_service_health(service_name, endpoint)
                    self.services[service_name] = health
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"❌ 健康檢查循環錯誤: {e}")
                time.sleep(60)
    
    def _check_service_health(self, service_name: str, endpoint: Optional[str]) -> ServiceHealth:
        """檢查單個服務健康狀態"""
        start_time = time.time()
        
        try:
            if service_name == 'web_api' and endpoint:
                # Web API 健康檢查
                response = requests.get(endpoint, timeout=10)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    status = ServiceStatus.RUNNING
                    error_message = None
                else:
                    status = ServiceStatus.ERROR
                    error_message = f"HTTP {response.status_code}"
                    
            elif service_name == 'database':
                # 數據庫連接檢查
                try:
                    conn = sqlite3.connect(self.config.database_url, timeout=5)
                    conn.execute("SELECT 1")
                    conn.close()
                    status = ServiceStatus.RUNNING
                    error_message = None
                    response_time = time.time() - start_time
                except Exception as e:
                    status = ServiceStatus.ERROR
                    error_message = str(e)
                    response_time = None
                    
            elif service_name == 'file_system':
                # 文件系統檢查
                try:
                    # 檢查磁盤空間
                    disk_usage = psutil.disk_usage('/')
                    free_space_gb = disk_usage.free / (1024**3)
                    
                    if free_space_gb < 1.0:  # 少於1GB
                        status = ServiceStatus.ERROR
                        error_message = f"磁盤空間不足: {free_space_gb:.1f}GB"
                    else:
                        status = ServiceStatus.RUNNING
                        error_message = None
                    
                    response_time = time.time() - start_time
                except Exception as e:
                    status = ServiceStatus.ERROR
                    error_message = str(e)
                    response_time = None
                    
            else:
                status = ServiceStatus.ERROR
                error_message = "未知服務類型"
                response_time = None
            
            # 獲取進程信息
            memory_usage, cpu_usage = self._get_process_usage(service_name)
            
            return ServiceHealth(
                service_name=service_name,
                status=status,
                response_time=response_time,
                last_check=datetime.now(),
                error_message=error_message,
                uptime=self._calculate_uptime(service_name),
                memory_usage=memory_usage,
                cpu_usage=cpu_usage
            )
            
        except Exception as e:
            return ServiceHealth(
                service_name=service_name,
                status=ServiceStatus.ERROR,
                response_time=None,
                last_check=datetime.now(),
                error_message=str(e),
                uptime=None,
                memory_usage=None,
                cpu_usage=None
            )
    
    def _get_process_usage(self, service_name: str) -> Tuple[Optional[float], Optional[float]]:
        """獲取進程資源使用情況"""
        try:
            current_process = psutil.Process()
            memory_usage = current_process.memory_percent()
            cpu_usage = current_process.cpu_percent()
            return memory_usage, cpu_usage
        except:
            return None, None
    
    def _calculate_uptime(self, service_name: str) -> Optional[timedelta]:
        """計算服務運行時間"""
        try:
            current_process = psutil.Process()
            create_time = datetime.fromtimestamp(current_process.create_time())
            uptime = datetime.now() - create_time
            return uptime
        except:
            return None
    
    def get_service_status(self, service_name: str) -> Optional[ServiceHealth]:
        """獲取服務狀態"""
        return self.services.get(service_name)
    
    def get_all_services_status(self) -> Dict[str, ServiceHealth]:
        """獲取所有服務狀態"""
        return self.services.copy()

# === 日誌管理器 ===

class LogManager:
    """日誌管理器"""
    
    def __init__(self, config: DeploymentConfig):
        self.config = config
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        # 日誌文件配置
        self.log_files = {
            'application': self.log_dir / 'application.log',
            'access': self.log_dir / 'access.log',
            'error': self.log_dir / 'error.log',
            'security': self.log_dir / 'security.log',
            'performance': self.log_dir / 'performance.log'
        }
        
        # 日誌輪轉配置
        self.max_file_size = 100 * 1024 * 1024  # 100MB
        self.max_backup_count = 10
        
    def setup_logging(self):
        """設置日誌配置"""
        try:
            # 創建格式化器
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            
            # 設置根日誌記錄器
            root_logger = logging.getLogger()
            root_logger.setLevel(getattr(logging, self.config.log_level.upper()))
            
            # 清除現有處理器
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
            
            # 添加文件處理器
            from logging.handlers import RotatingFileHandler
            
            file_handler = RotatingFileHandler(
                self.log_files['application'],
                maxBytes=self.max_file_size,
                backupCount=self.max_backup_count
            )
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
            
            # 添加控制台處理器
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
            
            logger.info("✅ 日誌系統配置完成")
            
        except Exception as e:
            print(f"❌ 日誌系統配置失敗: {e}")
    
    def rotate_logs(self):
        """手動輪轉日誌"""
        try:
            for log_name, log_path in self.log_files.items():
                if log_path.exists() and log_path.stat().st_size > self.max_file_size:
                    # 創建備份
                    backup_path = log_path.with_suffix(f".{int(time.time())}.bak")
                    shutil.move(str(log_path), str(backup_path))
                    
                    logger.info(f"✅ 日誌輪轉完成: {log_name}")
                    
                    # 清理舊備份
                    self._cleanup_old_backups(log_path)
                    
        except Exception as e:
            logger.error(f"❌ 日誌輪轉失敗: {e}")
    
    def _cleanup_old_backups(self, log_path: Path):
        """清理舊的備份文件"""
        try:
            backup_pattern = f"{log_path.stem}.*.bak"
            backup_files = list(log_path.parent.glob(backup_pattern))
            
            # 按修改時間排序，保留最新的N個
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            for old_backup in backup_files[self.max_backup_count:]:
                old_backup.unlink()
                logger.info(f"🗑️  清理舊備份: {old_backup.name}")
                
        except Exception as e:
            logger.error(f"❌ 清理備份失敗: {e}")
    
    def get_log_stats(self) -> Dict[str, Any]:
        """獲取日誌統計信息"""
        stats = {}
        
        for log_name, log_path in self.log_files.items():
            if log_path.exists():
                stat = log_path.stat()
                stats[log_name] = {
                    'size_mb': stat.st_size / (1024 * 1024),
                    'modified': datetime.fromtimestamp(stat.st_mtime),
                    'lines': self._count_log_lines(log_path)
                }
            else:
                stats[log_name] = {
                    'size_mb': 0,
                    'modified': None,
                    'lines': 0
                }
        
        return stats
    
    def _count_log_lines(self, log_path: Path) -> int:
        """計算日誌文件行數"""
        try:
            with open(log_path, 'r', encoding='utf-8') as f:
                return sum(1 for _ in f)
        except:
            return 0

# === 備份管理器 ===

class BackupManager:
    """備份管理器"""
    
    def __init__(self, config: DeploymentConfig):
        self.config = config
        self.backup_dir = Path("backups")
        self.backup_dir.mkdir(exist_ok=True)
        self.max_backups = 30  # 保留30個備份
        
    def create_backup(self) -> str:
        """創建系統備份"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"lottery_system_backup_{timestamp}"
            backup_path = self.backup_dir / backup_name
            backup_path.mkdir(exist_ok=True)
            
            logger.info(f"🔄 開始創建備份: {backup_name}")
            
            # 備份數據庫
            self._backup_database(backup_path)
            
            # 備份配置文件
            self._backup_configs(backup_path)
            
            # 備份日誌
            self._backup_logs(backup_path)
            
            # 備份模型和數據
            self._backup_models_data(backup_path)
            
            # 創建備份信息文件
            self._create_backup_info(backup_path, timestamp)
            
            # 壓縮備份
            archive_path = self._compress_backup(backup_path)
            
            # 清理舊備份
            self._cleanup_old_backups()
            
            logger.info(f"✅ 備份創建完成: {archive_path}")
            return str(archive_path)
            
        except Exception as e:
            logger.error(f"❌ 備份創建失敗: {e}")
            return ""
    
    def _backup_database(self, backup_path: Path):
        """備份數據庫"""
        try:
            db_backup_dir = backup_path / "database"
            db_backup_dir.mkdir(exist_ok=True)
            
            # 複製數據庫文件
            data_dir = Path("data")
            if data_dir.exists():
                shutil.copytree(data_dir, db_backup_dir / "data", dirs_exist_ok=True)
            
            logger.info("✅ 數據庫備份完成")
            
        except Exception as e:
            logger.error(f"❌ 數據庫備份失敗: {e}")
    
    def _backup_configs(self, backup_path: Path):
        """備份配置文件"""
        try:
            config_backup_dir = backup_path / "configs"
            config_backup_dir.mkdir(exist_ok=True)
            
            # 配置文件列表
            config_files = [
                "config/superclaude.yaml",
                "requirements.txt",
                "requirements_web.txt",
                "phase3/WEB_INTERFACE_README.md"
            ]
            
            for config_file in config_files:
                config_path = Path(config_file)
                if config_path.exists():
                    dest_path = config_backup_dir / config_path.name
                    shutil.copy2(config_path, dest_path)
            
            logger.info("✅ 配置文件備份完成")
            
        except Exception as e:
            logger.error(f"❌ 配置文件備份失敗: {e}")
    
    def _backup_logs(self, backup_path: Path):
        """備份日誌文件"""
        try:
            log_backup_dir = backup_path / "logs"
            log_backup_dir.mkdir(exist_ok=True)
            
            logs_dir = Path("logs")
            if logs_dir.exists():
                for log_file in logs_dir.glob("*.log"):
                    dest_path = log_backup_dir / log_file.name
                    shutil.copy2(log_file, dest_path)
            
            logger.info("✅ 日誌文件備份完成")
            
        except Exception as e:
            logger.error(f"❌ 日誌文件備份失敗: {e}")
    
    def _backup_models_data(self, backup_path: Path):
        """備份模型和數據文件"""
        try:
            models_backup_dir = backup_path / "models"
            models_backup_dir.mkdir(exist_ok=True)
            
            # 備份模型文件（如果存在）
            model_dirs = ["models", "prediction", "phase3"]
            for model_dir in model_dirs:
                model_path = Path(model_dir)
                if model_path.exists() and model_path.is_dir():
                    dest_path = models_backup_dir / model_dir
                    shutil.copytree(model_path, dest_path, dirs_exist_ok=True)
            
            logger.info("✅ 模型數據備份完成")
            
        except Exception as e:
            logger.error(f"❌ 模型數據備份失敗: {e}")
    
    def _create_backup_info(self, backup_path: Path, timestamp: str):
        """創建備份信息文件"""
        try:
            info = {
                'backup_time': timestamp,
                'system_version': '3.6.0',
                'python_version': sys.version,
                'platform': sys.platform,
                'backup_components': [
                    'database',
                    'configs', 
                    'logs',
                    'models'
                ]
            }
            
            info_file = backup_path / "backup_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(info, f, indent=2, ensure_ascii=False)
            
            logger.info("✅ 備份信息文件創建完成")
            
        except Exception as e:
            logger.error(f"❌ 備份信息文件創建失敗: {e}")
    
    def _compress_backup(self, backup_path: Path) -> Path:
        """壓縮備份文件"""
        try:
            archive_path = backup_path.with_suffix('.tar.gz')
            
            import tarfile
            with tarfile.open(archive_path, 'w:gz') as tar:
                tar.add(backup_path, arcname=backup_path.name)
            
            # 刪除原始目錄
            shutil.rmtree(backup_path)
            
            logger.info(f"✅ 備份壓縮完成: {archive_path}")
            return archive_path
            
        except Exception as e:
            logger.error(f"❌ 備份壓縮失敗: {e}")
            return backup_path
    
    def _cleanup_old_backups(self):
        """清理舊備份"""
        try:
            backup_files = list(self.backup_dir.glob("lottery_system_backup_*.tar.gz"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            for old_backup in backup_files[self.max_backups:]:
                old_backup.unlink()
                logger.info(f"🗑️  清理舊備份: {old_backup.name}")
                
        except Exception as e:
            logger.error(f"❌ 清理舊備份失敗: {e}")
    
    def restore_backup(self, backup_path: str) -> bool:
        """恢復備份"""
        try:
            logger.info(f"🔄 開始恢復備份: {backup_path}")
            
            backup_file = Path(backup_path)
            if not backup_file.exists():
                logger.error(f"❌ 備份文件不存在: {backup_path}")
                return False
            
            # 解壓備份
            import tarfile
            with tarfile.open(backup_file, 'r:gz') as tar:
                tar.extractall(self.backup_dir)
            
            # 恢復各個組件
            backup_name = backup_file.stem.replace('.tar', '')
            extracted_path = self.backup_dir / backup_name
            
            # 恢復數據庫
            self._restore_database(extracted_path)
            
            # 恢復配置文件
            self._restore_configs(extracted_path)
            
            logger.info("✅ 備份恢復完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 備份恢復失敗: {e}")
            return False
    
    def _restore_database(self, backup_path: Path):
        """恢復數據庫"""
        try:
            db_backup_path = backup_path / "database" / "data"
            if db_backup_path.exists():
                data_dir = Path("data")
                if data_dir.exists():
                    shutil.rmtree(data_dir)
                shutil.copytree(db_backup_path, data_dir)
            
            logger.info("✅ 數據庫恢復完成")
            
        except Exception as e:
            logger.error(f"❌ 數據庫恢復失敗: {e}")
    
    def _restore_configs(self, backup_path: Path):
        """恢復配置文件"""
        try:
            config_backup_path = backup_path / "configs"
            if config_backup_path.exists():
                for config_file in config_backup_path.glob("*"):
                    if config_file.is_file():
                        dest_path = Path(config_file.name)
                        shutil.copy2(config_file, dest_path)
            
            logger.info("✅ 配置文件恢復完成")
            
        except Exception as e:
            logger.error(f"❌ 配置文件恢復失敗: {e}")
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """列出所有備份"""
        backups = []
        
        try:
            backup_files = list(self.backup_dir.glob("lottery_system_backup_*.tar.gz"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            for backup_file in backup_files:
                stat = backup_file.stat()
                backups.append({
                    'name': backup_file.name,
                    'path': str(backup_file),
                    'size_mb': stat.st_size / (1024 * 1024),
                    'created': datetime.fromtimestamp(stat.st_mtime),
                    'age_days': (datetime.now() - datetime.fromtimestamp(stat.st_mtime)).days
                })
                
        except Exception as e:
            logger.error(f"❌ 列出備份失敗: {e}")
        
        return backups

# === 部署管理器 ===

class DeploymentManager:
    """部署管理器"""
    
    def __init__(self, config: DeploymentConfig):
        self.config = config
        self.system_monitor = SystemMonitor(config)
        self.health_checker = HealthChecker(config)
        self.log_manager = LogManager(config)
        self.backup_manager = BackupManager(config)
        self.executor = ThreadPoolExecutor(max_workers=4)
        
    async def deploy_system(self) -> bool:
        """部署系統"""
        try:
            logger.info(f"🚀 開始部署系統 ({self.config.mode.value} 模式)")
            
            # 1. 設置日誌
            self.log_manager.setup_logging()
            
            # 2. 檢查系統依賴
            if not await self._check_system_dependencies():
                return False
            
            # 3. 創建部署前備份
            if self.config.backup_enabled:
                backup_path = self.backup_manager.create_backup()
                if backup_path:
                    logger.info(f"✅ 部署前備份完成: {backup_path}")
            
            # 4. 配置系統環境
            await self._configure_system_environment()
            
            # 5. 啟動監控系統
            if self.config.monitoring_enabled:
                self.system_monitor.start_monitoring()
                self.health_checker.start_health_checks()
            
            # 6. 部署應用服務
            if not await self._deploy_application_services():
                return False
            
            # 7. 運行部署後檢查
            if not await self._post_deployment_checks():
                return False
            
            logger.info("🎉 系統部署完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 系統部署失敗: {e}")
            return False
    
    async def _check_system_dependencies(self) -> bool:
        """檢查系統依賴"""
        try:
            logger.info("🔍 檢查系統依賴...")
            
            # 檢查 Python 版本
            python_version = sys.version_info
            if python_version < (3, 8):
                logger.error("❌ Python 版本過低，需要 3.8 或更高版本")
                return False
            
            # 檢查必要的包
            required_packages = [
                'fastapi', 'uvicorn', 'psutil', 'requests',
                'pandas', 'numpy', 'matplotlib', 'seaborn'
            ]
            
            missing_packages = []
            for package in required_packages:
                try:
                    __import__(package)
                except ImportError:
                    missing_packages.append(package)
            
            if missing_packages:
                logger.error(f"❌ 缺少必要的包: {missing_packages}")
                return False
            
            # 檢查磁盤空間
            disk_usage = psutil.disk_usage('/')
            free_space_gb = disk_usage.free / (1024**3)
            
            if free_space_gb < 2.0:  # 至少需要2GB空間
                logger.error(f"❌ 磁盤空間不足: {free_space_gb:.1f}GB")
                return False
            
            # 檢查端口可用性
            if not self._check_port_available(self.config.port):
                logger.error(f"❌ 端口 {self.config.port} 已被占用")
                return False
            
            logger.info("✅ 系統依賴檢查通過")
            return True
            
        except Exception as e:
            logger.error(f"❌ 系統依賴檢查失敗: {e}")
            return False
    
    def _check_port_available(self, port: int) -> bool:
        """檢查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return True
        except OSError:
            return False
    
    async def _configure_system_environment(self):
        """配置系統環境"""
        try:
            logger.info("⚙️  配置系統環境...")
            
            # 創建必要的目錄
            directories = ['logs', 'data', 'backups', 'reports', 'exports']
            for directory in directories:
                Path(directory).mkdir(exist_ok=True)
            
            # 設置環境變量
            os.environ['LOTTERY_SYSTEM_MODE'] = self.config.mode.value
            os.environ['LOTTERY_SYSTEM_LOG_LEVEL'] = self.config.log_level
            
            # 配置數據庫
            await self._setup_database()
            
            logger.info("✅ 系統環境配置完成")
            
        except Exception as e:
            logger.error(f"❌ 系統環境配置失敗: {e}")
            raise
    
    async def _setup_database(self):
        """設置數據庫"""
        try:
            # 這裡可以添加數據庫初始化邏輯
            # 例如：創建表、運行遷移等
            logger.info("✅ 數據庫設置完成")
            
        except Exception as e:
            logger.error(f"❌ 數據庫設置失敗: {e}")
            raise
    
    async def _deploy_application_services(self) -> bool:
        """部署應用服務"""
        try:
            logger.info("🚀 部署應用服務...")
            
            # 這裡可以添加服務部署邏輯
            # 例如：啟動 Web API、調度器等
            
            logger.info("✅ 應用服務部署完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 應用服務部署失敗: {e}")
            return False
    
    async def _post_deployment_checks(self) -> bool:
        """部署後檢查"""
        try:
            logger.info("🔍 執行部署後檢查...")
            
            # 等待服務啟動
            await asyncio.sleep(5)
            
            # 檢查服務健康狀態
            max_retries = 10
            for i in range(max_retries):
                if self._check_services_health():
                    logger.info("✅ 服務健康檢查通過")
                    return True
                
                if i < max_retries - 1:
                    logger.info(f"⏳ 等待服務啟動... ({i+1}/{max_retries})")
                    await asyncio.sleep(5)
            
            logger.error("❌ 服務健康檢查失敗")
            return False
            
        except Exception as e:
            logger.error(f"❌ 部署後檢查失敗: {e}")
            return False
    
    def _check_services_health(self) -> bool:
        """檢查服務健康狀態"""
        try:
            # 檢查 Web API
            api_url = f"http://{self.config.host}:{self.config.port}/health"
            response = requests.get(api_url, timeout=10)
            
            if response.status_code == 200:
                return True
            else:
                logger.warning(f"⚠️ API 健康檢查失敗: {response.status_code}")
                return False
                
        except Exception as e:
            logger.warning(f"⚠️ API 健康檢查異常: {e}")
            return False
    
    def get_deployment_status(self) -> Dict[str, Any]:
        """獲取部署狀態"""
        try:
            # 獲取系統指標
            current_metrics = self.system_monitor.get_current_metrics()
            metrics_summary = self.system_monitor.get_metrics_summary()
            
            # 獲取服務狀態
            services_status = self.health_checker.get_all_services_status()
            
            # 獲取日誌統計
            log_stats = self.log_manager.get_log_stats()
            
            # 獲取備份信息
            backups = self.backup_manager.list_backups()
            
            return {
                'deployment_mode': self.config.mode.value,
                'system_metrics': asdict(current_metrics) if current_metrics else None,
                'metrics_summary': metrics_summary,
                'services_status': {
                    name: asdict(status) for name, status in services_status.items()
                },
                'log_stats': log_stats,
                'backup_count': len(backups),
                'latest_backup': backups[0] if backups else None,
                'uptime': self._calculate_system_uptime(),
                'last_updated': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"❌ 獲取部署狀態失敗: {e}")
            return {}
    
    def _calculate_system_uptime(self) -> Optional[str]:
        """計算系統運行時間"""
        try:
            boot_time = psutil.boot_time()
            uptime_seconds = time.time() - boot_time
            uptime = timedelta(seconds=uptime_seconds)
            
            days = uptime.days
            hours, remainder = divmod(uptime.seconds, 3600)
            minutes, _ = divmod(remainder, 60)
            
            return f"{days}天 {hours}小時 {minutes}分鐘"
            
        except:
            return None
    
    async def shutdown_system(self):
        """關閉系統"""
        try:
            logger.info("🛑 開始關閉系統...")
            
            # 停止監控
            if self.config.monitoring_enabled:
                self.system_monitor.stop_monitoring()
                self.health_checker.stop_health_checks()
            
            # 創建關閉前備份
            if self.config.backup_enabled:
                backup_path = self.backup_manager.create_backup()
                if backup_path:
                    logger.info(f"✅ 關閉前備份完成: {backup_path}")
            
            # 清理資源
            self.executor.shutdown(wait=True)
            
            logger.info("✅ 系統關閉完成")
            
        except Exception as e:
            logger.error(f"❌ 系統關閉失敗: {e}")

if __name__ == "__main__":
    # 測試示例
    async def test_deployment_system():
        config = DeploymentConfig(
            mode=DeploymentMode.DEVELOPMENT,
            host="localhost",
            port=8000,
            workers=4,
            max_requests=1000,
            timeout=30,
            ssl_enabled=False,
            ssl_cert_path=None,
            ssl_key_path=None,
            database_url="data/lottery_prediction.db",
            redis_url=None,
            log_level="INFO",
            backup_enabled=True,
            monitoring_enabled=True
        )
        
        deployment_manager = DeploymentManager(config)
        
        # 部署系統
        success = await deployment_manager.deploy_system()
        
        if success:
            print("✅ 部署系統測試完成")
            
            # 等待一段時間查看狀態
            await asyncio.sleep(10)
            
            # 獲取部署狀態
            status = deployment_manager.get_deployment_status()
            print(f"📊 部署狀態: {json.dumps(status, indent=2, default=str)}")
            
            # 關閉系統
            await deployment_manager.shutdown_system()
        else:
            print("❌ 部署系統測試失敗")
    
    asyncio.run(test_deployment_system())