<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>彩票預測系統 - 智能分析平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
        }
        
        body {
            background-color: #f8fafc;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            border: none;
            border-radius: 8px;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #1e40af, #1e3a8a);
        }
        
        .number-ball {
            display: inline-block;
            width: 40px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            border-radius: 50%;
            font-weight: bold;
            color: white;
            margin: 2px;
        }
        
        .main-number {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
        }
        
        .special-number {
            background: linear-gradient(135deg, var(--danger-color), #b91c1c);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-online { background-color: var(--success-color); }
        .status-offline { background-color: var(--danger-color); }
        .status-warning { background-color: var(--warning-color); }
        
        .loading-spinner {
            display: none;
        }
        
        .prediction-card {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-left: 4px solid var(--primary-color);
        }
        
        .confidence-bar {
            height: 8px;
            border-radius: 4px;
            background-color: #e5e7eb;
            overflow: hidden;
        }
        
        .confidence-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.5s ease;
        }
        
        .confidence-high { background: linear-gradient(90deg, var(--success-color), #34d399); }
        .confidence-medium { background: linear-gradient(90deg, var(--warning-color), #fbbf24); }
        .confidence-low { background: linear-gradient(90deg, var(--danger-color), #f87171); }
        
        .lottery-selector {
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .lottery-selector:hover {
            background-color: #e2e8f0;
        }
        
        .lottery-selector.active {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            color: white;
        }
        
        #websocket-status {
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-dice-six me-2"></i>
                彩票預測系統
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item">
                    <span id="websocket-status" class="nav-link">
                        <span class="status-indicator status-offline"></span>
                        連接狀態: 離線
                    </span>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 系統狀態卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-primary">
                            <i class="fas fa-heartbeat"></i>
                            系統狀態
                        </h5>
                        <h3 id="system-status" class="text-success">正常</h3>
                        <small class="text-muted" id="system-uptime">運行時間: --</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-info">
                            <i class="fas fa-chart-line"></i>
                            活躍預測
                        </h5>
                        <h3 id="active-predictions" class="text-info">0</h3>
                        <small class="text-muted">當前處理中</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-warning">
                            <i class="fas fa-database"></i>
                            總預測數
                        </h5>
                        <h3 id="total-predictions" class="text-warning">0</h3>
                        <small class="text-muted">歷史記錄</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success">
                            <i class="fas fa-percentage"></i>
                            成功率
                        </h5>
                        <h3 id="success-rate" class="text-success">--</h3>
                        <small class="text-muted">平均準確度</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 左側：預測控制面板 -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-magic"></i>
                            智能預測
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- 彩票類型選擇 -->
                        <div class="mb-3">
                            <label class="form-label">選擇彩票類型:</label>
                            <div id="lottery-types" class="row g-2">
                                <!-- 動態載入彩票類型 -->
                            </div>
                        </div>

                        <!-- 預測選項 -->
                        <div class="row">
                            <div class="col-md-6">
                                <label for="strategy-select" class="form-label">預測策略:</label>
                                <select id="strategy-select" class="form-select">
                                    <option value="ensemble">智能集成</option>
                                    <option value="frequency_based">頻率分析</option>
                                    <option value="pattern_based">模式分析</option>
                                    <option value="trend_analysis">趨勢分析</option>
                                    <option value="neural_network">神經網絡</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="ensemble-size" class="form-label">集成大小:</label>
                                <input type="number" id="ensemble-size" class="form-control" 
                                       value="10" min="1" max="50">
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           id="cross-learning" checked>
                                    <label class="form-check-label" for="cross-learning">
                                        啟用跨彩票學習
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="data-window" class="form-label">數據窗口:</label>
                                <input type="number" id="data-window" class="form-control" 
                                       value="100" min="10" max="500">
                            </div>
                        </div>

                        <!-- 預測按鈕 -->
                        <div class="mt-4">
                            <button id="predict-btn" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-wand-magic-sparkles me-2"></i>
                                開始預測
                                <div class="loading-spinner spinner-border spinner-border-sm ms-2" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </button>
                        </div>

                        <!-- 批量預測 -->
                        <div class="mt-3">
                            <button id="batch-predict-btn" class="btn btn-outline-primary w-100">
                                <i class="fas fa-layer-group me-2"></i>
                                批量預測所有彩票類型
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 預測建議卡片 -->
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-lightbulb"></i>
                            智能建議
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="recommendations">
                            <p class="text-muted">選擇彩票類型後顯示建議</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右側：預測結果 -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-star"></i>
                            預測結果
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="prediction-results">
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-dice-six fa-3x mb-3"></i>
                                <p>執行預測後顯示結果</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 歷史統計圖表 -->
                <div class="card">
                    <div class="card-header bg-warning text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-area"></i>
                            準確度分析
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="accuracy-chart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 預測歷史記錄 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-history"></i>
                            預測歷史
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="prediction-history">
                            <p class="text-muted">暫無預測記錄</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // API 基礎 URL
        const API_BASE = 'http://localhost:8000';
        
        // 全局變量
        let websocket = null;
        let selectedLotteryType = null;
        let predictionHistory = [];
        let accuracyChart = null;
        
        // 初始化應用
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });
        
        async function initializeApp() {
            try {
                await loadLotteryTypes();
                await loadSystemStatus();
                initializeWebSocket();
                initializeChart();
                setupEventListeners();
            } catch (error) {
                console.error('應用初始化失敗:', error);
                showAlert('系統初始化失敗，請檢查 API 服務是否正常運行', 'danger');
            }
        }
        
        // 載入彩票類型
        async function loadLotteryTypes() {
            try {
                const response = await fetch(`${API_BASE}/lotteries`);
                if (!response.ok) throw new Error('無法載入彩票類型');
                
                const lotteries = await response.json();
                const container = document.getElementById('lottery-types');
                
                container.innerHTML = '';
                lotteries.forEach((lottery, index) => {
                    const col = document.createElement('div');
                    col.className = 'col-6';
                    
                    col.innerHTML = `
                        <div class="lottery-selector p-3 rounded border ${index === 0 ? 'active' : ''}" 
                             data-type="${lottery.lottery_type}">
                            <div class="text-center">
                                <strong>${lottery.name}</strong><br>
                                <small class="text-muted">
                                    ${lottery.main_numbers_count}+${lottery.special_numbers_count}
                                </small>
                            </div>
                        </div>
                    `;
                    
                    container.appendChild(col);
                    
                    // 設置第一個為預設選擇
                    if (index === 0) {
                        selectedLotteryType = lottery.lottery_type;
                        loadRecommendations(lottery.lottery_type);
                    }
                });
                
                // 綁定點擊事件
                container.querySelectorAll('.lottery-selector').forEach(selector => {
                    selector.addEventListener('click', function() {
                        // 移除其他選擇
                        container.querySelectorAll('.lottery-selector').forEach(s => 
                            s.classList.remove('active'));
                        
                        // 添加當前選擇
                        this.classList.add('active');
                        selectedLotteryType = this.dataset.type;
                        
                        // 載入建議
                        loadRecommendations(selectedLotteryType);
                    });
                });
                
            } catch (error) {
                console.error('載入彩票類型失敗:', error);
                showAlert('載入彩票類型失敗', 'danger');
            }
        }
        
        // 載入系統狀態
        async function loadSystemStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (!response.ok) throw new Error('無法載入系統狀態');
                
                const status = await response.json();
                
                document.getElementById('system-status').textContent = status.status;
                document.getElementById('system-uptime').textContent = `運行時間: ${status.uptime}`;
                document.getElementById('active-predictions').textContent = status.active_predictions;
                document.getElementById('total-predictions').textContent = status.total_predictions;
                
                // 更新狀態顏色
                const statusElement = document.getElementById('system-status');
                statusElement.className = status.status === '正常' ? 'text-success' : 'text-danger';
                
            } catch (error) {
                console.error('載入系統狀態失敗:', error);
            }
        }
        
        // 載入預測建議
        async function loadRecommendations(lotteryType) {
            try {
                const response = await fetch(`${API_BASE}/recommendations/${lotteryType}`);
                if (!response.ok) throw new Error('無法載入建議');
                
                const recommendations = await response.json();
                const container = document.getElementById('recommendations');
                
                let html = '';
                
                // 推薦策略
                if (recommendations.recommended_strategies && recommendations.recommended_strategies.length > 0) {
                    html += '<h6><i class="fas fa-trophy text-warning"></i> 推薦策略:</h6>';
                    html += '<ul class="list-unstyled">';
                    recommendations.recommended_strategies.slice(0, 3).forEach(strategy => {
                        html += `<li class="mb-1">
                            <span class="badge bg-primary">${strategy.confidence.toFixed(1)}%</span>
                            ${strategy.strategy}
                        </li>`;
                    });
                    html += '</ul>';
                }
                
                // 跨學習機會
                if (recommendations.cross_learning_opportunities && recommendations.cross_learning_opportunities.length > 0) {
                    html += '<h6><i class="fas fa-link text-info"></i> 跨學習建議:</h6>';
                    html += '<ul class="list-unstyled">';
                    recommendations.cross_learning_opportunities.slice(0, 2).forEach(opp => {
                        const icon = opp.recommended ? 'check text-success' : 'times text-danger';
                        html += `<li class="mb-1">
                            <i class="fas fa-${icon}"></i>
                            ${opp.related_lottery} (相似度: ${opp.similarity_score.toFixed(3)})
                        </li>`;
                    });
                    html += '</ul>';
                }
                
                // 優化建議
                if (recommendations.optimization_suggestions && recommendations.optimization_suggestions.length > 0) {
                    html += '<h6><i class="fas fa-gear text-secondary"></i> 優化建議:</h6>';
                    html += '<ul class="list-unstyled">';
                    recommendations.optimization_suggestions.slice(0, 2).forEach(suggestion => {
                        html += `<li class="mb-1"><i class="fas fa-chevron-right text-muted"></i> ${suggestion}</li>`;
                    });
                    html += '</ul>';
                }
                
                container.innerHTML = html || '<p class="text-muted">暫無建議</p>';
                
            } catch (error) {
                console.error('載入建議失敗:', error);
                document.getElementById('recommendations').innerHTML = '<p class="text-danger">載入建議失敗</p>';
            }
        }
        
        // 初始化 WebSocket
        function initializeWebSocket() {
            try {
                websocket = new WebSocket(`ws://localhost:8000/ws`);
                
                websocket.onopen = function() {
                    updateWebSocketStatus('online', '已連接');
                    // 訂閱實時更新
                    websocket.send(JSON.stringify({type: 'subscribe'}));
                };
                
                websocket.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                };
                
                websocket.onclose = function() {
                    updateWebSocketStatus('offline', '已斷線');
                };
                
                websocket.onerror = function(error) {
                    console.error('WebSocket 錯誤:', error);
                    updateWebSocketStatus('warning', '連接錯誤');
                };
                
            } catch (error) {
                console.error('WebSocket 初始化失敗:', error);
                updateWebSocketStatus('offline', '連接失敗');
            }
        }
        
        // 更新 WebSocket 狀態
        function updateWebSocketStatus(status, message) {
            const statusElement = document.getElementById('websocket-status');
            const indicator = statusElement.querySelector('.status-indicator');
            
            indicator.className = `status-indicator status-${status}`;
            statusElement.innerHTML = `
                <span class="status-indicator status-${status}"></span>
                連接狀態: ${message}
            `;
        }
        
        // 處理 WebSocket 消息
        function handleWebSocketMessage(data) {
            switch (data.type) {
                case 'subscription_confirmed':
                    console.log('已訂閱實時更新');
                    break;
                case 'status_update':
                    updateRealTimeStatus(data.data);
                    break;
                case 'prediction_update':
                    handlePredictionUpdate(data.data);
                    break;
                default:
                    console.log('未知消息類型:', data);
            }
        }
        
        // 更新實時狀態
        function updateRealTimeStatus(data) {
            document.getElementById('active-predictions').textContent = data.active_predictions;
        }
        
        // 初始化圖表
        function initializeChart() {
            const ctx = document.getElementById('accuracy-chart').getContext('2d');
            
            accuracyChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '準確度',
                        data: [],
                        borderColor: 'rgb(37, 99, 235)',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }
        
        // 設置事件監聽器
        function setupEventListeners() {
            // 預測按鈕
            document.getElementById('predict-btn').addEventListener('click', createPrediction);
            
            // 批量預測按鈕
            document.getElementById('batch-predict-btn').addEventListener('click', createBatchPrediction);
            
            // 定期更新系統狀態
            setInterval(loadSystemStatus, 30000); // 30秒更新一次
        }
        
        // 創建預測
        async function createPrediction() {
            if (!selectedLotteryType) {
                showAlert('請先選擇彩票類型', 'warning');
                return;
            }
            
            const button = document.getElementById('predict-btn');
            const spinner = button.querySelector('.loading-spinner');
            
            try {
                // 顯示載入狀態
                button.disabled = true;
                spinner.style.display = 'inline-block';
                
                const requestData = {
                    lottery_type: selectedLotteryType,
                    strategy: document.getElementById('strategy-select').value,
                    use_cross_learning: document.getElementById('cross-learning').checked,
                    ensemble_size: parseInt(document.getElementById('ensemble-size').value),
                    data_window: parseInt(document.getElementById('data-window').value)
                };
                
                const response = await fetch(`${API_BASE}/predict`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.detail || '預測失敗');
                }
                
                const result = await response.json();
                displayPredictionResult(result);
                addToHistory(result);
                showAlert('預測完成！', 'success');
                
            } catch (error) {
                console.error('預測失敗:', error);
                showAlert(error.message || '預測失敗', 'danger');
            } finally {
                // 恢復按鈕狀態
                button.disabled = false;
                spinner.style.display = 'none';
            }
        }
        
        // 創建批量預測
        async function createBatchPrediction() {
            const button = document.getElementById('batch-predict-btn');
            
            try {
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>批量預測中...';
                
                // 獲取所有彩票類型
                const lotteryResponse = await fetch(`${API_BASE}/lotteries`);
                const lotteries = await lotteryResponse.json();
                const lotteryTypes = lotteries.map(l => l.lottery_type);
                
                const requestData = {
                    lottery_types: lotteryTypes,
                    strategy: document.getElementById('strategy-select').value,
                    use_cross_learning: document.getElementById('cross-learning').checked,
                    ensemble_size: Math.min(parseInt(document.getElementById('ensemble-size').value), 10)
                };
                
                const response = await fetch(`${API_BASE}/predict/batch`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.detail || '批量預測失敗');
                }
                
                const result = await response.json();
                displayBatchPredictionResult(result);
                showAlert(`批量預測完成！成功: ${result.successful_predictions}/${result.total_predictions}`, 'success');
                
            } catch (error) {
                console.error('批量預測失敗:', error);
                showAlert(error.message || '批量預測失敗', 'danger');
            } finally {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-layer-group me-2"></i>批量預測所有彩票類型';
            }
        }
        
        // 顯示預測結果
        function displayPredictionResult(result) {
            const container = document.getElementById('prediction-results');
            
            const confidenceClass = result.confidence >= 80 ? 'confidence-high' : 
                                   result.confidence >= 60 ? 'confidence-medium' : 'confidence-low';
            
            let numbersHtml = '';
            result.main_numbers.forEach(num => {
                numbersHtml += `<span class="number-ball main-number">${num}</span>`;
            });
            
            if (result.special_numbers && result.special_numbers.length > 0) {
                result.special_numbers.forEach(num => {
                    numbersHtml += `<span class="number-ball special-number">${num}</span>`;
                });
            }
            
            container.innerHTML = `
                <div class="prediction-card p-4 mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">
                            <i class="fas fa-dice-six text-primary"></i>
                            ${result.lottery_type}
                        </h6>
                        <small class="text-muted">${formatDateTime(result.prediction_timestamp)}</small>
                    </div>
                    
                    <div class="text-center mb-3">
                        ${numbersHtml}
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-4">
                            <small class="text-muted">信心度</small>
                            <div class="confidence-bar mt-1">
                                <div class="confidence-fill ${confidenceClass}" 
                                     style="width: ${result.confidence}%"></div>
                            </div>
                            <strong>${result.confidence.toFixed(1)}%</strong>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">策略</small><br>
                            <strong>${result.strategy_used}</strong>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">預測ID</small><br>
                            <small class="text-monospace">${result.prediction_id.substring(0, 8)}...</small>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // 顯示批量預測結果
        function displayBatchPredictionResult(batchResult) {
            const container = document.getElementById('prediction-results');
            
            let html = `
                <div class="mb-3">
                    <h6>
                        <i class="fas fa-layer-group text-primary"></i>
                        批量預測結果 (${batchResult.successful_predictions}/${batchResult.total_predictions})
                    </h6>
                    <small class="text-muted">${formatDateTime(batchResult.timestamp)}</small>
                </div>
            `;
            
            for (const [lotteryType, result] of Object.entries(batchResult.results)) {
                if (result) {
                    let numbersHtml = '';
                    result.main_numbers.forEach(num => {
                        numbersHtml += `<span class="number-ball main-number" style="width: 30px; height: 30px; line-height: 30px; font-size: 0.8rem;">${num}</span>`;
                    });
                    
                    html += `
                        <div class="border rounded p-3 mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <strong>${lotteryType}</strong>
                                <span class="badge bg-success">${result.confidence.toFixed(1)}%</span>
                            </div>
                            <div class="mt-2">${numbersHtml}</div>
                        </div>
                    `;
                } else {
                    html += `
                        <div class="border rounded p-3 mb-2 bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <strong>${lotteryType}</strong>
                                <span class="badge bg-danger">失敗</span>
                            </div>
                        </div>
                    `;
                }
            }
            
            container.innerHTML = html;
        }
        
        // 添加到歷史記錄
        function addToHistory(result) {
            predictionHistory.unshift(result);
            if (predictionHistory.length > 10) {
                predictionHistory.pop();
            }
            updateHistoryDisplay();
            updateChart();
        }
        
        // 更新歷史記錄顯示
        function updateHistoryDisplay() {
            const container = document.getElementById('prediction-history');
            
            if (predictionHistory.length === 0) {
                container.innerHTML = '<p class="text-muted">暫無預測記錄</p>';
                return;
            }
            
            let html = '<div class="table-responsive"><table class="table table-hover">';
            html += `
                <thead>
                    <tr>
                        <th>時間</th>
                        <th>彩票類型</th>
                        <th>預測號碼</th>
                        <th>策略</th>
                        <th>信心度</th>
                    </tr>
                </thead>
                <tbody>
            `;
            
            predictionHistory.forEach(result => {
                let numbersHtml = '';
                result.main_numbers.forEach(num => {
                    numbersHtml += `<span class="badge bg-primary me-1">${num}</span>`;
                });
                if (result.special_numbers) {
                    result.special_numbers.forEach(num => {
                        numbersHtml += `<span class="badge bg-danger me-1">${num}</span>`;
                    });
                }
                
                html += `
                    <tr>
                        <td>${formatDateTime(result.prediction_timestamp)}</td>
                        <td>${result.lottery_type}</td>
                        <td>${numbersHtml}</td>
                        <td>${result.strategy_used}</td>
                        <td>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: ${result.confidence}%"
                                     aria-valuenow="${result.confidence}" 
                                     aria-valuemin="0" aria-valuemax="100">
                                    ${result.confidence.toFixed(1)}%
                                </div>
                            </div>
                        </td>
                    </tr>
                `;
            });
            
            html += '</tbody></table></div>';
            container.innerHTML = html;
        }
        
        // 更新圖表
        function updateChart() {
            if (predictionHistory.length === 0) return;
            
            const labels = predictionHistory.slice().reverse().map(result => 
                formatTime(result.prediction_timestamp)
            );
            const data = predictionHistory.slice().reverse().map(result => result.confidence);
            
            accuracyChart.data.labels = labels;
            accuracyChart.data.datasets[0].data = data;
            accuracyChart.update();
        }
        
        // 顯示警告
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.top = '20px';
            alertDiv.style.right = '20px';
            alertDiv.style.zIndex = '9999';
            alertDiv.style.minWidth = '300px';
            
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            // 3秒後自動移除
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 3000);
        }
        
        // 格式化日期時間
        function formatDateTime(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleString('zh-TW');
        }
        
        // 格式化時間
        function formatTime(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleTimeString('zh-TW', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
        }
    </script>
</body>
</html>