#!/usr/bin/env python3
"""
Phase 3.4 Web Server 啟動器
啟動 FastAPI 服務器和靜態文件服務
"""

import os
import sys
import asyncio
import threading
import time
from pathlib import Path
import logging
import webbrowser
from http.server import HTTPServer, SimpleHTTPRequestHandler
import socket

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_port_available(port: int) -> bool:
    """檢查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', port))
            return True
    except OSError:
        return False

def start_static_server(port: int = 8080):
    """啟動靜態文件服務器"""
    try:
        # 切換到 phase3 目錄
        os.chdir(Path(__file__).parent)
        
        class CustomHTTPRequestHandler(SimpleHTTPRequestHandler):
            def end_headers(self):
                # 添加 CORS 標頭
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', '*')
                super().end_headers()
            
            def do_GET(self):
                # 默認返回 web_interface.html
                if self.path == '/':
                    self.path = '/web_interface.html'
                return super().do_GET()
        
        with HTTPServer(('localhost', port), CustomHTTPRequestHandler) as httpd:
            logger.info(f"🌐 靜態文件服務器啟動於: http://localhost:{port}")
            httpd.serve_forever()
            
    except Exception as e:
        logger.error(f"❌ 靜態文件服務器啟動失敗: {e}")

def start_api_server():
    """啟動 API 服務器"""
    try:
        import uvicorn
        from web_api import app
        
        logger.info("🚀 啟動 FastAPI 服務器...")
        
        # 檢查端口
        if not check_port_available(8000):
            logger.warning("⚠️ 端口 8000 已被占用，嘗試使用其他端口...")
            for port in range(8001, 8010):
                if check_port_available(port):
                    logger.info(f"使用端口 {port}")
                    break
            else:
                logger.error("❌ 無法找到可用端口")
                return
        else:
            port = 8000
        
        # 啟動 uvicorn 服務器
        uvicorn.run(
            app,
            host="localhost",
            port=port,
            log_level="info",
            access_log=True
        )
        
    except ImportError:
        logger.error("❌ 請安裝 uvicorn: pip install uvicorn")
        return
    except Exception as e:
        logger.error(f"❌ API 服務器啟動失敗: {e}")

def open_browser(url: str, delay: int = 3):
    """延遲打開瀏覽器"""
    time.sleep(delay)
    try:
        webbrowser.open(url)
        logger.info(f"🌐 已打開瀏覽器: {url}")
    except Exception as e:
        logger.error(f"❌ 無法打開瀏覽器: {e}")

def main():
    """主函數"""
    print("🎰 彩票預測系統 Web 服務器啟動器")
    print("=" * 50)
    
    # 檢查依賴
    try:
        import uvicorn
        import fastapi
    except ImportError:
        print("❌ 缺少必要依賴，請安裝:")
        print("pip install fastapi uvicorn")
        return
    
    # 確定端口
    api_port = 8000
    static_port = 8080
    
    # 檢查 API 端口
    if not check_port_available(api_port):
        logger.warning(f"⚠️ API 端口 {api_port} 已被占用")
        for port in range(8001, 8010):
            if check_port_available(port):
                api_port = port
                logger.info(f"使用 API 端口: {port}")
                break
        else:
            logger.error("❌ 無法找到可用的 API 端口")
            return
    
    # 檢查靜態文件端口
    if not check_port_available(static_port):
        logger.warning(f"⚠️ 靜態文件端口 {static_port} 已被占用")
        for port in range(8081, 8090):
            if check_port_available(port):
                static_port = port
                logger.info(f"使用靜態文件端口: {port}")
                break
        else:
            logger.error("❌ 無法找到可用的靜態文件端口")
            return
    
    print(f"📡 API 服務器: http://localhost:{api_port}")
    print(f"🌐 Web 界面: http://localhost:{static_port}")
    print("=" * 50)
    
    try:
        # 啟動靜態文件服務器 (在新線程中)
        static_thread = threading.Thread(
            target=start_static_server,
            args=(static_port,),
            daemon=True
        )
        static_thread.start()
        
        # 延遲打開瀏覽器
        browser_thread = threading.Thread(
            target=open_browser,
            args=(f"http://localhost:{static_port}", 5),
            daemon=True
        )
        browser_thread.start()
        
        # 啟動 API 服務器 (主線程)
        print("🚀 正在啟動服務器...")
        print("按 Ctrl+C 停止服務器")
        print("=" * 50)
        
        # 更新 web_interface.html 中的 API 基礎 URL
        update_api_url_in_html(api_port)
        
        start_api_server()
        
    except KeyboardInterrupt:
        print("\n🛑 正在停止服務器...")
        logger.info("服務器已停止")
    except Exception as e:
        logger.error(f"❌ 服務器啟動失敗: {e}")

def update_api_url_in_html(api_port: int):
    """更新 HTML 文件中的 API URL"""
    try:
        html_file = Path(__file__).parent / "web_interface.html"
        if html_file.exists():
            content = html_file.read_text(encoding='utf-8')
            
            # 替換 API 基礎 URL
            old_url = "const API_BASE = 'http://localhost:8000';"
            new_url = f"const API_BASE = 'http://localhost:{api_port}';"
            
            if old_url in content:
                content = content.replace(old_url, new_url)
                html_file.write_text(content, encoding='utf-8')
                logger.info(f"✅ 已更新 HTML 中的 API URL 為端口 {api_port}")
                
    except Exception as e:
        logger.warning(f"⚠️ 更新 HTML API URL 失敗: {e}")

if __name__ == "__main__":
    main()