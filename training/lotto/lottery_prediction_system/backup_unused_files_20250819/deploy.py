#!/usr/bin/env python3
"""
Phase 3.6 一鍵部署腳本
自動化部署彩票預測系統到生產環境
"""

import os
import sys
import json
import yaml
import subprocess
import argparse
import time
import requests
from pathlib import Path
from datetime import datetime
import logging

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DeploymentScript:
    """部署腳本"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.docker_dir = self.project_root / "docker"
        self.config_file = self.project_root / "deploy_config.yaml"
        self.docker_compose_cmd = self._get_docker_compose_command()
    
    def _get_docker_compose_command(self):
        """獲取正確的Docker Compose命令"""
        # 嘗試新版本格式
        try:
            subprocess.run(['docker', 'compose', '--version'], 
                         capture_output=True, text=True, check=True)
            return ['docker', 'compose']
        except (subprocess.CalledProcessError, FileNotFoundError):
            # 回退到舊版本格式
            try:
                subprocess.run(['docker-compose', '--version'], 
                             capture_output=True, text=True, check=True)
                return ['docker-compose']
            except (subprocess.CalledProcessError, FileNotFoundError):
                return None
        
    def create_default_config(self):
        """創建默認配置文件"""
        default_config = {
            'deployment': {
                'environment': 'production',
                'domain': 'localhost',
                'ssl_enabled': False,
                'backup_enabled': True,
                'monitoring_enabled': True
            },
            'services': {
                'api': {
                    'host': '0.0.0.0',
                    'port': 8000,
                    'workers': 4,
                    'timeout': 30
                },
                'web': {
                    'port': 8080
                },
                'redis': {
                    'enabled': True,
                    'max_memory': '256mb'
                },
                'prometheus': {
                    'enabled': True,
                    'retention': '30d'
                },
                'grafana': {
                    'enabled': True,
                    'admin_password': 'admin123'
                },
                'elasticsearch': {
                    'enabled': False,
                    'memory': '512m'
                }
            },
            'database': {
                'backup_schedule': '0 2 * * *',
                'backup_retention': 30
            },
            'security': {
                'rate_limit_api': '10r/s',
                'rate_limit_web': '5r/s',
                'allowed_hosts': ['localhost', '127.0.0.1']
            }
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"✅ 已創建默認配置文件: {self.config_file}")
        return default_config
    
    def load_config(self):
        """載入配置文件"""
        if not self.config_file.exists():
            return self.create_default_config()
        
        with open(self.config_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def check_prerequisites(self):
        """檢查部署先決條件"""
        logger.info("🔍 檢查部署先決條件...")
        
        # 檢查 Docker
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, check=True)
            logger.info(f"✅ Docker: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("❌ Docker 未安裝或無法訪問")
            return False
        
        # 檢查 Docker Compose
        if self.docker_compose_cmd is None:
            logger.error("❌ Docker Compose 未安裝或無法訪問")
            return False
        else:
            result = subprocess.run(self.docker_compose_cmd + ['--version'], 
                                  capture_output=True, text=True, check=True)
            logger.info(f"✅ Docker Compose: {result.stdout.strip()}")
        
        # 檢查端口占用（簡化版本）
        required_ports = [8001, 8081]  # 使用新端口
        for port in required_ports:
            if self.is_port_in_use(port):
                logger.warning(f"⚠️ 端口 {port} 已被占用")
        
        # 檢查磁盤空間
        import shutil
        total, used, free = shutil.disk_usage("/")
        free_gb = free // (1024**3)
        
        if free_gb < 5:
            logger.error(f"❌ 磁盤空間不足: {free_gb}GB (需要至少5GB)")
            return False
        else:
            logger.info(f"✅ 磁盤空間: {free_gb}GB 可用")
        
        return True
    
    def is_port_in_use(self, port):
        """檢查端口是否被占用"""
        import socket
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                return s.connect_ex(('localhost', port)) == 0
        except:
            return False
    
    def prepare_environment(self, config):
        """準備環境"""
        logger.info("⚙️ 準備部署環境...")
        
        # 創建必要的目錄
        directories = [
            'logs', 'data', 'backups', 'reports', 'exports',
            'docker/ssl', 'docker/grafana/dashboards', 
            'docker/grafana/datasources'
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"📁 創建目錄: {directory}")
        
        # 生成 Docker Compose 環境變量文件
        self.create_env_file(config)
        
        # 創建 Grafana 配置
        self.create_grafana_config(config)
        
        # 設置權限
        self.set_permissions()
        
        logger.info("✅ 環境準備完成")
    
    def create_env_file(self, config):
        """創建環境變量文件"""
        env_content = f"""# 彩票預測系統環境變量
# 生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

# 應用配置
LOTTERY_SYSTEM_MODE={config['deployment']['environment']}
LOTTERY_SYSTEM_LOG_LEVEL=INFO
DATABASE_URL=sqlite:///app/data/lottery_prediction.db

# API 配置
API_HOST={config['services']['api']['host']}
API_PORT={config['services']['api']['port']}
API_WORKERS={config['services']['api']['workers']}
API_TIMEOUT={config['services']['api']['timeout']}

# Web 配置
WEB_PORT={config['services']['web']['port']}

# Redis 配置
REDIS_URL=redis://redis:6379/0
REDIS_MAX_MEMORY={config['services']['redis']['max_memory']}

# Grafana 配置
GF_SECURITY_ADMIN_PASSWORD={config['services']['grafana']['admin_password']}

# 備份配置
BACKUP_SCHEDULE={config['database']['backup_schedule']}
BACKUP_RETENTION={config['database']['backup_retention']}

# 安全配置
RATE_LIMIT_API={config['security']['rate_limit_api']}
RATE_LIMIT_WEB={config['security']['rate_limit_web']}
"""
        
        env_file = self.project_root / ".env"
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        logger.info("✅ 環境變量文件已創建")
    
    def create_grafana_config(self, config):
        """創建 Grafana 配置"""
        # 數據源配置
        datasource_config = {
            'apiVersion': 1,
            'datasources': [
                {
                    'name': 'Prometheus',
                    'type': 'prometheus',
                    'access': 'proxy',
                    'url': 'http://prometheus:9090',
                    'isDefault': True
                }
            ]
        }
        
        datasource_file = self.project_root / "docker/grafana/datasources/prometheus.yaml"
        with open(datasource_file, 'w', encoding='utf-8') as f:
            yaml.dump(datasource_config, f)
        
        # 儀表板配置
        dashboard_config = {
            'apiVersion': 1,
            'providers': [
                {
                    'name': 'default',
                    'orgId': 1,
                    'folder': '',
                    'type': 'file',
                    'disableDeletion': False,
                    'updateIntervalSeconds': 10,
                    'options': {
                        'path': '/etc/grafana/provisioning/dashboards'
                    }
                }
            ]
        }
        
        dashboard_file = self.project_root / "docker/grafana/dashboards/dashboard.yaml"
        with open(dashboard_file, 'w', encoding='utf-8') as f:
            yaml.dump(dashboard_config, f)
        
        logger.info("✅ Grafana 配置已創建")
    
    def set_permissions(self):
        """設置文件權限"""
        try:
            # 設置腳本執行權限
            scripts = [
                'phase3/start_web_server.py',
                'deploy.py'
            ]
            
            for script in scripts:
                script_path = self.project_root / script
                if script_path.exists():
                    os.chmod(script_path, 0o755)
            
            logger.info("✅ 文件權限設置完成")
        except Exception as e:
            logger.warning(f"⚠️ 權限設置失敗: {e}")
    
    def build_images(self):
        """構建 Docker 鏡像"""
        logger.info("🔨 構建 Docker 鏡像...")
        
        try:
            # 構建主應用鏡像
            subprocess.run(
                self.docker_compose_cmd + ['build', '--no-cache'],
                cwd=self.project_root, check=True)
            
            logger.info("✅ Docker 鏡像構建完成")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Docker 鏡像構建失敗: {e}")
            return False
    
    def start_services(self, services=None):
        """啟動服務"""
        logger.info("🚀 啟動服務...")
        
        try:
            cmd = self.docker_compose_cmd + ['up', '-d']
            if services:
                cmd.extend(services)
            
            subprocess.run(cmd, cwd=self.project_root, check=True)
            
            logger.info("✅ 服務啟動完成")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ 服務啟動失敗: {e}")
            return False
    
    def wait_for_services(self, max_wait=300):
        """等待服務就緒"""
        logger.info("⏳ 等待服務就緒...")
        
        services_to_check = [
            ('API 服務', 'http://localhost:8001/health'),
            ('Web 界面', 'http://localhost:8081')
        ]
        
        start_time = time.time()
        
        for service_name, url in services_to_check:
            logger.info(f"🔍 檢查 {service_name}...")
            
            while time.time() - start_time < max_wait:
                try:
                    response = requests.get(url, timeout=5)
                    if response.status_code == 200:
                        logger.info(f"✅ {service_name} 就緒")
                        break
                except requests.RequestException:
                    pass
                
                time.sleep(5)
            else:
                logger.warning(f"⚠️ {service_name} 未在預期時間內就緒")
        
        elapsed = time.time() - start_time
        logger.info(f"✅ 服務檢查完成 (耗時: {elapsed:.1f}秒)")
    
    def run_health_checks(self):
        """運行健康檢查"""
        logger.info("🏥 運行健康檢查...")
        
        try:
            # 檢查容器狀態
            result = subprocess.run(
                self.docker_compose_cmd + ['ps'],
                cwd=self.project_root, capture_output=True, text=True, check=True)
            
            logger.info("📊 容器狀態:")
            print(result.stdout)
            
            # 檢查服務健康狀態
            health_checks = [
                ('API 健康檢查', 'http://localhost:8001/health'),
                ('API 追蹤統計', 'http://localhost:8001/tracking/stats/powercolor')
            ]
            
            for check_name, url in health_checks:
                try:
                    response = requests.get(url, timeout=10)
                    if response.status_code == 200:
                        logger.info(f"✅ {check_name}: 正常")
                    else:
                        logger.warning(f"⚠️ {check_name}: HTTP {response.status_code}")
                except requests.RequestException as e:
                    logger.error(f"❌ {check_name}: {e}")
            
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ 健康檢查失敗: {e}")
            return False
    
    def show_deployment_info(self, config):
        """顯示部署信息"""
        logger.info("📋 部署信息:")
        
        domain = config['deployment']['domain']
        ssl_enabled = config['deployment']['ssl_enabled']
        protocol = 'https' if ssl_enabled else 'http'
        
        print("\n" + "="*60)
        print("🎉 彩票預測系統部署完成！")
        print("="*60)
        
        print(f"\n📱 Web 界面:")
        print(f"   {protocol}://{domain}:8081")
        
        print(f"\n🔌 API 接口:")
        print(f"   {protocol}://{domain}:8001")
        print(f"   API 文檔: {protocol}://{domain}:8001/docs")
        
        # 簡化部署 - 移除監控面板
        
        print(f"\n📁 數據目錄:")
        print(f"   數據庫: ./data/")
        print(f"   日誌: ./logs/")
        print(f"   備份: ./backups/")
        print(f"   報告: ./reports/")
        
        print(f"\n🛠️ 管理命令:")
        cmd_str = ' '.join(self.docker_compose_cmd)
        print(f"   查看日誌: {cmd_str} logs -f")
        print(f"   重啟服務: {cmd_str} restart")
        print(f"   停止服務: {cmd_str} down")
        print(f"   更新服務: {cmd_str} pull && {cmd_str} up -d")
        
        print("\n" + "="*60)
    
    def deploy(self, args):
        """執行部署"""
        logger.info("🚀 開始部署彩票預測系統...")
        
        # 載入配置
        config = self.load_config()
        
        # 檢查先決條件
        if not self.check_prerequisites():
            logger.error("❌ 先決條件檢查失敗，部署終止")
            return False
        
        # 準備環境
        self.prepare_environment(config)
        
        # 構建鏡像
        if args.build:
            if not self.build_images():
                return False
        
        # 啟動服務
        services = args.services.split(',') if args.services else None
        if not self.start_services(services):
            return False
        
        # 等待服務就緒
        if args.wait:
            self.wait_for_services()
        
        # 運行健康檢查
        if args.health_check:
            self.run_health_checks()
        
        # 顯示部署信息
        self.show_deployment_info(config)
        
        logger.info("🎉 部署完成！")
        return True
    
    def stop_services(self):
        """停止服務"""
        logger.info("🛑 停止服務...")
        
        try:
            subprocess.run(
                self.docker_compose_cmd + ['down'],
                cwd=self.project_root, check=True)
            
            logger.info("✅ 服務已停止")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ 停止服務失敗: {e}")
            return False
    
    def cleanup(self):
        """清理部署"""
        logger.info("🧹 清理部署...")
        
        try:
            # 停止並刪除容器、網絡、數據卷
            subprocess.run(
                self.docker_compose_cmd + ['down', '-v', '--remove-orphans'],
                cwd=self.project_root, check=True)
            
            # 刪除鏡像
            subprocess.run([
                'docker', 'image', 'prune', '-f'
            ], check=True)
            
            logger.info("✅ 清理完成")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ 清理失敗: {e}")
            return False

def main():
    """主函數"""
    parser = argparse.ArgumentParser(description='彩票預測系統部署腳本')
    
    parser.add_argument('action', choices=['deploy', 'stop', 'cleanup', 'status'],
                       help='要執行的操作')
    parser.add_argument('--build', action='store_true',
                       help='重新構建 Docker 鏡像')
    parser.add_argument('--services', type=str,
                       help='要啟動的特定服務 (逗號分隔)')
    parser.add_argument('--wait', action='store_true', default=True,
                       help='等待服務就緒')
    parser.add_argument('--health-check', action='store_true', default=True,
                       help='運行健康檢查')
    
    args = parser.parse_args()
    
    deployment = DeploymentScript()
    
    if args.action == 'deploy':
        success = deployment.deploy(args)
        sys.exit(0 if success else 1)
    elif args.action == 'stop':
        success = deployment.stop_services()
        sys.exit(0 if success else 1)
    elif args.action == 'cleanup':
        success = deployment.cleanup()
        sys.exit(0 if success else 1)
    elif args.action == 'status':
        success = deployment.run_health_checks()
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()