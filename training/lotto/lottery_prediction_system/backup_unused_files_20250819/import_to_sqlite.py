# import_to_sqlite.py
import sqlite3
import pandas as pd
import os

def import_data_to_sqlite(sqlite_db_path):
    try:
        # 確保SQLite資料庫目錄存在
        db_dir = os.path.dirname(sqlite_db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
        
        # 連接SQLite數據庫
        conn = sqlite3.connect(sqlite_db_path)
        
        # 導入威力彩數據
        if os.path.exists('powercolor_data.csv'):
            power_df = pd.read_csv('powercolor_data.csv')
            power_df.to_sql('Powercolor', conn, if_exists='replace', index=False)
            print(f"已導入 {len(power_df)} 筆威力彩資料")
        
        # 導入大樂透數據
        if os.path.exists('lotto649_data.csv'):
            lotto_df = pd.read_csv('lotto649_data.csv')
            lotto_df.to_sql('Lotto649', conn, if_exists='replace', index=False)
            print(f"已導入 {len(lotto_df)} 筆大樂透資料")
        
        # 導入今彩539數據
        if os.path.exists('dailycash_data.csv'):
            daily_df = pd.read_csv('dailycash_data.csv')
            daily_df.to_sql('DailyCash', conn, if_exists='replace', index=False)
            print(f"已導入 {len(daily_df)} 筆今彩539資料")
        
        # 導入預測記錄
        if os.path.exists('powercolor_predictions.csv'):
            pred_power_df = pd.read_csv('powercolor_predictions.csv')
            pred_power_df.to_sql('PowercolorPredictions', conn, if_exists='replace', index=False)
            print(f"已導入 {len(pred_power_df)} 筆威力彩預測記錄")
        
        # 其他預測記錄表格也類似處理
        
        conn.close()
        print("資料成功導入到SQLite數據庫")
        
    except Exception as e:
        print(f"導入數據時出錯: {str(e)}")

if __name__ == "__main__":
    import_data_to_sqlite('lottery_data.db')