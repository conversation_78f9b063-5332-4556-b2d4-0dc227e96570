#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定期訓練優化調度器
自動定期執行訓練期間優化，並更新系統配置
"""

import os
import sys
import time
import json
import logging
import schedule
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from auto_training_optimizer import AutoTrainingOptimizer
from core.config import Config

class OptimizationScheduler:
    """訓練優化調度器"""
    
    def __init__(self):
        self.setup_logging()
        self.config = Config()
        self.optimizer = None
        self.last_optimization = None
        self.optimization_interval_days = 7  # 每週優化一次
        self.min_interval_hours = 24  # 最小間隔24小時
        
        # 創建必要的目錄
        self.reports_dir = Path('reports')
        self.reports_dir.mkdir(exist_ok=True)
        
        self.config_dir = Path('config')
        self.config_dir.mkdir(exist_ok=True)
        
    def setup_logging(self):
        """設置日誌"""
        log_dir = Path('logs')
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'scheduler.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def should_run_optimization(self) -> bool:
        """檢查是否應該運行優化"""
        if self.last_optimization is None:
            return True
            
        time_since_last = datetime.now() - self.last_optimization
        return time_since_last.total_seconds() >= self.min_interval_hours * 3600
        
    def load_current_best_settings(self) -> Dict[str, Any]:
        """載入當前最佳設置"""
        settings_file = self.config_dir / 'best_training_settings.json'
        
        if settings_file.exists():
            try:
                with open(settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"載入設置文件失敗: {e}")
                
        # 默認設置
        return {
            'powercolor': {'training_days': 90, 'accuracy': 0.0},
            'lotto649': {'training_days': 180, 'accuracy': 0.286},
            'dailycash': {'training_days': 60, 'accuracy': 0.200},
            'last_updated': None
        }
        
    def save_best_settings(self, optimization_results: Dict[str, Any]):
        """保存最佳設置"""
        current_settings = self.load_current_best_settings()
        updated = False
        
        for lottery_type, result in optimization_results.items():
            if 'error' in result:
                continue
                
            new_accuracy = result.get('best_accuracy', 0.0)
            new_training_days = result.get('best_training_days')
            
            if new_training_days is None:
                continue
                
            current_accuracy = current_settings.get(lottery_type, {}).get('accuracy', 0.0)
            
            # 如果新的準確度更好，或者是首次設置，則更新
            if new_accuracy > current_accuracy or current_settings.get(lottery_type, {}).get('training_days') is None:
                current_settings[lottery_type] = {
                    'training_days': new_training_days,
                    'accuracy': new_accuracy
                }
                updated = True
                self.logger.info(
                    f"更新 {lottery_type} 最佳設置: {new_training_days}天 (準確度: {new_accuracy:.3f})"
                )
                
        if updated:
            current_settings['last_updated'] = datetime.now().isoformat()
            
            settings_file = self.config_dir / 'best_training_settings.json'
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(current_settings, f, indent=2, ensure_ascii=False)
                
            self.logger.info(f"最佳設置已保存到: {settings_file}")
        else:
            self.logger.info("沒有發現更好的設置，保持現有配置")
            
    def run_optimization(self):
        """執行優化"""
        try:
            if not self.should_run_optimization():
                self.logger.info("距離上次優化時間太短，跳過本次執行")
                return
                
            self.logger.info("開始定期訓練優化")
            
            # 創建優化器實例
            self.optimizer = AutoTrainingOptimizer()
            
            # 運行優化
            results = self.optimizer.run_optimization()
            
            # 保存最佳設置
            self.save_best_settings(results)
            
            # 更新最後優化時間
            self.last_optimization = datetime.now()
            
            # 顯示結果摘要
            self.print_optimization_summary(results)
            
            self.logger.info("定期訓練優化完成")
            
        except Exception as e:
            self.logger.error(f"執行優化時發生錯誤: {e}")
            
    def print_optimization_summary(self, results: Dict[str, Any]):
        """打印優化結果摘要"""
        print("\n=== 訓練期間優化結果摘要 ===")
        
        for lottery_type, result in results.items():
            if 'error' in result:
                print(f"{lottery_type}: 優化失敗 - {result['error']}")
            else:
                best_days = result.get('best_training_days', 'N/A')
                best_accuracy = result.get('best_accuracy', 0.0)
                print(f"{lottery_type}: 最佳訓練期間 {best_days}天, 準確度 {best_accuracy:.3f}")
                
        print("=" * 40)
        
    def get_current_best_settings(self) -> Dict[str, Any]:
        """獲取當前最佳設置"""
        return self.load_current_best_settings()
        
    def schedule_optimization(self):
        """設置定期優化排程"""
        # 每週日凌晨2點執行優化
        schedule.every().sunday.at("02:00").do(self.run_optimization)
        
        # 每天檢查是否需要優化（用於手動觸發或錯過的排程）
        schedule.every().day.at("03:00").do(self.check_and_run_if_needed)
        
        self.logger.info("已設置定期優化排程:")
        self.logger.info("- 每週日凌晨2點自動優化")
        self.logger.info("- 每天凌晨3點檢查是否需要補充優化")
        
    def check_and_run_if_needed(self):
        """檢查並在需要時運行優化"""
        current_settings = self.load_current_best_settings()
        last_updated = current_settings.get('last_updated')
        
        if last_updated:
            last_update_time = datetime.fromisoformat(last_updated)
            days_since_update = (datetime.now() - last_update_time).days
            
            if days_since_update >= self.optimization_interval_days:
                self.logger.info(f"距離上次優化已過 {days_since_update} 天，執行補充優化")
                self.run_optimization()
            else:
                self.logger.info(f"距離上次優化 {days_since_update} 天，無需執行")
        else:
            self.logger.info("未找到上次優化記錄，執行初始優化")
            self.run_optimization()
            
    def run_scheduler(self):
        """運行調度器"""
        self.logger.info("啟動訓練優化調度器")
        
        # 設置排程
        self.schedule_optimization()
        
        # 顯示當前最佳設置
        current_settings = self.get_current_best_settings()
        print("\n=== 當前最佳訓練設置 ===")
        for lottery_type, settings in current_settings.items():
            if lottery_type != 'last_updated' and isinstance(settings, dict):
                days = settings.get('training_days', 'N/A')
                accuracy = settings.get('accuracy', 0.0)
                print(f"{lottery_type}: {days}天 (準確度: {accuracy:.3f})")
        print("=" * 35)
        
        # 主循環
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分鐘檢查一次
        except KeyboardInterrupt:
            self.logger.info("調度器已停止")
            
def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description='彩票預測系統訓練優化調度器')
    parser.add_argument('--run-once', action='store_true', help='立即執行一次優化後退出')
    parser.add_argument('--show-settings', action='store_true', help='顯示當前最佳設置')
    
    args = parser.parse_args()
    
    scheduler = OptimizationScheduler()
    
    if args.show_settings:
        settings = scheduler.get_current_best_settings()
        print("\n=== 當前最佳訓練設置 ===")
        for lottery_type, config in settings.items():
            if lottery_type != 'last_updated' and isinstance(config, dict):
                days = config.get('training_days', 'N/A')
                accuracy = config.get('accuracy', 0.0)
                print(f"{lottery_type}: {days}天 (準確度: {accuracy:.3f})")
        if settings.get('last_updated'):
            print(f"最後更新: {settings['last_updated']}")
        print("=" * 35)
        return
        
    if args.run_once:
        print("執行一次性優化...")
        scheduler.run_optimization()
        return
        
    # 運行持續調度器
    scheduler.run_scheduler()
    
if __name__ == '__main__':
    main()