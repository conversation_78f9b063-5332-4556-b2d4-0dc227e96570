#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彩票預測系統 - 簡易啟動器
最簡單的啟動方式
"""

import os
import sys
import subprocess

def main():
    """啟動彩票預測系統"""
    print("🎲 彩票預測系統啟動器")
    print("=" * 40)
    
    # 檢查 lottery_system.py 是否存在
    main_script = "lottery_system.py"
    if not os.path.exists(main_script):
        print(f"❌ 找不到主程式: {main_script}")
        print("請確保您在正確的目錄中運行此腳本")
        return
    
    try:
        # 直接執行主程式
        subprocess.run([sys.executable, main_script] + sys.argv[1:])
    except KeyboardInterrupt:
        print("\n👋 系統已退出")
    except Exception as e:
        print(f"❌ 執行失敗: {e}")

if __name__ == "__main__":
    main()