#!/usr/bin/env python3
"""
Web应用启动脚本
"""

import os
import sys
import webbrowser
from threading import Timer

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def open_browser():
    """延迟打开浏览器"""
    webbrowser.open('http://localhost:8080')

def install_requirements():
    """安装所需的依赖"""
    try:
        import flask
        import flask_cors
        print("✅ 依赖已安装")
        return True
    except ImportError:
        print("📦 正在安装Web依赖...")
        os.system('pip install flask flask-cors')
        print("✅ 依赖安装完成")
        return True

def main():
    """主函数"""
    print("🚀 启动彩票预测Web应用")
    print("=" * 50)
    
    # 检查并安装依赖
    if not install_requirements():
        print("❌ 依赖安装失败")
        return
    
    print("🔧 初始化Web应用...")
    
    # 延迟打开浏览器
    Timer(2.0, open_browser).start()
    
    # 启动Flask应用
    from web_app import app
    
    print("🌐 Web服务已启动！")
    print("📍 访问地址: http://localhost:8080")
    print("🛑 按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    try:
        app.run(debug=False, host='0.0.0.0', port=8080)
    except KeyboardInterrupt:
        print("\n👋 Web服务已停止")

if __name__ == "__main__":
    main()