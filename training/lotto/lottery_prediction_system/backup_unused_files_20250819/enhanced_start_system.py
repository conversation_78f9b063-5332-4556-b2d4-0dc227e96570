#!/usr/bin/env python3
"""
增強版彩票預測系統啟動腳本
整合開獎結果查詢、自動更新數據、回測分析等完整功能
"""

import os
import sys
import subprocess
import webbrowser
import threading
import time
import sqlite3
import logging
import socket
from datetime import datetime
from pathlib import Path


def setup_logging():
    """設置日誌"""
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'logs/system_startup_{datetime.now().strftime("%Y%m%d")}.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger('enhanced_system')


def check_dependencies():
    """檢查並安裝必要的依賴包"""
    required_packages = {
        'streamlit': 'streamlit',
        'pandas': 'pandas',
        'plotly': 'plotly',
        'numpy': 'numpy',
        'flask': 'flask',
        'requests': 'requests',
        'beautifulsoup4': 'bs4',
        'schedule': 'schedule',
        'scikit-learn': 'sklearn'
    }
    
    missing_packages = []
    
    print("🔍 檢查系統依賴...")
    for package_name, import_name in required_packages.items():
        try:
            if import_name == 'bs4':
                from bs4 import BeautifulSoup
            elif import_name == 'sklearn':
                import sklearn
            else:
                __import__(import_name)
            print(f"✅ {package_name}")
        except ImportError:
            missing_packages.append(package_name)
            print(f"❌ {package_name} 未安裝")
    
    if missing_packages:
        print(f"\n📦 正在安裝缺少的包: {', '.join(missing_packages)}")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install"] + missing_packages, check=True)
            print("✅ 所有依賴包安裝完成")
        except subprocess.CalledProcessError as e:
            print(f"❌ 依賴包安裝失敗: {e}")
            return False
    
    return True


def create_directories():
    """創建必要的目錄"""
    directories = [
        'logs',
        'data', 
        'analysis_results',
        'test_results',
        'web/logs',
        'web/templates',
        'web/static',
        'backtest_results'
    ]
    
    print("📁 檢查並創建必要目錄...")
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ 目錄 {directory}")


def check_port_available(port):
    """檢查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', port))
            return True
    except OSError:
        return False


def find_available_port(start_port=7890):
    """尋找可用端口"""
    for port in range(start_port, start_port + 100):
        if check_port_available(port):
            return port
    return None


def check_database():
    """檢查和初始化資料庫"""
    db_path = "data/lottery_data.db"
    
    if not os.path.exists(db_path):
        print("⚠️ 資料庫文件不存在，創建示例資料庫...")
        
        os.makedirs("data", exist_ok=True)
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 創建威力彩表格
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS Powercolor (
                Period TEXT PRIMARY KEY,
                Sdate TEXT,
                Anumber1 INTEGER,
                Anumber2 INTEGER,
                Anumber3 INTEGER,
                Anumber4 INTEGER,
                Anumber5 INTEGER,
                Anumber6 INTEGER,
                Second_district INTEGER
            )
        """)
        
        # 創建大樂透表格
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS Lotto649 (
                Period TEXT PRIMARY KEY,
                Sdate TEXT,
                Anumber1 INTEGER,
                Anumber2 INTEGER,
                Anumber3 INTEGER,
                Anumber4 INTEGER,
                Anumber5 INTEGER,
                Anumber6 INTEGER,
                Second_district INTEGER
            )
        """)
        
        # 創建今彩539表格
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS DailyCash (
                Period TEXT PRIMARY KEY,
                Sdate TEXT,
                Anumber1 INTEGER,
                Anumber2 INTEGER,
                Anumber3 INTEGER,
                Anumber4 INTEGER,
                Anumber5 INTEGER
            )
        """)
        
        # 插入威力彩示例數據
        sample_powercolor_data = [
            ('114000050', '2025-07-15', 5, 12, 18, 25, 31, 37, 3),
            ('114000051', '2025-07-16', 8, 15, 22, 28, 35, 38, 1),
            ('114000052', '2025-07-17', 2, 9, 16, 23, 30, 36, 5),
            ('114000053', '2025-07-18', 4, 11, 17, 24, 29, 33, 7),
            ('114000054', '2025-07-19', 6, 13, 19, 26, 32, 39, 2),
            ('114000055', '2025-07-20', 1, 2, 7, 14, 28, 31, 2),
            ('114000056', '2025-07-21', 3, 6, 19, 22, 37, 38, 4),
            ('114000057', '2025-07-22', 1, 4, 6, 21, 25, 28, 2),
            ('114000058', '2025-07-23', 10, 18, 20, 24, 36, 38, 4),
        ]
        
        cursor.executemany(
            "INSERT OR REPLACE INTO Powercolor VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            sample_powercolor_data
        )
        
        # 插入大樂透示例數據
        sample_lotto649_data = [
            ('114000050', '2025-07-15', 5, 12, 18, 25, 31, 42, 15),
            ('114000051', '2025-07-16', 8, 15, 22, 28, 35, 45, 22),
            ('114000052', '2025-07-17', 2, 9, 16, 23, 30, 41, 8),
            ('114000053', '2025-07-18', 4, 11, 17, 24, 29, 38, 17),
            ('114000054', '2025-07-19', 6, 13, 19, 26, 32, 44, 3),
            ('114000055', '2025-07-20', 1, 7, 14, 21, 28, 43, 12),
            ('114000056', '2025-07-21', 3, 10, 16, 23, 37, 46, 19),
            ('114000057', '2025-07-22', 9, 15, 22, 29, 36, 48, 7),
            ('114000058', '2025-07-23', 5, 18, 25, 32, 39, 47, 11),
        ]
        
        cursor.executemany(
            "INSERT OR REPLACE INTO Lotto649 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            sample_lotto649_data
        )
        
        # 插入今彩539示例數據
        sample_dailycash_data = [
            ('114000050', '2025-07-15', 5, 12, 18, 25, 31),
            ('114000051', '2025-07-16', 8, 15, 22, 28, 35),
            ('114000052', '2025-07-17', 2, 9, 16, 23, 30),
            ('114000053', '2025-07-18', 4, 11, 17, 24, 29),
            ('114000054', '2025-07-19', 6, 13, 19, 26, 32),
            ('114000055', '2025-07-20', 1, 7, 14, 21, 28),
            ('114000056', '2025-07-21', 3, 10, 16, 23, 37),
            ('114000057', '2025-07-22', 9, 15, 22, 29, 36),
            ('114000058', '2025-07-23', 5, 18, 25, 32, 39),
        ]
        
        cursor.executemany(
            "INSERT OR REPLACE INTO DailyCash VALUES (?, ?, ?, ?, ?, ?, ?)",
            sample_dailycash_data
        )
        
        conn.commit()
        conn.close()
        
        print("✅ 示例資料庫創建完成")
    else:
        print("✅ 資料庫檢查通過")
    
    return True


def update_lottery_data():
    """自動更新開獎數據"""
    print("🔄 檢查開獎數據更新...")
    
    try:
        # 嘗試導入並使用真實更新器
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        try:
            from data.real_lottery_updater import RealLotteryUpdater
            from data.db_manager import DBManager
            
            db_manager = DBManager()
            updater = RealLotteryUpdater(db_manager)
            
            print("🌐 正在從官方網站獲取最新開獎數據...")
            results = updater.update_all_lottery_results()
            
            success_count = sum(1 for r in results.values() if r.get('status') == 'success')
            total_count = len(results)
            
            if success_count > 0:
                print(f"✅ 成功更新 {success_count}/{total_count} 種彩票類型的數據")
            else:
                print("⚠️ 無法獲取最新數據，使用現有數據")
                
        except ImportError as e:
            print(f"⚠️ 數據更新器不可用: {e}")
            print("✅ 使用現有數據繼續運行")
            
    except Exception as e:
        print(f"⚠️ 數據更新失敗: {e}")
        print("✅ 使用現有數據繼續運行")
    
    return True


def show_system_info():
    """顯示系統信息"""
    print("=" * 70)
    print("🎯 增強版彩票預測系統 v3.0".center(70))
    print("=" * 70)
    print(f"📅 啟動時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔧 系統狀態: 就緒")
    print("📁 工作目錄:", os.getcwd())
    print("=" * 70)


def show_main_menu():
    """顯示主選單"""
    print("\n🎯 系統功能選單:")
    print("=" * 50)
    print("1. 🌐 啟動整合Web系統 (推薦)")
    print("2. 📊 啟動回測Web管理器")
    print("3. 🏆 啟動原版Web應用 (包含開獎查詢)")
    print("4. 💻 啟動命令行管理器")
    print("5. 🔄 手動更新開獎數據")
    print("6. 🧪 執行系統測試")
    print("7. 📖 查看使用指南")
    print("0. 退出系統")
    print("=" * 50)


def start_integrated_web():
    """啟動整合Web系統"""
    print("\n🌐 啟動整合Web系統...")
    print("📋 系統功能:")
    print("  - 🏆 開獎結果查詢 (修復版)")
    print("  - 📊 回測分析系統 (完整功能)")
    print("  - 🔍 預測方法分析 (智能比較)")
    print("  - 📈 系統管理和維護")
    
    try:
        print("\n🚀 正在啟動 Streamlit 服務器...")
        print("⏳ 請稍等，瀏覽器將自動開啟...")
        
        # 延遲打開瀏覽器
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open('http://localhost:8501')
                print("🌐 瀏覽器已自動開啟")
            except Exception as e:
                print(f"⚠️ 無法自動開啟瀏覽器: {e}")
                print("請手動訪問: http://localhost:8501")
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # 啟動streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "integrated_web_system.py", 
            "--server.port=8501",
            "--server.headless=true"
        ])
        
    except KeyboardInterrupt:
        print("\n👋 整合Web系統已關閉")
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")


def start_backtest_web():
    """啟動回測Web管理器"""
    print("\n📊 啟動回測Web管理器...")
    print("📋 專業回測功能:")
    print("  - 🔄 執行新回測")
    print("  - 📚 歷史記錄管理")
    print("  - 📈 結果分析")
    print("  - 🔍 方法比較")
    
    try:
        print("\n🚀 正在啟動...")
        
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open('http://localhost:8502')
                print("🌐 瀏覽器已自動開啟")
            except:
                print("請手動訪問: http://localhost:8502")
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        subprocess.run([
            sys.executable, "-m", "streamlit", "run",
            "backtest_web_manager.py",
            "--server.port=8502"
        ])
        
    except KeyboardInterrupt:
        print("\n👋 回測Web管理器已關閉")
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")


def start_original_web_app():
    """啟動原版Web應用"""
    print("\n🏆 啟動原版Web應用...")
    print("📋 原版功能:")
    print("  - 🏆 開獎結果查詢")
    print("  - 🤖 分離式預測")
    print("  - 📈 預測記錄")
    print("  - 📋 分析報告")
    print("  - 🔢 號碼分析")
    print("  - 🔄 自動更新數據")
    
    try:
        # 檢查並修改端口
        available_port = find_available_port(7890)
        if available_port is None:
            print("❌ 無法找到可用端口")
            return
        
        if available_port != 7890:
            print(f"⚠️ 端口 7890 被佔用，使用端口 {available_port}")
        
        # 修改app.py中的端口配置
        app_file = os.path.join('web', 'app.py')
        if os.path.exists(app_file):
            try:
                with open(app_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                import re
                content = re.sub(r'port: int = \d+', f'port: int = {available_port}', content)
                
                with open(app_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ 應用端口已設置為 {available_port}")
            except Exception as e:
                print(f"⚠️ 端口配置警告: {e}")
        
        print(f"\n🌐 Web應用配置:")
        print(f"   📍 訪問地址: http://localhost:{available_port}")
        print(f"   🔧 調試模式: 開啟")
        
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open(f'http://localhost:{available_port}')
                print("🌐 瀏覽器已自動開啟")
            except:
                print(f"請手動訪問: http://localhost:{available_port}")
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # 啟動Flask應用
        print("\n🚀 正在啟動 Flask 服務器...")
        print("⚠️ 按 Ctrl+C 停止服務器")
        
        from web.app import app
        app.run(
            debug=True,
            host='localhost',
            port=available_port,
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\n👋 原版Web應用已關閉")
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")


def start_cli_manager():
    """啟動命令行管理器"""
    print("\n💻 啟動命令行管理器...")
    
    try:
        subprocess.run([sys.executable, "backtest_manager.py"])
    except KeyboardInterrupt:
        print("\n👋 命令行管理器已關閉")
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")


def manual_update_data():
    """手動更新開獎數據"""
    print("\n🔄 手動更新開獎數據...")
    
    try:
        from data.real_lottery_updater import RealLotteryUpdater
        from data.db_manager import DBManager
        
        db_manager = DBManager()
        updater = RealLotteryUpdater(db_manager)
        
        print("🌐 正在從官方網站獲取最新數據...")
        results = updater.update_all_lottery_results()
        
        print("\n📊 更新結果:")
        for lottery_type, result in results.items():
            status = result.get('status', 'unknown')
            if status == 'success':
                count = result.get('new_records', 0)
                print(f"✅ {lottery_type}: 新增 {count} 筆記錄")
            else:
                error = result.get('error', '未知錯誤')
                print(f"❌ {lottery_type}: {error}")
        
        success_count = sum(1 for r in results.values() if r.get('status') == 'success')
        print(f"\n📈 更新完成: {success_count}/{len(results)} 種彩票類型更新成功")
        
    except ImportError:
        print("❌ 數據更新器不可用，請檢查相關模組")
    except Exception as e:
        print(f"❌ 更新失敗: {e}")


def run_system_test():
    """執行系統測試"""
    print("\n🧪 執行系統測試...")
    
    try:
        result = subprocess.run([
            sys.executable, "test_backtest_manager.py"
        ], capture_output=True, text=True)
        
        print("測試輸出:")
        print(result.stdout)
        if result.stderr:
            print("錯誤信息:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")


def show_guide():
    """顯示使用指南"""
    print("\n📖 使用指南")
    print("=" * 60)
    
    print("🚀 快速開始:")
    print("1. 選擇選項1啟動整合Web系統 (推薦新用戶)")
    print("2. 選擇選項3啟動原版Web應用 (包含完整開獎查詢)")
    print("3. 瀏覽器會自動開啟對應的界面")
    
    print("\n💡 功能對比:")
    print("📊 整合Web系統:")
    print("   - 現代化界面設計")
    print("   - 完整回測分析功能")
    print("   - 智能方法比較")
    print("   - 修復了截圖中的問題")
    
    print("🏆 原版Web應用:")
    print("   - 完整開獎結果查詢")
    print("   - 自動更新開獎數據")
    print("   - 傳統預測功能")
    print("   - 詳細分析報告")
    
    print("💻 命令行管理器:")
    print("   - 適合高級用戶")
    print("   - 批量操作功能")
    print("   - 詳細日誌記錄")
    
    print("\n🔧 故障排除:")
    print("- 如果端口被佔用，系統會自動尋找其他可用端口")
    print("- 如果缺少依賴包，系統會自動安裝")
    print("- 如果資料庫不存在，系統會創建示例資料庫")
    
    guide_files = [
        "BACKTEST_MANAGER_GUIDE.md",
        "SYSTEM_FIX_GUIDE.md",
        "FINAL_COMPLETION_SUMMARY.md"
    ]
    
    for guide_file in guide_files:
        if os.path.exists(guide_file):
            print(f"📄 詳細指南: {guide_file}")


def main():
    """主函數"""
    logger = setup_logging()
    
    show_system_info()
    
    # 系統初始化
    print("🔧 系統初始化中...")
    
    # 檢查依賴
    if not check_dependencies():
        print("❌ 依賴檢查失敗，部分功能可能不可用")
        input("按 Enter 繼續或 Ctrl+C 退出...")
    
    # 創建目錄
    create_directories()
    
    # 檢查資料庫
    if not check_database():
        print("❌ 資料庫初始化失敗")
        return
    
    # 更新開獎數據
    update_lottery_data()
    
    print("✅ 系統初始化完成，所有功能就緒！")
    logger.info("增強版彩票預測系統啟動完成")
    
    # 主循環
    while True:
        show_main_menu()
        
        try:
            choice = input("\n請選擇功能 (0-7): ").strip()
            
            if choice == "0":
                print("👋 感謝使用增強版彩票預測系統！")
                logger.info("系統正常退出")
                break
            elif choice == "1":
                start_integrated_web()
            elif choice == "2":
                start_backtest_web()
            elif choice == "3":
                start_original_web_app()
            elif choice == "4":
                start_cli_manager()
            elif choice == "5":
                manual_update_data()
            elif choice == "6":
                run_system_test()
            elif choice == "7":
                show_guide()
            else:
                print("❌ 無效選項，請重新選擇")
                
        except KeyboardInterrupt:
            print("\n\n👋 程式已中斷")
            logger.info("系統被用戶中斷")
            break
        except Exception as e:
            print(f"❌ 執行錯誤: {e}")
            logger.error(f"系統執行錯誤: {e}", exc_info=True)


if __name__ == "__main__":
    main()