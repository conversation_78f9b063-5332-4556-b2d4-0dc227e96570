#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型調校和時間長度測試模組

此模組用於測試不同的訓練資料時間長度對模型預測準確度的影響
以 2024/6/20 為基準日期，只使用該日期前一天的資料進行訓練
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import json
from typing import Dict, List, Tuple, Optional

# 添加專案根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.db_manager import DBManager
from data.feature_engineering import FeatureEngineer
from model.model_trainer import ModelTrainer
from prediction.lottery_predictor import LotteryPredictor
from config_manager import ConfigManager

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ModelTuningTester:
    """模型調校和時間長度測試器"""
    
    def __init__(self, target_date='2024-06-20'):
        """
        初始化測試器
        
        Args:
            target_date: 目標預測日期 (格式: YYYY-MM-DD)
        """
        self.target_date = datetime.strptime(target_date, '%Y-%m-%d')
        self.cutoff_date = self.target_date - timedelta(days=1)  # 訓練資料截止日期
        
        self.config_manager = ConfigManager()
        self.db_manager = DBManager(self.config_manager)
        
        # 測試的時間長度（天數）
        self.time_lengths = [30, 60, 90, 120, 180, 365, 730]  # 1個月到2年
        
        logger.info(f"初始化模型調校測試器")
        logger.info(f"目標預測日期: {self.target_date.strftime('%Y-%m-%d')}")
        logger.info(f"訓練資料截止日期: {self.cutoff_date.strftime('%Y-%m-%d')}")
    
    def load_training_data(self, lottery_type: str, days_back: int) -> pd.DataFrame:
        """
        載入指定時間範圍的訓練資料
        
        Args:
            lottery_type: 彩票類型
            days_back: 往前推的天數
            
        Returns:
            DataFrame: 篩選後的訓練資料
        """
        try:
            # 載入所有歷史資料
            df = self.db_manager.load_lottery_data(lottery_type)
            
            # 計算開始日期
            start_date = self.cutoff_date - timedelta(days=days_back)
            
            # 篩選資料：只使用截止日期前的資料
            mask = (df['Sdate'] >= start_date) & (df['Sdate'] <= self.cutoff_date)
            filtered_df = df[mask].copy().reset_index(drop=True)
            
            logger.info(f"載入 {lottery_type} 訓練資料: {len(filtered_df)} 筆")
            logger.info(f"資料範圍: {start_date.strftime('%Y-%m-%d')} 至 {self.cutoff_date.strftime('%Y-%m-%d')}")
            
            return filtered_df
            
        except Exception as e:
            logger.error(f"載入訓練資料時出錯: {str(e)}")
            return pd.DataFrame()
    
    def get_actual_result(self, lottery_type: str) -> Optional[Dict]:
        """
        獲取目標日期的實際開獎結果
        
        Args:
            lottery_type: 彩票類型
            
        Returns:
            Dict: 實際開獎結果，如果沒有則返回 None
        """
        try:
            df = self.db_manager.load_lottery_data(lottery_type)
            
            # 尋找目標日期的開獎結果
            target_results = df[df['Sdate'].dt.date == self.target_date.date()]
            
            if len(target_results) == 0:
                logger.warning(f"找不到 {self.target_date.strftime('%Y-%m-%d')} 的 {lottery_type} 開獎結果")
                return None
            
            result = target_results.iloc[0]
            
            if lottery_type.lower() == 'powercolor':
                return {
                    'period': result['Period'],
                    'date': result['Sdate'],
                    'main_numbers': [str(result['Anumber1']), str(result['Anumber2']), str(result['Anumber3']), 
                                   str(result['Anumber4']), str(result['Anumber5']), str(result['Anumber6'])],
                    'special_number': str(result['Second_district'])
                }
            elif lottery_type.lower() == 'lotto649':
                return {
                    'period': result['Period'],
                    'date': result['Sdate'],
                    'main_numbers': [result['Anumber1'], result['Anumber2'], result['Anumber3'], 
                                   result['Anumber4'], result['Anumber5'], result['Anumber6']],
                    'special_number': result['SpecialNumber']
                }
            elif lottery_type.lower() == 'dailycash':
                return {
                    'period': result['Period'],
                    'date': result['Sdate'],
                    'main_numbers': [result['Anumber1'], result['Anumber2'], result['Anumber3'], 
                                   result['Anumber4'], result['Anumber5']]
                }
                
        except Exception as e:
            logger.error(f"獲取實際結果時出錯: {str(e)}")
            return None
    
    def train_and_predict(self, lottery_type: str, days_back: int) -> Dict:
        """
        使用指定時間長度的資料訓練模型並進行預測
        
        Args:
            lottery_type: 彩票類型
            days_back: 訓練資料往前推的天數
            
        Returns:
            Dict: 預測結果和相關資訊
        """
        try:
            # 載入訓練資料
            training_data = self.load_training_data(lottery_type, days_back)
            
            if len(training_data) < 10:  # 至少需要10筆資料
                logger.warning(f"訓練資料不足 ({len(training_data)} 筆)，跳過此測試")
                return {
                    'success': False,
                    'error': '訓練資料不足',
                    'data_count': len(training_data)
                }
            
            # 進行特徵工程
            logger.info(f"開始特徵工程 (使用 {len(training_data)} 筆資料)")
            feature_engineer = FeatureEngineer()
            features_df = feature_engineer.create_basic_features(training_data, lottery_type)
            
            if len(features_df) == 0:
                logger.warning("特徵工程後沒有可用資料")
                return {
                    'success': False,
                    'error': '特徵工程後沒有可用資料',
                    'data_count': len(training_data)
                }
            
            logger.info(f"特徵工程完成，產生 {len(features_df)} 筆特徵資料，共 {len(features_df.columns)} 個特徵")
            
            # 初始化模型訓練器
            model_trainer = ModelTrainer()
            
            # 訓練模型
            logger.info(f"開始訓練 {lottery_type} 模型")
            models = model_trainer.train_models(features_df, lottery_type)
            
            if not models or 'error' in models:
                error_msg = models.get('error', '未知錯誤') if models else '模型訓練失敗'
                logger.error(f"模型訓練失敗: {error_msg}")
                return {
                    'success': False,
                    'error': f'模型訓練失敗: {error_msg}',
                    'data_count': len(training_data)
                }
            
            # 初始化預測器
            predictor = LotteryPredictor()
            
            # 進行預測
            logger.info(f"開始預測 {lottery_type}")
            # 使用最新的特徵資料進行預測
            latest_features = features_df.iloc[-1:].drop(['Period'] + [col for col in features_df.columns if col.startswith('Target_')], axis=1)
            prediction_result = predictor.predict(training_data, latest_features, lottery_type, prediction_methods=['ml'])
            
            return {
                'success': True,
                'prediction': prediction_result,
                'data_count': len(training_data),
                'features_count': len(features_df),
                'training_period': f"{(self.cutoff_date - timedelta(days=days_back)).strftime('%Y-%m-%d')} 至 {self.cutoff_date.strftime('%Y-%m-%d')}"
            }
            
        except Exception as e:
            logger.error(f"訓練和預測過程出錯: {str(e)}")
            import traceback
            logger.error(f"詳細錯誤: {traceback.format_exc()}")
            return {
                'success': False,
                'error': str(e),
                'data_count': 0
            }
    
    def calculate_accuracy(self, prediction: Dict, actual: Dict, lottery_type: str) -> Dict:
        """
        計算預測準確度
        
        Args:
            prediction: 預測結果
            actual: 實際結果
            lottery_type: 彩票類型
            
        Returns:
            Dict: 準確度統計
        """
        try:
            if not prediction or not actual:
                return {'main_matches': 0, 'special_match': False, 'accuracy_score': 0.0}
            
            # 提取預測號碼
            if hasattr(prediction, 'to_dict'):
                pred_data = prediction.to_dict()
            else:
                pred_data = prediction
            
            # 嘗試多種可能的欄位名稱
            pred_main = pred_data.get('第一區', pred_data.get('main_numbers', pred_data.get('numbers', [])))
            pred_special = pred_data.get('第二區', pred_data.get('special_number', pred_data.get('special', 0)))
            
            # 確保預測號碼是字符串列表（與實際結果格式一致）
            if pred_main:
                pred_main = [str(num) for num in pred_main]
            
            # 計算主號碼匹配數
            pred_main_set = set(pred_main)
            actual_main_set = set(actual['main_numbers'])
            main_matches = len(pred_main_set.intersection(actual_main_set))
            
            # 計算特別號匹配（如果適用）
            special_match = False
            if lottery_type.lower() in ['powercolor', 'lotto649']:
                special_match = (pred_special == actual['special_number'])
            
            # 計算總體準確度分數
            if lottery_type.lower() == 'dailycash':
                accuracy_score = main_matches / 5.0  # 今彩539只有5個號碼
            else:
                accuracy_score = (main_matches / 6.0) + (0.1 if special_match else 0)  # 主號碼權重0.9，特別號權重0.1
            
            return {
                'main_matches': main_matches,
                'special_match': special_match,
                'accuracy_score': accuracy_score,
                'predicted_main': pred_main,
                'actual_main': actual['main_numbers'],
                'predicted_special': pred_special if lottery_type.lower() in ['powercolor', 'lotto649'] else None,
                'actual_special': actual.get('special_number') if lottery_type.lower() in ['powercolor', 'lotto649'] else None
            }
            
        except Exception as e:
            logger.error(f"計算準確度時出錯: {str(e)}")
            return {'main_matches': 0, 'special_match': False, 'accuracy_score': 0.0}
    
    def run_time_length_test(self, lottery_type: str = 'powercolor') -> Dict:
        """
        執行時間長度測試
        
        Args:
            lottery_type: 彩票類型
            
        Returns:
            Dict: 測試結果
        """
        logger.info(f"開始執行 {lottery_type} 時間長度測試")
        
        # 獲取實際開獎結果
        actual_result = self.get_actual_result(lottery_type)
        if not actual_result:
            logger.error(f"無法獲取 {self.target_date.strftime('%Y-%m-%d')} 的實際開獎結果")
            return {'success': False, 'error': '無法獲取實際開獎結果'}
        
        logger.info(f"實際開獎結果: {actual_result}")
        
        results = []
        
        for days_back in self.time_lengths:
            logger.info(f"\n=== 測試 {days_back} 天訓練資料 ===")
            
            # 訓練模型並預測
            test_result = self.train_and_predict(lottery_type, days_back)
            
            if test_result['success']:
                # 計算準確度
                accuracy = self.calculate_accuracy(
                    test_result['prediction'], 
                    actual_result, 
                    lottery_type
                )
                
                result_entry = {
                    'days_back': days_back,
                    'data_count': test_result['data_count'],
                    'training_period': test_result['training_period'],
                    'prediction': test_result['prediction'],
                    'accuracy': accuracy,
                    'success': True
                }
                
                logger.info(f"預測結果: 主號碼匹配 {accuracy['main_matches']} 個")
                if lottery_type.lower() in ['powercolor', 'lotto649']:
                    logger.info(f"特別號匹配: {'是' if accuracy['special_match'] else '否'}")
                logger.info(f"準確度分數: {accuracy['accuracy_score']:.3f}")
                
            else:
                result_entry = {
                    'days_back': days_back,
                    'data_count': test_result.get('data_count', 0),
                    'error': test_result.get('error', '未知錯誤'),
                    'success': False
                }
                
                logger.warning(f"測試失敗: {test_result.get('error', '未知錯誤')}")
            
            results.append(result_entry)
        
        return {
            'success': True,
            'lottery_type': lottery_type,
            'target_date': self.target_date.strftime('%Y-%m-%d'),
            'cutoff_date': self.cutoff_date.strftime('%Y-%m-%d'),
            'actual_result': actual_result,
            'test_results': results
        }
    
    def generate_report(self, test_results: Dict, output_file: str = None) -> str:
        """
        生成測試報告
        
        Args:
            test_results: 測試結果
            output_file: 輸出檔案路徑（可選）
            
        Returns:
            str: 報告內容
        """
        if not test_results['success']:
            return f"測試失敗: {test_results.get('error', '未知錯誤')}"
        
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append(f"模型調校和時間長度測試報告")
        report_lines.append("=" * 80)
        report_lines.append(f"彩票類型: {test_results['lottery_type']}")
        report_lines.append(f"目標預測日期: {test_results['target_date']}")
        report_lines.append(f"訓練資料截止日期: {test_results['cutoff_date']}")
        report_lines.append("")
        
        actual = test_results['actual_result']
        report_lines.append(f"實際開獎結果:")
        report_lines.append(f"  期數: {actual['period']}")
        report_lines.append(f"  主號碼: {actual['main_numbers']}")
        if 'special_number' in actual:
            report_lines.append(f"  特別號: {actual['special_number']}")
        report_lines.append("")
        
        report_lines.append("測試結果:")
        report_lines.append("-" * 80)
        
        successful_results = [r for r in test_results['test_results'] if r['success']]
        
        if successful_results:
            # 按準確度排序
            successful_results.sort(key=lambda x: x['accuracy']['accuracy_score'], reverse=True)
            
            report_lines.append(f"{'訓練天數':<8} {'資料筆數':<8} {'主號匹配':<8} {'特別號':<6} {'準確度':<8} {'訓練期間':<30}")
            report_lines.append("-" * 80)
            
            for result in successful_results:
                acc = result['accuracy']
                special_str = '是' if acc.get('special_match', False) else '否'
                if test_results['lottery_type'].lower() == 'dailycash':
                    special_str = 'N/A'
                
                report_lines.append(
                    f"{result['days_back']:<8} "
                    f"{result['data_count']:<8} "
                    f"{acc['main_matches']:<8} "
                    f"{special_str:<6} "
                    f"{acc['accuracy_score']:.3f}    "
                    f"{result['training_period']:<30}"
                )
            
            # 最佳結果分析
            best_result = successful_results[0]
            report_lines.append("")
            report_lines.append("最佳結果分析:")
            report_lines.append(f"  最佳訓練天數: {best_result['days_back']} 天")
            report_lines.append(f"  使用資料筆數: {best_result['data_count']} 筆")
            report_lines.append(f"  準確度分數: {best_result['accuracy']['accuracy_score']:.3f}")
            report_lines.append(f"  主號碼匹配: {best_result['accuracy']['main_matches']} 個")
            if test_results['lottery_type'].lower() in ['powercolor', 'lotto649']:
                report_lines.append(f"  特別號匹配: {'是' if best_result['accuracy']['special_match'] else '否'}")
        
        failed_results = [r for r in test_results['test_results'] if not r['success']]
        if failed_results:
            report_lines.append("")
            report_lines.append("失敗的測試:")
            for result in failed_results:
                report_lines.append(f"  {result['days_back']} 天: {result['error']}")
        
        report_lines.append("")
        report_lines.append("=" * 80)
        
        report_content = "\n".join(report_lines)
        
        # 如果指定了輸出檔案，則寫入檔案
        if output_file:
            try:
                os.makedirs(os.path.dirname(output_file), exist_ok=True)
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(report_content)
                logger.info(f"報告已保存到: {output_file}")
            except Exception as e:
                logger.error(f"保存報告時出錯: {str(e)}")
        
        return report_content

def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description='模型調校和時間長度測試')
    parser.add_argument('--lottery-type', default='powercolor', 
                       choices=['powercolor', 'lotto649', 'dailycash'],
                       help='彩票類型')
    parser.add_argument('--target-date', default='2024-06-20',
                       help='目標預測日期 (YYYY-MM-DD)')
    parser.add_argument('--output', default='reports/model_tuning_report.txt',
                       help='輸出報告檔案路徑')
    
    args = parser.parse_args()
    
    try:
        # 初始化測試器
        tester = ModelTuningTester(target_date=args.target_date)
        
        # 執行測試
        results = tester.run_time_length_test(lottery_type=args.lottery_type)
        
        # 生成報告
        report = tester.generate_report(results, output_file=args.output)
        
        # 顯示報告
        print(report)
        
        # 保存結果為JSON
        json_output = args.output.replace('.txt', '.json')
        with open(json_output, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"詳細結果已保存到: {json_output}")
        
    except Exception as e:
        logger.error(f"執行測試時出錯: {str(e)}")
        raise

if __name__ == '__main__':
    main()