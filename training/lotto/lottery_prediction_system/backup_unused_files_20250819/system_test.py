#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彩票預測系統功能測試腳本
測試所有核心組件和API端點
"""

import logging
import json
import time
import sys
import os
from datetime import datetime

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('system_test')

class SystemTester:
    """系統測試器"""
    
    def __init__(self):
        self.test_results = {}
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
    
    def run_all_tests(self):
        """運行所有測試"""
        logger.info("=== 開始系統功能測試 ===")
        
        # 測試數據庫管理器
        self.test_database_manager()
        
        # 測試預測分類器
        self.test_prediction_classifier()
        
        # 測試準確度追蹤器
        self.test_accuracy_tracker()
        
        # 測試推薦引擎
        self.test_recommendation_engine()
        
        # 測試自動化更新器
        self.test_automation_updater()
        
        # 測試Web應用
        self.test_web_application()
        
        # 生成測試報告
        self.generate_test_report()
    
    def test_database_manager(self):
        """測試數據庫管理器"""
        test_name = "數據庫管理器"
        logger.info(f"測試 {test_name}...")
        
        try:
            from data.db_manager import DBManager
            
            # 初始化測試
            db_manager = DBManager()
            assert db_manager is not None, "數據庫管理器初始化失敗"
            
            # 連接測試
            conn = db_manager.create_connection()
            assert conn is not None, "數據庫連接失敗"
            
            # 表結構測試
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            assert len(tables) > 0, "數據庫表不存在"
            
            # 欄位檢查
            cursor.execute("PRAGMA table_info(PowercolorPredictions)")
            columns = [row[1] for row in cursor.fetchall()]
            required_columns = ['AccuracyScore', 'MethodCategory', 'ProcessingTime', 'LastUpdated']
            
            for col in required_columns:
                assert col in columns, f"缺少必要欄位: {col}"
            
            conn.close()
            
            self.record_test_result(test_name, True, "所有測試通過")
            
        except Exception as e:
            self.record_test_result(test_name, False, str(e))
    
    def test_prediction_classifier(self):
        """測試預測分類器"""
        test_name = "預測分類器"
        logger.info(f"測試 {test_name}...")
        
        try:
            from prediction_classifier import prediction_classifier, PredictionType
            
            # 分類測試
            method_info = prediction_classifier.classify_prediction_method('頻率分析')
            assert method_info is not None, "分類方法返回空值"
            assert hasattr(method_info, 'display_name'), "分類結果缺少顯示名稱"
            assert hasattr(method_info, 'category'), "分類結果缺少分類"
            
            # 所有方法類型測試
            method_count = len(prediction_classifier.method_info)
            assert method_count > 0, "沒有可用的方法類型"
            
            self.record_test_result(test_name, True, f"成功測試 {method_count} 種方法類型")
            
        except Exception as e:
            self.record_test_result(test_name, False, str(e))
    
    def test_accuracy_tracker(self):
        """測試準確度追蹤器"""
        test_name = "準確度追蹤器"
        logger.info(f"測試 {test_name}...")
        
        try:
            from prediction_accuracy_tracker import PredictionAccuracyTracker
            from data.db_manager import DBManager
            
            db_manager = DBManager()
            tracker = PredictionAccuracyTracker(db_manager)
            
            # 統計計算測試
            stats = tracker._calculate_overall_stats('powercolor')
            assert isinstance(stats, list), "統計結果格式錯誤"
            
            # 詳細統計測試
            detailed_stats = tracker._get_accuracy_statistics('powercolor', 30)
            assert isinstance(detailed_stats, dict), "詳細統計結果格式錯誤"
            assert 'total_predictions' in detailed_stats, "缺少總預測數統計"
            
            # 趨勢分析測試
            trends = tracker._get_accuracy_trends('powercolor', 30)
            assert isinstance(trends, dict), "趨勢分析結果格式錯誤"
            assert 'trend_direction' in trends, "缺少趨勢方向"
            
            self.record_test_result(test_name, True, "所有功能測試通過")
            
        except Exception as e:
            self.record_test_result(test_name, False, str(e))
    
    def test_recommendation_engine(self):
        """測試推薦引擎"""
        test_name = "推薦引擎"
        logger.info(f"測試 {test_name}...")
        
        try:
            from method_recommendation_engine import get_method_recommendation
            
            # 推薦生成測試
            result = get_method_recommendation('powercolor', 'balanced', 'medium')
            assert isinstance(result, dict), "推薦結果格式錯誤"
            
            if 'error' not in result:
                assert 'recommendations' in result, "缺少推薦列表"
                assert 'context' in result, "缺少推薦上下文"
                assert 'summary' in result, "缺少推薦摘要"
                
                recommendations = result['recommendations']
                if recommendations:
                    first_rec = recommendations[0]
                    assert 'method' in first_rec, "推薦項缺少方法名稱"
                    assert 'score' in first_rec, "推薦項缺少分數"
            
            self.record_test_result(test_name, True, "推薦生成測試通過")
            
        except Exception as e:
            self.record_test_result(test_name, False, str(e))
    
    def test_automation_updater(self):
        """測試自動化更新器"""
        test_name = "自動化更新器"
        logger.info(f"測試 {test_name}...")
        
        try:
            from automated_accuracy_updater import AutomatedAccuracyUpdater
            from data.db_manager import DBManager
            
            db_manager = DBManager()
            updater = AutomatedAccuracyUpdater(db_manager)
            
            # 初始化測試
            assert updater.components_available, "自動化組件不可用"
            
            # 手動更新測試
            result = updater.manual_update('powercolor')
            assert isinstance(result, dict), "手動更新結果格式錯誤"
            assert 'powercolor' in result, "缺少彩票類型結果"
            
            self.record_test_result(test_name, True, "自動化功能測試通過")
            
        except Exception as e:
            self.record_test_result(test_name, False, str(e))
    
    def test_web_application(self):
        """測試Web應用"""
        test_name = "Web應用"
        logger.info(f"測試 {test_name}...")
        
        try:
            from web.app import app
            
            # 檢查重要路由
            important_routes = [
                '/prediction_method_analysis',
                '/prediction_performance_dashboard', 
                '/api/accuracy_update',
                '/api/method_recommendation',
                '/api/automation/start'
            ]
            
            registered_routes = []
            for rule in app.url_map.iter_rules():
                registered_routes.append(rule.rule)
            
            missing_routes = []
            for route in important_routes:
                found = any(route in reg_route for reg_route in registered_routes)
                if not found:
                    missing_routes.append(route)
            
            assert len(missing_routes) == 0, f"缺少路由: {missing_routes}"
            
            self.record_test_result(test_name, True, f"所有 {len(important_routes)} 個重要路由已註冊")
            
        except Exception as e:
            self.record_test_result(test_name, False, str(e))
    
    def record_test_result(self, test_name, passed, message):
        """記錄測試結果"""
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
            logger.info(f"✅ {test_name}: {message}")
        else:
            self.failed_tests += 1
            logger.error(f"❌ {test_name}: {message}")
        
        self.test_results[test_name] = {
            'passed': passed,
            'message': message,
            'timestamp': datetime.now().isoformat()
        }
    
    def generate_test_report(self):
        """生成測試報告"""
        logger.info("=== 生成測試報告 ===")
        
        report = {
            'test_summary': {
                'total_tests': self.total_tests,
                'passed_tests': self.passed_tests,
                'failed_tests': self.failed_tests,
                'success_rate': (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
            },
            'test_results': self.test_results,
            'generated_at': datetime.now().isoformat()
        }
        
        # 保存報告到文件
        try:
            os.makedirs('test_results', exist_ok=True)
            report_file = f'test_results/system_test_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"測試報告已保存: {report_file}")
            
        except Exception as e:
            logger.error(f"保存測試報告失敗: {e}")
        
        # 控制台輸出摘要
        logger.info(f"測試完成: {self.passed_tests}/{self.total_tests} 通過 ({report['test_summary']['success_rate']:.1f}%)")
        
        if self.failed_tests > 0:
            logger.warning("失敗的測試:")
            for name, result in self.test_results.items():
                if not result['passed']:
                    logger.warning(f"  - {name}: {result['message']}")
        
        return report

def main():
    """主函數"""
    tester = SystemTester()
    report = tester.run_all_tests()
    
    # 如果有失敗的測試，返回錯誤代碼
    if tester.failed_tests > 0:
        sys.exit(1)
    else:
        logger.info("所有測試通過！系統功能正常")
        sys.exit(0)

if __name__ == "__main__":
    main()