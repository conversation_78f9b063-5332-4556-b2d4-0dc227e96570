#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, jsonify, request
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import DBManager
import pandas as pd
from datetime import datetime

app = Flask(__name__)
db_manager = DBManager()

@app.route('/')
def index():
    """首頁"""
    return jsonify({
        'message': '彩票真實數據API',
        'endpoints': {
            '/api/data/<lottery_type>': '獲取彩票數據',
            '/api/stats/<lottery_type>': '獲取統計信息',
            '/api/latest/<lottery_type>': '獲取最新開獎'
        },
        'lottery_types': ['powercolor', 'lotto649', 'dailycash']
    })

@app.route('/api/data/<lottery_type>')
def get_lottery_data(lottery_type):
    """獲取彩票數據"""
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        
        # 加載數據
        data = db_manager.load_lottery_data(lottery_type)
        if data is None or len(data) == 0:
            return jsonify({'error': f'沒有找到 {lottery_type} 的數據'}), 404
            
        # 分頁
        total = len(data)
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        page_data = data.iloc[start_idx:end_idx]
        
        # 轉換為字典格式
        results = []
        for _, row in page_data.iterrows():
            result = row.to_dict()
            # 確保日期格式正確
            if 'Sdate' in result:
                result['date'] = str(result['Sdate'])
            results.append(result)
            
        return jsonify({
            'lottery_type': lottery_type,
            'total': total,
            'page': page,
            'per_page': per_page,
            'data': results
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/latest/<lottery_type>')
def get_latest_result(lottery_type):
    """獲取最新開獎結果"""
    try:
        data = db_manager.load_lottery_data(lottery_type)
        if data is None or len(data) == 0:
            return jsonify({'error': f'沒有找到 {lottery_type} 的數據'}), 404
            
        # 獲取最新一筆
        latest = data.iloc[-1].to_dict()
        
        # 格式化輸出
        if lottery_type == 'powercolor':
            numbers = [latest[f'Anumber{i}'] for i in range(1, 7)]
            special = latest['Snumber']
            result = {
                'period': latest['Period'],
                'date': str(latest['Sdate']),
                'numbers': numbers,
                'special_number': special,
                'display': f"{'-'.join(map(str, numbers))} + {special}"
            }
        elif lottery_type == 'lotto649':
            numbers = [latest[f'Anumber{i}'] for i in range(1, 7)]
            special = latest['Snumber']
            result = {
                'period': latest['Period'],
                'date': str(latest['Sdate']),
                'numbers': numbers,
                'special_number': special,
                'display': f"{'-'.join(map(str, numbers))} + {special}"
            }
        elif lottery_type == 'dailycash':
            numbers = [latest[f'Anumber{i}'] for i in range(1, 6)]
            result = {
                'period': latest['Period'],
                'date': str(latest['Sdate']),
                'numbers': numbers,
                'display': '-'.join(map(str, numbers))
            }
            
        return jsonify({
            'lottery_type': lottery_type,
            'latest_result': result
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats/<lottery_type>')
def get_lottery_stats(lottery_type):
    """獲取彩票統計信息"""
    try:
        data = db_manager.load_lottery_data(lottery_type)
        if data is None or len(data) == 0:
            return jsonify({'error': f'沒有找到 {lottery_type} 的數據'}), 404
            
        # 基本統計
        total_draws = len(data)
        date_range = {
            'start': str(data['Sdate'].min()),
            'end': str(data['Sdate'].max())
        }
        
        # 號碼頻率統計
        if lottery_type in ['powercolor', 'lotto649']:
            number_cols = [f'Anumber{i}' for i in range(1, 7)]
            all_numbers = []
            for col in number_cols:
                all_numbers.extend(data[col].tolist())
            
            # 計算頻率
            from collections import Counter
            freq = Counter(all_numbers)
            hot_numbers = freq.most_common(10)
            cold_numbers = freq.most_common()[-10:]
            
            stats = {
                'total_draws': total_draws,
                'date_range': date_range,
                'hot_numbers': hot_numbers,
                'cold_numbers': cold_numbers[::-1],  # 反轉順序
                'number_range': '1-49' if lottery_type == 'powercolor' else '1-49'
            }
            
        elif lottery_type == 'dailycash':
            number_cols = [f'Anumber{i}' for i in range(1, 6)]
            all_numbers = []
            for col in number_cols:
                all_numbers.extend(data[col].tolist())
                
            from collections import Counter
            freq = Counter(all_numbers)
            hot_numbers = freq.most_common(10)
            cold_numbers = freq.most_common()[-10:]
            
            stats = {
                'total_draws': total_draws,
                'date_range': date_range,
                'hot_numbers': hot_numbers,
                'cold_numbers': cold_numbers[::-1],
                'number_range': '1-39'
            }
            
        return jsonify({
            'lottery_type': lottery_type,
            'statistics': stats
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("啟動彩票真實數據API服務器...")
    print("數據統計:")
    
    # 顯示數據統計
    for lottery_type in ['powercolor', 'lotto649', 'dailycash']:
        try:
            data = db_manager.load_lottery_data(lottery_type)
            if data is not None:
                print(f"  {lottery_type}: {len(data)} 筆記錄")
        except:
            print(f"  {lottery_type}: 加載失敗")
    
    print("\n服務器啟動在 http://localhost:5561")
    app.run(host='localhost', port=5561, debug=False)