import datetime
from TaiwanLottery import TaiwanLotteryCrawler

class LotteryQueryTool:
    def __init__(self):
        self.crawler = TaiwanLotteryCrawler()

    def query_specific_date(self, date):
        """
        查詢特定日期的彩券開獎結果
        
        參數:
        date (str): 日期字串，格式為 'YYYY-MM-DD'
        
        回傳:
        dict: 開獎結果詳細資訊
        """
        try:
            # 將日期字串轉換為 datetime 物件
            query_date = datetime.datetime.strptime(date, '%Y-%m-%d')
            
            # 使用爬蟲查詢開獎結果
            # 根據實際的類別方法名稱調整
            result = self.crawler.crawl(query_date)
            
            return result
        except ValueError as e:
            print(f"日期格式錯誤: {e}")
            print("請使用 'YYYY-MM-DD' 格式，例如 '2024-04-09'")
            return None
        except AttributeError:
            print("無法找到查詢方法。請確認 TaiwanLotteryCrawler 類別的方法名稱。")
            return None
        except Exception as e:
            print(f"查詢過程發生錯誤: {e}")
            return None

    def query_recent_results(self, num_of_results=5):
        """
        查詢最近的開獎結果
        
        參數:
        num_of_results (int): 要查詢的最近開獎次數，預設為5
        
        回傳:
        list: 最近的開獎結果清單
        """
        try:
            # 查詢最近的開獎結果
            # 根據實際的類別方法名稱調整
            recent_results = self.crawler.recent(num_of_results)
            
            return recent_results
        except AttributeError:
            print("無法找到查詢最近開獎結果的方法。請確認 TaiwanLotteryCrawler 類別的方法名稱。")
            return None
        except Exception as e:
            print(f"查詢最近開獎結果時發生錯誤: {e}")
            return None

def main():
    # 建立查詢工具實例
    lottery_tool = LotteryQueryTool()

    while True:
        print("\n--- 台灣彩券查詢工具 ---")
        print("1. 查詢特定日期開獎結果")
        print("2. 查詢最近開獎結果")
        print("3. 離開")
        
        choice = input("請選擇操作 (1/2/3): ")
        
        if choice == '1':
            date = input("請輸入日期 (格式: YYYY-MM-DD): ")
            result = lottery_tool.query_specific_date(date)
            if result:
                print("\n開獎結果:")
                print(result)
        
        elif choice == '2':
            num = input("請輸入要查詢的最近開獎次數 (預設為5): ")
            try:
                num = int(num) if num else 5
                results = lottery_tool.query_recent_results(num)
                if results:
                    print("\n最近的開獎結果:")
                    for idx, result in enumerate(results, 1):
                        print(f"\n第 {idx} 筆結果:")
                        print(result)
            except ValueError:
                print("請輸入有效的數字")
        
        elif choice == '3':
            print("感謝使用，再見！")
            break
        
        else:
            print("無效的選擇，請重新輸入")

if __name__ == "__main__":
    main()