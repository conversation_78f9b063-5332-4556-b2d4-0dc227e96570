#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增強板路分析模組
負責進行深度的彩票板路分析和模式識別
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
import json
from datetime import datetime, timedelta
from collections import Counter, defaultdict, deque
import statistics
import math

class EnhancedBoardAnalyzer:
    """
    增強板路分析器類
    負責進行深度的彩票板路分析、模式識別和趨勢預測
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化增強板路分析器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self.analysis_cache = {}
        self.board_patterns = {}
        self.trend_history = []
        
        # 板路分析參數
        self.pattern_window = self.config.get('pattern_window', 20)
        self.trend_threshold = self.config.get('trend_threshold', 0.6)
        self.confidence_threshold = self.config.get('confidence_threshold', 0.7)
        
    def analyze_board_patterns(self, draws: List[List[int]], lottery_type: str = 'powerball') -> Dict[str, Any]:
        """
        分析板路模式
        
        Args:
            draws: 開獎結果列表
            lottery_type: 彩票類型
            
        Returns:
            板路分析結果字典
        """
        try:
            self.logger.info(f"開始分析 {lottery_type} 板路模式")
            
            if not draws or len(draws) < 5:
                return self._get_empty_board_result(lottery_type)
            
            # 基本板路分析
            basic_patterns = self._analyze_basic_patterns(draws)
            
            # 高級模式識別
            advanced_patterns = self._analyze_advanced_patterns(draws)
            
            # 趨勢分析
            trend_analysis = self._analyze_board_trends(draws)
            
            # 週期性分析
            cycle_analysis = self._analyze_cycles(draws)
            
            # 相關性分析
            correlation_analysis = self._analyze_correlations(draws)
            
            # 預測建議
            predictions = self._generate_board_predictions(draws, basic_patterns, advanced_patterns)
            
            board_result = {
                'lottery_type': lottery_type,
                'total_draws': len(draws),
                'analysis_timestamp': datetime.now().isoformat(),
                'basic_patterns': basic_patterns,
                'advanced_patterns': advanced_patterns,
                'trend_analysis': trend_analysis,
                'cycle_analysis': cycle_analysis,
                'correlation_analysis': correlation_analysis,
                'predictions': predictions,
                'confidence_score': self._calculate_confidence_score(basic_patterns, advanced_patterns)
            }
            
            # 緩存結果
            cache_key = f"{lottery_type}_board_{len(draws)}"
            self.analysis_cache[cache_key] = board_result
            
            self.logger.info(f"{lottery_type} 板路分析完成")
            return board_result
            
        except Exception as e:
            self.logger.error(f"板路分析失敗: {e}")
            return self._get_empty_board_result(lottery_type)
    
    def _analyze_basic_patterns(self, draws: List[List[int]]) -> Dict[str, Any]:
        """
        分析基本板路模式
        """
        patterns = {
            'big_small_pattern': [],
            'odd_even_pattern': [],
            'sum_pattern': [],
            'range_pattern': []
        }
        
        max_num = max(max(draw) for draw in draws) if draws else 49
        mid_point = max_num // 2
        
        for draw in draws:
            # 大小路分析
            big_count = sum(1 for num in draw if num > mid_point)
            small_count = len(draw) - big_count
            patterns['big_small_pattern'].append('B' if big_count > small_count else 'S')
            
            # 奇偶路分析
            odd_count = sum(1 for num in draw if num % 2 == 1)
            even_count = len(draw) - odd_count
            patterns['odd_even_pattern'].append('O' if odd_count > even_count else 'E')
            
            # 和值路分析
            draw_sum = sum(draw)
            avg_sum = max_num * len(draw) // 2
            patterns['sum_pattern'].append('H' if draw_sum > avg_sum else 'L')
            
            # 區間路分析
            low_count = sum(1 for num in draw if num <= max_num // 3)
            mid_count = sum(1 for num in draw if max_num // 3 < num <= max_num * 2 // 3)
            high_count = sum(1 for num in draw if num > max_num * 2 // 3)
            
            if low_count >= mid_count and low_count >= high_count:
                patterns['range_pattern'].append('L')
            elif mid_count >= high_count:
                patterns['range_pattern'].append('M')
            else:
                patterns['range_pattern'].append('H')
        
        # 分析模式統計
        pattern_stats = {}
        for pattern_name, pattern_data in patterns.items():
            pattern_stats[pattern_name] = {
                'sequence': ''.join(pattern_data[-20:]),  # 最近20期
                'distribution': dict(Counter(pattern_data)),
                'current_streak': self._get_current_streak(pattern_data),
                'longest_streak': self._get_longest_streak(pattern_data)
            }
        
        return pattern_stats
    
    def _analyze_advanced_patterns(self, draws: List[List[int]]) -> Dict[str, Any]:
        """
        分析高級板路模式
        """
        advanced_patterns = {}
        
        # 跳躍模式分析
        jump_patterns = self._analyze_jump_patterns(draws)
        advanced_patterns['jump_patterns'] = jump_patterns
        
        # 重複模式分析
        repeat_patterns = self._analyze_repeat_patterns(draws)
        advanced_patterns['repeat_patterns'] = repeat_patterns
        
        # 間隔模式分析
        interval_patterns = self._analyze_interval_patterns(draws)
        advanced_patterns['interval_patterns'] = interval_patterns
        
        # 對稱模式分析
        symmetry_patterns = self._analyze_symmetry_patterns(draws)
        advanced_patterns['symmetry_patterns'] = symmetry_patterns
        
        return advanced_patterns
    
    def _analyze_jump_patterns(self, draws: List[List[int]]) -> Dict[str, Any]:
        """
        分析跳躍模式
        """
        if len(draws) < 3:
            return {}
        
        jump_sequences = []
        for i in range(len(draws) - 1):
            current_draw = set(draws[i])
            next_draw = set(draws[i + 1])
            
            # 計算跳躍程度
            common_numbers = len(current_draw & next_draw)
            total_numbers = len(current_draw | next_draw)
            jump_ratio = 1 - (common_numbers / total_numbers) if total_numbers > 0 else 0
            
            jump_sequences.append(jump_ratio)
        
        return {
            'average_jump_ratio': statistics.mean(jump_sequences) if jump_sequences else 0,
            'jump_trend': 'increasing' if len(jump_sequences) > 5 and jump_sequences[-5:] > jump_sequences[-10:-5] else 'stable',
            'recent_jumps': jump_sequences[-10:]
        }
    
    def _analyze_repeat_patterns(self, draws: List[List[int]]) -> Dict[str, Any]:
        """
        分析重複模式
        """
        if len(draws) < 2:
            return {}
        
        repeat_counts = []
        for i in range(1, len(draws)):
            prev_draw = set(draws[i-1])
            curr_draw = set(draws[i])
            repeat_count = len(prev_draw & curr_draw)
            repeat_counts.append(repeat_count)
        
        return {
            'average_repeats': statistics.mean(repeat_counts) if repeat_counts else 0,
            'max_repeats': max(repeat_counts) if repeat_counts else 0,
            'repeat_distribution': dict(Counter(repeat_counts)),
            'recent_repeat_trend': repeat_counts[-5:] if len(repeat_counts) >= 5 else repeat_counts
        }
    
    def _analyze_interval_patterns(self, draws: List[List[int]]) -> Dict[str, Any]:
        """
        分析間隔模式
        """
        if not draws:
            return {}
        
        # 計算每個號碼的出現間隔
        number_intervals = defaultdict(list)
        last_seen = {}
        
        for draw_index, draw in enumerate(draws):
            for num in draw:
                if num in last_seen:
                    interval = draw_index - last_seen[num]
                    number_intervals[num].append(interval)
                last_seen[num] = draw_index
        
        # 分析間隔統計
        interval_stats = {}
        for num, intervals in number_intervals.items():
            if intervals:
                interval_stats[num] = {
                    'average_interval': statistics.mean(intervals),
                    'min_interval': min(intervals),
                    'max_interval': max(intervals),
                    'interval_variance': statistics.variance(intervals) if len(intervals) > 1 else 0
                }
        
        return {
            'number_intervals': dict(interval_stats),
            'hot_numbers': [num for num, stats in interval_stats.items() if stats['average_interval'] < 5],
            'cold_numbers': [num for num, stats in interval_stats.items() if stats['average_interval'] > 15]
        }
    
    def _analyze_symmetry_patterns(self, draws: List[List[int]]) -> Dict[str, Any]:
        """
        分析對稱模式
        """
        if not draws:
            return {}
        
        max_num = max(max(draw) for draw in draws) if draws else 49
        symmetry_scores = []
        
        for draw in draws:
            # 計算對稱性分數
            sorted_draw = sorted(draw)
            center = max_num / 2
            
            symmetry_score = 0
            for num in sorted_draw:
                mirror_num = max_num + 1 - num
                if mirror_num in sorted_draw:
                    symmetry_score += 1
            
            symmetry_scores.append(symmetry_score / len(draw))
        
        return {
            'average_symmetry': statistics.mean(symmetry_scores) if symmetry_scores else 0,
            'symmetry_trend': symmetry_scores[-10:] if len(symmetry_scores) >= 10 else symmetry_scores,
            'high_symmetry_draws': [i for i, score in enumerate(symmetry_scores) if score > 0.5]
        }
    
    def _analyze_board_trends(self, draws: List[List[int]]) -> Dict[str, Any]:
        """
        分析板路趨勢
        """
        if len(draws) < 10:
            return {'trend_analysis': 'insufficient_data'}
        
        # 分析最近趨勢
        recent_draws = draws[-10:]
        earlier_draws = draws[-20:-10] if len(draws) >= 20 else draws[:-10]
        
        trends = {
            'number_frequency_trend': self._analyze_frequency_trend(recent_draws, earlier_draws),
            'sum_trend': self._analyze_sum_trend(recent_draws, earlier_draws),
            'pattern_stability': self._analyze_pattern_stability(draws)
        }
        
        return trends
    
    def _analyze_cycles(self, draws: List[List[int]]) -> Dict[str, Any]:
        """
        分析週期性模式
        """
        if len(draws) < 20:
            return {'cycle_analysis': 'insufficient_data'}
        
        # 檢測可能的週期
        cycles = {}
        
        for cycle_length in range(3, min(15, len(draws) // 4)):
            cycle_strength = self._calculate_cycle_strength(draws, cycle_length)
            if cycle_strength > 0.3:  # 閾值
                cycles[cycle_length] = cycle_strength
        
        return {
            'detected_cycles': cycles,
            'strongest_cycle': max(cycles.items(), key=lambda x: x[1]) if cycles else None,
            'cycle_prediction': self._predict_next_in_cycle(draws, cycles) if cycles else None
        }
    
    def _analyze_correlations(self, draws: List[List[int]]) -> Dict[str, Any]:
        """
        分析號碼相關性
        """
        if len(draws) < 10:
            return {}
        
        # 計算號碼共現頻率
        co_occurrence = defaultdict(int)
        total_pairs = 0
        
        for draw in draws:
            for i in range(len(draw)):
                for j in range(i + 1, len(draw)):
                    pair = tuple(sorted([draw[i], draw[j]]))
                    co_occurrence[pair] += 1
                    total_pairs += 1
        
        # 找出高相關性的號碼對
        high_correlation_pairs = []
        for pair, count in co_occurrence.items():
            correlation_strength = count / len(draws)
            if correlation_strength > 0.2:  # 閾值
                high_correlation_pairs.append((pair, correlation_strength))
        
        return {
            'high_correlation_pairs': sorted(high_correlation_pairs, key=lambda x: x[1], reverse=True)[:10],
            'total_analyzed_pairs': len(co_occurrence),
            'average_correlation': sum(co_occurrence.values()) / len(co_occurrence) if co_occurrence else 0
        }
    
    def _generate_board_predictions(self, draws: List[List[int]], basic_patterns: Dict, advanced_patterns: Dict) -> Dict[str, Any]:
        """
        基於板路分析生成預測
        """
        predictions = {
            'recommended_numbers': [],
            'avoid_numbers': [],
            'pattern_based_suggestions': [],
            'confidence_level': 'medium'
        }
        
        try:
            # 基於基本模式的預測
            if 'big_small_pattern' in basic_patterns:
                recent_pattern = basic_patterns['big_small_pattern']['sequence'][-5:]
                if recent_pattern.count('B') > recent_pattern.count('S'):
                    predictions['pattern_based_suggestions'].append('考慮選擇較多小號')
                else:
                    predictions['pattern_based_suggestions'].append('考慮選擇較多大號')
            
            # 基於高級模式的預測
            if 'interval_patterns' in advanced_patterns:
                hot_numbers = advanced_patterns['interval_patterns'].get('hot_numbers', [])
                cold_numbers = advanced_patterns['interval_patterns'].get('cold_numbers', [])
                
                predictions['recommended_numbers'].extend(hot_numbers[:5])
                predictions['avoid_numbers'].extend(cold_numbers[:3])
            
            # 設置信心水平
            if len(predictions['recommended_numbers']) > 3:
                predictions['confidence_level'] = 'high'
            elif len(predictions['recommended_numbers']) > 1:
                predictions['confidence_level'] = 'medium'
            else:
                predictions['confidence_level'] = 'low'
                
        except Exception as e:
            self.logger.error(f"生成預測失敗: {e}")
        
        return predictions
    
    def _calculate_confidence_score(self, basic_patterns: Dict, advanced_patterns: Dict) -> float:
        """
        計算分析信心分數
        """
        score = 0.5  # 基礎分數
        
        # 基於模式一致性調整分數
        if basic_patterns:
            score += 0.2
        
        if advanced_patterns:
            score += 0.2
        
        # 確保分數在0-1範圍內
        return min(1.0, max(0.0, score))
    
    def _get_current_streak(self, pattern_data: List[str]) -> Dict[str, int]:
        """
        獲取當前連續模式
        """
        if not pattern_data:
            return {}
        
        current_char = pattern_data[-1]
        streak = 1
        
        for i in range(len(pattern_data) - 2, -1, -1):
            if pattern_data[i] == current_char:
                streak += 1
            else:
                break
        
        return {current_char: streak}
    
    def _get_longest_streak(self, pattern_data: List[str]) -> Dict[str, int]:
        """
        獲取最長連續模式
        """
        if not pattern_data:
            return {}
        
        streaks = defaultdict(int)
        current_streaks = defaultdict(int)
        
        for char in pattern_data:
            current_streaks[char] += 1
            streaks[char] = max(streaks[char], current_streaks[char])
            
            # 重置其他字符的連續計數
            for other_char in current_streaks:
                if other_char != char:
                    current_streaks[other_char] = 0
        
        return dict(streaks)
    
    def _analyze_frequency_trend(self, recent_draws: List[List[int]], earlier_draws: List[List[int]]) -> Dict[str, Any]:
        """
        分析頻率趨勢
        """
        recent_freq = Counter([num for draw in recent_draws for num in draw])
        earlier_freq = Counter([num for draw in earlier_draws for num in draw])
        
        trending_up = []
        trending_down = []
        
        for num in set(list(recent_freq.keys()) + list(earlier_freq.keys())):
            recent_rate = recent_freq.get(num, 0) / len(recent_draws)
            earlier_rate = earlier_freq.get(num, 0) / len(earlier_draws) if earlier_draws else 0
            
            if recent_rate > earlier_rate * 1.5:
                trending_up.append(num)
            elif recent_rate < earlier_rate * 0.5:
                trending_down.append(num)
        
        return {
            'trending_up': trending_up,
            'trending_down': trending_down
        }
    
    def _analyze_sum_trend(self, recent_draws: List[List[int]], earlier_draws: List[List[int]]) -> Dict[str, Any]:
        """
        分析和值趨勢
        """
        recent_sums = [sum(draw) for draw in recent_draws]
        earlier_sums = [sum(draw) for draw in earlier_draws] if earlier_draws else []
        
        recent_avg = statistics.mean(recent_sums) if recent_sums else 0
        earlier_avg = statistics.mean(earlier_sums) if earlier_sums else recent_avg
        
        return {
            'recent_average': recent_avg,
            'earlier_average': earlier_avg,
            'trend_direction': 'increasing' if recent_avg > earlier_avg else 'decreasing'
        }
    
    def _analyze_pattern_stability(self, draws: List[List[int]]) -> Dict[str, Any]:
        """
        分析模式穩定性
        """
        if len(draws) < 10:
            return {'stability': 'insufficient_data'}
        
        # 計算最近10期與之前10期的相似度
        recent_10 = draws[-10:]
        previous_10 = draws[-20:-10] if len(draws) >= 20 else draws[:-10]
        
        # 簡單的相似度計算
        recent_numbers = set([num for draw in recent_10 for num in draw])
        previous_numbers = set([num for draw in previous_10 for num in draw])
        
        similarity = len(recent_numbers & previous_numbers) / len(recent_numbers | previous_numbers) if recent_numbers | previous_numbers else 0
        
        return {
            'pattern_similarity': similarity,
            'stability_level': 'high' if similarity > 0.7 else 'medium' if similarity > 0.4 else 'low'
        }
    
    def _calculate_cycle_strength(self, draws: List[List[int]], cycle_length: int) -> float:
        """
        計算週期強度
        """
        if len(draws) < cycle_length * 2:
            return 0.0
        
        matches = 0
        total_comparisons = 0
        
        for i in range(cycle_length, len(draws)):
            current_draw = set(draws[i])
            cycle_draw = set(draws[i - cycle_length])
            
            # 計算相似度
            intersection = len(current_draw & cycle_draw)
            union = len(current_draw | cycle_draw)
            
            if union > 0:
                matches += intersection / union
                total_comparisons += 1
        
        return matches / total_comparisons if total_comparisons > 0 else 0.0
    
    def _predict_next_in_cycle(self, draws: List[List[int]], cycles: Dict[int, float]) -> Dict[str, Any]:
        """
        基於週期預測下一期
        """
        if not cycles:
            return {}
        
        strongest_cycle = max(cycles.items(), key=lambda x: x[1])
        cycle_length, strength = strongest_cycle
        
        if len(draws) >= cycle_length:
            reference_draw = draws[-cycle_length]
            return {
                'cycle_length': cycle_length,
                'reference_draw': reference_draw,
                'strength': strength,
                'suggested_numbers': reference_draw[:3]  # 建議前3個號碼
            }
        
        return {}
    
    def _get_empty_board_result(self, lottery_type: str) -> Dict[str, Any]:
        """
        返回空的板路分析結果
        """
        return {
            'lottery_type': lottery_type,
            'total_draws': 0,
            'analysis_timestamp': datetime.now().isoformat(),
            'basic_patterns': {},
            'advanced_patterns': {},
            'trend_analysis': {},
            'cycle_analysis': {},
            'correlation_analysis': {},
            'predictions': {},
            'confidence_score': 0.0
        }
    
    def save_board_analysis(self, result: Dict[str, Any], filepath: str) -> bool:
        """
        保存板路分析結果
        
        Args:
            result: 分析結果
            filepath: 文件路徑
            
        Returns:
            是否保存成功
        """
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"板路分析結果已保存到: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存板路分析結果失敗: {e}")
            return False
    
    def get_cached_board_analysis(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """
        獲取緩存的板路分析結果
        
        Args:
            cache_key: 緩存鍵
            
        Returns:
            緩存的分析結果或None
        """
        return self.analysis_cache.get(cache_key)
    
    def clear_board_cache(self):
        """
        清除板路分析緩存
        """
        self.analysis_cache.clear()
        self.board_patterns.clear()
        self.trend_history.clear()
        self.logger.info("板路分析緩存已清除")