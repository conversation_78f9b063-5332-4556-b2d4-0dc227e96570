"""
彩票預測系統 - 主程式
整合所有模組提供完整功能，支援威力彩、大樂透和今彩539
支援多候選結果生成與管理
"""

import os
import logging
from datetime import datetime
import argparse
import json
import sys
import traceback
try:
    import pandas as pd
except ImportError:
    pd = None


# 設置日誌目錄
if not os.path.exists('logs'):
    os.makedirs('logs')

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/lottery_system_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('lottery_system')

def setup_environment():
    """設置環境，確保必要的目錄存在"""
    directories = ['models', 'analysis_results', 'logs', 'data']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            logger.info(f"已創建 {directory} 目錄")

def parse_arguments():
    """解析命令行參數"""
    parser = argparse.ArgumentParser(description='彩票預測系統')
    parser.add_argument('--mode', choices=['train', 'predict', 'analyze', 'all'], default='all',
                      help='運行模式: train=訓練模型, predict=預測, analyze=分析結果, all=全部運行')
    parser.add_argument('--lottery_type', choices=['powercolor', 'lotto649', 'dailycash', 'all'], default='powercolor',
                      help='彩票類型: powercolor=威力彩, lotto649=大樂透, dailycash=今彩539, all=全部')
    parser.add_argument('--model_type', choices=['random_forest', 'gradient_boosting', 'svm', 'mlp'], 
                      default='mlp', help='機器學習模型類型')
    parser.add_argument('--model_version', default='v2.0', help='模型版本')
    parser.add_argument('--tune', action='store_true', help='是否進行超參數調優')
    parser.add_argument('--method', choices=['ml', 'board_path', 'ensemble'], default='ensemble',
                      help='預測方法: ml=機器學習, board_path=板路分析, ensemble=整合預測')
    parser.add_argument('--period', help='要預測的下一期期數，例如: 114000021')
    parser.add_argument('--no-interactive', action='store_true', help='非互動模式，使用預設值不詢問使用者')
    parser.add_argument('--candidates', type=int, default=5, help='生成的候選結果數量')
    parser.add_argument('--min-confidence', type=float, default=0.5, help='候選結果最低信心分數')
    parser.add_argument('--filter-method', choices=['confidence', 'top_n', 'diversity', 'historical'],
                      help='篩選候選結果的方法')
    
    return parser.parse_args()

def get_lottery_name(lottery_type):
    """根據彩票類型獲取中文名稱"""
    name_map = {
        'powercolor': '威力彩',
        'lotto649': '大樂透',
        'dailycash': '今彩539'
    }
    return name_map.get(lottery_type.lower(), lottery_type)

def show_menu():
    """顯示菜單"""
    print("\n" + "="*50)
    print("彩票預測系統")
    print("="*50)
    print("請選擇功能:")
    print("1. 訓練預測模型")
    print("2. 預測下一期號碼")
    print("3. 分析預測準確度")
    print("4. 查看最近預測記錄")
    print("5. 更新開獎結果")
    print("6. 執行板路分析")
    print("7. 生成詳細報告")
    print("8. 訓練多標籤模型")
    print("9. 整合預測下一期")
    print("10. 多候選結果篩選")  # 新選項
    print("11. 候選結果比較")    # 新選項
    print("12. 分離式預測 (機器學習 + 板路分析)")  # 新選項
    print("13. 執行每日自動化任務")  # 新選項
    print("0. 退出程式")
    print("="*50)

def select_lottery_type():
    """選擇彩票類型"""
    print("\n" + "="*50)
    print("請選擇彩票類型:")
    print("="*50)
    print("1. 威力彩 (默認)")
    print("2. 大樂透")
    print("3. 今彩539")
    
    choice = input("請選擇 (1-3，默認1): ")
    
    lottery_map = {
        '1': 'powercolor',
        '2': 'lotto649',
        '3': 'dailycash'
    }
    
    lottery_type = lottery_map.get(choice, 'powercolor')
    lottery_name = get_lottery_name(lottery_type)
    
    print(f"\n已選擇: {lottery_name}")
    
    return lottery_type

def select_model_type():
    """選擇模型類型"""
    print("\n" + "="*50)
    print("請選擇模型類型:")
    print("="*50)
    print("1. 隨機森林 (默認)")
    print("2. 梯度提升樹")
    print("3. 支持向量機")
    print("4. 神經網絡")
    
    choice = input("請選擇 (1-4，默認4): ")
    
    model_map = {
        '1': 'random_forest',
        '2': 'gradient_boosting',
        '3': 'svm',
        '4': 'mlp'
    }
    
    model_type = model_map.get(choice, 'mlp')
    
    model_name_map = {
        'random_forest': '隨機森林',
        'gradient_boosting': '梯度提升樹',
        'svm': '支持向量機',
        'mlp': '神經網絡'
    }
    
    print(f"\n已選擇: {model_name_map.get(model_type, model_type)}")
    
    return model_type

def train_model(db, fe, board_analyzer, trainer, lottery_type, model_type, tune_hyperparams, model_version):
    """訓練模型"""
    lottery_name = get_lottery_name(lottery_type)
    
    print(f"\n" + "="*50)
    print(f"開始訓練 {lottery_name} 模型...")
    print("="*50)
    
    logger.info(f"開始訓練{lottery_name}模型...")
    
    # 加載數據
    try:
        df = db.load_lottery_data(lottery_type)
        
        if df.empty:
            logger.error(f"無法加載{lottery_name}歷史數據，請確保資料庫中有數據")
            print(f"錯誤: 無法加載{lottery_name}歷史數據，請確保資料庫中有數據")
            return
        
        print(f"已成功載入 {len(df)} 筆 {lottery_name} 歷史資料")
        
        # 創建特徵
        print("\n創建特徵...")
        features_df = fe.create_basic_features(df, lottery_type)
        features_df = fe.create_advanced_features(df, features_df, lottery_type)
        
        # 轉換板路分析器類型
        analyzer_type_map = {
            'powercolor': 'powerball',
            'lotto649': 'lotto',
            'dailycash': '539'
        }
        analyzer_type = analyzer_type_map.get(lottery_type, 'powerball')
        
        # 進行板路分析
        print(f"\n進行{lottery_name}板路分析...")
        # 使用整合版板路分析器建立實例
        from prediction.board_path_engine import BoardPathEngine
        analyzer = BoardPathEngine(db, lottery_type=analyzer_type)
        analyzer.run_full_analysis(df)
        analyzer.save_analysis_results()
        
        # 訓練機器學習模型
        print(f"\n訓練 {model_type} 模型...")
        model_info = trainer.train_models(
            features_df, 
            lottery_type=lottery_type,
            model_type=model_type, 
            tune_hyperparams=tune_hyperparams
        )
        
        # 保存模型
        trainer.save_models(lottery_type=lottery_type, version=model_version)
        
        logger.info(f"{lottery_name}模型訓練完成")
        
        print("\n" + "="*50)
        print(f"{lottery_name}模型訓練完成!")
        print("="*50)
        print(trainer.get_model_summary(lottery_type))
        print("="*50)
        
        return model_info
    except Exception as e:
        logger.error(f"訓練模型時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"\n錯誤: 訓練模型時出錯: {str(e)}")
        return None

def predict_next_draw(db, fe, board_analyzer, predictor, lottery_type, prediction_methods, ensemble, model_version, period=None, generation_options=None, no_interactive=False):
    """預測下一期號碼，支援多候選結果生成
    
    Args:
        db: 資料庫管理器
        fe: 特徵工程器
        board_analyzer: 板路分析器
        predictor: 預測器
        lottery_type: 彩票類型
        prediction_methods: 預測方法列表
        ensemble: 是否進行集成預測
        model_version: 模型版本
        period: 指定期數
        generation_options: 生成選項字典
        no_interactive: 是否為非互動模式
        
    Returns:
        PredictionResult: 預測結果物件
    """
    # 默認生成選項
    if generation_options is None:
        generation_options = {
            'candidates_count': 5,
            'stabilize_results': True,
            'use_enhanced_analysis': True,
            'min_confidence': 0.5
        }
    
    # 如果使用機器學習且在互動模式下
    if 'ml' in prediction_methods and not no_interactive:
        print("\n選擇使用哪種模型類型進行預測:")
        print("1. 隨機森林")
        print("2. 梯度提升樹")
        print("3. 支持向量機")
        print("4. 神經網絡 (默認)")
        
        model_choice = input("請選擇 (1-4，默認4): ")
        model_map = {'1': 'random_forest', '2': 'gradient_boosting', '3': 'svm', '4': 'mlp'}
        selected_model = model_map.get(model_choice, 'mlp')
    else:
        # 非互動模式下使用默認模型（mlp）
        selected_model = 'mlp'
        
    # 載入特定類型的模型
    version_with_type = f"{model_version}_{selected_model}"       
    predictor.load_models(lottery_type, version_with_type)
    
    lottery_name = get_lottery_name(lottery_type)
    
    print(f"\n" + "="*50)
    print(f"開始預測 {lottery_name} 下一期號碼...")
    print("="*50)
    
    logger.info(f"開始預測{lottery_name}下一期號碼...")
    
    # 加載數據
    try:
        df = db.load_lottery_data(lottery_type)
        
        if df.empty:
            logger.error(f"無法加載{lottery_name}歷史數據，請確保資料庫中有數據")
            print(f"錯誤: 無法加載{lottery_name}歷史數據，請確保資料庫中有數據")
            return
        
        print(f"已成功載入 {len(df)} 筆 {lottery_name} 歷史資料")
        
        # 確定預測的期數
        next_period = period
        if not next_period:
            # 如果沒有提供期數，使用最後一期+1
            last_period = int(df['Period'].iloc[-1])
            next_period = str(last_period + 1)
        
        print(f"預測期數: {next_period}")
        logger.info(f"預測期數: {next_period}")
        
        # 準備特徵（如果使用機器學習）
        if 'ml' in prediction_methods:
            print("\n準備特徵...")
            next_features = fe.prepare_next_period_features(df, lottery_type)
        else:
            next_features = None
        
        # 設置預測方法
        method_names = {
            'ml': '機器學習',
            'board_path': '板路分析',
        }
        prediction_method_names = [method_names.get(method, method) for method in prediction_methods]
        ensemble_text = "與" if ensemble else "不"
        print(f"使用預測方法: {', '.join(prediction_method_names)}{ensemble_text}進行集成")
        
        # 顯示生成選項
        print(f"候選數量: {generation_options['candidates_count']}, 最低信心分數: {generation_options['min_confidence']}")
        
        if 'board_path' in prediction_methods:
            # 如果使用板路分析且分析器不是正確的類型，創建新的分析器
            if not hasattr(board_analyzer, 'lottery_type') or getattr(board_analyzer, 'lottery_type', None) != lottery_type:
                try:
                    # 轉換彩票類型
                    analyzer_type_map = {
                        'powercolor': 'powerball',
                        'lotto649': 'lotto', 
                        'dailycash': '539'
                    }
                    analyzer_type = analyzer_type_map.get(lottery_type, 'powerball')
                    
                    # 使用整合版板路分析器
                    from prediction.board_path_engine import BoardPathEngine
                    analyzer = BoardPathEngine(db_manager, lottery_type=analyzer_type)
                    logger.info(f"已創建{lottery_name}板路分析器")
                    print(f"\n已設置{lottery_name}板路分析器")
                except Exception as e:
                    logger.error(f"創建板路分析器時出錯: {str(e)}")
                    if len(prediction_methods) == 1 and prediction_methods[0] == 'board_path':  # 如果只使用板路分析
                        logger.error("無法使用板路分析，將改為機器學習")
                        prediction_methods = ['ml']
                        next_features = fe.prepare_next_period_features(df, lottery_type)
                        print("\n錯誤: 無法使用板路分析，將改為使用機器學習")
                    else:
                        logger.error("無法使用板路分析，將只使用機器學習")
                        prediction_methods = [m for m in prediction_methods if m != 'board_path']
                        print("\n警告: 無法使用板路分析，將只使用機器學習")
            
            # 設置板路分析器
            predictor.set_board_path_analyzer(board_analyzer)
        
        # 預測（現在返回 PredictionResult 物件）
        prediction_result = predictor.predict_with_candidates(
            df, next_features, 
            lottery_type=lottery_type,
            prediction_methods=prediction_methods,
            ensemble=ensemble,
            model_version=model_version,
            generation_options=generation_options
        )
        
        if prediction_result is None:
            logger.error(f"預測失敗，無法獲取{lottery_name}預測結果")
            print(f"\n錯誤: 預測失敗，無法獲取{lottery_name}預測結果")
            return None
        
        # 保存預測結果
        db.save_prediction(prediction_result.to_dict(), next_period, lottery_type)
        
        # 顯示預測結果
        display_prediction_result(prediction_result, lottery_type)
        
        logger.info(f"{lottery_name}下一期號碼預測完成")
        
        return prediction_result
    except Exception as e:
        logger.error(f"預測下一期號碼時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"\n錯誤: 預測下一期號碼時出錯: {str(e)}")
        return None

def display_prediction_result(prediction_result, lottery_type):
    """顯示多候選預測結果
    
    Args:
        prediction_result: PredictionResult物件
        lottery_type: 彩票類型
    """
    lottery_name = get_lottery_name(lottery_type)
    
    print("\n" + "="*50)
    print(f"{lottery_name}預測結果")
    print("="*50)
    
    if not prediction_result.candidates:
        print("沒有候選預測結果")
        return
    
    # 顯示預測方法和版本
    print(f"預測方法: {prediction_result.method}")
    print(f"模型版本: {prediction_result.version}")
    print(f"總候選數: {len(prediction_result.candidates)}")
    print("="*50)
    
    # 顯示前三個候選結果（如果有那麼多）
    max_display = min(3, len(prediction_result.candidates))
    for i in range(max_display):
        candidate = prediction_result.candidates[i]
        
        print(f"\n候選 #{i+1} (信心分數: {candidate['confidence']:.2f})")
        
        if lottery_type == 'powercolor':
            main_numbers = candidate['main_numbers']
            special_number = candidate.get('special_number', 0)
            print(f"第一區: {main_numbers}")
            print(f"第二區: {special_number}")
        elif lottery_type == 'lotto649':
            main_numbers = candidate['main_numbers']
            special_number = candidate.get('special_number', 0)
            print(f"第一區: {main_numbers}")
            print(f"特別號: {special_number}")
        elif lottery_type == 'dailycash':
            main_numbers = candidate['main_numbers']
            print(f"號碼: {main_numbers}")
        
        # 顯示部分解釋（如果有）
        if 'explanation' in candidate and candidate['explanation']:
            print("\n預測理由:")
            explanations = candidate['explanation']
            
            if isinstance(explanations, list):
                # 最多顯示3個理由
                for explanation in explanations[:3]:
                    print(f"- {explanation}")
            elif isinstance(explanations, dict):
                # 如果是字典格式，則顯示主要分類
                for key in list(explanations.keys())[:2]:
                    print(f"- {key}: {explanations[key]}")
            elif isinstance(explanations, str):
                print(f"- {explanations}")
    
    # 如果有更多候選，顯示提示
    if len(prediction_result.candidates) > max_display:
        print(f"\n還有 {len(prediction_result.candidates) - max_display} 個候選結果未顯示")
    
    print("="*50)

def display_prediction(prediction, lottery_type):
    """顯示舊格式預測結果（相容舊函數）"""
    lottery_name = get_lottery_name(lottery_type)
    
    print("\n" + "="*50)
    print(f"{lottery_name}預測結果")
    print("="*50)
    
    if lottery_type == 'powercolor':
        first_area = prediction['第一區']
        second_area = prediction['第二區']
        print(f"第一區: {first_area}")
        print(f"第二區: {second_area}")
    elif lottery_type == 'lotto649':
        first_area = prediction['第一區']
        special = prediction['特別號']
        print(f"第一區: {first_area}")
        print(f"特別號: {special}")
    elif lottery_type == 'dailycash':
        numbers = prediction['號碼']
        print(f"號碼: {numbers}")
    
    # 如果有詳細分析，顯示預測理由
    if '解釋' in prediction:
        print("\n預測理由:")
        explanations = prediction['解釋']
        
        # 顯示部分解釋
        if 'cycle_explanations' in explanations:
            for i, explanation in enumerate(explanations['cycle_explanations'][:2]):
                print(f"- {explanation}")
        
        if 'pair_explanations' in explanations:
            for i, explanation in enumerate(explanations['pair_explanations'][:2]):
                print(f"- {explanation}")
        
        if 'second_area_explanations' in explanations:
            for i, explanation in enumerate(explanations['second_area_explanations'][:1]):
                print(f"- {explanation}")
    
    if '預測方法' in prediction:
        print(f"\n預測方法: {prediction['預測方法']}")
    
    if '模型版本' in prediction:
        print(f"模型版本: {prediction['模型版本']}")
    
    print("="*50)

def analyze_predictions(db, analyzer, lottery_type):
    """分析預測準確度"""
    lottery_name = get_lottery_name(lottery_type)
    
    print(f"\n" + "="*50)
    print(f"開始分析 {lottery_name} 預測結果...")
    print("="*50)
    
    logger.info(f"開始分析{lottery_name}預測結果...")
    
    try:
        # 更新預測結果與實際開獎結果的比對
        updated_count = db.update_prediction_results(lottery_type)
        logger.info(f"已更新 {updated_count} 筆預測記錄的實際結果")
        print(f"已更新 {updated_count} 筆預測記錄的實際結果")
        
        # 加載預測歷史
        prediction_df = analyzer.load_prediction_history(db, lottery_type)
        
        if prediction_df.empty:
            logger.warning(f"沒有找到{lottery_name}預測記錄，無法進行分析")
            print(f"沒有找到{lottery_name}預測記錄，請先進行預測")
            return
        
        print(f"已加載 {len(prediction_df)} 筆 {lottery_name} 預測歷史")
        
        # 根據彩種類型選擇不同的分析方法
        try:
            if lottery_type == 'dailycash':
                # 針對今彩539的自定義分析方法
                analysis_results = analyze_dailycash_predictions(prediction_df)
            else:
                # 使用原有分析方法
                analysis_results = analyzer.analyze_prediction_accuracy(prediction_df)
            
            if not analysis_results:
                logger.warning(f"無法分析{lottery_name}預測準確度")
                print(f"無法分析{lottery_name}預測準確度，可能沒有足夠的數據")
                return
            
            # 分析不同預測方法
            methods_comparison = analyzer.analyze_prediction_methods(prediction_df)
            
            # 生成詳細報告
            analyzer.generate_detailed_report(analysis_results, prediction_df)
            
            # 顯示分析結果
            print("\n" + "="*50)
            print(f"{lottery_name}預測分析結果")
            print("="*50)
            print(analyzer.format_analysis_for_display(analysis_results))
            print("="*50)
            print(f"詳細報告已生成，請查看 {analyzer.output_dir} 目錄")
            print("="*50)
            
            logger.info(f"{lottery_name}預測分析完成")
            return analysis_results
        except Exception as e:
            logger.error(f"分析預測準確度時出錯: {str(e)}")
            print(f"無法分析{lottery_name}預測準確度，可能數據結構不匹配")
            return
            
    except Exception as e:
        logger.error(f"分析預測結果時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"\n錯誤: 分析預測結果時出錯: {str(e)}")
        return None


def analyze_dailycash_predictions(prediction_df):
    """分析今彩539預測準確度"""
    try:
        # 只分析有實際結果的記錄
        completed_df = prediction_df.dropna(subset=['ActualA1'])
        
        if len(completed_df) == 0:
            logger.warning("沒有找到有實際結果的預測記錄")
            return None
        
        # 初始化計數器
        match_counts = {i: 0 for i in range(6)}  # 0-5個匹配
        prize_counts = {
            '頭獎': 0,
            '貳獎': 0,
            '參獎': 0,
            '肆獎': 0,
            '未中獎': 0
        }
        
        # 分析每筆記錄
        for _, row in completed_df.iterrows():
            # 提取預測和實際號碼
            pred_nums = {row['PredA1'], row['PredA2'], row['PredA3'], 
                         row['PredA4'], row['PredA5']}
            actual_nums = {row['ActualA1'], row['ActualA2'], row['ActualA3'], 
                           row['ActualA4'], row['ActualA5']}
            
            # 計算匹配數
            match_count = len(pred_nums.intersection(actual_nums))
            match_counts[match_count] += 1
            
            # 判斷中獎情況 (今彩539規則)
            if match_count == 5:
                prize_counts['頭獎'] += 1
            elif match_count == 4:
                prize_counts['貳獎'] += 1
            elif match_count == 3:
                prize_counts['參獎'] += 1
            elif match_count == 2:
                prize_counts['肆獎'] += 1
            else:
                prize_counts['未中獎'] += 1
        
        # 計算總數
        total = len(completed_df)
        
        # 計算百分比
        match_percentages = {k: v / total * 100 for k, v in match_counts.items()}
        prize_percentages = {k: v / total * 100 for k, v in prize_counts.items()}
        
        # 計算平均匹配數
        avg_match = sum(k * v for k, v in match_counts.items()) / total
        
        # 計算累積匹配率
        cumulative_percentages = {}
        for i in range(6):
            cumulative_percentages[f'匹配{i}個或以上'] = sum(match_counts[j] for j in range(i, 6)) / total * 100
        
        # 整理分析結果
        analysis_results = {
            'total_predictions': total,
            'match_counts': match_counts,
            'match_percentages': match_percentages,
            'prize_counts': prize_counts,
            'prize_percentages': prize_percentages,
            'average_match': avg_match,
            'cumulative_percentages': cumulative_percentages,
            'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return analysis_results
        
    except Exception as e:
        logger.error(f"分析今彩539預測準確度時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        return None

def view_recent_predictions(db, lottery_type, limit=10):
    """查看最近預測記錄，同時顯示機器學習和板路分析結果"""
    lottery_name = get_lottery_name(lottery_type)
    
    print(f"\n" + "="*50)
    print(f"查看 {lottery_name} 最近 {limit} 筆預測記錄...")
    print("="*50)
    
    logger.info(f"查看{lottery_name}最近 {limit} 筆預測記錄...")
    
    try:
        # 加載預測記錄
        df = db.load_prediction_records(lottery_type, limit)
        
        if df.empty:
            logger.warning(f"沒有找到{lottery_name}預測記錄")
            print(f"沒有找到{lottery_name}預測記錄，請先進行預測")
            return
        
        # 顯示預測記錄
        print(f"{lottery_name}最近 {len(df)} 筆預測記錄")

        # 查詢是否使用多候選結果
        candidate_counts = df['CandidateIndex'].nunique()
        has_candidates = candidate_counts > 0
        
        if has_candidates:
            # 分組顯示多候選結果
            prediction_groups = df.groupby('PredictionID')
            
            for pred_id, group in prediction_groups:
                print("="*80)
                first_row = group.iloc[0]
                
                # 處理日期格式
                pred_date = first_row['PredictionDate']
                if isinstance(pred_date, (int, float)) and pred_date > 1000000000000000:
                    # 轉換大整數時間戳
                    seconds = pred_date / 1000000000  # 轉換為秒
                    pred_date = datetime.fromtimestamp(seconds).strftime('%Y-%m-%d %H:%M:%S')
                
                print(f"期數: {first_row['Period']}, 預測日期: {pred_date}")
                print(f"預測方法: {first_row.get('PredictionMethod', '未知')}, 模型版本: {first_row.get('ModelVersion', '未知')}")
                print(f"候選數量: {len(group)}")
                
                # 按候選索引排序
                sorted_group = group.sort_values('CandidateIndex')
                
                # 最多顯示3個候選
                display_count = min(3, len(sorted_group))
                
                for i in range(display_count):
                    row = sorted_group.iloc[i]
                    confidence = row.get('Confidence', 1.0)
                    candidate_idx = row.get('CandidateIndex', i)
                    
                    print(f"\n候選 #{candidate_idx+1} (信心分數: {confidence:.2f})")
                    
                    if lottery_type == 'powercolor':
                        # 顯示整合預測結果
                        pred_numbers = [row['PredA1'], row['PredA2'], row['PredA3'], 
                                       row['PredA4'], row['PredA5'], row['PredA6']]
                        print(f"第一區: {pred_numbers}, 第二區: {row['PredS']}")
                    
                    elif lottery_type == 'lotto649':
                        # 顯示整合預測結果
                        pred_numbers = [row['PredA1'], row['PredA2'], row['PredA3'], 
                                       row['PredA4'], row['PredA5'], row['PredA6']]
                        print(f"第一區: {pred_numbers}, 特別號: {row['PredSpecial']}")
                    
                    elif lottery_type == 'dailycash':
                        # 顯示整合預測結果
                        pred_numbers = [row['PredA1'], row['PredA2'], row['PredA3'], 
                                       row['PredA4'], row['PredA5']]
                        print(f"號碼: {pred_numbers}")
                
                # 顯示實際結果（僅使用第一行即可）
                try:
                    if pd.notna(first_row['ActualA1']):
                        if lottery_type == 'powercolor':
                            actual_numbers = [
                                int(first_row['ActualA1']), int(first_row['ActualA2']), 
                                int(first_row['ActualA3']), int(first_row['ActualA4']), 
                                int(first_row['ActualA5']), int(first_row['ActualA6'])
                            ]
                            print(f"\n實際號碼: 第一區 {actual_numbers}, 第二區 {int(first_row['ActualS'])}")
                            print(f"匹配結果: 第一區匹配 {int(first_row['MatchCount'])} 個, 第二區匹配: {'是' if first_row['SecondMatch'] else '否'}")
                        elif lottery_type == 'lotto649':
                            actual_numbers = [
                                int(first_row['ActualA1']), int(first_row['ActualA2']), 
                                int(first_row['ActualA3']), int(first_row['ActualA4']), 
                                int(first_row['ActualA5']), int(first_row['ActualA6'])
                            ]
                            print(f"\n實際號碼: 第一區 {actual_numbers}, 特別號 {int(first_row['ActualSpecial'])}")
                            print(f"匹配結果: 第一區匹配 {int(first_row['MatchCount'])} 個, 特別號匹配: {'是' if first_row['SpecialMatch'] else '否'}")
                        elif lottery_type == 'dailycash':
                            actual_numbers = [
                                int(first_row['ActualA1']), int(first_row['ActualA2']), 
                                int(first_row['ActualA3']), int(first_row['ActualA4']), 
                                int(first_row['ActualA5'])
                            ]
                            print(f"\n實際號碼: {actual_numbers}")
                            print(f"匹配結果: 匹配 {int(first_row['MatchCount'])} 個")
                    else:
                        print("\n實際結果: 尚未開獎")
                except:
                    print("\n實際結果: 尚未開獎")
        else:
            # 使用舊版顯示方式
            for i, row in df.iterrows():
                print("="*80)
                
                # 處理日期格式
                pred_date = row['PredictionDate']
                if isinstance(pred_date, (int, float)) and pred_date > 1000000000000000:
                    # 轉換大整數時間戳
                    from datetime import datetime
                    seconds = pred_date / 1000000000  # 轉換為秒
                    pred_date = datetime.fromtimestamp(seconds).strftime('%Y-%m-%d %H:%M:%S')
                
                print(f"期數: {row['Period']}, 預測日期: {pred_date}")
                print(f"預測方法: {row.get('PredictionMethod', '未知')}, 模型版本: {row.get('ModelVersion', '未知')}")
                
                if lottery_type == 'powercolor':
                    # 顯示整合預測結果
                    pred_numbers = [row['PredA1'], row['PredA2'], row['PredA3'], 
                                   row['PredA4'], row['PredA5'], row['PredA6']]
                    print(f"整合預測: 第一區 {pred_numbers}, 第二區 {row['PredS']}")
                    
                    # 顯示機器學習預測結果
                    if 'MLResult' in row and row['MLResult'] is not None:
                        try:
                            ml_result = row['MLResult']
                            if isinstance(ml_result, str):
                                import json
                                ml_result = json.loads(ml_result)
                                
                            if '第一區' in ml_result and '第二區' in ml_result:
                                print(f"機器學習預測: 第一區 {ml_result['第一區']}, 第二區 {ml_result['第二區']}")
                        except:
                            print("機器學習預測: 無法解析")
                    
                    # 顯示板路分析預測結果
                    if 'BoardPathResult' in row and row['BoardPathResult'] is not None:
                        try:
                            bp_result = row['BoardPathResult']
                            if isinstance(bp_result, str):
                                import json
                                bp_result = json.loads(bp_result)
                                
                            if '第一區' in bp_result and '第二區' in bp_result:
                                print(f"板路分析預測: 第一區 {bp_result['第一區']}, 第二區 {bp_result['第二區']}")
                        except:
                            print("板路分析預測: 無法解析")
                    
                    # 檢查是否有實際結果
                    try:
                        if pd.notna(row['ActualA1']):
                            actual_numbers = [
                                int(row['ActualA1']), int(row['ActualA2']), 
                                int(row['ActualA3']), int(row['ActualA4']), 
                                int(row['ActualA5']), int(row['ActualA6'])
                            ]
                            print(f"實際號碼: 第一區 {actual_numbers}, 第二區 {int(row['ActualS'])}")
                            print(f"匹配結果: 第一區匹配 {int(row['MatchCount'])} 個, 第二區匹配: {'是' if row['SecondMatch'] else '否'}")
                        else:
                            print("實際結果: 尚未開獎")
                    except:
                        print("實際結果: 尚未開獎")
                
                elif lottery_type == 'lotto649':
                    # 顯示整合預測結果
                    pred_numbers = [row['PredA1'], row['PredA2'], row['PredA3'], 
                                   row['PredA4'], row['PredA5'], row['PredA6']]
                    print(f"整合預測: 第一區 {pred_numbers}, 特別號 {row['PredSpecial']}")
                    
                    # 顯示機器學習預測結果
                    if 'MLResult' in row and row['MLResult'] is not None:
                        try:
                            ml_result = row['MLResult']
                            if isinstance(ml_result, str):
                                import json
                                ml_result = json.loads(ml_result)
                                
                            if '第一區' in ml_result and '特別號' in ml_result:
                                print(f"機器學習預測: 第一區 {ml_result['第一區']}, 特別號 {ml_result['特別號']}")
                        except:
                            print("機器學習預測: 無法解析")
                    
                    # 顯示板路分析預測結果
                    if 'BoardPathResult' in row and row['BoardPathResult'] is not None:
                        try:
                            bp_result = row['BoardPathResult']
                            if isinstance(bp_result, str):
                                import json
                                bp_result = json.loads(bp_result)
                                
                            if '第一區' in bp_result and '特別號' in bp_result:
                                print(f"板路分析預測: 第一區 {bp_result['第一區']}, 特別號 {bp_result['特別號']}")
                        except:
                            print("板路分析預測: 無法解析")
                    
                    # 檢查是否有實際結果
                    try:
                        if pd.notna(row['ActualA1']):
                            actual_numbers = [
                                int(row['ActualA1']), int(row['ActualA2']), 
                                int(row['ActualA3']), int(row['ActualA4']), 
                                int(row['ActualA5']), int(row['ActualA6'])
                            ]
                            print(f"實際號碼: 第一區 {actual_numbers}, 特別號 {int(row['ActualSpecial'])}")
                            print(f"匹配結果: 第一區匹配 {int(row['MatchCount'])} 個, 特別號匹配: {'是' if row['SpecialMatch'] else '否'}")
                        else:
                            print("實際結果: 尚未開獎")
                    except:
                        print("實際結果: 尚未開獎")
                
                elif lottery_type == 'dailycash':
                    # 顯示整合預測結果
                    pred_numbers = [row['PredA1'], row['PredA2'], row['PredA3'], 
                                   row['PredA4'], row['PredA5']]
                    print(f"整合預測: {pred_numbers}")
                    
                    # 顯示機器學習預測結果
                    if 'MLResult' in row and row['MLResult'] is not None:
                        try:
                            ml_result = row['MLResult']
                            if isinstance(ml_result, str):
                                import json
                                ml_result = json.loads(ml_result)
                                
                            if '號碼' in ml_result:
                                print(f"機器學習預測: {ml_result['號碼']}")
                        except:
                            print("機器學習預測: 無法解析")
                    
                    # 顯示板路分析預測結果
                    if 'BoardPathResult' in row and row['BoardPathResult'] is not None:
                        try:
                            bp_result = row['BoardPathResult']
                            if isinstance(bp_result, str):
                                import json
                                bp_result = json.loads(bp_result)
                                
                            # 處理不同格式的結果
                            if '號碼' in bp_result:
                                print(f"板路分析預測: {bp_result['號碼']}")
                            elif '主區' in bp_result:
                                print(f"板路分析預測: {bp_result['主區']}")
                        except:
                            print("板路分析預測: 無法解析")
                    
                    # 檢查是否有實際結果
                    try:
                        if pd.notna(row['ActualA1']):
                            actual_numbers = [
                                int(row['ActualA1']), int(row['ActualA2']), 
                                int(row['ActualA3']), int(row['ActualA4']), 
                                int(row['ActualA5'])
                            ]
                            print(f"實際號碼: {actual_numbers}")
                            print(f"匹配結果: 匹配 {int(row['MatchCount'])} 個")
                        else:
                            print("實際結果: 尚未開獎")
                    except:
                        print("實際結果: 尚未開獎")
        
        logger.info(f"已顯示{lottery_name}最近 {len(df)} 筆預測記錄")
        return df
    except Exception as e:
        logger.error(f"查看最近預測記錄時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"\n錯誤: 查看最近預測記錄時出錯: {str(e)}")
        return None

def update_lottery_results():
    """更新開獎結果"""
    print("\n" + "="*50)
    print("開始更新彩券開獎結果...")
    print("="*50)
    
    logger.info("開始更新彩券開獎結果...")
    
    try:
        # 導入更新模組
        from lottery_daily_updater import download_latest_data
        
        # 下載最近的彩券資料
        print("正在下載最新的彩券資料...")
        results = download_latest_data()
        
        print("\n" + "="*50)
        print("彩券開獎結果更新完成")
        print("="*50)
        print(f"威力彩: 新增 {results['powercolor']} 筆")
        print(f"大樂透: 新增 {results['lotto649']} 筆")
        print(f"今彩539: 新增 {results['daily_cash']} 筆")
        print("="*50)
        
        logger.info("彩券開獎結果更新完成")
        return results
    except Exception as e:
        logger.error(f"更新開獎結果時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"\n錯誤: 更新開獎結果時出錯: {str(e)}")
        return None

def run_board_path_analysis(db, board_analyzer):
    """執行板路分析"""
    print("\n" + "="*50)
    print("執行板路分析...")
    print("="*50)
    
    # 選擇彩票類型
    print("\n" + "="*50)
    print("請選擇彩票類型:")
    print("="*50)
    print("1. 威力彩 (默認)")
    print("2. 大樂透")
    print("3. 今彩539")
    
    choice = input("請選擇 (1-3，默認1): ")
    
    lottery_map = {
        '1': 'powercolor',
        '2': 'lotto649',
        '3': 'dailycash'
    }
    
    lottery_type = lottery_map.get(choice, 'powercolor')
    lottery_name = get_lottery_name(lottery_type)
    
    print(f"\n已選擇: {lottery_name}")
    
    # 轉換為分析器使用的類型
    analyzer_type_map = {
        'powercolor': 'powerball',
        'lotto649': 'lotto',
        'dailycash': '539'
    }
    analyzer_type = analyzer_type_map.get(lottery_type, 'powerball')
    
    logger.info(f"開始執行{lottery_name}板路分析...")
    
    try:
        # 使用整合版板路分析器
        try:
            from prediction.board_path_engine import BoardPathEngine
            # 這裡將 db 傳遞給 BoardPathEngine 而不是 db_manager
            analyzer = BoardPathEngine(db, lottery_type=analyzer_type)
            logger.info(f"已創建{lottery_name}板路分析器")
        except Exception as e:
            logger.error(f"創建板路分析器時出錯: {str(e)}")
            print(f"錯誤: 創建板路分析器時出錯: {str(e)}")
            return
        
        # 加載數據
        df = db.load_lottery_data(lottery_type)
        
        if df.empty:
            logger.error(f"無法加載{lottery_name}歷史數據，請確保資料庫中有數據")
            print(f"錯誤: 無法加載{lottery_name}歷史數據，請確保資料庫中有數據")
            return
        
        print(f"已成功載入 {len(df)} 筆{lottery_name}歷史資料")
        
        # 詢問是否生成多候選結果
        use_candidates = input("\n是否生成多組候選結果? (y/n，默認y): ").lower() != 'n'
        
        if use_candidates:
            candidates_count = int(input("請輸入要生成的候選數量 (1-10，默認5): ") or "5")
            candidates_count = max(1, min(10, candidates_count))
            
            min_confidence = float(input("請輸入最低信心分數 (0.1-1.0，默認0.5): ") or "0.5")
            min_confidence = max(0.1, min(1.0, min_confidence))
            
            # 執行板路分析
            print("正在進行板路分析...")
            analyzer.run_full_analysis(df)
            
            # 生成多候選結果
            generation_options = {
                'candidates_count': candidates_count,
                'stabilize_results': True,
                'use_enhanced_analysis': True,
                'min_confidence': min_confidence
            }
            
            # ↓↓↓ 此處修改，使用 predict() 方法而不是 predict_next_numbers() ↓↓↓
            prediction_result = analyzer.predict(df, generation_options)
            
            # 顯示多候選結果
            print("\n" + "="*50)
            print(f"{lottery_name}板路分析預測結果")
            print("="*50)
            
            # 使用新的顯示函數
            display_prediction_result(prediction_result, lottery_type)
            
        else:
            # 執行板路分析
            print("正在進行板路分析...")
            results = analyzer.run_full_analysis(df)
            
            # 儲存分析結果
            analyzer.save_analysis_results()
            print("板路分析結果已保存")
            
            # 預測下一期（舊方法）
            print("\n正在使用板路分析預測下一期...")
            prediction = analyzer.predict_next_numbers(df)
            
            # 顯示預測結果
            print("\n" + "="*50)
            print(f"{lottery_name}板路分析預測結果")
            print("="*50)
            
            # 使用舊的顯示函數
            display_prediction(prediction, lottery_type)
        
        logger.info(f"{lottery_name}板路分析完成")
        return prediction_result if use_candidates else prediction
    except Exception as e:
        logger.error(f"執行板路分析時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"\n錯誤: 執行板路分析時出錯: {str(e)}")
        return None

def generate_detailed_report(db, analyzer, lottery_type):
    """生成詳細報告"""
    lottery_name = get_lottery_name(lottery_type)
    
    print(f"\n" + "="*50)
    print(f"開始生成 {lottery_name} 詳細報告...")
    print("="*50)
    
    logger.info(f"開始生成{lottery_name}詳細報告...")
    
    try:
        # 更新預測結果與實際開獎結果的比對
        updated_count = db.update_prediction_results(lottery_type)
        logger.info(f"已更新 {updated_count} 筆預測記錄的實際結果")
        print(f"已更新 {updated_count} 筆預測記錄的實際結果")
        
        # 加載預測歷史
        prediction_df = analyzer.load_prediction_history(db, lottery_type)
        
        if prediction_df.empty:
            logger.warning(f"沒有找到{lottery_name}預測記錄，無法生成報告")
            print(f"沒有找到{lottery_name}預測記錄，請先進行預測")
            return
        
        print(f"已加載 {len(prediction_df)} 筆 {lottery_name} 預測歷史")
        
        # 分析預測準確度
        analysis_results = analyzer.analyze_prediction_accuracy(prediction_df)
        
        if not analysis_results:
            logger.warning(f"無法分析{lottery_name}預測準確度")
            print(f"無法分析{lottery_name}預測準確度，可能沒有足夠的數據")
            return
        
        # 分析不同預測方法
        methods_comparison = analyzer.analyze_prediction_methods(prediction_df)
        
        # 生成詳細報告
        report_file = f"{lottery_type}_detailed_report_{datetime.now().strftime('%Y%m%d')}.txt"
        success = analyzer.generate_detailed_report(analysis_results, prediction_df, report_file)
        
        if success:
            logger.info(f"詳細報告已生成: {os.path.join(analyzer.output_dir, report_file)}")
            print(f"\n詳細報告已生成，請查看 {os.path.join(analyzer.output_dir, report_file)}")
        else:
            logger.error("生成詳細報告失敗")
            print("\n生成詳細報告失敗，請查看日誌了解詳情")
        
        print("\n" + "="*50)
        print(f"{lottery_name}詳細報告生成完成")
        print("="*50)
        
        return report_file
    except Exception as e:
        logger.error(f"生成詳細報告時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"\n錯誤: 生成詳細報告時出錯: {str(e)}")
        return None

def filter_prediction_candidates(db, lottery_type):
    """多候選結果篩選功能"""
    lottery_name = get_lottery_name(lottery_type)
    
    print(f"\n" + "="*50)
    print(f"{lottery_name}多候選結果篩選")
    print("="*50)
    
    try:
        # 先查詢最近的預測記錄
        df = db.load_prediction_records(lottery_type, limit=20)
        
        if df.empty:
            print(f"沒有找到 {lottery_name} 預測記錄")
            return
        
        # 篩選出帶有候選索引的記錄
        has_candidates = df['CandidateIndex'].notna().any()
        
        if not has_candidates:
            print(f"沒有找到 {lottery_name} 多候選預測記錄")
            return
        
        # 顯示與候選記錄相關的期數
        pred_ids = df.loc[df['CandidateIndex'].notna(), 'PredictionID'].unique()
        
        if len(pred_ids) == 0:
            print("沒有找到有效的多候選預測記錄")
            return
        
        print("找到以下多候選預測記錄:")
        for i, pred_id in enumerate(pred_ids):
            pred_info = df[df['PredictionID'] == pred_id].iloc[0]
            pred_date = pred_info['PredictionDate']
            period = pred_info['Period']
            candidates_count = len(df[df['PredictionID'] == pred_id])
            
            print(f"{i+1}. 期數: {period}, 日期: {pred_date}, 候選數: {candidates_count}")
        
        # 選擇要篩選的預測
        selected_input = input("\n請選擇要篩選的預測 (數字): ") or "1"  # 如果輸入為空，使用默認值1
        selected_idx = int(selected_input) - 1
        
        if selected_idx < 0 or selected_idx >= len(pred_ids):
            print("選擇無效")
            return
        
        selected_pred_id = pred_ids[selected_idx]
        candidates_df = df[df['PredictionID'] == selected_pred_id].sort_values('CandidateIndex')
        
        print(f"\n已選擇預測ID: {selected_pred_id}")
        print(f"共有 {len(candidates_df)} 個候選結果")
        
        # 創建 PredictionResult 物件
        from prediction.prediction_result import PredictionResult
        pred_result = PredictionResult(
            lottery_type=lottery_type,
            method=candidates_df.iloc[0].get('PredictionMethod', 'unknown'),
            version=candidates_df.iloc[0].get('ModelVersion', 'unknown')
        )
        
        # 添加候選結果
        for _, row in candidates_df.iterrows():
            if lottery_type == 'powercolor':
                main_numbers = [row['PredA1'], row['PredA2'], row['PredA3'], 
                               row['PredA4'], row['PredA5'], row['PredA6']]
                special_number = row['PredS']
            elif lottery_type == 'lotto649':
                main_numbers = [row['PredA1'], row['PredA2'], row['PredA3'], 
                               row['PredA4'], row['PredA5'], row['PredA6']]
                special_number = row['PredSpecial']
            else:  # dailycash
                main_numbers = [row['PredA1'], row['PredA2'], row['PredA3'], 
                               row['PredA4'], row['PredA5']]
                special_number = None
                
            # 嘗試解析預測詳情
            explanation = None
            if 'PredictionDetails' in row and row['PredictionDetails'] is not None:
                try:
                    if isinstance(row['PredictionDetails'], str):
                        explanation = json.loads(row['PredictionDetails'])
                    else:
                        explanation = row['PredictionDetails']
                except:
                    pass
            
            pred_result.add_candidate(
                main_numbers=main_numbers,
                special_number=special_number,
                confidence=row.get('Confidence', 1.0),
                explanation=explanation
            )
        
        # 選擇篩選方法
        print("\n請選擇篩選方法:")
        print("1. 信心分數閾值")
        print("2. 前N個候選")
        print("3. 多樣性篩選")
        print("4. 歷史相似度篩選")
        
        filter_choice = input("\n請選擇 (1-4): ")
        
        # 創建結果縮小器
        from prediction.result_narrower import ResultNarrower
        narrower = ResultNarrower(db_manager=db)
        
        # 根據選擇進行篩選
        if filter_choice == '1':
            threshold_input = input("請輸入信心分數閾值 (0.1-1.0): ") or "0.5"  # 如果輸入為空，使用默認值0.5
            threshold = float(threshold_input)
            filtered_result = narrower.narrow_by_confidence(pred_result, threshold)
            filter_desc = f"信心分數 >= {threshold}"
        elif filter_choice == '2':
            n = int(input("請輸入要保留的候選數量: "))
            filtered_result = narrower.narrow_by_top_n(pred_result, n)
            filter_desc = f"前 {n} 個候選"
        elif filter_choice == '3':
            max_candidates = int(input("請輸入最多保留的候選數量 (默認5): ") or "5")
            min_distance = int(input("請輸入候選之間的最小差異數量 (默認2): ") or "2")
            filtered_result = narrower.narrow_by_diversity(pred_result, max_candidates, min_distance)
            filter_desc = f"多樣性篩選 (最大數量: {max_candidates}, 最小差異: {min_distance})"
        elif filter_choice == '4':
            history_limit = int(input("請輸入考慮的歷史記錄數量 (默認20): ") or "20")
            filtered_result = narrower.narrow_by_historical_similarity(pred_result, history_limit)
            filter_desc = f"歷史相似度篩選 (歷史記錄數: {history_limit})"
        else:
            print("選擇無效")
            return
        
        # 顯示篩選結果
        print("\n" + "="*50)
        print(f"{filter_desc} 篩選結果")
        print("="*50)
        print(f"原始候選數: {len(pred_result.candidates)}")
        print(f"篩選後候選數: {len(filtered_result.candidates)}")
        
        # 顯示篩選後的候選結果
        display_prediction_result(filtered_result, lottery_type)
        
        # 詢問是否儲存
        save_choice = input("\n是否將篩選結果儲存為新的預測? (y/n): ").lower()
        
        if save_choice == 'y':
            period = candidates_df.iloc[0]['Period']
            db.save_prediction(filtered_result.to_dict(), period, lottery_type)
            print(f"篩選結果已儲存為新的預測記錄")
        
        return filtered_result
    except Exception as e:
        logger.error(f"篩選多候選結果時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"\n錯誤: 篩選多候選結果時出錯: {str(e)}")
        return None

def run_separated_prediction(db):
    """執行分離式預測 - 機器學習和板路分析分開"""
    print("\n" + "="*50)
    print("分離式預測 (機器學習 + 板路分析)")
    print("="*50)

    # 選擇彩票類型
    lottery_type = select_lottery_type()
    lottery_name = get_lottery_name(lottery_type)

    try:
        # 導入必要模組
        from prediction.integrated_predictor import IntegratedPredictor
        from display.prediction_display import PredictionDisplay

        # 創建整合預測器
        integrated_predictor = IntegratedPredictor(db)
        integrated_predictor.initialize_board_path_engines()

        # 創建展示器
        display = PredictionDisplay()

        print(f"\n開始 {lottery_name} 分離式預測...")

        # 設置預測參數
        candidates_count = int(input("請輸入候選數量 (1-10，默認5): ") or "5")
        candidates_count = max(1, min(10, candidates_count))

        min_confidence = float(input("請輸入最低信心分數 (0.1-1.0，默認0.5): ") or "0.5")
        min_confidence = max(0.1, min(1.0, min_confidence))

        # 執行分離式預測
        results = integrated_predictor.predict_separated(
            lottery_type=lottery_type,
            candidates_count=candidates_count,
            min_confidence=min_confidence
        )

        if results:
            # 顯示預測結果
            display.display_separated_predictions(results)
            display.display_comparison_summary(results)

            # 詢問是否保存報告
            save_report = input("\n是否保存預測報告到文件? (y/n，默認y): ").lower() != 'n'
            if save_report:
                report_path = display.save_prediction_report(results)
                if report_path:
                    print(f"預測報告已保存: {report_path}")

            logger.info(f"{lottery_name}分離式預測完成")
            return results
        else:
            print(f"\n錯誤: {lottery_name}分離式預測失敗")
            return None

    except Exception as e:
        logger.error(f"執行分離式預測時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"\n錯誤: 執行分離式預測時出錯: {str(e)}")
        return None

def run_daily_automation():
    """執行每日自動化任務"""
    print("\n" + "="*50)
    print("每日自動化任務")
    print("="*50)

    try:
        from daily_automation import DailyAutomation

        automation = DailyAutomation()

        print("選擇執行模式:")
        print("1. 立即執行一次完整任務")
        print("2. 只更新開獎結果")
        print("3. 只分析預測準確度")
        print("4. 只生成新預測")
        print("5. 啟動定時任務調度器")

        choice = input("\n請選擇 (1-5，默認1): ") or "1"

        if choice == '1':
            print("\n開始執行完整的每日自動化任務...")
            automation.run_daily_tasks()
            print("每日自動化任務執行完成")

        elif choice == '2':
            print("\n開始更新開獎結果...")
            automation.update_lottery_results()
            print("開獎結果更新完成")

        elif choice == '3':
            print("\n開始分析預測準確度...")
            automation.analyze_prediction_accuracy()
            print("預測準確度分析完成")

        elif choice == '4':
            print("\n開始生成新預測...")
            automation.generate_daily_predictions()
            print("新預測生成完成")

        elif choice == '5':
            print("\n啟動定時任務調度器...")
            print("調度器將在每日8:00和22:00執行任務")
            print("按 Ctrl+C 停止調度器")

            from daily_automation import run_scheduler
            run_scheduler()
        else:
            print("無效選擇")

    except Exception as e:
        logger.error(f"執行每日自動化任務時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"\n錯誤: 執行每日自動化任務時出錯: {str(e)}")
        return None

def compare_prediction_candidates(db, lottery_type):
    """候選結果比較功能"""
    lottery_name = get_lottery_name(lottery_type)
    
    print(f"\n" + "="*50)
    print(f"{lottery_name}候選結果比較")
    print("="*50)
    
    try:
        # 選擇要比較的期數
        period = input("請輸入要比較的期數 (留空則使用最新期數): ")
        
        if period:
            # 讀取指定期數的預測記錄
            df = db.load_prediction_records(lottery_type, period=period)
        else:
            # 讀取最近20筆記錄
            df = db.load_prediction_records(lottery_type, limit=20)
            
            if not df.empty:
                # 找出最新期數
                periods = df['Period'].unique()
                print("找到以下期數:")
                for i, p in enumerate(periods):
                    print(f"{i+1}. {p}")
                
                selected_input = input("\n請選擇要比較的期數 (數字): ") or "1"  # 如果輸入為空，使用默認值1
                selected_idx = int(selected_input) - 1

                
                if selected_idx < 0 or selected_idx >= len(periods):
                    print("選擇無效")
                    return
                
                period = periods[selected_idx]
                df = df[df['Period'] == period]
        
        if df.empty:
            print(f"沒有找到期數 {period} 的預測記錄")
            return
        
        # 檢查是否有多個預測ID
        pred_ids = df['PredictionID'].unique()
        
        if len(pred_ids) < 2:
            print(f"期數 {period} 只有一個預測ID，無法比較")
            return
        
        print(f"找到 {len(pred_ids)} 個不同的預測ID")
        
        # 顯示這些預測ID的基本信息
        for i, pred_id in enumerate(pred_ids):
            pred_df = df[df['PredictionID'] == pred_id]
            method = pred_df.iloc[0].get('PredictionMethod', '未知')
            date = pred_df.iloc[0]['PredictionDate']
            candidates_count = len(pred_df)
            
            print(f"{i+1}. 預測ID: {pred_id}, 方法: {method}, 日期: {date}, 候選數: {candidates_count}")
        
        # 選擇要比較的兩個預測ID
        print("\n請選擇要比較的兩個預測ID:")
        first_input = input("第一個 (數字): ") or "1"  # 如果輸入為空，使用默認值1
        first_idx = int(first_input) - 1

        second_input = input("第二個 (數字): ") or "2"  # 如果輸入為空，使用默認值2
        second_idx = int(second_input) - 1
        
        if (first_idx < 0 or first_idx >= len(pred_ids) or 
            second_idx < 0 or second_idx >= len(pred_ids) or
            first_idx == second_idx):
            print("選擇無效")
            return
        
        first_id = pred_ids[first_idx]
        second_id = pred_ids[second_idx]
        
        # 獲取這兩個預測的資料
        first_df = df[df['PredictionID'] == first_id].sort_values('CandidateIndex')
        second_df = df[df['PredictionID'] == second_id].sort_values('CandidateIndex')
        
        # 創建兩個 PredictionResult 物件
        from prediction.prediction_result import PredictionResult
        
        first_result = PredictionResult(
            lottery_type=lottery_type,
            method=first_df.iloc[0].get('PredictionMethod', 'unknown'),
            version=first_df.iloc[0].get('ModelVersion', 'unknown')
        )
        
        second_result = PredictionResult(
            lottery_type=lottery_type,
            method=second_df.iloc[0].get('PredictionMethod', 'unknown'),
            version=second_df.iloc[0].get('ModelVersion', 'unknown')
        )
        
        # 添加候選結果
        for _, row in first_df.iterrows():
            if lottery_type == 'powercolor':
                main_numbers = [row['PredA1'], row['PredA2'], row['PredA3'], 
                               row['PredA4'], row['PredA5'], row['PredA6']]
                special_number = row['PredS']
            elif lottery_type == 'lotto649':
                main_numbers = [row['PredA1'], row['PredA2'], row['PredA3'], 
                               row['PredA4'], row['PredA5'], row['PredA6']]
                special_number = row['PredSpecial']
            else:  # dailycash
                main_numbers = [row['PredA1'], row['PredA2'], row['PredA3'], 
                               row['PredA4'], row['PredA5']]
                special_number = None
            
            first_result.add_candidate(
                main_numbers=main_numbers,
                special_number=special_number,
                confidence=row.get('Confidence', 1.0)
            )
        
        for _, row in second_df.iterrows():
            if lottery_type == 'powercolor':
                main_numbers = [row['PredA1'], row['PredA2'], row['PredA3'], 
                               row['PredA4'], row['PredA5'], row['PredA6']]
                special_number = row['PredS']
            elif lottery_type == 'lotto649':
                main_numbers = [row['PredA1'], row['PredA2'], row['PredA3'], 
                               row['PredA4'], row['PredA5'], row['PredA6']]
                special_number = row['PredSpecial']
            else:  # dailycash
                main_numbers = [row['PredA1'], row['PredA2'], row['PredA3'], 
                               row['PredA4'], row['PredA5']]
                special_number = None
            
            second_result.add_candidate(
                main_numbers=main_numbers,
                special_number=special_number,
                confidence=row.get('Confidence', 1.0)
            )
        
        # 選擇比較操作
        print("\n請選擇比較操作:")
        print("1. 兩個預測的差異分析")
        print("2. 合併兩個預測的結果")
        
        op_choice = input("\n請選擇 (1-2): ")
        
        if op_choice == '1':
            # 差異分析
            print("\n" + "="*50)
            print(f"預測差異分析")
            print("="*50)
            
            # 計算主區號碼的重疊率
            overlap_rates = []
            
            for first_candidate in first_result.candidates:
                first_set = set(first_candidate['main_numbers'])
                
                for second_candidate in second_result.candidates:
                    second_set = set(second_candidate['main_numbers'])
                    
                    # 計算重疊數量和比例
                    overlap = len(first_set.intersection(second_set))
                    total = len(first_set.union(second_set))
                    overlap_rate = overlap / (len(first_set) if lottery_type == 'dailycash' else 6) * 100
                    
                    overlap_rates.append(overlap_rate)
            
            avg_overlap = sum(overlap_rates) / len(overlap_rates) if overlap_rates else 0
            
            print(f"兩個預測的平均重疊率: {avg_overlap:.2f}%")
            
            # 顯示兩個預測的候選排名比較
            print("\n候選排名比較:")
            
            max_display = min(3, len(first_result.candidates), len(second_result.candidates))
            
            for i in range(max_display):
                first_candidate = first_result.candidates[i]
                second_candidate = second_result.candidates[i]
                
                overlap = len(set(first_candidate['main_numbers']).intersection(set(second_candidate['main_numbers'])))
                
                print(f"排名 #{i+1}:")
                print(f"  預測1: {first_candidate['main_numbers']}, 信心分數: {first_candidate['confidence']:.2f}")
                print(f"  預測2: {second_candidate['main_numbers']}, 信心分數: {second_candidate['confidence']:.2f}")
                print(f"  重疊數量: {overlap} 個號碼")
                
            # 檢查實際結果（如果有）
            actual_row = df.loc[df['ActualA1'].notna()].iloc[0] if not df.loc[df['ActualA1'].notna()].empty else None
            
            if actual_row is not None:
                if lottery_type == 'powercolor':
                    actual_numbers = [
                        int(actual_row['ActualA1']), int(actual_row['ActualA2']), 
                        int(actual_row['ActualA3']), int(actual_row['ActualA4']), 
                        int(actual_row['ActualA5']), int(actual_row['ActualA6'])
                    ]
                    actual_special = int(actual_row['ActualS'])
                    
                    print("\n實際開獎結果:")
                    print(f"第一區: {actual_numbers}, 第二區: {actual_special}")
                    
                    # 計算兩個預測的命中情況
                    for i, result in enumerate([first_result, second_result]):
                        best_match = 0
                        best_special = False
                        
                        for candidate in result.candidates:
                            match_count = len(set(candidate['main_numbers']).intersection(set(actual_numbers)))
                            special_match = candidate['special_number'] == actual_special
                            
                            if match_count > best_match or (match_count == best_match and special_match and not best_special):
                                best_match = match_count
                                best_special = special_match
                        
                        print(f"預測 {i+1} 最佳命中: 第一區 {best_match} 個, 第二區: {'是' if best_special else '否'}")
                
                elif lottery_type == 'lotto649':
                    actual_numbers = [
                        int(actual_row['ActualA1']), int(actual_row['ActualA2']), 
                        int(actual_row['ActualA3']), int(actual_row['ActualA4']), 
                        int(actual_row['ActualA5']), int(actual_row['ActualA6'])
                    ]
                    actual_special = int(actual_row['ActualSpecial'])
                    
                    print("\n實際開獎結果:")
                    print(f"第一區: {actual_numbers}, 特別號: {actual_special}")
                    
                    # 計算兩個預測的命中情況
                    for i, result in enumerate([first_result, second_result]):
                        best_match = 0
                        best_special = False
                        
                        for candidate in result.candidates:
                            match_count = len(set(candidate['main_numbers']).intersection(set(actual_numbers)))
                            special_match = candidate['special_number'] == actual_special
                            
                            if match_count > best_match or (match_count == best_match and special_match and not best_special):
                                best_match = match_count
                                best_special = special_match
                        
                        print(f"預測 {i+1} 最佳命中: 第一區 {best_match} 個, 特別號: {'是' if best_special else '否'}")
                
                elif lottery_type == 'dailycash':
                    actual_numbers = [
                        int(actual_row['ActualA1']), int(actual_row['ActualA2']), 
                        int(actual_row['ActualA3']), int(actual_row['ActualA4']), 
                        int(actual_row['ActualA5'])
                    ]
                    
                    print("\n實際開獎結果:")
                    print(f"號碼: {actual_numbers}")
                    
                    # 計算兩個預測的命中情況
                    for i, result in enumerate([first_result, second_result]):
                        best_match = 0
                        
                        for candidate in result.candidates:
                            match_count = len(set(candidate['main_numbers']).intersection(set(actual_numbers)))
                            
                            if match_count > best_match:
                                best_match = match_count
                        
                        print(f"預測 {i+1} 最佳命中: {best_match} 個")
            
        elif op_choice == '2':
            # 合併兩個預測
            # 選擇合併方式
            print("\n請選擇合併方式:")
            print("1. 等權重合併")
            print("2. 自定義權重合併")
            
            merge_choice = input("\n請選擇 (1-2): ")
            
            # 設置權重
            if merge_choice == '2':
                first_weight = float(input(f"請輸入預測1的權重 (0-1): "))
                second_weight = float(input(f"請輸入預測2的權重 (0-1): "))
                
                # 標準化權重
                total = first_weight + second_weight
                first_weight /= total
                second_weight /= total
            else:
                first_weight = 0.5
                second_weight = 0.5
            
            # 創建結果縮小器
            from prediction.result_narrower import ResultNarrower
            narrower = ResultNarrower(db_manager=db)
            
            # 合併結果
            merged_result = first_result.merge(second_result, second_weight)
            
            # 顯示合併結果
            print("\n" + "="*50)
            print(f"合併結果 (權重比例 {first_weight:.2f}:{second_weight:.2f})")
            print("="*50)
            
            display_prediction_result(merged_result, lottery_type)
            
            # 詢問是否儲存
            save_choice = input("\n是否將合併結果儲存為新的預測? (y/n): ").lower()
            
            if save_choice == 'y':
                db.save_prediction(merged_result.to_dict(), period, lottery_type)
                print(f"合併結果已儲存為新的預測記錄")
            
            return merged_result
        
        else:
            print("選擇無效")
            return
        
    except Exception as e:
        logger.error(f"比較候選結果時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"\n錯誤: 比較候選結果時出錯: {str(e)}")
        return None

def interactive_mode(no_interactive=False):
    """互動模式主循環"""
    print("\n" + "="*50)
    print("歡迎使用彩票預測系統!")
    print("="*50)
    
    try:
        # 導入功能模組
        from data.db_manager import DBManager
        from data.feature_engineering import FeatureEngineer
        from prediction.board_path_analyzer import BoardPathAnalyzer
        from model.model_trainer import ModelTrainer
        from prediction.lottery_predictor import LotteryPredictor
        from analysis.result_analyzer import PredictionAnalyzer
        from config.config_manager import ConfigManager
        
        # 創建服務實例
        config_manager = ConfigManager()
        db = DBManager(config_manager=config_manager)
        fe = FeatureEngineer()
        board_analyzer = BoardPathAnalyzer('powerball')  # 默認使用威力彩
        trainer = ModelTrainer(model_dir='models')
        predictor = LotteryPredictor(model_dir='models')
        analyzer = PredictionAnalyzer()
        
        # 檢查是否有已存在的板路分析結果
        if os.path.exists("powerball_board_path_analysis.json"):
            board_analyzer.load_analysis_results()
            logger.info("已載入現有的板路分析結果")
        
        if no_interactive:
            # 非互動模式：執行默認操作
            lottery_type = 'dailycash'  # 可設定默認值
            methods = ['ml', 'board_path']
            ensemble = True
            model_version = 'v2.0'
            
            # 生成選項
            generation_options = {
                'candidates_count': 5,
                'stabilize_results': True,
                'use_enhanced_analysis': True,
                'min_confidence': 0.5
            }
            
            # 執行預設操作
            df = db.load_lottery_data(lottery_type)
            if not df.empty:
                next_features = fe.prepare_next_period_features(df, lottery_type)
                predict_next_draw(db, fe, board_analyzer, predictor, lottery_type, methods, ensemble, model_version, generation_options=generation_options, no_interactive=True)
            return

        # 有互動式選單的原始功能保持不變
        while True:
            show_menu()
            choice = input("\n請輸入選項 (0-11): ")

            if choice == '0':
                print("\n感謝使用彩票預測系統，再見!")
                break
                
            elif choice == '1':
                # 訓練預測模型
                print("\n訓練預測模型")
                lottery_type = select_lottery_type()
                model_type = select_model_type()
                tune = input("\n是否進行超參數調優? (y/n，默認n): ").lower() == 'y'
                model_version = input("\n請輸入模型版本 (默認v1.0): ") or "v1.0"
                
                train_model(db, fe, board_analyzer, trainer, lottery_type, model_type, tune, model_version)
                
            elif choice == '2':
                # 預測下一期號碼
                print("\n預測下一期號碼")
                lottery_type = select_lottery_type()
                
                # 選擇預測方法
                print("\n" + "="*50)
                print("請選擇預測方法:")
                print("="*50)
                print("1. 機器學習 (ML)")
                print("2. 板路分析")
                print("3. 整合預測 (默認)")
                
                method_choice = input("\n請選擇 (1-3，默認3): ") or "3"
                
                # 設置預測方法
                if method_choice == '1':
                    prediction_methods = ['ml']
                    ensemble = False
                elif method_choice == '2':
                    prediction_methods = ['board_path']
                    ensemble = False
                else:
                    prediction_methods = ['ml', 'board_path']
                    ensemble = True
                
                model_version = input("\n請輸入模型版本 (默認v1.0): ") or "v1.0"
                period = input("\n請輸入要預測的期數 (留空使用最後一期+1): ")
                
                # 設置多候選生成選項
                use_candidates = input("\n是否使用多候選結果生成? (y/n，默認y): ").lower() != 'n'
                
                if use_candidates:
                    candidates_count = int(input("請輸入候選數量 (1-10，默認5): ") or "5")
                    candidates_count = max(1, min(10, candidates_count))
                    
                    min_confidence = float(input("請輸入最低信心分數 (0.1-1.0，默認0.5): ") or "0.5")
                    min_confidence = max(0.1, min(1.0, min_confidence))
                    
                    generation_options = {
                        'candidates_count': candidates_count,
                        'stabilize_results': True,
                        'use_enhanced_analysis': True,
                        'min_confidence': min_confidence
                    }
                else:
                    generation_options = None
                
                # 如果選擇了板路分析，需要創建對應彩種的板路分析器
                if 'board_path' in prediction_methods:
                    try:
                        # 轉換彩票類型為分析器使用的格式
                        analyzer_type_map = {
                            'powercolor': 'powerball',
                            'lotto649': 'lotto',
                            'dailycash': '539'
                        }
                        analyzer_type = analyzer_type_map.get(lottery_type, 'powerball')
                        
                        # 創建分析器實例
                        board_analyzer = BoardPathAnalyzer(analyzer_type)
                        print(f"\n已設置{get_lottery_name(lottery_type)}板路分析器")
                    except Exception as e:
                        print(f"\n警告: 設置板路分析器時出錯: {str(e)}")
                        if method_choice == '2':
                            print("將改為使用機器學習預測")
                            prediction_methods = ['ml']
                            ensemble = False
                        else:
                            print("將只使用機器學習進行預測")
                            prediction_methods = ['ml']
                            ensemble = False
                
                predict_next_draw(db, fe, board_analyzer, predictor, lottery_type, prediction_methods, ensemble, model_version, period, generation_options)
                
            elif choice == '3':
                # 分析預測準確度
                print("\n分析預測準確度")
                lottery_type = select_lottery_type()
                analyze_predictions(db, analyzer, lottery_type)
                
            elif choice == '4':
                # 查看最近預測記錄
                print("\n查看最近預測記錄")
                lottery_type = select_lottery_type()
                limit = input("\n請輸入要顯示的記錄數量 (默認10): ") or "10"
                view_recent_predictions(db, lottery_type, int(limit))
                
            elif choice == '5':
                # 更新開獎結果
                update_lottery_results()
                
            elif choice == '6':
                # 執行板路分析
                run_board_path_analysis(db, board_analyzer)
                
            elif choice == '7':
                # 生成詳細報告
                print("\n生成詳細報告")
                lottery_type = select_lottery_type()
                generate_detailed_report(db, analyzer, lottery_type)
                
            elif choice == '8':
                # 訓練多標籤模型
                print("\n訓練多標籤模型")
                lottery_type = select_lottery_type()
                model_type = select_model_type()
                model_version = input("\n請輸入模型版本 (默認v2.0): ") or "v2.0"
                
                train_multi_label_model(db, fe, lottery_type, model_type, model_version)
                
            elif choice == '9':
                # 整合預測下一期
                print("\n整合預測下一期")
                lottery_type = select_lottery_type()
                
                # 是否使用板路分析
                use_board_path = input("\n是否結合板路分析? (y/n，默認y): ").lower() != 'n'
                
                # 是否進行集成
                ensemble = input("\n是否進行集成預測? (y/n，默認y): ").lower() != 'n'
                
                model_version = input("\n請輸入模型版本 (默認v2.0): ") or "v2.0"
                period = input("\n請輸入要預測的期數 (留空使用最後一期+1): ")
                
                methods = ['ml']
                if use_board_path:
                    methods.append('board_path')
                
                # 設置多候選生成選項
                candidates_count = int(input("請輸入要生成的候選數量 (1-10，默認5): ") or "5")
                candidates_count = max(1, min(10, candidates_count))
                
                generation_options = {
                    'candidates_count': candidates_count,
                    'stabilize_results': True,
                    'use_enhanced_analysis': True,
                    'min_confidence': 0.5
                }
                
                integrated_predict(db, fe, lottery_type, methods, ensemble, model_version, period)
                
            elif choice == '10':
                # 多候選結果篩選
                print("\n多候選結果篩選")
                lottery_type = select_lottery_type()
                filter_prediction_candidates(db, lottery_type)
                
            elif choice == '11':
                # 候選結果比較
                print("\n候選結果比較")
                lottery_type = select_lottery_type()
                compare_prediction_candidates(db, lottery_type)

            elif choice == '12':
                # 分離式預測
                run_separated_prediction(db)

            elif choice == '13':
                # 每日自動化任務
                run_daily_automation()

            else:
                print("\n無效選項，請重新選擇")
            
            input("\n按 Enter 鍵繼續...")
    
    except KeyboardInterrupt:
        print("\n程式被使用者中斷")
    except Exception as e:
        logger.error(f"程式運行時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"\n程式發生錯誤: {str(e)}")
        print("請查看日誌檔案以獲取詳細信息。")

def integrated_predict(db, fe, lottery_type, methods, ensemble, model_version, period=None):
    """整合預測下一期號碼"""
    lottery_name = get_lottery_name(lottery_type)
    
    print(f"\n" + "="*50)
    print(f"開始整合預測 {lottery_name} 下一期號碼...")
    print("="*50)
    
    logger.info(f"開始整合預測{lottery_name}下一期號碼...")
    
    try:
        # 導入整合預測器
        from prediction.lottery_predictor import IntegratedPredictor
        
        # 加載數據
        df = db.load_lottery_data(lottery_type)
        
        if df.empty:
            logger.error(f"無法加載{lottery_name}歷史數據，請確保資料庫中有數據")
            print(f"錯誤: 無法加載{lottery_name}歷史數據，請確保資料庫中有數據")
            return
        
        print(f"已成功載入 {len(df)} 筆 {lottery_name} 歷史資料")
        
        # 確定預測的期數
        next_period = period
        if not next_period:
            # 如果沒有提供期數，使用最後一期+1
            last_period = int(df['Period'].iloc[-1])
            next_period = str(last_period + 1)
        
        print(f"預測期數: {next_period}")
        logger.info(f"預測期數: {next_period}")
        
        # 準備特徵
        print("\n準備特徵...")
        next_features = fe.prepare_next_period_features(df, lottery_type)
        
        # 設定多候選生成選項
        candidates_count = int(input("請輸入要生成的候選數量 (1-10，默認5): ") or "5")
        candidates_count = max(1, min(10, candidates_count))
        
        min_confidence = float(input("請輸入最低信心分數 (0.1-1.0，默認0.5): ") or "0.5")
        min_confidence = max(0.1, min(1.0, min_confidence))
        
        generation_options = {
            'candidates_count': candidates_count,
            'stabilize_results': True,
            'use_enhanced_analysis': True,
            'min_confidence': min_confidence
        }
        
        # 顯示使用的方法
        method_names = {
            'ml': '多標籤機器學習',
            'board_path': '板路分析',
        }
        prediction_method_names = [method_names.get(method, method) for method in methods]
        ensemble_text = "集成" if ensemble else "不集成"
        print(f"使用方法: {', '.join(prediction_method_names)}，{ensemble_text}預測")
        
        # 創建整合預測器
        predictor = IntegratedPredictor(model_dir='models', version=model_version)
        
        # 進行預測
        prediction_result = predictor.predict_with_candidates(
            df, next_features, 
            lottery_type=lottery_type,
            methods=methods,
            ensemble=ensemble,
            period=next_period,
            generation_options=generation_options
        )
        
        if prediction_result is None:
            logger.error(f"預測失敗，無法獲取{lottery_name}預測結果")
            print(f"\n錯誤: 預測失敗，無法獲取{lottery_name}預測結果")
            return None
        
        # 保存預測結果
        db.save_prediction(prediction_result.to_dict(), next_period, lottery_type)
        
        # 顯示預測結果
        display_prediction_result(prediction_result, lottery_type)
        
        logger.info(f"{lottery_name}下一期號碼預測完成")
        
        return prediction_result
    except Exception as e:
        logger.error(f"整合預測下一期號碼時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"\n錯誤: 整合預測下一期號碼時出錯: {str(e)}")
        return None

def train_multi_label_model(db, fe, lottery_type, model_type, model_version):
    """訓練多標籤模型"""
    lottery_name = get_lottery_name(lottery_type)
    
    print(f"\n" + "="*50)
    print(f"開始訓練 {lottery_name} 多標籤模型...")
    print("="*50)
    
    logger.info(f"開始訓練{lottery_name}多標籤模型...")
    
    try:
        # 導入多標籤模型訓練器
        from multi_label_model_trainer import MultiLabelModelTrainer
        
        # 加載數據
        df = db.load_lottery_data(lottery_type)
        
        if df.empty:
            logger.error(f"無法加載{lottery_name}歷史數據，請確保資料庫中有數據")
            print(f"錯誤: 無法加載{lottery_name}歷史數據，請確保資料庫中有數據")
            return
        
        print(f"已成功載入 {len(df)} 筆 {lottery_name} 歷史資料")
        
        # 創建特徵
        print("\n創建特徵...")
        features_df = fe.create_basic_features(df, lottery_type)
        features_df = fe.create_advanced_features(df, features_df, lottery_type)
        
        # 訓練多標籤模型
        print(f"\n訓練 {model_type} 多標籤模型...")
        
        trainer = MultiLabelModelTrainer(model_dir='models')
        model_info = trainer.train_models(features_df, lottery_type=lottery_type, model_type=model_type)
        
        # 保存模型
        trainer.save_models(lottery_type=lottery_type, version=model_version)
        
        logger.info(f"{lottery_name}多標籤模型訓練完成")
        
        print("\n" + "="*50)
        print(f"{lottery_name}多標籤模型訓練完成!")
        print("="*50)
        
        # 顯示模型性能
        if lottery_type == 'powercolor':
            first_area_acc = model_info['performance']['first_area']['accuracy']
            second_area_acc = model_info['performance']['second_area']['accuracy']
            print(f"第一區準確率: {first_area_acc:.4f}")
            print(f"第二區準確率: {second_area_acc:.4f}")
        elif lottery_type == 'lotto649':
            first_area_acc = model_info['performance']['first_area']['accuracy']
            special_acc = model_info['performance']['special']['accuracy']
            print(f"第一區準確率: {first_area_acc:.4f}")
            print(f"特別號準確率: {special_acc:.4f}")
        elif lottery_type == 'dailycash':
            numbers_acc = model_info['performance']['numbers']['accuracy']
            print(f"號碼準確率: {numbers_acc:.4f}")
        
        print(f"總訓練時間: {model_info['training_time']:.1f}秒")
        print("="*50)
        
        return model_info
    except Exception as e:
        logger.error(f"訓練多標籤模型時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"\n錯誤: 訓練多標籤模型時出錯: {str(e)}")
        return None

def main():
    """主函數"""
    # 解析命令行參數
    args = parse_arguments()
    
    try:
        # 確保環境設置正確
        setup_environment()
        
        # 導入功能模組
        from data.db_manager import DBManager
        from data.feature_engineering import FeatureEngineer
        from prediction.board_path_analyzer import BoardPathAnalyzer
        from model.model_trainer import ModelTrainer
        from prediction.lottery_predictor import LotteryPredictor
        from analysis.result_analyzer import PredictionAnalyzer
        from config.config_manager import ConfigManager
        
        # 創建服務實例
        config_manager = ConfigManager()
        db = DBManager(config_manager=config_manager)
        fe = FeatureEngineer()
        board_analyzer = BoardPathAnalyzer('powerball')  # 默認使用威力彩
        trainer = ModelTrainer(model_dir='models')
        predictor = LotteryPredictor(model_dir='models', version=args.model_version)
        analyzer = PredictionAnalyzer()
        
        # 如果沒有提供命令行參數或使用互動模式
        if len(sys.argv) == 1:
            interactive_mode(args.no_interactive)
            return
                    
        # 決定要處理的彩票類型
        lottery_types = ['powercolor', 'lotto649', 'dailycash'] if args.lottery_type == 'all' else [args.lottery_type]
        
        # 生成選項
        generation_options = {
            'candidates_count': args.candidates,
            'stabilize_results': True,
            'use_enhanced_analysis': True,
            'min_confidence': args.min_confidence
        }
        
        for lottery_type in lottery_types:
            lottery_name = get_lottery_name(lottery_type)
            logger.info(f"開始處理 {lottery_name}...")
            
            # 加載歷史數據
            logger.info(f"加載{lottery_name}歷史數據...")
            df = db.load_lottery_data(lottery_type)
            
            if df.empty:
                logger.error(f"無法加載{lottery_name}歷史數據，請確保資料庫中有數據")
                continue
            
            # 根據運行模式執行相應的功能
            if args.mode == 'train' or args.mode == 'all':
                # 訓練模型
                train_model(db, fe, board_analyzer, trainer, lottery_type, args.model_type, args.tune, args.model_version)
            
            if args.mode == 'predict' or args.mode == 'all':
                # 預測下一期號碼
                # 設置預測方法
                if args.method == 'ml':
                    prediction_methods = ['ml']
                    ensemble = False
                elif args.method == 'board_path':
                    prediction_methods = ['board_path']
                    ensemble = False
                    
                    # 如果使用板路分析，需要創建對應的分析器
                    try:
                        # 轉換彩票類型
                        analyzer_type_map = {
                            'powercolor': 'powerball',
                            'lotto649': 'lotto',
                            'dailycash': '539'
                        }
                        analyzer_type = analyzer_type_map.get(lottery_type, 'powerball')
                        
                        # 創建分析器
                        board_analyzer = BoardPathAnalyzer(analyzer_type)
                        logger.info(f"已創建{lottery_name}板路分析器")
                    except Exception as e:
                        logger.error(f"創建板路分析器時出錯: {str(e)}")
                        prediction_methods = ['ml']
                        ensemble = False
                else:  # ensemble
                    prediction_methods = ['ml']
                    ensemble = True
                    
                    # 檢查是否可以添加板路分析
                    try:
                        # 轉換彩票類型
                        analyzer_type_map = {
                            'powercolor': 'powerball',
                            'lotto649': 'lotto',
                            'dailycash': '539'
                        }
                        analyzer_type = analyzer_type_map.get(lottery_type, 'powerball')
                        
                        # 創建分析器
                        board_analyzer = BoardPathAnalyzer(analyzer_type)
                        logger.info(f"已創建{lottery_name}板路分析器")
                        prediction_methods.append('board_path')
                    except Exception as e:
                        logger.error(f"創建板路分析器時出錯: {str(e)}")
                        logger.warning("只使用機器學習進行預測")
                
                predict_next_draw(db, fe, board_analyzer, predictor, lottery_type, prediction_methods, ensemble, 
                                 args.model_version, args.period, generation_options, args.no_interactive)
            
            if args.mode == 'analyze' or args.mode == 'all':
                # 分析預測結果
                analyze_predictions(db, analyzer, lottery_type)
        
        logger.info("彩票預測系統運行完成")
        
    except Exception as e:
        logger.error(f"程式運行時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"程式發生錯誤: {str(e)}")
        print("請查看日誌檔案以獲取詳細信息。")

if __name__ == "__main__":
    main()