"""
彩票預測系統 - 策略優化模組
基於成功分析結果，動態調整預測策略和參數
"""

import os
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging

# 設置日誌
logger = logging.getLogger('strategy_optimizer')

class StrategyOptimizer:
    """預測策略優化器"""
    
    def __init__(self, db_manager):
        """初始化策略優化器
        
        Args:
            db_manager: 資料庫管理器
        """
        self.db_manager = db_manager
        self.optimization_history = []
        
        # 默認策略參數
        self.default_strategies = {
            'powercolor': {
                'ml_weight': 0.6,
                'board_path_weight': 0.4,
                'candidates_count': 5,
                'min_confidence': 0.5,
                'feature_importance_threshold': 0.1,
                'time_window_days': 30
            },
            'lotto649': {
                'ml_weight': 0.7,
                'board_path_weight': 0.3,
                'candidates_count': 5,
                'min_confidence': 0.5,
                'feature_importance_threshold': 0.1,
                'time_window_days': 30
            },
            'dailycash': {
                'ml_weight': 0.5,
                'board_path_weight': 0.5,
                'candidates_count': 5,
                'min_confidence': 0.5,
                'feature_importance_threshold': 0.1,
                'time_window_days': 30
            }
        }
        
        # 當前優化策略
        self.current_strategies = self.default_strategies.copy()
    
    def optimize_strategy(self, lottery_type: str, success_analysis: Dict) -> Dict:
        """基於成功分析優化預測策略
        
        Args:
            lottery_type: 彩票類型
            success_analysis: 成功分析結果
            
        Returns:
            Dict: 優化後的策略參數
        """
        logger.info(f"開始優化 {lottery_type} 預測策略...")
        
        try:
            # 獲取當前策略
            current_strategy = self.current_strategies.get(lottery_type, self.default_strategies[lottery_type]).copy()
            
            # 分析歷史表現
            performance_analysis = self._analyze_historical_performance(lottery_type)
            
            # 基於成功因素調整策略
            optimized_strategy = self._adjust_strategy_based_on_success(
                current_strategy, success_analysis, performance_analysis
            )
            
            # 驗證策略有效性
            if self._validate_strategy(optimized_strategy, lottery_type):
                self.current_strategies[lottery_type] = optimized_strategy
                
                # 記錄優化歷史
                self._record_optimization(lottery_type, current_strategy, optimized_strategy, success_analysis)
                
                logger.info(f"{lottery_type} 策略優化完成")
                return optimized_strategy
            else:
                logger.warning(f"{lottery_type} 策略優化驗證失敗，保持原策略")
                return current_strategy
                
        except Exception as e:
            logger.error(f"優化 {lottery_type} 策略時出錯: {str(e)}")
            return self.current_strategies.get(lottery_type, self.default_strategies[lottery_type])
    
    def _analyze_historical_performance(self, lottery_type: str) -> Dict:
        """分析歷史預測表現
        
        Args:
            lottery_type: 彩票類型
            
        Returns:
            Dict: 歷史表現分析結果
        """
        try:
            # 載入最近的預測記錄
            df = self.db_manager.load_prediction_records(lottery_type, limit=100)
            if df.empty:
                return {'ml_performance': 0.5, 'board_path_performance': 0.5, 'overall_performance': 0.5}
            
            # 篩選有實際結果的記錄
            completed_df = df.dropna(subset=['MatchCount'])
            if completed_df.empty:
                return {'ml_performance': 0.5, 'board_path_performance': 0.5, 'overall_performance': 0.5}
            
            # 分析機器學習表現
            ml_records = completed_df[completed_df['PredictionMethod'].str.contains('ml', na=False)]
            ml_performance = self._calculate_method_performance(ml_records, lottery_type)
            
            # 分析板路分析表現
            bp_records = completed_df[completed_df['PredictionMethod'].str.contains('board_path', na=False)]
            bp_performance = self._calculate_method_performance(bp_records, lottery_type)
            
            # 整體表現
            overall_performance = self._calculate_method_performance(completed_df, lottery_type)
            
            return {
                'ml_performance': ml_performance,
                'board_path_performance': bp_performance,
                'overall_performance': overall_performance,
                'total_predictions': len(completed_df),
                'ml_predictions': len(ml_records),
                'bp_predictions': len(bp_records)
            }
            
        except Exception as e:
            logger.error(f"分析歷史表現時出錯: {str(e)}")
            return {'ml_performance': 0.5, 'board_path_performance': 0.5, 'overall_performance': 0.5}
    
    def _calculate_method_performance(self, records_df: pd.DataFrame, lottery_type: str) -> float:
        """計算預測方法的表現分數
        
        Args:
            records_df: 預測記錄DataFrame
            lottery_type: 彩票類型
            
        Returns:
            float: 表現分數 (0-1)
        """
        if records_df.empty:
            return 0.5
        
        try:
            # 計算平均匹配數
            avg_match = records_df['MatchCount'].mean()
            
            # 根據彩票類型設定最大匹配數
            max_match = {'powercolor': 6, 'lotto649': 6, 'dailycash': 5}.get(lottery_type, 6)
            
            # 轉換為0-1分數
            performance_score = min(1.0, avg_match / max_match + 0.2)
            
            # 考慮中獎率
            if lottery_type in ['powercolor', 'lotto649']:
                # 威力彩和大樂透考慮特別號匹配
                special_match_col = 'SecondMatch' if lottery_type == 'powercolor' else 'SpecialMatch'
                if special_match_col in records_df.columns:
                    special_match_rate = records_df[special_match_col].mean()
                    performance_score += special_match_rate * 0.2
            
            return min(1.0, performance_score)
            
        except Exception as e:
            logger.error(f"計算方法表現時出錯: {str(e)}")
            return 0.5
    
    def _adjust_strategy_based_on_success(self, current_strategy: Dict, success_analysis: Dict, 
                                        performance_analysis: Dict) -> Dict:
        """基於成功分析調整策略
        
        Args:
            current_strategy: 當前策略
            success_analysis: 成功分析結果
            performance_analysis: 歷史表現分析
            
        Returns:
            Dict: 調整後的策略
        """
        optimized_strategy = current_strategy.copy()
        
        try:
            # 根據方法表現調整權重
            ml_perf = performance_analysis.get('ml_performance', 0.5)
            bp_perf = performance_analysis.get('board_path_performance', 0.5)
            
            # 重新計算權重
            total_perf = ml_perf + bp_perf
            if total_perf > 0:
                optimized_strategy['ml_weight'] = ml_perf / total_perf
                optimized_strategy['board_path_weight'] = bp_perf / total_perf
            
            # 根據成功因素調整候選數量
            ml_factors = success_analysis.get('ml_success_factors', [])
            bp_factors = success_analysis.get('board_path_success_factors', [])
            
            if len(ml_factors) > len(bp_factors):
                # 機器學習表現更好，增加候選數量
                optimized_strategy['candidates_count'] = min(8, current_strategy['candidates_count'] + 1)
            elif len(bp_factors) > len(ml_factors):
                # 板路分析表現更好，保持或略減候選數量
                optimized_strategy['candidates_count'] = max(3, current_strategy['candidates_count'])
            
            # 根據整體表現調整信心分數閾值
            overall_perf = performance_analysis.get('overall_performance', 0.5)
            if overall_perf > 0.7:
                # 表現好，可以降低信心分數閾值
                optimized_strategy['min_confidence'] = max(0.3, current_strategy['min_confidence'] - 0.1)
            elif overall_perf < 0.4:
                # 表現差，提高信心分數閾值
                optimized_strategy['min_confidence'] = min(0.8, current_strategy['min_confidence'] + 0.1)
            
            # 根據成功模式調整時間窗口
            common_patterns = success_analysis.get('common_success_patterns', [])
            if len(common_patterns) > 3:
                # 有明顯成功模式，縮短時間窗口
                optimized_strategy['time_window_days'] = max(14, current_strategy['time_window_days'] - 7)
            elif len(common_patterns) < 2:
                # 成功模式不明顯，延長時間窗口
                optimized_strategy['time_window_days'] = min(60, current_strategy['time_window_days'] + 7)
            
            return optimized_strategy
            
        except Exception as e:
            logger.error(f"調整策略時出錯: {str(e)}")
            return current_strategy
    
    def _validate_strategy(self, strategy: Dict, lottery_type: str) -> bool:
        """驗證策略參數的有效性
        
        Args:
            strategy: 策略參數
            lottery_type: 彩票類型
            
        Returns:
            bool: 是否有效
        """
        try:
            # 檢查權重總和
            weight_sum = strategy.get('ml_weight', 0) + strategy.get('board_path_weight', 0)
            if abs(weight_sum - 1.0) > 0.1:
                return False
            
            # 檢查候選數量
            candidates_count = strategy.get('candidates_count', 5)
            if not (1 <= candidates_count <= 10):
                return False
            
            # 檢查信心分數
            min_confidence = strategy.get('min_confidence', 0.5)
            if not (0.1 <= min_confidence <= 1.0):
                return False
            
            # 檢查時間窗口
            time_window = strategy.get('time_window_days', 30)
            if not (7 <= time_window <= 90):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"驗證策略時出錯: {str(e)}")
            return False
    
    def _record_optimization(self, lottery_type: str, old_strategy: Dict, 
                           new_strategy: Dict, success_analysis: Dict):
        """記錄策略優化歷史
        
        Args:
            lottery_type: 彩票類型
            old_strategy: 舊策略
            new_strategy: 新策略
            success_analysis: 成功分析結果
        """
        try:
            optimization_record = {
                'timestamp': datetime.now().isoformat(),
                'lottery_type': lottery_type,
                'old_strategy': old_strategy,
                'new_strategy': new_strategy,
                'success_analysis_summary': {
                    'ml_factors_count': len(success_analysis.get('ml_success_factors', [])),
                    'bp_factors_count': len(success_analysis.get('board_path_success_factors', [])),
                    'common_patterns_count': len(success_analysis.get('common_success_patterns', []))
                }
            }
            
            self.optimization_history.append(optimization_record)
            
            # 保存到文件
            history_file = f"strategy_optimization_history_{lottery_type}.json"
            history_path = os.path.join('analysis_results', history_file)
            
            # 確保目錄存在
            os.makedirs(os.path.dirname(history_path), exist_ok=True)
            
            with open(history_path, 'w', encoding='utf-8') as f:
                json.dump(self.optimization_history, f, ensure_ascii=False, indent=2)
            
            logger.info(f"策略優化記錄已保存: {history_path}")
            
        except Exception as e:
            logger.error(f"記錄策略優化時出錯: {str(e)}")
    
    def get_current_strategy(self, lottery_type: str) -> Dict:
        """獲取當前策略
        
        Args:
            lottery_type: 彩票類型
            
        Returns:
            Dict: 當前策略參數
        """
        return self.current_strategies.get(lottery_type, self.default_strategies[lottery_type]).copy()
    
    def reset_strategy(self, lottery_type: str):
        """重置策略為默認值
        
        Args:
            lottery_type: 彩票類型
        """
        self.current_strategies[lottery_type] = self.default_strategies[lottery_type].copy()
        logger.info(f"{lottery_type} 策略已重置為默認值")
    
    def save_strategies(self, filepath: str = None):
        """保存當前策略到文件
        
        Args:
            filepath: 保存路徑，如果為None則使用默認路徑
        """
        try:
            if filepath is None:
                filepath = os.path.join('analysis_results', 'current_strategies.json')
            
            # 確保目錄存在
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.current_strategies, f, ensure_ascii=False, indent=2)
            
            logger.info(f"當前策略已保存: {filepath}")
            
        except Exception as e:
            logger.error(f"保存策略時出錯: {str(e)}")
    
    def load_strategies(self, filepath: str = None):
        """從文件載入策略
        
        Args:
            filepath: 載入路徑，如果為None則使用默認路徑
        """
        try:
            if filepath is None:
                filepath = os.path.join('analysis_results', 'current_strategies.json')
            
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    loaded_strategies = json.load(f)
                
                # 驗證載入的策略
                for lottery_type, strategy in loaded_strategies.items():
                    if self._validate_strategy(strategy, lottery_type):
                        self.current_strategies[lottery_type] = strategy
                    else:
                        logger.warning(f"載入的 {lottery_type} 策略無效，使用默認策略")
                
                logger.info(f"策略已從文件載入: {filepath}")
            else:
                logger.warning(f"策略文件不存在: {filepath}")
                
        except Exception as e:
            logger.error(f"載入策略時出錯: {str(e)}")

def optimize_prediction_strategy(db_manager, lottery_type: str, success_analysis: Dict) -> Dict:
    """優化預測策略的便利函數
    
    Args:
        db_manager: 資料庫管理器
        lottery_type: 彩票類型
        success_analysis: 成功分析結果
        
    Returns:
        Dict: 優化後的策略參數
    """
    optimizer = StrategyOptimizer(db_manager)
    return optimizer.optimize_strategy(lottery_type, success_analysis)
