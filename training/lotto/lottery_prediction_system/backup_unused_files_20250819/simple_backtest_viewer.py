#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單的回測結果查看器
解決用戶無法看到回測內容的問題
"""

import streamlit as st
import json
import os
import pandas as pd
from datetime import datetime
import plotly.express as px
import plotly.graph_objects as go

def load_backtest_reports():
    """載入所有回測報告"""
    reports_dir = "backtest_results"
    reports = []
    
    if not os.path.exists(reports_dir):
        return reports
    
    for filename in os.listdir(reports_dir):
        if filename.endswith('.json'):
            file_path = os.path.join(reports_dir, filename)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 解析檔名獲取資訊
                parts = filename.replace('.json', '').split('_')
                lottery_type = parts[0]
                timestamp = parts[-1]
                
                reports.append({
                    'filename': filename,
                    'lottery_type': lottery_type,
                    'timestamp': timestamp,
                    'data': data,
                    'file_path': file_path
                })
            except Exception as e:
                st.warning(f"載入報告失敗: {filename} - {str(e)}")
    
    return sorted(reports, key=lambda x: x['timestamp'], reverse=True)

def display_report_summary(report):
    """顯示報告摘要"""
    data = report['data']
    config = data.get('config', {})
    stats = data.get('statistics', {})
    
    st.subheader(f"📊 {config.get('game_type', '未知')} 回測報告")
    
    # 基本資訊
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("測試期數", stats.get('total_periods', 0))
    
    with col2:
        accuracy = stats.get('accuracy', 0)
        st.metric("平均準確度", f"{accuracy:.2f}%")
    
    with col3:
        total_matches = stats.get('total_matches', 0)
        st.metric("總匹配數", total_matches)
    
    with col4:
        avg_matches = stats.get('average_matches_per_period', 0)
        st.metric("平均匹配/期", f"{avg_matches:.2f}")
    
    # 配置資訊
    st.markdown("### 🔧 回測配置")
    config_col1, config_col2, config_col3 = st.columns(3)
    
    with config_col1:
        st.info(f"**彩票類型**: {config.get('game_type', '未知')}")
    
    with config_col2:
        st.info(f"**預測方法**: {config.get('method', '未知')}")
    
    with config_col3:
        st.info(f"**訓練窗口**: {config.get('training_window', 0)} 期")

def display_detailed_results(report):
    """顯示詳細結果"""
    data = report['data']
    
    # 獎項分佈
    if 'prize_distribution' in data:
        st.markdown("### 🏆 獎項分佈")
        prize_dist = data['prize_distribution']
        
        if any(prize_dist.values()):
            # 創建獎項分佈圖
            prizes = []
            counts = []
            for prize_level, count in prize_dist.items():
                if count > 0:
                    prizes.append(prize_level.replace('prize_', '第') + '獎')
                    counts.append(count)
            
            if prizes:
                fig = px.bar(x=prizes, y=counts, title="獎項分佈")
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("此次回測未中任何獎項")
        else:
            st.info("此次回測未中任何獎項")
    
    # 匹配數分佈
    if 'match_distribution' in data:
        st.markdown("### 🎯 匹配數分佈")
        match_dist = data['match_distribution']
        
        matches = []
        counts = []
        for match_level, count in match_dist.items():
            matches.append(match_level.replace('matches_', '') + '個匹配')
            counts.append(count)
        
        if matches:
            fig = px.pie(values=counts, names=matches, title="匹配數分佈")
            st.plotly_chart(fig, use_container_width=True)
    
    # 詳細結果表格
    if 'detailed_results' in data:
        st.markdown("### 📋 詳細回測結果")
        
        detailed = data['detailed_results']
        if detailed:
            # 轉換為DataFrame
            df_data = []
            for result in detailed[:20]:  # 只顯示前20筆
                df_data.append({
                    '期數': result.get('period', ''),
                    '預測號碼': ', '.join(map(str, result.get('predicted_numbers', []))),
                    '實際號碼': ', '.join(map(str, result.get('actual_numbers', []))),
                    '匹配數': result.get('matches', 0),
                    '準確度': f"{result.get('accuracy', 0) * 100:.1f}%",
                    '信心度': f"{result.get('confidence', 0) * 100:.1f}%"
                })
            
            if df_data:
                df = pd.DataFrame(df_data)
                st.dataframe(df, use_container_width=True)
            else:
                st.warning("沒有詳細結果數據")
        else:
            st.warning("沒有詳細結果數據")

def main():
    st.set_page_config(
        page_title="回測結果查看器",
        page_icon="📊",
        layout="wide"
    )
    
    st.title("📊 回測結果查看器")
    st.markdown("*簡單直觀的回測結果查看工具*")
    
    # 載入所有報告
    reports = load_backtest_reports()
    
    if not reports:
        st.error("❌ 沒有找到回測報告文件")
        st.info("回測報告應存放在 `backtest_results/` 目錄中")
        return
    
    st.success(f"✅ 找到 {len(reports)} 個回測報告")
    
    # 側邊欄選擇報告
    with st.sidebar:
        st.header("📁 選擇回測報告")
        
        # 按彩票類型篩選
        lottery_types = list(set([r['lottery_type'] for r in reports]))
        selected_lottery = st.selectbox("彩票類型", ['全部'] + lottery_types)
        
        # 篩選報告
        filtered_reports = reports
        if selected_lottery != '全部':
            filtered_reports = [r for r in reports if r['lottery_type'] == selected_lottery]
        
        # 選擇具體報告
        report_options = []
        for r in filtered_reports:
            timestamp = r['timestamp']
            formatted_time = f"{timestamp[:4]}-{timestamp[4:6]}-{timestamp[6:8]} {timestamp[8:10]}:{timestamp[10:12]}"
            option = f"{r['lottery_type']} ({formatted_time})"
            report_options.append(option)
        
        if report_options:
            selected_index = st.selectbox("選擇報告", range(len(report_options)), 
                                        format_func=lambda x: report_options[x])
            selected_report = filtered_reports[selected_index]
        else:
            st.error("沒有符合條件的報告")
            return
    
    # 主要內容區域
    if selected_report:
        # 顯示報告摘要
        display_report_summary(selected_report)
        
        st.markdown("---")
        
        # 顯示詳細結果
        display_detailed_results(selected_report)
        
        # 原始數據
        with st.expander("🔍 查看原始JSON數據"):
            st.json(selected_report['data'])
        
        # 下載按鈕
        st.download_button(
            label="📥 下載報告JSON",
            data=json.dumps(selected_report['data'], ensure_ascii=False, indent=2),
            file_name=selected_report['filename'],
            mime="application/json"
        )

if __name__ == "__main__":
    main()