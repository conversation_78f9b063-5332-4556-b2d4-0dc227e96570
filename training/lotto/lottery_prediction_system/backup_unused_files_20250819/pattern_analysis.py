#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模式分析模組
負責分析彩票號碼的各種模式和趨勢
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
import json
from datetime import datetime, timedelta
from collections import Counter, defaultdict
import statistics

class PatternAnalyzer:
    """
    模式分析器類
    負責分析彩票號碼的模式、趨勢和統計特徵
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化模式分析器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self.analysis_cache = {}
        self.pattern_history = []
        
    def analyze_number_patterns(self, numbers: List[List[int]], lottery_type: str = 'powerball') -> Dict[str, Any]:
        """
        分析號碼模式
        
        Args:
            numbers: 號碼列表
            lottery_type: 彩票類型
            
        Returns:
            模式分析結果字典
        """
        try:
            self.logger.info(f"開始分析 {lottery_type} 號碼模式")
            
            if not numbers:
                return self._get_empty_pattern_result()
            
            # 基本統計分析
            frequency_analysis = self._analyze_frequency(numbers)
            consecutive_analysis = self._analyze_consecutive_numbers(numbers)
            sum_analysis = self._analyze_number_sums(numbers)
            odd_even_analysis = self._analyze_odd_even_patterns(numbers)
            range_analysis = self._analyze_number_ranges(numbers)
            
            # 趨勢分析
            trend_analysis = self._analyze_trends(numbers)
            
            # 間隔分析
            gap_analysis = self._analyze_gaps(numbers)
            
            pattern_result = {
                'lottery_type': lottery_type,
                'total_draws': len(numbers),
                'analysis_timestamp': datetime.now().isoformat(),
                'frequency_analysis': frequency_analysis,
                'consecutive_analysis': consecutive_analysis,
                'sum_analysis': sum_analysis,
                'odd_even_analysis': odd_even_analysis,
                'range_analysis': range_analysis,
                'trend_analysis': trend_analysis,
                'gap_analysis': gap_analysis
            }
            
            # 緩存結果
            cache_key = f"{lottery_type}_{len(numbers)}"
            self.analysis_cache[cache_key] = pattern_result
            
            self.logger.info(f"{lottery_type} 號碼模式分析完成")
            return pattern_result
            
        except Exception as e:
            self.logger.error(f"號碼模式分析失敗: {e}")
            return self._get_empty_pattern_result()
    
    def _analyze_frequency(self, numbers: List[List[int]]) -> Dict[str, Any]:
        """
        分析號碼頻率
        """
        all_numbers = [num for draw in numbers for num in draw]
        frequency_counter = Counter(all_numbers)
        
        return {
            'most_frequent': frequency_counter.most_common(10),
            'least_frequent': frequency_counter.most_common()[-10:],
            'total_unique_numbers': len(frequency_counter),
            'average_frequency': statistics.mean(frequency_counter.values()) if frequency_counter else 0
        }
    
    def _analyze_consecutive_numbers(self, numbers: List[List[int]]) -> Dict[str, Any]:
        """
        分析連續號碼模式
        """
        consecutive_counts = []
        
        for draw in numbers:
            sorted_draw = sorted(draw)
            consecutive_count = 0
            
            for i in range(len(sorted_draw) - 1):
                if sorted_draw[i + 1] - sorted_draw[i] == 1:
                    consecutive_count += 1
            
            consecutive_counts.append(consecutive_count)
        
        return {
            'average_consecutive': statistics.mean(consecutive_counts) if consecutive_counts else 0,
            'max_consecutive': max(consecutive_counts) if consecutive_counts else 0,
            'consecutive_distribution': Counter(consecutive_counts)
        }
    
    def _analyze_number_sums(self, numbers: List[List[int]]) -> Dict[str, Any]:
        """
        分析號碼總和模式
        """
        sums = [sum(draw) for draw in numbers]
        
        return {
            'average_sum': statistics.mean(sums) if sums else 0,
            'median_sum': statistics.median(sums) if sums else 0,
            'min_sum': min(sums) if sums else 0,
            'max_sum': max(sums) if sums else 0,
            'sum_std_dev': statistics.stdev(sums) if len(sums) > 1 else 0
        }
    
    def _analyze_odd_even_patterns(self, numbers: List[List[int]]) -> Dict[str, Any]:
        """
        分析奇偶數模式
        """
        odd_even_patterns = []
        
        for draw in numbers:
            odd_count = sum(1 for num in draw if num % 2 == 1)
            even_count = len(draw) - odd_count
            odd_even_patterns.append((odd_count, even_count))
        
        pattern_counter = Counter(odd_even_patterns)
        
        return {
            'pattern_distribution': dict(pattern_counter),
            'most_common_pattern': pattern_counter.most_common(1)[0] if pattern_counter else None,
            'average_odd_count': statistics.mean([p[0] for p in odd_even_patterns]) if odd_even_patterns else 0,
            'average_even_count': statistics.mean([p[1] for p in odd_even_patterns]) if odd_even_patterns else 0
        }
    
    def _analyze_number_ranges(self, numbers: List[List[int]]) -> Dict[str, Any]:
        """
        分析號碼範圍分布
        """
        if not numbers:
            return {}
        
        # 假設號碼範圍是 1-49 (可根據彩票類型調整)
        max_number = max(max(draw) for draw in numbers) if numbers else 49
        range_size = max_number // 3
        
        range_distributions = []
        
        for draw in numbers:
            low_count = sum(1 for num in draw if num <= range_size)
            mid_count = sum(1 for num in draw if range_size < num <= range_size * 2)
            high_count = sum(1 for num in draw if num > range_size * 2)
            
            range_distributions.append({
                'low': low_count,
                'mid': mid_count,
                'high': high_count
            })
        
        return {
            'average_low': statistics.mean([r['low'] for r in range_distributions]) if range_distributions else 0,
            'average_mid': statistics.mean([r['mid'] for r in range_distributions]) if range_distributions else 0,
            'average_high': statistics.mean([r['high'] for r in range_distributions]) if range_distributions else 0,
            'range_boundaries': [range_size, range_size * 2, max_number]
        }
    
    def _analyze_trends(self, numbers: List[List[int]]) -> Dict[str, Any]:
        """
        分析號碼趨勢
        """
        if len(numbers) < 10:
            return {'trend_analysis': 'insufficient_data'}
        
        # 分析最近10期的趨勢
        recent_numbers = numbers[-10:]
        all_recent = [num for draw in recent_numbers for num in draw]
        recent_frequency = Counter(all_recent)
        
        # 分析前面期數的頻率
        earlier_numbers = numbers[:-10] if len(numbers) > 10 else []
        all_earlier = [num for draw in earlier_numbers for num in draw]
        earlier_frequency = Counter(all_earlier)
        
        # 找出趨勢變化
        trending_up = []
        trending_down = []
        
        for num in set(all_recent + all_earlier):
            recent_freq = recent_frequency.get(num, 0)
            earlier_freq = earlier_frequency.get(num, 0) / max(len(earlier_numbers), 1)
            recent_freq_normalized = recent_freq / len(recent_numbers)
            
            if recent_freq_normalized > earlier_freq * 1.5:
                trending_up.append(num)
            elif recent_freq_normalized < earlier_freq * 0.5:
                trending_down.append(num)
        
        return {
            'trending_up': trending_up[:10],
            'trending_down': trending_down[:10],
            'recent_hot_numbers': [num for num, freq in recent_frequency.most_common(10)],
            'recent_cold_numbers': [num for num, freq in recent_frequency.most_common()[-10:]]
        }
    
    def _analyze_gaps(self, numbers: List[List[int]]) -> Dict[str, Any]:
        """
        分析號碼間隔
        """
        if not numbers:
            return {}
        
        # 計算每個號碼的出現間隔
        number_gaps = defaultdict(list)
        number_last_seen = {}
        
        for draw_index, draw in enumerate(numbers):
            for num in draw:
                if num in number_last_seen:
                    gap = draw_index - number_last_seen[num]
                    number_gaps[num].append(gap)
                number_last_seen[num] = draw_index
        
        # 計算平均間隔
        average_gaps = {}
        for num, gaps in number_gaps.items():
            if gaps:
                average_gaps[num] = statistics.mean(gaps)
        
        return {
            'average_gaps': average_gaps,
            'numbers_with_long_gaps': [num for num, gap in average_gaps.items() if gap > 10],
            'numbers_with_short_gaps': [num for num, gap in average_gaps.items() if gap < 3]
        }
    
    def _get_empty_pattern_result(self) -> Dict[str, Any]:
        """
        返回空的模式分析結果
        """
        return {
            'lottery_type': 'unknown',
            'total_draws': 0,
            'analysis_timestamp': datetime.now().isoformat(),
            'frequency_analysis': {},
            'consecutive_analysis': {},
            'sum_analysis': {},
            'odd_even_analysis': {},
            'range_analysis': {},
            'trend_analysis': {},
            'gap_analysis': {}
        }
    
    def get_pattern_recommendations(self, analysis_result: Dict[str, Any]) -> List[str]:
        """
        基於模式分析結果提供建議
        
        Args:
            analysis_result: 模式分析結果
            
        Returns:
            建議列表
        """
        recommendations = []
        
        try:
            # 基於頻率分析的建議
            if 'frequency_analysis' in analysis_result:
                freq_analysis = analysis_result['frequency_analysis']
                if 'most_frequent' in freq_analysis:
                    hot_numbers = [str(num) for num, _ in freq_analysis['most_frequent'][:5]]
                    recommendations.append(f"考慮熱門號碼: {', '.join(hot_numbers)}")
            
            # 基於趨勢分析的建議
            if 'trend_analysis' in analysis_result:
                trend_analysis = analysis_result['trend_analysis']
                if 'trending_up' in trend_analysis and trend_analysis['trending_up']:
                    trending_numbers = [str(num) for num in trend_analysis['trending_up'][:3]]
                    recommendations.append(f"上升趨勢號碼: {', '.join(trending_numbers)}")
            
            # 基於奇偶分析的建議
            if 'odd_even_analysis' in analysis_result:
                odd_even = analysis_result['odd_even_analysis']
                if 'most_common_pattern' in odd_even and odd_even['most_common_pattern']:
                    pattern = odd_even['most_common_pattern'][0]
                    recommendations.append(f"建議奇偶比例: {pattern[0]}奇{pattern[1]}偶")
            
            if not recommendations:
                recommendations.append("數據不足，建議使用隨機選號")
                
        except Exception as e:
            self.logger.error(f"生成建議失敗: {e}")
            recommendations.append("分析出現錯誤，建議使用隨機選號")
        
        return recommendations
    
    def save_analysis_result(self, result: Dict[str, Any], filepath: str) -> bool:
        """
        保存分析結果到文件
        
        Args:
            result: 分析結果
            filepath: 文件路徑
            
        Returns:
            是否保存成功
        """
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"分析結果已保存到: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存分析結果失敗: {e}")
            return False
    
    def get_cached_analysis(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """
        獲取緩存的分析結果
        
        Args:
            cache_key: 緩存鍵
            
        Returns:
            緩存的分析結果或None
        """
        return self.analysis_cache.get(cache_key)
    
    def clear_cache(self):
        """
        清除分析緩存
        """
        self.analysis_cache.clear()
        self.logger.info("分析緩存已清除")