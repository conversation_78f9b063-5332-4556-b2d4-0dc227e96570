#!/usr/bin/env python3
"""
增强版预测系统集成模块
整合多组预测和准确性跟踪，提供完整的预测解决方案
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from .multi_group_predictor import MultiGroupPredictor
from .accuracy_tracker import AccuracyTracker
from .enhanced_multi_algorithm_predictor import EnhancedMultiAlgorithmPredictor

logger = logging.getLogger('enhanced_prediction_system')

class EnhancedPredictionSystem:
    """
    增强版预测系统
    
    核心功能：
    1. 整合多组预测功能
    2. 实时跟踪预测准确性
    3. 动态优化算法权重
    4. 提供完整的预测和分析解决方案
    """
    
    def __init__(self, lottery_type='powercolor', db_manager=None):
        """
        初始化增强版预测系统
        
        Args:
            lottery_type: 彩票类型
            db_manager: 数据库管理器
        """
        self.lottery_type = lottery_type
        self.db_manager = db_manager
        
        # 初始化各个组件
        self.enhanced_predictor = EnhancedMultiAlgorithmPredictor(lottery_type, db_manager)
        self.multi_group_predictor = MultiGroupPredictor(lottery_type, db_manager)
        self.accuracy_tracker = AccuracyTracker(f"accuracy_{lottery_type}.db")
        
        # 系统配置
        self.system_config = {
            'default_strategy': 'balanced',
            'auto_optimization': True,
            'min_confidence_threshold': 60,
            'max_groups': 8,
            'tracking_enabled': True,
            'report_generation': True
        }
        
        # 性能缓存
        self.performance_cache = {}
        self.last_optimization = None
        
        logger.info(f"增强版预测系统初始化完成 - 彩票类型: {lottery_type}")
    
    def generate_comprehensive_prediction(self, 
                                        df: pd.DataFrame,
                                        strategy: str = 'balanced',
                                        enable_multi_group: bool = True,
                                        custom_group_count: Optional[int] = None,
                                        next_draw_date: Optional[str] = None) -> Dict[str, Any]:
        """
        生成综合预测
        
        Args:
            df: 历史数据
            strategy: 预测策略
            enable_multi_group: 是否启用多组预测
            custom_group_count: 自定义组数
            next_draw_date: 下期开奖日期
            
        Returns:
            综合预测结果
        """
        logger.info(f"开始生成综合预测 - 策略: {strategy}, 多组: {enable_multi_group}")
        
        try:
            # 1. 优化算法权重（如果启用自动优化）
            if self.system_config['auto_optimization']:
                self._optimize_algorithm_weights()
            
            # 2. 生成基础预测
            if enable_multi_group:
                # 多组预测
                prediction_result = self.multi_group_predictor.generate_multi_group_predictions(
                    self.enhanced_predictor, df, strategy, custom_group_count
                )
                prediction_type = 'multi_group'
            else:
                # 单组预测
                prediction_result = self.enhanced_predictor.enhanced_ensemble_predict(df)
                prediction_type = 'single_group'
            
            if not prediction_result:
                logger.error("预测生成失败")
                return None
            
            # 3. 记录预测结果（如果启用跟踪）
            if self.system_config['tracking_enabled'] and next_draw_date:
                prediction_id = self.accuracy_tracker.record_prediction(
                    prediction_result, 
                    self.lottery_type,
                    next_draw_date,
                    strategy
                )
                prediction_result['tracking_id'] = prediction_id
            
            # 4. 增强预测结果
            enhanced_result = self._enhance_prediction_result(prediction_result, df, prediction_type)
            
            # 5. 生成建议和分析
            recommendations = self._generate_comprehensive_recommendations(enhanced_result, df)
            
            # 6. 组装最终结果
            final_result = {
                'system_version': 'Enhanced v3.0',
                'lottery_type': self.lottery_type,
                'prediction_type': prediction_type,
                'strategy': strategy,
                'timestamp': datetime.now().isoformat(),
                'prediction_data': enhanced_result,
                'recommendations': recommendations,
                'system_status': self._get_system_status(),
                'performance_metrics': self._get_current_performance_metrics()
            }
            
            logger.info(f"综合预测生成完成 - 类型: {prediction_type}")
            return final_result
            
        except Exception as e:
            logger.error(f"综合预测生成失败: {e}")
            return None
    
    def _optimize_algorithm_weights(self):
        """优化算法权重"""
        try:
            # 获取最近的性能数据
            current_time = datetime.now()
            if (self.last_optimization and 
                (current_time - self.last_optimization).days < 7):
                return  # 一周内已优化过
            
            # 获取各算法的性能指标
            algorithm_performance = {}
            for algorithm in self.enhanced_predictor.algorithm_weights.keys():
                metrics = self.accuracy_tracker.get_algorithm_metrics(
                    algorithm, self.lottery_type, days=30
                )
                if metrics:
                    algorithm_performance[algorithm] = {
                        'accuracy_rate': metrics.accuracy_rate,
                        'stability_score': metrics.stability_score,
                        'recent_performance': metrics.recent_performance,
                        'trend': metrics.trend
                    }
            
            if not algorithm_performance:
                logger.info("暂无性能数据，跳过权重优化")
                return
            
            # 计算新的权重
            new_weights = self._calculate_optimized_weights(algorithm_performance)
            
            # 应用新权重
            total_weight = sum(new_weights.values())
            for algorithm in self.enhanced_predictor.algorithm_weights:
                if algorithm in new_weights:
                    self.enhanced_predictor.algorithm_weights[algorithm] = (
                        new_weights[algorithm] / total_weight
                    )
            
            self.last_optimization = current_time
            logger.info(f"算法权重优化完成: {self.enhanced_predictor.algorithm_weights}")
            
        except Exception as e:
            logger.warning(f"权重优化失败: {e}")
    
    def _calculate_optimized_weights(self, performance_data: Dict[str, Dict]) -> Dict[str, float]:
        """计算优化后的权重"""
        optimized_weights = {}
        
        for algorithm, perf in performance_data.items():
            # 综合评分
            accuracy_score = perf['accuracy_rate'] * 0.4
            stability_score = perf['stability_score'] / 100 * 0.3
            recent_score = perf['recent_performance'] / 100 * 0.2
            
            # 趋势调整
            trend_multiplier = 1.0
            if perf['trend'] == 'improving':
                trend_multiplier = 1.2
            elif perf['trend'] == 'declining':
                trend_multiplier = 0.8
            
            # 最终权重
            final_score = (accuracy_score + stability_score + recent_score) * trend_multiplier * 0.1
            optimized_weights[algorithm] = max(0.05, min(0.4, final_score))  # 限制范围
        
        return optimized_weights
    
    def _enhance_prediction_result(self, 
                                 prediction_result: Dict[str, Any], 
                                 df: pd.DataFrame,
                                 prediction_type: str) -> Dict[str, Any]:
        """增强预测结果"""
        enhanced = prediction_result.copy()
        
        # 添加历史分析
        enhanced['historical_analysis'] = self._analyze_historical_patterns(df)
        
        # 添加风险评估
        enhanced['risk_assessment'] = self._assess_prediction_risk(prediction_result, df)
        
        # 添加期望分析
        enhanced['expectation_analysis'] = self._calculate_expectation_metrics(prediction_result)
        
        # 添加时间分析
        enhanced['timing_analysis'] = self._analyze_timing_factors()
        
        return enhanced
    
    def _analyze_historical_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析历史模式"""
        if df.empty:
            return {}
        
        main_cols = [f'Anumber{i}' for i in range(1, 7)]  # 假设6个主号码
        
        # 最近趋势分析
        recent_data = df.tail(10)
        all_recent_numbers = []
        for _, row in recent_data.iterrows():
            all_recent_numbers.extend([row[col] for col in main_cols if col in row])
        
        # 频率分析
        from collections import Counter
        number_freq = Counter(all_recent_numbers)
        
        # 模式分析
        patterns = {
            'hot_numbers': [num for num, freq in number_freq.most_common(10)],
            'cold_numbers': self._find_cold_numbers(df, main_cols),
            'consecutive_trend': self._analyze_consecutive_trend(recent_data, main_cols),
            'sum_trend': self._analyze_sum_trend(recent_data, main_cols),
            'odd_even_trend': self._analyze_odd_even_trend(recent_data, main_cols)
        }
        
        return patterns
    
    def _find_cold_numbers(self, df: pd.DataFrame, main_cols: List[str]) -> List[int]:
        """找出冷门号码"""
        all_numbers = []
        for _, row in df.tail(20).iterrows():
            all_numbers.extend([row[col] for col in main_cols if col in row])
        
        from collections import Counter
        freq = Counter(all_numbers)
        
        # 找出出现次数少的号码
        config = self.multi_group_predictor.config[self.lottery_type]
        main_numbers = config['main_numbers']
        
        cold_numbers = []
        avg_freq = len(all_numbers) / main_numbers
        
        for num in range(1, main_numbers + 1):
            if freq.get(num, 0) < avg_freq * 0.6:
                cold_numbers.append(num)
        
        return cold_numbers[:10]
    
    def _analyze_consecutive_trend(self, df: pd.DataFrame, main_cols: List[str]) -> Dict[str, Any]:
        """分析连续数趋势"""
        consecutive_counts = []
        
        for _, row in df.iterrows():
            numbers = sorted([row[col] for col in main_cols if col in row])
            consecutive = 0
            for i in range(1, len(numbers)):
                if numbers[i] == numbers[i-1] + 1:
                    consecutive += 1
            consecutive_counts.append(consecutive)
        
        return {
            'avg_consecutive': np.mean(consecutive_counts),
            'recent_trend': np.mean(consecutive_counts[-5:]) if len(consecutive_counts) >= 5 else 0,
            'max_consecutive': max(consecutive_counts) if consecutive_counts else 0
        }
    
    def _analyze_sum_trend(self, df: pd.DataFrame, main_cols: List[str]) -> Dict[str, Any]:
        """分析和值趋势"""
        sum_values = []
        
        for _, row in df.iterrows():
            total = sum([row[col] for col in main_cols if col in row])
            sum_values.append(total)
        
        return {
            'avg_sum': np.mean(sum_values),
            'recent_trend': np.mean(sum_values[-5:]) if len(sum_values) >= 5 else 0,
            'sum_range': [min(sum_values), max(sum_values)] if sum_values else [0, 0]
        }
    
    def _analyze_odd_even_trend(self, df: pd.DataFrame, main_cols: List[str]) -> Dict[str, Any]:
        """分析奇偶趋势"""
        odd_counts = []
        
        for _, row in df.iterrows():
            numbers = [row[col] for col in main_cols if col in row]
            odd_count = sum(1 for num in numbers if num % 2 == 1)
            odd_counts.append(odd_count)
        
        return {
            'avg_odd_count': np.mean(odd_counts),
            'recent_trend': np.mean(odd_counts[-5:]) if len(odd_counts) >= 5 else 0,
            'preferred_ratio': self._calculate_preferred_odd_ratio(odd_counts)
        }
    
    def _calculate_preferred_odd_ratio(self, odd_counts: List[int]) -> str:
        """计算偏好的奇偶比例"""
        from collections import Counter
        count_freq = Counter(odd_counts)
        most_common = count_freq.most_common(1)
        
        if most_common:
            preferred_odd = most_common[0][0]
            total_count = len([col for col in ['Anumber1', 'Anumber2', 'Anumber3', 'Anumber4', 'Anumber5', 'Anumber6']])
            preferred_even = total_count - preferred_odd
            return f"{preferred_odd}:{preferred_even}"
        
        return "3:3"
    
    def _assess_prediction_risk(self, prediction_result: Dict[str, Any], df: pd.DataFrame) -> Dict[str, Any]:
        """评估预测风险"""
        risk_factors = []
        overall_risk = "中等"
        
        # 信心度风险
        if 'groups' in prediction_result:
            confidences = [group['confidence'] for group in prediction_result['groups']]
            avg_confidence = np.mean(confidences)
        else:
            avg_confidence = prediction_result.get('confidence', 50)
        
        if avg_confidence < 60:
            risk_factors.append("平均信心度较低")
            overall_risk = "高"
        elif avg_confidence > 80:
            overall_risk = "低"
        
        # 多样性风险
        if 'groups' in prediction_result:
            all_numbers = set()
            for group in prediction_result['groups']:
                all_numbers.update(group['main_numbers'])
            
            config = self.multi_group_predictor.config[self.lottery_type]
            coverage_rate = len(all_numbers) / config['main_numbers']
            
            if coverage_rate < 0.4:
                risk_factors.append("号码覆盖率较低")
            elif coverage_rate > 0.8:
                risk_factors.append("号码过于分散")
        
        # 历史表现风险
        recent_performance = self._get_recent_algorithm_performance()
        if recent_performance < 0.3:
            risk_factors.append("近期算法表现不佳")
            if overall_risk != "高":
                overall_risk = "较高"
        
        return {
            'overall_risk': overall_risk,
            'risk_factors': risk_factors,
            'risk_score': self._calculate_risk_score(avg_confidence, len(risk_factors)),
            'mitigation_suggestions': self._generate_risk_mitigation_suggestions(risk_factors)
        }
    
    def _calculate_risk_score(self, confidence: float, risk_factor_count: int) -> float:
        """计算风险分数 (0-100, 越高风险越大)"""
        confidence_risk = max(0, (80 - confidence) / 80 * 50)
        factor_risk = min(40, risk_factor_count * 10)
        return confidence_risk + factor_risk
    
    def _generate_risk_mitigation_suggestions(self, risk_factors: List[str]) -> List[str]:
        """生成风险缓解建议"""
        suggestions = []
        
        for factor in risk_factors:
            if "信心度较低" in factor:
                suggestions.append("建议减少投注金额或等待更高信心度的预测")
            elif "覆盖率较低" in factor:
                suggestions.append("考虑增加预测组数以提高覆盖面")
            elif "过于分散" in factor:
                suggestions.append("建议集中投注前几组高信心度预测")
            elif "表现不佳" in factor:
                suggestions.append("建议等待算法性能改善后再进行投注")
        
        if not suggestions:
            suggestions.append("当前风险在可接受范围内，建议适度投注")
        
        return suggestions
    
    def _get_recent_algorithm_performance(self) -> float:
        """获取近期算法表现"""
        try:
            metrics = self.accuracy_tracker.get_real_time_metrics(self.lottery_type)
            if metrics['recent_performance']:
                avg_scores = [perf['avg_score'] for perf in metrics['recent_performance']]
                return np.mean(avg_scores) / 100 if avg_scores else 0.5
            return 0.5
        except:
            return 0.5
    
    def _calculate_expectation_metrics(self, prediction_result: Dict[str, Any]) -> Dict[str, Any]:
        """计算期望指标"""
        if 'groups' in prediction_result:
            # 多组预测
            groups = prediction_result['groups']
            total_confidence = sum(group['confidence'] for group in groups)
            weighted_expectation = sum(
                group['confidence'] * group.get('recommendation_weight', 1/len(groups))
                for group in groups
            )
            
            expectation = {
                'expected_success_probability': total_confidence / len(groups) / 100,
                'weighted_expectation': weighted_expectation / 100,
                'risk_return_ratio': self._calculate_risk_return_ratio(groups),
                'optimal_allocation': self._suggest_optimal_allocation(groups)
            }
        else:
            # 单组预测
            confidence = prediction_result.get('confidence', 50)
            expectation = {
                'expected_success_probability': confidence / 100,
                'weighted_expectation': confidence / 100,
                'risk_return_ratio': confidence / 100,
                'optimal_allocation': {'strategy': 'single_bet', 'allocation': '100%'}
            }
        
        return expectation
    
    def _calculate_risk_return_ratio(self, groups: List[Dict]) -> float:
        """计算风险收益比"""
        total_confidence = sum(group['confidence'] for group in groups)
        confidence_variance = np.var([group['confidence'] for group in groups])
        
        # 风险收益比 = 期望收益 / 风险（方差）
        expected_return = total_confidence / len(groups)
        risk = max(1, confidence_variance)  # 避免除零
        
        return expected_return / risk
    
    def _suggest_optimal_allocation(self, groups: List[Dict]) -> Dict[str, Any]:
        """建议最优分配策略"""
        sorted_groups = sorted(groups, key=lambda x: x['confidence'], reverse=True)
        
        # 基于信心度的分配策略
        total_confidence = sum(group['confidence'] for group in sorted_groups)
        
        if len(sorted_groups) >= 3:
            # 集中投注前3组
            top3_confidence = sum(group['confidence'] for group in sorted_groups[:3])
            if top3_confidence / total_confidence > 0.6:
                return {
                    'strategy': 'concentrated',
                    'description': '建议集中投注前3组',
                    'allocation': '60% 投注前3组，40% 分散其他组'
                }
        
        # 平均分配
        return {
            'strategy': 'balanced',
            'description': '建议平均分配所有组',
            'allocation': f'每组约 {100/len(sorted_groups):.1f}% 投注'
        }
    
    def _analyze_timing_factors(self) -> Dict[str, Any]:
        """分析时机因素"""
        now = datetime.now()
        
        timing_analysis = {
            'prediction_time': now.isoformat(),
            'day_of_week': now.strftime('%A'),
            'market_timing': self._assess_market_timing(now),
            'seasonal_factors': self._assess_seasonal_factors(now),
            'optimal_timing': self._suggest_optimal_timing()
        }
        
        return timing_analysis
    
    def _assess_market_timing(self, current_time: datetime) -> str:
        """评估市场时机"""
        hour = current_time.hour
        
        if 9 <= hour <= 11:
            return "上午黄金时段"
        elif 14 <= hour <= 16:
            return "下午黄金时段"
        elif 19 <= hour <= 21:
            return "晚间黄金时段"
        else:
            return "非高峰时段"
    
    def _assess_seasonal_factors(self, current_time: datetime) -> Dict[str, Any]:
        """评估季节因素"""
        month = current_time.month
        
        seasonal_info = {
            'month': month,
            'quarter': (month - 1) // 3 + 1,
            'season': ['winter', 'winter', 'spring', 'spring', 'spring', 'summer', 
                      'summer', 'summer', 'autumn', 'autumn', 'autumn', 'winter'][month - 1],
            'activity_level': 'high' if month in [1, 2, 11, 12] else 'normal'
        }
        
        return seasonal_info
    
    def _suggest_optimal_timing(self) -> str:
        """建议最优时机"""
        return "建议在开奖前2-4小时进行最终确认和投注"
    
    def _generate_comprehensive_recommendations(self, 
                                             prediction_result: Dict[str, Any], 
                                             df: pd.DataFrame) -> List[str]:
        """生成综合建议"""
        recommendations = []
        
        # 基于预测类型的建议
        if prediction_result.get('groups'):
            group_count = len(prediction_result['groups'])
            recommendations.append(f"本次生成了{group_count}组预测，建议根据资金情况选择投注组数")
            
            # 信心度建议
            high_conf_groups = [g for g in prediction_result['groups'] if g['confidence'] >= 75]
            if high_conf_groups:
                recommendations.append(f"有{len(high_conf_groups)}组高信心度预测，建议优先考虑")
        
        # 基于风险评估的建议
        risk_assessment = prediction_result.get('risk_assessment', {})
        if risk_assessment.get('overall_risk') == '高':
            recommendations.append("当前预测风险较高，建议谨慎投注或降低投注金额")
        elif risk_assessment.get('overall_risk') == '低':
            recommendations.append("当前预测风险较低，可适度增加投注金额")
        
        # 基于历史分析的建议
        historical = prediction_result.get('historical_analysis', {})
        if historical.get('hot_numbers'):
            hot_numbers = historical['hot_numbers'][:5]
            recommendations.append(f"近期热门号码: {hot_numbers}，可重点关注")
        
        # 基于时机分析的建议
        timing = prediction_result.get('timing_analysis', {})
        if timing.get('optimal_timing'):
            recommendations.append(timing['optimal_timing'])
        
        # 通用建议
        recommendations.extend([
            "建议合理分配资金，避免过度投注",
            "预测仅供参考，请理性对待中奖概率",
            "建议定期审视投注策略，及时调整"
        ])
        
        return recommendations
    
    def _get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'predictor_status': 'active',
            'tracker_status': 'active' if self.system_config['tracking_enabled'] else 'disabled',
            'optimization_status': 'enabled' if self.system_config['auto_optimization'] else 'disabled',
            'last_optimization': self.last_optimization.isoformat() if self.last_optimization else None,
            'algorithm_count': len(self.enhanced_predictor.algorithm_weights),
            'performance_cache_size': len(self.performance_cache)
        }
    
    def _get_current_performance_metrics(self) -> Dict[str, Any]:
        """获取当前性能指标"""
        try:
            real_time_metrics = self.accuracy_tracker.get_real_time_metrics(self.lottery_type)
            return {
                'pending_predictions': real_time_metrics.get('pending_predictions', 0),
                'recent_algorithm_performance': real_time_metrics.get('recent_performance', []),
                'system_reliability': self._calculate_system_reliability(),
                'data_freshness': self._assess_data_freshness()
            }
        except Exception as e:
            logger.warning(f"获取性能指标失败: {e}")
            return {'status': 'metrics_unavailable'}
    
    def _calculate_system_reliability(self) -> float:
        """计算系统可靠性"""
        try:
            # 基于最近的预测准确性计算
            report = self.accuracy_tracker.get_overall_performance_report(self.lottery_type, 7)
            success_rate = report['overall_statistics']['success_rate']
            return min(100, max(0, success_rate))
        except:
            return 75.0  # 默认值
    
    def _assess_data_freshness(self) -> str:
        """评估数据新鲜度"""
        # 简化实现，实际应该检查最新数据的时间戳
        return "fresh"  # 可以是 "fresh", "stale", "outdated"
    
    def verify_prediction_results(self, 
                                draw_date: str, 
                                actual_numbers: List[int], 
                                actual_special: Optional[int] = None) -> Dict[str, Any]:
        """
        验证预测结果
        
        Args:
            draw_date: 开奖日期
            actual_numbers: 实际开奖号码
            actual_special: 实际特别号
            
        Returns:
            验证结果
        """
        verification_result = self.accuracy_tracker.verify_predictions(
            draw_date, actual_numbers, actual_special, self.lottery_type
        )
        
        # 如果有验证结果，更新算法性能
        if verification_result['verified_count'] > 0:
            # 触发权重优化
            if self.system_config['auto_optimization']:
                self._optimize_algorithm_weights()
        
        return verification_result
    
    def generate_system_report(self, days: int = 30) -> str:
        """
        生成系统报告
        
        Args:
            days: 报告周期（天数）
            
        Returns:
            系统报告内容
        """
        try:
            # 获取性能报告
            performance_report = self.accuracy_tracker.get_overall_performance_report(
                self.lottery_type, days
            )
            
            # 生成综合报告
            report_lines = []
            report_lines.append("=" * 100)
            report_lines.append("🚀 增强版预测系统 - 综合性能报告")
            report_lines.append("=" * 100)
            
            report_lines.append(f"📊 系统信息:")
            report_lines.append(f"  • 彩票类型: {self.lottery_type}")
            report_lines.append(f"  • 报告周期: {days} 天")
            report_lines.append(f"  • 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report_lines.append(f"  • 系统版本: Enhanced v3.0")
            
            # 性能统计
            stats = performance_report['overall_statistics']
            report_lines.append(f"\n📈 性能统计:")
            report_lines.append(f"  • 总预测数: {stats['total_predictions']}")
            report_lines.append(f"  • 成功预测数: {stats['successful_predictions']}")
            report_lines.append(f"  • 成功率: {stats['success_rate']:.1f}%")
            report_lines.append(f"  • 平均匹配数: {stats['average_matches']:.2f}")
            
            # 算法排名
            if performance_report['algorithm_rankings']:
                report_lines.append(f"\n🏆 算法性能排名:")
                for i, alg in enumerate(performance_report['algorithm_rankings'][:5], 1):
                    report_lines.append(
                        f"  {i}. {alg['algorithm']}: 成功率{alg['success_rate']:.1f}%, "
                        f"平均分数{alg['avg_score']:.1f}"
                    )
            
            # 系统建议
            report_lines.append(f"\n💡 系统建议:")
            for rec in performance_report['recommendations']:
                report_lines.append(f"  • {rec}")
            
            # 系统状态
            status = self._get_system_status()
            report_lines.append(f"\n⚙️ 系统状态:")
            report_lines.append(f"  • 预测器状态: {status['predictor_status']}")
            report_lines.append(f"  • 跟踪器状态: {status['tracker_status']}")
            report_lines.append(f"  • 优化状态: {status['optimization_status']}")
            report_lines.append(f"  • 算法数量: {status['algorithm_count']}")
            
            report_lines.append("\n" + "=" * 100)
            
            return "\n".join(report_lines)
            
        except Exception as e:
            logger.error(f"生成系统报告失败: {e}")
            return f"报告生成失败: {e}"
    
    def update_system_config(self, config_updates: Dict[str, Any]):
        """更新系统配置"""
        self.system_config.update(config_updates)
        logger.info(f"系统配置已更新: {config_updates}")
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return {
            'lottery_type': self.lottery_type,
            'system_config': self.system_config,
            'system_status': self._get_system_status(),
            'performance_metrics': self._get_current_performance_metrics(),
            'algorithm_weights': self.enhanced_predictor.algorithm_weights,
            'last_optimization': self.last_optimization.isoformat() if self.last_optimization else None
        }


# 使用示例
if __name__ == "__main__":
    # 创建增强版预测系统
    prediction_system = EnhancedPredictionSystem('powercolor')
    
    # 模拟历史数据
    test_data = pd.DataFrame({
        'Anumber1': np.random.randint(1, 39, 100),
        'Anumber2': np.random.randint(1, 39, 100),
        'Anumber3': np.random.randint(1, 39, 100),
        'Anumber4': np.random.randint(1, 39, 100),
        'Anumber5': np.random.randint(1, 39, 100),
        'Anumber6': np.random.randint(1, 39, 100),
        'Snumber': np.random.randint(1, 9, 100)
    })
    
    # 生成综合预测
    result = prediction_system.generate_comprehensive_prediction(
        test_data, 
        strategy='balanced',
        enable_multi_group=True,
        next_draw_date='2025-08-19'
    )
    
    if result:
        print("预测生成成功！")
        print(f"预测类型: {result['prediction_type']}")
        print(f"系统状态: {result['system_status']}")
    
    # 生成系统报告
    report = prediction_system.generate_system_report(30)
    print("\n系统报告:")
    print(report)