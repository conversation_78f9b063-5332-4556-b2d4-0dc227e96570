#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重置預測系統並進行順序預測

功能：
1. 清理現有預測記錄
2. 從指定期號開始，使用歷史資料進行順序預測
3. 每期預測只使用該期之前的所有歷史資料
4. 支援逐期校正和驗證
"""

import sys
import os
sys.path.append('.')

from data.db_manager import DBManager
from prediction.enhanced_lottery_predictor import EnhancedLotteryPredictor
from data.feature_engineering import FeatureEngineer
import pandas as pd
from datetime import datetime
import json
import traceback

class SequentialPredictor:
    def __init__(self):
        from config_manager import ConfigManager
        config_manager = ConfigManager()
        self.db_manager = DBManager(config_manager)
        self.predictor = EnhancedLotteryPredictor(self.db_manager)
        self.feature_engineer = FeatureEngineer()
        
    def clear_prediction_records(self, lottery_type='powercolor', confirm=False):
        """
        清理預測記錄
        
        Args:
            lottery_type: 彩票類型
            confirm: 是否確認清理
        """
        if not confirm:
            print("⚠️  警告：此操作將刪除所有預測記錄！")
            print("如要確認清理，請設置 confirm=True")
            return False
            
        try:
            conn = self.db_manager.create_connection()
            cursor = conn.cursor()
            
            # 根據彩票類型選擇表名
            table_map = {
                'powercolor': 'PowercolorPredictions',
                'lotto649': 'Lotto649Predictions', 
                'dailycash': 'DailyCashPredictions'
            }
            
            table_name = table_map.get(lottery_type.lower())
            if not table_name:
                raise ValueError(f"不支援的彩票類型: {lottery_type}")
            
            # 刪除預測記錄
            cursor.execute(f"DELETE FROM {table_name}")
            
            # 也清理相關的元數據
            cursor.execute("DELETE FROM PredictionMetadata WHERE PredictionID NOT IN (SELECT PredictionID FROM PowercolorPredictions UNION SELECT PredictionID FROM Lotto649Predictions UNION SELECT PredictionID FROM DailyCashPredictions)")
            
            conn.commit()
            conn.close()
            
            print(f"✅ 已清理 {lottery_type} 的所有預測記錄")
            return True
            
        except Exception as e:
            print(f"❌ 清理預測記錄時發生錯誤: {str(e)}")
            traceback.print_exc()
            return False
    
    def get_available_periods(self, lottery_type='powercolor'):
        """
        獲取可用的期號列表（按時間順序）
        
        Args:
            lottery_type: 彩票類型
            
        Returns:
            list: 期號列表，按時間升序排列
        """
        try:
            periods = self.db_manager.get_periods_list(lottery_type, limit=2000)
            # 轉換為升序（最舊的在前）
            periods.reverse()
            return periods
        except Exception as e:
            print(f"❌ 獲取期號列表時發生錯誤: {str(e)}")
            return []
    
    def predict_single_period(self, target_period, lottery_type='powercolor', use_periods_before=None):
        """
        預測單一期號
        
        Args:
            target_period: 目標期號
            lottery_type: 彩票類型
            use_periods_before: 使用此期號之前的資料進行預測
            
        Returns:
            dict: 預測結果
        """
        try:
            print(f"\n🎯 開始預測 {lottery_type} 第 {target_period} 期")
            
            # 如果指定了使用期號，則只使用該期號之前的資料
            if use_periods_before:
                # 獲取指定期號之前的所有資料
                all_periods = self.get_available_periods(lottery_type)
                training_periods = [p for p in all_periods if int(str(p)) < int(str(use_periods_before))]
                
                if len(training_periods) < 10:
                    print(f"⚠️  警告：訓練資料不足（只有 {len(training_periods)} 期），建議至少需要10期以上")
                    return None
                
                print(f"📊 使用 {len(training_periods)} 期歷史資料進行預測（最新訓練期號：{training_periods[-1]}）")
                
                # 載入指定範圍的資料
                training_data = self.db_manager.load_lottery_data(lottery_type)
                training_data = training_data[training_data['Period'].isin(training_periods)]
            else:
                # 使用所有可用資料
                training_data = self.db_manager.load_lottery_data(lottery_type)
                print(f"📊 使用所有 {len(training_data)} 期歷史資料進行預測")
            
            if training_data.empty:
                print("❌ 沒有可用的訓練資料")
                return None
            
            # 進行預測
            prediction_result = self.predictor.predict(
                lottery_type=lottery_type,
                period=target_period,
                data=training_data
            )
            
            if prediction_result:
                print(f"✅ 預測完成，生成了 {len(prediction_result.get('predictions', []))} 組預測號碼")
                
                # 保存預測結果
                save_result = self.db_manager.save_prediction(
                    prediction=prediction_result,
                    period=target_period,
                    lottery_type=lottery_type,
                    metadata={
                        'training_periods_count': len(training_data),
                        'use_periods_before': use_periods_before,
                        'prediction_mode': 'sequential'
                    }
                )
                
                if save_result:
                    print(f"💾 預測結果已保存")
                else:
                    print(f"⚠️  預測結果保存失敗")
                
                return prediction_result
            else:
                print(f"❌ 預測失敗")
                return None
                
        except Exception as e:
            print(f"❌ 預測期號 {target_period} 時發生錯誤: {str(e)}")
            traceback.print_exc()
            return None
    
    def sequential_predict(self, start_period, end_period=None, lottery_type='powercolor'):
        """
        順序預測多個期號
        
        Args:
            start_period: 開始期號
            end_period: 結束期號（如果為None，則預測到最新期號）
            lottery_type: 彩票類型
        """
        try:
            print(f"\n🚀 開始順序預測 {lottery_type}")
            print(f"📅 預測範圍：從第 {start_period} 期開始")
            
            # 獲取所有可用期號
            all_periods = self.get_available_periods(lottery_type)
            
            # 找到開始期號的位置
            start_index = None
            for i, period in enumerate(all_periods):
                if int(str(period)) >= int(str(start_period)):
                    start_index = i
                    break
            
            if start_index is None:
                print(f"❌ 找不到開始期號 {start_period}")
                return
            
            # 確定預測範圍
            if end_period:
                end_index = None
                for i, period in enumerate(all_periods):
                    if int(str(period)) > int(str(end_period)):
                        end_index = i
                        break
                if end_index is None:
                    end_index = len(all_periods)
                predict_periods = all_periods[start_index:end_index]
            else:
                predict_periods = all_periods[start_index:]
            
            print(f"📋 將預測 {len(predict_periods)} 期：{predict_periods[:5]}{'...' if len(predict_periods) > 5 else ''}")
            
            # 逐期預測
            success_count = 0
            total_count = len(predict_periods)
            
            for i, target_period in enumerate(predict_periods):
                print(f"\n{'='*50}")
                print(f"進度：{i+1}/{total_count} ({(i+1)/total_count*100:.1f}%)")
                
                # 使用目標期號之前的所有資料進行預測
                result = self.predict_single_period(
                    target_period=target_period,
                    lottery_type=lottery_type,
                    use_periods_before=target_period
                )
                
                if result:
                    success_count += 1
                    
                    # 如果有實際開獎結果，進行驗證
                    actual_data = self.db_manager.get_period_data(target_period, lottery_type)
                    if actual_data:
                        print(f"🔍 找到實際開獎結果，進行驗證...")
                        # 這裡可以添加驗證邏輯
                else:
                    print(f"❌ 第 {target_period} 期預測失敗")
                
                # 每10期顯示一次進度總結
                if (i + 1) % 10 == 0:
                    print(f"\n📊 階段性總結：已完成 {i+1} 期，成功 {success_count} 期，成功率 {success_count/(i+1)*100:.1f}%")
            
            print(f"\n🎉 順序預測完成！")
            print(f"📊 總結：預測 {total_count} 期，成功 {success_count} 期，成功率 {success_count/total_count*100:.1f}%")
            
        except Exception as e:
            print(f"❌ 順序預測時發生錯誤: {str(e)}")
            traceback.print_exc()

def main():
    predictor = SequentialPredictor()
    
    print("=== 重置預測系統並進行順序預測 ===")
    print("\n選項：")
    print("1. 查看當前狀態")
    print("2. 清理預測記錄")
    print("3. 從 114000001 開始順序預測")
    print("4. 預測單一期號")
    print("5. 自定義範圍預測")
    
    choice = input("\n請選擇操作 (1-5): ").strip()
    
    if choice == '1':
        # 查看當前狀態
        print("\n=== 當前狀態 ===")
        records = predictor.db_manager.load_prediction_records('powercolor', limit=None)
        print(f"威力彩預測記錄數量: {len(records)}")
        
        periods = predictor.get_available_periods('powercolor')
        print(f"威力彩可用期號數量: {len(periods)}")
        if periods:
            print(f"期號範圍: {periods[0]} ~ {periods[-1]}")
            
            # 找出2025年期號
            periods_2025 = [p for p in periods if str(p).startswith('114')]
            print(f"2025年期號: {len(periods_2025)} 期 ({periods_2025[0]} ~ {periods_2025[-1]})")
    
    elif choice == '2':
        # 清理預測記錄
        confirm = input("⚠️  確認要清理所有威力彩預測記錄嗎？(yes/no): ").strip().lower()
        if confirm == 'yes':
            predictor.clear_prediction_records('powercolor', confirm=True)
        else:
            print("❌ 操作已取消")
    
    elif choice == '3':
        # 從114000001開始順序預測
        confirm = input("確認要從 114000001 開始順序預測嗎？這可能需要很長時間 (yes/no): ").strip().lower()
        if confirm == 'yes':
            predictor.sequential_predict(start_period='114000001', lottery_type='powercolor')
        else:
            print("❌ 操作已取消")
    
    elif choice == '4':
        # 預測單一期號
        period = input("請輸入要預測的期號 (例如: 114000001): ").strip()
        if period:
            predictor.predict_single_period(
                target_period=period,
                lottery_type='powercolor',
                use_periods_before=period
            )
    
    elif choice == '5':
        # 自定義範圍預測
        start = input("請輸入開始期號 (例如: 114000001): ").strip()
        end = input("請輸入結束期號 (例如: 114000010，留空表示到最新期): ").strip()
        
        if start:
            end_period = end if end else None
            predictor.sequential_predict(
                start_period=start,
                end_period=end_period,
                lottery_type='powercolor'
            )
    
    else:
        print("❌ 無效的選擇")

if __name__ == "__main__":
    main()