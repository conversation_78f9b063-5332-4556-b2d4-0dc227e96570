#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略優化器模組
負責優化預測策略和參數調整
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
import json
from datetime import datetime

class StrategyOptimizer:
    """
    策略優化器類
    負責優化預測策略、參數調整和性能評估
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化策略優化器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self.optimization_history = []
        self.best_parameters = {}
        
    def optimize_strategy(self, strategy_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        優化指定策略的參數
        
        Args:
            strategy_name: 策略名稱
            parameters: 當前參數
            
        Returns:
            優化後的參數字典
        """
        try:
            self.logger.info(f"開始優化策略: {strategy_name}")
            
            # 模擬優化過程
            optimized_params = parameters.copy()
            
            # 基本優化邏輯（模擬）
            if 'learning_rate' in optimized_params:
                optimized_params['learning_rate'] = min(0.01, optimized_params.get('learning_rate', 0.001) * 1.1)
            
            if 'batch_size' in optimized_params:
                optimized_params['batch_size'] = min(128, optimized_params.get('batch_size', 32) * 2)
                
            if 'epochs' in optimized_params:
                optimized_params['epochs'] = min(100, optimized_params.get('epochs', 50) + 10)
            
            # 記錄優化歷史
            optimization_record = {
                'timestamp': datetime.now().isoformat(),
                'strategy': strategy_name,
                'original_params': parameters,
                'optimized_params': optimized_params,
                'improvement_score': 0.05  # 模擬改進分數
            }
            
            self.optimization_history.append(optimization_record)
            self.best_parameters[strategy_name] = optimized_params
            
            self.logger.info(f"策略 {strategy_name} 優化完成")
            return optimized_params
            
        except Exception as e:
            self.logger.error(f"策略優化失敗: {e}")
            return parameters
    
    def evaluate_performance(self, strategy_name: str, results: List[Dict]) -> Dict[str, float]:
        """
        評估策略性能
        
        Args:
            strategy_name: 策略名稱
            results: 預測結果列表
            
        Returns:
            性能指標字典
        """
        try:
            if not results:
                return {'accuracy': 0.0, 'precision': 0.0, 'recall': 0.0}
            
            # 模擬性能計算
            total_predictions = len(results)
            correct_predictions = sum(1 for r in results if r.get('correct', False))
            
            accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0.0
            precision = accuracy * 0.9  # 模擬精確度
            recall = accuracy * 0.8     # 模擬召回率
            
            performance = {
                'accuracy': round(accuracy, 4),
                'precision': round(precision, 4),
                'recall': round(recall, 4),
                'total_predictions': total_predictions,
                'correct_predictions': correct_predictions
            }
            
            self.logger.info(f"策略 {strategy_name} 性能評估完成: {performance}")
            return performance
            
        except Exception as e:
            self.logger.error(f"性能評估失敗: {e}")
            return {'accuracy': 0.0, 'precision': 0.0, 'recall': 0.0}
    
    def get_best_parameters(self, strategy_name: str) -> Dict[str, Any]:
        """
        獲取策略的最佳參數
        
        Args:
            strategy_name: 策略名稱
            
        Returns:
            最佳參數字典
        """
        return self.best_parameters.get(strategy_name, {})
    
    def get_optimization_history(self) -> List[Dict]:
        """
        獲取優化歷史記錄
        
        Returns:
            優化歷史列表
        """
        return self.optimization_history.copy()
    
    def save_optimization_results(self, filepath: str) -> bool:
        """
        保存優化結果到文件
        
        Args:
            filepath: 文件路徑
            
        Returns:
            是否保存成功
        """
        try:
            results = {
                'best_parameters': self.best_parameters,
                'optimization_history': self.optimization_history,
                'timestamp': datetime.now().isoformat()
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"優化結果已保存到: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存優化結果失敗: {e}")
            return False
    
    def load_optimization_results(self, filepath: str) -> bool:
        """
        從文件載入優化結果
        
        Args:
            filepath: 文件路徑
            
        Returns:
            是否載入成功
        """
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            self.best_parameters = results.get('best_parameters', {})
            self.optimization_history = results.get('optimization_history', [])
            
            self.logger.info(f"優化結果已從 {filepath} 載入")
            return True
            
        except Exception as e:
            self.logger.error(f"載入優化結果失敗: {e}")
            return False
    
    def reset_optimization(self):
        """
        重置優化狀態
        """
        self.optimization_history.clear()
        self.best_parameters.clear()
        self.logger.info("優化狀態已重置")
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """
        獲取優化摘要
        
        Returns:
            優化摘要字典
        """
        return {
            'total_optimizations': len(self.optimization_history),
            'optimized_strategies': list(self.best_parameters.keys()),
            'last_optimization': self.optimization_history[-1] if self.optimization_history else None,
            'average_improvement': sum(r.get('improvement_score', 0) for r in self.optimization_history) / len(self.optimization_history) if self.optimization_history else 0
        }