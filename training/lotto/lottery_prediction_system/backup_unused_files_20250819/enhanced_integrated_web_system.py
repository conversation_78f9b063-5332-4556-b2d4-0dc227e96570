#!/usr/bin/env python3
"""
增強版整合式彩票預測系統 - Web界面
結合開獎查詢、回測分析、預測方法分析、自動更新數據的完整系統
"""

import streamlit as st
import pandas as pd
import json
import os
import sqlite3
import plotly.express as px
import plotly.graph_objects as go
import requests
from datetime import datetime, timedelta
from batch_backtest import BatchBacktester, BacktestConfig, GameType, PredictionMethod
from backtest_manager import BacktestManager
import logging


def init_session_state():
    """初始化session state"""
    if 'backtest_manager' not in st.session_state:
        st.session_state.backtest_manager = BacktestManager()
    if 'current_results' not in st.session_state:
        st.session_state.current_results = None
    if 'last_update_time' not in st.session_state:
        st.session_state.last_update_time = None


def main():
    st.set_page_config(
        page_title="增強版彩票預測系統",
        page_icon="🎯",
        layout="wide"
    )
    
    init_session_state()
    
    # 系統標題和狀態
    show_system_header()
    
    # 主導航選單
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "🏆 開獎結果查詢", 
        "📊 回測分析系統", 
        "🔍 預測方法分析", 
        "🔄 數據更新管理",
        "📈 系統管理"
    ])
    
    with tab1:
        show_lottery_results_page()
    
    with tab2:
        show_backtest_analysis_page()
    
    with tab3:
        show_prediction_analysis_page()
    
    with tab4:
        show_data_update_page()
    
    with tab5:
        show_system_management_page()


def show_system_header():
    """顯示系統標題和狀態"""
    st.markdown("""
        <div style='background: linear-gradient(90deg, #4e79a7 0%, #6b4c93 100%); padding: 1.5rem; border-radius: 10px; margin-bottom: 2rem;'>
            <h1 style='color: white; text-align: center; margin: 0;'>🎯 增強版彩票預測系統</h1>
            <p style='color: white; text-align: center; margin: 0.5rem 0 0 0; opacity: 0.9;'>完整的開獎查詢、自動更新、回測分析、預測方法評估系統</p>
        </div>
    """, unsafe_allow_html=True)
    
    # 系統狀態欄
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        # 檢查資料庫狀態
        db_status = check_database_status()
        st.metric("資料庫狀態", "✅ 正常" if db_status else "❌ 異常")
    
    with col2:
        # 顯示最後更新時間  
        if st.session_state.last_update_time:
            last_update = st.session_state.last_update_time.strftime("%H:%M")
        else:
            last_update = "未知"
        st.metric("最後更新", last_update)
    
    with col3:
        # 顯示資料記錄數
        total_records = get_total_records()
        st.metric("總記錄數", total_records)
    
    with col4:
        # 系統版本
        st.metric("系統版本", "v3.0")


def check_database_status():
    """檢查資料庫狀態"""
    try:
        db_path = "data/lottery_data.db"
        if not os.path.exists(db_path):
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        conn.close()
        
        expected_tables = ['Powercolor', 'Lotto649', 'DailyCash']
        existing_tables = [table[0] for table in tables]
        
        return all(table in existing_tables for table in expected_tables)
    except:
        return False


def get_total_records():
    """獲取總記錄數"""
    try:
        db_path = "data/lottery_data.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        total = 0
        for table in ['Powercolor', 'Lotto649', 'DailyCash']:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                total += count
            except:
                continue
        
        conn.close()
        return total
    except:
        return 0


def show_lottery_results_page():
    """開獎結果查詢頁面"""
    st.header("🏆 開獎結果查詢")
    
    # 搜尋控制面板
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        # 遊戲類型選擇
        game_options = {
            "威力彩": "Powercolor",
            "大樂透": "Lotto649", 
            "今彩539": "DailyCash"
        }
        selected_game = st.selectbox("選擇彩票類型", list(game_options.keys()))
        table_name = game_options[selected_game]
    
    with col2:
        # 期數搜尋
        period_input = st.text_input(
            "期數搜尋", 
            placeholder="例如: 114000058",
            help="格式: 114000### (民國114年第###期)"
        )
    
    with col3:
        # 日期範圍
        date_range = st.date_input(
            "日期範圍",
            value=None,
            help="選擇開獎日期範圍"
        )
    
    with col4:
        # 顯示筆數
        display_count = st.selectbox("顯示筆數", [10, 20, 50, 100], index=1)
    
    # 查詢按鈕
    col_btn1, col_btn2, col_btn3 = st.columns(3)
    with col_btn1:
        search_btn = st.button("🔍 查詢", type="primary", use_container_width=True)
    with col_btn2:
        refresh_btn = st.button("🔄 刷新", use_container_width=True)
    with col_btn3:
        update_btn = st.button("⬇️ 更新數據", use_container_width=True)
    
    # 執行更新數據
    if update_btn:
        with st.spinner("🔄 正在更新數據..."):
            update_result = update_lottery_data_realtime()
            if update_result:
                st.success("✅ 數據更新成功！")
                st.rerun()
            else:
                st.warning("⚠️ 數據更新失敗，使用現有數據")
    
    # 顯示查詢結果
    if search_btn or refresh_btn or not period_input:
        show_lottery_query_results(table_name, selected_game, period_input, date_range, display_count)


def update_lottery_data_realtime():
    """即時更新開獎數據"""
    try:
        # 嘗試導入數據更新器
        import sys
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from data.real_lottery_updater import RealLotteryUpdater
        from data.db_manager import DBManager
        
        db_manager = DBManager()
        updater = RealLotteryUpdater(db_manager)
        
        results = updater.update_all_lottery_results()
        
        success_count = sum(1 for r in results.values() if r.get('status') == 'success')
        st.session_state.last_update_time = datetime.now()
        
        return success_count > 0
        
    except Exception as e:
        st.error(f"更新失敗: {str(e)}")
        return False


def show_lottery_query_results(table_name, game_type, period_filter, date_range, limit):
    """顯示開獎查詢結果"""
    try:
        db_path = "data/lottery_data.db"
        if not os.path.exists(db_path):
            st.error("❌ 資料庫文件不存在")
            return
        
        conn = sqlite3.connect(db_path)
        
        # 構建查詢
        query = f"SELECT * FROM {table_name}"
        conditions = []
        params = []
        
        if period_filter:
            conditions.append("Period LIKE ?")
            params.append(f"%{period_filter}%")
        
        if date_range:
            if isinstance(date_range, (list, tuple)) and len(date_range) == 2:
                conditions.append("date(Sdate) >= ? AND date(Sdate) <= ?")
                params.extend([str(date_range[0]), str(date_range[1])])
            elif date_range:
                conditions.append("date(Sdate) = ?")
                params.append(str(date_range))
        
        if conditions:
            query += " WHERE " + " AND ".join(conditions)
        
        query += " ORDER BY CAST(Period AS INTEGER) DESC LIMIT ?"
        params.append(limit)
        
        df = pd.read_sql_query(query, conn, params=params)
        conn.close()
        
        if df.empty:
            st.warning("📝 未找到符合條件的開獎記錄")
            return
        
        # 顯示統計摘要
        show_lottery_statistics(df, game_type)
        
        st.markdown("---")
        
        # 格式化並顯示結果
        display_lottery_results_table(df, game_type)
        
        # 圖表分析
        if len(df) > 1:
            show_lottery_charts(df, game_type)
        
    except Exception as e:
        st.error(f"❌ 查詢失敗: {str(e)}")


def show_lottery_statistics(df, game_type):
    """顯示開獎統計摘要"""
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("查詢結果數", len(df))
    
    with col2:
        if not df.empty:
            latest_period = df.iloc[0]['Period']
            st.metric("最新期數", latest_period)
        else:
            st.metric("最新期數", "無")
    
    with col3:
        # 計算最常出現號碼
        numbers = []
        if game_type in ["威力彩", "大樂透"]:
            for _, row in df.iterrows():
                numbers.extend([
                    row.get('Anumber1', 0), row.get('Anumber2', 0), 
                    row.get('Anumber3', 0), row.get('Anumber4', 0),
                    row.get('Anumber5', 0), row.get('Anumber6', 0)
                ])
        elif game_type == "今彩539":
            for _, row in df.iterrows():
                numbers.extend([
                    row.get('Anumber1', 0), row.get('Anumber2', 0), 
                    row.get('Anumber3', 0), row.get('Anumber4', 0),
                    row.get('Anumber5', 0)
                ])
        
        numbers = [n for n in numbers if n > 0]
        if numbers:
            from collections import Counter
            most_common = Counter(numbers).most_common(1)[0]
            st.metric("最熱號碼", f"{most_common[0]} ({most_common[1]}次)")
        else:
            st.metric("最熱號碼", "無")
    
    with col4:
        if len(df) > 1:
            date_range = f"{df.iloc[-1]['Sdate'][:10]} ~ {df.iloc[0]['Sdate'][:10]}"
            st.metric("日期範圍", date_range)
        elif len(df) == 1:
            st.metric("開獎日期", df.iloc[0]['Sdate'][:10])
        else:
            st.metric("日期範圍", "無")


def display_lottery_results_table(df, game_type):
    """顯示開獎結果表格"""
    st.subheader("📋 開獎結果明細")
    
    # 格式化顯示
    display_df = df.copy()
    
    if game_type == "威力彩":
        display_df['開獎號碼'] = display_df.apply(lambda row: 
            f"{row.get('Anumber1', '?'):02d} {row.get('Anumber2', '?'):02d} {row.get('Anumber3', '?'):02d} "
            f"{row.get('Anumber4', '?'):02d} {row.get('Anumber5', '?'):02d} {row.get('Anumber6', '?'):02d} "
            f"+ {row.get('Second_district', '?'):02d}", axis=1
        )
    elif game_type == "大樂透":
        display_df['開獎號碼'] = display_df.apply(lambda row: 
            f"{row.get('Anumber1', '?'):02d} {row.get('Anumber2', '?'):02d} {row.get('Anumber3', '?'):02d} "
            f"{row.get('Anumber4', '?'):02d} {row.get('Anumber5', '?'):02d} {row.get('Anumber6', '?'):02d} "
            f"特別號 {row.get('Second_district', '?'):02d}", axis=1
        )
    elif game_type == "今彩539":
        display_df['開獎號碼'] = display_df.apply(lambda row: 
            f"{row.get('Anumber1', '?'):02d} {row.get('Anumber2', '?'):02d} {row.get('Anumber3', '?'):02d} "
            f"{row.get('Anumber4', '?'):02d} {row.get('Anumber5', '?'):02d}", axis=1
        )
    
    # 選擇要顯示的欄位
    columns_to_show = ['Period', 'Sdate', '開獎號碼'] if '開獎號碼' in display_df.columns else ['Period', 'Sdate']
    display_df = display_df[columns_to_show]
    display_df.columns = ['期數', '開獎日期', '開獎號碼'] if '開獎號碼' in display_df.columns else ['期數', '開獎日期']
    
    # 顯示表格
    st.dataframe(display_df, use_container_width=True, height=400)
    
    # 下載按鈕
    csv = display_df.to_csv(index=False, encoding='utf-8-sig')
    st.download_button(
        label="💾 下載開獎結果",
        data=csv,
        file_name=f"{game_type}_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
        mime="text/csv"
    )


def show_lottery_charts(df, game_type):
    """顯示開獎數據圖表分析"""
    st.subheader("📊 開獎數據分析")
    
    if game_type in ["威力彩", "大樂透"]:
        col1, col2 = st.columns(2)
        
        with col1:
            # 號碼頻率分析
            numbers = []
            for _, row in df.iterrows():
                numbers.extend([
                    row.get('Anumber1', 0), row.get('Anumber2', 0), 
                    row.get('Anumber3', 0), row.get('Anumber4', 0),
                    row.get('Anumber5', 0), row.get('Anumber6', 0)
                ])
            
            numbers = [n for n in numbers if n > 0]
            if numbers:
                from collections import Counter
                number_freq = Counter(numbers)
                
                freq_df = pd.DataFrame([
                    {"號碼": num, "出現次數": count} 
                    for num, count in number_freq.most_common(20)
                ])
                
                fig = px.bar(
                    freq_df, 
                    x="號碼", 
                    y="出現次數",
                    title="號碼出現頻率 (前20名)",
                    color="出現次數",
                    color_continuous_scale="viridis"
                )
                st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # 特別號分析
            if game_type == "威力彩":
                special_numbers = df['Second_district'].dropna().tolist()
                if special_numbers:
                    from collections import Counter
                    special_freq = Counter(special_numbers)
                    
                    special_df = pd.DataFrame([
                        {"特別號": num, "出現次數": count} 
                        for num, count in special_freq.most_common()
                    ])
                    
                    fig = px.pie(
                        special_df, 
                        values="出現次數", 
                        names="特別號",
                        title="威力彩特別號分佈"
                    )
                    st.plotly_chart(fig, use_container_width=True)


def show_backtest_analysis_page():
    """回測分析系統頁面 - 重用之前的實現"""
    st.header("📊 回測分析系統")
    
    # 這裡重用之前實現的回測分析功能
    subtab1, subtab2, subtab3 = st.tabs([
        "🔄 執行回測", 
        "📚 歷史記錄", 
        "📈 結果分析"
    ])
    
    with subtab1:
        show_backtest_execution()
    
    with subtab2:
        show_backtest_history()
    
    with subtab3:
        show_backtest_results_analysis()


def show_backtest_execution():
    """回測執行界面"""
    st.subheader("🔄 執行新回測")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### ⚙️ 基本設定")
        
        game_options = {
            "威力彩": GameType.POWERCOLOR,
            "大樂透": GameType.LOTTO649,
            "今彩539": GameType.DAILYCASH
        }
        game_choice = st.selectbox("🎮 選擇遊戲類型", list(game_options.keys()))
        game_type = game_options[game_choice]
        
        method_options = {
            "頻率分析": PredictionMethod.FREQUENCY_ANALYSIS,
            "模式分析": PredictionMethod.PATTERN_ANALYSIS,
            "連號分析": PredictionMethod.CONSECUTIVE_ANALYSIS,
            "隨機基準": PredictionMethod.RANDOM_BASELINE
        }
        method_choice = st.selectbox("🔍 選擇預測方法", list(method_options.keys()))
        method = method_options[method_choice]
        
    with col2:
        st.markdown("### 📅 期間設定")
        
        start_period = st.text_input(
            "開始期數", 
            value="114000030",
            help="例如: 114000030 表示民國114年第30期"
        )
        end_period = st.text_input(
            "結束期數", 
            value="114000058",
            help="例如: 114000058 表示民國114年第58期"
        )
        
        training_window = st.slider(
            "🔧 訓練窗口大小", 
            min_value=5, 
            max_value=50, 
            value=20,
            help="用於預測的歷史期數"
        )
    
    notes = st.text_area("📝 備註 (可選)", height=80)
    
    if st.button("🚀 開始回測", type="primary", use_container_width=True):
        execute_backtest_analysis(game_type, method, start_period, end_period, training_window, notes)


def execute_backtest_analysis(game_type, method, start_period, end_period, training_window, notes):
    """執行回測分析"""
    with st.spinner("🔄 執行回測中..."):
        try:
            config = BacktestConfig(
                game_type=game_type,
                method=method,
                start_period=start_period,
                end_period=end_period,
                training_window=training_window
            )
            
            backtester = BatchBacktester(config)
            results = backtester.run_backtest()
            
            if "error" in results:
                st.error(f"❌ 回測失敗: {results['error']}")
                return
            
            filepath = backtester.save_results()
            st.session_state.backtest_manager._save_to_history(results, filepath, notes)
            st.session_state.current_results = results
            
            st.success("✅ 回測執行完成！")
            display_backtest_summary(results)
            
        except Exception as e:
            st.error(f"❌ 執行過程中發生錯誤: {str(e)}")


def display_backtest_summary(results):
    """顯示回測結果摘要"""
    st.subheader("📊 回測結果摘要")
    
    stats = results['statistics']
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("總預測數", stats['total_periods'])
    with col2:
        st.metric("整體準確度", f"{stats['accuracy']:.2f}%")
    with col3:
        st.metric("總命中數", stats['total_matches'])
    with col4:
        st.metric("中獎率", f"{stats['winning_rate']:.2f}%")
    
    # 命中分佈圖
    match_dist = results.get('match_distribution', {})
    if match_dist:
        st.subheader("🎯 命中分佈")
        
        matches = []
        counts = []
        for i in range(7):
            key = f'matches_{i}'
            if key in match_dist and match_dist[key] > 0:
                matches.append(f"{i}個命中")
                counts.append(match_dist[key])
        
        if matches:
            fig = px.pie(values=counts, names=matches, title="命中分佈統計")
            st.plotly_chart(fig, use_container_width=True)


def show_backtest_history():
    """顯示回測歷史"""
    st.subheader("📚 回測歷史記錄")
    
    try:
        conn = sqlite3.connect(st.session_state.backtest_manager.history_db)
        df = pd.read_sql_query(
            "SELECT * FROM backtest_history ORDER BY created_at DESC LIMIT 50",
            conn
        )
        conn.close()
        
        if df.empty:
            st.info("📝 暫無回測歷史記錄")
            return
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("總記錄數", len(df))
        with col2:
            st.metric("平均準確率", f"{df['accuracy'].mean():.2f}%")
        with col3:
            st.metric("平均中獎率", f"{df['winning_rate'].mean():.2f}%")
        with col4:
            st.metric("最高準確率", f"{df['accuracy'].max():.2f}%")
        
        display_df = df[[
            'id', 'timestamp', 'game_type', 'method', 
            'total_periods', 'accuracy', 'winning_rate', 'notes'
        ]].copy()
        
        display_df.columns = [
            'ID', '時間戳', '遊戲類型', '預測方法',
            '總期數', '準確率(%)', '中獎率(%)', '備註'
        ]
        
        st.dataframe(display_df, use_container_width=True)
        
        selected_id = st.number_input("選擇記錄ID查看詳細結果", min_value=1, max_value=int(df['id'].max()) if not df.empty else 1)
        
        if st.button("📊 載入詳細結果"):
            detailed_result = st.session_state.backtest_manager.load_result_detail(selected_id)
            if detailed_result:
                st.session_state.current_results = detailed_result
                st.success(f"✅ 已載入記錄 #{selected_id} 的詳細結果")
                st.rerun()
        
    except Exception as e:
        st.error(f"❌ 載入歷史記錄失敗: {str(e)}")


def show_backtest_results_analysis():
    """顯示回測結果分析"""
    st.subheader("📈 結果分析")
    
    if st.session_state.current_results is None:
        st.info("📊 請先執行回測或載入歷史結果")
        return
    
    results = st.session_state.current_results
    config = results['config']
    
    st.markdown("### 📋 回測基本資訊")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.info(f"**遊戲類型**\n{config['game_type']}")
    with col2:
        st.info(f"**預測方法**\n{config['method']}")
    with col3:
        st.info(f"**測試期間**\n{config['test_period']}")
    with col4:
        st.info(f"**訓練窗口**\n{config['training_window']}期")
    
    detailed_results = results.get('results', [])
    if detailed_results:
        col1, col2 = st.columns(2)
        
        with col1:
            periods = [r['period'] for r in detailed_results]
            matches = [r['matches'] for r in detailed_results]
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=periods,
                y=matches,
                mode='lines+markers',
                name='命中數',
                line=dict(color='blue')
            ))
            
            fig.update_layout(
                title="命中數趨勢",
                xaxis_title="期數",
                yaxis_title="命中數"
            )
            
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            confidence = [r['confidence'] for r in detailed_results]
            fig = px.histogram(
                x=confidence,
                nbins=10,
                title="信心度分佈",
                labels={'x': '信心度(%)', 'y': '頻次'}
            )
            st.plotly_chart(fig, use_container_width=True)


def show_prediction_analysis_page():
    """預測方法分析頁面"""
    st.header("🔍 預測方法分析")
    
    st.markdown("### 📊 方法比較分析")
    st.info("💡 這個功能將同時測試多種預測方法，並比較它們的表現")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("⚙️ 分析設置")
        
        game_options = {
            "威力彩": GameType.POWERCOLOR,
            "大樂透": GameType.LOTTO649,
            "今彩539": GameType.DAILYCASH
        }
        game_choice = st.selectbox("彩票類型", list(game_options.keys()))
        game_type = game_options[game_choice]
        
    with col2:
        st.subheader("📅 分析時間範圍")
        
        start_period = st.text_input("開始期數", value="114000030")
        end_period = st.text_input("結束期數", value="114000058")
        training_window = st.slider("訓練窗口", 5, 50, 20)
    
    if st.button("📊 開始分析", type="primary", use_container_width=True):
        run_prediction_method_analysis(game_type, start_period, end_period, training_window)


def run_prediction_method_analysis(game_type, start_period, end_period, training_window):
    """執行預測方法分析"""
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    methods = [
        ("頻率分析", PredictionMethod.FREQUENCY_ANALYSIS),
        ("模式分析", PredictionMethod.PATTERN_ANALYSIS),
        ("連號分析", PredictionMethod.CONSECUTIVE_ANALYSIS),
        ("隨機基準", PredictionMethod.RANDOM_BASELINE)
    ]
    
    analysis_results = []
    
    for i, (method_name, method) in enumerate(methods):
        status_text.text(f"🔄 測試 {method_name}...")
        progress_bar.progress((i + 1) / len(methods))
        
        try:
            config = BacktestConfig(
                game_type=game_type,
                method=method,
                start_period=start_period,
                end_period=end_period,
                training_window=training_window
            )
            
            backtester = BatchBacktester(config)
            results = backtester.run_backtest()
            
            if "error" not in results:
                stats = results['statistics']
                analysis_results.append({
                    '方法': method_name,
                    '總期數': stats['total_periods'],
                    '準確率(%)': stats['accuracy'],
                    '中獎率(%)': stats['winning_rate'],
                    '平均命中數': stats['average_matches_per_period'],
                    '平均信心度(%)': stats['average_confidence']
                })
                
        except Exception as e:
            st.warning(f"⚠️ {method_name} 分析失敗: {str(e)}")
    
    status_text.text("✅ 分析完成！")
    
    if analysis_results:
        st.subheader("📊 方法分析結果")
        df = pd.DataFrame(analysis_results)
        st.dataframe(df, use_container_width=True)
        
        col1, col2 = st.columns(2)
        
        with col1:
            fig = px.bar(
                df, 
                x='方法', 
                y='準確率(%)',
                title="準確率比較",
                color='準確率(%)',
                color_continuous_scale='viridis'
            )
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            fig = px.bar(
                df, 
                x='方法', 
                y='中獎率(%)',
                title="中獎率比較", 
                color='中獎率(%)',
                color_continuous_scale='plasma'
            )
            st.plotly_chart(fig, use_container_width=True)
        
        best_accuracy = df.loc[df['準確率(%)'].idxmax()]
        best_winning = df.loc[df['中獎率(%)'].idxmax()]
        
        st.subheader("🏅 分析結論")
        col1, col2 = st.columns(2)
        
        with col1:
            st.success(f"**最佳準確率方法**\n{best_accuracy['方法']} ({best_accuracy['準確率(%)']:.2f}%)")
        
        with col2:
            st.success(f"**最佳中獎率方法**\n{best_winning['方法']} ({best_winning['中獎率(%)']:.2f}%)")


def show_data_update_page():
    """數據更新管理頁面"""
    st.header("🔄 數據更新管理")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📊 更新狀態")
        
        # 顯示最後更新時間
        if st.session_state.last_update_time:
            last_update_str = st.session_state.last_update_time.strftime("%Y-%m-%d %H:%M:%S")
            st.info(f"**最後更新時間**\n{last_update_str}")
        else:
            st.warning("**更新狀態**\n尚未執行過更新")
        
        # 資料庫統計
        total_records = get_total_records()
        st.metric("總記錄數", total_records)
        
        # 各遊戲記錄數
        game_stats = get_game_records_stats()
        for game, count in game_stats.items():
            st.metric(f"{game} 記錄數", count)
    
    with col2:
        st.subheader("🔄 更新操作")
        
        # 手動更新按鈕
        if st.button("🔄 立即更新全部數據", type="primary", use_container_width=True):
            with st.spinner("🔄 正在從官方網站獲取最新數據..."):
                update_result = perform_manual_update()
                if update_result:
                    st.success("✅ 數據更新成功！")
                    st.rerun()
                else:
                    st.error("❌ 數據更新失敗")
        
        # 單獨更新各遊戲
        st.markdown("### 🎮 單獨更新")
        
        games = ["威力彩", "大樂透", "今彩539"]
        for game in games:
            if st.button(f"更新 {game}", key=f"update_{game}", use_container_width=True):
                with st.spinner(f"🔄 更新 {game} 數據中..."):
                    success = update_single_game(game)
                    if success:
                        st.success(f"✅ {game} 更新成功！")
                        st.rerun()
                    else:
                        st.error(f"❌ {game} 更新失敗")
    
    # 更新日誌
    st.markdown("---")
    st.subheader("📋 更新日誌")
    show_update_logs()


def get_game_records_stats():
    """獲取各遊戲記錄統計"""
    try:
        db_path = "data/lottery_data.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        stats = {}
        game_tables = {
            "威力彩": "Powercolor",
            "大樂透": "Lotto649", 
            "今彩539": "DailyCash"
        }
        
        for game, table in game_tables.items():
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                stats[game] = count
            except:
                stats[game] = 0
        
        conn.close()
        return stats
    except:
        return {"威力彩": 0, "大樂透": 0, "今彩539": 0}


def perform_manual_update():
    """執行手動更新"""
    try:
        import sys
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from data.real_lottery_updater import RealLotteryUpdater
        from data.db_manager import DBManager
        
        db_manager = DBManager()
        updater = RealLotteryUpdater(db_manager)
        
        results = updater.update_all_lottery_results()
        
        success_count = sum(1 for r in results.values() if r.get('status') == 'success')
        if success_count > 0:
            st.session_state.last_update_time = datetime.now()
            
            # 顯示更新詳情
            st.subheader("📊 更新詳情")
            for lottery_type, result in results.items():
                if result.get('status') == 'success':
                    new_count = result.get('new_records', 0)
                    st.success(f"✅ {lottery_type}: 新增 {new_count} 筆記錄")
                else:
                    error = result.get('error', '未知錯誤')
                    st.error(f"❌ {lottery_type}: {error}")
            
            return True
        else:
            return False
            
    except Exception as e:
        st.error(f"更新失敗: {str(e)}")
        return False


def update_single_game(game_name):
    """更新單一遊戲數據"""
    try:
        # 這裡可以實現單一遊戲的更新邏輯
        # 暫時使用通用更新
        return perform_manual_update()
    except:
        return False


def show_update_logs():
    """顯示更新日誌"""
    # 這裡可以顯示更新歷史日誌
    # 暫時顯示基本信息
    if st.session_state.last_update_time:
        log_data = [{
            "時間": st.session_state.last_update_time.strftime("%Y-%m-%d %H:%M:%S"),
            "操作": "手動更新",
            "狀態": "成功",
            "詳情": "更新完成"
        }]
        
        log_df = pd.DataFrame(log_data)
        st.dataframe(log_df, use_container_width=True)
    else:
        st.info("📝 暫無更新日誌")


def show_system_management_page():
    """系統管理頁面"""
    st.header("📈 系統管理")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📊 系統統計")
        
        try:
            conn = sqlite3.connect(st.session_state.backtest_manager.history_db)
            stats_df = pd.read_sql_query(
                "SELECT COUNT(*) as total, game_type FROM backtest_history GROUP BY game_type",
                conn
            )
            conn.close()
            
            if not stats_df.empty:
                for _, row in stats_df.iterrows():
                    st.metric(f"{row['game_type']} 回測次數", row['total'])
            else:
                st.info("暫無統計數據")
        except:
            st.info("統計數據載入中...")
    
    with col2:
        st.subheader("🗂️ 檔案管理")
        
        results_dir = st.session_state.backtest_manager.results_dir
        if os.path.exists(results_dir):
            json_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
            st.metric("結果檔案數量", len(json_files))
            
            total_size = sum(
                os.path.getsize(os.path.join(results_dir, f)) 
                for f in json_files
            )
            st.metric("總佔用空間", f"{total_size / 1024 / 1024:.2f} MB")
    
    st.markdown("---")
    st.subheader("🧹 系統維護")
    
    days = st.slider("保留最近幾天的檔案", 1, 30, 7)
    
    if st.button("🗑️ 清理舊檔案", type="secondary"):
        try:
            if os.path.exists(results_dir):
                result_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
                cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)
                
                deleted_count = 0
                for filename in result_files:
                    filepath = os.path.join(results_dir, filename)
                    if os.path.getmtime(filepath) < cutoff_time:
                        os.remove(filepath)
                        deleted_count += 1
                
                st.success(f"✅ 已清理 {deleted_count} 個舊檔案")
            else:
                st.info("📁 結果目錄不存在")
        except Exception as e:
            st.error(f"❌ 清理失敗: {str(e)}")


if __name__ == "__main__":
    main()