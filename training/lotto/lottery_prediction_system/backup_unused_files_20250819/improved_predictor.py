#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改進的彩票預測器
確保期號正確性，基於期號-1的數據分析，只返回信心度最高的單組預測
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import sqlite3
import json
import sys
import os

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 導入預測分類器
try:
    from prediction_classifier import prediction_classifier, classify_prediction, get_method_category
    CLASSIFIER_AVAILABLE = True
except ImportError:
    print("警告: 預測分類器不可用")
    CLASSIFIER_AVAILABLE = False

try:
    from data.db_manager import DBManager
    from data.real_lottery_updater import RealLotteryUpdater
    from prediction import IntegratedPredictor
    from analysis import PredictionAnalyzer
    from enhanced_board_analysis import EnhancedBoardAnalyzer
    DB_AVAILABLE = True
except ImportError as e:
    print(f"警告: 部分模組導入失敗: {e}")
    DB_AVAILABLE = False

logger = logging.getLogger('improved_predictor')

class ImprovedPredictor:
    """改進的彩票預測器"""
    
    def __init__(self, db_manager=None):
        self.db_manager = db_manager or DBManager()
        self.updater = RealLotteryUpdater(self.db_manager)
        
        # 初始化預測器組件
        self.predictors = {}
        self.analyzer = None
        
        # 嘗試初始化預測組件
        try:
            if DB_AVAILABLE:
                # 只有在依賴可用時才初始化
                try:
                    self.predictors['integrated'] = IntegratedPredictor(self.db_manager)
                    logger.info("IntegratedPredictor 初始化成功")
                except Exception as e:
                    logger.warning(f"IntegratedPredictor 初始化失敗: {e}")
                
                try:
                    self.analyzer = PredictionAnalyzer(self.db_manager)
                    logger.info("PredictionAnalyzer 初始化成功")
                except Exception as e:
                    logger.warning(f"PredictionAnalyzer 初始化失敗: {e}")
                
                try:
                    self.board_analyzer = EnhancedBoardAnalyzer(self.db_manager)
                    logger.info("EnhancedBoardAnalyzer 初始化成功")
                except Exception as e:
                    logger.warning(f"EnhancedBoardAnalyzer 初始化失敗: {e}")
        except Exception as e:
            logger.warning(f"預測組件初始化過程失敗: {e}")
            # 繼續執行，不中斷整個預測器的初始化
        
        # 彩票配置
        self.lottery_config = {
            'powercolor': {
                'table_name': 'Powercolor',
                'prediction_table': 'PowercolorPredictions',
                'number_range': (1, 38),
                'special_range': (1, 8),
                'number_count': 6,
                'has_special': True
            },
            'lotto649': {
                'table_name': 'Lotto649',
                'prediction_table': 'Lotto649Predictions',
                'number_range': (1, 49),
                'special_range': (1, 49),
                'number_count': 6,
                'has_special': True
            },
            'dailycash': {
                'table_name': 'DailyCash',
                'prediction_table': 'DailyCashPredictions',
                'number_range': (1, 39),
                'special_range': None,
                'number_count': 5,
                'has_special': False
            }
        }
    
    def predict_next_draw(self, lottery_type: str) -> Optional[Dict]:
        """預測下一期開獎號碼
        
        Args:
            lottery_type: 彩票類型
            
        Returns:
            Dict: 預測結果，包含期號、號碼、信心度等
        """
        logger.info(f"開始預測 {lottery_type} 下一期號碼...")
        
        # 1. 更新最新開獎數據
        logger.info("更新最新開獎數據...")
        update_result = self.updater.fetch_latest_results(lottery_type)
        if update_result:
            self.updater.save_lottery_result(update_result)
        
        # 2. 確定下一期期號
        next_period = self.get_next_period(lottery_type)
        if not next_period:
            logger.error("無法確定下一期期號")
            return None
        
        logger.info(f"預測期號: {next_period}")
        
        # 3. 獲取分析數據（基於期號-1）
        analysis_data = self.get_analysis_data(lottery_type, next_period)
        if not analysis_data:
            logger.error("無法獲取分析數據")
            return None
        
        # 4. 執行多種預測方法
        predictions = self.generate_multiple_predictions(lottery_type, analysis_data, next_period)
        if not predictions:
            logger.error("預測生成失敗")
            return None
        
        # 5. 選擇信心度最高的預測
        best_prediction = self.select_best_prediction(predictions)
        
        # 6. 保存預測結果
        self.save_prediction(best_prediction, lottery_type)
        
        logger.info(f"預測完成，信心度: {best_prediction.get('confidence', 0):.3f}")
        return best_prediction
    
    def get_next_period(self, lottery_type: str) -> Optional[str]:
        """獲取下一期期號
        
        Args:
            lottery_type: 彩票類型
            
        Returns:
            str: 下一期期號
        """
        try:
            # 首先嘗試從updater獲取
            next_period = self.updater.get_next_expected_period(lottery_type)
            
            if next_period:
                logger.info(f"計算得出下一期期號: {next_period}")
                return next_period
            
            # 如果失敗，手動計算
            config = self.lottery_config[lottery_type]
            conn = self.db_manager.create_connection()
            cursor = conn.cursor()
            
            sql = f"SELECT Period FROM {config['table_name']} ORDER BY Period DESC LIMIT 1"
            cursor.execute(sql)
            result = cursor.fetchone()
            conn.close()
            
            if result:
                latest_period = str(result[0])
                # 解析並計算下一期
                period_num = int(latest_period[-3:])
                year_part = latest_period[:-3]
                next_num = period_num + 1
                next_period = f"{year_part}{next_num:03d}"
                
                logger.info(f"基於最新期號 {latest_period} 計算下一期: {next_period}")
                return next_period
            else:
                # 如果沒有歷史數據，返回當年第一期
                current_year = datetime.now().year
                year_suffix = str(current_year)[-3:]
                next_period = f"{year_suffix}001"
                logger.warning(f"沒有歷史數據，使用當年第一期: {next_period}")
                return next_period
                
        except Exception as e:
            logger.error(f"獲取下一期期號時發生錯誤: {str(e)}")
            return None
    
    def get_analysis_data(self, lottery_type: str, next_period: str) -> Optional[Dict]:
        """獲取用於分析的數據（基於期號-1）
        
        Args:
            lottery_type: 彩票類型
            next_period: 要預測的期號
            
        Returns:
            Dict: 分析數據
        """
        try:
            config = self.lottery_config[lottery_type]
            conn = self.db_manager.create_connection()
            
            # 獲取最近的開獎數據用於分析
            if config['has_special']:
                sql = f"""
                SELECT Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6,
                       {config['table_name']}.Second_district as SpecialNumber
                FROM {config['table_name']}
                WHERE Period < ?
                ORDER BY Period DESC
                LIMIT 50
                """
            else:
                sql = f"""
                SELECT Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5
                FROM {config['table_name']}
                WHERE Period < ?
                ORDER BY Period DESC
                LIMIT 50
                """
            
            df = pd.read_sql_query(sql, conn, params=(next_period,))
            conn.close()
            
            if df.empty:
                logger.warning("沒有足夠的歷史數據進行分析")
                return None
            
            logger.info(f"獲取到 {len(df)} 期歷史數據用於分析")
            
            # 構建分析數據
            analysis_data = {
                'historical_data': df,
                'latest_period': df.iloc[0]['Period'] if not df.empty else None,
                'data_count': len(df),
                'lottery_type': lottery_type,
                'next_period': next_period,
                'analysis_timestamp': datetime.now().isoformat()
            }
            
            return analysis_data
            
        except Exception as e:
            logger.error(f"獲取分析數據時發生錯誤: {str(e)}")
            return None
    
    def generate_multiple_predictions(self, lottery_type: str, analysis_data: Dict, next_period: str) -> List[Dict]:
        """生成多種預測方法的結果
        
        Args:
            lottery_type: 彩票類型
            analysis_data: 分析數據
            next_period: 預測期號
            
        Returns:
            List[Dict]: 多種預測結果
        """
        predictions = []
        
        try:
            # 方法1: 頻率分析預測
            freq_prediction = self.frequency_based_prediction(lottery_type, analysis_data)
            if freq_prediction:
                freq_prediction.update({
                    'method': '頻率分析',
                    'period': next_period,
                    'base_confidence': 0.6
                })
                predictions.append(freq_prediction)
            
            # 方法2: 趨勢分析預測
            trend_prediction = self.trend_based_prediction(lottery_type, analysis_data)
            if trend_prediction:
                trend_prediction.update({
                    'method': '趨勢分析',
                    'period': next_period,
                    'base_confidence': 0.65
                })
                predictions.append(trend_prediction)
            
            # 方法3: 機器學習預測（如果可用）
            if 'integrated' in self.predictors:
                try:
                    ml_prediction = self.machine_learning_prediction(lottery_type, analysis_data, next_period)
                    if ml_prediction:
                        ml_prediction.update({
                            'method': '機器學習',
                            'period': next_period,
                            'base_confidence': 0.75
                        })
                        predictions.append(ml_prediction)
                except Exception as e:
                    logger.warning(f"機器學習預測失敗: {e}")
            
            # 方法4: 綜合分析預測
            combined_prediction = self.combined_analysis_prediction(lottery_type, analysis_data)
            if combined_prediction:
                combined_prediction.update({
                    'method': '綜合分析',
                    'period': next_period,
                    'base_confidence': 0.7
                })
                predictions.append(combined_prediction)
            
            logger.info(f"生成了 {len(predictions)} 種預測方法的結果")
            return predictions
            
        except Exception as e:
            logger.error(f"生成預測時發生錯誤: {str(e)}")
            return []
    
    def frequency_based_prediction(self, lottery_type: str, analysis_data: Dict) -> Optional[Dict]:
        """基於頻率分析的預測
        
        Args:
            lottery_type: 彩票類型
            analysis_data: 分析數據
            
        Returns:
            Dict: 預測結果
        """
        try:
            df = analysis_data['historical_data']
            config = self.lottery_config[lottery_type]
            
            # 統計各號碼出現頻率
            number_columns = ['Anumber1', 'Anumber2', 'Anumber3', 'Anumber4', 'Anumber5']
            if config['number_count'] == 6:
                number_columns.append('Anumber6')
            
            all_numbers = []
            for col in number_columns:
                all_numbers.extend(df[col].dropna().tolist())
            
            # 計算頻率
            from collections import Counter
            freq_counter = Counter(all_numbers)
            
            # 選擇出現頻率適中的號碼（不是最高也不是最低）
            sorted_by_freq = sorted(freq_counter.items(), key=lambda x: x[1], reverse=True)
            
            # 選擇中等頻率的號碼
            mid_start = len(sorted_by_freq) // 4
            mid_end = len(sorted_by_freq) * 3 // 4
            candidate_numbers = [num for num, _ in sorted_by_freq[mid_start:mid_end]]
            
            # 隨機選擇所需數量的號碼
            import random
            random.seed(datetime.now().microsecond)
            
            if len(candidate_numbers) >= config['number_count']:
                selected_numbers = sorted(random.sample(candidate_numbers, config['number_count']))
            else:
                # 如果候選號碼不夠，添加其他號碼
                all_possible = list(range(config['number_range'][0], config['number_range'][1] + 1))
                remaining = [n for n in all_possible if n not in candidate_numbers]
                additional = random.sample(remaining, config['number_count'] - len(candidate_numbers))
                selected_numbers = sorted(candidate_numbers + additional)
            
            result = {
                'numbers': selected_numbers,
                'analysis_details': {
                    'frequency_analysis': dict(sorted_by_freq[:10]),  # 前10個頻率
                    'selection_method': '中等頻率選擇'
                }
            }
            
            # 特別號處理
            if config['has_special']:
                special_numbers = df['SpecialNumber'].dropna().tolist() if 'SpecialNumber' in df.columns else []
                if special_numbers:
                    special_freq = Counter(special_numbers)
                    # 選擇頻率適中的特別號
                    sorted_special = sorted(special_freq.items(), key=lambda x: x[1])
                    if sorted_special:
                        mid_idx = len(sorted_special) // 2
                        result['special_number'] = sorted_special[mid_idx][0]
                    else:
                        result['special_number'] = random.randint(*config['special_range'])
                else:
                    result['special_number'] = random.randint(*config['special_range'])
            
            return result
            
        except Exception as e:
            logger.error(f"頻率分析預測失敗: {str(e)}")
            return None
    
    def trend_based_prediction(self, lottery_type: str, analysis_data: Dict) -> Optional[Dict]:
        """基於趨勢分析的預測
        
        Args:
            lottery_type: 彩票類型
            analysis_data: 分析數據
            
        Returns:
            Dict: 預測結果
        """
        try:
            df = analysis_data['historical_data']
            config = self.lottery_config[lottery_type]
            
            # 分析最近的號碼趨勢
            recent_data = df.head(10)  # 最近10期
            
            number_columns = ['Anumber1', 'Anumber2', 'Anumber3', 'Anumber4', 'Anumber5']
            if config['number_count'] == 6:
                number_columns.append('Anumber6')
            
            # 計算每個號碼的趨勢分數
            trend_scores = {}
            for num in range(config['number_range'][0], config['number_range'][1] + 1):
                # 計算該號碼在最近期數中的出現情況
                recent_appearances = []
                for idx, row in recent_data.iterrows():
                    if num in [row[col] for col in number_columns if pd.notna(row[col])]:
                        recent_appearances.append(len(recent_data) - idx)  # 越近的期數分數越高
                
                # 計算趨勢分數（考慮出現頻率和時間權重）
                if recent_appearances:
                    trend_scores[num] = sum(recent_appearances) / len(recent_appearances)
                else:
                    trend_scores[num] = 0
            
            # 選擇趨勢分數較高的號碼
            sorted_trends = sorted(trend_scores.items(), key=lambda x: x[1], reverse=True)
            
            # 選擇前面的號碼，但加入一些隨機性
            import random
            random.seed(datetime.now().microsecond + 1)
            
            top_candidates = [num for num, _ in sorted_trends[:config['number_count'] * 2]]
            selected_numbers = sorted(random.sample(top_candidates, config['number_count']))
            
            result = {
                'numbers': selected_numbers,
                'analysis_details': {
                    'trend_scores': dict(sorted_trends[:15]),
                    'selection_method': '趨勢權重選擇'
                }
            }
            
            # 特別號處理
            if config['has_special']:
                special_trend = {}
                if 'SpecialNumber' in recent_data.columns:
                    for special_num in range(config['special_range'][0], config['special_range'][1] + 1):
                        special_appearances = []
                        for idx, row in recent_data.iterrows():
                            if pd.notna(row['SpecialNumber']) and int(row['SpecialNumber']) == special_num:
                                special_appearances.append(len(recent_data) - idx)
                        
                        if special_appearances:
                            special_trend[special_num] = sum(special_appearances) / len(special_appearances)
                        else:
                            special_trend[special_num] = 0
                    
                    if special_trend:
                        best_special = max(special_trend.items(), key=lambda x: x[1])[0]
                        result['special_number'] = best_special
                    else:
                        result['special_number'] = random.randint(*config['special_range'])
                else:
                    result['special_number'] = random.randint(*config['special_range'])
            
            return result
            
        except Exception as e:
            logger.error(f"趨勢分析預測失敗: {str(e)}")
            return None
    
    def machine_learning_prediction(self, lottery_type: str, analysis_data: Dict, next_period: str) -> Optional[Dict]:
        """機器學習預測方法
        
        Args:
            lottery_type: 彩票類型
            analysis_data: 分析數據
            next_period: 預測期號
            
        Returns:
            Dict: 預測結果
        """
        try:
            if 'integrated' not in self.predictors:
                logger.warning("機器學習預測器未初始化")
                return None
                
            # 調用集成預測器
            predictor = self.predictors['integrated']
            
            # 進行預測
            prediction_result = predictor.predict_lottery(lottery_type, candidates_count=1)
            
            if prediction_result and prediction_result.get('success') and prediction_result.get('predictions'):
                pred = prediction_result['predictions'][0]
                
                # 提取預測結果
                if lottery_type == 'dailycash':
                    numbers = [pred['A1'], pred['A2'], pred['A3'], pred['A4'], pred['A5']]
                    result = {
                        'numbers': numbers,
                        'analysis_details': {
                            'ml_confidence': pred.get('confidence', 0.5),
                            'model_version': prediction_result.get('model_version', 'unknown')
                        }
                    }
                else:
                    numbers = [pred['A1'], pred['A2'], pred['A3'], pred['A4'], pred['A5'], pred['A6']]
                    result = {
                        'numbers': numbers,
                        'special_number': pred.get('Special', 1),
                        'analysis_details': {
                            'ml_confidence': pred.get('confidence', 0.5),
                            'model_version': prediction_result.get('model_version', 'unknown')
                        }
                    }
                
                return result
            else:
                logger.warning("機器學習預測沒有返回有效結果")
                return None
                
        except Exception as e:
            logger.error(f"機器學習預測失敗: {str(e)}")
            return None
    
    def combined_analysis_prediction(self, lottery_type: str, analysis_data: Dict) -> Optional[Dict]:
        """綜合分析預測
        
        Args:
            lottery_type: 彩票類型
            analysis_data: 分析數據
            
        Returns:
            Dict: 預測結果
        """
        try:
            df = analysis_data['historical_data']
            config = self.lottery_config[lottery_type]
            
            # 綜合多種因素進行預測
            number_columns = ['Anumber1', 'Anumber2', 'Anumber3', 'Anumber4', 'Anumber5']
            if config['number_count'] == 6:
                number_columns.append('Anumber6')
            
            # 收集所有號碼
            all_numbers = []
            for col in number_columns:
                all_numbers.extend(df[col].dropna().tolist())
            
            # 計算綜合分數
            comprehensive_scores = {}
            
            for num in range(config['number_range'][0], config['number_range'][1] + 1):
                score = 0
                
                # 因素1: 頻率分數（出現頻率適中的號碼得分較高）
                freq = all_numbers.count(num)
                avg_freq = len(all_numbers) / (config['number_range'][1] - config['number_range'][0] + 1)
                freq_score = 1 - abs(freq - avg_freq) / avg_freq
                score += freq_score * 0.3
                
                # 因素2: 最近出現情況（適度的冷熱平衡）
                recent_appearances = 0
                for idx, row in df.head(5).iterrows():  # 最近5期
                    if num in [row[col] for col in number_columns if pd.notna(row[col])]:
                        recent_appearances += 1
                
                recent_score = 0.5 if recent_appearances == 0 else min(recent_appearances / 5, 1)
                score += recent_score * 0.3
                
                # 因素3: 數字特性（考慮奇偶、大小平衡）
                if num % 2 == 0:  # 偶數
                    parity_score = 0.5
                else:  # 奇數
                    parity_score = 0.5
                score += parity_score * 0.2
                
                # 因素4: 隨機因素
                import random
                random.seed(num + datetime.now().microsecond)
                random_score = random.random()
                score += random_score * 0.2
                
                comprehensive_scores[num] = score
            
            # 選擇分數最高的號碼
            sorted_scores = sorted(comprehensive_scores.items(), key=lambda x: x[1], reverse=True)
            selected_numbers = sorted([num for num, _ in sorted_scores[:config['number_count']]])
            
            result = {
                'numbers': selected_numbers,
                'analysis_details': {
                    'comprehensive_scores': dict(sorted_scores[:20]),
                    'selection_method': '綜合分析加權'
                }
            }
            
            # 特別號處理
            if config['has_special']:
                special_scores = {}
                special_numbers = df['SpecialNumber'].dropna().tolist() if 'SpecialNumber' in df.columns else []
                
                for special_num in range(config['special_range'][0], config['special_range'][1] + 1):
                    special_score = 0
                    
                    # 頻率因素
                    special_freq = special_numbers.count(special_num)
                    if special_numbers:
                        avg_special_freq = len(special_numbers) / (config['special_range'][1] - config['special_range'][0] + 1)
                        special_freq_score = 1 - abs(special_freq - avg_special_freq) / max(avg_special_freq, 1)
                    else:
                        special_freq_score = 0.5
                    
                    special_score += special_freq_score * 0.5
                    
                    # 隨機因素
                    import random
                    random.seed(special_num + datetime.now().microsecond + 100)
                    special_score += random.random() * 0.5
                    
                    special_scores[special_num] = special_score
                
                best_special = max(special_scores.items(), key=lambda x: x[1])[0]
                result['special_number'] = best_special
            
            return result
            
        except Exception as e:
            logger.error(f"綜合分析預測失敗: {str(e)}")
            return None
    
    def select_best_prediction(self, predictions: List[Dict]) -> Dict:
        """從多個預測中選擇信心度最高的
        
        Args:
            predictions: 多個預測結果
            
        Returns:
            Dict: 最佳預測結果
        """
        if not predictions:
            logger.error("沒有可用的預測結果")
            return None
        
        # 計算每個預測的總信心度
        for prediction in predictions:
            base_confidence = prediction.get('base_confidence', 0.5)
            
            # 根據分析細節調整信心度
            analysis_details = prediction.get('analysis_details', {})
            
            # 調整因子
            adjustment = 0
            
            # 機器學習預測的額外信心度
            if prediction.get('method') == '機器學習':
                ml_confidence = analysis_details.get('ml_confidence', 0.5)
                adjustment += (ml_confidence - 0.5) * 0.2
            
            # 數據完整性調整
            if len(analysis_details) > 2:
                adjustment += 0.1  # 有豐富分析細節
            
            # 隨機小幅調整以避免完全相同的信心度
            import random
            random.seed(hash(str(prediction)) % 2147483647)
            adjustment += (random.random() - 0.5) * 0.05
            
            final_confidence = max(0.1, min(0.95, base_confidence + adjustment))
            prediction['confidence'] = final_confidence
        
        # 選擇信心度最高的預測
        best_prediction = max(predictions, key=lambda x: x.get('confidence', 0))
        
        logger.info(f"選擇了 {best_prediction.get('method')} 方法的預測，信心度: {best_prediction.get('confidence', 0):.3f}")
        
        # 添加預測摘要
        best_prediction['prediction_summary'] = {
            'total_methods': len(predictions),
            'selected_method': best_prediction.get('method'),
            'selection_reason': '信心度最高',
            'all_methods': [p.get('method') for p in predictions]
        }
        
        return best_prediction
    
    def save_prediction(self, prediction: Dict, lottery_type: str) -> bool:
        """保存預測結果到數據庫
        
        Args:
            prediction: 預測結果
            lottery_type: 彩票類型
            
        Returns:
            bool: 是否保存成功
        """
        try:
            config = self.lottery_config[lottery_type]
            conn = self.db_manager.create_connection()
            cursor = conn.cursor()
            
            # 構建插入語句
            prediction_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            period = prediction['period']
            numbers = prediction['numbers']
            confidence = prediction.get('confidence', 0.5)
            method = prediction.get('method', 'unknown')
            
            # 添加預測分類信息
            method_classification = None
            if CLASSIFIER_AVAILABLE:
                method_info = classify_prediction(method)
                if method_info:
                    method_classification = {
                        'method_type': method_info.method_type.value,
                        'category': method_info.category.value,
                        'display_name': method_info.display_name,
                        'description': method_info.description,
                        'complexity': method_info.complexity,
                        'processing_time': method_info.processing_time
                    }
            
            # 準備詳細信息
            details = {
                'analysis_details': prediction.get('analysis_details', {}),
                'prediction_summary': prediction.get('prediction_summary', {}),
                'method_classification': method_classification,
                'timestamp': prediction_date
            }
            details_json = json.dumps(details, ensure_ascii=False)
            
            if lottery_type == 'powercolor':
                sql = """
                INSERT INTO PowercolorPredictions 
                (Period, PredictionDate, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6, 
                 PredS, Confidence, PredictionMethod, PredictionDetails)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                values = [
                    period, prediction_date, *numbers, 
                    prediction.get('special_number', 1),
                    confidence, method, details_json
                ]
                
            elif lottery_type == 'lotto649':
                sql = """
                INSERT INTO Lotto649Predictions 
                (Period, PredictionDate, PredA1, PredA2, PredA3, PredA4, PredA5, PredA6,
                 PredSpecial, Confidence, PredictionMethod, PredictionDetails)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                values = [
                    period, prediction_date, *numbers,
                    prediction.get('special_number', 1),
                    confidence, method, details_json
                ]
                
            elif lottery_type == 'dailycash':
                sql = """
                INSERT INTO DailyCashPredictions 
                (Period, PredictionDate, PredA1, PredA2, PredA3, PredA4, PredA5,
                 Confidence, PredictionMethod, PredictionDetails)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                values = [
                    period, prediction_date, *numbers,
                    confidence, method, details_json
                ]
            
            cursor.execute(sql, values)
            conn.commit()
            conn.close()
            
            logger.info(f"預測結果已保存: {lottery_type} 第 {period} 期")
            return True
            
        except Exception as e:
            logger.error(f"保存預測結果時發生錯誤: {str(e)}")
            return False

if __name__ == "__main__":
    # 測試代碼
    predictor = ImprovedPredictor()
    
    # 測試威力彩預測
    result = predictor.predict_next_draw('powercolor')
    if result:
        print(f"威力彩預測結果: {result}")
    else:
        print("威力彩預測失敗")