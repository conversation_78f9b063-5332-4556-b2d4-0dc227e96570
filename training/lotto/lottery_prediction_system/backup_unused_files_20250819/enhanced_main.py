#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增強彩票預測系統 - 主程序
基於成功率評估的彩票預測系統，替代信心指數，減少預測選項
"""

import os
import sys
import argparse
import logging
from datetime import datetime
import pandas as pd
import numpy as np

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import DBManager
from data.feature_engineer import FeatureEngineer
from prediction.enhanced_lottery_predictor import EnhancedLotteryPredictor
from display.prediction_display import PredictionDisplay
from analysis.result_analyzer import ResultAnalyzer
from config_manager import ConfigManager

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/enhanced_main.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def main():
    """
    主程序入口
    """
    parser = argparse.ArgumentParser(description='增強彩票預測系統')
    parser.add_argument('--lottery-type', '-t', default='powercolor', 
                       choices=['powercolor', 'lotto649', 'dailycash'],
                       help='彩票類型')
    parser.add_argument('--action', '-a', default='predict',
                       choices=['predict', 'analyze', 'performance', 'history'],
                       help='執行動作')
    parser.add_argument('--methods', '-m', nargs='+', 
                       choices=['ml', 'board_path', 'integrated', 'ensemble'],
                       help='預測方法（留空自動選擇最佳方法）')
    parser.add_argument('--period', '-p', help='期號')
    parser.add_argument('--days', '-d', type=int, default=30, help='分析天數')
    parser.add_argument('--db-path', default='data/lottery.db', help='數據庫路徑')
    
    args = parser.parse_args()
    
    try:
        # 初始化組件
        print("=== 增強彩票預測系統 ===")
        print(f"彩票類型：{get_lottery_name(args.lottery_type)}")
        print(f"執行動作：{args.action}")
        print(f"時間：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        # 初始化配置管理器和數據庫管理器
        config_manager = ConfigManager()
        db_manager = DBManager(config_manager=config_manager)
        
        # 初始化增強預測器
        predictor = EnhancedLotteryPredictor(db_path=args.db_path)
        
        # 初始化顯示器
        display = PredictionDisplay()
        
        # 根據動作執行相應功能
        if args.action == 'predict':
            execute_prediction(db_manager, predictor, display, args)
        elif args.action == 'analyze':
            execute_analysis(predictor, args)
        elif args.action == 'performance':
            execute_performance_analysis(predictor, args)
        elif args.action == 'history':
            execute_history_view(db_manager, args)
        
    except Exception as e:
        logger.error(f"程序執行錯誤：{str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

def execute_prediction(db_manager, predictor, display, args):
    """
    執行預測
    
    Args:
        db_manager: 數據庫管理器
        predictor: 增強預測器
        display: 顯示器
        args: 命令行參數
    """
    print("\n=== 開始預測 ===")
    
    try:
        # 載入歷史數據
        print("載入歷史數據...")
        df = db_manager.load_lottery_data(lottery_type=args.lottery_type)
        
        if df.empty:
            print(f"錯誤：沒有找到{get_lottery_name(args.lottery_type)}的歷史數據")
            return
        
        print(f"載入了 {len(df)} 筆歷史數據")
        
        # 特徵工程
        print("進行特徵工程...")
        feature_engineer = FeatureEngineer()
        features = feature_engineer.create_features(df, args.lottery_type)
        
        if features is None or len(features) == 0:
            print("錯誤：特徵工程失敗")
            return
        
        print(f"生成了 {features.shape[1]} 個特徵")
        
        # 顯示當前最佳預測方法
        best_method = predictor.get_best_prediction_method(args.lottery_type, args.days)
        print(f"\n當前最佳預測方法：{best_method}")
        
        # 顯示各方法成功率
        success_rates = predictor.get_method_success_rates(args.lottery_type, args.days)
        if success_rates:
            print("\n各方法成功率（過去{}天）：".format(args.days))
            for method, rate in success_rates.items():
                print(f"  {method}: {rate:.2%}")
        
        # 執行預測
        print("\n執行預測...")
        result = predictor.predict_with_success_rate(
            df=df,
            features=features[-1:],  # 使用最新的特徵
            lottery_type=args.lottery_type,
            methods=args.methods,
            period=args.period
        )
        
        if not result:
            print("預測失敗")
            return
        
        # 顯示預測結果
        print("\n=== 預測結果 ===")
        display_prediction_result(result, args.lottery_type)
        
        # 保存預測結果（可選）
        save_prediction = input("\n是否保存預測結果？(y/n): ").lower().strip()
        if save_prediction == 'y':
            # 這裡可以添加保存邏輯
            print("預測結果已保存")
        
    except Exception as e:
        logger.error(f"預測執行錯誤：{str(e)}")
        print(f"預測失敗：{str(e)}")

def execute_analysis(predictor, args):
    """
    執行分析
    
    Args:
        predictor: 增強預測器
        args: 命令行參數
    """
    print("\n=== 預測方法分析 ===")
    
    try:
        # 獲取成功率分析
        success_rates = predictor.get_method_success_rates(args.lottery_type, args.days)
        
        if not success_rates:
            print("沒有足夠的歷史數據進行分析")
            return
        
        print(f"\n{get_lottery_name(args.lottery_type)} - 過去{args.days}天成功率分析：")
        print("-" * 50)
        
        # 排序並顯示
        sorted_rates = sorted(success_rates.items(), key=lambda x: x[1], reverse=True)
        
        for i, (method, rate) in enumerate(sorted_rates, 1):
            status = "★" if i == 1 else " "
            print(f"{status} {i}. {method:15} : {rate:6.2%}")
        
        # 顯示推薦
        best_method = sorted_rates[0][0]
        best_rate = sorted_rates[0][1]
        
        print(f"\n推薦使用方法：{best_method}（成功率：{best_rate:.2%}）")
        
        if best_rate < 0.1:  # 10%
            print("⚠️  注意：所有方法的成功率都較低，建議謹慎投注")
        elif best_rate > 0.3:  # 30%
            print("✅ 當前最佳方法表現良好")
        
    except Exception as e:
        logger.error(f"分析執行錯誤：{str(e)}")
        print(f"分析失敗：{str(e)}")

def execute_performance_analysis(predictor, args):
    """
    執行性能分析
    
    Args:
        predictor: 增強預測器
        args: 命令行參數
    """
    print("\n=== 系統性能分析 ===")
    
    try:
        # 獲取詳細性能分析
        performance = predictor.analyze_prediction_performance(args.lottery_type, args.days)
        
        if not performance:
            print("沒有足夠的數據進行性能分析")
            return
        
        print(f"\n{get_lottery_name(args.lottery_type)} - 系統性能報告")
        print(f"分析期間：{performance.get('analysis_period', 'N/A')}")
        print(f"分析時間：{performance.get('analysis_date', 'N/A')}")
        print("-" * 60)
        
        # 顯示成功率
        success_rates = performance.get('success_rates', {})
        if success_rates:
            print("\n📊 各方法成功率：")
            for method, rate in success_rates.items():
                print(f"   {method:15} : {rate:6.2%}")
        
        # 顯示方法比較
        comparison = performance.get('method_comparison', {})
        if comparison:
            print("\n🏆 方法排名：")
            for i, (method, data) in enumerate(comparison.items(), 1):
                if isinstance(data, dict):
                    rate = data.get('success_rate', 0)
                    count = data.get('prediction_count', 0)
                    print(f"   {i}. {method:15} : {rate:6.2%} ({count}次預測)")
        
        # 顯示趨勢
        trends = performance.get('trends', {})
        if trends:
            print("\n📈 成功率趨勢：")
            for method, trend_data in trends.items():
                if isinstance(trend_data, dict):
                    trend = trend_data.get('trend', 'stable')
                    recent_rate = trend_data.get('recent_success_rate', 0)
                    print(f"   {method:15} : {trend:8} (近期: {recent_rate:6.2%})")
        
    except Exception as e:
        logger.error(f"性能分析執行錯誤：{str(e)}")
        print(f"性能分析失敗：{str(e)}")

def execute_history_view(db_manager, args):
    """
    查看歷史記錄
    
    Args:
        db_manager: 數據庫管理器
        args: 命令行參數
    """
    print("\n=== 歷史預測記錄 ===")
    
    try:
        # 載入預測記錄
        records = db_manager.load_prediction_records(
            lottery_type=args.lottery_type,
            limit=20
        )
        
        if not records:
            print("沒有找到歷史預測記錄")
            return
        
        print(f"\n{get_lottery_name(args.lottery_type)} - 最近20次預測記錄：")
        print("-" * 80)
        print(f"{'期號':10} {'預測方法':12} {'成功率':8} {'預測號碼':20} {'結果':6}")
        print("-" * 80)
        
        for record in records:
            period = record.get('period', 'N/A')
            method = record.get('method', 'N/A')
            success_rate = record.get('success_rate', 0)
            predicted = record.get('predicted_numbers', [])
            is_verified = record.get('is_verified', False)
            
            # 格式化預測號碼
            if isinstance(predicted, list):
                predicted_str = ','.join(map(str, predicted[:6]))  # 只顯示前6個
            else:
                predicted_str = str(predicted)
            
            result_str = "已驗證" if is_verified else "待驗證"
            
            print(f"{period:10} {method:12} {success_rate:6.2%} {predicted_str:20} {result_str:6}")
        
    except Exception as e:
        logger.error(f"歷史記錄查看錯誤：{str(e)}")
        print(f"查看歷史記錄失敗：{str(e)}")

def display_prediction_result(result, lottery_type):
    """
    顯示預測結果
    
    Args:
        result: 預測結果
        lottery_type: 彩票類型
    """
    try:
        print(f"彩票類型：{get_lottery_name(lottery_type)}")
        print(f"預測方法：{result.get('prediction_method', 'N/A')}")
        print(f"成功率：{result.get('success_rate', 0):.2%}")
        print(f"預測時間：{result.get('prediction_time', 'N/A')}")
        
        if lottery_type == 'powercolor':
            first_area = result.get('第一區', [])
            second_area = result.get('第二區', 0)
            print(f"\n第一區號碼：{' '.join(map(str, first_area))}")
            print(f"第二區號碼：{second_area}")
        elif lottery_type == 'lotto649':
            main_numbers = result.get('一般號碼', [])
            special_number = result.get('特別號', 0)
            print(f"\n一般號碼：{' '.join(map(str, main_numbers))}")
            print(f"特別號：{special_number}")
        elif lottery_type == 'dailycash':
            numbers = result.get('號碼', [])
            print(f"\n號碼：{' '.join(map(str, numbers))}")
        
        # 顯示說明
        explanation = result.get('explanation', [])
        if explanation:
            print("\n預測說明：")
            for i, exp in enumerate(explanation, 1):
                print(f"  {i}. {exp}")
        
        # 顯示性能報告
        performance = result.get('performance_report', {})
        if performance:
            print("\n系統性能：")
            for key, value in performance.items():
                if isinstance(value, float):
                    print(f"  {key}: {value:.2%}")
                else:
                    print(f"  {key}: {value}")
        
    except Exception as e:
        logger.error(f"顯示預測結果錯誤：{str(e)}")
        print(f"顯示結果失敗：{str(e)}")

def get_lottery_name(lottery_type):
    """
    獲取彩票類型中文名稱
    
    Args:
        lottery_type: 彩票類型代碼
        
    Returns:
        中文名稱
    """
    names = {
        'powercolor': '威力彩',
        'lotto649': '大樂透',
        'dailycash': '今彩539'
    }
    return names.get(lottery_type, lottery_type)

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)