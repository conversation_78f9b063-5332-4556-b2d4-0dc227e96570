#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能預測方法推薦引擎
基於歷史表現、用戶偏好和情境條件推薦最佳預測方法
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json
from dataclasses import dataclass

logger = logging.getLogger('method_recommendation_engine')

@dataclass
class RecommendationContext:
    """推薦上下文"""
    lottery_type: str
    user_preference: str = "balanced"  # conservative, balanced, aggressive
    time_constraint: str = "medium"    # fast, medium, slow
    accuracy_priority: float = 0.7     # 0-1, 準確度重要性
    speed_priority: float = 0.3        # 0-1, 速度重要性
    historical_days: int = 30
    min_predictions: int = 5           # 最少預測次數才納入推薦

class MethodRecommendationEngine:
    """預測方法推薦引擎"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        
        # 導入分類器和追蹤器
        try:
            from prediction_classifier import prediction_classifier
            from prediction_accuracy_tracker import PredictionAccuracyTracker
            self.classifier = prediction_classifier
            self.accuracy_tracker = PredictionAccuracyTracker(db_manager)
            self.components_available = True
        except ImportError:
            logger.warning("預測組件不完整，將使用基礎推薦")
            self.components_available = False
    
    def recommend_methods(self, context: RecommendationContext) -> Dict:
        """推薦預測方法"""
        try:
            # 1. 獲取歷史表現數據
            performance_data = self._get_method_performance(context)
            
            # 2. 計算推薦分數
            recommendations = self._calculate_recommendation_scores(performance_data, context)
            
            # 3. 生成推薦報告
            report = self._generate_recommendation_report(recommendations, context)
            
            return report
            
        except Exception as e:
            logger.error(f"方法推薦失敗: {str(e)}")
            return self._get_fallback_recommendations(context)
    
    def _get_method_performance(self, context: RecommendationContext) -> Dict:
        """獲取方法歷史表現數據"""
        table_map = {
            'powercolor': 'PowercolorPredictions',
            'lotto649': 'Lotto649Predictions',
            'dailycash': 'DailyCashPredictions'
        }
        
        table_name = table_map.get(context.lottery_type)
        conn = self.db_manager.create_connection()
        
        # 獲取最近的預測數據
        cutoff_date = (datetime.now() - timedelta(days=context.historical_days)).strftime('%Y-%m-%d')
        
        sql = f"""
        SELECT 
            PredictionMethod,
            AVG(CASE WHEN MatchCount IS NOT NULL THEN MatchCount ELSE 0 END) as avg_match,
            AVG(CASE WHEN AccuracyScore IS NOT NULL THEN AccuracyScore ELSE 0 END) as avg_accuracy,
            AVG(Confidence) as avg_confidence,
            COUNT(*) as total_predictions,
            COUNT(CASE WHEN MatchCount IS NOT NULL THEN 1 END) as verified_predictions,
            MAX(PredictionDate) as last_used,
            PredictionDetails
        FROM {table_name}
        WHERE PredictionDate >= ?
        GROUP BY PredictionMethod
        HAVING COUNT(*) >= ?
        ORDER BY avg_accuracy DESC
        """
        
        df = pd.read_sql_query(sql, conn, params=(cutoff_date, context.min_predictions))
        conn.close()
        
        # 解析方法分類信息
        performance_data = {}
        for _, row in df.iterrows():
            method = row['PredictionMethod']
            
            # 嘗試獲取分類信息
            method_info = None
            if self.components_available:
                method_info = self.classifier.classify_prediction_method(method)
            
            performance_data[method] = {
                'avg_match': row['avg_match'],
                'avg_accuracy': row['avg_accuracy'],
                'avg_confidence': row['avg_confidence'],
                'total_predictions': row['total_predictions'],
                'verified_predictions': row['verified_predictions'],
                'last_used': row['last_used'],
                'verification_rate': row['verified_predictions'] / row['total_predictions'] if row['total_predictions'] > 0 else 0,
                'method_info': method_info
            }
        
        return performance_data
    
    def _calculate_recommendation_scores(self, performance_data: Dict, context: RecommendationContext) -> List[Dict]:
        """計算推薦分數"""
        recommendations = []
        
        for method, data in performance_data.items():
            score = 0.0
            factors = {}
            
            # 1. 準確度因子 (40%)
            accuracy_factor = (data['avg_accuracy'] / 100.0) * 0.4
            factors['accuracy'] = accuracy_factor
            
            # 2. 信心度因子 (20%)
            confidence_factor = data['avg_confidence'] * 0.2
            factors['confidence'] = confidence_factor
            
            # 3. 驗證率因子 (15%)
            verification_factor = data['verification_rate'] * 0.15
            factors['verification'] = verification_factor
            
            # 4. 使用頻率因子 (10%)
            usage_factor = min(data['total_predictions'] / 20.0, 1.0) * 0.1
            factors['usage'] = usage_factor
            
            # 5. 時效性因子 (10%)
            try:
                last_used = datetime.strptime(data['last_used'], '%Y-%m-%d %H:%M:%S')
                days_since = (datetime.now() - last_used).days
                recency_factor = max(0, (30 - days_since) / 30.0) * 0.1
            except:
                recency_factor = 0.05
            factors['recency'] = recency_factor
            
            # 6. 方法特性匹配 (5%)
            method_match_factor = self._calculate_method_match(data['method_info'], context) * 0.05
            factors['method_match'] = method_match_factor
            
            # 計算總分
            total_score = sum(factors.values())
            
            # 根據用戶偏好調整
            if context.user_preference == "conservative":
                # 偏好高信心度和驗證率
                total_score *= (1 + data['avg_confidence'] * 0.2 + data['verification_rate'] * 0.3)
            elif context.user_preference == "aggressive":
                # 偏好高準確度
                total_score *= (1 + (data['avg_accuracy'] / 100.0) * 0.5)
            
            recommendation = {
                'method': method,
                'score': total_score,
                'factors': factors,
                'performance_data': data,
                'recommendation_reason': self._generate_recommendation_reason(method, data, factors)
            }
            
            recommendations.append(recommendation)
        
        # 按分數排序
        recommendations.sort(key=lambda x: x['score'], reverse=True)
        
        return recommendations
    
    def _calculate_method_match(self, method_info, context: RecommendationContext) -> float:
        """計算方法與上下文的匹配度"""
        if not method_info:
            return 0.5  # 中性分數
        
        match_score = 0.5
        
        # 時間約束匹配
        if context.time_constraint == "fast" and method_info.processing_time == "fast":
            match_score += 0.3
        elif context.time_constraint == "medium" and method_info.processing_time == "medium":
            match_score += 0.2
        elif context.time_constraint == "slow" and method_info.processing_time in ["medium", "slow"]:
            match_score += 0.1
        
        # 複雜度匹配
        if context.accuracy_priority > 0.7 and method_info.complexity == "complex":
            match_score += 0.2
        elif context.speed_priority > 0.7 and method_info.complexity == "simple":
            match_score += 0.2
        
        return min(match_score, 1.0)
    
    def _generate_recommendation_reason(self, method: str, data: Dict, factors: Dict) -> str:
        """生成推薦理由"""
        reasons = []
        
        if data['avg_accuracy'] > 70:
            reasons.append(f"高準確度 ({data['avg_accuracy']:.1f}%)")
        
        if data['avg_confidence'] > 0.7:
            reasons.append(f"高信心度 ({data['avg_confidence']:.2f})")
        
        if data['verification_rate'] > 0.8:
            reasons.append(f"高驗證率 ({data['verification_rate']:.1%})")
        
        if data['total_predictions'] > 15:
            reasons.append("豐富使用經驗")
        
        if not reasons:
            reasons.append("平衡表現")
        
        return " | ".join(reasons)
    
    def _generate_recommendation_report(self, recommendations: List[Dict], context: RecommendationContext) -> Dict:
        """生成推薦報告"""
        report = {
            'context': {
                'lottery_type': context.lottery_type,
                'user_preference': context.user_preference,
                'time_constraint': context.time_constraint,
                'accuracy_priority': context.accuracy_priority,
                'speed_priority': context.speed_priority,
                'analysis_period': f"{context.historical_days}天"
            },
            'recommendations': recommendations[:5],  # 前5個推薦
            'summary': {
                'total_methods_analyzed': len(recommendations),
                'best_method': recommendations[0]['method'] if recommendations else None,
                'best_score': recommendations[0]['score'] if recommendations else 0,
                'avg_accuracy': np.mean([r['performance_data']['avg_accuracy'] for r in recommendations]) if recommendations else 0
            },
            'generated_at': datetime.now().isoformat()
        }
        
        return report
    
    def _get_fallback_recommendations(self, context: RecommendationContext) -> Dict:
        """獲取備用推薦（當沒有足夠數據時）"""
        fallback_methods = {
            'fast': ['頻率分析', '趨勢分析'],
            'medium': ['綜合分析', '板路分析'],
            'slow': ['機器學習', '增強分析']
        }
        
        methods = fallback_methods.get(context.time_constraint, ['綜合分析'])
        
        recommendations = []
        for i, method in enumerate(methods):
            recommendations.append({
                'method': method,
                'score': 0.7 - i * 0.1,
                'factors': {'fallback': True},
                'performance_data': {'note': '基於方法特性的備用推薦'},
                'recommendation_reason': '系統預設推薦'
            })
        
        return {
            'context': context.__dict__,
            'recommendations': recommendations,
            'summary': {
                'total_methods_analyzed': 0,
                'best_method': methods[0],
                'note': '資料不足，使用備用推薦'
            },
            'generated_at': datetime.now().isoformat()
        }

# 快捷函數
def get_method_recommendation(lottery_type: str, user_preference: str = "balanced", 
                            time_constraint: str = "medium") -> Dict:
    """快捷推薦函數"""
    try:
        from data.db_manager import DBManager
        db_manager = DBManager()
        engine = MethodRecommendationEngine(db_manager)
        
        context = RecommendationContext(
            lottery_type=lottery_type,
            user_preference=user_preference,
            time_constraint=time_constraint
        )
        
        return engine.recommend_methods(context)
    except Exception as e:
        logger.error(f"推薦失敗: {e}")
        return {'error': str(e)}

if __name__ == "__main__":
    # 測試推薦引擎
    result = get_method_recommendation('powercolor', 'balanced', 'medium')
    print("方法推薦結果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))