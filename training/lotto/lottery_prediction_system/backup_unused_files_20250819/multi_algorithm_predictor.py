#!/usr/bin/env python3
"""
多算法預測器 - Phase 2 整合多種預測方法
結合板路分析、特徵工程、統計學習等多種方法提升預測效果
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter
import logging
from datetime import datetime
from .board_path_analyzer import BoardPathAnalyzer
from .optimized_board_path_analyzer import OptimizedBoardPathAnalyzer
from .enhanced_feature_analyzer import EnhancedFeatureAnalyzer
from .feature_enhanced_predictor import FeatureEnhancedPredictor
from sklearn.ensemble import RandomForestClassifier, VotingClassifier
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import cross_val_score
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger('multi_algorithm_predictor')

class MultiAlgorithmPredictor:
    """
    多算法預測器
    
    主要功能：
    1. 整合多種預測算法
    2. 智能權重分配
    3. 投票機制決策
    4. 動態算法選擇
    """
    
    def __init__(self, lottery_type='powercolor', db_manager=None):
        """
        初始化多算法預測器
        
        Args:
            lottery_type: 彩票類型
            db_manager: 數據庫管理器
        """
        self.lottery_type = lottery_type
        self.db_manager = db_manager
        
        # 彩票配置
        self.config = {
            'powercolor': {'main_numbers': 38, 'main_count': 6, 'special_numbers': 8, 'has_special': True},
            'lotto649': {'main_numbers': 49, 'main_count': 6, 'special_numbers': 10, 'has_special': True},
            'dailycash': {'main_numbers': 39, 'main_count': 5, 'special_numbers': 0, 'has_special': False}
        }
        
        self.main_numbers = self.config[lottery_type]['main_numbers']
        self.main_count = self.config[lottery_type]['main_count']
        self.special_numbers = self.config[lottery_type]['special_numbers']
        self.has_special = self.config[lottery_type]['has_special']
        
        # 初始化預測器
        self.predictors = {
            'board_path': BoardPathAnalyzer(lottery_type, db_manager),
            'optimized_board_path': OptimizedBoardPathAnalyzer(lottery_type, db_manager, 2),
            'feature_enhanced': FeatureEnhancedPredictor(lottery_type, db_manager, 2),
            'statistical_ml': None,  # 將在後續初始化
            'frequency_based': None,  # 將在後續初始化
            'pattern_based': None    # 將在後續初始化
        }
        
        # 算法權重 (會動態調整)
        self.algorithm_weights = {
            'board_path': 0.15,
            'optimized_board_path': 0.25,
            'feature_enhanced': 0.30,
            'statistical_ml': 0.15,
            'frequency_based': 0.10,
            'pattern_based': 0.05
        }
        
        # 預測配置
        self.prediction_config = {
            'ensemble_size': 10,        # 集成預測數量
            'voting_threshold': 0.6,    # 投票閾值
            'confidence_threshold': 0.7, # 信心閾值
            'diversity_weight': 0.3     # 多樣性權重
        }
        
        # 性能追蹤
        self.performance_history = {
            'board_path': [],
            'optimized_board_path': [],
            'feature_enhanced': [],
            'statistical_ml': [],
            'frequency_based': [],
            'pattern_based': []
        }
        
        logger.info(f"多算法預測器初始化完成 - 彩票類型: {lottery_type}")
    
    def _init_statistical_ml_predictor(self, df):
        """初始化統計機器學習預測器"""
        logger.info("初始化統計機器學習預測器...")
        
        # 準備訓練數據
        X, y = self._prepare_ml_data(df)
        
        if len(X) < 10:
            logger.warning("訓練數據不足，跳過機器學習預測器初始化")
            return None
        
        # 創建集成分類器
        rf_classifier = RandomForestClassifier(n_estimators=100, random_state=42)
        svm_classifier = SVC(probability=True, random_state=42)
        mlp_classifier = MLPClassifier(hidden_layer_sizes=(100, 50), random_state=42, max_iter=500)
        
        # 投票分類器
        voting_classifier = VotingClassifier(
            estimators=[
                ('rf', rf_classifier),
                ('svm', svm_classifier),
                ('mlp', mlp_classifier)
            ],
            voting='soft'
        )
        
        try:
            # 訓練模型
            voting_classifier.fit(X, y)
            logger.info("統計機器學習預測器初始化完成")
            return voting_classifier
        except Exception as e:
            logger.warning(f"統計機器學習預測器初始化失敗: {e}")
            return None
    
    def _init_frequency_based_predictor(self, df):
        """初始化基於頻率的預測器"""
        logger.info("初始化基於頻率的預測器...")
        
        # 計算號碼頻率
        main_cols = [f'Anumber{i}' for i in range(1, self.main_count + 1)]
        number_frequency = defaultdict(int)
        
        for _, row in df.iterrows():
            for col in main_cols:
                number_frequency[row[col]] += 1
        
        # 計算加權頻率 (近期權重更高)
        weighted_frequency = defaultdict(float)
        total_periods = len(df)
        
        for idx, row in df.iterrows():
            # 線性衰減權重
            weight = (idx + 1) / total_periods
            for col in main_cols:
                weighted_frequency[row[col]] += weight
        
        predictor = {
            'number_frequency': dict(number_frequency),
            'weighted_frequency': dict(weighted_frequency),
            'total_periods': total_periods
        }
        
        logger.info("基於頻率的預測器初始化完成")
        return predictor
    
    def _init_pattern_based_predictor(self, df):
        """初始化基於模式的預測器"""
        logger.info("初始化基於模式的預測器...")
        
        main_cols = [f'Anumber{i}' for i in range(1, self.main_count + 1)]
        
        # 分析奇偶模式
        odd_even_patterns = []
        for _, row in df.iterrows():
            numbers = [row[col] for col in main_cols]
            odd_count = sum(1 for num in numbers if num % 2 == 1)
            odd_even_patterns.append(odd_count)
        
        # 分析大小模式
        mid_point = self.main_numbers // 2
        size_patterns = []
        for _, row in df.iterrows():
            numbers = [row[col] for col in main_cols]
            small_count = sum(1 for num in numbers if num <= mid_point)
            size_patterns.append(small_count)
        
        # 分析連續模式
        consecutive_patterns = []
        for _, row in df.iterrows():
            numbers = sorted([row[col] for col in main_cols])
            consecutive_count = 0
            for i in range(1, len(numbers)):
                if numbers[i] == numbers[i-1] + 1:
                    consecutive_count += 1
            consecutive_patterns.append(consecutive_count)
        
        predictor = {
            'odd_even_patterns': odd_even_patterns,
            'size_patterns': size_patterns,
            'consecutive_patterns': consecutive_patterns,
            'avg_odd_count': np.mean(odd_even_patterns),
            'avg_small_count': np.mean(size_patterns),
            'avg_consecutive': np.mean(consecutive_patterns)
        }
        
        logger.info("基於模式的預測器初始化完成")
        return predictor
    
    def _prepare_ml_data(self, df):
        """準備機器學習數據"""
        main_cols = [f'Anumber{i}' for i in range(1, self.main_count + 1)]
        
        X = []
        y = []
        
        # 使用滑動窗口創建特徵
        window_size = 5
        
        for i in range(window_size, len(df)):
            # 特徵：前N期的號碼出現情況
            features = []
            
            # 前N期的統計特徵
            window_data = df.iloc[i-window_size:i]
            
            # 號碼頻率特徵
            number_counts = defaultdict(int)
            for _, row in window_data.iterrows():
                for col in main_cols:
                    number_counts[row[col]] += 1
            
            # 轉換為特徵向量
            for num in range(1, self.main_numbers + 1):
                features.append(number_counts[num])
            
            # 奇偶比例
            odd_count = 0
            total_count = 0
            for _, row in window_data.iterrows():
                for col in main_cols:
                    if row[col] % 2 == 1:
                        odd_count += 1
                    total_count += 1
            features.append(odd_count / total_count if total_count > 0 else 0)
            
            # 大小比例
            small_count = 0
            mid_point = self.main_numbers // 2
            for _, row in window_data.iterrows():
                for col in main_cols:
                    if row[col] <= mid_point:
                        small_count += 1
            features.append(small_count / total_count if total_count > 0 else 0)
            
            X.append(features)
            
            # 標籤：當前期的號碼
            current_numbers = [df.iloc[i][col] for col in main_cols]
            y.append(min(current_numbers))  # 簡化：使用最小號碼作為標籤
        
        return np.array(X), np.array(y)
    
    def initialize_predictors(self, df):
        """初始化所有預測器"""
        logger.info("初始化所有預測器...")
        
        # 初始化統計機器學習預測器
        self.predictors['statistical_ml'] = self._init_statistical_ml_predictor(df)
        
        # 初始化基於頻率的預測器
        self.predictors['frequency_based'] = self._init_frequency_based_predictor(df)
        
        # 初始化基於模式的預測器
        self.predictors['pattern_based'] = self._init_pattern_based_predictor(df)
        
        logger.info("所有預測器初始化完成")
    
    def predict_with_board_path(self, df):
        """使用板路分析進行預測"""
        try:
            result = self.predictors['board_path'].predict_next_numbers(df)
            if result and '主區' in result:
                return {
                    'main_numbers': result['主區'],
                    'confidence': result.get('信心度', 50),
                    'method': 'board_path',
                    'special_number': result.get('特區', 1) if self.has_special else None
                }
        except Exception as e:
            logger.warning(f"板路分析預測失敗: {e}")
        
        return None
    
    def predict_with_optimized_board_path(self, df):
        """使用優化板路分析進行預測"""
        try:
            result = self.predictors['optimized_board_path'].predict_next_numbers_optimized(df)
            if result and 'main_numbers' in result:
                return {
                    'main_numbers': result['main_numbers'],
                    'confidence': result.get('confidence', 50),
                    'method': 'optimized_board_path',
                    'special_number': result.get('special_number', 1) if self.has_special else None
                }
        except Exception as e:
            logger.warning(f"優化板路分析預測失敗: {e}")
        
        return None
    
    def predict_with_feature_enhanced(self, df):
        """使用特徵增強預測器進行預測"""
        try:
            result = self.predictors['feature_enhanced'].predict_with_enhanced_features(df)
            if result and 'main_numbers' in result:
                return {
                    'main_numbers': result['main_numbers'],
                    'confidence': result.get('confidence', 50),
                    'method': 'feature_enhanced',
                    'special_number': result.get('special_number', 1) if self.has_special else None
                }
        except Exception as e:
            logger.warning(f"特徵增強預測失敗: {e}")
        
        return None
    
    def predict_with_statistical_ml(self, df):
        """使用統計機器學習進行預測"""
        if self.predictors['statistical_ml'] is None:
            return None
        
        try:
            # 準備預測特徵
            X, _ = self._prepare_ml_data(df)
            if len(X) == 0:
                return None
            
            # 使用最後一個樣本的特徵進行預測
            last_features = X[-1].reshape(1, -1)
            
            # 預測概率
            probabilities = self.predictors['statistical_ml'].predict_proba(last_features)[0]
            
            # 選擇高概率的號碼
            sorted_indices = np.argsort(probabilities)[::-1]
            
            # 生成預測號碼
            predicted_numbers = []
            for i in range(min(self.main_count, len(sorted_indices))):
                predicted_numbers.append(sorted_indices[i] + 1)  # 轉換為1-based
            
            # 確保在有效範圍內
            predicted_numbers = [num for num in predicted_numbers if 1 <= num <= self.main_numbers]
            
            # 如果數量不足，補充高頻號碼
            while len(predicted_numbers) < self.main_count:
                for num in range(1, self.main_numbers + 1):
                    if num not in predicted_numbers:
                        predicted_numbers.append(num)
                        break
            
            return {
                'main_numbers': sorted(predicted_numbers[:self.main_count]),
                'confidence': max(probabilities) * 100,
                'method': 'statistical_ml',
                'special_number': np.random.randint(1, self.special_numbers + 1) if self.has_special else None
            }
        except Exception as e:
            logger.warning(f"統計機器學習預測失敗: {e}")
        
        return None
    
    def predict_with_frequency_based(self, df):
        """使用基於頻率的方法進行預測"""
        if self.predictors['frequency_based'] is None:
            return None
        
        try:
            predictor = self.predictors['frequency_based']
            
            # 結合歷史頻率和加權頻率
            combined_scores = {}
            
            for num in range(1, self.main_numbers + 1):
                historical_freq = predictor['number_frequency'].get(num, 0)
                weighted_freq = predictor['weighted_frequency'].get(num, 0)
                
                # 加權組合
                combined_scores[num] = historical_freq * 0.4 + weighted_freq * 0.6
            
            # 選擇最高分的號碼
            sorted_numbers = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
            predicted_numbers = [num for num, score in sorted_numbers[:self.main_count]]
            
            # 計算信心度
            top_scores = [score for num, score in sorted_numbers[:self.main_count]]
            confidence = (np.mean(top_scores) / max(combined_scores.values())) * 100 if combined_scores else 50
            
            return {
                'main_numbers': sorted(predicted_numbers),
                'confidence': confidence,
                'method': 'frequency_based',
                'special_number': np.random.randint(1, self.special_numbers + 1) if self.has_special else None
            }
        except Exception as e:
            logger.warning(f"基於頻率的預測失敗: {e}")
        
        return None
    
    def predict_with_pattern_based(self, df):
        """使用基於模式的方法進行預測"""
        if self.predictors['pattern_based'] is None:
            return None
        
        try:
            predictor = self.predictors['pattern_based']
            
            # 基於歷史模式生成預測
            target_odd_count = round(predictor['avg_odd_count'])
            target_small_count = round(predictor['avg_small_count'])
            target_consecutive = round(predictor['avg_consecutive'])
            
            # 生成符合模式的號碼組合
            predicted_numbers = []
            
            # 選擇奇數號碼
            odd_numbers = [num for num in range(1, self.main_numbers + 1) if num % 2 == 1]
            selected_odds = np.random.choice(odd_numbers, size=min(target_odd_count, len(odd_numbers)), replace=False)
            predicted_numbers.extend(selected_odds)
            
            # 選擇偶數號碼
            remaining_count = self.main_count - len(predicted_numbers)
            if remaining_count > 0:
                even_numbers = [num for num in range(1, self.main_numbers + 1) if num % 2 == 0 and num not in predicted_numbers]
                selected_evens = np.random.choice(even_numbers, size=min(remaining_count, len(even_numbers)), replace=False)
                predicted_numbers.extend(selected_evens)
            
            # 確保數量正確
            while len(predicted_numbers) < self.main_count:
                for num in range(1, self.main_numbers + 1):
                    if num not in predicted_numbers:
                        predicted_numbers.append(num)
                        break
            
            return {
                'main_numbers': sorted(predicted_numbers[:self.main_count]),
                'confidence': 60,  # 基於模式的信心度
                'method': 'pattern_based',
                'special_number': np.random.randint(1, self.special_numbers + 1) if self.has_special else None
            }
        except Exception as e:
            logger.warning(f"基於模式的預測失敗: {e}")
        
        return None
    
    def ensemble_predict(self, df, ensemble_size=10):
        """集成預測方法"""
        logger.info(f"開始集成預測 - 集成大小: {ensemble_size}")
        
        # 初始化預測器
        self.initialize_predictors(df)
        
        # 收集所有預測結果
        all_predictions = []
        
        # 各算法預測
        prediction_methods = [
            ('board_path', self.predict_with_board_path),
            ('optimized_board_path', self.predict_with_optimized_board_path),
            ('feature_enhanced', self.predict_with_feature_enhanced),
            ('statistical_ml', self.predict_with_statistical_ml),
            ('frequency_based', self.predict_with_frequency_based),
            ('pattern_based', self.predict_with_pattern_based)
        ]
        
        for method_name, method_func in prediction_methods:
            try:
                prediction = method_func(df)
                if prediction:
                    prediction['weight'] = self.algorithm_weights[method_name]
                    all_predictions.append(prediction)
                    logger.info(f"{method_name} 預測: {prediction['main_numbers']} (信心度: {prediction['confidence']:.1f}%)")
            except Exception as e:
                logger.warning(f"{method_name} 預測失敗: {e}")
        
        if not all_predictions:
            logger.error("所有預測方法都失敗")
            return None
        
        # 投票機制
        number_votes = defaultdict(float)
        total_weight = 0
        
        for prediction in all_predictions:
            weight = prediction['weight']
            confidence_weight = prediction['confidence'] / 100
            final_weight = weight * confidence_weight
            total_weight += final_weight
            
            for number in prediction['main_numbers']:
                number_votes[number] += final_weight
        
        # 選擇得票最高的號碼
        sorted_votes = sorted(number_votes.items(), key=lambda x: x[1], reverse=True)
        final_numbers = [num for num, votes in sorted_votes[:self.main_count]]
        
        # 計算綜合信心度
        weighted_confidence = sum(pred['confidence'] * pred['weight'] for pred in all_predictions) / sum(pred['weight'] for pred in all_predictions)
        
        # 多樣性調整
        diversity_bonus = self._calculate_diversity_bonus(all_predictions)
        final_confidence = min(95, weighted_confidence + diversity_bonus)
        
        # 特別號預測 (使用投票)
        special_number = None
        if self.has_special:
            special_votes = defaultdict(int)
            for prediction in all_predictions:
                if prediction.get('special_number'):
                    special_votes[prediction['special_number']] += 1
            
            if special_votes:
                special_number = max(special_votes.items(), key=lambda x: x[1])[0]
            else:
                special_number = np.random.randint(1, self.special_numbers + 1)
        
        result = {
            'method': 'multi_algorithm_ensemble',
            'version': 'v1.0',
            'main_numbers': sorted(final_numbers),
            'confidence': final_confidence,
            'special_number': special_number,
            'individual_predictions': all_predictions,
            'algorithm_weights': self.algorithm_weights,
            'voting_summary': dict(sorted_votes),
            'explanation': f'集成{len(all_predictions)}種算法的預測結果'
        }
        
        logger.info(f"集成預測完成 - 最終預測: {result['main_numbers']} (信心度: {result['confidence']:.1f}%)")
        
        return result
    
    def _calculate_diversity_bonus(self, predictions):
        """計算多樣性獎勵"""
        if len(predictions) < 2:
            return 0
        
        # 計算預測結果的多樣性
        all_numbers = set()
        for prediction in predictions:
            all_numbers.update(prediction['main_numbers'])
        
        # 多樣性得分
        diversity_score = len(all_numbers) / (len(predictions) * self.main_count)
        
        # 方法多樣性
        method_diversity = len(set(pred['method'] for pred in predictions)) / len(predictions)
        
        # 綜合多樣性獎勵
        diversity_bonus = (diversity_score + method_diversity) * 5
        
        return diversity_bonus
    
    def update_algorithm_weights(self, actual_result, predictions):
        """根據實際結果更新算法權重"""
        if not actual_result or not predictions:
            return
        
        logger.info("更新算法權重...")
        
        # 計算各算法的準確率
        for prediction in predictions:
            method = prediction['method']
            predicted_numbers = set(prediction['main_numbers'])
            actual_numbers = set(actual_result)
            
            # 計算匹配數
            matches = len(predicted_numbers.intersection(actual_numbers))
            accuracy = matches / len(actual_numbers)
            
            # 更新性能歷史
            self.performance_history[method].append(accuracy)
            
            # 保持歷史記錄在合理範圍內
            if len(self.performance_history[method]) > 50:
                self.performance_history[method] = self.performance_history[method][-50:]
        
        # 重新計算權重
        total_performance = 0
        performance_scores = {}
        
        for method in self.algorithm_weights.keys():
            if self.performance_history[method]:
                # 使用近期表現計算權重
                recent_performance = np.mean(self.performance_history[method][-10:])
                performance_scores[method] = recent_performance
                total_performance += recent_performance
        
        # 更新權重
        if total_performance > 0:
            for method in self.algorithm_weights.keys():
                if method in performance_scores:
                    self.algorithm_weights[method] = performance_scores[method] / total_performance
        
        logger.info(f"算法權重更新完成: {self.algorithm_weights}")
    
    def generate_comprehensive_report(self, df, result):
        """生成綜合預測報告"""
        logger.info("生成綜合預測報告...")
        
        report = []
        report.append("=" * 80)
        report.append("🚀 多算法集成預測系統 - 綜合報告")
        report.append("=" * 80)
        
        report.append(f"📊 數據概況:")
        report.append(f"  • 歷史期數: {len(df)}")
        report.append(f"  • 彩票類型: {self.lottery_type}")
        report.append(f"  • 預測時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        report.append(f"\n🎯 最終預測結果:")
        report.append(f"  • 主要號碼: {result.get('main_numbers', 'N/A')}")
        if result.get('special_number'):
            report.append(f"  • 特別號碼: {result.get('special_number', 'N/A')}")
        report.append(f"  • 綜合信心度: {result.get('confidence', 0):.1f}%")
        report.append(f"  • 預測方法: {result.get('method', 'N/A')}")
        
        report.append(f"\n🔄 各算法預測結果:")
        individual_predictions = result.get('individual_predictions', [])
        for i, pred in enumerate(individual_predictions, 1):
            method = pred.get('method', 'Unknown')
            numbers = pred.get('main_numbers', [])
            confidence = pred.get('confidence', 0)
            weight = pred.get('weight', 0)
            report.append(f"  {i}. {method}: {numbers} (信心度: {confidence:.1f}%, 權重: {weight:.3f})")
        
        report.append(f"\n⚖️ 算法權重分配:")
        for method, weight in self.algorithm_weights.items():
            report.append(f"  • {method}: {weight:.3f}")
        
        report.append(f"\n📈 投票統計:")
        voting_summary = result.get('voting_summary', {})
        sorted_votes = sorted(voting_summary.items(), key=lambda x: x[1], reverse=True)
        for i, (number, votes) in enumerate(sorted_votes[:10], 1):
            report.append(f"  {i:2d}. 號碼 {number:2d}: {votes:.3f} 票")
        
        report.append(f"\n🎲 預測策略:")
        report.append(f"  • 參與算法數: {len(individual_predictions)}")
        report.append(f"  • 投票機制: 加權投票")
        report.append(f"  • 信心度計算: 綜合加權平均")
        report.append(f"  • 多樣性獎勵: 已啟用")
        
        if self.performance_history:
            report.append(f"\n📊 歷史表現:")
            for method, history in self.performance_history.items():
                if history:
                    avg_performance = np.mean(history[-10:])  # 最近10次表現
                    report.append(f"  • {method}: {avg_performance:.3f} (近期平均)")
        
        report.append("\n" + "=" * 80)
        
        return "\n".join(report)
    
    def predict_with_comprehensive_analysis(self, df, ensemble_size=10, generate_report=True):
        """
        使用綜合分析進行預測
        
        Args:
            df: 歷史數據DataFrame
            ensemble_size: 集成大小
            generate_report: 是否生成報告
            
        Returns:
            dict: 預測結果
        """
        logger.info(f"開始綜合分析預測 - 集成大小: {ensemble_size}")
        
        # 執行集成預測
        result = self.ensemble_predict(df, ensemble_size)
        
        if result is None:
            logger.error("綜合分析預測失敗")
            return None
        
        # 生成報告
        if generate_report:
            report = self.generate_comprehensive_report(df, result)
            result['report'] = report
        
        logger.info(f"綜合分析預測完成 - 信心度: {result.get('confidence', 0):.1f}%")
        
        return result