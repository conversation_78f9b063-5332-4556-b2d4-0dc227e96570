#!/usr/bin/env python3
"""
批次回測執行腳本
用於快速測試和執行批次回測功能
"""

import sys
import os
from datetime import datetime
from batch_backtest import BatchBacktester, BacktestConfig, GameType, PredictionMethod


def print_separator(title=""):
    """列印分隔線"""
    print("=" * 60)
    if title:
        print(f" {title} ".center(60, "="))
        print("=" * 60)


def run_single_backtest(game_type, method, start_period, end_period, training_window=30):
    """執行單一回測"""
    print(f"\n🔄 執行回測...")
    print(f"遊戲類型: {game_type.value}")
    print(f"預測方法: {method.value}")
    print(f"測試期間: {start_period} - {end_period}")
    print(f"訓練窗口: {training_window} 期")
    
    # 創建配置
    config = BacktestConfig(
        game_type=game_type,
        method=method,
        start_period=start_period,
        end_period=end_period,
        training_window=training_window
    )
    
    # 執行回測
    backtester = BatchBacktester(config)
    results = backtester.run_backtest()
    
    if "error" in results:
        print(f"❌ 回測失敗: {results['error']}")
        return None
    
    # 顯示結果摘要
    print_separator("回測結果摘要")
    stats = results['statistics']
    
    print(f"📊 基本統計:")
    print(f"   總測試期數: {stats['total_periods']}")
    print(f"   總命中數: {stats['total_matches']}")
    print(f"   平均命中數: {stats['average_matches_per_period']:.2f}")
    print(f"   號碼準確率: {stats['accuracy']:.2f}%")
    print(f"   中獎率: {stats['winning_rate']:.2f}%")
    print(f"   平均信心度: {stats['average_confidence']:.2f}%")
    
    # 獎項統計
    prize_dist = results.get('prize_distribution', {})
    total_prizes = sum(prize_dist.values())
    
    if total_prizes > 0:
        print(f"\n🏆 獎項分佈:")
        prize_names = {
            'prize_1': '頭獎', 'prize_2': '貳獎', 'prize_3': '參獎', 'prize_4': '肆獎',
            'prize_5': '伍獎', 'prize_6': '陸獎', 'prize_7': '柒獎', 'prize_8': '捌獎'
        }
        
        for prize_key, prize_name in prize_names.items():
            count = prize_dist.get(prize_key, 0)
            if count > 0:
                percentage = (count / total_prizes) * 100
                print(f"   {prize_name}: {count} 次 ({percentage:.1f}%)")
    else:
        print(f"\n🏆 獎項分佈: 未中任何獎項")
    
    # 命中分佈
    match_dist = results.get('match_distribution', {})
    print(f"\n🎯 命中分佈:")
    
    for i in range(7):  # 0-6個命中
        key = f'matches_{i}'
        count = match_dist.get(key, 0)
        if count > 0:
            percentage = (count / stats['total_periods']) * 100
            print(f"   {i} 個命中: {count} 次 ({percentage:.1f}%)")
    
    # 儲存結果
    filepath = backtester.save_results()
    print(f"\n💾 詳細結果已儲存至: {filepath}")
    
    return results


def run_comparison_test():
    """執行多方法比較測試"""
    print_separator("多方法比較測試")
    
    # 測試配置 - 使用真實期數格式
    game_type = GameType.POWERCOLOR
    methods = [
        PredictionMethod.FREQUENCY_ANALYSIS,
        PredictionMethod.PATTERN_ANALYSIS,
        PredictionMethod.CONSECUTIVE_ANALYSIS,
        PredictionMethod.RANDOM_BASELINE
    ]
    
    start_period = "114000040"  # 從第40期開始
    end_period = "114000058"    # 到第58期
    training_window = 15
    
    results_summary = []
    
    for method in methods:
        print(f"\n>>> 測試方法: {method.value}")
        result = run_single_backtest(game_type, method, start_period, end_period, training_window)
        
        if result:
            stats = result['statistics']
            results_summary.append({
                'method': method.value,
                'accuracy': stats['accuracy'],
                'winning_rate': stats['winning_rate'],
                'avg_matches': stats['average_matches_per_period'],
                'confidence': stats['average_confidence']
            })
    
    # 比較結果
    if results_summary:
        print_separator("方法比較結果")
        print(f"{'方法':<20} {'準確率':<10} {'中獎率':<10} {'平均命中':<10} {'信心度':<10}")
        print("-" * 60)
        
        for result in results_summary:
            print(f"{result['method']:<20} {result['accuracy']:<10.2f} {result['winning_rate']:<10.2f} {result['avg_matches']:<10.2f} {result['confidence']:<10.2f}")
        
        # 找出最佳方法
        best_accuracy = max(results_summary, key=lambda x: x['accuracy'])
        best_winning = max(results_summary, key=lambda x: x['winning_rate'])
        
        print(f"\n🏅 最佳準確率: {best_accuracy['method']} ({best_accuracy['accuracy']:.2f}%)")
        print(f"🏅 最佳中獎率: {best_winning['method']} ({best_winning['winning_rate']:.2f}%)")


def run_quick_test():
    """執行快速測試"""
    print_separator("快速測試")
    
    # 使用較小的測試範圍 - 真實期數格式
    result = run_single_backtest(
        game_type=GameType.POWERCOLOR,
        method=PredictionMethod.FREQUENCY_ANALYSIS,
        start_period="114000050",  # 從第50期開始
        end_period="114000058",    # 到第58期
        training_window=10
    )
    
    if result:
        print("\n✅ 快速測試完成！")
    else:
        print("\n❌ 快速測試失敗！")


def main():
    """主函數"""
    print_separator("彩票批次回測系統")
    print("選擇執行模式:")
    print("1. 快速測試")
    print("2. 單一回測")
    print("3. 多方法比較")
    print("4. 啟動Web界面")
    
    try:
        choice = input("\n請輸入選項 (1-4): ").strip()
        
        if choice == "1":
            run_quick_test()
            
        elif choice == "2":
            print("\n=== 單一回測配置 ===")
            
            # 選擇遊戲類型
            print("遊戲類型:")
            print("1. 威力彩 (PowerColor)")
            print("2. 大樂透 (Lotto649)")  
            print("3. 今彩539 (DailyCash)")
            
            game_choice = input("選擇遊戲 (1-3): ").strip()
            game_map = {"1": GameType.POWERCOLOR, "2": GameType.LOTTO649, "3": GameType.DAILYCASH}
            game_type = game_map.get(game_choice, GameType.POWERCOLOR)
            
            # 選擇預測方法
            print("\n預測方法:")
            print("1. 頻率分析")
            print("2. 模式分析")
            print("3. 連號分析")
            print("4. 隨機基準")
            
            method_choice = input("選擇方法 (1-4): ").strip()
            method_map = {
                "1": PredictionMethod.FREQUENCY_ANALYSIS,
                "2": PredictionMethod.PATTERN_ANALYSIS,
                "3": PredictionMethod.CONSECUTIVE_ANALYSIS,
                "4": PredictionMethod.RANDOM_BASELINE
            }
            method = method_map.get(method_choice, PredictionMethod.FREQUENCY_ANALYSIS)
            
            # 期間設定 - 使用真實期數格式
            start_period = input("開始期數 (例如: 114000030): ").strip() or "114000030"
            end_period = input("結束期數 (例如: 114000058): ").strip() or "114000058"
            training_window = int(input("訓練窗口 (例如: 20): ").strip() or "20")
            
            run_single_backtest(game_type, method, start_period, end_period, training_window)
            
        elif choice == "3":
            run_comparison_test()
            
        elif choice == "4":
            print("\n🌐 啟動Web界面...")
            print("請執行以下命令啟動Web界面:")
            print("streamlit run batch_backtest_web.py")
            
        else:
            print("❌ 無效選項")
            
    except KeyboardInterrupt:
        print("\n\n👋 程式已中斷")
    except Exception as e:
        print(f"\n❌ 執行錯誤: {e}")


if __name__ == "__main__":
    # 直接執行快速測試以供演示
    run_quick_test()