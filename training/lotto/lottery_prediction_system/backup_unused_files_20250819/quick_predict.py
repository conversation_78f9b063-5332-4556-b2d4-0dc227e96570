#!/usr/bin/env python3
"""
快速预测脚本 - 一键生成今日所有彩票预测
"""

import os
import sys
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from generate_today_predictions import TodayPredictionGenerator

def quick_predict():
    """快速生成今日预测"""
    
    print("🎯 快速彩票预测系统")
    print("="*60)
    print(f"📅 日期: {datetime.now().strftime('%Y年%m月%d日')}")
    print("="*60)
    
    # 选择策略
    print("\n请选择预测策略:")
    print("1. 保守策略 (3组预测，低风险)")
    print("2. 平衡策略 (5组预测，中等风险) [推荐]")
    print("3. 激进策略 (8组预测，高风险)")
    
    choice = input("\n输入选择 (1/2/3，默认2): ").strip() or "2"
    
    strategy_map = {
        "1": "conservative",
        "2": "balanced", 
        "3": "aggressive"
    }
    
    strategy = strategy_map.get(choice, "balanced")
    strategy_names = {'conservative': '保守', 'balanced': '平衡', 'aggressive': '激进'}
    print(f"\n✅ 使用{strategy_names[strategy]}策略")
    
    # 选择彩票类型
    print("\n请选择彩票类型:")
    print("1. 威力彩")
    print("2. 大乐透")
    print("3. 今彩539")
    print("4. 全部 [推荐]")
    
    lottery_choice = input("\n输入选择 (1/2/3/4，默认4): ").strip() or "4"
    
    lottery_map = {
        "1": "powercolor",
        "2": "super_lotto",
        "3": "daily539",
        "4": "all"
    }
    
    lottery_type = lottery_map.get(lottery_choice, "all")
    
    # 生成预测
    print("\n" + "="*60)
    print("🔮 开始生成预测...")
    print("="*60)
    
    generator = TodayPredictionGenerator()
    
    if lottery_type == "all":
        predictions = generator.generate_all_predictions(strategy)
    else:
        generator.initialize_systems()
        predictions = {}
        result = generator.generate_prediction(lottery_type, strategy)
        if result:
            predictions[lottery_type] = result
    
    # 显示最终结果
    if predictions:
        print("\n" + "="*60)
        print("🎉 预测完成！最终号码：")
        print("="*60)
        
        for lt, pred in predictions.items():
            print(f"\n🎲 {pred['lottery_name']}:")
            
            # 格式化显示号码
            main_nums = [str(n).zfill(2) for n in pred['main_numbers']]
            print(f"   主号码: {' '.join(main_nums)}")
            
            if pred['special_number']:
                print(f"   特别号: {str(pred['special_number']).zfill(2)}")
            
            # 显示质量指标
            confidence = pred['confidence_level']
            color = {'高': '🟢', '中等': '🟡', '低': '🔴'}[confidence]
            print(f"   信心度: {color} {confidence} ({pred['confidence']:.1f}%)")
            
            # 投注建议
            if confidence == '高':
                print(f"   💡 建议: 信心度高，可适度增加投注")
            elif confidence == '中等':
                print(f"   💡 建议: 信心度中等，正常投注即可")
            else:
                print(f"   💡 建议: 信心度较低，建议谨慎投注")
    
    else:
        print("\n⚠️ 没有生成任何预测")
    
    print("\n" + "="*60)
    print("📌 重要提醒:")
    print("• 预测结果仅供参考，不保证中奖")
    print("• 请根据个人经济能力理性投注")
    print("• 博彩有风险，投注需谨慎")
    print("="*60)

if __name__ == "__main__":
    quick_predict()