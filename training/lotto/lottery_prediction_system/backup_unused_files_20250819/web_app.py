#!/usr/bin/env python3
"""
彩票预测Web应用
提供友好的Web界面来生成和查看预测
"""

from flask import Flask, render_template, request, jsonify, session
from flask_cors import CORS
import os
import sys
import json
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from generate_today_predictions import TodayPredictionGenerator, LOTTERY_CONFIGS
from prediction.accuracy_tracker import AccuracyTracker

def convert_numpy_types(data):
    """递归转换numpy类型为Python原生类型"""
    if isinstance(data, dict):
        return {key: convert_numpy_types(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [convert_numpy_types(item) for item in data]
    elif isinstance(data, np.ndarray):
        return data.tolist()
    elif isinstance(data, np.integer):
        return int(data)
    elif isinstance(data, np.floating):
        return float(data)
    elif hasattr(data, 'dtype'):  # 其他numpy类型
        if hasattr(data, 'tolist'):
            return data.tolist()
        elif 'int' in str(data.dtype):
            return int(data)
        elif 'float' in str(data.dtype):
            return float(data)
        else:
            return str(data)
    else:
        return data

app = Flask(__name__)
app.secret_key = 'lottery_prediction_2024_secret_key'
CORS(app)

# 全局生成器实例
generator = None
tracker = AccuracyTracker()

def init_generator():
    """初始化生成器"""
    global generator
    if not generator:
        generator = TodayPredictionGenerator()
        generator.initialize_systems()
    return generator

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/lottery_types')
def get_lottery_types():
    """获取所有彩票类型"""
    lottery_info = []
    for key, config in LOTTERY_CONFIGS.items():
        lottery_info.append({
            'id': key,
            'name': config['name'],
            'description': config['description'],
            'draw_days': config['draw_days'],
            'main_count': config['main_count'],
            'has_special': config['special_range'] is not None
        })
    return jsonify(lottery_info)

@app.route('/api/generate_prediction', methods=['POST'])
def generate_prediction():
    """生成预测"""
    data = request.json
    lottery_type = data.get('lottery_type', 'powercolor')
    strategy = data.get('strategy', 'balanced')
    
    # 初始化生成器
    gen = init_generator()
    
    # 生成预测
    try:
        result = gen.generate_prediction(lottery_type, strategy)
        
        if result:
            # 转换numpy类型并格式化响应
            clean_result = convert_numpy_types(result)
            response = {
                'success': True,
                'lottery_name': clean_result['lottery_name'],
                'lottery_type': clean_result['lottery_type'],
                'main_numbers': clean_result['main_numbers'],
                'special_number': clean_result['special_number'],
                'confidence': round(float(clean_result['confidence']), 1),
                'expected_hit_rate': round(float(clean_result['expected_hit_rate']) * 100, 2),
                'confidence_level': clean_result['confidence_level'],
                'strategy': clean_result['strategy'],
                'prediction_date': clean_result['prediction_date'],
                'multi_groups': clean_result['multi_groups']
            }
            
            # 添加投注建议
            if result['confidence_level'] == '高':
                response['suggestion'] = '信心度高，建议适度增加投注'
                response['suggestion_type'] = 'success'
            elif result['confidence_level'] == '中等':
                response['suggestion'] = '信心度中等，建议正常投注'
                response['suggestion_type'] = 'warning'
            else:
                response['suggestion'] = '信心度较低，建议谨慎投注'
                response['suggestion_type'] = 'danger'
                
            return jsonify(response)
        else:
            return jsonify({
                'success': False,
                'message': '该彩票今日不开奖或预测生成失败'
            })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'生成预测时出错: {str(e)}'
        })

@app.route('/api/batch_generate', methods=['POST'])
def batch_generate():
    """批量生成所有彩票预测"""
    data = request.json
    strategy = data.get('strategy', 'balanced')
    
    # 初始化生成器
    gen = init_generator()
    
    results = []
    for lottery_type in LOTTERY_CONFIGS.keys():
        try:
            result = gen.generate_prediction(lottery_type, strategy)
            if result:
                clean_result = convert_numpy_types(result)
                results.append({
                    'lottery_type': lottery_type,
                    'lottery_name': clean_result['lottery_name'],
                    'main_numbers': clean_result['main_numbers'],
                    'special_number': clean_result['special_number'],
                    'confidence': round(float(clean_result['confidence']), 1),
                    'confidence_level': clean_result['confidence_level'],
                    'expected_hit_rate': round(float(clean_result['expected_hit_rate']) * 100, 2)
                })
        except Exception as e:
            print(f"生成{lottery_type}预测失败: {e}")
            
    return jsonify({
        'success': True,
        'predictions': results,
        'total': len(results),
        'strategy': strategy,
        'date': datetime.now().strftime('%Y-%m-%d')
    })

@app.route('/api/history')
def get_history():
    """获取历史预测记录"""
    try:
        # 获取最近30天的预测记录
        days = request.args.get('days', 30, type=int)
        lottery_type = request.args.get('lottery_type', None)
        
        # 从tracker获取历史记录
        if lottery_type:
            metrics = tracker.get_algorithm_metrics('core_refined_balanced', lottery_type, days)
        else:
            metrics = tracker.get_overall_performance_report('all', days)
            
        return jsonify({
            'success': True,
            'history': metrics if metrics else {},
            'days': days
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

@app.route('/api/verify_prediction', methods=['POST'])
def verify_prediction():
    """验证预测结果"""
    data = request.json
    draw_date = data.get('draw_date')
    actual_numbers = data.get('actual_numbers')
    actual_special = data.get('actual_special')
    lottery_type = data.get('lottery_type')
    
    try:
        # 验证预测
        verification = tracker.verify_predictions(
            draw_date,
            actual_numbers,
            actual_special,
            lottery_type
        )
        
        return jsonify({
            'success': True,
            'verified_count': verification['verified_count'],
            'results': verification['results']
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

@app.route('/api/performance_stats')
def get_performance_stats():
    """获取性能统计"""
    try:
        lottery_type = request.args.get('lottery_type', 'powercolor')
        
        # 获取实时指标
        real_time_metrics = tracker.get_real_time_metrics(lottery_type)
        
        # 获取30天性能报告
        report = tracker.get_overall_performance_report(lottery_type, 30)
        
        return jsonify({
            'success': True,
            'real_time': real_time_metrics,
            'monthly_report': report
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

if __name__ == '__main__':
    # 创建模板目录
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    print("🌐 启动Web应用...")
    print("📍 访问地址: http://localhost:5000")
    print("🛑 按 Ctrl+C 停止服务器")
    
    app.run(debug=True, host='0.0.0.0', port=5000)