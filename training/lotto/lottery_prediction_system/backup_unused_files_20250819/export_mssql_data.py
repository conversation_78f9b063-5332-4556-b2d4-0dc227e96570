# export_mssql_data.py
import pyodbc
import pandas as pd
import os
import time

def export_data_to_csv():
    # MSSQL連接字串
    conn_str = "DRIVER={ODBC Driver 17 for SQL Server};SERVER=localhost;DATABASE=lotto;UID=sa;PWD=**********"
    
    try:
        print("嘗試連接 SQL Server...")
        # 連接MSSQL
        conn = pyodbc.connect(conn_str)
        print("SQL Server 連接成功！")
        
        # 導出威力彩數據
        print("開始匯出威力彩數據...")
        power_query = "SELECT * FROM Powercolor ORDER BY Period"
        power_df = pd.read_sql(power_query, conn)
        if not power_df.empty:
            power_df.to_csv('powercolor_data.csv', index=False)
            print(f"已成功導出 {len(power_df)} 筆威力彩數據")
        else:
            print("警告: 威力彩表中沒有數據")
        
        # 導出大樂透數據
        print("開始匯出大樂透數據...")
        lotto_query = "SELECT * FROM Lotto649 ORDER BY Period"
        lotto_df = pd.read_sql(lotto_query, conn)
        if not lotto_df.empty:
            lotto_df.to_csv('lotto649_data.csv', index=False)
            print(f"已成功導出 {len(lotto_df)} 筆大樂透數據")
        else:
            print("警告: 大樂透表中沒有數據")
        
        # 導出今彩539數據
        print("開始匯出今彩539數據...")
        daily_query = "SELECT * FROM DailyCash ORDER BY Period"
        daily_df = pd.read_sql(daily_query, conn)
        if not daily_df.empty:
            daily_df.to_csv('dailycash_data.csv', index=False)
            print(f"已成功導出 {len(daily_df)} 筆今彩539數據")
        else:
            print("警告: 今彩539表中沒有數據")
        
        # 導出預測記錄
        print("開始匯出威力彩預測記錄...")
        try:
            pred_power_query = "SELECT * FROM PowercolorPredictions"
            pred_power_df = pd.read_sql(pred_power_query, conn)
            if not pred_power_df.empty:
                pred_power_df.to_csv('powercolor_predictions.csv', index=False)
                print(f"已成功導出 {len(pred_power_df)} 筆威力彩預測記錄")
            else:
                print("威力彩預測記錄表中沒有數據")
        except Exception as e:
            print(f"導出威力彩預測記錄時出錯: {str(e)}")
        
        conn.close()
        print("所有資料成功導出為CSV檔案")
        
    except Exception as e:
        print(f"導出數據時出錯: {str(e)}")
        print("檢查 SQL Server 連接是否正確設置...")

if __name__ == "__main__":
    export_data_to_csv()