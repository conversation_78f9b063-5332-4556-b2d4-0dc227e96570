#!/usr/bin/env python3
"""
彩票預測系統啟動腳本
解決現有系統問題，提供完整功能
"""

import os
import sys
import subprocess
import webbrowser
from datetime import datetime


def check_dependencies():
    """檢查必要的依賴包"""
    required_packages = [
        'streamlit',
        'pandas', 
        'plotly',
        'numpy',
        'sqlite3'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'sqlite3':
                import sqlite3
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少必要的依賴包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n請執行以下命令安裝:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def check_database():
    """檢查資料庫是否存在"""
    db_path = "data/lottery_data.db"
    
    if not os.path.exists(db_path):
        print("⚠️ 資料庫文件不存在")
        print(f"請確認 {db_path} 文件存在")
        
        # 創建空的資料庫結構用於演示
        import sqlite3
        os.makedirs("data", exist_ok=True)
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 創建威力彩表格
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS Powercolor (
                Period TEXT PRIMARY KEY,
                Sdate TEXT,
                Anumber1 INTEGER,
                Anumber2 INTEGER,
                Anumber3 INTEGER,
                Anumber4 INTEGER,
                Anumber5 INTEGER,
                Anumber6 INTEGER,
                Second_district INTEGER
            )
        """)
        
        # 插入一些示例數據
        sample_data = [
            ('114000055', '2025-07-20', 1, 2, 7, 14, 28, 31, 2),
            ('114000056', '2025-07-21', 3, 6, 19, 22, 37, 38, 4),
            ('114000057', '2025-07-22', 1, 4, 6, 21, 25, 28, 2),
            ('114000058', '2025-07-23', 10, 18, 20, 24, 36, 38, 4),
        ]
        
        cursor.executemany(
            "INSERT OR REPLACE INTO Powercolor VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            sample_data
        )
        
        conn.commit()
        conn.close()
        
        print("✅ 已創建示例資料庫")
        return True
    
    print("✅ 資料庫檢查通過")
    return True


def show_system_info():
    """顯示系統信息"""
    print("=" * 60)
    print("🎯 彩票預測系統 v2.0".center(60))
    print("=" * 60)
    print(f"📅 啟動時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔧 系統狀態: 就緒")
    print("📁 工作目錄:", os.getcwd())
    print("=" * 60)


def show_menu():
    """顯示選單"""
    print("\n📋 可用功能:")
    print("1. 🌐 啟動整合Web系統 (推薦)")
    print("2. 📊 啟動回測Web管理器")  
    print("3. 💻 啟動命令行管理器")
    print("4. 🧪 執行系統測試")
    print("5. 📖 查看使用指南")
    print("0. 退出")


def start_integrated_web():
    """啟動整合Web系統"""
    print("\n🌐 啟動整合Web系統...")
    print("這個系統包含:")
    print("  - 🏆 開獎結果查詢 (解決你截圖中的問題)")
    print("  - 📊 回測分析系統 (完整功能)")
    print("  - 🔍 預測方法分析 (修復分析功能)")
    print("  - 📈 系統管理")
    
    try:
        # 啟動streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "integrated_web_system.py", 
            "--server.port=8501",
            "--server.headless=true"
        ])
    except KeyboardInterrupt:
        print("\n👋 系統已關閉")
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")


def start_backtest_web():
    """啟動回測Web管理器"""
    print("\n📊 啟動回測Web管理器...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run",
            "backtest_web_manager.py",
            "--server.port=8502"
        ])
    except KeyboardInterrupt:
        print("\n👋 系統已關閉")
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")


def start_cli_manager():
    """啟動命令行管理器"""
    print("\n💻 啟動命令行管理器...")
    
    try:
        subprocess.run([sys.executable, "backtest_manager.py"])
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")


def run_system_test():
    """執行系統測試"""
    print("\n🧪 執行系統測試...")
    
    try:
        result = subprocess.run([
            sys.executable, "test_backtest_manager.py"
        ], capture_output=True, text=True)
        
        print("測試輸出:")
        print(result.stdout)
        if result.stderr:
            print("錯誤信息:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")


def show_guide():
    """顯示使用指南"""
    print("\n📖 使用指南")
    print("=" * 50)
    
    guide_file = "BACKTEST_MANAGER_GUIDE.md"
    if os.path.exists(guide_file):
        print(f"📄 詳細指南: {guide_file}")
        
        # 顯示快速指南
        print("\n🚀 快速開始:")
        print("1. 選擇選項1啟動整合Web系統")
        print("2. 瀏覽器會自動開啟 http://localhost:8501")
        print("3. 選擇對應功能頁面進行操作")
        
        print("\n💡 主要功能:")
        print("- 🏆 開獎結果查詢: 查看歷史開獎記錄")
        print("- 📊 回測分析: 執行預測方法回測")
        print("- 🔍 方法分析: 比較不同預測方法效果")
        print("- 📈 系統管理: 維護和清理功能")
        
    else:
        print("❌ 使用指南文件未找到")


def main():
    """主函數"""
    show_system_info()
    
    # 檢查依賴
    print("🔍 檢查系統依賴...")
    if not check_dependencies():
        return
    
    # 檢查資料庫
    print("🔍 檢查資料庫...")
    if not check_database():
        return
    
    print("✅ 系統檢查完成，所有功能就緒！")
    
    while True:
        show_menu()
        
        try:
            choice = input("\n請選擇功能 (0-5): ").strip()
            
            if choice == "0":
                print("👋 再見！")
                break
            elif choice == "1":
                start_integrated_web()
            elif choice == "2":
                start_backtest_web()
            elif choice == "3":
                start_cli_manager()
            elif choice == "4":
                run_system_test()
            elif choice == "5":
                show_guide()
            else:
                print("❌ 無效選項，請重新選擇")
                
        except KeyboardInterrupt:
            print("\n\n👋 程式已中斷")
            break
        except Exception as e:
            print(f"❌ 執行錯誤: {e}")


if __name__ == "__main__":
    main()