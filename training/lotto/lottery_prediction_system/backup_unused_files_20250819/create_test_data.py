# create_test_data.py
import sqlite3
import pandas as pd
import numpy as np
import os
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_test_data_sqlite(sqlite_db_path):
    try:
        # 確保SQLite資料庫目錄存在
        db_dir = os.path.dirname(sqlite_db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
        
        # 連接SQLite數據庫
        conn = sqlite3.connect(sqlite_db_path)
        cursor = conn.cursor()
        
        # 創建威力彩表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS Powercolor (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            Period TEXT,
            Sdate TEXT,
            Anumber1 INTEGER,
            Anumber2 INTEGER,
            Anumber3 INTEGER,
            Anumber4 INTEGER,
            Anumber5 INTEGER,
            Anumber6 INTEGER,
            Second_district INTEGER
        )
        ''')
        
        # 創建大樂透表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS Lotto649 (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            Period TEXT,
            Sdate TEXT,
            Anumber1 INTEGER,
            Anumber2 INTEGER,
            Anumber3 INTEGER,
            Anumber4 INTEGER,
            Anumber5 INTEGER,
            Anumber6 INTEGER,
            SpecialNumber INTEGER
        )
        ''')
        
        # 創建今彩539表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS DailyCash (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            Period TEXT,
            Sdate TEXT,
            Anumber1 INTEGER,
            Anumber2 INTEGER,
            Anumber3 INTEGER,
            Anumber4 INTEGER,
            Anumber5 INTEGER
        )
        ''')
        
        # 清空現有數據
        cursor.execute("DELETE FROM Powercolor")
        cursor.execute("DELETE FROM Lotto649")
        cursor.execute("DELETE FROM DailyCash")
        
        # 生成測試數據
        base_date = datetime.now() - timedelta(days=100)
        
        # 威力彩測試數據 (30筆)
        print("產生威力彩測試數據...")
        for i in range(30):
            period = f"11300000{i+1:02d}"
            date = (base_date + timedelta(days=i*3)).strftime('%Y-%m-%d')
            
            # 隨機生成第一區號碼 (1-38之間的6個不重複數字)
            numbers = sorted(np.random.choice(range(1, 39), 6, replace=False))
            # 隨機生成第二區號碼 (1-8之間)
            second_district = np.random.randint(1, 9)
            
            cursor.execute('''
            INSERT INTO Powercolor (Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, Second_district)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (period, date, numbers[0], numbers[1], numbers[2], numbers[3], numbers[4], numbers[5], second_district))
        
        # 大樂透測試數據 (30筆)
        print("產生大樂透測試數據...")
        for i in range(30):
            period = f"11200000{i+1:02d}"
            date = (base_date + timedelta(days=i*3)).strftime('%Y-%m-%d')
            
            # 隨機生成第一區號碼 (1-49之間的6個不重複數字)
            numbers = sorted(np.random.choice(range(1, 50), 6, replace=False))
            # 隨機生成特別號 (1-49之間)
            special = np.random.randint(1, 50)
            
            cursor.execute('''
            INSERT INTO Lotto649 (Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, SpecialNumber)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (period, date, numbers[0], numbers[1], numbers[2], numbers[3], numbers[4], numbers[5], special))
        
        # 今彩539測試數據 (50筆)
        print("產生今彩539測試數據...")
        for i in range(50):
            period = f"11000000{i+1:02d}"
            date = (base_date + timedelta(days=i*2)).strftime('%Y-%m-%d')
            
            # 隨機生成號碼 (1-39之間的5個不重複數字)
            numbers = sorted(np.random.choice(range(1, 40), 5, replace=False))
            
            cursor.execute('''
            INSERT INTO DailyCash (Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (period, date, numbers[0], numbers[1], numbers[2], numbers[3], numbers[4]))
        
        conn.commit()
        
        # 確認導入的數據
        cursor.execute("SELECT COUNT(*) FROM Powercolor")
        power_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM Lotto649")
        lotto_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM DailyCash")
        daily_count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"測試數據創建完成！")
        print(f"威力彩: {power_count} 筆")
        print(f"大樂透: {lotto_count} 筆")
        print(f"今彩539: {daily_count} 筆")
        
    except Exception as e:
        print(f"創建測試數據時出錯: {str(e)}")
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    create_test_data_sqlite('lottery_data.db')