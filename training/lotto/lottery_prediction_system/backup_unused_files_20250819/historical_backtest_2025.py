#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2025年歷史回測系統
對今年每一期進行預測，使用該期之前的所有歷史數據作為訓練集
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import datetime
import logging
from typing import Dict, List, Optional

# 添加項目路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.db_manager import DBManager
from prediction.enhanced_board_path_analyzer import EnhancedBoardPathAnalyzer
from prediction.best_prediction_integrator import BestPredictionIntegrator

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Historical2025Backtest:
    """2025年歷史回測系統"""
    
    def __init__(self):
        self.db_manager = DBManager()
        self.results = []
        self.lottery_type = 'powercolor'
        
        # 創建結果目錄
        self.results_dir = 'historical_backtest_2025_results'
        os.makedirs(self.results_dir, exist_ok=True)
        
        logger.info("歷史回測系統初始化完成")
    
    def load_2025_periods(self) -> pd.DataFrame:
        """載入2025年的所有期數"""
        df = self.db_manager.load_lottery_data(self.lottery_type)
        
        # 篩選2025年數據
        df_2025 = df[df['Sdate'] >= '2025-01-01'].copy()
        
        # 按日期排序（從最早到最新）
        df_2025 = df_2025.sort_values('Sdate').reset_index(drop=True)
        
        logger.info(f"載入2025年期數: {len(df_2025)}期")
        logger.info(f"期數範圍: {df_2025['Period'].min()} - {df_2025['Period'].max()}")
        logger.info(f"日期範圍: {df_2025['Sdate'].min()} - {df_2025['Sdate'].max()}")
        
        return df_2025
    
    def get_training_data(self, target_date: str, all_data: pd.DataFrame) -> pd.DataFrame:
        """
        獲取指定日期之前的所有訓練數據
        
        Args:
            target_date: 目標預測日期
            all_data: 完整的歷史數據
            
        Returns:
            訓練數據集
        """
        # 選擇目標日期之前的所有數據作為訓練集
        training_data = all_data[all_data['Sdate'] < target_date].copy()
        
        return training_data
    
    def predict_single_period(self, period: str, training_data: pd.DataFrame) -> Dict:
        """
        對單一期數進行預測
        
        Args:
            period: 期數
            training_data: 訓練數據
            
        Returns:
            預測結果
        """
        try:
            logger.info(f"開始預測期數 {period}，使用 {len(training_data)} 期歷史數據")
            
            # 創建增強板路分析器
            analyzer = EnhancedBoardPathAnalyzer(self.db_manager, self.lottery_type)
            
            # 執行增強分析預測
            prediction_result = analyzer.predict_with_enhanced_analysis(training_data, candidates_count=5)
            
            if not prediction_result:
                logger.warning(f"期數 {period} 預測失敗")
                return None
            
            # 創建最佳預測整合器
            integrator = BestPredictionIntegrator(self.db_manager)
            
            # 如果有候選預測，生成最佳整合預測
            best_prediction = None
            candidates = prediction_result.get('predictions', [])  # 修復: 使用 'predictions' 而不是 'candidates'
            if candidates:
                best_prediction = integrator.integrate_best_prediction(candidates, self.lottery_type)
            
            result = {
                'period': period,
                'training_periods': len(training_data),
                'prediction_time': datetime.now().isoformat(),
                'candidates': candidates,  # 修復: 使用已取得的 candidates 變量
                'best_prediction': best_prediction,
                'analysis': prediction_result.get('analysis', {}),
                'success': True
            }
            
            logger.info(f"期數 {period} 預測完成，生成 {len(result['candidates'])} 個候選")
            return result
            
        except Exception as e:
            logger.error(f"預測期數 {period} 時發生錯誤: {str(e)}")
            return {
                'period': period,
                'error': str(e),
                'success': False
            }
    
    def calculate_accuracy(self, prediction: Dict, actual_numbers: List[int], actual_special: int = None) -> Dict:
        """
        計算預測準確度
        
        Args:
            prediction: 預測結果
            actual_numbers: 實際開獎號碼
            actual_special: 實際特別號
            
        Returns:
            準確度分析
        """
        accuracy_results = {
            'candidates_accuracy': [],
            'best_prediction_accuracy': None,
            'max_matches': 0,
            'avg_matches': 0
        }
        
        try:
            # 分析候選預測準確度
            if 'candidates' in prediction and prediction['candidates']:
                total_matches = 0
                
                for i, candidate in enumerate(prediction['candidates']):
                    pred_numbers = candidate.get('main_numbers', [])
                    pred_special = candidate.get('special_number')
                    
                    # 計算主要號碼匹配
                    main_matches = len(set(pred_numbers) & set(actual_numbers))
                    
                    # 計算特別號匹配
                    special_match = 1 if (pred_special and actual_special and pred_special == actual_special) else 0
                    
                    total_matches_this_candidate = main_matches + (0.5 if special_match else 0)
                    total_matches += main_matches
                    
                    candidate_accuracy = {
                        'candidate_index': i,
                        'main_matches': main_matches,
                        'special_match': special_match,
                        'total_matches': total_matches_this_candidate,
                        'accuracy_score': main_matches / 6.0  # 基於6個主要號碼
                    }
                    
                    accuracy_results['candidates_accuracy'].append(candidate_accuracy)
                
                # 計算統計
                accuracy_results['max_matches'] = max([c['main_matches'] for c in accuracy_results['candidates_accuracy']])
                accuracy_results['avg_matches'] = total_matches / len(prediction['candidates'])
            
            # 分析最佳預測準確度
            if 'best_prediction' in prediction and prediction['best_prediction']:
                best_pred = prediction['best_prediction']
                
                if self.lottery_type == 'powercolor':
                    pred_main = best_pred.get('第一區', [])
                    pred_special = best_pred.get('第二區')
                    
                    main_matches = len(set(pred_main) & set(actual_numbers))
                    special_match = 1 if (pred_special and actual_special and pred_special == actual_special) else 0
                    
                    accuracy_results['best_prediction_accuracy'] = {
                        'main_matches': main_matches,
                        'special_match': special_match,
                        'total_matches': main_matches + (0.5 if special_match else 0),
                        'accuracy_score': main_matches / 6.0
                    }
            
        except Exception as e:
            logger.error(f"計算準確度時發生錯誤: {str(e)}")
        
        return accuracy_results
    
    def run_full_backtest(self) -> Dict:
        """執行完整的2025年歷史回測"""
        logger.info("開始執行2025年完整歷史回測")
        
        # 載入所有歷史數據
        all_historical_data = self.db_manager.load_lottery_data(self.lottery_type)
        
        # 載入2025年期數
        periods_2025 = self.load_2025_periods()
        
        # 結果統計
        total_periods = len(periods_2025)
        successful_predictions = 0
        failed_predictions = 0
        
        all_results = []
        
        for index, row in periods_2025.iterrows():
            period = str(row['Period'])
            target_date = row['Sdate']
            
            # 提取實際開獎號碼
            actual_numbers = []
            for i in range(1, 7):  # 威力彩6個主要號碼
                col = f'Anumber{i}'
                if col in row and pd.notna(row[col]):
                    actual_numbers.append(int(row[col]))
            
            actual_special = None
            if 'PowerBall' in row and pd.notna(row['PowerBall']):
                actual_special = int(row['PowerBall'])
            
            # 獲取該期之前的訓練數據
            training_data = self.get_training_data(target_date, all_historical_data)
            
            if len(training_data) < 50:  # 確保有足夠的訓練數據
                logger.warning(f"期數 {period} 訓練數據不足 ({len(training_data)}期)，跳過")
                continue
            
            # 進行預測
            logger.info(f"正在預測第 {index+1}/{total_periods} 期: {period}")
            prediction = self.predict_single_period(period, training_data)
            
            if prediction and prediction.get('success'):
                # 計算準確度
                accuracy = self.calculate_accuracy(prediction, actual_numbers, actual_special)
                
                # 整合結果
                result = {
                    'period': period,
                    'date': target_date.strftime('%Y-%m-%d'),
                    'actual_numbers': actual_numbers,
                    'actual_special': actual_special,
                    'prediction': prediction,
                    'accuracy': accuracy,
                    'training_periods': len(training_data)
                }
                
                all_results.append(result)
                successful_predictions += 1
                
                # 輸出進度
                max_matches = accuracy['max_matches']
                avg_matches = accuracy['avg_matches']
                logger.info(f"期數 {period} 完成: 最高匹配={max_matches}, 平均匹配={avg_matches:.1f}")
                
            else:
                failed_predictions += 1
                logger.error(f"期數 {period} 預測失敗")
        
        # 計算總體統計
        overall_stats = self.calculate_overall_statistics(all_results)
        
        # 準備最終報告
        final_report = {
            'backtest_info': {
                'lottery_type': self.lottery_type,
                'year': 2025,
                'total_periods': total_periods,
                'successful_predictions': successful_predictions,
                'failed_predictions': failed_predictions,
                'success_rate': successful_predictions / total_periods if total_periods > 0 else 0,
                'completion_time': datetime.now().isoformat()
            },
            'overall_statistics': overall_stats,
            'detailed_results': all_results
        }
        
        # 保存結果
        self.save_results(final_report)
        
        logger.info(f"歷史回測完成！成功預測 {successful_predictions}/{total_periods} 期")
        return final_report
    
    def calculate_overall_statistics(self, results: List[Dict]) -> Dict:
        """計算總體統計"""
        if not results:
            return {}
        
        # 收集所有準確度數據
        all_max_matches = []
        all_avg_matches = []
        all_best_matches = []
        
        match_distribution = {0: 0, 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0}
        
        for result in results:
            accuracy = result['accuracy']
            
            max_matches = accuracy['max_matches']
            avg_matches = accuracy['avg_matches']
            
            all_max_matches.append(max_matches)
            all_avg_matches.append(avg_matches)
            
            # 統計匹配數分佈
            match_distribution[max_matches] += 1
            
            # 最佳預測匹配
            if accuracy.get('best_prediction_accuracy'):
                best_matches = accuracy['best_prediction_accuracy']['main_matches']
                all_best_matches.append(best_matches)
        
        stats = {
            'total_predictions': len(results),
            'average_max_matches': np.mean(all_max_matches) if all_max_matches else 0,
            'average_avg_matches': np.mean(all_avg_matches) if all_avg_matches else 0,
            'best_single_prediction': max(all_max_matches) if all_max_matches else 0,
            'match_distribution': match_distribution,
            'hit_rates': {
                '1+': sum(1 for m in all_max_matches if m >= 1) / len(all_max_matches) if all_max_matches else 0,
                '2+': sum(1 for m in all_max_matches if m >= 2) / len(all_max_matches) if all_max_matches else 0,
                '3+': sum(1 for m in all_max_matches if m >= 3) / len(all_max_matches) if all_max_matches else 0,
                '4+': sum(1 for m in all_max_matches if m >= 4) / len(all_max_matches) if all_max_matches else 0,
                '5+': sum(1 for m in all_max_matches if m >= 5) / len(all_max_matches) if all_max_matches else 0,
                '6': sum(1 for m in all_max_matches if m == 6) / len(all_max_matches) if all_max_matches else 0,
            }
        }
        
        if all_best_matches:
            stats['best_prediction_stats'] = {
                'average_matches': np.mean(all_best_matches),
                'max_matches': max(all_best_matches),
                'hit_rates': {
                    '3+': sum(1 for m in all_best_matches if m >= 3) / len(all_best_matches),
                    '4+': sum(1 for m in all_best_matches if m >= 4) / len(all_best_matches),
                    '5+': sum(1 for m in all_best_matches if m >= 5) / len(all_best_matches),
                    '6': sum(1 for m in all_best_matches if m == 6) / len(all_best_matches),
                }
            }
        
        return stats
    
    def save_results(self, report: Dict):
        """保存回測結果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存完整報告
        full_report_path = os.path.join(self.results_dir, f'historical_backtest_2025_full_{timestamp}.json')
        with open(full_report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        # 保存摘要報告
        summary_report = {
            'backtest_info': report['backtest_info'],
            'overall_statistics': report['overall_statistics']
        }
        
        summary_report_path = os.path.join(self.results_dir, f'historical_backtest_2025_summary_{timestamp}.json')
        with open(summary_report_path, 'w', encoding='utf-8') as f:
            json.dump(summary_report, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"結果已保存至:")
        logger.info(f"完整報告: {full_report_path}")
        logger.info(f"摘要報告: {summary_report_path}")
    
    def print_summary(self, report: Dict):
        """打印摘要結果"""
        info = report['backtest_info']
        stats = report['overall_statistics']
        
        print("\n" + "="*60)
        print("🎯 2025年威力彩歷史回測結果摘要")
        print("="*60)
        print(f"📊 總期數: {info['total_periods']}")
        print(f"✅ 成功預測: {info['successful_predictions']}")
        print(f"❌ 失敗預測: {info['failed_predictions']}")
        print(f"📈 成功率: {info['success_rate']:.1%}")
        print()
        
        if stats:
            print("🎯 準確度統計:")
            print(f"   平均最高匹配: {stats['average_max_matches']:.2f}")
            print(f"   最佳單次預測: {stats['best_single_prediction']} 個匹配")
            print()
            
            print("📊 命中率:")
            for level, rate in stats['hit_rates'].items():
                print(f"   {level} 匹配: {rate:.1%}")
            print()
            
            print("📈 匹配數分佈:")
            for matches, count in stats['match_distribution'].items():
                print(f"   {matches} 個匹配: {count} 次")
        
        print("="*60)

def main():
    """主函數"""
    backtest = Historical2025Backtest()
    
    try:
        # 執行完整回測
        report = backtest.run_full_backtest()
        
        # 打印摘要
        backtest.print_summary(report)
        
    except KeyboardInterrupt:
        logger.info("用戶中斷執行")
    except Exception as e:
        logger.error(f"執行過程中發生錯誤: {str(e)}")
        raise

if __name__ == "__main__":
    main()