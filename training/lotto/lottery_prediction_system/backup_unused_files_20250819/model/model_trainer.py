"""
彩票預測系統 - 模型訓練模組
負責訓練和保存機器學習模型，支援威力彩、大樂透和今彩539
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.svm import SVR
from sklearn.neural_network import MLPRegressor
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.impute import SimpleImputer
import matplotlib.pyplot as plt
import os
import pickle
import logging
import time
import json

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/model_trainer.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('model_trainer')

class ModelTrainer:
    def __init__(self, model_dir='models'):
        """初始化模型訓練器"""
        self.model_dir = model_dir
        self.models = {
            'powercolor': {
                'first_area_models': [],
                'second_area_model': None
            },
            'lotto649': {
                'first_area_models': [],
                'special_number_model': None
            },
            'dailycash': {
                'number_models': []
            }
        }
        self.scalers = {}
        self.model_info = {}
        
        # 確保模型目錄存在
        if not os.path.exists(model_dir):
            os.makedirs(model_dir)
    
    def train_models(self, features_df, lottery_type='powercolor', test_size=0.2, model_type='random_forest', 
                     tune_hyperparams=False):
        """訓練模型

        Args:
            features_df: 包含特徵和目標的DataFrame
            lottery_type: 彩票類型 ('powercolor'=威力彩, 'lotto649'=大樂透, 'dailycash'=今彩539)
            test_size: 測試集比例
            model_type: 模型類型 ('random_forest', 'gradient_boosting', 'svm', 'mlp')
            tune_hyperparams: 是否進行超參數調優
            
        Returns:
            字典，包含模型評估結果
        """
        logger.info(f"開始訓練{self._get_lottery_name(lottery_type)}模型 (類型: {model_type})...")
        start_time = time.time()
        
    # 檢查數據集大小
        if len(features_df) <= 5:
            logger.warning(f"數據集太小 ({len(features_df)} 筆記錄)，無法訓練可靠的模型。請增加數據量。")
            # 返回一個空的模型信息
            return {
                'model_type': model_type,
                'error': '數據集太小，無法訓練模型'
            }
        
        # 根據彩票類型選擇不同的訓練方法
        if lottery_type.lower() == 'powercolor':
            return self._train_powercolor_models(features_df, test_size, model_type, tune_hyperparams)
        elif lottery_type.lower() == 'lotto649':
            return self._train_lotto649_models(features_df, test_size, model_type, tune_hyperparams)
        elif lottery_type.lower() == 'dailycash':
            return self._train_dailycash_models(features_df, test_size, model_type, tune_hyperparams)
        else:
            logger.error(f"不支援的彩票類型: {lottery_type}")
            return None
    
    def _train_powercolor_models(self, features_df, test_size, model_type, tune_hyperparams):
        """訓練威力彩模型"""
        # 添加這一行來初始化計時器      
        start_time = time.time()
        # 設置目標列名稱
        first_area_cols = ['Target_A1', 'Target_A2', 'Target_A3', 'Target_A4', 'Target_A5', 'Target_A6']
        second_area_col = 'Target_S'
        
        # 處理特徵和目標變量
        target_cols = first_area_cols + [second_area_col]
        if 'Period' in features_df.columns:
            target_cols.append('Period')
            
        X = features_df.drop(target_cols, axis=1, errors='ignore')
        y_first = features_df[first_area_cols]
        y_second = features_df[second_area_col]
        
        logger.info(f"特徵形狀: {X.shape}, 第一區目標形狀: {y_first.shape}, 第二區目標形狀: {y_second.shape}")
        
        # 檢查數據集大小，如果太小則特殊處理
        if len(X) <= 5:  # 根據實際情況調整閾值
            logger.warning(f"數據集太小 ({len(X)} 筆記錄)，無法進行正常的訓練/測試分割，將使用所有數據進行訓練")
            # 使用所有數據進行訓練
            X_train = X
            X_test = X
            y_first_train = y_first
            y_first_test = y_first
            y_second_train = y_second
            y_second_test = y_second
        else:
            # 檢查並處理缺失值
            X = self._handle_missing_values(X)

            # 分割訓練集和測試集
            X_train, X_test, y_first_train, y_first_test, y_second_train, y_second_test = train_test_split(
                X, y_first, y_second, test_size=test_size, random_state=42
            )

        # 標準化特徵
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = self._handle_missing_values(pd.DataFrame(scaler.transform(X_test), columns=X_test.columns))
        self.scalers['powercolor'] = scaler

        # 選擇模型類型
        base_model, param_grid = self._get_model_and_params(model_type, tune_hyperparams)
        
        # 訓練模型
        self.models['powercolor']['first_area_models'] = []  # 清空之前的模型
        first_area_metrics = []
        
        # 對第一區的每個目標列分別訓練模型
        for i, col in enumerate(first_area_cols):
            logger.info(f"訓練第一區第 {i+1} 個號碼模型 ({col})...")
            
            target_y = y_first_train[col]
            
            if tune_hyperparams and len(X) > 5:  # 只有在數據集足夠大時才進行超參數調優
                model = GridSearchCV(
                    base_model.__class__(**base_model.get_params()), 
                    param_grid, 
                    cv=min(5, len(X)),  # 確保交叉驗證折數不超過樣本數
                    scoring='neg_mean_squared_error', 
                    n_jobs=-1
                )
                model.fit(X_train_scaled, target_y)
                fitted_model = model.best_estimator_
                logger.info(f"{col} 最佳參數: {model.best_params_}")
            else:
                fitted_model = base_model.__class__(**base_model.get_params())
                fitted_model.fit(X_train_scaled, target_y)
            
            self.models['powercolor']['first_area_models'].append(fitted_model)
            
            # 評估模型
            y_pred = fitted_model.predict(X_test_scaled)
            mse = mean_squared_error(y_first_test[col], y_pred)
            rmse = np.sqrt(mse)
            r2 = r2_score(y_first_test[col], y_pred)
            
            first_area_metrics.append({
                'column': col,
                'mse': mse,
                'rmse': rmse,
                'r2': r2
            })
            
            logger.info(f"{col} - RMSE: {rmse:.4f}, R²: {r2:.4f}")
        
        # 訓練第二區模型
        logger.info("訓練第二區模型...")
        
        if tune_hyperparams and len(X) > 5:  # 只有在數據集足夠大時才進行超參數調優
            second_area_model = GridSearchCV(
                base_model.__class__(**base_model.get_params()), 
                param_grid, 
                cv=min(5, len(X)),  # 確保交叉驗證折數不超過樣本數
                scoring='neg_mean_squared_error', 
                n_jobs=-1
            )
            second_area_model.fit(X_train_scaled, y_second_train)
            self.models['powercolor']['second_area_model'] = second_area_model.best_estimator_
            logger.info(f"第二區最佳參數: {second_area_model.best_params_}")
        else:
            self.models['powercolor']['second_area_model'] = base_model.__class__(**base_model.get_params())
            self.models['powercolor']['second_area_model'].fit(X_train_scaled, y_second_train)
        
        # 評估第二區模型
        y_second_pred = self.models['powercolor']['second_area_model'].predict(X_test_scaled)
        second_area_mse = mean_squared_error(y_second_test, y_second_pred)
        second_area_rmse = np.sqrt(second_area_mse)
        second_area_r2 = r2_score(y_second_test, y_second_pred)
        
        logger.info(f"第二區 - RMSE: {second_area_rmse:.4f}, R²: {second_area_r2:.4f}")
        
        # 計算預測命中率
        # 對於第一區，我們計算實際號碼與預測號碼的重疊數量
        hit_counts = []
        
        # 為每個測試樣本進行預測
        for i in range(len(X_test_scaled)):
            # 預測第一區的6個號碼
            pred_numbers = set()
            
            # 對每個號碼模型進行預測並排序
            model_preds = {}
            
            # 確保 X_test_scaled 是 DataFrame 而不是 numpy 數組
            if isinstance(X_test_scaled, np.ndarray):
                x_sample = X_test_scaled[i].reshape(1, -1)
            else:
                # 確保正確處理 DataFrame 的索引方式
                try:
                    x_sample = X_test_scaled.iloc[i].values.reshape(1, -1)
                except:
                    x_sample = X_test_scaled.values[i].reshape(1, -1)
            
            for j, model in enumerate(self.models['powercolor']['first_area_models']):
                pred = model.predict(x_sample)[0]
                model_preds[j + 1] = pred  # 使用1-38的編號
            
            # 選擇預測值最高的6個不同號碼
            sorted_preds = sorted(model_preds.items(), key=lambda x: x[1], reverse=True)
            for num, _ in sorted_preds[:6]:
                pred_numbers.add(num)
            
            # 如果沒有6個不同的號碼，補充
            remaining = 6 - len(pred_numbers)
            if remaining > 0:
                for num in range(1, 39):
                    if num not in pred_numbers and remaining > 0:
                        pred_numbers.add(num)
                        remaining -= 1
            
            # 獲取實際號碼
            # 根據 y_first_test 的類型來選擇正確的索引方式
            if isinstance(y_first_test, pd.DataFrame):
                try:
                    actual_numbers = set(y_first_test.iloc[i])
                except:
                    actual_numbers = set(y_first_test.values[i])
            else:
                actual_numbers = set(y_first_test[i])
            
            # 計算重疊數量
            overlap_count = len(actual_numbers.intersection(pred_numbers))
            hit_counts.append(overlap_count)
        
        # 計算各種命中水平的比例
        hit_levels = {
            '命中6個': sum(1 for x in hit_counts if x == 6) / max(1, len(hit_counts)),
            '命中5個或以上': sum(1 for x in hit_counts if x >= 5) / max(1, len(hit_counts)),
            '命中4個或以上': sum(1 for x in hit_counts if x >= 4) / max(1, len(hit_counts)),
            '命中3個或以上': sum(1 for x in hit_counts if x >= 3) / max(1, len(hit_counts)),
            '平均命中數': sum(hit_counts) / max(1, len(hit_counts))
        }
        
        # 第二區命中率
        second_area_hits = []
        for i in range(len(y_second_test)):
            # 根據 y_second_test 的類型來選擇正確的索引方式
            if isinstance(y_second_test, pd.Series):
                try:
                    actual = y_second_test.iloc[i]
                except:
                    actual = y_second_test.values[i]
            else:
                actual = y_second_test[i]
                
            pred = round(y_second_pred[i])  # 四捨五入到最接近的整數
            if 1 <= pred <= 8:  # 確保預測在有效範圍內
                second_area_hits.append(1 if pred == actual else 0)
            else:
                second_area_hits.append(0)
        
        second_hit_rate = sum(second_area_hits) / max(1, len(second_area_hits))
        
        # 計算模型的特徵重要性（如果支持）
        feature_importance = self._get_feature_importance(self.models['powercolor']['first_area_models'][0], X)
        
        # 繪製特徵重要性圖表
        if feature_importance:
            self._plot_feature_importance(feature_importance, 'powercolor')
        
        # 記錄模型資訊
        # 計算第一區平均指標
        avg_first_area_metrics = {
            'mse': np.mean([m['mse'] for m in first_area_metrics]),
            'rmse': np.mean([m['rmse'] for m in first_area_metrics]),
            'r2': np.mean([m['r2'] for m in first_area_metrics]),
            'hit_levels': hit_levels
        }
        
        self.model_info['powercolor'] = {
            'model_type': model_type,
            'feature_count': X.shape[1],
            'train_size': len(X_train),
            'test_size': len(X_test),
            'first_area_metrics': avg_first_area_metrics,
            'second_area_metrics': {
                'mse': second_area_mse,
                'rmse': second_area_rmse,
                'r2': second_area_r2,
                'hit_rate': second_hit_rate
            },
            'training_time': time.time() - start_time,
            'feature_importance': feature_importance
        }
        
        # 輸出訓練結果
        logger.info(f"威力彩模型訓練完成！總耗時: {self.model_info['powercolor']['training_time']:.1f}秒")
        logger.info(f"第一區 - 平均RMSE: {avg_first_area_metrics['rmse']:.4f}, 平均R²: {avg_first_area_metrics['r2']:.4f}")
        logger.info(f"第二區 - RMSE: {second_area_rmse:.4f}, R²: {second_area_r2:.4f}")
        logger.info(f"第一區平均命中數: {hit_levels['平均命中數']:.2f}, 命中率(3+): {hit_levels['命中3個或以上']:.2%}")
        logger.info(f"第二區命中率: {second_hit_rate:.2%}")
        
        return self.model_info['powercolor']
    
    
    def _train_lotto649_models(self, features_df, test_size, model_type, tune_hyperparams):
        """訓練大樂透模型"""
        # 添加這一行來初始化計時器      
        start_time = time.time()
        # 設置目標列名稱
        first_area_cols = ['Target_A1', 'Target_A2', 'Target_A3', 'Target_A4', 'Target_A5', 'Target_A6']
        special_col = 'Target_Special'
        
        # 處理特徵和目標變量
        target_cols = first_area_cols + [special_col]
        if 'Period' in features_df.columns:
            target_cols.append('Period')
            
        X = features_df.drop(target_cols, axis=1, errors='ignore')
        y_first = features_df[first_area_cols]
        y_special = features_df[special_col]
        
        logger.info(f"特徵形狀: {X.shape}, 第一區目標形狀: {y_first.shape}, 特別號目標形狀: {y_special.shape}")
        
        # 檢查並處理缺失值
        X = self._handle_missing_values(X)

        # 分割訓練集和測試集
        X_train, X_test, y_first_train, y_first_test, y_special_train, y_special_test = train_test_split(
            X, y_first, y_special, test_size=test_size, random_state=42
        )

        # 標準化特徵
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = self._handle_missing_values(pd.DataFrame(scaler.transform(X_test), columns=X_test.columns))
        self.scalers['lotto649'] = scaler

        # 選擇模型類型
        base_model, param_grid = self._get_model_and_params(model_type, tune_hyperparams)
        
        # 訓練模型
        self.models['lotto649']['first_area_models'] = []  # 清空之前的模型
        first_area_metrics = []
        
        # 對第一區的每個目標列分別訓練模型
        for i, col in enumerate(first_area_cols):
            logger.info(f"訓練大樂透第一區第 {i+1} 個號碼模型 ({col})...")
            
            target_y = y_first_train[col]
            
            if tune_hyperparams:
                model = GridSearchCV(
                    base_model.__class__(**base_model.get_params()), 
                    param_grid, 
                    cv=5, 
                    scoring='neg_mean_squared_error', 
                    n_jobs=-1
                )
                model.fit(X_train_scaled, target_y)
                fitted_model = model.best_estimator_
                logger.info(f"{col} 最佳參數: {model.best_params_}")
            else:
                fitted_model = base_model.__class__(**base_model.get_params())
                fitted_model.fit(X_train_scaled, target_y)
            
            self.models['lotto649']['first_area_models'].append(fitted_model)
            
            # 評估模型
            y_pred = fitted_model.predict(X_test_scaled)
            mse = mean_squared_error(y_first_test[col], y_pred)
            rmse = np.sqrt(mse)
            r2 = r2_score(y_first_test[col], y_pred)
            
            first_area_metrics.append({
                'column': col,
                'mse': mse,
                'rmse': rmse,
                'r2': r2
            })
            
            logger.info(f"{col} - RMSE: {rmse:.4f}, R²: {r2:.4f}")
        
        # 訓練特別號模型
        logger.info("訓練大樂透特別號模型...")
        
        if tune_hyperparams:
            special_model = GridSearchCV(
                base_model.__class__(**base_model.get_params()), 
                param_grid, 
                cv=5, 
                scoring='neg_mean_squared_error', 
                n_jobs=-1
            )
            special_model.fit(X_train_scaled, y_special_train)
            self.models['lotto649']['special_number_model'] = special_model.best_estimator_
            logger.info(f"特別號最佳參數: {special_model.best_params_}")
        else:
            self.models['lotto649']['special_number_model'] = base_model.__class__(**base_model.get_params())
            self.models['lotto649']['special_number_model'].fit(X_train_scaled, y_special_train)
        
        # 評估特別號模型
        y_special_pred = self.models['lotto649']['special_number_model'].predict(X_test_scaled)
        special_mse = mean_squared_error(y_special_test, y_special_pred)
        special_rmse = np.sqrt(special_mse)
        special_r2 = r2_score(y_special_test, y_special_pred)
        
        logger.info(f"特別號 - RMSE: {special_rmse:.4f}, R²: {special_r2:.4f}")
        
        # 計算預測命中率
        # 對於第一區，我們計算實際號碼與預測號碼的重疊數量
        hit_counts = []
        
        # 為每個測試樣本進行預測
        for i in range(len(X_test_scaled)):
            # 預測第一區的6個號碼
            pred_numbers = set()
            
            # 對每個號碼模型進行預測並排序
            model_preds = {}
            for j, model in enumerate(self.models['lotto649']['first_area_models']):
                pred = model.predict([X_test_scaled.iloc[i]])[0]
                model_preds[j + 1] = pred  # 使用1-49的編號
            
            # 選擇預測值最高的6個不同號碼
            sorted_preds = sorted(model_preds.items(), key=lambda x: x[1], reverse=True)
            for num, _ in sorted_preds[:6]:
                pred_numbers.add(num)
            
            # 如果沒有6個不同的號碼，補充
            remaining = 6 - len(pred_numbers)
            if remaining > 0:
                for num in range(1, 50):
                    if num not in pred_numbers and remaining > 0:
                        pred_numbers.add(num)
                        remaining -= 1
            
            # 獲取實際號碼
            actual_numbers = set(y_first_test.iloc[i])
            
            # 計算重疊數量
            overlap_count = len(actual_numbers.intersection(pred_numbers))
            hit_counts.append(overlap_count)
        
        # 計算各種命中水平的比例
        hit_levels = {
            '命中6個': sum(1 for x in hit_counts if x == 6) / len(hit_counts),
            '命中5個或以上': sum(1 for x in hit_counts if x >= 5) / len(hit_counts),
            '命中4個或以上': sum(1 for x in hit_counts if x >= 4) / len(hit_counts),
            '命中3個或以上': sum(1 for x in hit_counts if x >= 3) / len(hit_counts),
            '平均命中數': sum(hit_counts) / len(hit_counts)
        }
        
        # 特別號命中率
        special_hits = []
        for i in range(len(y_special_test)):
            actual = y_special_test.iloc[i]
            pred = round(y_special_pred[i])  # 四捨五入到最接近的整數
            if 1 <= pred <= 49:  # 確保預測在有效範圍內
                special_hits.append(1 if pred == actual else 0)
            else:
                special_hits.append(0)
        
        special_hit_rate = sum(special_hits) / len(special_hits)
        
        # 計算模型的特徵重要性（如果支持）
        feature_importance = self._get_feature_importance(self.models['lotto649']['first_area_models'][0], X)
        
        # 繪製特徵重要性圖表
        if feature_importance:
            self._plot_feature_importance(feature_importance, 'lotto649')
        
        # 記錄模型資訊
        # 計算第一區平均指標
        avg_first_area_metrics = {
            'mse': np.mean([m['mse'] for m in first_area_metrics]),
            'rmse': np.mean([m['rmse'] for m in first_area_metrics]),
            'r2': np.mean([m['r2'] for m in first_area_metrics]),
            'hit_levels': hit_levels
        }
        
        self.model_info['lotto649'] = {
            'model_type': model_type,
            'feature_count': X.shape[1],
            'train_size': len(X_train),
            'test_size': len(X_test),
            'first_area_metrics': avg_first_area_metrics,
            'special_metrics': {
                'mse': special_mse,
                'rmse': special_rmse,
                'r2': special_r2,
                'hit_rate': special_hit_rate
            },
            'training_time': time.time() - start_time,
            'feature_importance': feature_importance
        }
        
        # 輸出訓練結果
        logger.info(f"大樂透模型訓練完成！總耗時: {self.model_info['lotto649']['training_time']:.1f}秒")
        logger.info(f"第一區 - 平均RMSE: {avg_first_area_metrics['rmse']:.4f}, 平均R²: {avg_first_area_metrics['r2']:.4f}")
        logger.info(f"特別號 - RMSE: {special_rmse:.4f}, R²: {special_r2:.4f}")
        logger.info(f"第一區平均命中數: {hit_levels['平均命中數']:.2f}, 命中率(3+): {hit_levels['命中3個或以上']:.2%}")
        logger.info(f"特別號命中率: {special_hit_rate:.2%}")
        
        return self.model_info['lotto649']
    
    def _train_dailycash_models(self, features_df, test_size, model_type, tune_hyperparams):
        """訓練今彩539模型"""
        # 添加這一行來初始化計時器
        start_time = time.time()
        # 設置目標列名稱
        number_cols = ['Target_A1', 'Target_A2', 'Target_A3', 'Target_A4', 'Target_A5']
        
        # 處理特徵和目標變量
        target_cols = number_cols.copy()
        if 'Period' in features_df.columns:
            target_cols.append('Period')
            
        X = features_df.drop(target_cols, axis=1, errors='ignore')
        y_numbers = features_df[number_cols]
        
        logger.info(f"特徵形狀: {X.shape}, 號碼目標形狀: {y_numbers.shape}")
        
        # 檢查並處理缺失值
        X = self._handle_missing_values(X)

        # 分割訓練集和測試集
        X_train, X_test, y_numbers_train, y_numbers_test = train_test_split(
            X, y_numbers, test_size=test_size, random_state=42
        )

        # 標準化特徵
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = self._handle_missing_values(pd.DataFrame(scaler.transform(X_test), columns=X_test.columns))
        self.scalers['dailycash'] = scaler

        # 選擇模型類型
        base_model, param_grid = self._get_model_and_params(model_type, tune_hyperparams)
        
        # 訓練模型
        self.models['dailycash']['number_models'] = []  # 清空之前的模型
        number_metrics = []
        
        # 對每個號碼列分別訓練模型
        for i, col in enumerate(number_cols):
            logger.info(f"訓練今彩539第 {i+1} 個號碼模型 ({col})...")
            
            target_y = y_numbers_train[col]
            
            if tune_hyperparams:
                model = GridSearchCV(
                    base_model.__class__(**base_model.get_params()), 
                    param_grid, 
                    cv=5, 
                    scoring='neg_mean_squared_error', 
                    n_jobs=-1
                )
                model.fit(X_train_scaled, target_y)
                fitted_model = model.best_estimator_
                logger.info(f"{col} 最佳參數: {model.best_params_}")
            else:
                fitted_model = base_model.__class__(**base_model.get_params())
                fitted_model.fit(X_train_scaled, target_y)
            
            self.models['dailycash']['number_models'].append(fitted_model)
            
            # 評估模型
            y_pred = fitted_model.predict(X_test_scaled)
            mse = mean_squared_error(y_numbers_test[col], y_pred)
            rmse = np.sqrt(mse)
            r2 = r2_score(y_numbers_test[col], y_pred)
            
            number_metrics.append({
                'column': col,
                'mse': mse,
                'rmse': rmse,
                'r2': r2
            })
            
            logger.info(f"{col} - RMSE: {rmse:.4f}, R²: {r2:.4f}")
        
        # 計算預測命中率
        # 對於今彩539，我們計算實際號碼與預測號碼的重疊數量
        hit_counts = []
        
        # 為每個測試樣本進行預測
        for i in range(len(X_test_scaled)):
            # 預測5個號碼
            pred_numbers = set()
            
            # 對每個號碼模型進行預測並排序
            model_preds = {}
            for j, model in enumerate(self.models['dailycash']['number_models']):
                pred = model.predict([X_test_scaled.iloc[i]])[0]
                model_preds[j + 1] = pred  # 使用1-39的編號
            
            # 選擇預測值最高的5個不同號碼
            sorted_preds = sorted(model_preds.items(), key=lambda x: x[1], reverse=True)
            for num, _ in sorted_preds[:5]:
                pred_numbers.add(num)
            
            # 如果沒有5個不同的號碼，補充
            remaining = 5 - len(pred_numbers)
            if remaining > 0:
                for num in range(1, 40):
                    if num not in pred_numbers and remaining > 0:
                        pred_numbers.add(num)
                        remaining -= 1
            
            # 獲取實際號碼
            actual_numbers = set(y_numbers_test.iloc[i])
            
            # 計算重疊數量
            overlap_count = len(actual_numbers.intersection(pred_numbers))
            hit_counts.append(overlap_count)
        
        # 計算各種命中水平的比例
        hit_levels = {
            '命中5個': sum(1 for x in hit_counts if x == 5) / len(hit_counts),
            '命中4個或以上': sum(1 for x in hit_counts if x >= 4) / len(hit_counts),
            '命中3個或以上': sum(1 for x in hit_counts if x >= 3) / len(hit_counts),
            '命中2個或以上': sum(1 for x in hit_counts if x >= 2) / len(hit_counts),
            '平均命中數': sum(hit_counts) / len(hit_counts)
        }
        
        # 計算模型的特徵重要性（如果支持）
        feature_importance = self._get_feature_importance(self.models['dailycash']['number_models'][0], X)
        
        # 繪製特徵重要性圖表
        if feature_importance:
            self._plot_feature_importance(feature_importance, 'dailycash')
        
        # 記錄模型資訊
        # 計算平均指標
        avg_metrics = {
            'mse': np.mean([m['mse'] for m in number_metrics]),
            'rmse': np.mean([m['rmse'] for m in number_metrics]),
            'r2': np.mean([m['r2'] for m in number_metrics]),
            'hit_levels': hit_levels
        }
        
        self.model_info['dailycash'] = {
            'model_type': model_type,
            'feature_count': X.shape[1],
            'train_size': len(X_train),
            'test_size': len(X_test),
            'metrics': avg_metrics,
            'training_time': time.time() - start_time,
            'feature_importance': feature_importance
        }
        
        # 輸出訓練結果
        logger.info(f"今彩539模型訓練完成！總耗時: {self.model_info['dailycash']['training_time']:.1f}秒")
        logger.info(f"平均RMSE: {avg_metrics['rmse']:.4f}, 平均R²: {avg_metrics['r2']:.4f}")
        logger.info(f"平均命中數: {hit_levels['平均命中數']:.2f}, 命中率(3+): {hit_levels['命中3個或以上']:.2%}")
        
        return self.model_info['dailycash']
    
    def _handle_missing_values(self, X):
        """處理缺失值"""
        if X.isna().any().any():
            logger.info("檢測到特徵數據中有缺失值(NaN)，正在進行處理...")
            
            # 用中位數填充NaN值
            imputer = SimpleImputer(strategy='median')
            X_imputed = imputer.fit_transform(X)
            X = pd.DataFrame(X_imputed, columns=X.columns)
            
            logger.info(f"缺失值處理完成，剩餘 {len(X)} 筆數據")
        
        return X
    
    def _get_model_and_params(self, model_type, tune_hyperparams):
        """根據模型類型獲取基礎模型和超參數網格"""
        if model_type == 'random_forest':
            base_model = RandomForestRegressor(random_state=42)
            param_grid = {
                'n_estimators': [50, 100, 200] if tune_hyperparams else [100],
                'max_depth': [None, 10, 20] if tune_hyperparams else [None],
                'min_samples_split': [2, 5, 10] if tune_hyperparams else [2]
            }
        elif model_type == 'gradient_boosting':
            base_model = GradientBoostingRegressor(random_state=42)
            param_grid = {
                'n_estimators': [50, 100, 200] if tune_hyperparams else [100],
                'learning_rate': [0.01, 0.1, 0.2] if tune_hyperparams else [0.1],
                'max_depth': [3, 5, 7] if tune_hyperparams else [3]
            }
        elif model_type == 'svm':
            base_model = SVR()
            param_grid = {
                'C': [0.1, 1, 10] if tune_hyperparams else [1],
                'gamma': ['scale', 'auto'] if tune_hyperparams else ['scale'],
                'kernel': ['rbf', 'linear'] if tune_hyperparams else ['rbf']
            }
        elif model_type == 'mlp':
            base_model = MLPRegressor(random_state=42, max_iter=500)
            param_grid = {
                'hidden_layer_sizes': [(50,), (100,), (50, 50)] if tune_hyperparams else [(100,)],
                'alpha': [0.0001, 0.001, 0.01] if tune_hyperparams else [0.0001],
                'learning_rate': ['constant', 'adaptive'] if tune_hyperparams else ['constant']
            }
        else:
            raise ValueError(f"不支援的模型類型: {model_type}")
        
        return base_model, param_grid
    
    def _get_feature_importance(self, model, X):
        """獲取特徵重要性（如果模型支持）"""
        feature_importance = {}
        
        # 檢查模型是否有特徵重要性屬性
        if hasattr(model, 'feature_importances_'):
            # 獲取特徵重要性
            importances = model.feature_importances_
            feature_names = X.columns
            
            # 將特徵重要性排序並轉換為字典
            feature_importance = dict(sorted(zip(feature_names, importances), key=lambda x: x[1], reverse=True))
            
            # 轉換numpy類型為Python內置類型，以便於JSON序列化
            feature_importance = {k: float(v) for k, v in feature_importance.items()}
        else:
            # 對於不支持特徵重要性的模型（如MLP、SVM），返回空字典
            logger.info("無法獲取特徵重要性，模型不支持或未實現feature_importances_屬性")
        
        return feature_importance

    
    def _plot_feature_importance(self, feature_importance, lottery_type):
        """繪製特徵重要性圖表"""
        plt.figure(figsize=(12, 8))
        top_features = dict(list(feature_importance.items())[:15])
        plt.barh(list(top_features.keys()), list(top_features.values()))
        plt.xlabel('重要性')
        plt.title(f'{self._get_lottery_name(lottery_type)}前15個最重要的特徵')
        plt.gca().invert_yaxis()  # 反轉y軸，使最重要的特徵在頂部
        plt.tight_layout()
        
        # 確保目錄存在
        if not os.path.exists(self.model_dir):
            os.makedirs(self.model_dir)
            
        plt.savefig(os.path.join(self.model_dir, f'{lottery_type}_feature_importance.png'))
        logger.info(f"特徵重要性圖表已保存至 {os.path.join(self.model_dir, f'{lottery_type}_feature_importance.png')}")
        plt.close()
    
    def save_models(self, lottery_type='powercolor', version="v1.0"):
        """保存訓練好的模型
        
        Args:
            lottery_type: 彩票類型 ('powercolor'=威力彩, 'lotto649'=大樂透, 'dailycash'=今彩539)
            version: 模型版本
            
        Returns:
            bool: 是否成功保存模型
        """
        if lottery_type.lower() not in self.models:
            logger.error(f"沒有找到{self._get_lottery_name(lottery_type)}模型")
            return False
        # 添加模型類型到版本名
        model_type = self.model_info[lottery_type]['model_type']
        version_with_type = f"{version}_{model_type}"       
        try:
            # 確保模型目錄存在
            if not os.path.exists(self.model_dir):
                os.makedirs(self.model_dir)
                
            # 根據彩票類型保存模型
            if lottery_type.lower() == 'powercolor':
                # 保存第一區模型
                for i, model in enumerate(self.models['powercolor']['first_area_models']):
                    with open(os.path.join(self.model_dir, f'powercolor_first_area_model_{i+1}_{version}.pkl'), 'wb') as f:
                        pickle.dump(model, f)
                
                # 保存第二區模型
                with open(os.path.join(self.model_dir, f'powercolor_second_area_model_{version}.pkl'), 'wb') as f:
                    pickle.dump(self.models['powercolor']['second_area_model'], f)
                
                # 保存標準化器
                with open(os.path.join(self.model_dir, f'powercolor_scaler_{version}.pkl'), 'wb') as f:
                    pickle.dump(self.scalers['powercolor'], f)
                
            elif lottery_type.lower() == 'lotto649':
                # 保存第一區模型
                for i, model in enumerate(self.models['lotto649']['first_area_models']):
                    with open(os.path.join(self.model_dir, f'lotto649_first_area_model_{i+1}_{version}.pkl'), 'wb') as f:
                        pickle.dump(model, f)
                
                # 保存特別號模型
                with open(os.path.join(self.model_dir, f'lotto649_special_number_model_{version}.pkl'), 'wb') as f:
                    pickle.dump(self.models['lotto649']['special_number_model'], f)
                
                # 保存標準化器
                with open(os.path.join(self.model_dir, f'lotto649_scaler_{version}.pkl'), 'wb') as f:
                    pickle.dump(self.scalers['lotto649'], f)
                
            elif lottery_type.lower() == 'dailycash':
                # 保存號碼模型
                for i, model in enumerate(self.models['dailycash']['number_models']):
                    with open(os.path.join(self.model_dir, f'dailycash_number_model_{i+1}_{version}.pkl'), 'wb') as f:
                        pickle.dump(model, f)
                
                # 保存標準化器
                with open(os.path.join(self.model_dir, f'dailycash_scaler_{version}.pkl'), 'wb') as f:
                    pickle.dump(self.scalers['dailycash'], f)
            
            # 保存模型信息
            with open(os.path.join(self.model_dir, f'{lottery_type}_model_info_{version}.json'), 'w', encoding='utf-8') as f:
                # 轉換numpy類型為Python內置類型
                model_info_serializable = json.loads(
                    json.dumps(self.model_info[lottery_type], default=lambda obj: float(obj) if isinstance(obj, (np.float32, np.float64)) else obj)
                )
                json.dump(model_info_serializable, f, ensure_ascii=False, indent=4)
            
            logger.info(f"{self._get_lottery_name(lottery_type)}模型已保存到 {self.model_dir} (版本: {version})")
            return True
        
        except Exception as e:
            logger.error(f"保存{self._get_lottery_name(lottery_type)}模型時出錯: {str(e)}")
            return False
    
    def load_models(self, lottery_type='powercolor', version="v1.0"):
        """加載已訓練的模型
        
        Args:
            lottery_type: 彩票類型 ('powercolor'=威力彩, 'lotto649'=大樂透, 'dailycash'=今彩539)
            version: 模型版本
            
        Returns:
            bool: 是否成功加載模型
        """
        try:
            # 根據彩票類型加載模型
            if lottery_type.lower() == 'powercolor':
                # 清空當前模型
                self.models['powercolor']['first_area_models'] = []
                
                # 嘗試載入6個第一區模型
                for i in range(1, 7):
                    model_path = os.path.join(self.model_dir, f'powercolor_first_area_model_{i}_{version}.pkl')
                    if os.path.exists(model_path):
                        with open(model_path, 'rb') as f:
                            model = pickle.load(f)
                            self.models['powercolor']['first_area_models'].append(model)
                
                # 加載第二區模型
                with open(os.path.join(self.model_dir, f'powercolor_second_area_model_{version}.pkl'), 'rb') as f:
                    self.models['powercolor']['second_area_model'] = pickle.load(f)
                
                # 加載標準化器
                with open(os.path.join(self.model_dir, f'powercolor_scaler_{version}.pkl'), 'rb') as f:
                    self.scalers['powercolor'] = pickle.load(f)
                
            elif lottery_type.lower() == 'lotto649':
                # 清空當前模型
                self.models['lotto649']['first_area_models'] = []
                
                # 嘗試載入6個第一區模型
                for i in range(1, 7):
                    model_path = os.path.join(self.model_dir, f'lotto649_first_area_model_{i}_{version}.pkl')
                    if os.path.exists(model_path):
                        with open(model_path, 'rb') as f:
                            model = pickle.load(f)
                            self.models['lotto649']['first_area_models'].append(model)
                
                # 加載特別號模型
                with open(os.path.join(self.model_dir, f'lotto649_special_number_model_{version}.pkl'), 'rb') as f:
                    self.models['lotto649']['special_number_model'] = pickle.load(f)
                
                # 加載標準化器
                with open(os.path.join(self.model_dir, f'lotto649_scaler_{version}.pkl'), 'rb') as f:
                    self.scalers['lotto649'] = pickle.load(f)
                
            elif lottery_type.lower() == 'dailycash':
                # 清空當前模型
                self.models['dailycash']['number_models'] = []
                
                # 嘗試載入5個號碼模型
                for i in range(1, 6):
                    model_path = os.path.join(self.model_dir, f'dailycash_number_model_{i}_{version}.pkl')
                    if os.path.exists(model_path):
                        with open(model_path, 'rb') as f:
                            model = pickle.load(f)
                            self.models['dailycash']['number_models'].append(model)
                
                # 加載標準化器
                with open(os.path.join(self.model_dir, f'dailycash_scaler_{version}.pkl'), 'rb') as f:
                    self.scalers['dailycash'] = pickle.load(f)
            
            # 加載模型信息
            with open(os.path.join(self.model_dir, f'{lottery_type}_model_info_{version}.json'), 'r', encoding='utf-8') as f:
                self.model_info[lottery_type] = json.load(f)
            
            logger.info(f"已加載{self._get_lottery_name(lottery_type)}模型 (版本: {version})")
            return True
        
        except FileNotFoundError:
            logger.warning(f"找不到{self._get_lottery_name(lottery_type)}模型文件 (版本: {version})")
            return False
        
        except Exception as e:
            logger.error(f"加載{self._get_lottery_name(lottery_type)}模型時出錯: {str(e)}")
            return False
    
    def get_model_summary(self, lottery_type='powercolor'):
        """獲取模型摘要信息
        
        Args:
            lottery_type: 彩票類型 ('powercolor'=威力彩, 'lotto649'=大樂透, 'dailycash'=今彩539)
            
        Returns:
            str: 模型摘要信息
        """
        if lottery_type.lower() not in self.model_info:
            return f"沒有可用的{self._get_lottery_name(lottery_type)}模型信息"
        
        model_info = self.model_info[lottery_type]
        
        if lottery_type.lower() == 'powercolor':
            summary = [
                f"威力彩模型摘要",
                f"模型類型: {model_info.get('model_type', 'unknown')}",
                f"特徵數量: {model_info.get('feature_count', 0)}",
                f"訓練集大小: {model_info.get('train_size', 0)}",
                f"測試集大小: {model_info.get('test_size', 0)}",
                f"訓練耗時: {model_info.get('training_time', 0):.1f} 秒",
                "\n第一區性能:",
                f"  RMSE: {model_info.get('first_area_metrics', {}).get('rmse', 0):.4f}",
                f"  R²: {model_info.get('first_area_metrics', {}).get('r2', 0):.4f}",
                f"  平均命中數: {model_info.get('first_area_metrics', {}).get('hit_levels', {}).get('平均命中數', 0):.2f}",
                f"  命中率(3+): {model_info.get('first_area_metrics', {}).get('hit_levels', {}).get('命中3個或以上', 0):.2%}",
                "\n第二區性能:",
                f"  RMSE: {model_info.get('second_area_metrics', {}).get('rmse', 0):.4f}",
                f"  R²: {model_info.get('second_area_metrics', {}).get('r2', 0):.4f}",
                f"  命中率: {model_info.get('second_area_metrics', {}).get('hit_rate', 0):.2%}",
            ]
        elif lottery_type.lower() == 'lotto649':
            summary = [
                f"大樂透模型摘要",
                f"模型類型: {model_info.get('model_type', 'unknown')}",
                f"特徵數量: {model_info.get('feature_count', 0)}",
                f"訓練集大小: {model_info.get('train_size', 0)}",
                f"測試集大小: {model_info.get('test_size', 0)}",
                f"訓練耗時: {model_info.get('training_time', 0):.1f} 秒",
                "\n第一區性能:",
                f"  RMSE: {model_info.get('first_area_metrics', {}).get('rmse', 0):.4f}",
                f"  R²: {model_info.get('first_area_metrics', {}).get('r2', 0):.4f}",
                f"  平均命中數: {model_info.get('first_area_metrics', {}).get('hit_levels', {}).get('平均命中數', 0):.2f}",
                f"  命中率(3+): {model_info.get('first_area_metrics', {}).get('hit_levels', {}).get('命中3個或以上', 0):.2%}",
                "\n特別號性能:",
                f"  RMSE: {model_info.get('special_metrics', {}).get('rmse', 0):.4f}",
                f"  R²: {model_info.get('special_metrics', {}).get('r2', 0):.4f}",
                f"  命中率: {model_info.get('special_metrics', {}).get('hit_rate', 0):.2%}",
            ]
        elif lottery_type.lower() == 'dailycash':
            summary = [
                f"今彩539模型摘要",
                f"模型類型: {model_info.get('model_type', 'unknown')}",
                f"特徵數量: {model_info.get('feature_count', 0)}",
                f"訓練集大小: {model_info.get('train_size', 0)}",
                f"測試集大小: {model_info.get('test_size', 0)}",
                f"訓練耗時: {model_info.get('training_time', 0):.1f} 秒",
                "\n模型性能:",
                f"  RMSE: {model_info.get('metrics', {}).get('rmse', 0):.4f}",
                f"  R²: {model_info.get('metrics', {}).get('r2', 0):.4f}",
                f"  平均命中數: {model_info.get('metrics', {}).get('hit_levels', {}).get('平均命中數', 0):.2f}",
                f"  命中率(3+): {model_info.get('metrics', {}).get('hit_levels', {}).get('命中3個或以上', 0):.2%}",
            ]
        
        # 添加前10個最重要的特徵
        if model_info.get('feature_importance'):
            summary.append("\n前10個最重要的特徵:")
            for i, (feature, importance) in enumerate(list(model_info['feature_importance'].items())[:10]):
                summary.append(f"  {i+1}. {feature}: {importance:.4f}")
        
        return "\n".join(summary)
    
    def _get_lottery_name(self, lottery_type):
        """根據彩票類型獲取中文名稱"""
        name_map = {
            'powercolor': '威力彩',
            'lotto649': '大樂透',
            'dailycash': '今彩539'
        }
        return name_map.get(lottery_type.lower(), lottery_type)

# 測試代碼
if __name__ == "__main__":
    from db_manager import DBManager
    from feature_engineering import FeatureEngineer
    from config.config_manager import ConfigManager
    
    # 加載數據
    config_manager = ConfigManager()
    db = DBManager(config_manager=config_manager)
    
    # 威力彩
    print("\n=== 測試威力彩模型訓練 ===")
    powercolor_df = db.load_lottery_data('powercolor')
    
    # 創建特徵
    fe = FeatureEngineer()
    features_df = fe.create_basic_features(powercolor_df, 'powercolor')
    features_df = fe.create_advanced_features(powercolor_df, features_df, 'powercolor')
    
    # 訓練模型
    trainer = ModelTrainer()
    model_info = trainer.train_models(features_df, lottery_type='powercolor', model_type='random_forest')
    
    # 保存模型
    trainer.save_models(lottery_type='powercolor', version="v1.0")
    
    # 顯示模型摘要
    print(trainer.get_model_summary('powercolor'))