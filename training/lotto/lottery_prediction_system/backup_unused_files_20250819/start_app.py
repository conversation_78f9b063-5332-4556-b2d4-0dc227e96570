#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彩票預測系統 - 統一啟動腳本
整合版本，使用 http://localhost:7890啟動應用
"""

import os
import sys
import subprocess
import webbrowser
import time
import logging
from datetime import datetime
from pathlib import Path

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """設置日誌"""
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'logs/app_startup_{datetime.now().strftime("%Y%m%d")}.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger('app_startup')

def check_dependencies():
    """檢查並安裝必要的依賴"""
    required_packages = {
        'flask': 'flask',
        'pandas': 'pandas', 
        'numpy': 'numpy',
        'scikit-learn': 'sklearn',
        'schedule': 'schedule'
    }
    
    missing_packages = []
    
    print("🔍 檢查依賴包...")
    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"✅ {package_name} 已安裝")
        except ImportError:
            missing_packages.append(package_name)
            print(f"❌ {package_name} 未安裝")
    
    if missing_packages:
        print(f"\n📦 正在安裝缺少的包: {', '.join(missing_packages)}")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install"] + missing_packages, check=True)
            print("✅ 所有依賴包安裝完成")
        except subprocess.CalledProcessError as e:
            print(f"❌ 依賴包安裝失敗: {e}")
            return False
    
    return True

def create_directories():
    """創建必要的目錄"""
    directories = [
        'logs',
        'data', 
        'analysis_results',
        'test_results',
        'web/logs',
        'web/templates',
        'web/static'
    ]
    
    print("📁 檢查並創建必要目錄...")
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ 目錄 {directory} 已準備")

def check_database():
    """檢查數據庫連接"""
    try:
        from data.db_manager import DBManager
        from config_manager import ConfigManager
        
        config_manager = ConfigManager()
        db = DBManager(config_manager=config_manager)
        print("✅ 數據庫連接正常")
        return True
    except Exception as e:
        print(f"⚠️  數據庫連接失敗: {str(e)}")
        print("   系統將以基礎模式運行")
        return False

def check_port_available(port):
    """檢查端口是否可用"""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', port))
            return True
    except OSError:
        return False

def find_available_port(start_port=7890):
    """尋找可用端口"""
    for port in range(start_port, start_port + 100):
        if check_port_available(port):
            return port
    return None

def modify_app_port():
    """修改應用端口為可用端口"""
    # 尋找可用端口
    available_port = find_available_port(7890)
    
    if available_port is None:
        print("❌ 無法找到可用端口")
        return False, 7890
    
    if available_port != 7890:
        print(f"⚠️  端口 7890 被佔用，使用端口 {available_port}")
    
    app_file = os.path.join('web', 'app.py')
    
    try:
        with open(app_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替換端口配置
        import re
        content = re.sub(r'port: int = \d+', f'port: int = {available_port}', content)
        
        with open(app_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 應用端口已設置為 {available_port}")
        return True, available_port
    except Exception as e:
        print(f"❌ 修改端口配置失敗: {e}")
        return False, 7890

def open_browser_delayed(port):
    """延遲打開瀏覽器"""
    time.sleep(3)
    try:
        webbrowser.open(f'http://localhost:{port}')
        print("🌐 瀏覽器已打開")
    except Exception as e:
        print(f"⚠️  無法自動打開瀏覽器: {e}")
        print(f"   請手動訪問: http://localhost:{port}")

def start_web_app():
    """啟動Web應用"""
    print("\n🚀 啟動彩票預測系統...")
    print("=" * 60)
    
    # 設置日誌
    logger = setup_logging()
    logger.info("開始啟動彩票預測系統")
    
    # 檢查依賴
    if not check_dependencies():
        print("❌ 依賴檢查失敗，無法啟動應用")
        sys.exit(1)
    
    # 創建目錄
    create_directories()
    
    # 檢查數據庫
    db_status = check_database()
    
    # 修改端口配置
    port_success, app_port = modify_app_port()
    if not port_success:
        print("❌ 端口配置失敗，無法啟動應用")
        sys.exit(1)
    
    print("\n🌐 Web應用配置:")
    print(f"   📍 訪問地址: http://localhost:{app_port}")
    print(f"   📊 數據庫狀態: {'正常' if db_status else '基礎模式'}")
    print(f"   🔧 調試模式: 開啟")
    
    print("\n📋 功能介紹:")
    print("   📊 儀表板 - 系統狀態和統計")
    print("   🤖 分離式預測 - 機器學習 + 板路分析")
    print("   📈 預測記錄 - 歷史預測查看")
    print("   📋 分析報告 - 準確度分析")
    print("   🔢 號碼分析 - 數字組合分析")
    
    print("\n⚠️  按 Ctrl+C 停止服務器")
    print("=" * 60)
    
    # 延遲打開瀏覽器
    import threading
    browser_thread = threading.Thread(target=open_browser_delayed, args=(app_port,))
    browser_thread.daemon = True
    browser_thread.start()
    
    # 啟動Flask應用
    try:
        from web.app import app
        logger.info("Flask應用啟動中...")
        app.run(
            debug=True,
            host='localhost',
            port=app_port,
            use_reloader=False  # 避免重複啟動
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 Web應用已停止")
        logger.info("Web應用正常停止")
    except Exception as e:
        print(f"\n❌ 啟動Web應用時出錯: {str(e)}")
        logger.error(f"Web應用啟動失敗: {str(e)}", exc_info=True)
        print("\n🔧 故障排除:")
        print("  1. 確認所有依賴已安裝")
        print("  2. 檢查端口是7890否被佔用")
        print("  3. 查看logs目錄中的錯誤日誌")
        print("  4. 嘗試重新運行腳本")
        sys.exit(1)

if __name__ == "__main__":
    start_web_app()