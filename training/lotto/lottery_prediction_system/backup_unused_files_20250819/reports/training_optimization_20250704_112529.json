{"powercolor": {"lottery_type": "powercolor", "best_training_days": null, "best_accuracy": 0.0, "all_results": [{"lottery_type": "powercolor", "training_days": 30, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "powercolor", "training_days": 60, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "powercolor", "training_days": 90, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "powercolor", "training_days": 120, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "powercolor", "training_days": 150, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "powercolor", "training_days": 180, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "powercolor", "training_days": 210, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "powercolor", "training_days": 240, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "powercolor", "training_days": 270, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "powercolor", "training_days": 300, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}], "optimization_date": "2025-07-04T11:25:24.555185"}, "lotto649": {"lottery_type": "lotto649", "best_training_days": null, "best_accuracy": 0.0, "all_results": [{"lottery_type": "lotto649", "training_days": 30, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "lotto649", "training_days": 60, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "lotto649", "training_days": 90, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "lotto649", "training_days": 120, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "lotto649", "training_days": 150, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "lotto649", "training_days": 180, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "lotto649", "training_days": 210, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "lotto649", "training_days": 240, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "lotto649", "training_days": 270, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "lotto649", "training_days": 300, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}], "optimization_date": "2025-07-04T11:25:25.918607"}, "dailycash": {"lottery_type": "dailycash", "best_training_days": null, "best_accuracy": 0.0, "all_results": [{"lottery_type": "dailycash", "training_days": 30, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "dailycash", "training_days": 60, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "dailycash", "training_days": 90, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "dailycash", "training_days": 120, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "dailycash", "training_days": 150, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "dailycash", "training_days": 180, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "dailycash", "training_days": 210, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "dailycash", "training_days": 240, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "dailycash", "training_days": 270, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}, {"lottery_type": "dailycash", "training_days": 300, "avg_accuracy": 0.0, "valid_tests": 0, "total_tests": 30, "detailed_results": []}], "optimization_date": "2025-07-04T11:25:29.999935"}}