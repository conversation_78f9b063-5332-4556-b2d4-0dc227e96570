{"test_date": "2025-07-04T11:15:46.211470", "target_date": "2025-06-20", "training_periods": [30, 60, 90, 120, 180, 365, 730], "results": {"dailycash": {"30_days": {"status": "failed", "reason": "ufunc 'add' did not contain a loop with signature matching types (dtype('S8'), dtype('S8')) -> None", "training_records": 0}, "60_days": {"status": "failed", "reason": "ufunc 'add' did not contain a loop with signature matching types (dtype('S8'), dtype('S8')) -> None", "training_records": 0}, "90_days": {"status": "failed", "reason": "ufunc 'add' did not contain a loop with signature matching types (dtype('S8'), dtype('S8')) -> None", "training_records": 0}, "120_days": {"status": "failed", "reason": "ufunc 'add' did not contain a loop with signature matching types (dtype('S8'), dtype('S8')) -> None", "training_records": 0}, "180_days": {"status": "failed", "reason": "ufunc 'add' did not contain a loop with signature matching types (dtype('S8'), dtype('S8')) -> None", "training_records": 0}, "365_days": {"status": "failed", "reason": "ufunc 'add' did not contain a loop with signature matching types (dtype('S8'), dtype('S8')) -> None", "training_records": 0}, "730_days": {"status": "failed", "reason": "ufunc 'add' did not contain a loop with signature matching types (dtype('S8'), dtype('S8')) -> None", "training_records": 0}}}}