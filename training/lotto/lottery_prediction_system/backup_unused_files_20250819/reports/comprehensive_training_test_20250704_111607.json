{"test_date": "2025-07-04T11:16:07.263863", "target_date": "2025-06-20", "training_periods": [30, 60, 90, 120, 180, 365, 730], "results": {"dailycash": {"30_days": {"status": "failed", "reason": "Cannot convert non-finite values (NA or inf) to integer", "training_records": 0}, "60_days": {"status": "failed", "reason": "Cannot convert non-finite values (NA or inf) to integer", "training_records": 0}, "90_days": {"status": "failed", "reason": "Cannot convert non-finite values (NA or inf) to integer", "training_records": 0}, "120_days": {"status": "failed", "reason": "Cannot convert non-finite values (NA or inf) to integer", "training_records": 0}, "180_days": {"status": "failed", "reason": "Cannot convert non-finite values (NA or inf) to integer", "training_records": 0}, "365_days": {"status": "failed", "reason": "Cannot convert non-finite values (NA or inf) to integer", "training_records": 0}, "730_days": {"status": "failed", "reason": "Cannot convert non-finite values (NA or inf) to integer", "training_records": 0}}}}