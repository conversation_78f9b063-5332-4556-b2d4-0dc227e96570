#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彩票預測系統 - 統一健康檢查API端點
為Flask和FastAPI應用添加健康檢查功能
"""

import json
import time
import psutil
import sqlite3
import redis
import requests
from datetime import datetime
from flask import Flask, jsonify
from typing import Dict, Any

class SystemHealthChecker:
    """系統健康檢查器"""
    
    def __init__(self):
        self.start_time = time.time()
        self.version = "3.0.0"
        
    def get_uptime(self) -> str:
        """獲取系統運行時間"""
        uptime_seconds = time.time() - self.start_time
        
        days = int(uptime_seconds // 86400)
        hours = int((uptime_seconds % 86400) // 3600)
        minutes = int((uptime_seconds % 3600) // 60)
        
        if days > 0:
            return f"{days}d {hours}h {minutes}m"
        elif hours > 0:
            return f"{hours}h {minutes}m"
        else:
            return f"{minutes}m"
    
    def check_redis(self) -> Dict[str, Any]:
        """檢查Redis連接"""
        try:
            r = redis.Redis(host='127.0.0.1', port=6379, db=0, socket_timeout=5)
            r.ping()
            return {
                "status": "healthy",
                "response_time_ms": 0,  # Redis ping通常很快
                "info": "Redis連接正常"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "info": "Redis連接失敗"
            }
    
    def check_database(self) -> Dict[str, Any]:
        """檢查SQLite數據庫"""
        try:
            db_path = "/app/data/lottery.db"
            conn = sqlite3.connect(db_path, timeout=5)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' LIMIT 1")
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    "status": "healthy",
                    "tables_count": len(result) if result else 0,
                    "info": "數據庫連接正常"
                }
            else:
                return {
                    "status": "warning",
                    "info": "數據庫為空或未初始化"
                }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "info": "數據庫連接失敗"
            }
    
    def check_services(self) -> Dict[str, Any]:
        """檢查內部服務狀態"""
        services = {}
        
        # 檢查Flask服務
        try:
            response = requests.get("http://127.0.0.1:7890", timeout=5)
            services["flask"] = {
                "status": "healthy" if response.status_code == 200 else "unhealthy",
                "port": 7890,
                "response_code": response.status_code
            }
        except Exception as e:
            services["flask"] = {
                "status": "unhealthy",
                "port": 7890,
                "error": str(e)
            }
        
        # 檢查FastAPI服務
        try:
            response = requests.get("http://127.0.0.1:8000", timeout=5)
            services["fastapi"] = {
                "status": "healthy" if response.status_code == 200 else "unhealthy",
                "port": 8000,
                "response_code": response.status_code
            }
        except Exception as e:
            services["fastapi"] = {
                "status": "unhealthy",
                "port": 8000,
                "error": str(e)
            }
        
        return services
    
    def get_system_resources(self) -> Dict[str, Any]:
        """獲取系統資源使用情況"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 內存使用情況
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available_gb = round(memory.available / (1024**3), 2)
            
            # 磁盤使用情況
            disk = psutil.disk_usage('/app')
            disk_percent = round(disk.used / disk.total * 100, 2)
            disk_free_gb = round(disk.free / (1024**3), 2)
            
            return {
                "cpu": {
                    "usage_percent": cpu_percent,
                    "status": "healthy" if cpu_percent < 80 else "warning" if cpu_percent < 95 else "critical"
                },
                "memory": {
                    "usage_percent": memory_percent,
                    "available_gb": memory_available_gb,
                    "status": "healthy" if memory_percent < 80 else "warning" if memory_percent < 95 else "critical"
                },
                "disk": {
                    "usage_percent": disk_percent,
                    "free_gb": disk_free_gb,
                    "status": "healthy" if disk_percent < 80 else "warning" if disk_percent < 95 else "critical"
                }
            }
        except Exception as e:
            return {
                "error": str(e),
                "status": "unknown"
            }
    
    def get_health_status(self) -> Dict[str, Any]:
        """獲取完整的系統健康狀態"""
        # 檢查各個組件
        redis_status = self.check_redis()
        db_status = self.check_database()
        services_status = self.check_services()
        resources = self.get_system_resources()
        
        # 計算總體健康狀態
        unhealthy_components = []
        
        if redis_status["status"] != "healthy":
            unhealthy_components.append("redis")
        
        if db_status["status"] == "unhealthy":
            unhealthy_components.append("database")
        
        for service, status in services_status.items():
            if status["status"] != "healthy":
                unhealthy_components.append(service)
        
        # 檢查資源狀態
        if resources.get("cpu", {}).get("status") == "critical":
            unhealthy_components.append("cpu")
        if resources.get("memory", {}).get("status") == "critical":
            unhealthy_components.append("memory")
        if resources.get("disk", {}).get("status") == "critical":
            unhealthy_components.append("disk")
        
        overall_status = "healthy" if len(unhealthy_components) == 0 else "unhealthy"
        
        return {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "status": overall_status,
            "version": self.version,
            "uptime": self.get_uptime(),
            "components": {
                "redis": redis_status,
                "database": db_status,
                "services": services_status
            },
            "resources": resources,
            "unhealthy_components": unhealthy_components if unhealthy_components else None
        }

# 創建全局健康檢查器實例
health_checker = SystemHealthChecker()

def create_health_app():
    """創建健康檢查Flask應用"""
    app = Flask(__name__)
    
    @app.route('/health', methods=['GET'])
    def health():
        """健康檢查端點"""
        try:
            health_data = health_checker.get_health_status()
            status_code = 200 if health_data["status"] == "healthy" else 503
            return jsonify(health_data), status_code
        except Exception as e:
            return jsonify({
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "status": "error",
                "error": str(e),
                "version": health_checker.version
            }), 500
    
    @app.route('/health/simple', methods=['GET'])
    def health_simple():
        """簡單健康檢查端點"""
        try:
            health_data = health_checker.get_health_status()
            return health_data["status"], 200 if health_data["status"] == "healthy" else 503
        except Exception as e:
            return f"error: {str(e)}", 500
    
    return app

# FastAPI健康檢查端點
def add_health_routes_to_fastapi(app):
    """為FastAPI應用添加健康檢查路由"""
    
    @app.get("/health")
    async def health():
        """健康檢查端點"""
        try:
            health_data = health_checker.get_health_status()
            return health_data
        except Exception as e:
            return {
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "status": "error",
                "error": str(e),
                "version": health_checker.version
            }
    
    @app.get("/health/simple")
    async def health_simple():
        """簡單健康檢查端點"""
        try:
            health_data = health_checker.get_health_status()
            return {"status": health_data["status"]}
        except Exception as e:
            return {"status": "error", "error": str(e)}

if __name__ == "__main__":
    # 作為獨立健康檢查服務運行
    app = create_health_app()
    print("🏥 健康檢查服務啟動在端口 9999")
    app.run(host="0.0.0.0", port=9999, debug=False)