"""
改進版彩票資料重新下載工具

此程式用於重新下載所有歷史彩票開獎資料，適用於更換資料庫後的完整資料重建。
支援威力彩、大樂透和今彩539等彩票類型，並提供多種下載策略與錯誤恢復機制。
針對「查無資料」問題進行了特別優化。
"""

import os
import logging
import sqlite3
import json
import time
import traceback
import requests
from datetime import datetime, timedelta
import argparse
import concurrent.futures
import re
from bs4 import BeautifulSoup
import random

# 設置日誌目錄
if not os.path.exists('logs'):
    os.makedirs('logs')

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/lottery_redownloader_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('lottery_redownloader')

def parse_arguments():
    """解析命令行參數"""
    parser = argparse.ArgumentParser(description='改進版彩票資料重新下載工具')
    parser.add_argument('--lottery_type', choices=['powercolor', 'lotto649', 'dailycash', 'all'], default='all',
                      help='彩票類型: powercolor=威力彩, lotto649=大樂透, dailycash=今彩539, all=全部')
    parser.add_argument('--start_year', type=int, default=None, help='開始年份 (預設為最早年份)')
    parser.add_argument('--end_year', type=int, default=None, help='結束年份 (預設為當前年份)')
    parser.add_argument('--batch_size', type=int, default=10, help='每批次下載的期數 (預設10期)')
    parser.add_argument('--delay', type=float, default=2.0, help='每批次下載間隔的秒數 (預設2秒)')
    parser.add_argument('--threads', type=int, default=1, help='下載執行緒數量 (預設1，增加可能會導致被封鎖)')
    parser.add_argument('--new_db', action='store_true', help='建立全新的資料庫 (會刪除現有資料)')
    parser.add_argument('--method', choices=['api', 'web', 'auto'], default='auto', 
                      help='下載方法: api=使用API, web=網頁爬蟲, auto=自動選擇')
    parser.add_argument('--retry', type=int, default=3, help='失敗重試次數 (預設3次)')
    parser.add_argument('--export_csv', action='store_true', help='是否將下載的資料匯出為CSV檔案')
    parser.add_argument('--single_mode', action='store_true', help='使用單一期數查詢模式')
    parser.add_argument('--debug', action='store_true', help='啟用除錯模式')
    
    return parser.parse_args()

def get_lottery_name(lottery_type):
    """根據彩票類型獲取中文名稱"""
    name_map = {
        'powercolor': '威力彩',
        'lotto649': '大樂透',
        'dailycash': '今彩539'
    }
    return name_map.get(lottery_type.lower(), lottery_type)

def create_connection(db_path="lottery_data.db"):
    """建立 SQLite 資料庫連接"""
    try:
        # 確保資料庫目錄存在
        db_dir = os.path.dirname(db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
            
        return sqlite3.connect(db_path)
    except sqlite3.Error as e:
        logger.error(f"資料庫連接錯誤: {e}")
        raise

def setup_database(new_db=False):
    """設置資料庫，如果指定則建立全新的資料庫"""
    db_path = "lottery_data.db"
    
    # 如果要建立新資料庫且檔案已存在，則先刪除舊檔案
    if new_db and os.path.exists(db_path):
        try:
            os.remove(db_path)
            logger.info(f"已刪除現有資料庫檔案: {db_path}")
        except Exception as e:
            logger.error(f"刪除資料庫檔案時出錯: {e}")
            return False
    
    # 創建資料庫連接
    try:
        conn = create_connection(db_path)
        cursor = conn.cursor()
        
        # 創建必要的資料表
        tables = {
            'Powercolor': [
                ('ID', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                ('gameKind', 'TEXT'),
                ('Period', 'TEXT'),
                ('Sdate', 'TEXT'),
                ('Anumber1', 'TEXT'),
                ('Anumber2', 'TEXT'),
                ('Anumber3', 'TEXT'),
                ('Anumber4', 'TEXT'),
                ('Anumber5', 'TEXT'),
                ('Anumber6', 'TEXT'),
                ('Second_district', 'TEXT')
            ],
            'Lotto649': [
                ('ID', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                ('gameKind', 'TEXT'),
                ('Period', 'TEXT'),
                ('Sdate', 'TEXT'),
                ('Anumber1', 'TEXT'),
                ('Anumber2', 'TEXT'),
                ('Anumber3', 'TEXT'),
                ('Anumber4', 'TEXT'),
                ('Anumber5', 'TEXT'),
                ('Anumber6', 'TEXT'),
                ('SpecialNumber', 'TEXT')
            ],
            'DailyCash': [
                ('ID', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                ('gameKind', 'TEXT'),
                ('Period', 'TEXT'),
                ('Sdate', 'TEXT'),
                ('Anumber1', 'TEXT'),
                ('Anumber2', 'TEXT'),
                ('Anumber3', 'TEXT'),
                ('Anumber4', 'TEXT'),
                ('Anumber5', 'TEXT')
            ],
            'LotteryUpdateLog': [
                ('ID', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                ('LotteryType', 'TEXT'),
                ('LastUpdateTime', 'TEXT')
            ],
            'DownloadProgress': [
                ('ID', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                ('LotteryType', 'TEXT'),
                ('StartPeriod', 'TEXT'),
                ('EndPeriod', 'TEXT'), 
                ('LastCompletedPeriod', 'TEXT'),
                ('StartTime', 'TEXT'),
                ('LastUpdateTime', 'TEXT'),
                ('Status', 'TEXT')
            ],
            'FailedPeriods': [
                ('ID', 'INTEGER PRIMARY KEY AUTOINCREMENT'),
                ('LotteryType', 'TEXT'),
                ('Period', 'TEXT'),
                ('FailReason', 'TEXT'),
                ('FailTime', 'TEXT'),
                ('RetryCount', 'INTEGER')
            ]
        }
        
        for table_name, columns in tables.items():
            columns_sql = ', '.join([f'{col[0]} {col[1]}' for col in columns])
            cursor.execute(f"CREATE TABLE IF NOT EXISTS {table_name} ({columns_sql})")
            
            # 在期數欄位上創建索引以提高查詢效率
            if table_name in ['Powercolor', 'Lotto649', 'DailyCash']:
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{table_name}_Period ON {table_name} (Period)")
        
        conn.commit()
        logger.info(f"資料庫設置完成，{'建立了全新的' if new_db else '使用現有的'}資料庫")
        return True
    except Exception as e:
        logger.error(f"設置資料庫時出錯: {e}")
        logger.error(traceback.format_exc())
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def try_import_taiwan_lottery():
    """嘗試導入台灣彩券爬蟲模組"""
    try:
        from TaiwanLottery import TaiwanLotteryCrawler
        return TaiwanLotteryCrawler
    except ImportError:
        logger.warning("無法導入 TaiwanLottery 模組，將使用網頁爬蟲代替")
        return None

def get_lottery_first_year(lottery_type):
    """取得各彩種的起始年份"""
    first_years = {
        'powercolor': 2008,  # 威力彩改制後的起始年份約為2008年
        'lotto649': 2004,    # 大樂透約2004年開始
        'dailycash': 2004    # 今彩539約2004年開始
    }
    return first_years.get(lottery_type, 2004)

def get_lottery_official_name(lottery_type):
    """獲取彩票類型對應的官方代號"""
    official_names = {
        'powercolor': 'SUPERLOTTO638',  # 威力彩
        'lotto649': 'LOTTO649',        # 大樂透
        'dailycash': 'DAILYCASH'        # 今彩539
    }
    return official_names.get(lottery_type, lottery_type)

def generate_periods_with_date_range(lottery_type, start_year=None, end_year=None):
    """根據年份範圍生成期數和日期範圍，確保符合官方格式"""
    # 設定預設年份範圍
    if start_year is None:
        start_year = get_lottery_first_year(lottery_type)
    
    if end_year is None:
        end_year = datetime.now().year
    
    # 各彩種開獎頻率
    draw_frequency = {
        'powercolor': 2,  # 每週開獎2次，週一和週四
        'lotto649': 2,    # 每週開獎2次，週二和週五
        'dailycash': 7    # 每週開獎7次，每天一次
    }
    
    # 計算大約的期數範圍
    start_date = datetime(start_year, 1, 1)
    end_date = datetime(end_year, 12, 31)
    days_diff = (end_date - start_date).days
    weeks_diff = days_diff // 7
    
    # 根據頻率計算估計的期數量
    frequency = draw_frequency.get(lottery_type, 2)
    estimated_periods_count = weeks_diff * frequency + frequency
    
    # 建立期數格式
    periods = []
    
    # 根據彩券類型生成正確的期數格式
    if lottery_type == 'powercolor':  # 威力彩
        # 威力彩期數格式為 "nnnnnnn" (例如 "110000xx")
        for year in range(start_year, end_year + 1):
            tw_year = year - 1911  # 轉換為民國年
            for period_num in range(1, 110):  # 每年約104期
                # 威力彩期數格式示例: 11000001 (110年第1期)
                period_str = f"{tw_year:03d}{period_num:04d}"
                periods.append(period_str)
    
    elif lottery_type == 'lotto649':  # 大樂透
        # 大樂透期數格式為 "nnnnnnn" (例如 "110000xx")
        for year in range(start_year, end_year + 1):
            tw_year = year - 1911  # 轉換為民國年
            for period_num in range(1, 110):  # 每年約104期
                # 大樂透期數格式示例: 11000001 (110年第1期)
                period_str = f"{tw_year:03d}{period_num:04d}"
                periods.append(period_str)
    
    elif lottery_type == 'dailycash':  # 今彩539
        # 今彩539期數格式為 "nnnnnnn" (例如 "110000xx")
        for year in range(start_year, end_year + 1):
            tw_year = year - 1911  # 轉換為民國年
            for period_num in range(1, 370):  # 今彩539每年約365期
                # 今彩539期數格式示例: 11000001 (110年第1期)
                period_str = f"{tw_year:03d}{period_num:04d}"
                periods.append(period_str)
    
    logger.info(f"已生成 {lottery_type} 從 {start_year} 到 {end_year} 年的期數清單，共 {len(periods)} 期")
    logger.debug(f"期數格式示例: {periods[:5]}...")
    return periods

def period_exists(cursor, table, period):
    """檢查期數是否已存在於資料庫中"""
    query = f'SELECT COUNT(*) FROM {table} WHERE Period = ?'
    cursor.execute(query, (period,))
    count = cursor.fetchone()[0]
    return count > 0

def log_failed_period(lottery_type, period, reason):
    """記錄下載失敗的期數"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 檢查是否已有失敗記錄
        cursor.execute("SELECT RetryCount FROM FailedPeriods WHERE LotteryType = ? AND Period = ?", 
                     (lottery_type, period))
        result = cursor.fetchone()
        
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        if result:
            # 更新重試次數
            retry_count = result[0] + 1
            cursor.execute("""
                UPDATE FailedPeriods SET 
                    FailReason = ?, 
                    FailTime = ?, 
                    RetryCount = ?
                WHERE LotteryType = ? AND Period = ?
            """, (reason, now, retry_count, lottery_type, period))
        else:
            # 新增失敗記錄
            cursor.execute("""
                INSERT INTO FailedPeriods (LotteryType, Period, FailReason, FailTime, RetryCount)
                VALUES (?, ?, ?, ?, 1)
            """, (lottery_type, period, reason, now))
        
        conn.commit()
        logger.debug(f"已記錄 {lottery_type} 期數 {period} 的失敗原因: {reason}")
    except Exception as e:
        logger.error(f"記錄失敗期數時出錯: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'conn' in locals():
            conn.close()

def download_using_api(lottery_type, periods, retry_count=3, batch_size=10, delay=1.0, single_mode=False):
    """使用TaiwanLottery API下載彩票資料，優化期數處理"""
    TaiwanLotteryCrawler = try_import_taiwan_lottery()
    if TaiwanLotteryCrawler is None:
        logger.error("無法使用API方式下載，缺少必要的TaiwanLottery模組")
        return {'inserted': 0, 'skipped': 0, 'failed': len(periods), 'completed_periods': []}
    
    # 初始化爬蟲實例
    try:
        crawler = TaiwanLotteryCrawler()
    except Exception as e:
        logger.error(f"初始化彩券爬蟲時出錯: {str(e)}")
        return {'inserted': 0, 'skipped': 0, 'failed': len(periods), 'completed_periods': []}
    
    # 建立資料庫連接
    conn = create_connection()
    cursor = conn.cursor()
    
    # 設定各彩種對應的資料表名稱和下載方法
    lottery_config = {
        'powercolor': {
            'table': 'Powercolor',
            'method': getattr(crawler, 'super_lotto', None),
            'save_func': save_powercolor_data
        },
        'lotto649': {
            'table': 'Lotto649',
            'method': getattr(crawler, 'lotto649', None),
            'save_func': save_lotto649_data
        },
        'dailycash': {
            'table': 'DailyCash',
            'method': getattr(crawler, 'daily_cash', None),
            'save_func': save_daily_cash_data
        }
    }
    
    # 檢查是否所有需要的方法都存在
    for key, config in lottery_config.items():
        if config['method'] is None:
            logger.warning(f"TaiwanLottery模組不支援 {key} 的下載方法")
            if key == 'powercolor':
                lottery_config[key]['method'] = crawler.super_lotto
            elif key == 'lotto649':
                lottery_config[key]['method'] = crawler.lotto649
            elif key == 'dailycash':
                lottery_config[key]['method'] = crawler.daily_cash
    
    # 獲取對應的配置
    config = lottery_config.get(lottery_type)
    if not config:
        logger.error(f"未知的彩票類型: {lottery_type}")
        conn.close()
        return {'inserted': 0, 'skipped': 0, 'failed': len(periods), 'completed_periods': []}
    
    # 統計資訊
    stats = {'inserted': 0, 'skipped': 0, 'failed': 0, 'completed_periods': []}
    
    try:
        # 我們默認使用單一期數模式，這樣更可靠
        logger.info(f"使用單一期數模式下載 {lottery_type} 資料")
        
        for period in periods:
            # 檢查是否已存在
            if period_exists(cursor, config['table'], period):
                logger.info(f"{get_lottery_name(lottery_type)}期數 {period} 已存在，跳過")
                stats['skipped'] += 1
                stats['completed_periods'].append(period)
                continue
            
            # 嘗試單一期數下載
            success = False
            
            for attempt in range(retry_count):
                try:
                    logger.info(f"嘗試下載 {get_lottery_name(lottery_type)} 期數 {period}，第 {attempt+1} 次嘗試")
                    
                    # 使用正確的期數格式進行查詢
                    if hasattr(crawler, f"{config['method'].__name__}_by_period"):
                        # 如果支援直接按期數查詢
                        data = getattr(crawler, f"{config['method'].__name__}_by_period")(period)
                    else:
                        # 否則使用原來的方法，但只傳一個期數
                        data = config['method']([period])
                    
                    if data and len(data) > 0:
                        # 儲存資料
                        inserted, skipped = config['save_func'](cursor, data)
                        
                        if inserted > 0:
                            stats['inserted'] += inserted
                            stats['completed_periods'].append(period)
                            conn.commit()
                            logger.info(f"成功下載 {get_lottery_name(lottery_type)} 期數 {period}")
                            success = True
                            break
                    
                    # 如果沒有資料，可能是格式問題，嘗試其他格式
                    if not success and attempt == 0:
                        logger.warning(f"{get_lottery_name(lottery_type)} 期數 {period} 查無資料，嘗試調整格式")
                        
                        # 嘗試不同的期數格式
                        alt_formats = generate_alternative_period_formats(lottery_type, period)
                        for alt_period in alt_formats:
                            logger.debug(f"嘗試替代期數格式: {alt_period}")
                            
                            try:
                                if hasattr(crawler, f"{config['method'].__name__}_by_period"):
                                    alt_data = getattr(crawler, f"{config['method'].__name__}_by_period")(alt_period)
                                else:
                                    alt_data = config['method']([alt_period])
                                
                                if alt_data and len(alt_data) > 0:
                                    # 儲存資料
                                    alt_inserted, alt_skipped = config['save_func'](cursor, alt_data)
                                    
                                    if alt_inserted > 0:
                                        stats['inserted'] += alt_inserted
                                        stats['completed_periods'].append(period)  # 仍記錄原始期數
                                        conn.commit()
                                        logger.info(f"使用替代格式 {alt_period} 成功下載 {get_lottery_name(lottery_type)} 期數 {period}")
                                        success = True
                                        break
                            except Exception as alt_e:
                                logger.debug(f"嘗試替代格式 {alt_period} 失敗: {str(alt_e)}")
                    
                    # 如果嘗試替代格式後仍然失敗，等待後重試
                    if not success:
                        logger.warning(f"{get_lottery_name(lottery_type)} 期數 {period} 查無資料，等待後重試")
                        time.sleep(delay + random.uniform(0.5, 1.5))
                    
                except Exception as e:
                    logger.error(f"下載 {get_lottery_name(lottery_type)} 期數 {period} 時出錯: {str(e)}")
                    time.sleep(delay + random.uniform(0.5, 1.5))
            
            if not success:
                stats['failed'] += 1
                log_failed_period(lottery_type, period, "API查詢無結果")
            
            # 每個期數之間稍微延遲，避免請求過於頻繁
            time.sleep(delay * 0.5)
    
    except Exception as e:
        logger.error(f"下載過程中發生錯誤: {str(e)}")
        logger.error(traceback.format_exc())
        conn.rollback()
    finally:
        conn.close()
    
    return stats

def generate_alternative_period_formats(lottery_type, period):
    """生成多種期數格式供嘗試"""
    alternative_formats = []
    
    try:
        # 嘗試解析期數
        if len(period) >= 7:  # 假設期數長度至少為7位
            # 格式可能是 "1100001"，表示110年第1期
            year_part = period[:3]  # 取前3位作為年份
            num_part = period[3:]  # 取後面作為期數
            
            # 生成替代格式1: "YYY-NNN" (例如 "110-001")
            alt_format1 = f"{year_part}-{num_part}"
            alternative_formats.append(alt_format1)
            
            # 生成替代格式2: 去掉前導零 (例如 "110001" 而不是 "1100001")
            alt_format2 = f"{int(year_part)}{int(num_part)}"
            alternative_formats.append(alt_format2)
            
            # 生成替代格式3: "YYY/NNN" (例如 "110/001")
            alt_format3 = f"{year_part}/{num_part}"
            alternative_formats.append(alt_format3)
        
        # 如果期數格式已經包含'-'，生成不含'-'的版本
        if '-' in period:
            parts = period.split('-')
            if len(parts) == 2:
                year_part, num_part = parts
                # 生成替代格式4: 純數字格式 (例如 "1100001")
                alt_format4 = f"{year_part}{num_part.zfill(4)}"
                alternative_formats.append(alt_format4)
        
        # 針對威力彩、大樂透和今彩539的特殊格式嘗試
        if lottery_type == 'powercolor':
            # 威力彩特有格式
            if len(period) >= 7:
                # 格式可能是 "yyynnn" (例如 "110001")
                year_part = period[:3]
                num_part = period[3:]
                # 生成替代格式5: 特殊分隔格式 (例如 "110/001")
                alt_format5 = f"{year_part}/{num_part.zfill(3)}"
                alternative_formats.append(alt_format5)
        
        elif lottery_type == 'lotto649':
            # 大樂透特有格式
            if len(period) >= 7:
                # 格式可能與威力彩類似
                year_part = period[:3]
                num_part = period[3:]
                # 生成替代格式6: 特殊分隔格式
                alt_format6 = f"{year_part}/{num_part.zfill(3)}"
                alternative_formats.append(alt_format6)
        
        elif lottery_type == 'dailycash':
            # 今彩539特有格式
            if len(period) >= 7:
                # 格式可能不同
                year_part = period[:3]
                num_part = period[3:]
                # 生成替代格式7: 特殊格式
                alt_format7 = f"{year_part}/{num_part.zfill(3)}"
                alternative_formats.append(alt_format7)
    
    except Exception as e:
        logger.error(f"生成替代期數格式時出錯: {str(e)}")
    
    # 移除重複的格式
    return list(set(alternative_formats))    
    
def adjust_period_format(lottery_type, period):
    """調整期數格式以符合API需求"""
    try:
        if lottery_type == 'powercolor' or lottery_type == 'lotto649':
            # 如果期數格式為 "109-001"，嘗試轉換為 "10900001"
            if '-' in period:
                year, num = period.split('-')
                return f"{year}{num}"
            # 如果已經是純數字格式，直接返回
            return period
        elif lottery_type == 'dailycash':
            # 確保是純數字格式
            return period.replace('-', '')
        else:
            return period
    except Exception as e:
        logger.error(f"調整期數格式時出錯: {str(e)}")
        return period

def download_from_official_website(lottery_type, periods, retry_count=3, batch_size=10, delay=2.0):
    """從官方網站直接爬取彩票開獎資料"""
    lottery_name = get_lottery_name(lottery_type)
    logger.info(f"使用官方網站爬蟲下載 {lottery_name} 資料")
    
    # 建立資料庫連接
    conn = create_connection()
    cursor = conn.cursor()
    
    # 設定各彩種對應的資料表
    table_map = {
        'powercolor': 'Powercolor',
        'lotto649': 'Lotto649',
        'dailycash': 'DailyCash'
    }
    
    table_name = table_map.get(lottery_type)
    if not table_name:
        logger.error(f"未知的彩票類型: {lottery_type}")
        conn.close()
        return {'inserted': 0, 'skipped': 0, 'failed': len(periods), 'completed_periods': []}
    
    # 統計資訊
    stats = {'inserted': 0, 'skipped': 0, 'failed': 0, 'completed_periods': []}
    
    # 設置API URL和請求頭
    api_url = "https://www.taiwanlottery.com.tw/lotto/history.aspx"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    }
    
    # 設定彩種對應的查詢參數
    lottery_param = get_lottery_official_name(lottery_type)
    
    try:
        # 以單一期數方式爬取
        for period in periods:
            # 檢查是否已存在
            if period_exists(cursor, table_name, period):
                logger.info(f"{lottery_name}期數 {period} 已存在，跳過")
                stats['skipped'] += 1
                stats['completed_periods'].append(period)
                continue
            
            success = False
            
            for attempt in range(retry_count):
                try:
                    logger.info(f"嘗試從官網下載 {lottery_name} 期數 {period}，第 {attempt+1} 次嘗試")
                    
                    # 準備查詢參數
                    params = {
                        'type': lottery_param,
                        'period': period
                    }
                    
                    # 發送請求
                    response = requests.get(api_url, params=params, headers=headers, timeout=10)
                    
                    if response.status_code == 200:
                        # 解析HTML內容
                        draw_data = parse_lottery_html(response.text, lottery_type, period)
                        
                        if draw_data:
                            # 儲存資料
                            if lottery_type == 'powercolor':
                                inserted = save_powercolor_data_from_web(cursor, draw_data)
                            elif lottery_type == 'lotto649':
                                inserted = save_lotto649_data_from_web(cursor, draw_data)
                            elif lottery_type == 'dailycash':
                                inserted = save_daily_cash_data_from_web(cursor, draw_data)
                            
                            if inserted > 0:
                                stats['inserted'] += inserted
                                stats['completed_periods'].append(period)
                                conn.commit()
                                logger.info(f"成功從官網下載 {lottery_name} 期數 {period}")
                                success = True
                                break
                    
                    logger.warning(f"{lottery_name} 期數 {period} 從官網查無資料，等待後重試")
                    time.sleep(delay + random.uniform(0.5, 1.5))
                    
                except Exception as e:
                    logger.error(f"從官網下載 {lottery_name} 期數 {period} 時出錯: {str(e)}")
                    time.sleep(delay + random.uniform(0.5, 1.5))
            
            if not success:
                stats['failed'] += 1
                log_failed_period(lottery_type, period, "網頁爬蟲查詢無結果")
            
            # 每個期數之間稍微延遲
            time.sleep(delay * 0.8)
    
    except Exception as e:
        logger.error(f"從官網下載過程中發生錯誤: {str(e)}")
        logger.error(traceback.format_exc())
        conn.rollback()
    finally:
        conn.close()
    
    return stats

def parse_lottery_html(html_content, lottery_type, period):
    """解析彩票官網HTML內容，提取開獎資料"""
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 初始化開獎資料
        draw_data = {
            '期別': period,
            '開獎日期': ''
        }
        
        # 根據彩種類型進行不同的解析
        if lottery_type == 'powercolor':  # 威力彩
            # 尋找開獎日期
            date_elem = soup.select_one('.contents_box02 .tx_md.date')
            if date_elem:
                draw_data['開獎日期'] = date_elem.text.strip()
            
            # 找到第一區號碼
            first_area = []
            number_elems = soup.select('.contents_box02 .ball_blue')
            for elem in number_elems[:6]:  # 威力彩第一區有6個號碼
                if elem and elem.text.strip().isdigit():
                    first_area.append(int(elem.text.strip()))
            
            if len(first_area) == 6:
                draw_data['第一區'] = first_area
            
            # 找到第二區號碼
            second_area_elem = soup.select_one('.contents_box02 .ball_red')
            if second_area_elem and second_area_elem.text.strip().isdigit():
                draw_data['第二區'] = int(second_area_elem.text.strip())
        
        elif lottery_type == 'lotto649':  # 大樂透
            # 尋找開獎日期
            date_elem = soup.select_one('.contents_box02 .tx_md.date')
            if date_elem:
                draw_data['開獎日期'] = date_elem.text.strip()
            
            # 找到普通號碼
            numbers = []
            number_elems = soup.select('.contents_box02 .ball_yellow')
            for elem in number_elems[:6]:  # 大樂透有6個普通號碼
                if elem and elem.text.strip().isdigit():
                    numbers.append(int(elem.text.strip()))
            
            if len(numbers) == 6:
                draw_data['獎號'] = numbers
            
            # 找到特別號
            special_elem = soup.select_one('.contents_box02 .ball_red')
            if special_elem and special_elem.text.strip().isdigit():
                draw_data['特別號'] = int(special_elem.text.strip())
        
        elif lottery_type == 'dailycash':  # 今彩539
            # 尋找開獎日期
            date_elem = soup.select_one('.contents_box02 .tx_md.date')
            if date_elem:
                draw_data['開獎日期'] = date_elem.text.strip()
            
            # 找到號碼
            numbers = []
            number_elems = soup.select('.contents_box02 .ball_green')
            for elem in number_elems[:5]:  # 今彩539有5個號碼
                if elem and elem.text.strip().isdigit():
                    numbers.append(int(elem.text.strip()))
            
            if len(numbers) == 5:
                draw_data['獎號'] = numbers
        
        # 檢查是否成功解析到開獎資料
        if (lottery_type == 'powercolor' and '第一區' in draw_data and '第二區' in draw_data) or \
           (lottery_type == 'lotto649' and '獎號' in draw_data and '特別號' in draw_data) or \
           (lottery_type == 'dailycash' and '獎號' in draw_data):
            return draw_data
        else:
            logger.warning(f"無法從HTML中解析出完整的{get_lottery_name(lottery_type)}開獎資料")
            return None
        
    except Exception as e:
        logger.error(f"解析HTML時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        return None

def save_powercolor_data_from_web(cursor, draw_data):
    """儲存從網頁爬蟲獲取的威力彩資料"""
    try:
        period = draw_data.get('期別', '')
        
        # 檢查期數是否已存在
        if period_exists(cursor, 'Powercolor', period):
            logger.info(f"威力彩期數 {period} 已存在，跳過")
            return 0
        
        # 準備插入資料
        date = draw_data.get('開獎日期', '')
        numbers = draw_data.get('第一區', [0, 0, 0, 0, 0, 0])
        second_district = draw_data.get('第二區', 0)
        
        # 插入資料
        insert_query = '''
        INSERT INTO Powercolor (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, Second_district)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        
        cursor.execute(insert_query, (
            '威力彩',
            str(period),
            date,
            str(numbers[0]) if len(numbers) > 0 else '0',
            str(numbers[1]) if len(numbers) > 1 else '0',
            str(numbers[2]) if len(numbers) > 2 else '0',
            str(numbers[3]) if len(numbers) > 3 else '0',
            str(numbers[4]) if len(numbers) > 4 else '0',
            str(numbers[5]) if len(numbers) > 5 else '0',
            str(second_district)
        ))
        
        logger.info(f"成功插入威力彩期數 {period} 的資料")
        return 1
        
    except Exception as e:
        logger.error(f"儲存威力彩資料時出錯: {str(e)}")
        return 0

def save_lotto649_data_from_web(cursor, draw_data):
    """儲存從網頁爬蟲獲取的大樂透資料"""
    try:
        period = draw_data.get('期別', '')
        
        # 檢查期數是否已存在
        if period_exists(cursor, 'Lotto649', period):
            logger.info(f"大樂透期數 {period} 已存在，跳過")
            return 0
        
        # 準備插入資料
        date = draw_data.get('開獎日期', '')
        numbers = draw_data.get('獎號', [0, 0, 0, 0, 0, 0])
        special_number = draw_data.get('特別號', 0)
        
        # 插入資料
        insert_query = '''
        INSERT INTO Lotto649 (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, SpecialNumber)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        
        cursor.execute(insert_query, (
            '大樂透',
            str(period),
            date,
            str(numbers[0]) if len(numbers) > 0 else '0',
            str(numbers[1]) if len(numbers) > 1 else '0',
            str(numbers[2]) if len(numbers) > 2 else '0',
            str(numbers[3]) if len(numbers) > 3 else '0',
            str(numbers[4]) if len(numbers) > 4 else '0',
            str(numbers[5]) if len(numbers) > 5 else '0',
            str(special_number)
        ))
        
        logger.info(f"成功插入大樂透期數 {period} 的資料")
        return 1
        
    except Exception as e:
        logger.error(f"儲存大樂透資料時出錯: {str(e)}")
        return 0

def save_daily_cash_data_from_web(cursor, draw_data):
    """儲存從網頁爬蟲獲取的今彩539資料"""
    try:
        period = draw_data.get('期別', '')
        
        # 檢查期數是否已存在
        if period_exists(cursor, 'DailyCash', period):
            logger.info(f"今彩539期數 {period} 已存在，跳過")
            return 0
        
        # 準備插入資料
        date = draw_data.get('開獎日期', '')
        numbers = draw_data.get('獎號', [0, 0, 0, 0, 0])
        
        # 插入資料
        insert_query = '''
        INSERT INTO DailyCash (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        '''
        
        cursor.execute(insert_query, (
            '今彩539',
            str(period),
            date,
            str(numbers[0]) if len(numbers) > 0 else '0',
            str(numbers[1]) if len(numbers) > 1 else '0',
            str(numbers[2]) if len(numbers) > 2 else '0',
            str(numbers[3]) if len(numbers) > 3 else '0',
            str(numbers[4]) if len(numbers) > 4 else '0'
        ))
        
        logger.info(f"成功插入今彩539期數 {period} 的資料")
        return 1
        
    except Exception as e:
        logger.error(f"儲存今彩539資料時出錯: {str(e)}")
        return 0

def save_powercolor_data(cursor, data):
    """儲存威力彩資料到資料庫 (API 模式)"""
    inserted = 0
    skipped = 0
    
    for draw in data:
        try:
            period = draw.get('期別', '')
            if not period:
                continue
                
            # 跳過已存在的期數
            if period_exists(cursor, 'Powercolor', period):
                logger.info(f"威力彩期數 {period} 已存在，跳過")
                skipped += 1
                continue
                
            # 準備插入資料
            date = draw.get('開獎日期', '').split('T')[0] if '開獎日期' in draw else ''
            numbers = draw.get('第一區', [0, 0, 0, 0, 0, 0])
            second_district = draw.get('第二區', 0)
            
            # 插入資料
            insert_query = '''
            INSERT INTO Powercolor (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, Second_district)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            
            cursor.execute(insert_query, (
                '威力彩',
                str(period),
                date,
                str(numbers[0]) if len(numbers) > 0 else '0',
                str(numbers[1]) if len(numbers) > 1 else '0',
                str(numbers[2]) if len(numbers) > 2 else '0',
                str(numbers[3]) if len(numbers) > 3 else '0',
                str(numbers[4]) if len(numbers) > 4 else '0',
                str(numbers[5]) if len(numbers) > 5 else '0',
                str(second_district)
            ))
            
            inserted += 1
            logger.info(f"成功插入威力彩期數 {period} 的資料")
        except Exception as e:
            logger.error(f"處理威力彩資料期數 {draw.get('期別', '未知')} 時出錯: {str(e)}")
    
    return inserted, skipped

def save_lotto649_data(cursor, data):
    """儲存大樂透資料到資料庫 (API 模式)"""
    inserted = 0
    skipped = 0
    
    for draw in data:
        try:
            period = draw.get('期別', '')
            if not period:
                continue
                
            # 跳過已存在的期數
            if period_exists(cursor, 'Lotto649', period):
                logger.info(f"大樂透期數 {period} 已存在，跳過")
                skipped += 1
                continue
                
            # 準備插入資料
            date = draw.get('開獎日期', '').split('T')[0] if '開獎日期' in draw else ''
            numbers = draw.get('獎號', [0, 0, 0, 0, 0, 0])
            special_number = draw.get('特別號', 0)
            
            # 插入資料
            insert_query = '''
            INSERT INTO Lotto649 (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5, Anumber6, SpecialNumber)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            
            cursor.execute(insert_query, (
                '大樂透',
                str(period),
                date,
                str(numbers[0]) if len(numbers) > 0 else '0',
                str(numbers[1]) if len(numbers) > 1 else '0',
                str(numbers[2]) if len(numbers) > 2 else '0',
                str(numbers[3]) if len(numbers) > 3 else '0',
                str(numbers[4]) if len(numbers) > 4 else '0',
                str(numbers[5]) if len(numbers) > 5 else '0',
                str(special_number)
            ))
            
            inserted += 1
            logger.info(f"成功插入大樂透期數 {period} 的資料")
        except Exception as e:
            logger.error(f"處理大樂透資料期數 {draw.get('期別', '未知')} 時出錯: {str(e)}")
    
    return inserted, skipped

def save_daily_cash_data(cursor, data):
    """儲存今彩539資料到資料庫 (API 模式)"""
    inserted = 0
    skipped = 0
    
    for draw in data:
        try:
            period = draw.get('期別', '')
            if not period:
                continue
                
            # 跳過已存在的期數
            if period_exists(cursor, 'DailyCash', period):
                logger.info(f"今彩539期數 {period} 已存在，跳過")
                skipped += 1
                continue
                
            # 準備插入資料
            date = draw.get('開獎日期', '').split('T')[0] if '開獎日期' in draw else ''
            numbers = draw.get('獎號', [0, 0, 0, 0, 0])
            
            # 插入資料
            insert_query = '''
            INSERT INTO DailyCash (gameKind, Period, Sdate, Anumber1, Anumber2, Anumber3, Anumber4, Anumber5)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            '''
            
            cursor.execute(insert_query, (
                '今彩539',
                str(period),
                date,
                str(numbers[0]) if len(numbers) > 0 else '0',
                str(numbers[1]) if len(numbers) > 1 else '0',
                str(numbers[2]) if len(numbers) > 2 else '0',
                str(numbers[3]) if len(numbers) > 3 else '0',
                str(numbers[4]) if len(numbers) > 4 else '0'
            ))
            
            inserted += 1
            logger.info(f"成功插入今彩539期數 {period} 的資料")
        except Exception as e:
            logger.error(f"處理今彩539資料期數 {draw.get('期別', '未知')} 時出錯: {str(e)}")
    
    return inserted, skipped

def update_download_progress(lottery_type, start_period, end_period, last_completed_period, status):
    """更新下載進度記錄"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 檢查是否已有該彩種的下載記錄
        cursor.execute("SELECT COUNT(*) FROM DownloadProgress WHERE LotteryType = ?", (lottery_type,))
        exists = cursor.fetchone()[0] > 0
        
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        if exists:
            # 更新現有記錄
            query = """
            UPDATE DownloadProgress SET 
                StartPeriod = ?,
                EndPeriod = ?,
                LastCompletedPeriod = ?,
                LastUpdateTime = ?,
                Status = ?
            WHERE LotteryType = ?
            """
            cursor.execute(query, (
                start_period,
                end_period,
                last_completed_period if last_completed_period else '',
                now,
                status,
                lottery_type
            ))
        else:
            # 插入新記錄
            query = """
            INSERT INTO DownloadProgress 
                (LotteryType, StartPeriod, EndPeriod, LastCompletedPeriod, StartTime, LastUpdateTime, Status)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            cursor.execute(query, (
                lottery_type,
                start_period,
                end_period,
                last_completed_period if last_completed_period else '',
                now,
                now,
                status
            ))
        
        conn.commit()
        logger.info(f"已更新 {lottery_type} 的下載進度記錄")
    except Exception as e:
        logger.error(f"更新下載進度記錄時出錯: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'conn' in locals():
            conn.close()

def export_to_csv(lottery_type):
    """將資料庫中的彩票資料匯出為CSV檔案"""
    try:
        conn = create_connection()
        cursor = conn.cursor()
        
        # 建立匯出目錄
        export_dir = 'exported_data'
        if not os.path.exists(export_dir):
            os.makedirs(export_dir)
        
        # 獲取表名和欄位
        table_map = {
            'powercolor': ('Powercolor', '威力彩'),
            'lotto649': ('Lotto649', '大樂透'),
            'dailycash': ('DailyCash', '今彩539')
        }
        
        table_name, chinese_name = table_map.get(lottery_type, (None, None))
        if not table_name:
            logger.error(f"未知的彩票類型: {lottery_type}")
            return False
        
        # 取得欄位名稱
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [row[1] for row in cursor.fetchall()]
        
        # 查詢所有資料
        cursor.execute(f"SELECT * FROM {table_name} ORDER BY Period")
        rows = cursor.fetchall()
        
        if not rows:
            logger.warning(f"{chinese_name}資料表中沒有資料可匯出")
            return False
        
        # 建立CSV檔案
        filename = f"{export_dir}/{lottery_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        with open(filename, 'w', encoding='utf-8') as f:
            # 寫入標頭
            f.write(','.join(columns) + '\n')
            
            # 寫入資料
            for row in rows:
                f.write(','.join([str(item) for item in row]) + '\n')
        
        logger.info(f"已將 {chinese_name} 資料匯出至 {filename}，共 {len(rows)} 筆記錄")
        return True
    
    except Exception as e:
        logger.error(f"匯出 {lottery_type} 資料時出錯: {str(e)}")
        logger.error(traceback.format_exc())
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def download_with_threads(lottery_types, periods_dict, method, batch_size, delay, threads, retry_count, single_mode):
    """使用多執行緒下載多種彩票資料"""
    if threads <= 1:
        # 單執行緒模式
        results = {}
        for lottery_type in lottery_types:
            logger.info(f"開始下載 {get_lottery_name(lottery_type)} 資料...")
            update_download_progress(
                lottery_type, 
                periods_dict[lottery_type][0] if periods_dict[lottery_type] else '',
                periods_dict[lottery_type][-1] if periods_dict[lottery_type] else '',
                None,
                'starting'
            )
            
            if method == 'api':
                result = download_using_api(
                    lottery_type, 
                    periods_dict[lottery_type], 
                    retry_count, 
                    batch_size, 
                    delay,
                    single_mode
                )
            elif method == 'web':
                result = download_from_official_website(
                    lottery_type, 
                    periods_dict[lottery_type], 
                    retry_count, 
                    batch_size, 
                    delay
                )
            else:  # auto
                # 先嘗試API，如果失敗則使用網頁爬蟲
                logger.info(f"嘗試使用API下載 {get_lottery_name(lottery_type)} 資料")
                result = download_using_api(
                    lottery_type, 
                    periods_dict[lottery_type], 
                    1,  # 只嘗試一次 
                    batch_size, 
                    delay,
                    single_mode
                )
                
                # 檢查API是否成功下載了資料
                if result['inserted'] == 0 and result['failed'] > 0:
                    logger.info(f"API下載失敗，切換為網頁爬蟲下載 {get_lottery_name(lottery_type)} 資料")
                    result = download_from_official_website(
                        lottery_type, 
                        periods_dict[lottery_type], 
                        retry_count, 
                        batch_size, 
                        delay
                    )
            
            update_download_progress(
                lottery_type, 
                periods_dict[lottery_type][0] if periods_dict[lottery_type] else '',
                periods_dict[lottery_type][-1] if periods_dict[lottery_type] else '',
                result['completed_periods'][-1] if result['completed_periods'] else None,
                'completed'
            )
            
            results[lottery_type] = result
        
        return results
    else:
        # 多執行緒模式
        results = {}
        
        def download_task(lottery_type):
            logger.info(f"執行緒開始下載 {get_lottery_name(lottery_type)} 資料...")
            update_download_progress(
                lottery_type, 
                periods_dict[lottery_type][0] if periods_dict[lottery_type] else '',
                periods_dict[lottery_type][-1] if periods_dict[lottery_type] else '',
                None,
                'starting'
            )
            
            if method == 'api':
                result = download_using_api(
                    lottery_type, 
                    periods_dict[lottery_type], 
                    retry_count, 
                    batch_size, 
                    delay,
                    single_mode
                )
            elif method == 'web':
                result = download_from_official_website(
                    lottery_type, 
                    periods_dict[lottery_type], 
                    retry_count, 
                    batch_size, 
                    delay
                )
            else:  # auto
                # 先嘗試API，如果失敗則使用網頁爬蟲
                result = download_using_api(
                    lottery_type, 
                    periods_dict[lottery_type], 
                    1,  # 只嘗試一次
                    batch_size, 
                    delay,
                    single_mode
                )
                
                if result['inserted'] == 0 and result['failed'] > 0:
                    result = download_from_official_website(
                        lottery_type, 
                        periods_dict[lottery_type], 
                        retry_count, 
                        batch_size, 
                        delay
                    )
            
            update_download_progress(
                lottery_type, 
                periods_dict[lottery_type][0] if periods_dict[lottery_type] else '',
                periods_dict[lottery_type][-1] if periods_dict[lottery_type] else '',
                result['completed_periods'][-1] if result['completed_periods'] else None,
                'completed'
            )
            
            return (lottery_type, result)
        
        # 使用執行緒池進行並行下載
        with concurrent.futures.ThreadPoolExecutor(max_workers=min(threads, len(lottery_types))) as executor:
            # 提交所有下載任務
            future_to_lottery = {
                executor.submit(download_task, lottery_type): lottery_type 
                for lottery_type in lottery_types
            }
            
            # 當任務完成時收集結果
            for future in concurrent.futures.as_completed(future_to_lottery):
                lottery_type = future_to_lottery[future]
                try:
                    lottery_type, result = future.result()
                    results[lottery_type] = result
                    logger.info(f"{get_lottery_name(lottery_type)} 下載任務完成")
                except Exception as e:
                    logger.error(f"{get_lottery_name(lottery_type)} 下載任務失敗: {str(e)}")
                    logger.error(traceback.format_exc())
        
        return results

def display_download_summary(results):
    """顯示下載結果摘要"""
    print("\n" + "="*60)
    print("彩票資料下載摘要")
    print("="*60)
    
    total_inserted = 0
    total_skipped = 0
    total_failed = 0
    
    for lottery_type, result in results.items():
        lottery_name = get_lottery_name(lottery_type)
        inserted = result.get('inserted', 0)
        skipped = result.get('skipped', 0)
        failed = result.get('failed', 0)
        
        total_inserted += inserted
        total_skipped += skipped
        total_failed += failed
        
        print(f"{lottery_name}:")
        print(f"  成功下載: {inserted} 筆")
        print(f"  已存在跳過: {skipped} 筆")
        print(f"  下載失敗: {failed} 筆")
        
        if result.get('completed_periods'):
            latest_period = result['completed_periods'][-1] if result['completed_periods'] else "無"
            print(f"  最新完成期數: {latest_period}")
        
        print("-"*60)
    
    print(f"總計:")
    print(f"  成功下載: {total_inserted} 筆")
    print(f"  已存在跳過: {total_skipped} 筆")
    print(f"  下載失敗: {total_failed} 筆")
    print("="*60)

def main():
    """主函數"""
    start_time = datetime.now()
    print("\n" + "="*60)
    print("改進版彩票資料重新下載工具")
    print("="*60)
    print("開始時間:", start_time.strftime("%Y-%m-%d %H:%M:%S"))
    print("="*60)
    
    # 解析命令行參數
    args = parse_arguments()
    
    # 設置日誌級別
    if args.debug:
        logger.setLevel(logging.DEBUG)
        logger.info("已啟用除錯模式")
    
    # 設置資料庫
    if not setup_database(args.new_db):
        logger.error("設置資料庫失敗，程式終止")
        return
    
    # 確定要下載的彩票類型
    lottery_types = []
    if args.lottery_type == 'all':
        lottery_types = ['powercolor', 'lotto649', 'dailycash']
    else:
        lottery_types = [args.lottery_type]
    
    # 為每種彩票類型生成期數
    periods_dict = {}
    for lottery_type in lottery_types:
        periods = generate_periods_with_date_range(lottery_type, args.start_year, args.end_year)
        periods_dict[lottery_type] = periods
    
    # 開始下載
    try:
        print("\n開始下載彩票資料...")
        print(f"使用下載方法: {args.method}")
        if args.single_mode:
            print("已啟用單一期數查詢模式")
        
        results = download_with_threads(
            lottery_types, 
            periods_dict, 
            args.method,
            args.batch_size, 
            args.delay, 
            args.threads,
            args.retry,
            args.single_mode
        )
        
        # 顯示下載結果摘要
        display_download_summary(results)
        
        # 如果需要，匯出為CSV
        if args.export_csv:
            print("\n正在匯出資料為CSV檔案...")
            for lottery_type in lottery_types:
                if export_to_csv(lottery_type):
                    print(f"已匯出 {get_lottery_name(lottery_type)} 資料")
                else:
                    print(f"匯出 {get_lottery_name(lottery_type)} 資料失敗")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("\n" + "="*60)
        print("下載完成!")
        print("="*60)
        print(f"開始時間: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"結束時間: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"總耗時: {duration}")
        print("="*60)
        
    except Exception as e:
        logger.error(f"下載過程中發生錯誤: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"\n錯誤: 下載過程中發生錯誤: {str(e)}")
        print("請查看日誌檔案以獲取詳細信息")
    
    logger.info("程式執行完成")

if __name__ == "__main__":
    main()