#!/usr/bin/env python3
"""
今日彩票预测生成器
支持：威力彩、大乐透、今彩539
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from prediction.enhanced_prediction_system import EnhancedPredictionSystem
from prediction.core_number_refiner import CoreNumberRefiner
from prediction.accuracy_tracker import AccuracyTracker

def convert_numpy_types(data):
    """递归转换numpy类型为Python原生类型"""
    if isinstance(data, dict):
        return {key: convert_numpy_types(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [convert_numpy_types(item) for item in data]
    elif isinstance(data, np.ndarray):
        return data.tolist()
    elif isinstance(data, np.integer):
        return int(data)
    elif isinstance(data, np.floating):
        return float(data)
    elif hasattr(data, 'dtype'):  # 其他numpy类型
        if hasattr(data, 'tolist'):
            return data.tolist()
        elif 'int' in str(data.dtype):
            return int(data)
        elif 'float' in str(data.dtype):
            return float(data)
        else:
            return str(data)
    else:
        return data

# 彩票类型配置
LOTTERY_CONFIGS = {
    'powercolor': {
        'name': '威力彩',
        'main_range': (1, 38),
        'main_count': 6,
        'special_range': (1, 8),
        'special_count': 1,
        'draw_days': ['一', '四'],  # 周一、周四
        'description': '从01-38选6个号码，第二区从01-08选1个号码'
    },
    'super_lotto': {
        'name': '大乐透',
        'main_range': (1, 49),
        'main_count': 6,
        'special_range': (1, 10),
        'special_count': 1,
        'draw_days': ['二', '五'],  # 周二、周五
        'description': '从01-49选6个号码，特别号从01-10选1个号码'
    },
    'daily539': {
        'name': '今彩539',
        'main_range': (1, 39),
        'main_count': 5,
        'special_range': None,  # 今彩539没有特别号
        'special_count': 0,
        'draw_days': ['一', '二', '三', '四', '五', '六'],  # 每天开奖（除周日）
        'description': '从01-39选5个号码'
    }
}

class TodayPredictionGenerator:
    """今日预测生成器"""
    
    def __init__(self):
        """初始化生成器"""
        self.configs = LOTTERY_CONFIGS
        self.today = datetime.now()
        self.systems = {}
        self.refiners = {}
        self.tracker = AccuracyTracker()
        
    def initialize_systems(self):
        """初始化所有彩票类型的预测系统"""
        print("🔧 初始化预测系统...")
        
        for lottery_type in self.configs.keys():
            try:
                # 创建预测系统
                self.systems[lottery_type] = EnhancedPredictionSystem(lottery_type)
                # 创建精炼器
                self.refiners[lottery_type] = CoreNumberRefiner(lottery_type)
                print(f"   ✅ {self.configs[lottery_type]['name']}系统初始化成功")
            except Exception as e:
                print(f"   ⚠️ {self.configs[lottery_type]['name']}系统初始化失败: {e}")
                
    def load_historical_data(self, lottery_type):
        """加载历史数据"""
        # 尝试加载真实历史数据
        data_file = f"data/{lottery_type}_history.csv"
        
        if os.path.exists(data_file):
            print(f"   📊 加载历史数据: {data_file}")
            return pd.read_csv(data_file)
        else:
            print(f"   ⚠️ 未找到历史数据，生成模拟数据...")
            return self.generate_mock_data(lottery_type)
            
    def generate_mock_data(self, lottery_type):
        """生成模拟历史数据"""
        config = self.configs[lottery_type]
        periods = 100
        
        data = []
        for i in range(periods):
            # 生成主号码
            main_numbers = sorted(np.random.choice(
                range(config['main_range'][0], config['main_range'][1] + 1),
                config['main_count'],
                replace=False
            ))
            
            row = {
                'Period': f'2024{str(i+1).zfill(6)}',
                'Date': (self.today - timedelta(days=periods-i)).strftime('%Y-%m-%d')
            }
            
            # 添加主号码
            for j, num in enumerate(main_numbers, 1):
                row[f'Anumber{j}'] = num
            
            # 添加特别号（如果有）
            if config['special_range']:
                row['Snumber'] = np.random.randint(
                    config['special_range'][0], 
                    config['special_range'][1] + 1
                )
            
            data.append(row)
            
        return pd.DataFrame(data)
    
    def generate_prediction(self, lottery_type, strategy='balanced'):
        """生成单个彩票类型的预测"""
        config = self.configs[lottery_type]
        
        print(f"\n{'='*60}")
        print(f"🎲 {config['name']} - 预测生成")
        print(f"{'='*60}")
        
        # 检查是否今天开奖 - 允许演示模式
        weekday = ['一', '二', '三', '四', '五', '六', '日'][self.today.weekday()]
        if weekday not in config['draw_days']:
            print(f"   ℹ️ {config['name']}今天（周{weekday}）不开奖，生成演示预测")
            print(f"   下次开奖日: 周{config['draw_days'][0]}")
            # 继续生成演示预测，不返回None
            
        print(f"   📅 开奖日期: {self.today.strftime('%Y-%m-%d')} (周{weekday})")
        print(f"   📝 规则: {config['description']}")
        
        # 加载历史数据
        historical_data = self.load_historical_data(lottery_type)
        
        # 生成多组预测
        print(f"\n   🔄 生成{strategy}策略预测...")
        
        try:
            # 生成多组预测
            system = self.systems.get(lottery_type)
            if not system:
                print(f"   ❌ {config['name']}系统未初始化")
                return None
                
            multi_result = system.generate_comprehensive_prediction(
                historical_data,
                strategy=strategy,
                enable_multi_group=True,
                next_draw_date=self.today.strftime('%Y-%m-%d')
            )
            
            if not multi_result or 'prediction_data' not in multi_result:
                print(f"   ❌ 预测生成失败")
                return None
                
            # 显示多组预测
            groups = multi_result['prediction_data']['groups']
            print(f"\n   📊 生成了{len(groups)}组预测:")
            for i, group in enumerate(groups, 1):
                main_nums = group['main_numbers'][:config['main_count']]  # 确保正确数量
                print(f"      第{i}组: {main_nums}")
                print(f"            信心度: {group['confidence']:.1f}%")
                print(f"            风险等级: {group['risk_level']}")
            
            # 精炼核心号码
            print(f"\n   🎯 精炼核心号码...")
            refiner = self.refiners.get(lottery_type)
            if not refiner:
                print(f"   ❌ {config['name']}精炼器未初始化")
                return None
                
            core_result = refiner.refine_core_numbers(
                multi_result['prediction_data'],
                historical_data,
                target_count=config['main_count']
            )
            
            if not core_result:
                print(f"   ❌ 核心号码精炼失败")
                return None
            
            # 显示核心预测结果
            print(f"\n   🎯 {'='*40}")
            print(f"   🌟 最终核心预测:")
            print(f"   {'='*40}")
            
            final_numbers = sorted(core_result['core_numbers'][:config['main_count']])
            print(f"   主号码: {final_numbers}")
            
            # 生成特别号（如果需要）
            if config['special_range']:
                special_number = np.random.randint(
                    config['special_range'][0],
                    config['special_range'][1] + 1
                )
                print(f"   特别号: {special_number}")
            else:
                special_number = None
            
            # 显示预测质量
            metrics = core_result['performance_metrics']
            print(f"\n   📈 预测质量:")
            print(f"      预期命中率: {metrics['expected_hit_probability']:.1%}")
            print(f"      信心度等级: {metrics['confidence_level']}")
            print(f"      平均信心度: {metrics['avg_confidence']:.1f}%")
            
            # 显示投注建议
            print(f"\n   💡 投注建议:")
            if metrics['confidence_level'] == '高':
                print(f"      ✅ 信心度高，建议适度增加投注")
            elif metrics['confidence_level'] == '中等':
                print(f"      ⚠️ 信心度中等，建议正常投注")
            else:
                print(f"      ❌ 信心度低，建议谨慎投注或等待")
            
            # 保存预测记录 - 转换numpy类型
            clean_final_numbers = convert_numpy_types(final_numbers)
            clean_special_number = convert_numpy_types(special_number)
            clean_metrics = convert_numpy_types(metrics)
            
            prediction_record = {
                'lottery_type': lottery_type,
                'lottery_name': config['name'],
                'prediction_date': self.today.strftime('%Y-%m-%d'),
                'main_numbers': clean_final_numbers,
                'special_number': clean_special_number,
                'strategy': strategy,
                'confidence': float(clean_metrics['avg_confidence']),
                'expected_hit_rate': float(clean_metrics['expected_hit_probability']),
                'confidence_level': clean_metrics['confidence_level'],
                'multi_groups': len(groups),
                'timestamp': datetime.now().isoformat()
            }
            
            # 记录到数据库
            self.tracker.record_prediction(
                {
                    'main_numbers': clean_final_numbers,
                    'special_number': clean_special_number,
                    'confidence': float(clean_metrics['avg_confidence']),
                    'method': f'core_refined_{strategy}'
                },
                lottery_type,
                self.today.strftime('%Y-%m-%d'),
                strategy
            )
            
            return prediction_record
            
        except Exception as e:
            print(f"   ❌ 预测生成错误: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def generate_all_predictions(self, strategy='balanced'):
        """生成所有彩票类型的预测"""
        print(f"\n{'='*80}")
        print(f"🎯 今日彩票预测 - {self.today.strftime('%Y年%m月%d日')}")
        print(f"{'='*80}")
        
        # 初始化系统
        self.initialize_systems()
        
        # 生成各彩票预测
        predictions = {}
        
        for lottery_type in self.configs.keys():
            result = self.generate_prediction(lottery_type, strategy)
            if result:
                predictions[lottery_type] = result
        
        # 保存今日预测
        if predictions:
            self.save_predictions(predictions)
            
        return predictions
    
    def save_predictions(self, predictions):
        """保存预测结果"""
        # 创建输出目录
        output_dir = 'daily_predictions'
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存为JSON
        filename = f"{output_dir}/predictions_{self.today.strftime('%Y%m%d')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(predictions, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 预测已保存到: {filename}")
        
        # 生成易读报告
        report_file = f"{output_dir}/report_{self.today.strftime('%Y%m%d')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"今日彩票预测报告\n")
            f.write(f"日期: {self.today.strftime('%Y年%m月%d日')}\n")
            f.write(f"{'='*60}\n\n")
            
            for lottery_type, pred in predictions.items():
                f.write(f"{pred['lottery_name']}\n")
                f.write(f"-" * 30 + "\n")
                f.write(f"主号码: {pred['main_numbers']}\n")
                if pred['special_number']:
                    f.write(f"特别号: {pred['special_number']}\n")
                f.write(f"信心度: {pred['confidence']:.1f}%\n")
                f.write(f"预期命中率: {pred['expected_hit_rate']:.1%}\n")
                f.write(f"信心等级: {pred['confidence_level']}\n")
                f.write(f"\n")
        
        print(f"📄 报告已保存到: {report_file}")
    
    def show_summary(self, predictions):
        """显示预测摘要"""
        if not predictions:
            print("\n⚠️ 今日没有生成任何预测")
            return
            
        print(f"\n{'='*80}")
        print(f"📋 今日预测总结")
        print(f"{'='*80}")
        
        for lottery_type, pred in predictions.items():
            print(f"\n🎲 {pred['lottery_name']}:")
            print(f"   号码: {pred['main_numbers']}", end="")
            if pred['special_number']:
                print(f" + {pred['special_number']}")
            else:
                print()
            print(f"   信心度: {pred['confidence_level']} ({pred['confidence']:.1f}%)")
        
        print(f"\n💡 使用提醒:")
        print(f"   • 预测仅供参考，请理性投注")
        print(f"   • 关注信心度等级，调整投注策略")
        print(f"   • 长期跟踪验证，持续优化系统")
        print(f"   • 请在法律允许范围内参与")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='生成今日彩票预测')
    parser.add_argument('--strategy', 
                       choices=['conservative', 'balanced', 'aggressive'],
                       default='balanced',
                       help='预测策略 (默认: balanced)')
    parser.add_argument('--lottery',
                       choices=['powercolor', 'super_lotto', 'daily539', 'all'],
                       default='all',
                       help='彩票类型 (默认: all)')
    
    args = parser.parse_args()
    
    # 创建生成器
    generator = TodayPredictionGenerator()
    
    # 生成预测
    if args.lottery == 'all':
        predictions = generator.generate_all_predictions(args.strategy)
    else:
        predictions = {}
        result = generator.generate_prediction(args.lottery, args.strategy)
        if result:
            predictions[args.lottery] = result
    
    # 显示总结
    generator.show_summary(predictions)
    
    print(f"\n✅ 预测生成完成！")

if __name__ == "__main__":
    main()