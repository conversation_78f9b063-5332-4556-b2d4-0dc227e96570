#!/usr/bin/env python3
"""
策略追蹤系統
記錄和分析不同AI預測策略的表現
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger('strategy_tracker')

class StrategyTracker:
    """策略預測結果追蹤器"""
    
    def __init__(self, db_path: str = 'data/lottery_data.db'):
        self.db_path = db_path
        self.init_tables()
        
        # 策略類型定義
        self.strategy_types = {
            'AI集成-組1': 'AI綜合預測第一組',
            'AI集成-組2': 'AI綜合預測第二組', 
            'AI集成-組3': 'AI綜合預測第三組',
            'AI集成-組4': 'AI綜合預測第四組',
            'AI集成-組5': 'AI綜合預測第五組',
            'AI集成-組6': 'AI綜合預測第六組',
            '逐期回測': '基於歷史數據的逐期分析',
            '歷史校正': '歷史數據校正預測',
            '綜合回測': '多方法綜合分析'
        }
    
    def init_tables(self):
        """初始化資料表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 策略預測記錄表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS StrategyPredictions (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            StrategyType TEXT NOT NULL,
            LotteryType TEXT NOT NULL,
            PredictionDate DATETIME NOT NULL,
            TargetPeriod TEXT,
            PredictedNumbers TEXT NOT NULL,
            SpecialNumber INTEGER,
            Confidence REAL,
            ActualNumbers TEXT,
            ActualSpecial INTEGER,
            IsMatched BOOLEAN DEFAULT 0,
            HitCount INTEGER DEFAULT 0,
            SpecialHit BOOLEAN DEFAULT 0,
            Accuracy REAL DEFAULT 0.0,
            CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 策略表現統計表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS StrategyPerformance (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            StrategyType TEXT NOT NULL,
            LotteryType TEXT NOT NULL,
            TotalPredictions INTEGER DEFAULT 0,
            TotalHits INTEGER DEFAULT 0,
            SpecialHits INTEGER DEFAULT 0,
            AverageHitCount REAL DEFAULT 0.0,
            AverageAccuracy REAL DEFAULT 0.0,
            BestAccuracy REAL DEFAULT 0.0,
            WorstAccuracy REAL DEFAULT 0.0,
            LastUpdated DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(StrategyType, LotteryType)
        )
        """)
        
        conn.commit()
        conn.close()
        logger.info("策略追蹤資料表初始化完成")
    
    def record_prediction(self, strategy_type: str, lottery_type: str, 
                         predicted_numbers: List[int], special_number: Optional[int] = None,
                         target_period: Optional[str] = None, confidence: float = 0.0):
        """記錄策略預測結果"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
        INSERT INTO StrategyPredictions 
        (StrategyType, LotteryType, PredictionDate, TargetPeriod, 
         PredictedNumbers, SpecialNumber, Confidence)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            strategy_type,
            lottery_type,
            datetime.now(),
            target_period,
            json.dumps(predicted_numbers),
            special_number,
            confidence
        ))
        
        conn.commit()
        conn.close()
        logger.info(f"記錄預測: {strategy_type} - {lottery_type}")
    
    def update_actual_results(self, period: str, lottery_type: str,
                             actual_numbers: List[int], actual_special: Optional[int] = None):
        """更新實際開獎結果並計算命中率"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 找到對應期號的預測記錄
        cursor.execute("""
        SELECT ID, StrategyType, PredictedNumbers, SpecialNumber 
        FROM StrategyPredictions 
        WHERE TargetPeriod = ? AND LotteryType = ? AND ActualNumbers IS NULL
        """, (period, lottery_type))
        
        predictions = cursor.fetchall()
        
        for pred_id, strategy_type, pred_nums_json, pred_special in predictions:
            predicted_numbers = json.loads(pred_nums_json)
            
            # 計算命中數
            hit_count = len(set(predicted_numbers) & set(actual_numbers))
            special_hit = (pred_special == actual_special) if pred_special and actual_special else False
            
            # 計算準確度 
            total_numbers = len(predicted_numbers)
            accuracy = (hit_count / total_numbers * 100) if total_numbers > 0 else 0.0
            if special_hit:
                accuracy += 10.0  # 特別號額外加分
            
            # 更新記錄
            cursor.execute("""
            UPDATE StrategyPredictions 
            SET ActualNumbers = ?, ActualSpecial = ?, IsMatched = ?, 
                HitCount = ?, SpecialHit = ?, Accuracy = ?
            WHERE ID = ?
            """, (
                json.dumps(actual_numbers), actual_special,
                hit_count > 0 or special_hit, hit_count, special_hit, accuracy, pred_id
            ))
            
            # 更新策略統計
            self._update_strategy_stats(strategy_type, lottery_type)
        
        conn.commit()
        conn.close()
        logger.info(f"更新實際結果: {period} - {lottery_type}")
    
    def _update_strategy_stats(self, strategy_type: str, lottery_type: str):
        """更新策略統計數據"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 計算統計數據
        cursor.execute("""
        SELECT 
            COUNT(*) as total_predictions,
            SUM(CASE WHEN IsMatched = 1 THEN 1 ELSE 0 END) as total_hits,
            SUM(CASE WHEN SpecialHit = 1 THEN 1 ELSE 0 END) as special_hits,
            AVG(HitCount) as avg_hit_count,
            AVG(Accuracy) as avg_accuracy,
            MAX(Accuracy) as best_accuracy,
            MIN(Accuracy) as worst_accuracy
        FROM StrategyPredictions 
        WHERE StrategyType = ? AND LotteryType = ? AND ActualNumbers IS NOT NULL
        """, (strategy_type, lottery_type))
        
        stats = cursor.fetchone()
        
        # 更新或插入統計記錄
        cursor.execute("""
        INSERT OR REPLACE INTO StrategyPerformance 
        (StrategyType, LotteryType, TotalPredictions, TotalHits, SpecialHits,
         AverageHitCount, AverageAccuracy, BestAccuracy, WorstAccuracy, LastUpdated)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            strategy_type, lottery_type, stats[0], stats[1], stats[2],
            stats[3] or 0, stats[4] or 0, stats[5] or 0, stats[6] or 0,
            datetime.now()
        ))
        
        conn.commit()
        conn.close()
    
    def get_strategy_performance(self, lottery_type: Optional[str] = None) -> List[Dict]:
        """獲取策略表現數據"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = """
        SELECT StrategyType, LotteryType, TotalPredictions, TotalHits, SpecialHits,
               AverageHitCount, AverageAccuracy, BestAccuracy, WorstAccuracy, LastUpdated
        FROM StrategyPerformance
        """
        params = []
        
        if lottery_type:
            query += " WHERE LotteryType = ?"
            params.append(lottery_type)
        
        query += " ORDER BY AverageAccuracy DESC"
        
        cursor.execute(query, params)
        results = cursor.fetchall()
        
        performance_data = []
        for row in results:
            performance_data.append({
                'strategy_type': row[0],
                'lottery_type': row[1],
                'total_predictions': row[2],
                'total_hits': row[3],
                'special_hits': row[4],
                'average_hit_count': round(row[5], 2),
                'average_accuracy': round(row[6], 2),
                'best_accuracy': round(row[7], 2),
                'worst_accuracy': round(row[8], 2),
                'last_updated': row[9],
                'hit_rate': round((row[3] / row[2] * 100), 2) if row[2] > 0 else 0
            })
        
        conn.close()
        return performance_data
    
    def get_recent_predictions(self, strategy_type: Optional[str] = None, 
                              lottery_type: Optional[str] = None, limit: int = 20) -> List[Dict]:
        """獲取最近的預測記錄"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = """
        SELECT ID, StrategyType, LotteryType, PredictionDate, TargetPeriod,
               PredictedNumbers, SpecialNumber, Confidence, ActualNumbers, 
               ActualSpecial, IsMatched, HitCount, SpecialHit, Accuracy
        FROM StrategyPredictions
        WHERE 1=1
        """
        params = []
        
        if strategy_type:
            query += " AND StrategyType = ?"
            params.append(strategy_type)
        
        if lottery_type:
            query += " AND LotteryType = ?"
            params.append(lottery_type)
        
        query += " ORDER BY PredictionDate DESC LIMIT ?"
        params.append(limit)
        
        cursor.execute(query, params)
        results = cursor.fetchall()
        
        predictions = []
        for row in results:
            predictions.append({
                'id': row[0],
                'strategy_type': row[1],
                'lottery_type': row[2],
                'prediction_date': row[3],
                'target_period': row[4],
                'predicted_numbers': json.loads(row[5]),
                'special_number': row[6],
                'confidence': row[7],
                'actual_numbers': json.loads(row[8]) if row[8] else None,
                'actual_special': row[9],
                'is_matched': row[10],
                'hit_count': row[11],
                'special_hit': row[12],
                'accuracy': row[13]
            })
        
        conn.close()
        return predictions

    def simulate_ai_predictions(self, lottery_type: str, target_period: str):
        """模擬AI策略生成6組預測（用於演示）"""
        import random
        
        # 根據彩票類型設定號碼範圍
        if lottery_type == 'powercolor':
            main_range = (1, 38)  # 威力彩第一區
            special_range = (1, 8)  # 第二區
            main_count = 6
        elif lottery_type == 'lotto649':
            main_range = (1, 49)  # 大樂透
            special_range = (1, 49)
            main_count = 6
        elif lottery_type == 'dailycash':
            main_range = (1, 39)  # 今彩539
            special_range = None
            main_count = 5
        else:
            return
        
        # 生成6組AI預測
        for i in range(1, 7):
            strategy_name = f'AI集成-組{i}'
            
            # 生成號碼（每組稍有不同的邏輯）
            if i <= 2:
                # 組1-2: 偏向小號碼
                numbers = sorted(random.sample(range(main_range[0], main_range[1]//2 + 5), main_count))
            elif i <= 4:
                # 組3-4: 平均分佈
                numbers = sorted(random.sample(range(main_range[0], main_range[1]+1), main_count))
            else:
                # 組5-6: 偏向大號碼
                numbers = sorted(random.sample(range(main_range[1]//2 - 3, main_range[1]+1), main_count))
            
            special = random.randint(special_range[0], special_range[1]) if special_range else None
            confidence = random.uniform(0.6, 0.9)
            
            self.record_prediction(
                strategy_name, lottery_type, numbers, special, target_period, confidence
            )
        
        logger.info(f"為 {lottery_type} 期號 {target_period} 生成了6組AI預測")

def main():
    """測試函數"""
    tracker = StrategyTracker()
    
    print("🎯 策略追蹤系統測試")
    print("="*50)
    
    # 模擬生成預測
    tracker.simulate_ai_predictions('lotto649', '114000082')
    
    # 顯示表現統計
    performance = tracker.get_strategy_performance('lotto649')
    print(f"\n📊 找到 {len(performance)} 個策略表現記錄")
    
    # 顯示最近預測
    recent = tracker.get_recent_predictions(lottery_type='lotto649', limit=10)
    print(f"📋 最近 {len(recent)} 筆預測記錄")
    
    for pred in recent[:3]:
        print(f"  • {pred['strategy_type']}: {pred['predicted_numbers']} (信心度: {pred['confidence']:.1%})")

if __name__ == "__main__":
    main()